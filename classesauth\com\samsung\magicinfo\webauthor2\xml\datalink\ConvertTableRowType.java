package com.samsung.magicinfo.webauthor2.xml.datalink;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ConvertTableRowType", propOrder = {"type", "from", "to"})
public class ConvertTableRowType {
  @XmlAttribute(name = "type")
  private String type = "";
  
  @XmlElement(name = "From")
  private String from;
  
  @XmlElement(name = "To")
  private ValueType to;
  
  public String getType() {
    return this.type;
  }
  
  public void setType(String type) {
    this.type = type;
  }
  
  public String getFrom() {
    return this.from;
  }
  
  public void setFrom(String from) {
    this.from = from;
  }
  
  public ValueType getTo() {
    return this.to;
  }
  
  public void setTo(ValueType to) {
    this.to = to;
  }
}
