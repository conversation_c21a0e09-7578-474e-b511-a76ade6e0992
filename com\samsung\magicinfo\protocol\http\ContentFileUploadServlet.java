package com.samsung.magicinfo.protocol.http;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.exception.CMSExceptionCode;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.AbilityUtils;
import com.samsung.common.utils.ContentUtils;
import com.samsung.common.utils.FileUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.common.FileManager;
import com.samsung.magicinfo.framework.common.FileManagerImpl;
import com.samsung.magicinfo.framework.content.constants.ContentConstants;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.entity.PlaylistContent;
import com.samsung.magicinfo.framework.content.manager.ContentCodeInfo;
import com.samsung.magicinfo.framework.content.manager.ContentCodeInfoImpl;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.setup.entity.TagEntity;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfo;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.TagInfo;
import com.samsung.magicinfo.framework.setup.manager.TagInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URLDecoder;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import javax.imageio.ImageIO;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;

public class ContentFileUploadServlet extends HttpServlet {
   private static final long serialVersionUID = 1L;
   private Logger logger = LoggingManagerV2.getLogger(ContentFileUploadServlet.class);
   private static List fexts = Arrays.asList("gif", "jpeg", "jpg", "png", "swf", "bmp", "asf", "avi", "mpeg", "mpg", "ts", "trp", "m2v", "m2p", "mp4", "m1v", "m4v", "m4t", "vob", "m2t", "tsp", "mov", "asx", "wmv", "tp", "doc", "docx", "ppt", "pptx", "xls", "xlsx", "htm", "html", "pps", "ppsx", "pdf", "mp3", "ogg", "wav", "wma", "mp2", "ac3", "pcm", "lpcm", "flv", "wmf", "emf", "tif", "tiff", "mid", "mkv", "ra", "rm", "ram", "rmvb", "3gp", "svi", "m2ts", "divx", "mts", "vro", "zip", "xml", "wgt");

   public ContentFileUploadServlet() {
      super();
   }

   public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");
      this.doPost(request, response);
   }

   public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      ContentInfo cmsDao = ContentInfoImpl.getInstance();
      UserInfo uInfo = UserInfoImpl.getInstance();
      String groupId = request.getParameter("groupId");
      String userId = (String)request.getSession().getAttribute("userId");
      String updatedContentId = request.getParameter("contentId");
      String contentType = request.getParameter("contentType");
      String webContentName = request.getParameter("webContentName");
      if (webContentName != null) {
         webContentName = URLDecoder.decode(webContentName, "UTF-8");
      }

      String startPage = request.getParameter("startPage");
      String refreshInterval = request.getParameter("refreshInterval");
      String mode = request.getParameter("mode");
      boolean contentUpdate = false;
      if (updatedContentId != null && !updatedContentId.equals("")) {
         contentUpdate = true;
      }

      long orgCreatorId;
      try {
         orgCreatorId = cmsDao.getRootId(userId);
      } catch (SQLException var143) {
         response.sendError(404, "NOT FOUND");
         return;
      }

      if (groupId == null || groupId.equals("") || groupId.equals("0") || groupId.equals("null")) {
         groupId = String.valueOf(orgCreatorId);
      }

      if (!ServletFileUpload.isMultipartContent(request)) {
         response.sendError(602, CMSExceptionCode.APP602[2]);
      } else {
         String CONTENTS_HOME = null;
         String THUMBNAIL_HOME = null;

         try {
            CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
            THUMBNAIL_HOME = CommonConfig.get("THUMBNAIL_HOME").replace('/', File.separatorChar);
         } catch (ConfigException var142) {
            response.sendError(404, "NOT FOUND");
            return;
         }

         File cmsHome = SecurityUtils.getSafeFile(CONTENTS_HOME);
         if (!cmsHome.exists()) {
            cmsHome.mkdir();
         }

         ServletFileUpload uploadHandler = new ServletFileUpload(new DiskFileItemFactory());
         PrintWriter writer = response.getWriter();
         response.setContentType("application/json");
         JSONArray json = new JSONArray();
         ContentCodeInfo codeDao = ContentCodeInfoImpl.getInstance();
         FileManager fileManager = FileManagerImpl.getInstance();
         boolean bExistFile = false;
         boolean bMustAddContent = false;
         List doNotSupportContentList = new ArrayList();
         boolean contentsApprovalEnable = false;

         try {
            long orgId = uInfo.getRootGroupIdByUserId(userId);
            ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
            Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
            contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
         } catch (Exception var141) {
         }

         try {
            List items = uploadHandler.parseRequest(request);
            String categoryId = null;
            Iterator var153 = items.iterator();

            FileItem item;
            while(var153.hasNext()) {
               item = (FileItem)var153.next();
               synchronized(this) {
                  if (item.isFormField() && item.getFieldName().equals("categoryId") && item.getString() != null && !item.getString().equals("")) {
                     categoryId = item.getString();
                  }
               }
            }

            String fileName;
            String fext;
            String thumbnailFileId;
            String hashId;
            String strmUrl;
            ContentFile cmsContentFile;
            Map thumbnailMap;
            if ("SAPP".equalsIgnoreCase(contentType)) {
               File wgtFile = null;
               File configFile = null;
               String wgtFileID = null;
               fileName = null;
               fext = null;
               int success = 0;
               Map data = new HashMap();
               String status = null;
               String message = null;
               Iterator var161 = items.iterator();

               while(var161.hasNext()) {
                  FileItem item = (FileItem)var161.next();
                  synchronized(this) {
                     if (!item.isFormField()) {
                        thumbnailFileId = item.getName();
                        hashId = thumbnailFileId.substring(thumbnailFileId.lastIndexOf(".") + 1, thumbnailFileId.length());
                        strmUrl = thumbnailFileId.substring(0, thumbnailFileId.lastIndexOf("."));
                        cmsContentFile = null;
                        if (hashId.equalsIgnoreCase("wgt") || thumbnailFileId.toUpperCase().equals("SSSP_CONFIG.XML")) {
                           if (contentType == null || contentType.equals("") || !this.supportContentType(hashId)) {
                              doNotSupportContentList.add(thumbnailFileId);
                              break;
                           }

                           long fileSize = item.getSize();
                           String fileID = UUID.randomUUID().toString().toUpperCase();
                           File file = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separator + fileID + File.separator + thumbnailFileId, (String)null));
                           file.getParentFile().mkdir();
                           item.write(file);
                           if (fileSize != file.length()) {
                              file.delete();
                              file.getParentFile().delete();
                              throw new Exception("Uploaded file size is differ with original file size. Delete upload file and folder." + thumbnailFileId);
                           }

                           thumbnailMap = null;
                           if (hashId.equalsIgnoreCase("wgt")) {
                              wgtFile = file;
                              wgtFileID = fileID;
                              ++success;
                           } else if (thumbnailFileId.toUpperCase().equals("SSSP_CONFIG.XML")) {
                              configFile = file;
                              fileName = fileID;
                              ++success;
                           }
                        }
                     }
                  }
               }

               JSONObject jsono;
               if (success == 2) {
                  try {
                     data.put("refreshInterval", refreshInterval);
                     data.put("fileSize", wgtFile.length());
                     data.put("configFilePath", configFile.getPath());
                     data.put("configFileTempField2", fileName);
                     if (mode != null && mode.equalsIgnoreCase("update")) {
                        data.put("mode", "UPDATE");
                     }

                     if (contentUpdate) {
                        fext = updatedContentId;
                     } else {
                        fext = UUID.randomUUID().toString().toUpperCase();
                     }

                     int code = ContentUtils.createWebContent(userId, "", 0.0F, wgtFile.getPath(), data, contentType, fext, webContentName, wgtFileID);
                     if (code != 1) {
                        status = "fail";
                     } else {
                        status = "success";
                     }
                  } catch (Exception var139) {
                     this.logger.error("", var139);
                  }

                  jsono = new JSONObject();
                  jsono.put("name", webContentName);
                  jsono.put("size", wgtFile.length());
                  jsono.put("status", status);
                  json.put(jsono);
               } else {
                  jsono = new JSONObject();
                  jsono.put("status", "fail to upload sssp content");
                  json.put(jsono);
               }

               try {
                  File parentFile;
                  if (wgtFile.exists()) {
                     wgtFile.delete();
                     parentFile = wgtFile.getParentFile();
                     parentFile.delete();
                  }

                  if (configFile.exists()) {
                     configFile.delete();
                     parentFile = configFile.getParentFile();
                     parentFile.delete();
                  }

                  this.logger.info("[MagicInfo_UploadSAppContent] delete temp Folder");
               } catch (Exception var138) {
                  this.logger.error("[MagicInfo_UploadSAppContent] fail to delete temp Folder", var138);
               }
            } else {
               var153 = items.iterator();

               while(var153.hasNext()) {
                  item = (FileItem)var153.next();
                  synchronized(this) {
                     if (!item.isFormField()) {
                        fileName = item.getName();
                        fext = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
                        String contentName = fileName.substring(0, fileName.lastIndexOf("."));
                        String mediaType = null;
                        if (contentType == null || !contentType.equals("HTML") && !ContentConstants.getMediaTypeForAuthor().contains(contentType)) {
                           mediaType = StrUtils.nvl(codeDao.getMediaTypeByFileType(fext.toUpperCase()));
                        } else {
                           mediaType = contentType;
                        }

                        if (mediaType == null || mediaType.equals("") || !this.supportContentType(fext)) {
                           doNotSupportContentList.add(fileName);
                           break;
                        }

                        long fileSize = item.getSize();
                        String fileID = UUID.randomUUID().toString().toUpperCase();
                        String contentId = null;
                        if (contentUpdate) {
                           contentId = updatedContentId;
                        } else {
                           contentId = UUID.randomUUID().toString().toUpperCase();
                        }

                        File file = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separator + fileID + File.separator + fileName, (String)null));
                        file.getParentFile().mkdir();
                        item.write(file);
                        if (fileSize != file.length()) {
                           file.delete();
                           file.getParentFile().delete();
                           throw new Exception("Uploaded file size is differ with original file size. Delete upload file and folder." + fileName);
                        }

                        thumbnailFileId = null;
                        if (contentType != null && !contentType.isEmpty() && contentType.equals("HTML")) {
                           hashId = null;
                           strmUrl = null;
                           if (fext.equalsIgnoreCase("zip")) {
                              try {
                                 Map data = new HashMap();
                                 data.put("startPage", startPage);
                                 data.put("refreshInterval", refreshInterval);
                                 data.put("fileSize", fileSize);
                                 if (mode != null && mode.equalsIgnoreCase("update")) {
                                    data.put("mode", "UPDATE");
                                 }

                                 int code = ContentUtils.createWebContent(userId, "", 0.0F, file.getPath(), data, contentType, contentId, webContentName, fileID);
                                 if (code != 1) {
                                    hashId = "fail";
                                 } else {
                                    hashId = "success";
                                 }

                                 try {
                                    file.delete();
                                    File parentFile = file.getParentFile();
                                    parentFile.delete();
                                    this.logger.info("[MagicInfo_UploadHtmlContent] delete temp Folder");
                                 } catch (Exception var136) {
                                    this.logger.error("[MagicInfo_UploadHtmlContent] fail to delete temp Folder", var136);
                                 }
                              } catch (Exception var137) {
                                 this.logger.error("", var137);
                              }

                              JSONObject jsono = new JSONObject();
                              jsono.put("name", item.getName());
                              jsono.put("size", item.getSize());
                              jsono.put("status", hashId);
                              json.put(jsono);
                           }
                        } else {
                           hashId = FileUtils.getHash(file);
                           strmUrl = null;
                           String filePath;
                           File checkFile;
                           int mediumHeight;
                           File thumb_File;
                           String image_url;
                           if (contentUpdate) {
                              List fileListToSave = new ArrayList();
                              boolean createThumbnail = true;
                              if (cmsDao.isExistFileByHash(fileName, fileSize, hashId)) {
                                 bExistFile = true;
                              }

                              if (mediaType.equalsIgnoreCase("STRM")) {
                                 BufferedReader br = null;

                                 try {
                                    char[] c = new char[(int)file.length()];
                                    br = new BufferedReader(new FileReader(file));
                                    br.read(c);
                                    strmUrl = new String(c);
                                 } catch (FileNotFoundException var133) {
                                    this.logger.error("", var133);
                                 } catch (Exception var134) {
                                    this.logger.error("", var134);
                                 } finally {
                                    try {
                                       br.close();
                                    } catch (IOException var129) {
                                       this.logger.error("", var129);
                                    }

                                 }
                              }

                              if (bExistFile) {
                                 file.delete();
                                 fileID = cmsDao.getFileIDByHash(fileName, fileSize, hashId);
                                 file = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separatorChar + fileID + File.separatorChar + fileName, (String)null));
                              } else {
                                 ContentFile cmsContentFile = new ContentFile();
                                 if (!fext.equalsIgnoreCase("mpeg") && !fext.equalsIgnoreCase("mpg") && !fext.equalsIgnoreCase("wmv") && !fext.equalsIgnoreCase("avi") && !fext.equalsIgnoreCase("mov") && !fext.equalsIgnoreCase("mp4") && !fext.equalsIgnoreCase("asf")) {
                                    cmsContentFile.setIs_streaming("N");
                                 } else {
                                    cmsContentFile.setIs_streaming("Y");
                                 }

                                 cmsContentFile.setFile_id(fileID);
                                 cmsContentFile.setHash_code(hashId);
                                 cmsContentFile.setFile_type("MAIN");
                                 cmsContentFile.setFile_name(fileName);
                                 cmsContentFile.setFile_size(fileSize);
                                 cmsContentFile.setCreator_id(userId);
                                 cmsContentFile.setFile_path(CONTENTS_HOME + File.separator + fileID);
                                 cmsDao.addFile(cmsContentFile);
                                 fileListToSave.add(cmsContentFile);
                              }

                              try {
                                 long versionId = cmsDao.getMaxContentVersionId(contentId) + 1L;
                                 long existGroupId = cmsDao.getGroupId(contentId);
                                 Content content = new Content();
                                 content.setMain_file_id(fileID);
                                 content.setMain_file_Extension(fext.toUpperCase());
                                 content.setContent_id(contentId);
                                 content.setVersion_id(versionId);
                                 content.setCreator_id(userId);
                                 content.setMedia_type(mediaType);
                                 content.setGroup_id(existGroupId);
                                 content.setShare_flag(1);
                                 content.setContent_meta_data("");
                                 content.setOrganization_id(uInfo.getRootGroupIdByUserId(userId));
                                 content.setTotal_size(fileSize);
                                 content.setIs_active("N");
                                 content.setOrg_creator_id(String.valueOf(orgCreatorId));
                                 filePath = null;
                                 if (bExistFile) {
                                    filePath = cmsDao.getThumbIdByMainFileId(fileID);
                                    if (filePath != null) {
                                       ContentFile thumbFile = cmsDao.getFileInfo(filePath);
                                       content.setThumb_file_id(filePath);
                                       content.setThumb_file_name(thumbFile.getFile_name());
                                       createThumbnail = false;
                                    }
                                 }

                                 int smallWidth;
                                 int mediumWidth;
                                 String thmubHash;
                                 int orgWidth;
                                 int orgHeight;
                                 if (content.getMedia_type().equalsIgnoreCase("SOUND")) {
                                    content.setThumb_file_id("SOUND_THUMBNAIL");
                                    content.setThumb_file_name("SOUND_THUMBNAIL.PNG");
                                    String[] fileMeta;
                                    if (fext.equals("MP3")) {
                                       try {
                                          fileMeta = fileManager.getFileMeta(file);
                                          if (fileMeta[0] != null) {
                                             image_url = fileMeta[0];
                                             if (image_url.length() > 8) {
                                                content.setPlay_time(image_url.substring(0, 8));
                                                content.setPlay_time_milli(image_url.substring(9, image_url.length()));
                                             } else {
                                                content.setPlay_time(image_url);
                                             }
                                          } else {
                                             image_url = fileManager.getMP3PlayTime(file);
                                             if (image_url.length() > 8) {
                                                content.setPlay_time(image_url.substring(0, 8));
                                                content.setPlay_time_milli(image_url.substring(9, image_url.length()));
                                             } else {
                                                content.setPlay_time(image_url);
                                             }
                                          }
                                       } catch (Exception var132) {
                                          content.setPlay_time("");
                                       }
                                    } else {
                                       fileMeta = fileManager.getFileMeta(file);
                                       if (fileMeta[0] != null) {
                                          image_url = fileMeta[0];
                                          if (image_url.length() > 8) {
                                             content.setPlay_time(image_url.substring(0, 8));
                                             content.setPlay_time_milli(image_url.substring(9, image_url.length()));
                                          } else {
                                             content.setPlay_time(image_url);
                                          }
                                       } else {
                                          content.setPlay_time("");
                                       }
                                    }
                                 } else if (!content.getMedia_type().equalsIgnoreCase("IMAGE") && !content.getMedia_type().equalsIgnoreCase("MOVIE")) {
                                    if (content.getMedia_type().equalsIgnoreCase("STRM")) {
                                       if (strmUrl != null) {
                                          cmsDao.updateUrlSetting(content.getContent_id(), content.getContent_name(), strmUrl);
                                       }

                                       content.setThumb_file_id("STRM_THUMBNAIL");
                                       content.setThumb_file_name("STRM_THUMBNAIL.PNG");
                                    } else if (content.getMedia_type().equalsIgnoreCase("OFFICE")) {
                                       content.setThumb_file_id("OFFICE_THUMBNAIL");
                                       content.setThumb_file_name("OFFICE_THUMBNAIL.PNG");
                                       thumbnailFileId = "OFFICE_THUMBNAIL";
                                    } else if (content.getMedia_type().equalsIgnoreCase("FLASH")) {
                                       content.setThumb_file_id("FLASH_THUMBNAIL");
                                       content.setThumb_file_name("FLASH_THUMBNAIL.PNG");
                                       thumbnailFileId = "FLASH_THUMBNAIL";
                                    } else if (content.getMedia_type().equalsIgnoreCase("PDF")) {
                                       content.setThumb_file_id("PDF_THUMBNAIL");
                                       content.setThumb_file_name("PDF_THUMBNAIL.PNG");
                                       thumbnailFileId = "PDF_THUMBNAIL";
                                    } else {
                                       content.setThumb_file_id("ETC_THUMBNAIL");
                                       content.setThumb_file_name("ETC_THUMBNAIL.PNG");
                                       thumbnailFileId = "ETC_THUMBNAIL";
                                    }
                                 } else {
                                    boolean addThumbFile = true;
                                    ContentFile cmsThumbFile = new ContentFile();
                                    thumbnailFileId = null;
                                    if (createThumbnail) {
                                       thumbnailFileId = UUID.randomUUID().toString().toUpperCase();
                                    } else {
                                       thumbnailFileId = filePath;
                                    }

                                    Map thumbnailMap = fileManager.createThumbnailFile(file, thumbnailFileId, content);
                                    if (thumbnailMap != null && thumbnailMap.get("status") != null && thumbnailMap.get("status").equals("error")) {
                                       throw new Exception("error create thumbnail");
                                    }

                                    if (thumbnailMap != null && thumbnailMap.get("resolution") != null) {
                                       content.setResolution((String)thumbnailMap.get("resolution"));
                                    }

                                    if (thumbnailMap != null && thumbnailMap.get("playTime") != null) {
                                       String play_time = (String)thumbnailMap.get("playTime");
                                       if (play_time.length() > 8) {
                                          content.setPlay_time(play_time.substring(0, 8));
                                          content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                                       } else {
                                          content.setPlay_time(play_time);
                                       }
                                    }

                                    checkFile = null;
                                    if (thumbnailMap != null && thumbnailMap.get("file") != null) {
                                       checkFile = (File)thumbnailMap.get("file");
                                    }

                                    thmubHash = FileUtils.getHash(checkFile);
                                    if (thmubHash == null) {
                                       content.setThumb_file_id("NOIMAGE_THUMBNAIL");
                                       content.setThumb_file_name("NOIMAGE_THUMBNAIL.PNG");
                                    } else {
                                       if (cmsDao.isExistFileByHash(fileName + ".png", checkFile.length(), thmubHash)) {
                                          thumbnailFileId = cmsDao.getFileIDByHash(fileName + ".png", checkFile.length(), thmubHash);
                                       }

                                       content.setThumb_file_id(thumbnailFileId);
                                       content.setThumb_file_name(fileName + ".png");
                                    }

                                    addThumbFile = true;
                                    File fileCmsHome = SecurityUtils.getSafeFile(THUMBNAIL_HOME);
                                    File thumbnailFileChk = new File(SecurityUtils.directoryTraversalChecker(THUMBNAIL_HOME + "/" + thumbnailFileId, (String)null));
                                    if (thumbnailFileChk.exists()) {
                                       addThumbFile = false;
                                    }

                                    if (addThumbFile) {
                                       cmsThumbFile.setFile_id(thumbnailFileId);
                                       cmsThumbFile.setHash_code(thmubHash);
                                       cmsThumbFile.setFile_type("THUMBNAIL");
                                       cmsThumbFile.setFile_name(fileName + ".png");
                                       cmsThumbFile.setFile_size(checkFile.length());
                                       cmsThumbFile.setCreator_id("SYSTEM");
                                       cmsThumbFile.setFile_path(CONTENTS_HOME + File.separator + thumbnailFileId);
                                       cmsDao.addFile(cmsThumbFile);
                                       if (cmsThumbFile != null) {
                                          fileListToSave.add(cmsThumbFile);
                                       }

                                       content.setArr_file_list(fileListToSave);
                                       if (thumbnailMap != null && thumbnailMap.get("resolution") != null) {
                                          content.setResolution((String)thumbnailMap.get("resolution"));
                                       }

                                       String filePath;
                                       if (thumbnailMap != null && thumbnailMap.get("playTime") != null) {
                                          filePath = (String)thumbnailMap.get("playTime");
                                          if (filePath.length() > 8) {
                                             content.setPlay_time(filePath.substring(0, 8));
                                             content.setPlay_time_milli(filePath.substring(9, filePath.length()));
                                          } else {
                                             content.setPlay_time(filePath);
                                          }
                                       }

                                       try {
                                          if (!fileCmsHome.exists()) {
                                             boolean fSuccess = fileCmsHome.mkdir();
                                             if (!fSuccess) {
                                                this.logger.error("mkdir Fail");
                                             }
                                          }

                                          filePath = THUMBNAIL_HOME + File.separator + content.getThumb_file_id();
                                          File fileCmsFile = SecurityUtils.getSafeFile(filePath);
                                          if (!fileCmsFile.exists()) {
                                             boolean fSuccess = fileCmsFile.mkdir();
                                             if (!fSuccess) {
                                                this.logger.error("mkdir Fail");
                                             }
                                          }

                                          String image_url = CONTENTS_HOME + "/" + content.getThumb_file_id() + "/" + content.getThumb_file_name();
                                          String thumb_url = THUMBNAIL_HOME + "/" + cmsThumbFile.getFile_id() + "/" + cmsThumbFile.getFile_name();
                                          File checkFile = SecurityUtils.getSafeFile(thumb_url);
                                          if (!checkFile.exists()) {
                                             thumb_File = SecurityUtils.getSafeFile(image_url);
                                             if (thumb_File.exists()) {
                                                BufferedImage bufferedImage = ImageIO.read(thumb_File);
                                                if (bufferedImage == null) {
                                                   this.logger.error("[ContentFileUploadServlet] null thumbnail image : " + thumb_File.getPath());
                                                   throw new NullPointerException();
                                                }

                                                orgWidth = bufferedImage.getWidth();
                                                orgHeight = bufferedImage.getHeight();
                                                smallWidth = 50;
                                                int smallHeight = 38;
                                                mediumWidth = 165;
                                                int mediumHeight = 109;
                                                if ((long)(orgWidth / mediumWidth) > (long)(orgHeight / mediumHeight)) {
                                                   mediumHeight = orgHeight * mediumWidth / orgWidth;
                                                   if (mediumHeight % 2 != 0) {
                                                      ++mediumHeight;
                                                   }
                                                } else {
                                                   mediumWidth = orgWidth * mediumHeight / orgHeight;
                                                   if (mediumWidth % 2 != 0) {
                                                      ++mediumWidth;
                                                   }
                                                }

                                                if ((long)(orgWidth / smallWidth) > (long)(orgHeight / smallHeight)) {
                                                   smallHeight = orgHeight * smallWidth / orgWidth;
                                                   if (smallHeight % 2 != 0) {
                                                      ++smallHeight;
                                                   }
                                                } else {
                                                   smallWidth = orgWidth * smallHeight / orgHeight;
                                                   if (smallWidth % 2 != 0) {
                                                      ++smallWidth;
                                                   }
                                                }

                                                if (mediumWidth < 1) {
                                                   mediumWidth = 1;
                                                }

                                                if (mediumHeight < 1) {
                                                   mediumHeight = 1;
                                                }

                                                if (smallWidth < 1) {
                                                   smallWidth = 1;
                                                }

                                                if (smallHeight < 1) {
                                                   smallHeight = 1;
                                                }

                                                File thumb_File = SecurityUtils.getSafeFile(thumb_url);
                                                File thumb_smallFile = SecurityUtils.getSafeFile(thumb_url + "_SMALL.PNG");
                                                File thumb_mediumFile = SecurityUtils.getSafeFile(thumb_url + "_MEDIUM.PNG");
                                                ImageIO.write(fileManager.createResizedCopy(bufferedImage, orgWidth, orgHeight), "PNG", thumb_File);
                                                ImageIO.write(fileManager.createResizedCopy(bufferedImage, smallWidth, smallHeight), "PNG", thumb_smallFile);
                                                ImageIO.write(fileManager.createResizedCopy(bufferedImage, mediumWidth, mediumHeight), "PNG", thumb_mediumFile);
                                                Map hdThumbnailMap = fileManager.createThumbnailFile(file, THUMBNAIL_HOME, thumbnailFileId, content, 1280, 720, "_HD.PNG");
                                                if (hdThumbnailMap != null && hdThumbnailMap.get("status") != null && hdThumbnailMap.get("status").equals("error")) {
                                                   this.logger.error("HD Thumbnail create error.");
                                                }
                                             }
                                          }
                                       } catch (Exception var131) {
                                          this.logger.error("[ContentFileUploadServlet] error create thumbnail");
                                          throw new Exception("error create thumbnail!");
                                       }
                                    }
                                 }

                                 this.logger.info("[MagicInfo_ContentFileUpload] content update! contentId : " + contentId + " new version : " + versionId + " fileId : " + fileID);
                                 cmsDao.addContentVersionInfo(content);
                                 cmsDao.addMapContentFile(contentId, versionId, fileID);
                                 if (thumbnailFileId == null) {
                                    thumbnailFileId = "ETC_THUMBNAIL";
                                 }

                                 cmsDao.addMapContentFile(contentId, versionId, thumbnailFileId);
                                 cmsDao.setActiveVersionForUploader(contentId, versionId, false);

                                 try {
                                    PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
                                    List playlistInfoList = playlistInfo.getPlaylistInfoByContentId(contentId);

                                    for(int idx = 0; idx < playlistInfoList.size(); ++idx) {
                                       Map mapPlaylistInfo = (Map)playlistInfoList.get(idx);
                                       thmubHash = mapPlaylistInfo.get("playlist_id").toString();
                                       long lVersionId = Long.parseLong(mapPlaylistInfo.get("version_id").toString());
                                       Playlist curPlaylist = playlistInfo.getPlaylistVerInfo(thmubHash, lVersionId);
                                       if (curPlaylist != null) {
                                          List playlistContentList;
                                          if (!"4".equals(curPlaylist.getPlaylist_type()) && !StringUtils.isEmpty(content.getPlay_time())) {
                                             if ("3".equals(curPlaylist.getPlaylist_type())) {
                                                playlistContentList = playlistInfo.getContentList(curPlaylist.getPlaylist_id(), curPlaylist.getVersion_id());
                                                PlaylistContent playlistContent0 = (PlaylistContent)playlistContentList.get(0);
                                                PlaylistContent playlistContentN = (PlaylistContent)playlistContentList.get(playlistContentList.size() - 1);
                                                mediumHeight = Integer.valueOf(playlistContentN.getSync_play_id()) - Integer.valueOf(playlistContent0.getSync_play_id()) + 1;
                                                int iContentCount = playlistContentList.size() / mediumHeight;

                                                for(int i = 0; i < iContentCount; ++i) {
                                                   Long lMaxPlayTime = 0L;
                                                   String sMaxPlayTimeMilli = "";

                                                   PlaylistContent tmpPlaylistContent;
                                                   for(smallWidth = 0; smallWidth < mediumHeight; ++smallWidth) {
                                                      tmpPlaylistContent = (PlaylistContent)playlistContentList.get(i + iContentCount * smallWidth);
                                                      if (tmpPlaylistContent.getContent_id().equals(content.getContent_id())) {
                                                         tmpPlaylistContent.setContent_duration(ContentUtils.getPlayTimeStr(content.getPlay_time()));
                                                         tmpPlaylistContent.setContent_duration_milli(content.getPlay_time_milli());
                                                      }

                                                      if (lMaxPlayTime < tmpPlaylistContent.getContent_duration()) {
                                                         lMaxPlayTime = tmpPlaylistContent.getContent_duration();
                                                         sMaxPlayTimeMilli = tmpPlaylistContent.getContent_duration_milli();
                                                      } else if (lMaxPlayTime == tmpPlaylistContent.getContent_duration() && sMaxPlayTimeMilli.compareTo(tmpPlaylistContent.getContent_duration_milli()) < 0) {
                                                         lMaxPlayTime = tmpPlaylistContent.getContent_duration();
                                                         sMaxPlayTimeMilli = tmpPlaylistContent.getContent_duration_milli();
                                                      }
                                                   }

                                                   for(smallWidth = 0; smallWidth < mediumHeight; ++smallWidth) {
                                                      tmpPlaylistContent = (PlaylistContent)playlistContentList.get(i + iContentCount * smallWidth);
                                                      playlistInfo.setContentDurationByVersionOfPlaylist(curPlaylist.getPlaylist_id(), curPlaylist.getVersion_id(), tmpPlaylistContent.getContent_id(), lMaxPlayTime);
                                                      playlistInfo.setContentDurationMilliByVersionOfPlaylist(curPlaylist.getPlaylist_id(), curPlaylist.getVersion_id(), tmpPlaylistContent.getContent_id(), sMaxPlayTimeMilli);
                                                   }
                                                }
                                             } else {
                                                playlistInfo.setContentDurationByVersionOfPlaylist(curPlaylist.getPlaylist_id(), curPlaylist.getVersion_id(), content.getContent_id(), ContentUtils.getPlayTimeStr(content.getPlay_time()));
                                                playlistInfo.setContentDurationMilliByVersionOfPlaylist(curPlaylist.getPlaylist_id(), curPlaylist.getVersion_id(), content.getContent_id(), content.getPlay_time_milli());
                                             }
                                          }

                                          playlistContentList = playlistInfo.getContentList(curPlaylist.getPlaylist_id(), curPlaylist.getVersion_id());
                                          long lPlayTime = 0L;
                                          long lTotalSize = 0L;
                                          String sContentList;
                                          if ("3".equals(curPlaylist.getPlaylist_type())) {
                                             PlaylistContent playlistContent0 = (PlaylistContent)playlistContentList.get(0);
                                             PlaylistContent playlistContentN = (PlaylistContent)playlistContentList.get(playlistContentList.size() - 1);
                                             orgHeight = Integer.valueOf(playlistContentN.getSync_play_id()) - Integer.valueOf(playlistContent0.getSync_play_id()) + 1;
                                             smallWidth = playlistContentList.size() / orgHeight;
                                             String sContentList = "";

                                             for(mediumWidth = 0; mediumWidth < smallWidth; ++mediumWidth) {
                                                List unduplicatedPlaylistContentList = new ArrayList();

                                                int j;
                                                PlaylistContent tmpPlaylistContent;
                                                for(j = 0; j < orgHeight; ++j) {
                                                   tmpPlaylistContent = (PlaylistContent)playlistContentList.get(mediumWidth + smallWidth * j);
                                                   if (j == 0) {
                                                      lPlayTime += tmpPlaylistContent.getContent_duration();
                                                   }

                                                   if (!sContentList.contains(tmpPlaylistContent.getContent_id())) {
                                                      sContentList = sContentList + tmpPlaylistContent.getContent_id() + "|";
                                                      unduplicatedPlaylistContentList.add(tmpPlaylistContent);
                                                   }
                                                }

                                                for(j = 0; j < unduplicatedPlaylistContentList.size(); ++j) {
                                                   tmpPlaylistContent = (PlaylistContent)unduplicatedPlaylistContentList.get(j);
                                                   Content tmpContent = cmsDao.getContentActiveVerInfo(tmpPlaylistContent.getContent_id());
                                                   if (tmpContent != null) {
                                                      lTotalSize += tmpContent.getTotal_size();
                                                   }
                                                }
                                             }
                                          } else {
                                             sContentList = "";

                                             for(orgWidth = 0; orgWidth < playlistContentList.size(); ++orgWidth) {
                                                PlaylistContent playlistContent = (PlaylistContent)playlistContentList.get(orgWidth);
                                                Content contentInfo = cmsDao.getContentActiveVerInfo(playlistContent.getContent_id());
                                                if (playlistContent.getContent_duration() != null) {
                                                   lPlayTime += playlistContent.getContent_duration();
                                                }

                                                if (contentInfo != null && contentInfo.getTotal_size() != null && !sContentList.contains(contentInfo.getContent_id())) {
                                                   sContentList = sContentList + contentInfo.getContent_id() + "|";
                                                   lTotalSize += contentInfo.getTotal_size();
                                                }
                                             }
                                          }

                                          sContentList = ContentUtils.getPlayTimeFormattedStr(lPlayTime);
                                          if (!"4".equals(curPlaylist.getPlaylist_type())) {
                                             playlistInfo.setPlaytime(curPlaylist.getPlaylist_id(), curPlaylist.getVersion_id(), sContentList);
                                          }

                                          playlistInfo.setTotalSize(curPlaylist.getPlaylist_id(), curPlaylist.getVersion_id(), lTotalSize);
                                          this.logger.info("[SF00179387]UPDATED playTime and totalSize of Playlist[" + curPlaylist.getPlaylist_id() + "][" + curPlaylist.getPlaylist_name() + "]ver[" + curPlaylist.getVersion_id() + "][" + sContentList + "][" + lTotalSize + "]");
                                       }
                                    }
                                 } catch (Exception var145) {
                                    this.logger.error("[SF00179387]can NOT update playTime and totalSize of Playlist", var145);
                                 }

                                 ScheduleInfo sInfo = ScheduleInfoImpl.getInstance();
                                 sInfo.setContentTrigger(contentId);
                                 EventInfo eInfo = EventInfoImpl.getInstance();
                                 eInfo.setContentTrigger(contentId);
                                 List pList = cmsDao.getPlaylistListUsingContent(contentId);

                                 for(int i = 0; i < pList.size(); ++i) {
                                    Map map = (Map)pList.get(i);
                                    String playlistId = (String)map.get("playlist_id");
                                    sInfo.setPlaylistTrigger(playlistId);
                                 }

                                 TagInfo tagInfo = TagInfoImpl.getInstance();
                                 List tagList = tagInfo.getContentTagList(contentId);
                                 if (tagList != null && tagList.size() > 0) {
                                    List triggerTagList = new ArrayList();
                                    Iterator var213 = tagList.iterator();

                                    while(var213.hasNext()) {
                                       TagEntity tag = (TagEntity)var213.next();
                                       if (!triggerTagList.contains((long)tag.getTag_id())) {
                                          triggerTagList.add((long)tag.getTag_id());
                                       }
                                    }

                                    if (triggerTagList.size() > 0) {
                                       tagInfo.setPlaylistTrigger(triggerTagList);
                                    }
                                 }
                              } catch (Exception var146) {
                                 this.logger.error("[MagicInfo_ContentFileUpload] fail update content error : " + var146.getMessage(), var146);
                              }
                           } else {
                              cmsContentFile = new ContentFile();
                              if (!fext.equalsIgnoreCase("mpeg") && !fext.equalsIgnoreCase("mpg") && !fext.equalsIgnoreCase("wmv") && !fext.equalsIgnoreCase("avi") && !fext.equalsIgnoreCase("mov") && !fext.equalsIgnoreCase("mp4") && !fext.equalsIgnoreCase("asf")) {
                                 cmsContentFile.setIs_streaming("N");
                              } else {
                                 cmsContentFile.setIs_streaming("Y");
                              }

                              if (cmsDao.isExistFileByHash(fileName, fileSize, hashId)) {
                                 bExistFile = true;
                              }

                              ContentFile cmsSFIContentFile;
                              if (bExistFile) {
                                 cmsSFIContentFile = cmsDao.getMainFileInfo(contentId);
                                 if (cmsSFIContentFile == null) {
                                    cmsSFIContentFile = cmsDao.getMainFileInfoOfTmpVer(contentId);
                                 }

                                 if (cmsSFIContentFile != null) {
                                    if (cmsSFIContentFile.getFile_name().equals(cmsContentFile.getFile_name()) && cmsSFIContentFile.getFile_size().equals(cmsContentFile.getFile_size()) && cmsSFIContentFile.getHash_code().equalsIgnoreCase(cmsContentFile.getHash_code())) {
                                       bMustAddContent = false;
                                    } else {
                                       bMustAddContent = true;
                                    }
                                 }
                              } else {
                                 bMustAddContent = true;
                              }

                              if (bExistFile) {
                                 file.delete();
                                 fileID = cmsDao.getFileIDByHash(fileName, fileSize, hashId);
                                 file = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separatorChar + fileID + File.separatorChar + fileName, (String)null));
                              }

                              cmsSFIContentFile = new ContentFile();
                              if (mediaType.equalsIgnoreCase("OFFICE") || mediaType.equalsIgnoreCase("FLASH")) {
                                 Map data = new HashMap();
                                 data.put("startPage", startPage);
                                 data.put("fileSize", fileSize);
                                 cmsSFIContentFile = ContentUtils.createSfiFile(data, userId, contentId, hashId, fileID, fileName);
                              }

                              cmsContentFile.setFile_type("MAIN");
                              cmsContentFile.setFile_id(fileID);
                              cmsContentFile.setHash_code(hashId);
                              cmsContentFile.setFile_name(fileName);
                              cmsContentFile.setFile_size(fileSize);
                              cmsContentFile.setCreator_id(userId);
                              cmsContentFile.setFile_path(CONTENTS_HOME + File.separator + fileID);
                              codeDao.getFileTypeListByFileType(fext.toUpperCase(), mediaType);
                              Content content = new Content();
                              content.setVersion_id(1L);
                              content.setContent_id(contentId);
                              content.setGroup_id(Long.valueOf(groupId));
                              content.setShare_flag(1);
                              content.setContent_meta_data("");
                              content.setCreator_id(userId);
                              content.setMedia_type(mediaType);
                              content.setOrganization_id(uInfo.getRootGroupIdByUserId(userId));
                              content.setTotal_size(fileSize);
                              if (!cmsSFIContentFile.getFile_id().equals("")) {
                                 content.setSfi_file_id(cmsSFIContentFile.getFile_id());
                              }

                              content.setIs_active("Y");
                              content.setOrg_creator_id(String.valueOf(orgCreatorId));
                              ContentFile cmsThumbFile = null;
                              this.logger.error("[ContentFileUploadServlet] content name : " + content.getContent_name() + " content Media Type : " + content.getMedia_type());
                              if (content.getMedia_type().equalsIgnoreCase("LFD") || content.getMedia_type().equalsIgnoreCase("LFT") || content.getMedia_type().equalsIgnoreCase("VWL") || content.getMedia_type().equalsIgnoreCase("PROM")) {
                                 doNotSupportContentList.add(contentName);
                                 break;
                              }

                              File fileCmsHome;
                              if (content.getMedia_type().equalsIgnoreCase("SOUND")) {
                                 content.setThumb_file_id("SOUND_THUMBNAIL");
                                 content.setThumb_file_name("SOUND_THUMBNAIL.PNG");
                                 String[] fileMeta;
                                 String play_time;
                                 if (fext.equals("MP3")) {
                                    try {
                                       fileMeta = fileManager.getFileMeta(file);
                                       if (fileMeta[0] != null) {
                                          play_time = fileMeta[0];
                                          if (play_time.length() > 8) {
                                             content.setPlay_time(play_time.substring(0, 8));
                                             content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                                          } else {
                                             content.setPlay_time(play_time);
                                          }
                                       } else {
                                          play_time = fileManager.getMP3PlayTime(file);
                                          if (play_time.length() > 8) {
                                             content.setPlay_time(play_time.substring(0, 8));
                                             content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                                          } else {
                                             content.setPlay_time(play_time);
                                          }
                                       }
                                    } catch (Exception var130) {
                                       content.setPlay_time("");
                                    }
                                 } else {
                                    fileMeta = fileManager.getFileMeta(file);
                                    if (fileMeta[0] != null) {
                                       play_time = fileMeta[0];
                                       if (play_time.length() > 8) {
                                          content.setPlay_time(play_time.substring(0, 8));
                                          content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                                       } else {
                                          content.setPlay_time(play_time);
                                       }
                                    } else {
                                       content.setPlay_time("");
                                    }
                                 }
                              } else if (!content.getMedia_type().equalsIgnoreCase("IMAGE") && !content.getMedia_type().equalsIgnoreCase("MOVIE")) {
                                 if (content.getMedia_type().equalsIgnoreCase("OFFICE")) {
                                    content.setThumb_file_id("OFFICE_THUMBNAIL");
                                    content.setThumb_file_name("OFFICE_THUMBNAIL.PNG");
                                 } else if (content.getMedia_type().equalsIgnoreCase("FLASH")) {
                                    content.setThumb_file_id("FLASH_THUMBNAIL");
                                    content.setThumb_file_name("FLASH_THUMBNAIL.PNG");
                                 } else if (content.getMedia_type().equalsIgnoreCase("PDF")) {
                                    content.setThumb_file_id("PDF_THUMBNAIL");
                                    content.setThumb_file_name("PDF_THUMBNAIL.PNG");
                                 } else {
                                    content.setThumb_file_id("ETC_THUMBNAIL");
                                    content.setThumb_file_name("ETC_THUMBNAIL.PNG");
                                 }
                              } else {
                                 cmsThumbFile = new ContentFile();
                                 thumbnailFileId = UUID.randomUUID().toString().toUpperCase();
                                 thumbnailMap = fileManager.createThumbnailFile(file, thumbnailFileId, content);
                                 if (thumbnailMap != null && thumbnailMap.get("status") != null && thumbnailMap.get("status").equals("error")) {
                                    throw new Exception("error create thumbnail");
                                 }

                                 fileCmsHome = null;
                                 if (thumbnailMap != null && thumbnailMap.get("file") != null) {
                                    fileCmsHome = (File)thumbnailMap.get("file");
                                 }

                                 filePath = FileUtils.getHash(fileCmsHome);
                                 if (filePath == null) {
                                    content.setThumb_file_id("NOIMAGE_THUMBNAIL");
                                    content.setThumb_file_name("NOIMAGE_THUMBNAIL.PNG");
                                 } else {
                                    if (cmsDao.isExistFileByHash(fileName + ".png", fileCmsHome.length(), filePath)) {
                                       thumbnailFileId = cmsDao.getFileIDByHash(fileName + ".png", fileCmsHome.length(), filePath);
                                    }

                                    content.setThumb_file_id(thumbnailFileId);
                                    content.setThumb_file_name(fileName + ".png");
                                 }

                                 cmsThumbFile.setFile_id(thumbnailFileId);
                                 cmsThumbFile.setHash_code(filePath);
                                 cmsThumbFile.setFile_type("THUMBNAIL");
                                 cmsThumbFile.setFile_name(fileName + ".png");
                                 cmsThumbFile.setFile_size(fileCmsHome.length());
                                 cmsThumbFile.setCreator_id(userId);
                                 cmsThumbFile.setFile_path(CONTENTS_HOME + File.separator + thumbnailFileId);
                                 if (thumbnailMap != null && thumbnailMap.get("resolution") != null) {
                                    content.setResolution((String)thumbnailMap.get("resolution"));
                                 }

                                 if (thumbnailMap != null && thumbnailMap.get("playTime") != null) {
                                    String play_time = (String)thumbnailMap.get("playTime");
                                    if (play_time.length() > 8) {
                                       content.setPlay_time(play_time.substring(0, 8));
                                       content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                                    } else {
                                       content.setPlay_time(play_time);
                                    }
                                 }
                              }

                              if (contentName.length() > 100) {
                                 contentName = contentName.substring(0, 100);
                              }

                              content.setContent_name(contentName);
                              content.setMain_file_id(fileID);
                              content.setMain_file_Extension(fext.toUpperCase());
                              if (contentsApprovalEnable) {
                                 if (content.getMain_file_Extension().equalsIgnoreCase("LFT")) {
                                    content.setApproval_status("APPROVED");
                                 } else {
                                    AbilityUtils abilityUtils = new AbilityUtils();
                                    if (abilityUtils.isContentApprovalAuthority(userId)) {
                                       content.setApproval_status("APPROVED");
                                    } else {
                                       content.setApproval_status("UNAPPROVED");
                                    }
                                 }
                              } else {
                                 content.setApproval_status("APPROVED");
                              }

                              List fileListToSave = new ArrayList();
                              fileListToSave.add(cmsContentFile);
                              if (cmsThumbFile != null) {
                                 fileListToSave.add(cmsThumbFile);
                              }

                              if (mediaType.equalsIgnoreCase("OFFICE") || mediaType.equalsIgnoreCase("FLASH")) {
                                 fileListToSave.add(cmsSFIContentFile);
                              }

                              content.setArr_file_list(fileListToSave);
                              if (categoryId != null) {
                                 CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
                                 categoryInfo.setCategoryFromContentId(categoryId, contentId);
                              }

                              cmsDao.addContent(content);

                              try {
                                 fileCmsHome = SecurityUtils.getSafeFile(THUMBNAIL_HOME);
                                 if (!fileCmsHome.exists()) {
                                    boolean fSuccess = fileCmsHome.mkdir();
                                    if (!fSuccess) {
                                       this.logger.error("mkdir Fail");
                                    }
                                 }

                                 filePath = THUMBNAIL_HOME + File.separator + content.getThumb_file_id();
                                 File fileCmsFile = SecurityUtils.getSafeFile(filePath);
                                 if (!fileCmsFile.exists()) {
                                    boolean fSuccess = fileCmsFile.mkdir();
                                    if (!fSuccess) {
                                       this.logger.error("mkdir Fail");
                                    }
                                 }

                                 image_url = CONTENTS_HOME + "/" + content.getThumb_file_id() + "/" + content.getThumb_file_name();
                                 String thumb_url = THUMBNAIL_HOME + "/" + content.getThumb_file_id() + "/" + content.getThumb_file_name();
                                 checkFile = SecurityUtils.getSafeFile(thumb_url);
                                 if (!checkFile.exists()) {
                                    File thumbFile = SecurityUtils.getSafeFile(image_url);
                                    if (thumbFile.exists()) {
                                       BufferedImage bufferedImage = ImageIO.read(thumbFile);
                                       if (bufferedImage == null) {
                                          this.logger.error("[ContentFileUploadServlet] null thumbnail image : " + thumbFile.getPath());
                                          throw new NullPointerException();
                                       }

                                       int orgWidth = bufferedImage.getWidth();
                                       int orgHeight = bufferedImage.getHeight();
                                       int smallWidth = 50;
                                       int smallHeight = 38;
                                       int mediumWidth = 165;
                                       mediumHeight = 109;
                                       if ((long)(orgWidth / mediumWidth) > (long)(orgHeight / mediumHeight)) {
                                          mediumHeight = orgHeight * mediumWidth / orgWidth;
                                          if (mediumHeight % 2 != 0) {
                                             ++mediumHeight;
                                          }
                                       } else {
                                          mediumWidth = orgWidth * mediumHeight / orgHeight;
                                          if (mediumWidth % 2 != 0) {
                                             ++mediumWidth;
                                          }
                                       }

                                       if ((long)(orgWidth / smallWidth) > (long)(orgHeight / smallHeight)) {
                                          smallHeight = orgHeight * smallWidth / orgWidth;
                                          if (smallHeight % 2 != 0) {
                                             ++smallHeight;
                                          }
                                       } else {
                                          smallWidth = orgWidth * smallHeight / orgHeight;
                                          if (smallWidth % 2 != 0) {
                                             ++smallWidth;
                                          }
                                       }

                                       if (mediumWidth < 1) {
                                          mediumWidth = 1;
                                       }

                                       if (mediumHeight < 1) {
                                          mediumHeight = 1;
                                       }

                                       if (smallWidth < 1) {
                                          smallWidth = 1;
                                       }

                                       if (smallHeight < 1) {
                                          smallHeight = 1;
                                       }

                                       thumb_File = SecurityUtils.getSafeFile(thumb_url);
                                       File thumb_smallFile = SecurityUtils.getSafeFile(thumb_url + "_SMALL.PNG");
                                       File thumb_mediumFile = SecurityUtils.getSafeFile(thumb_url + "_MEDIUM.PNG");
                                       ImageIO.write(fileManager.createResizedCopy(bufferedImage, orgWidth, orgHeight), "PNG", thumb_File);
                                       ImageIO.write(fileManager.createResizedCopy(bufferedImage, smallWidth, smallHeight), "PNG", thumb_smallFile);
                                       ImageIO.write(fileManager.createResizedCopy(bufferedImage, mediumWidth, mediumHeight), "PNG", thumb_mediumFile);
                                       Map hdThumbnailMap = fileManager.createThumbnailFile(file, THUMBNAIL_HOME, thumbnailFileId, content, 1280, 720, "_HD.PNG");
                                       if (hdThumbnailMap != null && hdThumbnailMap.get("status") != null && hdThumbnailMap.get("status").equals("error")) {
                                          this.logger.error("HD Thumbnail create error.");
                                       }
                                    }
                                 }
                              } catch (Exception var147) {
                                 this.logger.error("[ContentFileUploadServlet] error create thumbnail");
                                 throw new Exception("error create thumbnail!");
                              }

                              cmsDao.setActiveVersion(contentId, true);
                              JSONObject jsono = new JSONObject();
                              jsono.put("name", item.getName());
                              jsono.put("size", item.getSize());
                              jsono.put("contentId", content.getContent_id());
                              json.put(jsono);
                              cmsDao.setContentInfo(content.getContent_id(), content.getContent_name(), content.getContent_meta_data(), content.getShare_flag());
                              cmsDao.setContentGroup(content.getContent_id(), content.getGroup_id());
                           }
                        }
                     }
                  }
               }
            }

            if (doNotSupportContentList != null && doNotSupportContentList.size() > 0) {
               JSONObject jsono = new JSONObject();
               jsono.put("message", "error");
               jsono.put("doNotSupportContentList", doNotSupportContentList);
               json.put(jsono);
            }
         } catch (Exception var149) {
            JSONObject jsono = new JSONObject();
            jsono.put("message", "error");
            json.put(jsono);
            this.logger.error("[MagicInfo_ContentUpload][" + userId + "] fail file upload! message : " + var149.getMessage(), var149);
         } finally {
            writer.write(json.toString());
            writer.close();
         }

      }
   }

   private boolean supportContentType(String fext) {
      return fexts.contains(fext.toLowerCase());
   }
}
