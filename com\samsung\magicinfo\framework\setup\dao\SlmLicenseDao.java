package com.samsung.magicinfo.framework.setup.dao;

import com.samsung.common.db.PagedListInfo;
import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.setup.entity.CompanyInfoEntity;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseEntity;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseHistoryEntity;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseOrgEntity;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseStatusEntity;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class SlmLicenseDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(SlmLicenseDao.class);
   String[] productCodes = new String[]{"01015A", "01064A"};
   Map licenseTypeByProductCode = new HashMap();

   public SlmLicenseDao() {
      super();
      List licenseTypeForNewUniversal = new ArrayList();
      licenseTypeForNewUniversal.add("11");
      List licenseTypeForRms = new ArrayList();
      licenseTypeForRms.add("11");
      this.licenseTypeByProductCode.put("01015A", licenseTypeForNewUniversal);
      this.licenseTypeByProductCode.put("01064A", licenseTypeForRms);
   }

   public boolean addSlmLicenseInfo(SlmLicenseEntity slmlicense) throws SQLException {
      Timestamp currentTime = new Timestamp(Calendar.getInstance().getTimeInMillis());
      Integer retValue = ((SlmLicenseDaoMapper)this.getMapper()).addSlmLicenseInfo(slmlicense, currentTime);
      if (retValue > 0) {
         this.logger.debug("Log_License sql : insert success");
      } else {
         this.logger.error("Log_License sql : insert fail");
      }

      return retValue > 0;
   }

   public boolean addSlmLicenseInfoForE2E(SlmLicenseEntity slmlicense, String deviceId) throws SQLException {
      Timestamp currentTime = new Timestamp(Calendar.getInstance().getTimeInMillis());
      boolean retValue = ((SlmLicenseDaoMapper)this.getMapper()).addSlmLicenseInfoForE2E(slmlicense, currentTime, deviceId);
      if (retValue) {
         this.logger.debug("Log_License sql : insert success deviceId = " + deviceId);
      } else {
         this.logger.error("Log_License sql : insert fail deviceId = " + deviceId);
      }

      return retValue;
   }

   public boolean updateSlmLicenseInfo(SlmLicenseEntity slmlicense) throws SQLException {
      boolean cnt = ((SlmLicenseDaoMapper)this.getMapper()).updateSlmLicenseInfo(slmlicense);
      if (cnt) {
         this.logger.error("Log_License sql : insert success.");
      } else {
         this.logger.error("Log_License sql : insert fail.");
      }

      return cnt;
   }

   public boolean updateSlmLicenseInfoForE2E(SlmLicenseEntity slmlicense) throws SQLException {
      boolean cnt = ((SlmLicenseDaoMapper)this.getMapper()).updateSlmLicenseInfoForE2E(slmlicense);
      if (cnt) {
         this.logger.error("Log_License sql : insert success.");
      } else {
         this.logger.error("Log_License sql : insert fail.");
      }

      return cnt;
   }

   public boolean extendSlmLicenseInfoForE2E(SlmLicenseEntity slmlicense) throws Exception {
      boolean cnt = ((SlmLicenseDaoMapper)this.getMapper()).extendSlmLicenseInfoForE2E(slmlicense);
      if (cnt) {
         this.logger.error("extend_License sql : insert success. " + slmlicense.getDevice_id());
      } else {
         this.logger.error("extend_License sql : insert fail. " + slmlicense.getDevice_id());
      }

      return cnt;
   }

   public String getActivationKey(String licenseKey) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getActivationKey(licenseKey);
   }

   public boolean deleteLicenseInfo(String license_key) throws SQLException {
      Integer res = ((SlmLicenseDaoMapper)this.getMapper()).deleteLicenseInfo(license_key);
      if (res > 0) {
         this.logger.debug(" Log_License sql : Delete, license_key=" + license_key);
         return true;
      } else {
         this.logger.error(" Log_License sql : Failed to delete, license_key=" + license_key);
         return false;
      }
   }

   public boolean deleteMappingInfoOfSlmLicenseOrg(String productCode) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).deleteMappingInfoOfSlmLicenseOrg(productCode);
   }

   public boolean deleteLicenseInfoForE2EByDeviceId(String deviceId) throws SQLException {
      Integer res = ((SlmLicenseDaoMapper)this.getMapper()).deleteLicenseInfoForE2EByDeviceId(deviceId);
      if (res > 0) {
         this.logger.debug(" Log_License sql : Delete, license_key=" + deviceId);
         return true;
      } else {
         this.logger.error(" Log_License sql : Failed to delete, license_key=" + deviceId);
         return false;
      }
   }

   public boolean deleteAllLicense() throws SQLException {
      boolean cnt = ((SlmLicenseDaoMapper)this.getMapper()).deleteAllLicense();
      if (cnt) {
         this.logger.error(" Log_License sql : Delete error");
      }

      return cnt;
   }

   public PagedListInfo getLicenseList(int startPos, int pageSize, Map condition) throws SQLException {
      List result = null;
      int totCnt = false;
      String sort = (String)condition.get("sort");
      String dir = (String)condition.get("order");
      if (sort != null && !sort.equals("") && dir != null && !dir.equals("")) {
         sort = sort.toUpperCase();
         dir = dir.toUpperCase();
      }

      --startPos;
      result = ((SlmLicenseDaoMapper)this.getMapper()).getLicenseList(startPos, pageSize, sort, dir);
      int totCnt = ((SlmLicenseDaoMapper)this.getMapper()).getLicenseListCount();
      return new PagedListInfo(result, totCnt);
   }

   public PagedListInfo getLicenseListForE2E(int startPos, int pageSize, Map condition) throws SQLException {
      List result = null;
      int totCnt = false;
      String sort = (String)condition.get("sort");
      String dir = (String)condition.get("order");
      String searchText = (String)condition.get("search");
      if (sort != null && !sort.equals("") && dir != null && !dir.equals("")) {
         sort = sort.toUpperCase();
         dir = dir.toUpperCase();
      }

      if (searchText != null && !searchText.equals("")) {
         searchText = searchText.toUpperCase();
      }

      --startPos;
      result = ((SlmLicenseDaoMapper)this.getMapper()).getLicenseListForE2E(startPos, pageSize, sort, dir, searchText);
      int totCnt = ((SlmLicenseDaoMapper)this.getMapper()).getLicenseListCountForE2E();
      return new PagedListInfo(result, totCnt);
   }

   public SlmLicenseEntity getSlmLicense(String licenseKey) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getSlmLicense(licenseKey);
   }

   public SlmLicenseEntity getSlmLicenseForE2E(String licenseKey) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getSlmLicenseForE2E(licenseKey);
   }

   public SlmLicenseEntity getSlmLicenseForE2EByDeviceId(String deviceId) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getSlmLicenseForE2EByDeviceId(deviceId);
   }

   public List getAllSlmlLicense() throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getAllSlmlLicense();
   }

   public List getAllSlmlLicenseForE2E() throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getAllSlmlLicenseForE2E();
   }

   public List getProductCodeSlmLicense(String productCode) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getProductCodeSlmLicense(productCode);
   }

   public int getCntSlmLicenseByProductCode(String productCode) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getCntSlmLicenseByProductCode(productCode);
   }

   public List getCntSlmLicenseByProductCode(String productCode, String licenseType) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getCntSlmLicensebyProductCodeAndLicenseType(productCode, licenseType);
   }

   public boolean addDeviceSamples(String mac, int num) throws SQLException {
      ((SlmLicenseDaoMapper)this.getMapper()).addDeviceSamples(mac, num);
      return ((SlmLicenseDaoMapper)this.getMapper()).addDeviceSamplesSql2(mac);
   }

   public boolean addSocDeviceSamples(String mac, int num) throws SQLException {
      ((SlmLicenseDaoMapper)this.getMapper()).addSocDeviceSamples(mac, num);
      return ((SlmLicenseDaoMapper)this.getMapper()).addSocDeviceSamplesSql2(mac);
   }

   public boolean addLiteDeviceSamples(String mac, int num) throws SQLException {
      ((SlmLicenseDaoMapper)this.getMapper()).addLiteDeviceSamples(mac, num);
      return ((SlmLicenseDaoMapper)this.getMapper()).addLiteDeviceSamplesSql2(mac);
   }

   public boolean TestAddLicenseKey(String slmLicenseKey, String activationKey) throws SQLException {
      SlmLicenseEntity slmlicense = new SlmLicenseEntity();
      slmlicense.setActivation_key(activationKey);
      slmlicense.setLicense_key(slmLicenseKey);
      slmlicense.setLicense_type("11");
      slmlicense.setMax_clients(100L);
      slmlicense.setProduct_code("11111");
      slmlicense.setProduct_name("test");
      return ((SlmLicenseDaoMapper)this.getMapper()).TestAddLicenseKey(slmlicense);
   }

   public String getHwUniqueKey() throws SQLException {
      Map rtn = ((SlmLicenseDaoMapper)this.getMapper()).getHwUniqueKey();
      return rtn == null ? null : (String)rtn.get("hwunique_key");
   }

   public int setHwUniqueKey(String hwuniqueKey) {
      short rtn = 0;

      try {
         ((SlmLicenseDaoMapper)this.getMapper()).setHwUniqueKey(hwuniqueKey);
      } catch (SQLException var4) {
         rtn = 500;
         this.logger.error("", var4);
      }

      return rtn;
   }

   public void setSlmLicenseHistory(SlmLicenseHistoryEntity licenseHistory) throws SQLException {
      Timestamp currentTime = new Timestamp(Calendar.getInstance().getTimeInMillis());
      ((SlmLicenseDaoMapper)this.getMapper()).setSlmLicenseHistory(licenseHistory, currentTime);
   }

   public List getAllSlmLicenseHistory() throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getAllSlmLicenseHistory();
   }

   public List getAllSlmLicenseHistory(int startPoint) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getAllSlmLicenseHistoryByPageNumber(25, startPoint);
   }

   public int getCntSlmLicenseHistory() {
      try {
         return ((SlmLicenseDaoMapper)this.getMapper()).getCntSlmLicenseHistory();
      } catch (SQLException var2) {
         return 0;
      }
   }

   public SlmLicenseHistoryEntity getSlmLicenseHistory(String licenseKey) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getSlmLicenseHistory(licenseKey);
   }

   public int getCntAllSlmLicenseHistoryForE2E() throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getCntAllSlmLicenseHistoryForE2E();
   }

   public List getAllSlmLicenseHistoryForE2E(int startPoint) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getAllSlmLicenseHistoryForE2E(10, startPoint);
   }

   public int getCntSlmLicenseHistoryForE2EByDeviceId(String deviceId) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getCntSlmLicenseHistoryForE2EByDeviceId(deviceId);
   }

   public List getSlmLicenseHistoryForE2EByDeviceId(String deviceId, int startPoint) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getSlmLicenseHistoryForE2EByDeviceId(deviceId, 10, startPoint);
   }

   public int getCntSlmlicense() throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getCntSlmlicense();
   }

   public int getLicenseFromProductAndType(String productCode, String license_type) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getLicenseFromProductAndType(productCode, license_type);
   }

   public boolean setValid(String licenseKey, boolean state) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).setValid(licenseKey, state);
   }

   public boolean addLicenseStatus(SlmLicenseStatusEntity status) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).addLicenseStatus(status);
   }

   public List getListLicenseStatus() throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getListLicenseStatus();
   }

   public int chkLicenseStatus(String productCode) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).chkLicenseStatus(productCode);
   }

   public boolean delLicenseStatus(String product_code) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).delLicenseStatus(product_code);
   }

   public SlmLicenseStatusEntity getLicenseStaus(String productCode) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getLicenseStaus(productCode);
   }

   public List getCntSlmLicensebyProductCodeList(List productCode) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getCntSlmLicensebyProductCodeList(productCode);
   }

   public CompanyInfoEntity getCompanyInfo() throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getCompanyInfo();
   }

   public boolean insertCompanyInfo(CompanyInfoEntity companyInfoEntity) throws SQLException {
      ((SlmLicenseDaoMapper)this.getMapper()).deleteCompanyInfo();
      return ((SlmLicenseDaoMapper)this.getMapper()).insertCompanyInfo(companyInfoEntity);
   }

   public void setSlmLicenseHistoryForE2E(SlmLicenseHistoryEntity licenseHistory) throws SQLException {
      Timestamp currentTime = new Timestamp(Calendar.getInstance().getTimeInMillis());
      ((SlmLicenseDaoMapper)this.getMapper()).setSlmLicenseHistoryForE2E(licenseHistory, currentTime);
   }

   public boolean addDeviceTypeListByProductCode(String productCode, String productName, String deviceTypeList) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).addDeviceTypeListByProductCode(productCode, productName, deviceTypeList);
   }

   public boolean updateDeviceTypeListByProductCode(String productCode, String deviceTypeList) throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).updateDeviceTypeListByProductCode(productCode, deviceTypeList);
   }

   public List getLicenseInfoAssignedToOrganization() throws SQLException {
      return ((SlmLicenseDaoMapper)this.getMapper()).getLicenseInfoAssignedToOrganization();
   }

   public boolean editLicenseInfoAssignedToOrganization(List slmLicenseOrgEntities) throws SQLException {
      SqlSession sqlSession = this.openNewSession(false);
      SlmLicenseDaoMapper slmLicenseDaoMapper = (SlmLicenseDaoMapper)this.getMapper(sqlSession);

      try {
         Iterator var4 = slmLicenseOrgEntities.iterator();

         while(var4.hasNext()) {
            SlmLicenseOrgEntity slmLicenseOrgEntity = (SlmLicenseOrgEntity)var4.next();
            if (!slmLicenseOrgEntity.isAssigned()) {
               slmLicenseDaoMapper.deleteMaxLicenseCount(slmLicenseOrgEntity.getProduct_code(), slmLicenseOrgEntity.getOrganization_id());
            } else {
               slmLicenseDaoMapper.updateMaxLicenseCount(slmLicenseOrgEntity.getProduct_code(), slmLicenseOrgEntity.getOrganization_id(), slmLicenseOrgEntity.getMax_license_count());
               slmLicenseDaoMapper.addOrgSlmLicenseInfo(slmLicenseOrgEntity.getProduct_code(), slmLicenseOrgEntity.getOrganization_id(), slmLicenseOrgEntity.getMax_license_count());
            }
         }

         slmLicenseDaoMapper.getLicenseInfoAssignedToOrganization();
         if (!this.verifyAssignedLicenseInfo(slmLicenseDaoMapper)) {
            sqlSession.rollback();
            throw new RuntimeException();
         }

         sqlSession.commit();
      } catch (SQLException var9) {
         sqlSession.rollback();
         throw var9;
      } finally {
         sqlSession.close();
      }

      return true;
   }

   private boolean verifyAssignedLicenseInfo(SlmLicenseDaoMapper slmLicenseDaoMapper) throws SQLException {
      List licenseInfoAssignedToOrganization = slmLicenseDaoMapper.getLicenseInfoAssignedToOrganization();
      Map numAssignedLicense = new HashMap();
      Map numUsingCommonLicense = new HashMap();
      String[] var5 = this.productCodes;
      int var6 = var5.length;

      int var7;
      String productCode;
      for(var7 = 0; var7 < var6; ++var7) {
         productCode = var5[var7];
         numAssignedLicense.put(productCode, 0L);
         numUsingCommonLicense.put(productCode, 0L);
      }

      Iterator var14 = licenseInfoAssignedToOrganization.iterator();

      while(var14.hasNext()) {
         SlmLicenseOrgEntity slmLicenseOrgEntity = (SlmLicenseOrgEntity)var14.next();
         long max_license_count = slmLicenseOrgEntity.getMax_license_count();
         long used_license_count = slmLicenseOrgEntity.getUsed_license_count();
         String productCode = slmLicenseOrgEntity.getProduct_code();
         Long sumAssignedLicense = (Long)numAssignedLicense.get(productCode);
         sumAssignedLicense = sumAssignedLicense + (max_license_count > 0L ? max_license_count : 0L);
         numAssignedLicense.put(productCode, sumAssignedLicense);
         Long sumUsingCommonLicense = (Long)numUsingCommonLicense.get(productCode);
         sumUsingCommonLicense = sumUsingCommonLicense + (max_license_count > 0L ? max_license_count : used_license_count);
         numUsingCommonLicense.put(productCode, sumUsingCommonLicense);
      }

      var5 = this.productCodes;
      var6 = var5.length;

      for(var7 = 0; var7 < var6; ++var7) {
         productCode = var5[var7];
         int cntSlmLicenseByProductCodeForAssigningLicenseEachOrganization = slmLicenseDaoMapper.getCntSlmLicenseByProductCodeForAssigningLicenseEachOrganization(productCode, (List)this.licenseTypeByProductCode.get(productCode));
         int cntSlmLicenseByProductCode = this.getCntSlmLicenseByProductCode(productCode);
         if ((long)cntSlmLicenseByProductCodeForAssigningLicenseEachOrganization < (Long)numAssignedLicense.get(productCode) || (long)cntSlmLicenseByProductCode < (Long)numUsingCommonLicense.get(productCode)) {
            return false;
         }
      }

      return true;
   }
}
