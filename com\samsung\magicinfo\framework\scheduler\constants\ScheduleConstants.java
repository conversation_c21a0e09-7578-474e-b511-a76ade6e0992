package com.samsung.magicinfo.framework.scheduler.constants;

import java.util.HashMap;
import java.util.Map;

public class ScheduleConstants {
   public static final String LFD_PROGRAM_TYPE = "00";
   public static final String VIDEOWALL_PROGRAM_TYPE = "01";
   public static final String CONTENT_SCHEDULE_TYPE = "00";
   public static final String PANELOFF_SCHEDULE_TYPE = "01";
   public static final String ZEROFRAME_SCHEDULE_TYPE = "02";
   public static final String HWCONTROL_SCHEDULE_TYPE = "03";
   public static final String HWCONTROL_SCH_CHANNEL_TYPE = "04";
   public static final String EVENT_SCHEDULE_TYPE = "EVENTSCHEDULE";
   public static final String REPEAT_TYPE_ONCE = "once";
   public static final String REPEAT_TYPE_DAILY = "daily";
   public static final String REPEAT_TYPE_WEEKLY = "weekly";
   public static final String REPEAT_TYPE_MONTHLY = "monthly";
   public static final String REPEAT_TYPE_DAYOFMONTH = "day_of_month";
   public static final String REPEAT_TYPE_DAYOFWEEK = "day_of_week";
   public static final String REPEAT_TYPE_PERIOD = "period";
   public static final String LOG_EVENT_TYPE_CREATE = "create";
   public static final String LOG_EVENT_TYPE_MODIFY = "modify";
   public static final String LOG_EVENT_TYPE_DELETE = "delete";
   public static final String LOG_PROGRAM_CREATE = "00";
   public static final String LOG_PROGRAM_EDIT = "01";
   public static final String LOG_PROGRAM_DELETE = "02";
   public static final String LOG_CONTENT_SCHEDULE_EDIT = "03";
   public static final String LOG_PROGRAM_DELETE_PERMANENTLY = "04";
   public static final String EVENT_NAME_CONTENT_SCHEDULE_RESTORE = "Content Schedule Restore";
   public static final String LOG_PROGRAM_AUTO_TRIGGER = "05";
   public static final int DEFAULT_CHANNEL_NO = 1;
   public static final int MAX_CHANNEL_NO = 99;
   public static final String PLAYER_MODE_SINGLE = "single";
   public static final String PLAYER_MODE_VWL = "vwl";
   public static final String DEFAULT_CHANNEL_NAME = "Channel";
   public static final String DEFAULT_PROGRAM_NAME = "Default Program";
   public static final int CONTENT_TRIGGER_SCHEDULE = 1;
   public static final int PLAYLIST_TRIGGER_SCHEDULE = 2;
   public static final String DOWNLOAD_STATUS_BY_DEVICE = "DOWNLOAD_STATUS_BY_DEVICE";
   public static final String INIT_DOWNLOAD_FROM_DOWNLOAD_ACTIVITY = "INIT_DOWNLOAD_FROM_DOWNLOAD_ACTIVITY";
   public static final String INIT_DOWNLOAD_FROM_NEW_OR_EDIT_SCHEDULE = "INIT_DOWNLOAD_FROM_NEW_OR_EDIT_SCHEDULE";
   public static final String INIT_DOWNLOAD_FROM_RE_PUBLISH = "INIT_DOWNLOAD_FROM_RE_PUBLISH";
   public static final String DOWNLOAD_STATUS_WAITING = "WAITING";

   public ScheduleConstants() {
      super();
   }

   public static Map getConstantsMap() {
      Map result = new HashMap();
      result.put("REPEAT_TYPE_DAYOFMONTH", "day_of_month");
      result.put("REPEAT_TYPE_DAYOFWEEK", "day_of_week");
      return result;
   }
}
