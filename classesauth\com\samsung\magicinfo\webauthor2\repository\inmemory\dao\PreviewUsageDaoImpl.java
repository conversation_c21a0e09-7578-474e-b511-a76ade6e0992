package com.samsung.magicinfo.webauthor2.repository.inmemory.dao;

import com.samsung.magicinfo.webauthor2.repository.inmemory.model.PreviewUsage;
import com.samsung.magicinfo.webauthor2.repository.inmemory.model.PreviewUsageMapper;
import java.util.List;
import javax.inject.Inject;
import org.joda.time.LocalDate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

@Repository
public class PreviewUsageDaoImpl implements PreviewUsageDao {
  private JdbcTemplate jdbcTemplate;
  
  private PreviewUsageMapper mapper = new PreviewUsageMapper();
  
  @Inject
  public PreviewUsageDaoImpl(JdbcTemplate jdbcTemplate) {
    this.jdbcTemplate = jdbcTemplate;
  }
  
  public List<PreviewUsage> findAll() {
    String sql = "SELECT * FROM previewusage";
    List<PreviewUsage> result = this.jdbcTemplate.query(sql, (RowMapper)this.mapper);
    return result;
  }
  
  public List<PreviewUsage> findOlderThanDays(int Days) {
    LocalDate boundry = LocalDate.now().minusDays(Days);
    String sql = "SELECT * FROM previewusage WHERE lastused < ? ";
    List<PreviewUsage> result = this.jdbcTemplate.query(sql, new Object[] { boundry.toString() }, (RowMapper)this.mapper);
    return result;
  }
  
  public void insert(PreviewUsage previewUsage) {
    String sql = "INSERT INTO previewusage (CONTENTID, VERSIONID, LASTUSED, USERID, STARTPAGE, PROGRESS) VALUES (?,?,?,?,?,?)";
    this.jdbcTemplate.update(sql, new Object[] { previewUsage.getContentId(), Integer.valueOf(previewUsage.getVersionId()), previewUsage.getLastused().toString(), previewUsage
          .getUserId(), previewUsage.getStartPage(), Integer.valueOf(previewUsage.getProgress()) });
  }
  
  public void update(PreviewUsage previewUsage) {
    String sql = "UPDATE previewusage SET versionid = ?, lastused = ?, userid = ?, startpage =?, progress=? WHERE contentid = ? ";
    this.jdbcTemplate.update(sql, new Object[] { Integer.valueOf(previewUsage.getVersionId()), previewUsage.getLastused().toString(), previewUsage.getUserId(), previewUsage
          .getStartPage(), Integer.valueOf(previewUsage.getProgress()), previewUsage.getContentId() });
  }
  
  public void delete(String contentId) {
    String sql = "DELETE FROM previewusage WHERE contentid = ?";
    this.jdbcTemplate.update(sql, new Object[] { contentId });
  }
  
  public PreviewUsage findByContentId(String contentId) {
    PreviewUsage result;
    String sql = "SELECT * FROM previewusage WHERE contentid = ?";
    try {
      result = (PreviewUsage)this.jdbcTemplate.queryForObject(sql, new Object[] { contentId }, (RowMapper)this.mapper);
    } catch (Exception e) {
      return null;
    } 
    return result;
  }
}
