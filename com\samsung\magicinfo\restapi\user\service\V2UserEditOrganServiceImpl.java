package com.samsung.magicinfo.restapi.user.service;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserLogManager;
import com.samsung.magicinfo.framework.user.manager.UserLogManagerImpl;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.user.controller.V2UserEditOrganController;
import com.samsung.magicinfo.restapi.user.model.V2OrganSaveResource;
import com.samsung.magicinfo.restapi.user.model.V2OrganSaveWrapper;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import org.apache.logging.log4j.Logger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2UserEditOrganService")
@Transactional
public class V2UserEditOrganServiceImpl implements V2UserEditOrganService {
   protected Logger logger = LoggingManagerV2.getLogger(V2UserEditOrganController.class);

   public V2UserEditOrganServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2OrganSaveResource organSave(String organizationId, V2OrganSaveWrapper body, HttpServletRequest request) throws Exception {
      V2OrganSaveResource resource = new V2OrganSaveResource();
      List savedUserIds = new ArrayList();
      List unSavedUserIds = new ArrayList();
      List userIds = body.getUserIds();
      boolean flag = false;

      for(int i = 0; i < userIds.size(); ++i) {
         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, (String)userIds.get(i));
         } catch (Exception var23) {
            flag = true;
            break;
         }
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER);
      }

      UserLogManager logMgr = UserLogManagerImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      boolean copyContentsToNewOrganization = body.isCopyContentsToNewOrganization();
      String organizationName = userGroupInfo.getGroupNameByGroupId(Long.valueOf(organizationId));
      long organization_id = Long.valueOf(organizationId);
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, organization_id);
      UserInfo userInfo = UserInfoImpl.getInstance();

      for(int i = 0; i < userIds.size(); ++i) {
         try {
            User chUser = userInfo.getAllByUserId((String)userIds.get(i));
            String roleName = chUser.getRole_name();
            String userId = chUser.getUser_id();
            if (userId.equals("admin") || roleName.equals("Administrator")) {
               throw new RestServiceException(RestExceptionCode.FORBIDDEN_ADMINISTRATOR_ORGANIZATION_NOT_MOVE);
            }

            if (chUser.getRoot_group_id() == organization_id) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_NOT_MOVE_TO_CURRENT_ORGANIZATION);
            }

            ContentInfoImpl contentInfo;
            if (copyContentsToNewOrganization) {
               contentInfo = ContentInfoImpl.getInstance();
               contentInfo.copyContents(chUser.getUser_id(), organization_id);
            }

            chUser.setOrganization(organizationName);
            chUser.setRoot_group_id(organization_id);
            chUser.setIs_approved("N");
            chUser.setApproval_type("TEXT_MOVE_ORGAN_P");
            userInfo.setUser(chUser);
            userInfo.setIsApprovedByUserId(chUser.getIs_approved(), chUser.getUser_id());
            contentInfo = ContentInfoImpl.getInstance();
            contentInfo.addDefaultGroup(chUser.getUser_id());
            PlaylistInfo playlintInfo = PlaylistInfoImpl.getInstance();
            playlintInfo.addDefaultGroup(chUser.getUser_id());
            savedUserIds.add(userIds.get(i));
         } catch (Exception var22) {
            unSavedUserIds.add(userIds.get(i));
         }
      }

      resource.setSavedUserIds(savedUserIds);
      resource.setUnSavedUserIds(unSavedUserIds);
      resource.setCopyContentsToNewOrganization(copyContentsToNewOrganization);
      resource.setOrganizationId(Long.valueOf(organizationId));
      return resource;
   }
}
