package com.samsung.magicinfo.webauthor2.repository.datalink.servers;

import com.samsung.magicinfo.webauthor2.exception.repository.CannotGetDataLinkTableInfoException;
import com.samsung.magicinfo.webauthor2.exception.repository.CannotGetDataLinkTablesException;
import com.samsung.magicinfo.webauthor2.model.DataLinkServer;
import com.samsung.magicinfo.webauthor2.model.DataLinkTable;
import com.samsung.magicinfo.webauthor2.repository.model.dlk.DLKTableData;
import com.samsung.magicinfo.webauthor2.repository.model.dlk.DLKTableRowData;
import java.util.List;
import javax.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.web.client.RestTemplate;

@Repository
public class DataLinkRepositoryImpl implements DataLinkRepository {
  private static final Logger logger = LoggerFactory.getLogger(DataLinkRepositoryImpl.class);
  
  private final RestTemplate dataLinkRestTemplate;
  
  @Inject
  public DataLinkRepositoryImpl(@Qualifier("dataLinkRestTemplate") RestTemplate dataLinkRestTemplate) {
    this.dataLinkRestTemplate = dataLinkRestTemplate;
  }
  
  public List<DLKTableData> getDataTableList(DataLinkServer dataLinkServer) {
    GetDataTableListDLKMethod dlkMethod = new GetDataTableListDLKMethod(this.dataLinkRestTemplate, dataLinkServer.getUseSsl(), dataLinkServer.getPrivateMode().booleanValue() ? dataLinkServer.getPrivateIpAddress() : dataLinkServer.getIpAddress(), dataLinkServer.getPrivateMode().booleanValue() ? dataLinkServer.getPrivateWebPort() : dataLinkServer.getPort());
    try {
      return dlkMethod.callMethod();
    } catch (Exception e) {
      logger.error(e.getMessage());
      throw new CannotGetDataLinkTablesException(e.getMessage());
    } 
  }
  
  public List<DLKTableRowData> getDataTableInfo(DataLinkServer dataLinkServer, DataLinkTable dataLinkTable) {
    GetDataTableInfoDLKMethod dlkMethod = new GetDataTableInfoDLKMethod(this.dataLinkRestTemplate, dataLinkServer.getUseSsl(), dataLinkServer.getPrivateMode().booleanValue() ? dataLinkServer.getPrivateIpAddress() : dataLinkServer.getIpAddress(), dataLinkServer.getPrivateMode().booleanValue() ? dataLinkServer.getPrivateWebPort() : dataLinkServer.getPort(), dataLinkTable.getDynaName());
    try {
      return dlkMethod.callMethod();
    } catch (Exception ex) {
      logger.error(ex.getMessage(), ex);
      throw new CannotGetDataLinkTableInfoException(ex.getMessage());
    } 
  }
}
