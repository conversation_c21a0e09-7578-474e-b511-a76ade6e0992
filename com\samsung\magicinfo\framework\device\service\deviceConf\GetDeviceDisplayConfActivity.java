package com.samsung.magicinfo.framework.device.service.deviceConf;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManagerImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocInfo;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocInfoImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManager;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManagerImpl;
import com.samsung.magicinfo.protocol.exception.ServiceException;
import com.samsung.magicinfo.protocol.rmql.RMQL;
import com.samsung.magicinfo.protocol.rmql.RMQLException;
import com.samsung.magicinfo.protocol.rmql.ResultSet;
import com.samsung.magicinfo.protocol.servicemanager.ClientOpActivity;
import com.samsung.magicinfo.protocol.servicestatus.ServiceStatusManager;
import com.samsung.magicinfo.protocol.servicestatus.ServiceStatusManagerImpl;
import com.samsung.magicinfo.protocol.util.RMQLInstanceCreator;
import java.sql.SQLException;
import java.util.HashMap;
import org.apache.logging.log4j.Logger;

public class GetDeviceDisplayConfActivity extends ClientOpActivity {
   Logger logger = LoggingManagerV2.getLogger(GetDeviceDisplayConfActivity.class);
   boolean isLiteDevice = false;

   public GetDeviceDisplayConfActivity() {
      super();
   }

   protected void doPreprocess(HashMap params) {
   }

   protected Object response(ResultSet rs) throws ServiceException {
      try {
         String mode = (String)this.ctxt.getServiceParamMap().get("device_display_view_mode");
         String targetChildIndex = rs.getResultAddress();
         DeviceDisplayConf info = new DeviceDisplayConf();
         if (targetChildIndex == null) {
            info.setDevice_id(this.ctxt.getDevice().getDevice_id());
         } else {
            info.setDevice_id(this.ctxt.getDevice().getDevice_id() + "_" + targetChildIndex);
         }

         int deviceModelCode = 0;
         if (this.ctxt.getDevice() != null && !this.ctxt.getDevice().getDevice_model_name().equalsIgnoreCase("DEFAULT")) {
            deviceModelCode = Integer.parseInt(this.ctxt.getDevice().getDevice_model_code());
         }

         this.isLiteDevice = this.ctxt.getDevice().getDevice_type().equals("LPLAYER");
         DeviceDisplayConfManager displayDao;
         if (mode.toUpperCase().equals("MOBILE_MDC")) {
            if (rs.getString(".MO.DISPLAY_CONF.BASIC.POWER", targetChildIndex) != null) {
               info.setBasic_power(rs.getString(".MO.DISPLAY_CONF.BASIC.POWER"));
            }

            if (rs.getString(".MO.DISPLAY_CONF.BASIC.VOLUME", targetChildIndex) != null) {
               info.setBasic_volume((long)rs.getInt(".MO.DISPLAY_CONF.BASIC.VOLUME", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.BASIC.MUTE", targetChildIndex) != null) {
               info.setBasic_mute((long)rs.getInt(".MO.DISPLAY_CONF.BASIC.MUTE", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.BASIC.SOURCE", targetChildIndex) != null) {
               info.setBasic_source((long)rs.getInt(".MO.DISPLAY_CONF.BASIC.SOURCE", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS", targetChildIndex) != null) {
               info.setBasic_panel_status((long)rs.getInt(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.MODE", targetChildIndex) != null) {
               info.setPv_mode((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.MODE", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SPECIALIZED_PICTURE_MODE") != null) {
               info.setSpecialized_picture_mode((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.SPECIALIZED_PICTURE_MODE", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.CONTRAST", targetChildIndex) != null) {
               info.setPv_contrast((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.CONTRAST", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.BRIGHTNESS", targetChildIndex) != null) {
               info.setPv_brightness((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.BRIGHTNESS", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SHARPNESS", targetChildIndex) != null) {
               info.setPv_sharpness((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.SHARPNESS", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR", targetChildIndex) != null) {
               info.setPv_color((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.TINT", targetChildIndex) != null) {
               info.setPv_tint((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.TINT", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TONE", targetChildIndex) != null) {
               info.setPv_colortone((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TONE", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TEMPERATURE", targetChildIndex) != null) {
               info.setPv_color_temperature((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TEMPERATURE", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.MAGIC_BRIGHT", targetChildIndex) != null) {
               info.setPpc_magic_bright((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.MAGIC_BRIGHT", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_CONTRAST", targetChildIndex) != null) {
               info.setPpc_contrast((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.RGB_CONTRAST", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS", targetChildIndex) != null) {
               info.setPpc_brightness((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PC_COLOR_TONE", targetChildIndex) != null) {
               info.setPpc_colortone((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.PC_COLOR_TONE", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.COLOR_TEMPERATURE", targetChildIndex) != null) {
               info.setPpc_color_temperature((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.COLOR_TEMPERATURE", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_RED", targetChildIndex) != null) {
               info.setPpc_red((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.RGB_RED", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_GREEN", targetChildIndex) != null) {
               info.setPpc_green((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.RGB_GREEN", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BLUE", targetChildIndex) != null) {
               info.setPpc_blue((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BLUE", targetChildIndex));
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PICTURE_SIZE", targetChildIndex) != null) {
               info.setPpc_size((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.PICTURE_SIZE", targetChildIndex));
            }

            displayDao = DeviceDisplayConfManagerImpl.getInstance();
            displayDao.setDeviceDisplayConf(info);
            displayDao.setDeviceDisplayExtConf(info);
         } else if (mode.toUpperCase().equals("ADVANCED_MDC")) {
            if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.AUTO_BRIGHTNESS", targetChildIndex) != null) {
               info.setAuto_brightness(rs.getString(".MO.DISPLAY_CONF.ADVANCED.AUTO_BRIGHTNESS"));
            }

            displayDao = DeviceDisplayConfManagerImpl.getInstance();
            displayDao.setDeviceDisplayConf(info);
         } else {
            if (!mode.toUpperCase().equals("ONLY_EXT_MDC")) {
               info.setBasic_power("1");
               if (rs.getString(".MO.DISPLAY_CONF.BASIC.VOLUME", targetChildIndex) != null) {
                  info.setBasic_volume((long)rs.getInt(".MO.DISPLAY_CONF.BASIC.VOLUME", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.BASIC.MUTE", targetChildIndex) != null) {
                  info.setBasic_mute((long)rs.getInt(".MO.DISPLAY_CONF.BASIC.MUTE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.BASIC.SOURCE", targetChildIndex) != null) {
                  info.setBasic_source((long)rs.getInt(".MO.DISPLAY_CONF.BASIC.SOURCE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.BASIC.WEB_BROWSER_URL", targetChildIndex) != null) {
                  info.setWeb_browser_url(rs.getString(".MO.DISPLAY_CONF.BASIC.WEB_BROWSER_URL", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.BASIC.SCREEN_MUTE", targetChildIndex) != null) {
                  info.setScreen_mute((long)rs.getInt(".MO.DISPLAY_CONF.BASIC.SCREEN_MUTE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.BASIC.FREEZE", targetChildIndex) != null) {
                  info.setScreen_freeze((long)rs.getInt(".MO.DISPLAY_CONF.BASIC.FREEZE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.BASIC.CUSTOM_LOGO", targetChildIndex) != null) {
                  info.setCustom_logo(rs.getString(".MO.DISPLAY_CONF.BASIC.CUSTOM_LOGO", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS", targetChildIndex) != null) {
                  info.setBasic_panel_status((long)rs.getInt(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS", targetChildIndex) != null) {
                  info.setPpc_brightness((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.TIME.CURRENT_TIME", targetChildIndex) != null) {
                  info.setTime_current_time(rs.getString(".MO.DISPLAY_CONF.TIME.CURRENT_TIME"));
               } else if (rs.getString(".MO.DISPLAY_CONF.TIMER.CLOCK", targetChildIndex) != null) {
                  info.setTime_current_time(rs.getString(".MO.DISPLAY_CONF.TIMER.CLOCK"));
               }

               info.setTime_on_time(rs.getString(".MO.DISPLAY_CONF.TIME.ON_TIME"));
               info.setTime_off_time(rs.getString(".MO.DISPLAY_CONF.TIME.OFF_TIME"));
               if (rs.getString(".MO.DISPLAY_CONF.MISC.OSD", targetChildIndex) != null) {
                  info.setMisc_osd((long)rs.getInt(".MO.DISPLAY_CONF.MISC.OSD", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.MISC.OSD_MENU_SIZE", targetChildIndex) != null) {
                  info.setOsd_menu_size((long)rs.getInt(".MO.DISPLAY_CONF.MISC.OSD_MENU_SIZE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.MONITOR_TEMPERATURE", targetChildIndex) != null) {
                  info.setDiagnosis_monitor_temperature(Long.valueOf(rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.MONITOR_TEMPERATURE", targetChildIndex)));
               }

               if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.ALARM_TEMPERATURE", targetChildIndex) != null) {
                  info.setDiagnosis_alarm_temperature(Long.valueOf(rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.ALARM_TEMPERATURE", targetChildIndex)));
               }

               if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.PANEL_ON_TIME", targetChildIndex) != null) {
                  info.setDiagnosis_panel_on_time(rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.PANEL_ON_TIME", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.SENSOR_INTERNAL_TEMPERATURE", targetChildIndex) != null && !"".equals(rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.SENSOR_INTERNAL_TEMPERATURE", targetChildIndex))) {
                  info.setSensor_internal_temperature(Float.valueOf(rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.SENSOR_INTERNAL_TEMPERATURE", targetChildIndex)));
               }

               if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.SENSOR_INTERNAL_HUMIDITY", targetChildIndex) != null && !"".equals(rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.SENSOR_INTERNAL_HUMIDITY", targetChildIndex))) {
                  info.setSensor_internal_humidity(Float.valueOf(rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.SENSOR_INTERNAL_HUMIDITY", targetChildIndex)));
               }

               if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.SENSOR_ENVIRONMENT_TEMPERATURE", targetChildIndex) != null && !"".equals(rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.SENSOR_ENVIRONMENT_TEMPERATURE", targetChildIndex))) {
                  info.setSensor_environment_temperature(Float.valueOf(rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.SENSOR_ENVIRONMENT_TEMPERATURE", targetChildIndex)));
               }

               if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.SENSOR_FRONTGLASS_TEMPERATURE", targetChildIndex) != null && !"".equals(rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.SENSOR_FRONTGLASS_TEMPERATURE", targetChildIndex))) {
                  info.setSensor_frontglass_temperature(Float.valueOf(rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.SENSOR_FRONTGLASS_TEMPERATURE", targetChildIndex)));
               }

               if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.SENSOR_FRONTGLASS_HUMIDITY", targetChildIndex) != null && !"".equals(rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.SENSOR_FRONTGLASS_HUMIDITY", targetChildIndex))) {
                  info.setSensor_frontglass_humidity(Float.valueOf(rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.SENSOR_FRONTGLASS_HUMIDITY", targetChildIndex)));
               }

               if (deviceModelCode > 23 && rs.getString(".MO.DISPLAY_CONF.BASIC.DIRECT_CHANNEL", targetChildIndex) != null) {
                  info.setBasic_direct_channel(rs.getString(".MO.DISPLAY_CONF.BASIC.DIRECT_CHANNEL"));
               }

               displayDao = DeviceDisplayConfManagerImpl.getInstance();
               displayDao.setDeviceDisplayConf(info);
            }

            if (mode.toUpperCase().equals("ONLY_EXT_MDC") || mode.toUpperCase().equals("ALL_MDC")) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.MODE", targetChildIndex) != null) {
                  info.setPv_mode((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.MODE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SPECIALIZED_PICTURE_MODE") != null) {
                  info.setSpecialized_picture_mode((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.SPECIALIZED_PICTURE_MODE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.CONTRAST", targetChildIndex) != null) {
                  info.setPv_contrast((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.CONTRAST", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.BRIGHTNESS", targetChildIndex) != null) {
                  info.setPv_brightness((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.BRIGHTNESS", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SHARPNESS", targetChildIndex) != null) {
                  info.setPv_sharpness((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.SHARPNESS", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR", targetChildIndex) != null) {
                  info.setPv_color((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.TINT", targetChildIndex) != null) {
                  info.setPv_tint((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.TINT", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TONE", targetChildIndex) != null) {
                  info.setPv_colortone((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TONE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TEMPERATURE", targetChildIndex) != null) {
                  info.setPv_color_temperature((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TEMPERATURE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SIZE", targetChildIndex) != null) {
                  info.setPv_size((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.SIZE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.DIGITAL_NR", targetChildIndex) != null) {
                  info.setPv_digitalnr((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.DIGITAL_NR", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.FILM_MODE", targetChildIndex) != null) {
                  info.setPv_filmmode((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.FILM_MODE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.MPEG_NOISE_FILTER", targetChildIndex) != null) {
                  info.setPv_mpeg_noise_filter((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.MPEG_NOISE_FILTER", targetChildIndex));
               }

               if (deviceModelCode > 55 && deviceModelCode < 7000) {
                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.VIDEO_PICTURE_POSITION_SIZE", targetChildIndex) != null) {
                     info.setPv_video_picture_position_size(rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.VIDEO_PICTURE_POSITION_SIZE"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.HDMI_BLACK_LEVEL", targetChildIndex) != null) {
                     info.setPv_hdmi_black_level((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_VIDEO.HDMI_BLACK_LEVEL", targetChildIndex));
                  }
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.MAGIC_BRIGHT", targetChildIndex) != null) {
                  info.setPpc_magic_bright((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.MAGIC_BRIGHT", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_CONTRAST", targetChildIndex) != null) {
                  info.setPpc_contrast((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.RGB_CONTRAST", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS", targetChildIndex) != null) {
                  info.setPpc_brightness((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PC_COLOR_TONE", targetChildIndex) != null) {
                  info.setPpc_colortone((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.PC_COLOR_TONE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.COLOR_TEMPERATURE", targetChildIndex) != null) {
                  info.setPpc_color_temperature((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.COLOR_TEMPERATURE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_RED", targetChildIndex) != null) {
                  info.setPpc_red((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.RGB_RED", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_GREEN", targetChildIndex) != null) {
                  info.setPpc_green((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.RGB_GREEN", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BLUE", targetChildIndex) != null) {
                  info.setPpc_blue((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BLUE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PICTURE_SIZE", targetChildIndex) != null) {
                  info.setPpc_size((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.PICTURE_SIZE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.LED_HDR", targetChildIndex) != null && !"FAIL".equalsIgnoreCase(rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.LED_HDR", targetChildIndex))) {
                  info.setLed_hdr(rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.LED_HDR", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.LED_HDR_DRE", targetChildIndex) != null && !"FAIL".equalsIgnoreCase(rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.LED_HDR_DRE", targetChildIndex))) {
                  info.setLed_hdr_dre(Long.valueOf(rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.LED_HDR_DRE", targetChildIndex)));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.LED_PICTURE_SIZE", targetChildIndex) != null && !"FAIL".equalsIgnoreCase(rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.LED_PICTURE_SIZE", targetChildIndex))) {
                  info.setLed_picture_size(rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.LED_PICTURE_SIZE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.AUTO_MOTION_PLUS", targetChildIndex) != null) {
                  info.setAuto_motion_plus((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.AUTO_MOTION_PLUS", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.AUTO_MOTION_PLUS_JUDDER_REDUCTION", targetChildIndex) != null) {
                  info.setAuto_motion_plus_judder_reduction((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.AUTO_MOTION_PLUS_JUDDER_REDUCTION", targetChildIndex));
               }

               if (deviceModelCode > 55 && deviceModelCode < 7000) {
                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.GAMMA", targetChildIndex) != null) {
                     info.setPpc_gamma((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.GAMMA", targetChildIndex));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.HDMI_BLACK_LEVEL", targetChildIndex) != null) {
                     info.setPpc_hdmi_black_level((long)rs.getInt(".MO.DISPLAY_CONF.PICTURE_PC.HDMI_BLACK_LEVEL", targetChildIndex));
                  }
               }

               if (rs.getString(".MO.DISPLAY_CONF.SOUND.MODE", targetChildIndex) != null) {
                  info.setSound_mode((long)rs.getInt(".MO.DISPLAY_CONF.SOUND.MODE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.SOUND.BASS", targetChildIndex) != null) {
                  info.setSound_bass((long)rs.getInt(".MO.DISPLAY_CONF.SOUND.BASS", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.SOUND.TREBLE", targetChildIndex) != null) {
                  info.setSound_treble((long)rs.getInt(".MO.DISPLAY_CONF.SOUND.TREBLE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.SOUND.BALANCE", targetChildIndex) != null) {
                  info.setSound_balance((long)rs.getInt(".MO.DISPLAY_CONF.SOUND.BALANCE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.SOUND.SRS", targetChildIndex) != null) {
                  info.setSound_srs((long)rs.getInt(".MO.DISPLAY_CONF.SOUND.SRS", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.SOUND.EFFECT", targetChildIndex) != null) {
                  info.setSound_effect((long)rs.getInt(".MO.DISPLAY_CONF.SOUND.EFFECT", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.STATE", targetChildIndex) != null) {
                  info.setSb_status((long)rs.getInt(".MO.DISPLAY_CONF.SIGNAL_BALANCE.STATE", targetChildIndex));
               }

               if (deviceModelCode > 23) {
                  if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_GAIN", targetChildIndex) != null) {
                     info.setSb_rgain((long)rs.getInt(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_GAIN", targetChildIndex));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_GAIN", targetChildIndex) != null) {
                     info.setSb_ggain((long)rs.getInt(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_GAIN", targetChildIndex));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_GAIN", targetChildIndex) != null) {
                     info.setSb_bgain((long)rs.getInt(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_GAIN", targetChildIndex));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_OFFSET", targetChildIndex) != null) {
                     info.setSb_r_offset((long)rs.getInt(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_OFFSET", targetChildIndex));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_OFFSET", targetChildIndex) != null) {
                     info.setSb_g_offset((long)rs.getInt(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_OFFSET", targetChildIndex));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_OFFSET", targetChildIndex) != null) {
                     info.setSb_b_offset((long)rs.getInt(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_OFFSET", targetChildIndex));
                  }
               }

               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_GAIN", targetChildIndex) != null) {
                  info.setSb_gain((long)rs.getInt(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_GAIN", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_SHARPNESS", targetChildIndex) != null) {
                  info.setSb_sharp((long)rs.getInt(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_SHARPNESS", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.AUTO", targetChildIndex) != null) {
                  info.setMnt_auto(rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.AUTO"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.MANUAL", targetChildIndex) != null) {
                  info.setMnt_manual((long)rs.getInt(".MO.DISPLAY_CONF.MAINTENANCE.MANUAL", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_TIMER", targetChildIndex) != null) {
                  info.setMnt_safety_screen_timer(rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_TIMER"));
               }

               if (deviceModelCode > 23) {
                  if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_RUN", targetChildIndex) != null) {
                     info.setMnt_safety_screen_run((long)rs.getInt(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_RUN", targetChildIndex));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.PIXEL_SHIFT", targetChildIndex) != null) {
                     info.setMnt_pixel_shift(rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.PIXEL_SHIFT"));
                  }
               }

               if (deviceModelCode > 55 && deviceModelCode < 7000) {
                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.RJ45_SETTING_REFRESH", targetChildIndex) != null) {
                     info.setAdvanced_rj45_setting_refresh((long)rs.getInt(".MO.DISPLAY_CONF.ADVANCED.RJ45_SETTING_REFRESH", targetChildIndex));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.OSD_DISPLAY_TYPE", targetChildIndex) != null) {
                     info.setAdvanced_osd_display_type(rs.getString(".MO.DISPLAY_CONF.ADVANCED.OSD_DISPLAY_TYPE"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.FAN_CONTROL", targetChildIndex) != null) {
                     info.setAdvanced_fan_control((long)rs.getInt(".MO.DISPLAY_CONF.ADVANCED.FAN_CONTROL", targetChildIndex));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.RESET", targetChildIndex) != null) {
                     info.setAdvanced_reset((long)rs.getInt(".MO.DISPLAY_CONF.ADVANCED.RESET", targetChildIndex));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.FAN_SPEED", targetChildIndex) != null) {
                     info.setAdvanced_fan_speed((long)rs.getInt(".MO.DISPLAY_CONF.ADVANCED.FAN_SPEED", targetChildIndex));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.AUTO_POWER", targetChildIndex) != null) {
                     info.setAdvanced_auto_power((long)rs.getInt(".MO.DISPLAY_CONF.ADVANCED.AUTO_POWER", targetChildIndex));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.USER_AUTO_COLOR", targetChildIndex) != null) {
                     info.setAdvanced_user_auto_color((long)rs.getInt(".MO.DISPLAY_CONF.ADVANCED.USER_AUTO_COLOR", targetChildIndex));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.STAND_BY", targetChildIndex) != null) {
                     info.setAdvanced_stand_by((long)rs.getInt(".MO.DISPLAY_CONF.ADVANCED.STAND_BY", targetChildIndex));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.NETWORK_STAND_BY") != null && this.ctxt != null && this.ctxt.getDevice() != null && this.ctxt.getDevice().getDevice_type() != null && !this.ctxt.getDevice().getDevice_type().equalsIgnoreCase("iPLAYER") && !this.isLiteDevice) {
                     info.setNetwork_standby_mode(rs.getString(".MO.DISPLAY_CONF.ADVANCED.NETWORK_STAND_BY"));
                  }
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.AUTO_SOURCE_SWITCHING", targetChildIndex) != null) {
                  info.setAuto_source_switching(rs.getString(".MO.DISPLAY_CONF.ADVANCED.AUTO_SOURCE_SWITCHING", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.MAX_POWER_SAVING", targetChildIndex) != null) {
                  info.setMax_power_saving(rs.getLong(".MO.DISPLAY_CONF.ADVANCED.MAX_POWER_SAVING", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.BRIGHTNESS_LIMIT", targetChildIndex) != null) {
                  info.setBrightness_limit(rs.getLong(".MO.DISPLAY_CONF.ADVANCED.BRIGHTNESS_LIMIT", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.TOUCH_CONTROL_LOCK", targetChildIndex) != null) {
                  info.setTouch_control_lock(rs.getLong(".MO.DISPLAY_CONF.ADVANCED.TOUCH_CONTROL_LOCK", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.AUTO_BRIGHTNESS", targetChildIndex) != null) {
                  info.setAuto_brightness(rs.getString(".MO.DISPLAY_CONF.ADVANCED.AUTO_BRIGHTNESS", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.BLACK_TONE", targetChildIndex) != null) {
                  info.setBlack_tone(rs.getLong(".MO.DISPLAY_CONF.ADVANCED.BLACK_TONE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.FLESH_TONE", targetChildIndex) != null) {
                  info.setFlesh_tone(rs.getLong(".MO.DISPLAY_CONF.ADVANCED.FLESH_TONE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.RGB_ONLY_MODE", targetChildIndex) != null) {
                  info.setRgb_only_mode(rs.getLong(".MO.DISPLAY_CONF.ADVANCED.RGB_ONLY_MODE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.PICTURE_ENHANCER", targetChildIndex) != null) {
                  info.setPicture_enhancer(rs.getLong(".MO.DISPLAY_CONF.ADVANCED.PICTURE_ENHANCER", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.COLOR_SPACE", targetChildIndex) != null) {
                  info.setColor_space(rs.getString(".MO.DISPLAY_CONF.ADVANCED.COLOR_SPACE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.ECO_SENSOR", targetChildIndex) != null) {
                  info.setEco_sensor(rs.getLong(".MO.DISPLAY_CONF.ADVANCED.ECO_SENSOR", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.MIN_BRIGHTNESS", targetChildIndex) != null) {
                  info.setMin_brightness(rs.getLong(".MO.DISPLAY_CONF.ADVANCED.MIN_BRIGHTNESS", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.LIVE_MODE", targetChildIndex) != null) {
                  info.setLive_mode(rs.getLong(".MO.DISPLAY_CONF.ADVANCED.LIVE_MODE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.DISPLAY_OUTPUT_MODE", targetChildIndex) != null) {
                  info.setDisplay_output_mode(rs.getLong(".MO.DISPLAY_CONF.ADVANCED.DISPLAY_OUTPUT_MODE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.INSTALL_ENVIRONMENT", targetChildIndex) != null) {
                  info.setInstall_environment(rs.getString(".MO.DISPLAY_CONF.ADVANCED.INSTALL_ENVIRONMENT", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.DEHUMIDIFY", targetChildIndex) != null) {
                  info.setDehumidify(rs.getString(".MO.DISPLAY_CONF.ADVANCED.DEHUMIDIFY", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_OPTION", targetChildIndex) != null) {
                  info.setDimming_option(rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_OPTION", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_NIGHT_TIME_OVERRIDE", targetChildIndex) != null) {
                  info.setDimming_night_time_override(rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_NIGHT_TIME_OVERRIDE", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_ECO_SENSOR", targetChildIndex) != null) {
                  info.setDimming_eco_sensor(rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_ECO_SENSOR", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_SUNRISE_SUNSET", targetChildIndex) != null) {
                  info.setDimming_sunrise_sunset(rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_SUNRISE_SUNSET", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_SUNRISE_SUNSET_TIMES", targetChildIndex) != null) {
                  info.setDimming_sunrise_sunset_times(rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_SUNRISE_SUNSET_TIMES", targetChildIndex));
               }

               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_BRIGHTNESS_OUTPUT", targetChildIndex) != null) {
                  info.setDimming_brightness_output(rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_BRIGHTNESS_OUTPUT", targetChildIndex));
               }

               displayDao = DeviceDisplayConfManagerImpl.getInstance();
               if (mode.toUpperCase().equals("ALL_MDC")) {
                  displayDao.setDeviceDisplayConf(info);
               }

               displayDao.setDeviceDisplayExtConf(info);
            }
         }

         this.setDisplayConfData(rs, this.ctxt.getDevice().getDevice_id());
         if (!this.ctxt.getServiceParamMap().get("session_id").toString().equals("")) {
            System.out.println("[Display GetResponse] DISPLAY GET Response by UI !!!");
            ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
            if (targetChildIndex == null) {
               statusManager.setResultset(this.ctxt.getServiceParamMap().get("session_id").toString(), this.ctxt.getServiceID(), this.ctxt.getDevice().getDevice_id(), rs);
            } else {
               statusManager.setResultset(this.ctxt.getServiceParamMap().get("session_id").toString(), this.ctxt.getServiceID(), this.ctxt.getDevice().getDevice_id() + "_" + targetChildIndex, rs);
            }
         }

         try {
            if (DeviceUtils.isSupportNOC()) {
               DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
               DeviceNocInfo nocDao = DeviceNocInfoImpl.getInstance();
               String groupId = deviceDao.getDeviceGroupIdByDeviceId(info.getDevice_id());
               boolean nocGroup = nocDao.isNocSupportGroup(Long.valueOf(groupId));
               if (nocGroup) {
                  DeviceNocManager nocService = DeviceNocManagerImpl.getInstance();
                  nocService.thingworxUpdateDisplayControl(info);
                  this.logger.error("[GetDeviceDisplayConfActivity] success to updated display information : " + info.getDevice_id());
               }
            }
         } catch (Exception var11) {
            this.logger.error("[GetDeviceDisplayConfActivity] failed to updated display information : " + info.getDevice_id());
         }
      } catch (RMQLException var12) {
         this.logger.error("", var12);
      } catch (SQLException var13) {
         this.logger.error("", var13);
      }

      return null;
   }

   protected RMQL setRMQL(Device device, Long service_id, HashMap params) throws Exception {
      RMQL rmql = RMQLInstanceCreator.getInstance(device, "GET", service_id);
      String mode = (String)params.get("device_display_view_mode");
      int deviceModelCode = 0;
      if (device != null && !device.getDevice_model_name().equalsIgnoreCase("DEFAULT")) {
         deviceModelCode = Integer.parseInt(device.getDevice_model_code());
      }

      this.isLiteDevice = this.ctxt.getDevice().getDevice_type().equals("LPLAYER");
      String[] childIndex = null;
      if (params != null) {
         childIndex = (String[])((String[])params.get("childIndex"));
      }

      boolean isNotChild = false;
      if (childIndex == null) {
         isNotChild = true;
      }

      if (mode.toUpperCase().equals("MOBILE_MDC")) {
         rmql.addMO(".MO.DISPLAY_CONF.BASIC.POWER", childIndex, "0");
         rmql.addMO(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS", childIndex, "0");
         rmql.addMO(".MO.DISPLAY_CONF.BASIC.VOLUME", childIndex, "0");
         rmql.addMO(".MO.DISPLAY_CONF.BASIC.MUTE", childIndex, "0");
         rmql.addMO(".MO.DISPLAY_CONF.BASIC.SOURCE", childIndex, "0");
         rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO", childIndex, "");
         rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC", childIndex, "");
      } else if (mode.toUpperCase().equals("ADVANCED_MDC")) {
         rmql.addMO(".MO.DISPLAY_CONF.ADVANCED", childIndex, "");
      } else {
         if (!mode.toUpperCase().equals("ONLY_EXT_MDC")) {
            rmql.addMO(".MO.DISPLAY_CONF.BASIC", childIndex, "");
            rmql.addMO(".MO.DISPLAY_CONF.DIAGNOSIS", childIndex, "0");
            rmql.addMO(".MO.DISPLAY_CONF.MISC", childIndex, "0");
            if (deviceModelCode > 23 && deviceModelCode < 7000 && isNotChild) {
               rmql.addMO(".MO.DISPLAY_CONF.BASIC.DIRECT_CHANNEL", childIndex, "0");
            }
         }

         if ((mode.toUpperCase().equals("ONLY_EXT_MDC") || mode.toUpperCase().equals("ALL_MDC")) && isNotChild) {
            if (!this.isLiteDevice) {
               rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO", childIndex, "");
               rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC", childIndex, "");
               rmql.addMO(".MO.DISPLAY_CONF.SOUND", childIndex, "");
               rmql.addMO(".MO.DISPLAY_CONF.SIGNAL_BALANCE", childIndex, "");
               rmql.addMO(".MO.DISPLAY_CONF.MAINTENANCE", childIndex, "");
               if (deviceModelCode > 55 && deviceModelCode < 7000) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED", childIndex, "");
               }
            } else {
               rmql.addMO(".MO.DISPLAY_CONF.MAINTENANCE.AUTO", childIndex, "0");
               rmql.addMO(".MO.DISPLAY_CONF.MAINTENANCE.MANUAL", childIndex, "0");
               rmql.addMO(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_TIMER", childIndex, "0");
               rmql.addMO(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_RUN", childIndex, "0");
               rmql.addMO(".MO.DISPLAY_CONF.MAINTENANCE.PIXEL_SHIFT", childIndex, "0");
               if (deviceModelCode > 55 && deviceModelCode < 7000) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.OSD_DISPLAY_TYPE", childIndex, "0");
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.FAN_CONTROL", childIndex, "0");
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.AUTO_POWER", childIndex, "0");
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.STAND_BY", childIndex, "0");
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.RJ45_SETTING_REFRESH", childIndex, "0");
               }
            }
         }
      }

      return rmql;
   }

   private void setDisplayConfData(ResultSet rs, String deviceId) {
      try {
         if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.LIVE_MODE") != null) {
            DeviceDisplayConfManager displayDao = DeviceDisplayConfManagerImpl.getInstance();
            DeviceDisplayConf info = new DeviceDisplayConf();
            info.setDevice_id(this.ctxt.getDevice().getDevice_id());
            info.setLive_mode(rs.getLong(".MO.DISPLAY_CONF.ADVANCED.LIVE_MODE"));
            displayDao.setDeviceDisplayExtConf(info);
         }
      } catch (Exception var5) {
         this.logger.error(var5);
      }

   }
}
