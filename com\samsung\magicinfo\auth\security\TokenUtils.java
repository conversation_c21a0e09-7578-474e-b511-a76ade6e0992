package com.samsung.magicinfo.auth.security;

import com.google.gson.Gson;
import com.samsung.common.cache.CacheFactory;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.security.manager.SecurityInfo;
import com.samsung.magicinfo.framework.security.manager.SecurityInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManager;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManagerImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.utils.V2TokenUtils;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.function.Supplier;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.mobile.device.Device;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

@Component("tokenUtils")
public class TokenUtils {
   private static final Logger logger = LoggingManagerV2.getLogger(TokenUtils.class);
   private final String AUDIENCE_UNKNOWN = "unknown";
   private final String AUDIENCE_WEB = "web";
   private final String AUDIENCE_MOBILE = "mobile";
   private final String AUDIENCE_TABLET = "tablet";
   public static String secret = null;
   protected final UserInfo userInfo = UserInfoImpl.getInstance();
   public static Long expiration = (long)SecurityUtils.getMaxInactiveInterval();
   List abilityList = new ArrayList();
   Map viewAbilityJSON = new LinkedHashMap();
   Map contentJSON = new LinkedHashMap();
   Map scheduleJSON = new LinkedHashMap();
   Map userJSON = new LinkedHashMap();
   Map deviceJSON = new LinkedHashMap();
   Map etcJSON = new LinkedHashMap();
   Map ruleManagerJSON = new LinkedHashMap();
   Map insightJSON = new LinkedHashMap();
   public static String tokenHeader = "api_key";
   static final long TOKEN_DEFAULT_LIFE_TIME = 1800L;
   static final long REFERESH_TOKEN_DEFAULT_LIFE_TIME = 604800L;
   public static String TOKEN_LIFE_TIME_KEY = "token.lifetime";
   public static String REFRESH_LIFE_TIME_KEY = "refreshToken.lifetime";

   public TokenUtils() {
      super();
   }

   public String getSecret() {
      try {
         SecurityInfo securityInfo = SecurityInfoImpl.getInstance();
         return null == securityInfo ? null : securityInfo.getSecretKey();
      } catch (Exception var2) {
         logger.error("[TokenUtils] Fail to get secret key.", var2);
         return null;
      }
   }

   public String getUsernameFromToken(String token) {
      String username;
      try {
         Claims claims = this.getClaimsFromToken(token);
         username = claims.getSubject();
      } catch (Exception var4) {
         username = null;
      }

      return username;
   }

   public Date getCreatedDateFromToken(String token) {
      Date created;
      try {
         Claims claims = this.getClaimsFromToken(token);
         created = new Date((Long)claims.get("created"));
      } catch (Exception var4) {
         created = null;
      }

      return created;
   }

   public Date getExpirationDateFromToken(String token) {
      Date expiration;
      try {
         Claims claims = this.getClaimsFromToken(token);
         expiration = claims.getExpiration();
      } catch (Exception var4) {
         expiration = null;
      }

      return expiration;
   }

   public String getAudienceFromToken(String token) {
      String audience;
      try {
         Claims claims = this.getClaimsFromToken(token);
         audience = (String)claims.get("audience");
      } catch (Exception var4) {
         audience = null;
      }

      return audience;
   }

   public static boolean isBlockToken(String token) {
      Set hashSet = null;
      Object obj = null;

      try {
         obj = CacheFactory.getCache().get("BLOCK_TOKEN_LIST");
         if (obj == null) {
            return false;
         } else {
            hashSet = (Set)obj;
            return hashSet.contains(token);
         }
      } catch (Exception var4) {
         return false;
      }
   }

   public static boolean setBlockToken(String token) {
      Set hashSet = null;
      Object obj = null;

      try {
         obj = CacheFactory.getCache().get("BLOCK_TOKEN_LIST");
         if (obj == null) {
            hashSet = new HashSet();
         } else {
            hashSet = (Set)obj;
         }

         ((Set)hashSet).add(token);
         CacheFactory.getCache().set("BLOCK_TOKEN_LIST", hashSet);
      } catch (Exception var6) {
         logger.error(var6);
         return false;
      }

      try {
         V2TokenUtils.deleteUserContainerFromCache(token);
      } catch (Exception var5) {
         logger.error(var5);
      }

      try {
         V2TokenUtils.deleteLastestAPITimeOnCache(token);
      } catch (Exception var4) {
         logger.error(var4);
      }

      return true;
   }

   private Claims getClaimsFromToken(String token) {
      try {
         if (secret == null) {
            secret = this.getSecret();
         }

         Claims claims = (Claims)Jwts.parser().setSigningKey(secret).parseClaimsJws(token).getBody();
         return claims;
      } catch (ExpiredJwtException var4) {
         if (V2TokenUtils.isExpiredTokenAPITimeout(token)) {
            throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_TOKEN_EXPIRED_API_TIMEOUT);
         } else {
            throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_TOKEN_EXPIRED);
         }
      } catch (Exception var5) {
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_TOKEN_INVALID);
      }
   }

   private Date generateCurrentDate() {
      return new Date(System.currentTimeMillis());
   }

   private Date generateExpirationDate() {
      long sessionTimeOut = 99999999L;

      try {
         if (CommonConfig.get("mobile.sessionTimeOut") != null) {
            long configTime = Long.parseLong(CommonConfig.get("mobile.sessionTimeOut"));
            if (configTime > 0L) {
               sessionTimeOut = configTime;
            }
         }
      } catch (Exception var5) {
         sessionTimeOut = 99999999L;
      }

      return new Date(System.currentTimeMillis() + sessionTimeOut * 1000L);
   }

   private Boolean isTokenExpired(String token) {
      Date expiration = this.getExpirationDateFromToken(token);
      return expiration.before(this.generateCurrentDate());
   }

   private Boolean isCreatedBeforeLastPasswordReset(Date created, Date lastPasswordReset) {
      return lastPasswordReset != null && created.before(lastPasswordReset);
   }

   private String generateAudience(Device device) {
      this.getClass();
      String audience = "unknown";
      if (device == null) {
         return audience;
      } else {
         if (device.isNormal()) {
            this.getClass();
            audience = "web";
         } else if (device.isTablet()) {
            audience = "tablet";
         } else if (device.isMobile()) {
            audience = "mobile";
         }

         return audience;
      }
   }

   private Boolean ignoreTokenExpiration(String token) {
      String audience = this.getAudienceFromToken(token);
      this.getClass();
      boolean var10000;
      if (!"tablet".equals(audience)) {
         this.getClass();
         if (!"mobile".equals(audience)) {
            var10000 = false;
            return var10000;
         }
      }

      var10000 = true;
      return var10000;
   }

   public String generateToken(UserDetails userDetails) {
      Date expirationDate = TokenUtils.ExpirationDateBuilder.TOKEN.get();
      return this.generateToken(userDetails, expirationDate);
   }

   public String generateToken(UserDetails userDetails, Date expirationDate) {
      String userUniqueKey = UUID.randomUUID().toString();
      Map claims = this.generateClaims(userDetails.getUsername(), expirationDate);
      return this.generateToken(claims, expirationDate);
   }

   public String generateToken(String userId, String userUniqueKey) {
      Date expirationDate = TokenUtils.ExpirationDateBuilder.TOKEN.get();
      Map claims = this.generateClaims(userId, expirationDate);
      return this.generateToken(claims, expirationDate, userUniqueKey);
   }

   public String generateToken(String userId, String userUniqueKey, Date expirationDate) {
      Map claims = this.generateClaims(userId, expirationDate);
      return this.generateToken(claims, expirationDate, userUniqueKey);
   }

   private Map generateClaims(String userId, Date expirationDate) {
      Map claims = new HashMap();
      claims.put("sub", userId);
      claims.put("created", this.generateCurrentDate());
      claims.put("expired", expirationDate);

      try {
         Collection authorities = SecurityContextHolder.getContext().getAuthentication().getAuthorities();
         String viewAuthoritiesJSON = this.checkViewAuthority(authorities);
         claims.put("viewAuthority", viewAuthoritiesJSON);
         List authoritiesList = this.checkAuthority(authorities);
         String commaSprAuthorities = StringUtils.join(authoritiesList, ',');
         claims.put("authority", commaSprAuthorities);
      } catch (Exception var8) {
         logger.error(var8);
      }

      return claims;
   }

   private String generateToken(Map claims, Date expirationDate, String uniqueKey) {
      SlmLicenseManager licenseManager = SlmLicenseManagerImpl.getInstance();
      licenseManager.SlmlicenseLoadingConfig();

      try {
         if (secret == null) {
            secret = this.getSecret();
         }

         return Jwts.builder().setClaims(claims).setExpiration(expirationDate).setId(uniqueKey).signWith(SignatureAlgorithm.HS512, secret).compact();
      } catch (Exception var6) {
         logger.error("[TokenUtils] fail to generate token.");
         return null;
      }
   }

   private String generateToken(Map claims, Date expirationDate) {
      return this.generateToken(claims, expirationDate, UUID.randomUUID().toString());
   }

   public Boolean canTokenBeRefreshed(String token, Date lastPasswordReset) {
      Date created = this.getCreatedDateFromToken(token);
      return !this.isCreatedBeforeLastPasswordReset(created, lastPasswordReset) && (!this.isTokenExpired(token) || this.ignoreTokenExpiration(token));
   }

   public Boolean canTokenBeRefreshed(String token) {
      return !this.isTokenExpired(token) || this.ignoreTokenExpiration(token);
   }

   public String refreshToken(String token) {
      String refreshedToken;
      try {
         Claims claims = this.getClaimsFromToken(token);
         claims.put("created", this.generateCurrentDate());
         refreshedToken = this.generateToken((Map)claims, (Date)this.generateExpirationDate());
      } catch (Exception var4) {
         refreshedToken = null;
      }

      return refreshedToken;
   }

   public Boolean validateToken(String token, UserDetails userDetails) {
      String username = this.getUsernameFromToken(token);
      this.getCreatedDateFromToken(token);
      this.getExpirationDateFromToken(token);
      return username.equals(userDetails.getUsername()) && !this.isTokenExpired(token);
   }

   public static Long getTokenLifeTime(String key) {
      long lifeTime = 1800L;
      if ("refreshToken.lifetime".equals(key)) {
         lifeTime = 604800L;
      }

      try {
         String life = CommonConfig.get(key);
         if (life != null && StrUtils.isNumeric(life)) {
            lifeTime = Long.parseLong(life);
         }
      } catch (ConfigException var4) {
      }

      return lifeTime;
   }

   public String generateToken(String userId) {
      return this.generateToken(userId, UUID.randomUUID().toString());
   }

   public String generateRefreshToken(String userId) {
      String uniqueKey = UUID.randomUUID().toString();
      return this.generateRefreshToken(userId, uniqueKey);
   }

   public String generateRefreshToken(String userId, String uniqueKey) {
      Map claims = new HashMap();
      return Jwts.builder().setClaims(claims).setSubject(userId).setIssuedAt(new Date(System.currentTimeMillis())).setId(uniqueKey).setExpiration(TokenUtils.ExpirationDateBuilder.REFRESH_TOKEN.get()).signWith(SignatureAlgorithm.HS512, secret).compact();
   }

   public Object getClaimFromToken(String token, Function claimsResolver) {
      Claims claims = this.getClaimsFromToken(token);
      return claimsResolver.apply(claims);
   }

   public List getAuthorities(String token) {
      Claims claims = this.getClaimsFromToken(token);
      return (List)Optional.ofNullable(claims.get("authority")).orElse(new ArrayList());
   }

   public LocalDateTime getExpirationTimeFromToken(String token) {
      Date expirationDate = (Date)this.getClaimFromToken(token, Claims::getExpiration);
      Instant instant = Instant.ofEpochMilli(expirationDate.getTime());
      return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
   }

   public boolean isExpiredToken(String token) {
      return LocalDateTime.now().isAfter(this.getExpirationTimeFromToken(token));
   }

   public void initAbilityList() {
      if (this.abilityList.size() <= 0) {
         this.abilityList.add("Content Read Authority");
         this.abilityList.add("Content Write Authority");
         this.abilityList.add("Content Manage Authority");
         this.abilityList.add("Content Schedule Read Authority");
         this.abilityList.add("Content Schedule Write Authority");
         this.abilityList.add("Content Schedule Manage Authority");
         this.abilityList.add("Server Setup Manage Authority");
         this.abilityList.add("Device Read Authority");
         this.abilityList.add("Device Write Authority");
         this.abilityList.add("User Read Authority");
         this.abilityList.add("User Write Authority");
         this.abilityList.add("User Approval Authority");
      }
   }

   public List checkAuthority(Collection original) {
      this.initAbilityList();
      List result = new ArrayList();
      GrantedAuthority g = null;
      Iterator it = original.iterator();

      while(it.hasNext()) {
         g = (GrantedAuthority)it.next();
         if (this.abilityList.contains(g.getAuthority())) {
            result.add(g.getAuthority());
         }
      }

      return result;
   }

   public void initViewAbilityList() {
      this.contentJSON.put("READ", false);
      this.contentJSON.put("CREATE", false);
      this.contentJSON.put("LOCK", false);
      this.contentJSON.put("MANAGE", false);
      this.contentJSON.put("ADDELEMENT", false);
      this.contentJSON.put("CONTENTUPLOAD", false);
      this.scheduleJSON.put("READ", false);
      this.scheduleJSON.put("CREATE", false);
      this.scheduleJSON.put("MANAGE", false);
      this.userJSON.put("READ", false);
      this.userJSON.put("CREATE", false);
      this.userJSON.put("MANAGE", false);
      this.deviceJSON.put("READ", false);
      this.deviceJSON.put("CONTROL", false);
      this.deviceJSON.put("CREATE", false);
      this.deviceJSON.put("MANAGE", false);
      this.deviceJSON.put("APPROVAL", false);
      this.deviceJSON.put("MOVE", false);
      this.deviceJSON.put("DELETE", false);
      this.deviceJSON.put("CUSTOMIZE", false);
      this.deviceJSON.put("SW_UPDATE", false);
      this.deviceJSON.put("SECURITY", false);
      this.etcJSON.put("SERVER_SETUP_MANAGE", false);
      this.etcJSON.put("STATISTICS", false);
      this.ruleManagerJSON.put("STORE_MANAGER", false);
      this.ruleManagerJSON.put("HQ_MANAGER", false);
      this.insightJSON.put("READ", false);
      this.insightJSON.put("MANAGE", false);
   }

   public String checkViewAuthority(Collection original) {
      this.initViewAbilityList();
      GrantedAuthority g = null;
      Iterator it = original.iterator();

      while(it.hasNext()) {
         g = (GrantedAuthority)it.next();
         if (g.getAuthority().equals("Content Read Authority")) {
            this.contentJSON.put("READ", true);
         }

         if (g.getAuthority().equals("Content Write Authority")) {
            this.contentJSON.put("CREATE", true);
         }

         if (g.getAuthority().equals("Content Lock Authority")) {
            this.contentJSON.put("LOCK", true);
         }

         if (g.getAuthority().equals("Content Manage Authority")) {
            this.contentJSON.put("MANAGE", true);
         }

         if (g.getAuthority().equals("Content Add Element Authority")) {
            this.contentJSON.put("ADDELEMENT", true);
         }

         if (g.getAuthority().equals("Content Upload Authority")) {
            this.contentJSON.put("CONTENTUPLOAD", true);
         }

         if (g.getAuthority().equals("Content Schedule Read Authority")) {
            this.scheduleJSON.put("READ", true);
         }

         if (g.getAuthority().equals("Content Schedule Write Authority")) {
            this.scheduleJSON.put("CREATE", true);
            this.scheduleJSON.put("MANAGE", true);
         }

         if (g.getAuthority().equals("User Read Authority")) {
            this.userJSON.put("READ", true);
         }

         if (g.getAuthority().equals("User Write Authority")) {
            this.userJSON.put("CREATE", true);
         }

         if (g.getAuthority().equals("User Approval Authority")) {
            this.userJSON.put("MANAGE", true);
         }

         if (g.getAuthority().equals("Device Read Authority")) {
            this.deviceJSON.put("READ", true);
         }

         if (g.getAuthority().equals("Device Control Authority")) {
            this.deviceJSON.put("CONTROL", true);
         }

         if (g.getAuthority().equals("Device Write Authority")) {
            this.deviceJSON.put("CREATE", true);
         }

         if (g.getAuthority().equals("Device Approval Authority")) {
            this.deviceJSON.put("MANAGE", true);
         }

         if (g.getAuthority().equals("Device Move Authority")) {
            this.deviceJSON.put("MOVE", true);
         }

         if (g.getAuthority().equals("Device Delete Authority")) {
            this.deviceJSON.put("DELETE", true);
         }

         if (g.getAuthority().equals("Device Only Approval Authority")) {
            this.deviceJSON.put("APPROVAL", true);
         }

         if (g.getAuthority().equals("Device Customize Authority")) {
            this.deviceJSON.put("CUSTOMIZE", true);
         }

         if (g.getAuthority().equals("Device Software Update Authority")) {
            this.deviceJSON.put("SW_UPDATE", true);
         }

         if (g.getAuthority().equals("Device Security Authority")) {
            this.deviceJSON.put("SECURITY", true);
         }

         if (g.getAuthority().equals("Server Setup Manage Authority")) {
            this.etcJSON.put("SERVER_SETUP_MANAGE", true);
         }

         if (g.getAuthority().equals("Statistics Manage Authority")) {
            this.etcJSON.put("STATISTICS_MANAGE", true);
         }

         if (g.getAuthority().equals("RuleManager Store Authority")) {
            this.ruleManagerJSON.put("STORE_MANAGER", true);
         }

         if (g.getAuthority().equals("Statistics Manage Authority")) {
            this.ruleManagerJSON.put("HQ_MANAGER", true);
         }

         if (g.getAuthority().equals("Insight Read Authority")) {
            this.insightJSON.put("READ", true);
         }

         if (g.getAuthority().equals("Insight Manage Authority")) {
            this.insightJSON.put("MANAGE", true);
         }

         this.viewAbilityJSON.put("CONTENT_PLAYLIST_RULESET", this.contentJSON);
         this.viewAbilityJSON.put("SCHEDULE", this.scheduleJSON);
         this.viewAbilityJSON.put("USER", this.userJSON);
         this.viewAbilityJSON.put("DEVICE", this.deviceJSON);
         this.viewAbilityJSON.put("ETC", this.etcJSON);
         this.viewAbilityJSON.put("RULEMANAGER", this.ruleManagerJSON);
         this.viewAbilityJSON.put("INSIGHT", this.insightJSON);
      }

      Gson gson = new Gson();
      return gson.toJson(this.viewAbilityJSON);
   }

   private User getUserFromClaims(Map map) {
      User user = new User();
      String keyAttribute = null;
      String setMethodString = "set";
      String methodString = null;
      Iterator itr = map.keySet().iterator();

      while(itr.hasNext()) {
         keyAttribute = (String)itr.next();
         methodString = setMethodString + keyAttribute.substring(0, 1).toUpperCase() + keyAttribute.substring(1);
         Method[] methods = user.getClass().getDeclaredMethods();

         for(int i = 0; i < methods.length; ++i) {
            if (methodString.equals(methods[i].getName())) {
               try {
                  methods[i].invoke(user, map.get(keyAttribute));
               } catch (Exception var10) {
               }
            }
         }
      }

      user.setGroup_id((long)(Integer)map.get("group_id"));
      user.setRoot_group_id((long)(Integer)map.get("root_group_id"));
      user.setCreate_date(new Timestamp((Long)map.get("create_date")));
      user.setLast_login_date(new Timestamp((Long)map.get("last_login_date")));
      user.setModify_date(new Timestamp((Long)map.get("modify_date")));
      user.setPassword_change_date(new Timestamp((Long)map.get("password_change_date")));
      return user;
   }

   public static enum ExpirationDateBuilder implements Supplier {
      TOKEN(() -> {
         long lifeTime = TokenUtils.getTokenLifeTime(TokenUtils.TOKEN_LIFE_TIME_KEY);
         return getDate(lifeTime);
      }),
      REFRESH_TOKEN(() -> {
         long lifeTime = TokenUtils.getTokenLifeTime(TokenUtils.REFRESH_LIFE_TIME_KEY);
         return getDate(lifeTime);
      });

      private final Supplier expirationDateBuilder;

      private static Date getDate(long time) {
         return new Date(System.currentTimeMillis() + time * 1000L);
      }

      public Date get() {
         return (Date)this.expirationDateBuilder.get();
      }

      private ExpirationDateBuilder(Supplier expirationDateBuilder) {
         this.expirationDateBuilder = expirationDateBuilder;
      }
   }
}
