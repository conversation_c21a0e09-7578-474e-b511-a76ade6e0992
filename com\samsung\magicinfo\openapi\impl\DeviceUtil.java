package com.samsung.magicinfo.openapi.impl;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.MDCTimeStrUtils;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemSetupConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTimeConf;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceTimeConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceTimeConfManagerImpl;
import org.apache.logging.log4j.Logger;

public class DeviceUtil {
   static Logger logger = LoggingManagerV2.getLogger(DeviceUtil.class);
   public static final int OPENAPI_PREMIUM = 1;
   public static final int OPENAPI_LITE = 2;
   public static final int OPENAPI_VWL = 4;
   public static final int OPENAPI_MULTI = 16;
   public static final String OPENAPI_STR_ACTIVE = "ACTIVE";
   public static final String OPENAPI_STR_PASSIVE = "PASSIVE";
   public static final String OPENAPI_STR_TRUE = "true";
   public static final String OPENAPI_STR_FALSE = "false";
   public static final String OPENAPI_STR_SUN = "Sun";
   public static final String OPENAPI_STR_MON = "Mon";
   public static final String OPENAPI_STR_TUE = "Tue";
   public static final String OPENAPI_STR_WED = "Wed";
   public static final String OPENAPI_STR_THU = "Thu";
   public static final String OPENAPI_STR_FRI = "Fri";
   public static final String OPENAPI_STR_SAT = "Sat";

   public DeviceUtil() {
      super();
   }

   public static String[] checkDeviceSystemSetupConfEntity(DeviceSystemSetupConf in, int flag) {
      int chgFlag = 0;
      DeviceSystemSetupConf db = null;
      if (in.getDevice_id() != null && !in.getDevice_id().equals("")) {
         try {
            if (flag == 1) {
               DeviceSystemSetupConfManager systemSetupDao = DeviceSystemSetupConfManagerImpl.getInstance();
               db = systemSetupDao.getDeviceSystemSetupConf(in.getDevice_id());
            }
         } catch (Exception var10) {
            logger.error(var10);
            return OpenApiExceptionCode.D300;
         }
      }

      if (in.getTrigger_interval() != null) {
         if (db != null && db.getTrigger_interval().equals(in.getTrigger_interval())) {
            return OpenApiExceptionCode.D302;
         }

         if (!checkSec(in.getTrigger_interval().intValue())) {
            return OpenApiExceptionCode.D303;
         }

         ++chgFlag;
      }

      if (in.getFtp_connect_mode() != null && !in.getFtp_connect_mode().equals("")) {
         if (db != null && db.getFtp_connect_mode().toUpperCase().equals(in.getFtp_connect_mode())) {
            return OpenApiExceptionCode.D304;
         }

         if (in.getFtp_connect_mode().toUpperCase().equals("ACTIVE")) {
            in.setFtp_connect_mode("ACTIVE");
         } else {
            if (!in.getFtp_connect_mode().toUpperCase().equals("PASSIVE")) {
               return OpenApiExceptionCode.D305;
            }

            in.setFtp_connect_mode("PASSIVE");
         }

         ++chgFlag;
      }

      if (in.getMonitoring_interval() != null) {
         if (db != null && db.getMonitoring_interval().equals(in.getMonitoring_interval())) {
            return OpenApiExceptionCode.D306;
         }

         if (!checkMinute(in.getMonitoring_interval().intValue())) {
            return OpenApiExceptionCode.D307;
         }

         ++chgFlag;
      }

      int intTmp;
      if (in.getConnection_limit_time() != null) {
         if (db != null && db.getConnection_limit_time().equals(in.getConnection_limit_time())) {
            return OpenApiExceptionCode.D308;
         }

         intTmp = in.getConnection_limit_time().intValue();
         if (intTmp > 300 || intTmp <= 0) {
            return OpenApiExceptionCode.D309;
         }

         ++chgFlag;
      }

      int hour;
      String[] arrContentMnt;
      if (flag == 1 || flag == 4) {
         if (in.getRepository_path() != null && !in.getRepository_path().equals("")) {
            if (db != null && db.getRepository_path().equals(in.getRepository_path())) {
               return OpenApiExceptionCode.D314;
            }

            if (in.getRepository_path().length() > 200) {
               return OpenApiExceptionCode.D315;
            }

            ++chgFlag;
         }

         if (in.getProxy_setting() != null && !in.getProxy_setting().equals("")) {
            if (db != null && db.getProxy_setting().equals(in.getProxy_setting())) {
               return OpenApiExceptionCode.D318;
            }

            arrContentMnt = in.getProxy_setting().split(";");
            if (arrContentMnt.length != 1 && arrContentMnt.length != 5) {
               return OpenApiExceptionCode.D319;
            }

            if (arrContentMnt[0].equals("0")) {
               in.setProxy_setting("0;;;;");
            } else if (arrContentMnt[0].equals("1")) {
               in.setProxy_setting("1;;;;");
            } else {
               if (!arrContentMnt[0].equals("2")) {
                  return OpenApiExceptionCode.D319;
               }

               if (arrContentMnt.length != 5) {
                  return OpenApiExceptionCode.D319;
               }
            }

            ++chgFlag;
         }

         if (in.getMnt_folder_path() != null && !in.getMnt_folder_path().equals("")) {
            if (db != null && db.getMnt_folder_path().equals(in.getMnt_folder_path())) {
               return OpenApiExceptionCode.D320;
            }

            if (in.getMnt_folder_path().length() > 200) {
               return OpenApiExceptionCode.D321;
            }

            ++chgFlag;
         }

         int validDay;
         int validSize;
         if (in.getLog_mnt() != null && !in.getLog_mnt().equals("")) {
            if (db != null && db.getLog_mnt().equals(in.getLog_mnt())) {
               return OpenApiExceptionCode.D326;
            }

            arrContentMnt = in.getLog_mnt().split(";");
            if (arrContentMnt.length != 3) {
               return OpenApiExceptionCode.D327;
            }

            validDay = Integer.parseInt(arrContentMnt[0]);
            validSize = Integer.parseInt(arrContentMnt[1]);
            hour = Integer.parseInt(arrContentMnt[2]);
            if (validDay > 2 || validDay < 0 || validSize > 365 || validSize <= 0 || hour > 300 || hour <= 0) {
               return OpenApiExceptionCode.D327;
            }

            ++chgFlag;
         }

         if (in.getProof_of_play_mnt() != null && !in.getProof_of_play_mnt().equals("")) {
            if (db != null && db.getProof_of_play_mnt().equals(in.getProof_of_play_mnt())) {
               return OpenApiExceptionCode.D328;
            }

            arrContentMnt = in.getProof_of_play_mnt().split(";");
            if (arrContentMnt.length != 2) {
               return OpenApiExceptionCode.D329;
            }

            validDay = Integer.parseInt(arrContentMnt[0]);
            validSize = Integer.parseInt(arrContentMnt[1]);
            if (validDay > 365 || validDay <= 0 || validSize > 300 || validSize <= 0) {
               return OpenApiExceptionCode.D329;
            }

            ++chgFlag;
         }

         if (in.getContent_mnt() != null && !in.getContent_mnt().equals("")) {
            if (db != null && db.getContent_mnt().equals(in.getContent_mnt())) {
               return OpenApiExceptionCode.D330;
            }

            arrContentMnt = in.getContent_mnt().split(";");
            if (arrContentMnt.length != 2) {
               return OpenApiExceptionCode.D331;
            }

            validDay = Integer.parseInt(arrContentMnt[0]);
            validSize = Integer.parseInt(arrContentMnt[1]);
            if (validDay > 365 || validDay <= 0 || validSize > 300 || validSize <= 0) {
               return OpenApiExceptionCode.D331;
            }

            ++chgFlag;
         }
      }

      if (flag == 1) {
         if (in.getTunneling_server() != null && !in.getTunneling_server().equals("")) {
            if (db != null && db.getTunneling_server().equals(in.getTunneling_server())) {
               return OpenApiExceptionCode.D312;
            }

            if (in.getTunneling_server().length() > 80) {
               return OpenApiExceptionCode.D313;
            }

            ++chgFlag;
         }

         if (in.getTime_zone_index() != null && !in.getTime_zone_index().equals("")) {
            if (db != null && db.getTime_zone_index().equals(in.getTime_zone_index())) {
               return OpenApiExceptionCode.D310;
            }

            boolean rtn = false;
            DeviceSystemSetupConfManagerImpl deviceConf = DeviceSystemSetupConfManagerImpl.getInstance();

            try {
               rtn = deviceConf.isExistTimeZoneIndex(in.getTime_zone_index());
            } catch (Exception var9) {
               logger.error(var9);
               return OpenApiExceptionCode.D300;
            }

            if (!rtn) {
               return OpenApiExceptionCode.D311;
            }

            ++chgFlag;
         }

         if (in.getScreen_capture_interval() != null) {
            if (db != null && db.getScreen_capture_interval().equals(in.getScreen_capture_interval())) {
               return OpenApiExceptionCode.D316;
            }

            intTmp = in.getScreen_capture_interval().intValue();
            if (intTmp > 180 || intTmp <= 0) {
               return OpenApiExceptionCode.D317;
            }

            ++chgFlag;
         }

         if (in.getSystem_restart_interval() != null && !in.getSystem_restart_interval().equals("")) {
            if (db != null && db.getSystem_restart_interval().equals(in.getSystem_restart_interval())) {
               return OpenApiExceptionCode.D322;
            }

            arrContentMnt = in.getSystem_restart_interval().split(" ");
            if (arrContentMnt.length != 2) {
               return OpenApiExceptionCode.D323;
            }

            String[] arrDays = arrContentMnt[0].split(";");
            String[] arrTime = arrContentMnt[1].split(":");
            if (arrDays.length <= 0 || arrDays.length > 7) {
               return OpenApiExceptionCode.D324;
            }

            for(hour = 0; hour < arrDays.length; ++hour) {
               if (!arrDays[hour].equals("Sun") && !arrDays[hour].equals("Mon") && !arrDays[hour].equals("Tue") && !arrDays[hour].equals("Wed") && !arrDays[hour].equals("Thu") && !arrDays[hour].equals("Fri") && !arrDays[hour].equals("Sat")) {
                  return OpenApiExceptionCode.D324;
               }
            }

            if (arrTime.length != 2) {
               return OpenApiExceptionCode.D325;
            }

            hour = Integer.parseInt(arrTime[0]);
            int min = Integer.parseInt(arrTime[1]);
            if (!checkHour24(hour) || !checkMinute(min)) {
               return OpenApiExceptionCode.D325;
            }

            ++chgFlag;
         }

         if (in.getScreen_rotation() != null) {
            if (db != null && db.getScreen_rotation().equals(in.getScreen_rotation())) {
               return OpenApiExceptionCode.D332;
            }

            intTmp = in.getScreen_rotation().intValue();
            if (intTmp != 0 && intTmp != 270) {
               return OpenApiExceptionCode.D333;
            }

            ++chgFlag;
         }

         if (in.getDay_light_saving() != null) {
            if (db != null && db.getDay_light_saving().equals(in.getDay_light_saving())) {
               return OpenApiExceptionCode.D337;
            }

            ++chgFlag;
         }

         if (in.getDay_light_saving_manual() != null && !in.getDay_light_saving_manual().equals("")) {
            if (db != null && db.getDay_light_saving_manual().equals(in.getDay_light_saving_manual())) {
               return OpenApiExceptionCode.D338;
            }

            ++chgFlag;
         }
      }

      if (chgFlag <= 0) {
         return OpenApiExceptionCode.D301;
      } else {
         return null;
      }
   }

   public static String[] checkDeviceDisplayConfEntity(DeviceDisplayConf in, int flag) {
      int chgFlag = 0;
      DeviceDisplayConf db = null;
      if (in.getDevice_id() != null && !in.getDevice_id().equals("")) {
         try {
            if (flag == 1) {
               DeviceDisplayConfManager systemSetupDao = DeviceDisplayConfManagerImpl.getInstance();
               db = systemSetupDao.getDeviceDisplayConf(in.getDevice_id());
            }
         } catch (Exception var8) {
            logger.error(var8);
            return OpenApiExceptionCode.D400;
         }
      }

      if (in.getBasic_power() != null && !in.getBasic_power().equals("")) {
         if (db != null && db.getBasic_power().equals(in.getBasic_power())) {
            return OpenApiExceptionCode.D402;
         }

         if (!in.getBasic_power().equals("0")) {
            return OpenApiExceptionCode.D403;
         }

         ++chgFlag;
      }

      if (in.getBasic_volume() != null) {
         if (db != null && db.getBasic_volume().equals(in.getBasic_volume())) {
            return OpenApiExceptionCode.D404;
         }

         if (!checkVolume(in.getBasic_volume().intValue())) {
            return OpenApiExceptionCode.D405;
         }

         ++chgFlag;
      }

      if (in.getBasic_mute() != null) {
         if (db != null && db.getBasic_mute().equals(in.getBasic_mute())) {
            return OpenApiExceptionCode.D406;
         }

         if (!checkOnOff(in.getBasic_mute().intValue())) {
            return OpenApiExceptionCode.D407;
         }

         ++chgFlag;
      }

      if (in.getBasic_source() != null) {
         if (db != null && db.getBasic_source().equals(in.getBasic_source())) {
            return OpenApiExceptionCode.D408;
         }

         if (!checkBasicInputSource(in.getBasic_source().intValue())) {
            return OpenApiExceptionCode.D409;
         }

         ++chgFlag;
      }

      int intTmp;
      int i;
      if (in.getBasic_direct_channel() != null && !in.getBasic_direct_channel().equals("")) {
         if (db == null || db.getBasic_direct_channel().equals(in.getBasic_direct_channel())) {
            return OpenApiExceptionCode.D410;
         }

         intTmp = db.getBasic_source().intValue();
         if (intTmp != 48 && intTmp != 64) {
            return OpenApiExceptionCode.D411;
         }

         String[] arrCh = in.getBasic_direct_channel().split(";");
         if (arrCh.length != 6) {
            return OpenApiExceptionCode.D412;
         }

         int intTmp = false;

         for(int i = 1; i < 6; ++i) {
            i = Integer.parseInt(arrCh[i]);
            if (i != 1 && i != 2 && i != 4) {
               if ((i == 3 || i == 5) && (i > 999 || i < 0)) {
                  return OpenApiExceptionCode.D412;
               }
            } else if (!checkOnOff(i)) {
               return OpenApiExceptionCode.D412;
            }
         }

         in.setBasic_direct_channel("1;" + arrCh[1] + ";" + arrCh[2] + ";" + arrCh[3] + ";" + arrCh[4] + ";" + arrCh[5]);
         ++chgFlag;
      }

      if (in.getBasic_panel_status() != null) {
         if (db != null && db.getBasic_panel_status().equals(in.getBasic_panel_status())) {
            return OpenApiExceptionCode.D413;
         }

         if (!checkOnOff(in.getBasic_panel_status().intValue())) {
            return OpenApiExceptionCode.D414;
         }

         ++chgFlag;
      }

      String[] arrOsdType;
      boolean intTmp;
      int intTmp;
      if (in.getMnt_auto() != null && !in.getMnt_auto().equals("")) {
         if (db != null && db.getMnt_auto().equals(in.getMnt_auto())) {
            return OpenApiExceptionCode.D415;
         }

         arrOsdType = in.getMnt_auto().split(";");
         if (arrOsdType.length != 8) {
            return OpenApiExceptionCode.D416;
         }

         intTmp = false;
         i = 0;

         while(true) {
            if (i >= 8) {
               ++chgFlag;
               break;
            }

            intTmp = Integer.parseInt(arrOsdType[i]);
            if (i != 0 && i != 4) {
               if (i != 1 && i != 5) {
                  if (i != 2 && i != 6) {
                     if ((i == 3 || i == 7) && (intTmp > 100 || intTmp < 0)) {
                        return OpenApiExceptionCode.D416;
                     }
                  } else if (!checkAmPm(intTmp)) {
                     return OpenApiExceptionCode.D416;
                  }
               } else if (!checkMinute(intTmp)) {
                  return OpenApiExceptionCode.D416;
               }
            } else if (!checkHour12(intTmp)) {
               return OpenApiExceptionCode.D416;
            }

            ++i;
         }
      }

      if (in.getMnt_manual() != null) {
         if (db != null && db.getMnt_manual().equals(in.getMnt_manual())) {
            return OpenApiExceptionCode.D417;
         }

         intTmp = in.getMnt_manual().intValue();
         if (intTmp > 100 || intTmp < 0) {
            return OpenApiExceptionCode.D418;
         }

         ++chgFlag;
      }

      if (in.getMnt_safety_screen_timer() != null && !in.getMnt_safety_screen_timer().equals("")) {
         if (db != null && db.getMnt_safety_screen_timer().equals(in.getMnt_safety_screen_timer())) {
            return OpenApiExceptionCode.D419;
         }

         arrOsdType = in.getMnt_safety_screen_timer().split(";");
         if (arrOsdType.length != 3) {
            return OpenApiExceptionCode.D420;
         }

         intTmp = false;
         intTmp = Integer.parseInt(arrOsdType[0]);
         if (intTmp != 3 && intTmp != 4 && intTmp != 5 && intTmp != 6 && intTmp != 9 && intTmp != 10) {
            return OpenApiExceptionCode.D420;
         }

         intTmp = Integer.parseInt(arrOsdType[1]);
         if (!checkHour24(intTmp)) {
            return OpenApiExceptionCode.D420;
         }

         intTmp = Integer.parseInt(arrOsdType[2]);
         if (intTmp > 30 || intTmp < 1) {
            return OpenApiExceptionCode.D420;
         }

         ++chgFlag;
      }

      if (in.getMnt_safety_screen_run() != null) {
         if (db != null && db.getMnt_safety_screen_run().equals(in.getMnt_safety_screen_run())) {
            return OpenApiExceptionCode.D421;
         }

         intTmp = in.getMnt_safety_screen_run().intValue();
         if (intTmp != 0 && intTmp != 1 && intTmp != 2 && intTmp != 3 && intTmp != 4 && intTmp != 6 && intTmp != 7) {
            return OpenApiExceptionCode.D422;
         }

         ++chgFlag;
      }

      if (in.getMnt_pixel_shift() != null && !in.getMnt_pixel_shift().equals("")) {
         if (db != null && db.getMnt_pixel_shift().equals(in.getMnt_pixel_shift())) {
            return OpenApiExceptionCode.D423;
         }

         arrOsdType = in.getMnt_pixel_shift().split(";");
         if (arrOsdType.length != 4) {
            return OpenApiExceptionCode.D424;
         }

         intTmp = false;
         intTmp = Integer.parseInt(arrOsdType[0]);
         if (!checkOnOff(intTmp)) {
            return OpenApiExceptionCode.D424;
         }

         i = 1;

         while(true) {
            if (i >= 4) {
               ++chgFlag;
               break;
            }

            intTmp = Integer.parseInt(arrOsdType[i]);
            if (intTmp > 4 || intTmp < 0) {
               return OpenApiExceptionCode.D424;
            }

            ++i;
         }
      }

      if (in.getMnt_safety_lock() != null) {
         if (db != null && db.getMnt_safety_lock().equals(in.getMnt_safety_lock())) {
            return OpenApiExceptionCode.D425;
         }

         if (!checkOnOff(in.getMnt_safety_lock().intValue())) {
            return OpenApiExceptionCode.D426;
         }

         ++chgFlag;
      }

      if (in.getMisc_remocon() != null) {
         if (db != null && db.getMisc_remocon().equals(in.getMisc_remocon())) {
            return OpenApiExceptionCode.D435;
         }

         if (!checkOnOff(in.getMisc_remocon().intValue())) {
            return OpenApiExceptionCode.D436;
         }

         ++chgFlag;
      }

      if (in.getMisc_panel_lock() != null) {
         if (db != null && db.getMisc_panel_lock().equals(in.getMisc_panel_lock())) {
            return OpenApiExceptionCode.D437;
         }

         if (!checkOnOff(in.getMisc_panel_lock().intValue())) {
            return OpenApiExceptionCode.D438;
         }

         ++chgFlag;
      }

      if (in.getMisc_osd() != null) {
         if (db != null && db.getMisc_osd().equals(in.getMisc_osd())) {
            return OpenApiExceptionCode.D439;
         }

         if (!checkOnOff(in.getMisc_osd().intValue())) {
            return OpenApiExceptionCode.D440;
         }

         ++chgFlag;
      }

      if (in.getMisc_all_lock() != null) {
         if (db != null && db.getMisc_all_lock().equals(in.getMisc_all_lock())) {
            return OpenApiExceptionCode.D443;
         }

         if (!checkOnOff(in.getMisc_all_lock().intValue())) {
            return OpenApiExceptionCode.D444;
         }

         ++chgFlag;
      }

      if (in.getDiagnosis_alarm_temperature() != null) {
         if (db != null && db.getDiagnosis_alarm_temperature().equals(in.getDiagnosis_alarm_temperature())) {
            return OpenApiExceptionCode.D441;
         }

         intTmp = in.getDiagnosis_alarm_temperature().intValue();
         if (intTmp > 125 || intTmp < 75) {
            return OpenApiExceptionCode.D442;
         }

         ++chgFlag;
      }

      if (in.getAdvanced_osd_display_type() != null && !in.getAdvanced_osd_display_type().equals("")) {
         if (db != null && db.getAdvanced_osd_display_type().equals(in.getAdvanced_osd_display_type())) {
            return OpenApiExceptionCode.D427;
         }

         arrOsdType = in.getAdvanced_osd_display_type().split(";");
         if (arrOsdType.length != 2) {
            return OpenApiExceptionCode.D428;
         }

         intTmp = false;
         intTmp = Integer.parseInt(arrOsdType[0]);
         if (intTmp > 3 || intTmp < 0) {
            return OpenApiExceptionCode.D428;
         }

         intTmp = Integer.parseInt(arrOsdType[1]);
         if (!checkOnOff(intTmp)) {
            return OpenApiExceptionCode.D428;
         }

         ++chgFlag;
      }

      if (in.getAdvanced_fan_control() != null) {
         if (db != null && db.getAdvanced_fan_control().equals(in.getAdvanced_fan_control())) {
            return OpenApiExceptionCode.D429;
         }

         if (!checkOnOff(in.getAdvanced_fan_control().intValue())) {
            return OpenApiExceptionCode.D430;
         }

         ++chgFlag;
      }

      if (in.getAdvanced_auto_power() != null) {
         if (db != null && db.getAdvanced_auto_power().equals(in.getAdvanced_auto_power())) {
            return OpenApiExceptionCode.D431;
         }

         if (!checkOnOff(in.getAdvanced_auto_power().intValue())) {
            return OpenApiExceptionCode.D432;
         }

         ++chgFlag;
      }

      if (in.getAdvanced_stand_by() != null) {
         if (db != null && db.getAdvanced_stand_by().equals(in.getAdvanced_stand_by())) {
            return OpenApiExceptionCode.D433;
         }

         intTmp = in.getAdvanced_stand_by().intValue();
         if (intTmp > 2 || intTmp < 0) {
            return OpenApiExceptionCode.D434;
         }

         ++chgFlag;
      }

      if (chgFlag <= 0) {
         return OpenApiExceptionCode.D401;
      } else {
         return null;
      }
   }

   public static String[] checkDeviceTimeConfEntity(DeviceTimeConf in, int flag) {
      DeviceTimeConf db = null;
      String deviceModelCode = "";
      String modelKind = "";

      int timerCnt;
      try {
         if (flag == 1) {
            DeviceInfo mgr = DeviceInfoImpl.getInstance();
            deviceModelCode = mgr.getDeviceModelCodeByDeviceId(in.getDevice_id());
         }

         modelKind = MDCTimeStrUtils.getModelKind(deviceModelCode);
         timerCnt = MDCTimeStrUtils.getTimerCnt(deviceModelCode);
      } catch (Exception var12) {
         logger.error(var12);
         return OpenApiExceptionCode.D013;
      }

      int chgNewFlag = 0;
      int chgOldFlag = 0;
      if (in.getDevice_id() != null && !in.getDevice_id().equals("")) {
         try {
            if (flag == 1) {
               DeviceTimeConfManager deviceDao = DeviceTimeConfManagerImpl.getInstance();
               if (modelKind.equalsIgnoreCase("NEW")) {
                  db = deviceDao.getDeviceNewTimeConf(in.getDevice_id(), timerCnt);
               } else {
                  db = deviceDao.getDeviceOldTimeConf(in.getDevice_id());
               }
            }
         } catch (Exception var11) {
            logger.error(var11);
            return OpenApiExceptionCode.D500;
         }
      }

      boolean intTmp;
      int i;
      String[] arrOffTime;
      int intTmp;
      if (in.getTimer_clock() != null && !in.getTimer_clock().equals("")) {
         if (db != null && db.getTimer_clock().equals(in.getTimer_clock())) {
            return OpenApiExceptionCode.D503;
         }

         arrOffTime = in.getTimer_clock().split(";");
         if (arrOffTime.length != 7) {
            return OpenApiExceptionCode.D504;
         }

         intTmp = false;

         for(i = 0; i < arrOffTime.length; ++i) {
            intTmp = Integer.parseInt(arrOffTime[i]);
            switch(i) {
            case 0:
               if (!checkDay(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 1:
               if (!checkHour12(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 2:
               if (!checkMinute(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 3:
               if (!checkMonth(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
            case 4:
            case 5:
            default:
               break;
            case 6:
               if (!checkAmPm(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
            }
         }

         ++chgNewFlag;
      }

      if (in.getTimer_timer1() != null && !in.getTimer_timer1().equals("")) {
         if (db != null && db.getTimer_timer1().equals(in.getTimer_timer1())) {
            return OpenApiExceptionCode.D505;
         }

         arrOffTime = in.getTimer_timer1().split(";");
         if (arrOffTime.length != 15 && arrOffTime.length != 13) {
            return OpenApiExceptionCode.D506;
         }

         intTmp = false;

         for(i = 0; i < arrOffTime.length; ++i) {
            intTmp = Integer.parseInt(arrOffTime[i]);
            switch(i) {
            case 0:
            case 4:
               if (!checkHour12(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 1:
            case 5:
               if (!checkMinute(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 2:
            case 6:
               if (!checkAmPm(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 3:
            case 7:
               if (!checkOnOff(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 8:
            case 10:
               if (!checkTimeRepeat(intTmp)) {
                  return OpenApiExceptionCode.D507;
               }
               break;
            case 9:
            case 11:
               if (!checkTimeManualWeekly(intTmp)) {
                  return OpenApiExceptionCode.D507;
               }
               break;
            case 12:
               if (!checkVolume(intTmp)) {
                  return OpenApiExceptionCode.D507;
               }
               break;
            case 13:
               if (!checkBasicInputSource(intTmp)) {
                  return OpenApiExceptionCode.D507;
               }
               break;
            case 14:
               if (!checkTimeHolidayApply(intTmp)) {
                  return OpenApiExceptionCode.D507;
               }
            }
         }

         ++chgNewFlag;
      }

      if (in.getTimer_timer2() != null && !in.getTimer_timer2().equals("")) {
         if (db != null && db.getTimer_timer2().equals(in.getTimer_timer2())) {
            return OpenApiExceptionCode.D508;
         }

         arrOffTime = in.getTimer_timer2().split(";");
         if (arrOffTime.length != 15 && arrOffTime.length != 13) {
            return OpenApiExceptionCode.D509;
         }

         intTmp = false;

         for(i = 0; i < arrOffTime.length; ++i) {
            intTmp = Integer.parseInt(arrOffTime[i]);
            switch(i) {
            case 0:
            case 4:
               if (!checkHour12(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 1:
            case 5:
               if (!checkMinute(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 2:
            case 6:
               if (!checkAmPm(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 3:
            case 7:
               if (!checkOnOff(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 8:
            case 10:
               if (!checkTimeRepeat(intTmp)) {
                  return OpenApiExceptionCode.D510;
               }
               break;
            case 9:
            case 11:
               if (!checkTimeManualWeekly(intTmp)) {
                  return OpenApiExceptionCode.D510;
               }
               break;
            case 12:
               if (!checkVolume(intTmp)) {
                  return OpenApiExceptionCode.D510;
               }
               break;
            case 13:
               if (!checkBasicInputSource(intTmp)) {
                  return OpenApiExceptionCode.D510;
               }
               break;
            case 14:
               if (!checkTimeHolidayApply(intTmp)) {
                  return OpenApiExceptionCode.D510;
               }
            }
         }

         ++chgNewFlag;
      }

      if (in.getTimer_timer3() != null && !in.getTimer_timer3().equals("")) {
         if (db != null && db.getTimer_timer3().equals(in.getTimer_timer3())) {
            return OpenApiExceptionCode.D511;
         }

         arrOffTime = in.getTimer_timer1().split(";");
         if (arrOffTime.length != 15 && arrOffTime.length != 13) {
            return OpenApiExceptionCode.D512;
         }

         intTmp = false;

         for(i = 0; i < arrOffTime.length; ++i) {
            intTmp = Integer.parseInt(arrOffTime[i]);
            switch(i) {
            case 0:
            case 4:
               if (!checkHour12(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 1:
            case 5:
               if (!checkMinute(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 2:
            case 6:
               if (!checkAmPm(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 3:
            case 7:
               if (!checkOnOff(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 8:
            case 10:
               if (!checkTimeRepeat(intTmp)) {
                  return OpenApiExceptionCode.D513;
               }
               break;
            case 9:
            case 11:
               if (!checkTimeManualWeekly(intTmp)) {
                  return OpenApiExceptionCode.D513;
               }
               break;
            case 12:
               if (!checkVolume(intTmp)) {
                  return OpenApiExceptionCode.D513;
               }
               break;
            case 13:
               if (!checkBasicInputSource(intTmp)) {
                  return OpenApiExceptionCode.D513;
               }
               break;
            case 14:
               if (!checkTimeHolidayApply(intTmp)) {
                  return OpenApiExceptionCode.D513;
               }
            }
         }

         ++chgNewFlag;
      }

      if (in.getTimer_holiday() != null && !in.getTimer_holiday().equals("")) {
         arrOffTime = in.getTimer_holiday().split(";");
         if (arrOffTime.length != 5) {
            return OpenApiExceptionCode.D515;
         }

         intTmp = false;
         i = 0;

         while(true) {
            if (i >= arrOffTime.length) {
               ++chgNewFlag;
               break;
            }

            intTmp = Integer.parseInt(arrOffTime[i]);
            switch(i) {
            case 0:
               if (intTmp > 2 || intTmp < 0) {
                  return OpenApiExceptionCode.D516;
               }
               break;
            case 1:
            case 3:
               if (!checkMonth(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 2:
            case 4:
               if (!checkDay(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
            }

            ++i;
         }
      }

      if (in.getTime_current_time() != null && !in.getTime_current_time().equals("")) {
         if (db != null && db.getTime_current_time().equals(in.getTime_current_time())) {
            return OpenApiExceptionCode.D517;
         }

         arrOffTime = in.getTime_current_time().split(";");
         if (arrOffTime.length != 3) {
            return OpenApiExceptionCode.D518;
         }

         intTmp = false;

         for(i = 0; i < arrOffTime.length; ++i) {
            intTmp = Integer.parseInt(arrOffTime[i]);
            switch(i) {
            case 0:
               if (!checkAmPm(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 1:
               if (!checkHour12(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 2:
               if (!checkMinute(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
            }
         }

         ++chgOldFlag;
      }

      if (in.getTime_on_time() != null && !in.getTime_on_time().equals("")) {
         if (db != null && db.getTime_on_time().equals(in.getTime_on_time())) {
            return OpenApiExceptionCode.D519;
         }

         arrOffTime = in.getTime_on_time().split(";");
         if (arrOffTime.length != 6) {
            return OpenApiExceptionCode.D520;
         }

         intTmp = false;

         for(i = 0; i < arrOffTime.length; ++i) {
            intTmp = Integer.parseInt(arrOffTime[i]);
            switch(i) {
            case 0:
               if (!checkAmPm(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 1:
               if (!checkHour12(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 2:
               if (!checkMinute(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 3:
               if (!checkVolume(intTmp)) {
                  return OpenApiExceptionCode.D521;
               }
               break;
            case 4:
               if (!checkOnOff(intTmp)) {
                  return OpenApiExceptionCode.D521;
               }
               break;
            case 5:
               if (!checkBasicInputSource(intTmp)) {
                  return OpenApiExceptionCode.D521;
               }
            }
         }

         ++chgOldFlag;
      }

      if (in.getTime_off_time() != null && !in.getTime_off_time().equals("")) {
         if (db != null && db.getTime_off_time().equals(in.getTime_off_time())) {
            return OpenApiExceptionCode.D522;
         }

         arrOffTime = in.getTime_off_time().split(";");
         if (arrOffTime.length != 4) {
            return OpenApiExceptionCode.D523;
         }

         intTmp = false;

         for(i = 0; i < arrOffTime.length; ++i) {
            intTmp = Integer.parseInt(arrOffTime[i]);
            switch(i) {
            case 0:
               if (!checkAmPm(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 1:
               if (!checkHour12(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 2:
               if (!checkMinute(intTmp)) {
                  return OpenApiExceptionCode.D502;
               }
               break;
            case 3:
               if (!checkOnOff(intTmp)) {
                  return OpenApiExceptionCode.D524;
               }
            }
         }

         ++chgOldFlag;
      }

      if (modelKind.equalsIgnoreCase("NEW") && chgOldFlag > 0) {
         return OpenApiExceptionCode.D525;
      } else if (modelKind.equalsIgnoreCase("OLD") && chgNewFlag > 0) {
         return OpenApiExceptionCode.D526;
      } else {
         return chgNewFlag + chgOldFlag <= 0 ? OpenApiExceptionCode.D501 : null;
      }
   }

   private static boolean checkMonth(int month) {
      return month <= 12 && month >= 1;
   }

   private static boolean checkDay(int day) {
      return day <= 31 && day >= 1;
   }

   private static boolean checkHour12(int hour) {
      return hour <= 12 && hour >= 0;
   }

   private static boolean checkHour24(int hour) {
      return hour <= 23 && hour >= 0;
   }

   private static boolean checkMinute(int min) {
      return min <= 60 && min >= 0;
   }

   private static boolean checkSec(int sec) {
      return sec <= 60 && sec >= 0;
   }

   private static boolean checkAmPm(int ampm) {
      return ampm <= 1 && ampm >= 0;
   }

   private static boolean checkOnOff(int onOff) {
      return onOff <= 1 && onOff >= 0;
   }

   private static boolean checkBasicInputSource(int inSource) {
      return inSource == 4 || inSource == 8 || inSource == 12 || inSource == 20 || inSource == 24 || inSource == 30 || inSource == 32 || inSource == 33 || inSource == 35 || inSource == 37 || inSource == 48 || inSource == 64 || inSource == 96;
   }

   private static boolean checkTimeRepeat(int repeat) {
      return repeat <= 5 && repeat >= 0;
   }

   private static boolean checkTimeManualWeekly(int weekly) {
      return weekly <= 255 && weekly >= 0;
   }

   private static boolean checkVolume(int volume) {
      return volume <= 100 && volume >= 0;
   }

   private static boolean checkTimeHolidayApply(int hApply) {
      return hApply <= 3 && hApply >= 0;
   }

   public static void setDefaultDevice(Device device) {
      device.setDevice_model_code("7000");
      device.setDevice_model_name("ExtraDisplay");
      device.setDevice_name("-");
      device.setSerial_decimal("-");
      device.setScreen_size((String)null);
      device.setFirmware_version("Unknown");
      device.setApplication_version("Unknown");
      device.setRule_version("-");
      device.setOs_image_version("Unknown");
      device.setCpu_type("Unknown");
      device.setHdd_size(0L);
      device.setMem_size(0L);
      device.setVideo_adapter("Unknown");
      device.setNetwork_driver("Unknown");
      device.setNetwork_adapter("Unknown");
      device.setMac_address("-");
      device.setIp_setting_type("-");
      device.setIp_address("-");
      device.setSubnet_mask("-");
      device.setGateway("-");
      device.setDns_server_main("0.0.0.0");
      device.setDns_server_sub("0.0.0.0");
      device.setPort(0L);
      device.setTrigger_interval(10L);
      device.setMonitoring_interval(5L);
      device.setTunneling_server("-");
      device.setBg_color("-");
      device.setAuto_time_setting(false);
      device.setOn_timer_setting(false);
      device.setOff_timer_setting(false);
      device.setTime_zone_index("-");
      device.setDay_light_saving(false);
      device.setFtp_connect_mode("-");
      device.setRepository_path("-");
      device.setMagicinfo_server_url("-");
      device.setCabinet_group_layout("-");
      device.setScreen_capture_interval(10L);
      device.setEwf_state(false);
      device.setResolution("-");
      device.setDevice_type("-");
      device.setIs_reverse(false);
      device.setProxy_setting("0;;;;");
      device.setConnection_limit_time(0L);
      device.setMnt_folder_path("-");
      device.setSystem_restart_interval("-");
      device.setLog_mnt("-");
      device.setProof_of_play_mnt("-");
      device.setContent_mnt("-");
      device.setPlay_mode(0L);
      device.setReset_password(0L);
      device.setTime_zone_version("-");
      device.setDisk_space_repository(10000000000L);
      device.setCreator_id("-");
      device.setCaptured_file_name("-");
      device.setAuto_ip_set(false);
      device.setAuto_computer_name_set(false);
      device.setComputer_name("-");
      device.setVnc_password("-");
      device.setUse_mpplayer(false);
      device.setFiledata_del_size(0L);
      device.setContent_ready_interval(0L);
      device.setPlayer_start_timeout(0L);
      device.setVideo_memory(0L);
   }
}
