package com.samsung.magicinfo.framework.content.manager;

import com.samsung.common.db.PagedListInfo;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.AbilityUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.content.dao.PlaylistDao;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.Effect;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.entity.PlaylistContent;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.setup.entity.ContentTagEntity;
import com.samsung.magicinfo.framework.setup.entity.TagEntity;
import com.samsung.magicinfo.framework.setup.manager.TagInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.entity.UserGroup;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.Logger;

public class PlaylistInfoImpl implements PlaylistInfo {
   Logger logger = LoggingManagerV2.getLogger(PlaylistInfoImpl.class);
   static PlaylistInfoImpl instance = null;
   static PlaylistDao dao = null;

   private PlaylistInfoImpl() {
      super();
   }

   public static PlaylistInfo getInstance() {
      Class var0;
      if (instance == null) {
         var0 = PlaylistInfoImpl.class;
         synchronized(PlaylistInfoImpl.class) {
            if (instance == null) {
               instance = new PlaylistInfoImpl();
            }
         }
      }

      if (dao == null) {
         var0 = PlaylistInfoImpl.class;
         synchronized(PlaylistInfoImpl.class) {
            if (dao == null) {
               dao = new PlaylistDao();
            }
         }
      }

      return instance;
   }

   public Boolean getCanEditOthers(String userID, String groupType, HttpServletRequest request) throws SQLException {
      Boolean canEditOthers = false;
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String loginID = userContainer.getUser().getUser_id();
      if (groupType.equalsIgnoreCase("USER")) {
         UserInfo user = UserInfoImpl.getInstance();
         User myInfo = user.getUserByUserId(loginID);
         User userInfo = user.getUserByUserId(userID);
         if (userInfo != null && !userInfo.getRoot_group_id().equals(myInfo.getRoot_group_id()) && myInfo.getRoot_group_id() == 0L) {
            canEditOthers = true;
         } else {
            AbilityUtils ability = new AbilityUtils();
            canEditOthers = ability.checkAuthority("Playlist Manage");
         }
      } else if (groupType.equalsIgnoreCase("ORGAN")) {
         AbilityUtils ability = new AbilityUtils();
         canEditOthers = ability.checkAuthority("Playlist Manage");
      }

      return canEditOthers;
   }

   public String getPlaylistName(String playlistID) throws SQLException {
      return dao.getPlaylistName(playlistID);
   }

   public List getPlaylistAllVerInfo(String playlistID) throws SQLException {
      return dao.getPlaylistAllVerInfo(playlistID);
   }

   public Playlist getPlaylistActiveVerInfo(String playlistID) throws SQLException {
      return dao.getPlaylistActiveVerInfo(playlistID);
   }

   public List getPlaylistActiveVerInfoForSync(String playlistID) throws SQLException {
      return dao.getPlaylistActiveVerInfoForSync(playlistID);
   }

   public Long getPlaylistActiveVersionId(String playlistID) throws SQLException {
      return dao.getPlaylistActiveVersionId(playlistID);
   }

   public Playlist getPlaylistVerInfo(String playlistID, Long versionID) throws SQLException {
      return dao.getPlaylistVerInfo(playlistID, versionID);
   }

   public List getContentListOfPlaylist(String playlistID, Long versionID) throws SQLException {
      return dao.getContentListOfPlaylist(playlistID, versionID);
   }

   public List getTagContentListOfPlaylist(String playlistId, Long versionId) throws SQLException {
      return dao.getTagContentListOfPlaylist(playlistId, versionId);
   }

   public List getContentListOfSyncGroup(String playlistID, Long versionID) throws SQLException {
      return dao.getContentListOfSyncGroup(playlistID, versionID);
   }

   public List getSearchList(Map map) throws SQLException {
      return dao.getSearchList(map);
   }

   public int getSearchListCnt(Map map) throws SQLException {
      return dao.getSearchListCnt(map);
   }

   public List getPlaylistList(Map map) throws SQLException {
      return dao.getPlaylistList(map);
   }

   public List getAllDeletedPlaylistList(String creatorID) throws SQLException {
      return dao.getAllDeletedPlaylistList(creatorID);
   }

   public List getAllPlaylistList(String creatorID) throws SQLException {
      return dao.getAllPlaylistList(creatorID);
   }

   public List getPlaylistListByUser(String creatorID, boolean canReadUnsharedPlaylist) throws SQLException {
      return dao.getPlaylistListByUser(creatorID, canReadUnsharedPlaylist);
   }

   public List getAllPlaylistList(String creatorID, Long groupID) throws SQLException {
      return dao.getAllPlaylistList(creatorID, groupID);
   }

   public List getContentList(String playlistID, Long versionID) throws SQLException {
      return dao.getContentList(playlistID, versionID);
   }

   public List getTagList(String playlistID, Long versionID) throws SQLException {
      return dao.getTagList(playlistID, versionID);
   }

   public List getTagListWithExpiredDate(String playlistID, Long versionID, String expiredDate) throws SQLException {
      return dao.getTagListWithExpiredDate(playlistID, versionID, expiredDate);
   }

   public List getTagConditionWithTagIdList(String playlistId, long versionId, long tagId) throws SQLException {
      return dao.getTagConditionWithTagIdList(playlistId, versionId, tagId);
   }

   public List getTagConditionWithTagIdList(String playlistId, long versionId, long tagId, String number_str) throws SQLException {
      return dao.getTagConditionWithTagIdList(playlistId, versionId, tagId, number_str);
   }

   public int getTagConditionWithTagIdListSize(String playlistId, long versionId, long tagId) throws SQLException {
      return dao.getTagConditionWithTagIdListSize(playlistId, versionId, tagId);
   }

   public int getTagConditionWithTagIdListSize(String playlistId, long versionId, long tagId, String number_str) throws SQLException {
      return dao.getTagConditionWithTagIdListSize(playlistId, versionId, tagId, number_str);
   }

   public int getContentCount(String playlistID, Long versionID) throws SQLException {
      return dao.getContentCount(playlistID, versionID);
   }

   public int getCountSyncGroup(String playlistID, Long versionID) throws SQLException {
      return dao.getCountSyncGroup(playlistID, versionID);
   }

   public String getMainContentId(String playlistID) throws SQLException {
      return dao.getMainContentId(playlistID);
   }

   public List getActiveVerContentList(String playlistID) throws SQLException {
      return dao.getActiveVerContentList(playlistID);
   }

   public List getActiveVerContentListForDownloadCheck(String playlistID) throws SQLException {
      return dao.getActiveVerContentListForDownloadCheck(playlistID);
   }

   public PlaylistContent getContentEffectInfo(String playlistID, Long versionID, String contentID) throws SQLException {
      return dao.getContentEffectInfo(playlistID, versionID, contentID);
   }

   public PlaylistContent getContentEffectInfoByOrder(String playlistID, Long versionID, String contentOrder) throws SQLException {
      return dao.getContentEffectInfoByOrder(playlistID, versionID, contentOrder);
   }

   public Effect getEffectInfoByEffectName(String effectName) throws SQLException {
      return dao.getEffectInfoByEffectName(effectName);
   }

   public Effect getSocEffectInfoByEffectName(String effectName) throws SQLException {
      return dao.getSocEffectInfoByEffectName(effectName);
   }

   public Effect getVWLEffectInfoByEffectName(String effectName) throws SQLException {
      return dao.getVWLEffectInfoByEffectName(effectName);
   }

   public Boolean isExistPlaylistID(String playlistID) throws SQLException {
      return dao.isExistPlaylistID(playlistID);
   }

   public Boolean isExistPlaylistVersion(String playlistID, Long versionID) throws SQLException {
      return dao.isExistPlaylistVersion(playlistID, versionID);
   }

   public Boolean isUpdatablePlaylist(String playlistID) throws SQLException {
      return dao.isUpdatablePlaylist(playlistID);
   }

   public Boolean isDeletablePlaylist(String playlistID, String userID, String sessionID) throws SQLException {
      return dao.isDeletablePlaylist(playlistID, userID, sessionID);
   }

   public Boolean isLockedPlaylist(String playlistID, String sessionID) throws SQLException {
      return dao.isLockedPlaylist(playlistID, sessionID);
   }

   public int addPlaylist(Playlist playlist) throws SQLException, ConfigException, Exception {
      return dao.addPlaylist(playlist);
   }

   public int deleteContentFromPlaylist(String playlistID, String[] contentIdList, String creatorID, String sessionID) throws SQLException, ConfigException, Exception {
      return dao.deleteContentFromPlaylist(playlistID, contentIdList, creatorID, sessionID);
   }

   public int deleteContentFromPlaylist(String[] playlistIdList, String[] contentIdList, String userID, String sessionID) throws SQLException, ConfigException, Exception {
      int result = 0;

      for(int i = 0; i < playlistIdList.length; ++i) {
         result += dao.deleteContentFromPlaylist(playlistIdList[i], contentIdList, userID, sessionID);
      }

      return result;
   }

   public List getPlaylistList(String userId, int startIndex, int resultsCount) {
      return dao.getPlaylistList(userId, startIndex, resultsCount);
   }

   public List getPlaylistListByDeviceType(String userId, int startIndex, int resultsCount, String deviceType, float deviceTypeVersion) {
      return dao.getPlaylistListByDeviceType(userId, startIndex, resultsCount, deviceType, deviceTypeVersion);
   }

   public List getPlaylistListByDeviceType(String userId, int startIndex, int resultsCount, String deviceType, float deviceTypeVersion, List selectedDeviceType) {
      return dao.getPlaylistListByDeviceType(userId, startIndex, resultsCount, deviceType, deviceTypeVersion, selectedDeviceType);
   }

   public Integer getPlaylistListCountByDeviceType(String userId, String deviceType, float deviceTypeVersion) {
      return dao.getPlaylistListCountByCountDeviceType(userId, deviceType, deviceTypeVersion);
   }

   public Integer getPlaylistListCountByDeviceType(String userId, String deviceType, float deviceTypeVersion, List selectedDeviceType) {
      return dao.getPlaylistListCountByCountDeviceType(userId, deviceType, deviceTypeVersion, selectedDeviceType);
   }

   public List getPlaylistListToDeleteContent(String userId, String[] contentIdList, int startIndex, int resultsCount) {
      return dao.getPlaylistListToDeleteContent(userId, contentIdList, startIndex, resultsCount);
   }

   public int addContentToPlaylist(String playlistID, String[] contentIdList, String creatorID) throws SQLException, ConfigException, Exception {
      return dao.addContentToPlaylist(playlistID, contentIdList, creatorID);
   }

   public int addContentToPlaylist(String[] playlistIdList, String[] contentIdList, String creatorID) throws SQLException, ConfigException, Exception {
      int result = 0;

      for(int i = 0; i < playlistIdList.length; ++i) {
         result += dao.addContentToPlaylist(playlistIdList[i], contentIdList, creatorID);
      }

      return result;
   }

   public int setMaxVersionPlaylistActive(String playlistID) throws SQLException {
      return dao.setMaxVersionPlaylistActive(playlistID);
   }

   public int addPlaylistInfo(Playlist playlist) throws SQLException, ConfigException {
      return dao.addPlaylistInfo(playlist);
   }

   public int addMapPlaylistContent(PlaylistContent content) throws SQLException {
      return dao.addMapPlaylistContent(content);
   }

   public int addMapGroupPlaylist(String playlistID, Long groupID) throws SQLException {
      return dao.addMapGroupPlaylist(playlistID, groupID);
   }

   public int setPlaylistInfo(String playlistID, String playlistName, String playlistMetaData, int shareFlag, int ignoreTag, int evenessPlayback) throws SQLException, ConfigException {
      return dao.setPlaylistInfo(playlistID, playlistName, playlistMetaData, shareFlag, ignoreTag, evenessPlayback);
   }

   public int setActiveVersion(String playlistID, Long versionID) throws SQLException, ConfigException, Exception {
      return dao.setActiveVersion(playlistID, versionID);
   }

   public int deletePlaylist(String playlistID, String userID, String sessionID) throws SQLException, ConfigException {
      return dao.deletePlaylist(playlistID, userID, sessionID);
   }

   public int restorePlaylist(String playlistID) throws SQLException, ConfigException {
      return dao.restorePlaylist(playlistID);
   }

   public int deletePlaylistCompletely(String playlistID) throws SQLException {
      try {
         if (dao.checkTagPlaylist(playlistID) > 0) {
            dao.deleteTagPlaylistPerm(playlistID);
         }
      } catch (Exception var3) {
         this.logger.error("[MagicInfo_Playlist] error about during delete tag playlist id : " + playlistID + " error : " + var3.getMessage());
      }

      return dao.deletePlaylistCompletely(playlistID);
   }

   public int setPlaylistLock(String playlistID, String sessionID) throws SQLException {
      return dao.setPlaylistLock(playlistID, sessionID);
   }

   public int setPlaylistGroup(String playlistID, Long groupID) throws SQLException, ConfigException {
      return dao.setPlaylistGroup(playlistID, groupID);
   }

   public int setPlaylistShare(String playlistID, Long shareFlag) throws SQLException, ConfigException {
      return dao.setPlaylistShare(playlistID, shareFlag);
   }

   public int setPlaylistMetaData(String playlistID, String metaData) throws SQLException, ConfigException {
      return dao.setPlaylistMetaData(playlistID, metaData);
   }

   public int setPlaylistEffect(PlaylistContent content) throws SQLException, ConfigException {
      return dao.setPlaylistEffect(content);
   }

   public Long getRootId(String userID) throws SQLException {
      return dao.getRootId(userID);
   }

   public Long getRootId(String userId, long organizationId) throws SQLException {
      return dao.getRootId(userId, organizationId);
   }

   public Boolean isExistGroupName(String groupName, String userID) throws SQLException {
      return dao.isExistGroupName(groupName, userID);
   }

   public Long getGroupId(String playlistID) throws SQLException {
      return dao.getGroupId(playlistID);
   }

   public String getGroupName(Long groupID) throws SQLException {
      return dao.getGroupName(groupID);
   }

   public Group getGroupInfo(Long groupID) throws SQLException {
      return dao.getGroupInfo(groupID);
   }

   public List getGroupList(String creatorID) throws SQLException {
      return dao.getGroupList(creatorID);
   }

   public List getGroupList(String creatorID, long organization_id) throws SQLException {
      return dao.getGroupList(creatorID, organization_id);
   }

   public Long addGroup(Group group) throws SQLException, ConfigException {
      return dao.addGroup(group);
   }

   public Long addDefaultGroup(String userID) throws SQLException, ConfigException {
      return dao.addDefaultGroup(userID);
   }

   public Long addDefaultGroup(String userID, long organization_id) throws SQLException, ConfigException {
      return dao.addDefaultGroup(userID, organization_id);
   }

   public int setGroupInfo(Group group) throws SQLException {
      return dao.setGroupInfo(group);
   }

   public int deleteGroup(Long groupID) throws SQLException {
      return dao.deleteGroup(groupID);
   }

   public Boolean isDeletableGroup(Long groupID) throws SQLException {
      return dao.isDeletableGroup(groupID);
   }

   public List getGroupedPlaylistIdList(Long groupID) throws SQLException {
      return dao.getGroupedPlaylistIdList(groupID);
   }

   public List getChildGroupList(Long group_id, boolean recursive, String creatorID) throws SQLException {
      return dao.getChildGroupList(group_id, recursive, creatorID);
   }

   public List getChildGroupList(Long group_id, boolean recursive, String creatorID, long organizationId) throws SQLException {
      return dao.getChildGroupList(group_id, recursive, creatorID, organizationId);
   }

   public List getChildGroupIdList(Long group_id, boolean recursive) throws SQLException {
      return dao.getChildGroupIdList(group_id, recursive);
   }

   public PagedListInfo getPagedList(int startPos, int pageSize, Map condition, String section) throws Exception {
      int totCount = 0;
      List retList = null;
      if (section.equals("getSearchList")) {
         totCount = dao.getSearchListCnt(condition);
         retList = dao.getSearchListPage(condition, startPos, pageSize);
      } else if (section.equals("getPlaylistList")) {
         totCount = dao.getPlaylistListCnt(condition);
         retList = dao.getPlaylistListPage(condition, startPos, pageSize);
      } else if (section.equals("getPlaylistListByFilter")) {
         totCount = dao.getPlaylistListCnt(condition);
         retList = dao.getPlaylistListByFilter(condition, startPos, pageSize);
      }

      return new PagedListInfo(retList, totCount);
   }

   public int deletePlaylistVersion(String playlistID, String playlistVersionID, String userID, String sessionID) throws SQLException, ConfigException {
      return dao.deletePlaylistVersion(playlistID, playlistVersionID, userID, sessionID);
   }

   public List getPlaylistEffectList(String deviceType) throws SQLException, ConfigException {
      return dao.getPlaylistEffectList(deviceType);
   }

   public int deleteContentTag(String playlistId, String cId, int tagId) throws SQLException {
      return dao.deleteContentTag(playlistId, cId, tagId);
   }

   public int deleteContentTagAll(String playlistId, String cId) throws SQLException {
      return dao.deleteContentTag(playlistId, cId, 0);
   }

   public int setContentTag(String playlistId, Long versionID, String contentId, int contentOrder, String matchType, int tagId) throws SQLException {
      return dao.setContentTag(playlistId, versionID, contentId, contentOrder, matchType, tagId);
   }

   public List getContentTag(String playlistId, String cId, Long versionId) throws SQLException {
      return dao.getContentTag(playlistId, cId, versionId);
   }

   public List getContentTag(String playlistId, Long versionID, String contentId, int contentOrder) throws SQLException {
      return dao.getContentTag(playlistId, versionID, contentId, contentOrder);
   }

   public List getPlaylistTag(String playlistId, Long versionID, long playlistTagId, long tagOrder) throws SQLException {
      return dao.getPlaylistTag(playlistId, versionID, playlistTagId, tagOrder);
   }

   public String getTagValueListString(String playlistId, String cId, Long versionId) {
      StringBuffer result = new StringBuffer();
      TagInfoImpl tagDao = TagInfoImpl.getInstance();

      try {
         List tmp = dao.getContentTag(playlistId, cId, versionId);

         for(int k = 0; k < tmp.size(); ++k) {
            if (k > 0) {
               result.append(",");
            }

            int tagId = Integer.parseInt(((Map)tmp.get(k)).get("tag_id").toString());
            TagEntity tagEntity = tagDao.getTag(tagId);
            if (tagEntity != null) {
               result.append(tagEntity.getTag_value());
            }
         }
      } catch (Exception var10) {
      }

      return result.toString();
   }

   public String getTagValueListString(String playlistId, String cId, Long versionId, int contentOrder) {
      StringBuffer result = new StringBuffer();
      TagInfoImpl tagDao = TagInfoImpl.getInstance();

      try {
         List tmp = dao.getContentTag(playlistId, versionId, cId, contentOrder);

         for(int k = 0; k < tmp.size(); ++k) {
            if (k > 0) {
               result.append(",");
            }

            int tagId = Integer.parseInt(((Map)tmp.get(k)).get("tag_id").toString());
            TagEntity tagEntity = tagDao.getTag(tagId);
            if (tagEntity != null) {
               result.append(tagEntity.getTag_value());
            }
         }
      } catch (Exception var11) {
      }

      return result.toString();
   }

   public String getTagValueListStringInSchedule(String playlistId, Long versionId) {
      String resultStr = "";
      StringBuffer result = new StringBuffer();
      TagInfoImpl tagDao = TagInfoImpl.getInstance();

      try {
         List tmp = dao.getContentTagInSchedule(playlistId, versionId);

         for(int k = 0; k < tmp.size(); ++k) {
            if (k > 0) {
               result.append(",");
            }

            int tagId = Integer.parseInt(((Map)tmp.get(k)).get("tag_id").toString());
            TagEntity tagEntity = tagDao.getTag(tagId);
            if (tagEntity != null) {
               result.append(tagEntity.getTag_value());
            }
         }
      } catch (Exception var10) {
      }

      if (result != null && result.length() > 0) {
         resultStr = result.toString();
      } else {
         resultStr = "";
      }

      return resultStr;
   }

   public String getTagIdListString(String playlistId, Long versionID, String contentId, int contentOrder) {
      StringBuffer result = new StringBuffer();
      TagInfoImpl.getInstance();

      try {
         List tmp = dao.getContentTag(playlistId, versionID, contentId, contentOrder);

         for(int k = 0; k < tmp.size(); ++k) {
            if (k > 0) {
               result.append(",");
            }

            result.append(((Map)tmp.get(k)).get("tag_id").toString());
            result.append(":");
            result.append(((Map)tmp.get(k)).get("match_type").toString());
         }
      } catch (Exception var8) {
      }

      return result.toString();
   }

   public String getPlaylistContentTagList(String playlistID, Long versionID) throws SQLException {
      String result = "";
      StringBuffer resultBuffer = new StringBuffer();
      String splitTagChar = "↑";
      String splitInfoChar = "↓";
      String itemChar = ",";
      getInstance();
      if (playlistID != null && versionID != null) {
         try {
            List tagEntityList = dao.getPlaylistContentTagList(playlistID, versionID);
            HashMap tagMap = new HashMap();
            Iterator it = tagEntityList.iterator();

            while(it.hasNext()) {
               ContentTagEntity contentTagEntity = (ContentTagEntity)it.next();
               String key = contentTagEntity.getContent_id() + splitInfoChar + contentTagEntity.getContent_order() + splitInfoChar + contentTagEntity.getMatch_type();
               if (tagMap.containsKey(key)) {
                  tagMap.put(key, (String)tagMap.get(key) + splitTagChar + contentTagEntity.getTag_id());
               } else {
                  tagMap.put(key, splitInfoChar + contentTagEntity.getTag_id());
               }
            }

            it = tagMap.entrySet().iterator();

            while(it.hasNext()) {
               Entry entry = (Entry)it.next();
               resultBuffer.append(entry.getKey());
               resultBuffer.append(entry.getValue());
               resultBuffer.append(itemChar);
            }
         } catch (NumberFormatException var13) {
            this.logger.error("", var13);
         } catch (SQLException var14) {
            this.logger.error("", var14);
         }
      }

      result = resultBuffer.toString();
      if (result.length() > 0) {
         result = result.substring(0, result.length() - 1);
      }

      return result;
   }

   public List getPlaylistContentTagEntityList(String playlistId, Long versionId) throws SQLException {
      return dao.getPlaylistContentTagList(playlistId, versionId);
   }

   public int setContentDuraionByContentID(String contentID, Long contentDuration) {
      return dao.setContentDuraionByContentID(contentID, contentDuration);
   }

   public int setContentDuraionMilliByContentID(String contentID, String milli) {
      return dao.setContentDuraionMilliByContentID(contentID, milli);
   }

   public List getPlaylistIDListByContentID(String contentID) {
      return dao.getPlaylistIDListByContentID(contentID);
   }

   public int setPlaytime(String playlistID, Long versionID, String playTime) {
      return dao.setPlaytime(playlistID, versionID, playTime);
   }

   public Long getSumOfContentDuration(String playlist_id, Long version_id) {
      return dao.getSumOfContentDuration(playlist_id, version_id);
   }

   public String isDeleted(String playlistId) throws SQLException {
      return dao.isDelete(playlistId);
   }

   public String getCreatorIdByPlaylistId(String playlistId) throws SQLException {
      return dao.getCreatorIdByPlaylistId(playlistId);
   }

   public int unlockAllSession() throws SQLException {
      return dao.unlockAllSession();
   }

   public int setPlaylistUnlock(String playlistID, String sessionID) throws SQLException {
      return dao.setPlaylistUnlock(playlistID, sessionID);
   }

   public int setPlaylistUnlockBySessionID(String sessionID) throws SQLException {
      return dao.setPlaylistUnlockBySessionID(sessionID);
   }

   public void deleteContentFromPlaylist(String contentId) {
      try {
         dao.deleteContentFromPlaylist(contentId);
      } catch (SQLException var3) {
         this.logger.error("", var3);
      } catch (Exception var4) {
         this.logger.error("", var4);
      }

   }

   public List getContentOrderListOfPlaylist(String playlistId, Long versionId) {
      return dao.getContentOrderListOfPlaylist(playlistId, versionId);
   }

   public List getSubPlaylistContentOrderListOfPlaylist(String playlistId, Long versionId) {
      return dao.getSubPlaylistContentOrderListOfPlaylist(playlistId, versionId);
   }

   public void updateContentOrder(int newContentOrder, String playlistId, Long versionId, Long oldContentOrder) {
      try {
         dao.updateContentOrder(newContentOrder, playlistId, versionId, oldContentOrder);
      } catch (SQLException var6) {
         this.logger.error("", var6);
      } catch (Exception var7) {
         this.logger.error("", var7);
      }

   }

   public void updateSubPlaylistContentOrder(int newContentOrder, String playlistId, Long versionId, Long oldContentOrder) {
      try {
         dao.updateSubPlaylistContentOrder(newContentOrder, playlistId, versionId, oldContentOrder);
      } catch (SQLException var6) {
         this.logger.error("", var6);
      } catch (Exception var7) {
         this.logger.error("", var7);
      }

   }

   public void updateContentCount(int contentCount, String playlistId, Long versionId) {
      try {
         dao.updateContentCount(contentCount, playlistId, versionId);
      } catch (Exception var5) {
         this.logger.error("", var5);
      }

   }

   public List getSyncGroupInfo(String playlistId, Long versionId) throws SQLException, Exception {
      return dao.getSyncGroupInfo(playlistId, versionId);
   }

   public int getUsedPlaylistCount(long organizationId) throws SQLException {
      return dao.getUsedPlaylistCount(organizationId);
   }

   public ContentFile getThumbFileInfo(String contentId) throws SQLException {
      return dao.getThumbFileInfo(contentId);
   }

   public List getPlaylistTagConditionList(String playlistId, long versionId, long tagId) throws SQLException {
      return dao.getPlaylistTagConditionList(playlistId, versionId, tagId);
   }

   public boolean addTagConditionMapping(String playlistId, long versionId, long tagId, long tagConditionId) throws SQLException {
      return dao.addTagConditionMapping(playlistId, versionId, tagId, tagConditionId);
   }

   public ContentFile getTagPlaylistThumbFileInfo(String playlistId) throws SQLException {
      return dao.getTagPlaylistThumbFileInfo(playlistId);
   }

   public void checkExistTagCondition(String playlistId, Long versionId, Long tagId, Long conditionId) throws SQLException {
      if (dao.checkExistTagCondition(playlistId, versionId, tagId, conditionId) > 0) {
         dao.deleteTagPlaylistCondition(playlistId, versionId);
      }

   }

   public List getCntContentAtTagPlaylist(Long tagId, String conditionIds) throws SQLException {
      return dao.getCntContentAtTagPlaylist(tagId, conditionIds);
   }

   public List getThumbContentAtTagPlaylist(Long tagId, String conditionIds) throws SQLException {
      return dao.getThumbContentAtTagPlaylist(tagId, conditionIds);
   }

   public List getThumbContentAtTagPlaylistAll(Long tagId, String conditionIds) throws SQLException {
      return dao.getThumbContentAtTagPlaylistAll(tagId, conditionIds);
   }

   public List getTagPlaylistTagList(String playlistId, Long versionId) throws SQLException {
      return dao.getTagPlaylistTagList(playlistId, versionId);
   }

   public List getTagPlaylistTagConditionList(String playlistId, Long versionId) throws SQLException {
      return dao.getTagPlaylistTagConditionList(playlistId, versionId);
   }

   public String getConditionIdWithTagNumber(long TagId, String[] tagConditionEqual, String[] tagConditionUp, String[] tagConditionDown) throws SQLException {
      return dao.getConditionIdWithTagNumber(TagId, tagConditionEqual, tagConditionUp, tagConditionDown);
   }

   public List getCntContentAtTagPlaylist(Long tagId, String[] conditionIds) throws SQLException {
      return dao.getCntContentAtTagPlaylist(tagId, conditionIds);
   }

   public List getContentListFromTagId(List parameters) throws SQLException {
      return dao.getContentListFromTagId(parameters);
   }

   public String getTagConditionIdWithTagNumber(String playlistId, Long versionId, Long tagId, String number_str) throws SQLException {
      return dao.getTagConditionIdWithTagNumber(playlistId, versionId, tagId, number_str);
   }

   public List getListLinkedPlaylistProgramId(String playlistId) throws SQLException {
      return dao.getListLinkedPlaylistProgramId(playlistId);
   }

   public List getContentTagListWithPlaylistId(String playlistId) throws SQLException {
      return dao.getContentTagListWithPlaylistId(playlistId);
   }

   public List getListLinkedPlaylistPlaylistId(String playlistId) throws SQLException {
      return dao.getListLinkedPlaylistPlaylistId(playlistId);
   }

   public List getContentListWithTag(String playlistId, String contentId, Long versionId, Long contentOrder, List tagList) throws SQLException {
      return dao.getContentListWithTag(playlistId, contentId, versionId, contentOrder, tagList);
   }

   public boolean chkOrganizationByPlaylistId(String organization, String playlistId) throws SQLException {
      boolean rtn = false;
      String playlistOrg = dao.getOrganizationByPlaylistId(playlistId);
      return playlistOrg != null && playlistOrg.equals(organization) ? true : rtn;
   }

   public boolean checkMappingSchedule(long groupId, String userId) throws SQLException {
      return dao.checkMappingSchedule(groupId, userId);
   }

   public int getCountPlaylistToExpire(String userId, Long groupId, String stopDate, SelectCondition condition) throws SQLException {
      List groupList = this.getGroupIdsByOrgManagerUserId(userId, groupId);
      return dao.getCountPlaylistToExpire(groupList, userId, stopDate, condition);
   }

   public List getListPlaylistToExpire(int startPos, int pageSize, String userId, Long groupId, String stopDate, SelectCondition condition) throws SQLException {
      List groupList = this.getGroupIdsByOrgManagerUserId(userId, groupId);
      return dao.getListPlaylistToExpire(startPos - 1, pageSize, groupList, stopDate, condition);
   }

   private List getGroupIdsByOrgManagerUserId(String userId, Long groupId) throws SQLException {
      List groups = null;
      if (groupId == 0L) {
         groups = dao.getPlayListGroupByUserId(userId);
      }

      if (!CollectionUtils.isNotEmpty((Collection)groups)) {
         groups = new ArrayList();
         UserGroup group = new UserGroup();
         group.setGroup_id(groupId);
         ((List)groups).add(group);
      }

      return (List)groups;
   }

   public int deleteContentTagFromPlaylist(String contentId) throws SQLException {
      return dao.deleteContentTagFromPlaylist(contentId);
   }

   public void updateContentOrderOfTag(String playlistId, Long versionId, String contentId, int newContentOrder) {
      try {
         dao.updateContentOrderOfTag(playlistId, versionId, contentId, newContentOrder);
      } catch (SQLException var6) {
         this.logger.error("", var6);
      }

   }

   public long getDefaultPlaylistGroupId(String userId, long orgnizationId) throws SQLException {
      return dao.getDefaultPlaylistGroupId(userId, orgnizationId);
   }

   public void transferPlaylistToAdmin(User user, String[] deleteUsers) throws SQLException {
      try {
         if (user != null && user.isMu()) {
            List userManageGroupList = null;
            UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
            userManageGroupList = userGroupInfo.getMngGroupListByUserId(user.getUser_id());
            UserInfo userInfo = UserInfoImpl.getInstance();
            if (userManageGroupList != null) {
               Iterator var6 = userManageGroupList.iterator();

               while(var6.hasNext()) {
                  UserGroup userGroup = (UserGroup)var6.next();
                  long rootGroupId = userGroup.getRoot_group_id();
                  this.transferPlaylistToAdminSub(user, rootGroupId, deleteUsers);
               }
            }
         } else {
            this.transferPlaylistToAdminSub(user, user.getRoot_group_id(), deleteUsers);
         }
      } catch (Exception var10) {
         this.logger.error("", var10);
      }

   }

   public void transferPlaylistToAdmin(User user) throws SQLException {
      try {
         if (user != null && user.isMu()) {
            List userManageGroupList = null;
            UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
            userManageGroupList = userGroupInfo.getMngGroupListByUserId(user.getUser_id());
            if (userManageGroupList != null) {
               Iterator var4 = userManageGroupList.iterator();

               while(var4.hasNext()) {
                  UserGroup userGroup = (UserGroup)var4.next();
                  long rootGroupId = userGroup.getRoot_group_id();
                  this.transferPlaylistToAdminSub(user, rootGroupId);
               }
            }
         } else {
            this.transferPlaylistToAdmin(user, user.getRoot_group_id());
         }
      } catch (Exception var8) {
         this.logger.error("", var8);
      }

   }

   public void transferPlaylistToAdmin(User user, long orgId) throws SQLException {
      try {
         this.transferPlaylistToAdminSub(user, orgId);
      } catch (Exception var5) {
         this.logger.error("", var5);
      }

   }

   private void transferPlaylistToAdminSub(User user, long rootGroupId) {
      try {
         UserInfo userInfo = UserInfoImpl.getInstance();
         User admin = userInfo.getAdminOfOrganizationByRootGroupId(rootGroupId);
         long defaultContentGroupId = this.getDefaultPlaylistGroupId(admin.getUser_id(), rootGroupId);
         this.changeGroupIdOf_MI_CMS_MAP_GROUP_PLAYLIST(defaultContentGroupId, user.getUser_id(), rootGroupId);
         this.changeCreatorIdOf_MI_CMS_INFO_PLAYLIST_VERSION(user.getUser_id(), admin.getUser_id(), rootGroupId);
         this.changeCreatorIdOf_MI_CMS_INFO_PLAYLIST(user.getUser_id(), admin.getUser_id(), rootGroupId);
      } catch (Exception var8) {
         this.logger.error("", var8);
      }

   }

   private void transferPlaylistToAdminSub(User user, long rootGroupId, String[] deleteUsers) {
      try {
         UserInfo userInfo = UserInfoImpl.getInstance();
         User admin = userInfo.getAdminOfOrganizationNotInDeleteUsers(rootGroupId, deleteUsers);
         long defaultContentGroupId = this.getDefaultPlaylistGroupId(admin.getUser_id(), rootGroupId);
         this.changeGroupIdOf_MI_CMS_MAP_GROUP_PLAYLIST(defaultContentGroupId, user.getUser_id(), rootGroupId);
         this.changeCreatorIdOf_MI_CMS_INFO_PLAYLIST_VERSION(user.getUser_id(), admin.getUser_id(), rootGroupId);
         this.changeCreatorIdOf_MI_CMS_INFO_PLAYLIST(user.getUser_id(), admin.getUser_id(), rootGroupId);
      } catch (Exception var9) {
         var9.printStackTrace();
      }

   }

   private void changeGroupIdOf_MI_CMS_MAP_GROUP_PLAYLIST(Long groupId, String fromUserId, Long organizationId) throws SQLException {
      dao.changeGroupIdOf_MI_CMS_MAP_GROUP_PLAYLIST(groupId, fromUserId, organizationId);
   }

   private void changeCreatorIdOf_MI_CMS_INFO_PLAYLIST(String fromUserId, String toUserId, Long organizationId) throws SQLException {
      dao.changeCreatorIdOf_MI_CMS_INFO_PLAYLIST(fromUserId, toUserId, organizationId);
   }

   private void changeCreatorIdOf_MI_CMS_INFO_PLAYLIST_VERSION(String fromUserId, String toUserId, Long organizationId) throws SQLException {
      dao.changeCreatorIdOf_MI_CMS_INFO_PLAYLIST_VERSION(fromUserId, toUserId, organizationId);
   }

   public void deleteGroupByCreatorId(String creatorId) throws SQLException {
      dao.deleteGroupByCreatorId(creatorId);
   }

   public long getCntAllPlaylists(String creatorId, Long organizationId) throws SQLException {
      return dao.getCntAllPlaylists(creatorId, organizationId);
   }

   public int deleteContentFromPlaylist(String playlistID, String contentId) throws Exception {
      return dao.deleteContentFromPlaylist(playlistID, contentId);
   }

   public List getUpperPlaylist(String playlistId) throws Exception {
      return dao.getUpperPlaylist(playlistId);
   }

   public int getContentCountInTagPlaylist(String playlistId) throws Exception {
      return dao.getContentCountInTagPlaylist(playlistId);
   }

   public List getPlaylistInfoByContentId(String contentId) throws Exception {
      return dao.getPlaylistInfoByContentId(contentId);
   }

   public int setTotalSize(String playlistId, long versionId, long totalSize) throws Exception {
      return dao.setTotalSize(playlistId, versionId, totalSize);
   }

   public int setContentDurationByVersionOfPlaylist(String playlistId, long versionId, String contentId, long contentDuration) throws Exception {
      return dao.setContentDurationByVersionOfPlaylist(playlistId, versionId, contentId, contentDuration);
   }

   public int setContentDurationMilliByVersionOfPlaylist(String playlistId, long versionId, String contentId, String contentDurationMilli) throws Exception {
      return dao.setContentDurationMilliByVersionOfPlaylist(playlistId, versionId, contentId, contentDurationMilli);
   }

   public List getGroupList(Long organizationId) throws SQLException {
      return dao.getGroupListByOrganizationId(organizationId);
   }

   public List getPlaylistIdListByPlaylistName(String[] playlistNameList) throws SQLException {
      return dao.getPlaylistIdListByPlaylistName(playlistNameList);
   }

   public List getPlaylistIdListByRegex(String regex) throws SQLException {
      return dao.getPlaylistIdListByRegex(regex);
   }

   public List getPlaylistScheduleMapping(List playlistIds) throws SQLException {
      return dao.getPlaylistScheduleMapping(playlistIds);
   }

   public List getPlayListGroupBySearch(String searchText, long organizationId, String userId) throws SQLException {
      return dao.getPlayListGroupBySearch(searchText, organizationId, userId);
   }

   public List getPlayListGroupBySearch(String searchText, long organizationId) throws SQLException {
      return dao.getPlayListGroupBySearch(searchText, organizationId, (String)null);
   }

   public List getParentsGroupList(int pGroupId) throws SQLException {
      return dao.getParentsGroupList(pGroupId);
   }

   public Long getOrganizationIdByGroupId(Long groupId) throws SQLException {
      return dao.getOrganizationIdByGroupId(groupId);
   }

   public List getSubGroupList(Long group_id, boolean recursive, Long organizationId) throws SQLException {
      return dao.getSubGroupList(group_id, recursive, organizationId);
   }

   public int deletePlaylistVersion(String playlistId, String playlistVersionId, String userId) throws SQLException {
      return dao.deletePlaylistVersion(playlistId, playlistVersionId, userId);
   }

   public long getPlaylistMaxVer(String playlistId) throws SQLException {
      return dao.getPlaylistMaxVer(playlistId);
   }

   public int setVersionPlaylistActive(String playlistId, Long versionId) throws SQLException {
      return dao.setVersionPlaylistActive(playlistId, versionId);
   }

   public List getActivePlaylistCountOne(String contentID) {
      return dao.getActivePlaylistCountOne(contentID);
   }

   public Boolean isExistMapPlaylistID(String playlistID, Long playlistVersionId) throws SQLException {
      return dao.isExistMapPlaylistID(playlistID, playlistVersionId);
   }

   public int getPlaylistListCnt(Map map) throws SQLException {
      return dao.getPlaylistListCnt(map);
   }

   public int setPlaylistModifiedDate(String playlistID) throws SQLException, ConfigException {
      return dao.setPlaylistModifiedDate(playlistID);
   }

   public List getPlaylistCountByPlaylistType() throws SQLException {
      List result = dao.getPlaylistCountByPlaylistType();
      String playlistType = null;
      Map map = null;

      for(int i = 0; i < result.size(); ++i) {
         map = (Map)result.get(i);
         playlistType = (String)map.get("playlist_type");
         if ("0".equals(playlistType)) {
            map.put("playlist_type", "PREMIUM");
         } else if ("1".equals(playlistType)) {
            map.put("playlist_type", "AMS");
         } else if ("2".equals(playlistType)) {
            map.put("playlist_type", "VWL");
         } else if ("3".equals(playlistType)) {
            map.put("playlist_type", "SYNCPLAY");
         } else if ("4".equals(playlistType)) {
            map.put("playlist_type", "ADVERTISEMENT");
         } else if ("5".equals(playlistType)) {
            map.put("playlist_type", "TAG");
         } else if ("6".equals(playlistType)) {
            map.put("playlist_type", "LINKED");
         }

         result.set(i, map);
      }

      return result;
   }

   public int getPlaylistGroupTotalCount() throws SQLException {
      return dao.getPlaylistGroupTotalCount();
   }
}
