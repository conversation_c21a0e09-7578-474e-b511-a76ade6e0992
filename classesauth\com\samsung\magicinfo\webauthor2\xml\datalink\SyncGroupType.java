package com.samsung.magicinfo.webauthor2.xml.datalink;

import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SyncGroupType", propOrder = {"splitGroups"})
public class SyncGroupType {
  @XmlElement(name = "SplitGroup", required = true)
  protected List<SplitGroupType> splitGroups;
  
  @XmlAttribute(name = "id")
  protected Integer id;
  
  public List<SplitGroupType> getSplitGroups() {
    return this.splitGroups;
  }
  
  public void setSplitGroups(List<SplitGroupType> splitGroups) {
    this.splitGroups = splitGroups;
  }
  
  public Integer getId() {
    return this.id;
  }
  
  public void setId(Integer value) {
    this.id = value;
  }
}
