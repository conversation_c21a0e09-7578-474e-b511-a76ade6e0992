package com.samsung.magicinfo.openapi.scenario.service;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.openapi.scenario.info.ActionItem;
import com.samsung.magicinfo.openapi.scenario.info.ActionParameter;
import com.samsung.magicinfo.openapi.scenario.info.Field;
import com.samsung.magicinfo.openapi.scenario.info.ScenarioGroup;
import com.samsung.magicinfo.openapi.scenario.info.ScenarioItem;
import com.samsung.magicinfo.openapi.scenario.model.ScenarioModelManager;
import java.io.IOException;
import java.io.Serializable;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.logging.log4j.Logger;
import org.codehaus.jackson.map.ObjectMapper;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;

@Controller
public class ScenarioServiceController {
   protected WebApplicationContext applicationContext;
   protected Logger logger = LoggingManagerV2.getLogger(ScenarioServiceController.class);
   private static final String USER_ID = "id";
   private static final String PASSWORD = "pw";
   private static final String REPORT_ERROR = "Unable to run scenario. ";
   private static final String DEFAULT_STATUS = "OK";
   private static final String URL = "url";

   public ScenarioServiceController() {
      super();
   }

   @RequestMapping(
      value = {"/run"},
      method = {RequestMethod.GET}
   )
   @ResponseBody
   public String service(HttpServletRequest request, HttpServletResponse response) throws JSONException {
      String status = "OK";
      JSONObject resObject = new JSONObject();
      Map resList = null;
      if (request != null && request.getParameterMap().containsKey("id") && request.getParameterMap().containsKey("pw") && request.getParameterMap().containsKey("url")) {
         ScenarioModelManager mgr = this.getManager(request.getSession());
         if (mgr != null) {
            try {
               String user = request.getParameter("id");
               String password = request.getParameter("pw");
               URL targetUrl = this.getTargetURL(request);
               URL currentUrl = this.getCurrentURL(request);
               if (!user.isEmpty() && !password.isEmpty() && targetUrl != null && currentUrl != null) {
                  if (mgr.importAllScenarios(targetUrl) == null) {
                     status = "Could not import scenarios from specified location";
                  } else {
                     resList = mgr.executeAllScenarios(currentUrl.toString(), user, password);
                     this.logger.info("Scenarios from " + targetUrl + " have been executed");
                     Iterator var11 = resList.entrySet().iterator();

                     while(var11.hasNext()) {
                        Entry sceGroupEntry = (Entry)var11.next();
                        ScenarioGroup sceGroup = (ScenarioGroup)sceGroupEntry.getKey();
                        Map map = (Map)sceGroupEntry.getValue();
                        JSONObject siMapObj = new JSONObject();
                        siMapObj.put("group", sceGroup.getName());
                        Iterator var16 = map.entrySet().iterator();

                        while(var16.hasNext()) {
                           Entry siEntry = (Entry)var16.next();
                           ScenarioItem si = (ScenarioItem)siEntry.getKey();
                           JSONObject scenarioObject = new JSONObject();
                           scenarioObject.put("scenario", si.getName());
                           ObjectMapper mapper = new ObjectMapper();
                           String expected = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(si.getExpectedResult());
                           expected = this.treatQuotes(expected);
                           scenarioObject.put("expected", expected);
                           List aciResList = (List)siEntry.getValue();
                           String actual = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(aciResList);
                           scenarioObject.put("actual", actual);
                           scenarioObject.put("actions", this.treatAIListQuotes(si.getActionItem()));
                           siMapObj.append("scenarios", scenarioObject);
                        }

                        resObject.append("groups", siMapObj);
                     }
                  }
               } else {
                  status = "Unable to run scenario. Missing required parameter. ";
                  status = status + " Check for valid target URL format: <protocol>://<host>:<port>/<path>";
                  this.logger.error(status);
               }
            } catch (MalformedURLException var24) {
               status = "Unable to run scenario. Target url has incorrect fomat. ";
               this.logger.error(status);
            } catch (JSONException var25) {
               status = "Unable to run scenario. Reporting JSON object creation error. ";
               this.logger.error(status);
            } catch (IOException var26) {
               status = "Unable to run scenario. Read JSON value error. ";
               this.logger.error(status);
            }
         } else {
            status = "Unable to run scenario. Scenario manager could not be initialized. ";
            this.logger.error(status);
         }
      } else {
         status = "Unable to run scenario. Missing required parameter. ";
         this.logger.error(status);
      }

      resObject.put("result", status);
      return resObject.toString();
   }

   private ScenarioModelManager getManager(HttpSession session) {
      ScenarioModelManager mgr = null;
      if (session != null && session.getServletContext() != null) {
         WebApplicationContext applicationContext = WebApplicationContextUtils.getWebApplicationContext(session.getServletContext());
         if (applicationContext != null) {
            mgr = (ScenarioModelManager)applicationContext.getBean("scenarioModelManager");
         }
      }

      return mgr;
   }

   private URL getTargetURL(HttpServletRequest request) throws MalformedURLException {
      String targetURLStr = request.getParameter("url");
      URL targetUrl = new URL(targetURLStr);
      return targetUrl;
   }

   private URL getCurrentURL(HttpServletRequest request) throws MalformedURLException {
      String currentHostUrlStr = request.getRequestURL().toString();
      String contextPath = request.getContextPath();
      currentHostUrlStr = currentHostUrlStr.replaceFirst(contextPath + ".*", contextPath);
      URL currentUrl = new URL(currentHostUrlStr);
      if (currentUrl == null || currentUrl.getProtocol().isEmpty() || currentUrl.getHost().isEmpty() || currentUrl.getPath().isEmpty() || currentUrl.getProtocol().isEmpty()) {
         currentUrl = null;
      }

      return currentUrl;
   }

   private String treatQuotes(String target) {
      if (target.length() > 1) {
         if (target.startsWith("\"")) {
            target = target.substring(1);
         }

         if (target.endsWith("\"")) {
            target = target.substring(0, target.length() - 1);
         }

         String regexp = "[^\\\\]\"";
         Pattern p = Pattern.compile(regexp);

         String found;
         for(Matcher m = p.matcher(target); m.find(); target = target.replace(found, found.charAt(0) + "\\" + found.charAt(1))) {
            found = m.group();
         }
      }

      if (target.length() == 1 && target.equals("\"")) {
         target = "";
      }

      return target;
   }

   private List treatAIListQuotes(List aiList) {
      List newAIList = new LinkedList();
      Iterator var3 = aiList.iterator();

      while(var3.hasNext()) {
         ActionItem ai = (ActionItem)var3.next();
         ActionItem aiNew = new ActionItem();
         Iterator var6 = ai.getActionParameter().iterator();

         while(var6.hasNext()) {
            ActionParameter ap = (ActionParameter)var6.next();
            ActionParameter apNew = new ActionParameter();
            Iterator var9 = ap.getContent().iterator();

            while(var9.hasNext()) {
               Serializable content = (Serializable)var9.next();
               if (content instanceof Field) {
                  Field f = (Field)content;
                  if (f.getContent().size() == 1) {
                     Serializable cont = (Serializable)f.getContent().get(0);
                     if (cont instanceof String) {
                        Serializable cont = ((String)cont).replace("\"", "\\\"");
                        f.getContent().clear();
                        f.getContent().add(cont);
                     }
                  }

                  apNew.getContent().add(f);
               }

               if (content instanceof String) {
                  String contStr = (String)content;
                  apNew.getContent().add(contStr.replace("\"", "\\\""));
               }
            }

            apNew.setAlias(ap.getAlias());
            apNew.setAliasType(ap.getAliasType());
            apNew.setAliasValue(ap.getAliasValue());
            apNew.setName(ap.getName());
            apNew.setType(ap.getType());
            aiNew.getActionParameter().add(apNew);
         }

         aiNew.setMethod(ai.getMethod());
         aiNew.setService(ai.getService());
         newAIList.add(aiNew);
      }

      return newAIList;
   }
}
