package com.samsung.magicinfo.webauthor2.repository.model.device;

import java.io.Serializable;
import java.util.List;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;

public class DeviceListResultListData implements Serializable {
  @XmlElement
  private Integer totalCount;
  
  @XmlElementWrapper(name = "resultList")
  @XmlElement(name = "Device")
  private List<DeviceData> resultList;
  
  public Integer getTotalCount() {
    return this.totalCount;
  }
  
  public List<DeviceData> getResultList() {
    return this.resultList;
  }
}
