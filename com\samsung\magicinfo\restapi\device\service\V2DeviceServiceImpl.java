package com.samsung.magicinfo.restapi.device.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.exception.CMSExceptionCode;
import com.samsung.common.export.PdfBuilder;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.AES256Cipher;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.DBCacheUtils;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.ExternalSystemUtils;
import com.samsung.common.utils.RequestUtils;
import com.samsung.common.utils.RoleUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.SequenceDB;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.WakeOnLan;
import com.samsung.common.utils.page.ListManager;
import com.samsung.common.utils.page.PageManager;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.common.DAOFactory;
import com.samsung.magicinfo.framework.common.FileManagerImpl;
import com.samsung.magicinfo.framework.content.dao.PlaylistDao;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.device.alarmRule.entity.AlarmRuleSchedulesToExpireByDevice;
import com.samsung.magicinfo.framework.device.constants.DeviceConstants;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceDao;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceGroupDao;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceControl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceLogCollectEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceLoopOutEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSecurityConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceServiceConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSoftwareConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemInfoConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemSetupConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTag;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTimeConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.LedCabinet;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceGeneralConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceGeneralConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSboxConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSboxConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSecurityConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSecurityConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceTimeConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceTimeConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.LedCabinetConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.LedCabinetConfManagerImpl;
import com.samsung.magicinfo.framework.device.job.entity.JobEntity;
import com.samsung.magicinfo.framework.device.job.manager.JobManager;
import com.samsung.magicinfo.framework.device.job.manager.JobManagerImpl;
import com.samsung.magicinfo.framework.device.log.entity.ServerLogEntity;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManager;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManagerImpl;
import com.samsung.magicinfo.framework.device.preconfig.entity.DevicePreconfig;
import com.samsung.magicinfo.framework.device.preconfig.manager.DevicePreconfigInfo;
import com.samsung.magicinfo.framework.device.preconfig.manager.DevicePreconfigInfoImpl;
import com.samsung.magicinfo.framework.device.ruleProcessing.Manager.AlarmManager;
import com.samsung.magicinfo.framework.device.ruleProcessing.Manager.AlarmManagerImpl;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManager;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManagerImpl;
import com.samsung.magicinfo.framework.device.software.entity.Software;
import com.samsung.magicinfo.framework.device.software.manager.SoftwareManager;
import com.samsung.magicinfo.framework.device.software.manager.SoftwareManagerImpl;
import com.samsung.magicinfo.framework.device.warningRule.entity.DeviceThreshold;
import com.samsung.magicinfo.framework.device.warningRule.entity.DeviceWarningRule;
import com.samsung.magicinfo.framework.device.warningRule.manager.DeviceWarningRuleInfo;
import com.samsung.magicinfo.framework.device.warningRule.manager.DeviceWarningRuleInfoImpl;
import com.samsung.magicinfo.framework.monitoring.entity.ClientFaultEntity;
import com.samsung.magicinfo.framework.monitoring.entity.ContentList;
import com.samsung.magicinfo.framework.monitoring.entity.CurrentPlayingEntity;
import com.samsung.magicinfo.framework.monitoring.entity.ScheduleInfoEntity;
import com.samsung.magicinfo.framework.monitoring.entity.ServerTimeDescCompare;
import com.samsung.magicinfo.framework.monitoring.manager.DownloadStatusInfo;
import com.samsung.magicinfo.framework.monitoring.manager.DownloadStatusInfoImpl;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerInfo;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerInfoImpl;
import com.samsung.magicinfo.framework.playlist.manager.common.PlaylistInterface;
import com.samsung.magicinfo.framework.scheduler.dao.ScheduleInfoDAO;
import com.samsung.magicinfo.framework.scheduler.entity.DetailDownloadContentEntity;
import com.samsung.magicinfo.framework.scheduler.entity.DownloadContentEntity;
import com.samsung.magicinfo.framework.scheduler.entity.EventEntity;
import com.samsung.magicinfo.framework.scheduler.entity.EventScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.MessageEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ScheduleAdminEntity;
import com.samsung.magicinfo.framework.scheduler.manager.EventScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventScheduleInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfo;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ProgramGroupInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ProgramGroupInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleUtility;
import com.samsung.magicinfo.framework.setup.dao.RmServerDao;
import com.samsung.magicinfo.framework.setup.dao.SlmLicenseDao;
import com.samsung.magicinfo.framework.setup.entity.RmServerEntity;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManager;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManagerImpl;
import com.samsung.magicinfo.framework.setup.manager.TagInfo;
import com.samsung.magicinfo.framework.setup.manager.TagInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.openapi.auth.TokenRegistry;
import com.samsung.magicinfo.openapi.impl.ClassUtil;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.file.ChecksumCRC32;
import com.samsung.magicinfo.protocol.file.FileLoaderServlet;
import com.samsung.magicinfo.protocol.file.FileUploadCommonHelper;
import com.samsung.magicinfo.protocol.interfaces.WSCall;
import com.samsung.magicinfo.protocol.util.TimeUtil;
import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonGroupData;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.common.model.V2CommonStatusResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonUpdateResult;
import com.samsung.magicinfo.restapi.device.model.V2DeletedDevice;
import com.samsung.magicinfo.restapi.device.model.V2DeviceCabinetScanInfo;
import com.samsung.magicinfo.restapi.device.model.V2DeviceCabinetSendCmdWrapper;
import com.samsung.magicinfo.restapi.device.model.V2DeviceConversion;
import com.samsung.magicinfo.restapi.device.model.V2DeviceCustomConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceDataResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceDeleteResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceDisplayConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceEntityResources;
import com.samsung.magicinfo.restapi.device.model.V2DeviceEventEntity;
import com.samsung.magicinfo.restapi.device.model.V2DeviceExpireResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceExternalPowerResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceFilter;
import com.samsung.magicinfo.restapi.device.model.V2DeviceFlipConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceFlipResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceGeneralConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceLedCabinetResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceLedCabinetWithGroupResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceLicenseConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceListResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceLogCollectResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceLogFileResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceLogFileStatusResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceLogProcess;
import com.samsung.magicinfo.restapi.device.model.V2DeviceNoticeListResource;
import com.samsung.magicinfo.restapi.device.model.V2DevicePlayContentSchConf;
import com.samsung.magicinfo.restapi.device.model.V2DevicePreconfig;
import com.samsung.magicinfo.restapi.device.model.V2DevicePreconfigData;
import com.samsung.magicinfo.restapi.device.model.V2DevicePreconfigResource;
import com.samsung.magicinfo.restapi.device.model.V2DevicePresetConfig;
import com.samsung.magicinfo.restapi.device.model.V2DevicePresetCopyParam;
import com.samsung.magicinfo.restapi.device.model.V2DevicePresetDeleteResource;
import com.samsung.magicinfo.restapi.device.model.V2DevicePresetDeployStatusResource;
import com.samsung.magicinfo.restapi.device.model.V2DevicePresetFilter;
import com.samsung.magicinfo.restapi.device.model.V2DevicePresetResource;
import com.samsung.magicinfo.restapi.device.model.V2DevicePresetResultResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSaveChannelConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSecurityConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceServerStatus;
import com.samsung.magicinfo.restapi.device.model.V2DeviceServiceConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceServiceConfig;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSetSboxLayoutResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSoftwareConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceStatusResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSystemSetupConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSystemUsageResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceTag;
import com.samsung.magicinfo.restapi.device.model.V2DeviceTagAssignment;
import com.samsung.magicinfo.restapi.device.model.V2DeviceTagResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceTimeConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceTypeResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceVncConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceVncViewerJnlpStringBuilder;
import com.samsung.magicinfo.restapi.device.model.V2DeviceWarningRule;
import com.samsung.magicinfo.restapi.device.model.V2DownloadContentResource;
import com.samsung.magicinfo.restapi.device.model.V2FlipResource;
import com.samsung.magicinfo.restapi.device.model.V2RemoteControlIds;
import com.samsung.magicinfo.restapi.device.model.V2ServerConfig;
import com.samsung.magicinfo.restapi.device.model.V2UpcommingExpirySchedule;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.playlist.model.V2PlaylistResourceResponse;
import com.samsung.magicinfo.restapi.utils.ConvertUtil;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import com.samsung.magicinfo.rms.model.DeviceFilter;
import com.samsung.magicinfo.rms.model.DeviceLedCabinetResource;
import com.samsung.magicinfo.rms.model.DeviceSystemSetupConfResource;
import com.samsung.magicinfo.rms.model.GeneralInfoResource;
import com.samsung.magicinfo.rms.util.DeviceModelConverter;
import com.samsung.magicinfo.service.statistics.DeviceStatisticsDownloadService;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.io.StringReader;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.Socket;
import java.net.SocketException;
import java.net.URL;
import java.net.URLEncoder;
import java.net.UnknownHostException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.KeyManager;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLEngine;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509ExtendedTrustManager;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import jodd.util.StringUtil;
import net.lingala.zip4j.core.ZipFile;
import net.lingala.zip4j.model.ZipParameters;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;
import org.apache.xml.security.utils.Base64;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import org.xml.sax.SAXParseException;

@Service("V2DeviceService")
@Transactional
public class V2DeviceServiceImpl implements V2DeviceService {
   protected Logger logger = LoggingManagerV2.getLogger(V2DeviceServiceImpl.class);
   private DeviceStatisticsDownloadService downloadService = null;
   private static final int BUF_SIZE = 1048576;
   private static final int EOF = -1;
   final int PUBLISH_DETAIL_MODE = 1;
   final int PUBLISH_TOTAL_MODE = 2;
   final String TYPE_PLATFORM = "platform";
   final String TYPE_W_PLAYER = "wplayer";
   final String TYPE_THIRD_APPLICATION = "third_application";
   final String SCRIPT_W_PLAYER = "wplayer";
   final String SCRIPT_W_PLAYER_API_NOPSWD = "wplayer_api_nopswd";
   final String SCRIPT_THIRD_APPLICATION_NOPSWD = "third_application_nopswd";
   public static long maxLogFileSize = CommonUtils.getConfigLongNumber("device.log_collect.max_size", 10485760L, 419430400L);
   DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
   DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
   DeviceGroupDao devGroupDao = new DeviceGroupDao();
   DeviceLogManager logMgr = DeviceLogManagerImpl.getInstance();
   MonitoringManager monMgr = MonitoringManagerImpl.getInstance();
   DeviceDao dao = new DeviceDao((SqlSession)null);
   AlarmManager alarmManager = AlarmManagerImpl.getInstance();
   DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
   JobManager jobMgr = JobManagerImpl.getInstance();
   MonitoringManagerInfo deviceMonitoringDao = MonitoringManagerInfoImpl.getInstance("PREMIUM");
   DeviceTimeConfManager deviceConf = DeviceTimeConfManagerImpl.getInstance("PREMIUM");
   DeviceSystemSetupConfManager deviceSetupConf = DeviceSystemSetupConfManagerImpl.getInstance("PREMIUM");
   MessageInfo msgInfo = MessageInfoImpl.getInstance();
   ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
   ContentInfo contentDao = ContentInfoImpl.getInstance();
   ScheduleInfoDAO scheduleDao = new ScheduleInfoDAO();
   private DevicePreconfigInfo preconfigInfo = DevicePreconfigInfoImpl.getInstance();
   final String ALL_MDC = "ALL_MDC";
   final String MODEL_KIND_NEW = "NEW";
   final String REQUEST_ID = "requestId";

   public V2DeviceServiceImpl() {
      super();
   }

   public void setDownloadService(DeviceStatisticsDownloadService downloadService) {
      this.downloadService = downloadService;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public V2PageResource getAllDeviceList(V2DeviceFilter filter, HttpServletRequest request, HttpServletResponse response) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      V2PageResource result = new V2PageResource();
      String productType = StrUtils.nvl(filter.getProductType()).equals("") ? "PREMIUM" : filter.getProductType();
      DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
      String groupId = StrUtils.nvl(filter.getGroupId());
      String expirationDate;
      if (!groupId.equals("")) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, Long.valueOf(groupId));
      } else {
         UserInfo userInfo = UserInfoImpl.getInstance();
         long mngOrgId = userContainer.getUser().getRoot_group_id();
         if (userContainer.getUser().getIs_mu().equalsIgnoreCase("Y")) {
            mngOrgId = userInfo.getCurMngOrgId(userContainer.getUser().getUser_id());
         }

         expirationDate = userInfo.getOrganGroupName(mngOrgId);
         groupId = String.valueOf(deviceGroupDao.getOrganGroupIdByName(expirationDate));
      }

      String connectionStatus = StrUtils.nvl(filter.getConnectionStatus()).equals("") ? "device_status_view_all" : filter.getConnectionStatus();
      if (StrUtils.nvl(filter.getSortColumn()).equals("")) {
         filter.setSortColumn("device_id");
      }

      String disconnectPeriod = null;
      int lastindex = connectionStatus.lastIndexOf("_");
      if (connectionStatus.substring(0, lastindex).equals("device_status_view_disconnection")) {
         disconnectPeriod = connectionStatus.substring(lastindex + 1);
         connectionStatus = connectionStatus.substring(0, lastindex);
      }

      expirationDate = "device_status_view_all";
      String customInputVal = "0";
      String groupIds = "";
      if (filter.getGroupIds() != null) {
         groupIds = this.convertString(filter.getGroupIds());
      }

      String tagIds = null;
      if (filter.getTagIds() != null) {
         tagIds = this.convertString(filter.getTagIds());
      }

      String alarmTypes = "";
      if (filter.getAlarmTypes() != null) {
         alarmTypes = this.convertString(filter.getAlarmTypes());
      }

      String functionTypes = "";
      if (filter.getFunctionTypes() != null) {
         functionTypes = this.convertString(filter.getFunctionTypes());
      }

      String searchText = StrUtils.nvl(filter.getSearchText()).equals("") ? "" : filter.getSearchText();
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      String inputSources = "";
      if (filter.getInputSources() != null) {
         inputSources = this.convertString(filter.getInputSources());
      }

      boolean isRoot = false;
      Long rootGroupId = userContainer.getUser().getRoot_group_id();
      if (rootGroupId.equals(0L)) {
         isRoot = true;
      }

      String searchId = StrUtils.nvl(filter.getSearchId()).equals("") ? "-1" : filter.getSearchId();
      String deviceType = "";
      if (filter.getDeviceType() != null) {
         deviceType = this.convertString(filter.getDeviceType());
      }

      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      ListManager listMgr = new ListManager(deviceDao, "commonlist");
      SelectCondition condition = new SelectCondition();
      condition.setSort_name(filter.getSortColumn());
      condition.setOrder_dir(filter.getSortOrder());
      condition.setGroup_id(Long.valueOf(groupId));
      condition.setSrc_name(searchText);
      condition.setIsRoot(isRoot);
      condition.setStatus_view_mode(connectionStatus);
      if (disconnectPeriod != null) {
         condition.setDisconn_period(disconnectPeriod);
      }

      condition.setExpiration_date(expirationDate);
      condition.setCustom_input_val(customInputVal);
      condition.setFilter_group_ids(groupIds);
      condition.setRole_name(userContainer.getUser().getRole_name());
      condition.setUser_id(userContainer.getUser().getUser_id());
      condition.setTagFilter(tagIds);
      condition.setSourceFilter(inputSources);
      condition.setFirmware_indicators(filter.getFirmwareIndicators());
      if (StringUtils.isNotBlank(alarmTypes)) {
         condition.setAlarmFiltersByString(alarmTypes);
      }

      if (StringUtils.isNotBlank(functionTypes)) {
         condition.setFunctionFiltersByString(functionTypes);
      }

      if (StringUtils.isNotBlank(searchText)) {
         condition.setCommonSearchKeyword(searchText);
      }

      if (deviceType != null && !deviceType.equals("")) {
         condition.setDevice_type(deviceType);
      }

      ArrayList sourceList;
      String[] deviceList;
      if (StringUtils.isNotBlank(condition.getSourceFilter())) {
         sourceList = new ArrayList();
         deviceList = condition.getSourceFilter().split(",");
         String[] var29 = deviceList;
         int var30 = deviceList.length;

         for(int var31 = 0; var31 < var30; ++var31) {
            String tag = var29[var31];
            sourceList.add(Long.parseLong(tag));
         }

         condition.setSourceFilterList(sourceList);
      }

      sourceList = null;
      deviceList = null;
      listMgr.addSearchInfo("condition", condition);
      if (!searchId.equals("-1")) {
         listMgr.addSearchInfo("search_id", searchId);
      }

      listMgr.setLstSize(Integer.valueOf(filter.getPageSize()));
      listMgr.setSection("getApprovedDeviceListWithFilter");
      List deviceList = listMgr.V2dbexecute(filter.getStartIndex(), filter.getPageSize());
      PageManager pageMgr = listMgr.getPageManager();
      response.setContentType("application/json;charset=UTF-8");
      List pollingDeviceIds = filter.getPollingDeviceIds();
      if (pollingDeviceIds != null && pollingDeviceIds.size() > 0) {
         deviceList = new ArrayList();
         Iterator var53 = pollingDeviceIds.iterator();

         while(var53.hasNext()) {
            String pollingDeviceId = (String)var53.next();
            DeviceGeneralConf deviceGeneral = deviceDao.getDeviceGeneralConf(pollingDeviceId, true);
            ((List)deviceList).add(deviceGeneral);
         }
      }

      result.setRecordsReturned(((List)deviceList).size());
      result.setRecordsTotal(pageMgr.getTotalRowCount());
      result.setRecordsFiltered(pageMgr.getTotalRowCount());
      result.setPageSize(pageMgr.getPageSize());
      if (filter.getSortColumn() != null && !filter.getSortColumn().equals("")) {
         result.setSortColumn(filter.getSortColumn());
         result.setSortOrder(filter.getSortOrder());
      }

      DeviceInfo dInfo = DeviceInfoImpl.getInstance();
      List list = new ArrayList();
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      Map cacheGroups = deviceGroupInfo.getCacheDeviceGroup();
      int i = 0;

      for(boolean var35 = false; i < ((List)deviceList).size(); ++i) {
         new V2DeviceListResource();
         DeviceGeneralConf device = (DeviceGeneralConf)((List)deviceList).get(i);
         V2DeviceListResource resource = this.writeData(request, productType, device);
         List groups = deviceGroupInfo.getParentGroupNamePathByGroupId(cacheGroups, device.getGroup_id());
         List groupPathNames = new ArrayList();
         List groupPathIds = new ArrayList();
         Iterator var41 = groups.iterator();

         while(var41.hasNext()) {
            DeviceGroup deviceGroup = (DeviceGroup)var41.next();
            groupPathNames.add(deviceGroup.getGroup_name());
            groupPathIds.add(String.valueOf(deviceGroup.getGroup_id()));
         }

         resource.setGroupName(String.join(">", groupPathNames));
         resource.setGroupPathId(String.join("|", groupPathIds));
         if (StringUtils.isNotEmpty(resource.getPreconfig())) {
            String[] temp = resource.getPreconfig().split(";");
            String preconfigVersion = temp[0];
            if (preconfigVersion != null && preconfigVersion.length() > 0 && !"-".equals(preconfigVersion)) {
               DevicePreconfig preconfig = this.preconfigInfo.getPreconfigByDeviceId(resource.getDeviceId());
               Integer preconfigSuccess = 0;
               Integer preconfigFailed = 0;

               try {
                  preconfigSuccess = Integer.valueOf(temp[1]);
                  preconfigFailed = Integer.valueOf(temp[2]);
               } catch (Exception var47) {
                  preconfigSuccess = 0;
                  preconfigFailed = 0;
               }

               if (preconfig == null) {
                  resource.setPreconfigVersion("-");
               } else {
                  resource.setPreconfigVersion(preconfigVersion);
               }

               resource.setPreconfigSuccess(preconfigSuccess);
               resource.setPreconfigFailed(preconfigFailed);
            } else {
               resource.setPreconfigVersion("-");
            }
         }

         list.add(resource);
         if (device.getHas_child() != null && device.getHas_child()) {
            for(int j = 0; (long)j < device.getChild_cnt(); ++j) {
               DeviceGeneralConf childDevice = dInfo.getDeviceGeneralConf(device.getDevice_id() + "_" + (j + 1));
               if (childDevice != null && childDevice.getDevice_id() != null) {
                  resource = this.writeData(request, productType, childDevice);
                  list.add(resource);
               }
            }
         }
      }

      result = V2PageResource.createPageResource(list, pageMgr);
      result.setStartIndex(filter.getStartIndex());
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public V2CommonUpdateResult networkMode(V2CommonIds deviceIds) throws Exception {
      boolean checkFlag = false;
      Iterator var3 = deviceIds.getIds().iterator();

      String userId;
      while(var3.hasNext()) {
         userId = (String)var3.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, userId);
         } catch (Exception var17) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      UserContainer userContainer = SecurityUtils.getUserContainer();
      userId = userContainer.getUser().getUser_id();
      List successList = new ArrayList();
      List failList = new ArrayList();
      V2CommonUpdateResult resource = new V2CommonUpdateResult();
      JobManager jobMgr = JobManagerImpl.getInstance();
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Iterator var10 = deviceIds.getIds().iterator();

      while(var10.hasNext()) {
         String deviceId = (String)var10.next();

         try {
            int jobId = SequenceDB.getNextValue("MI_DMS_INFO_JOB");
            JobEntity job = new JobEntity();
            job.setJob_id((long)jobId);
            job.setJob_name("service_" + jobId);
            job.setRepeat_type("immediately");
            job.setUser_id(userId);
            job.setJob_type("service");
            job.setTarget("System");
            job.setIs_canceled(false);
            String[] deviceArr = new String[]{deviceId};
            if (jobMgr.addPremiumJob(job, deviceArr)) {
               Device device = deviceDao.getDevice(deviceId);
               if (device != null) {
                  jobMgr.deployNetworkModeJobSchedule(String.valueOf(jobId), device);
                  successList.add(deviceId);
               } else {
                  failList.add(deviceId);
               }
            } else {
               failList.add(deviceId);
            }
         } catch (Exception var16) {
            failList.add(deviceId);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceSystemUsageResource deviceUsage(String device_id, HttpServletRequest request, HttpServletResponse response) throws Exception {
      String deviceId = StrUtils.nvl(device_id);
      DeviceSystemInfoConf deviceSystemInfoConf = null;
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      String sId = request.getSession().getId();

      try {
         confMgr.reqGetSystemInfoFromDevice(deviceId, sId);

         for(int i = 0; i < 30; ++i) {
            deviceSystemInfoConf = confMgr.getSystemInfoResultSet(deviceId, sId, "GET_DEVICE_SYSTEM_INFO_CONF");
            if (deviceSystemInfoConf != null) {
               break;
            }

            Thread.sleep(1000L);
         }
      } catch (Exception var13) {
         this.logger.error("Failed to request system information from device.", var13);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_REQUEST_FROM_DEVICE, new String[]{"system"});
      }

      V2DeviceSystemUsageResource resource = new V2DeviceSystemUsageResource();
      if (deviceSystemInfoConf == null) {
         deviceSystemInfoConf = new DeviceSystemInfoConf();
         deviceSystemInfoConf.setCpu_usage(0L);
         deviceSystemInfoConf.setRam_usage(0L);
         deviceSystemInfoConf.setNetwork_usage(0L);
         SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
         Calendar cal = Calendar.getInstance();
         String today = formatter.format(cal.getTime());
         Timestamp ts = Timestamp.valueOf(today);
         deviceSystemInfoConf.setSystem_time(ts);
         this.logger.error("info is null. set default!!!");
      }

      response.setContentType("application/json;charset=UTF-8");
      resource.setStatus("success");
      resource.setCpu(deviceSystemInfoConf.getCpu_usage());
      resource.setMem(deviceSystemInfoConf.getRam_usage());
      resource.setNetwork(deviceSystemInfoConf.getNetwork_usage());
      resource.setTime((new SimpleDateFormat("hh:mm:ss")).format(deviceSystemInfoConf.getSystem_time()));
      return resource;
   }

   private void updateDeviceTag(String deviceId, List tagIdList, List tagConditionList) throws SQLException {
      DeviceSystemSetupConfManager systemSetupDao = DeviceSystemSetupConfManagerImpl.getInstance();
      int i;
      if (tagConditionList != null) {
         systemSetupDao.deleteDeviceVarTagAll(deviceId);
         if (tagIdList.size() > 0 && !((String)tagIdList.get(0)).equals("0")) {
            for(i = 0; i < tagIdList.size(); ++i) {
               if (tagConditionList != null && tagConditionList.size() > i) {
                  systemSetupDao.setDeviceTag(deviceId, Long.parseLong((String)tagIdList.get(i)), Long.parseLong((String)tagConditionList.get(i)));
               }
            }
         }
      } else {
         systemSetupDao.deleteDeviceTagAll(deviceId);
         if (tagIdList.size() > 0) {
            for(i = 0; i < tagIdList.size(); ++i) {
               if (!((String)tagIdList.get(0)).equals("0")) {
                  systemSetupDao.setDeviceTag(deviceId, Long.parseLong((String)tagIdList.get(i)));
               }
            }
         }
      }

   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority')")
   public V2CommonIds deploySchedule(String deviceId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      V2CommonIds resource = new V2CommonIds();
      ArrayList tempList = new ArrayList();

      try {
         DeviceInfo devInfo = DeviceInfoImpl.getInstance();
         ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
         MonitoringManager mgr = MonitoringManagerImpl.getInstance();
         String pId = devInfo.getProgramIdByDeviceId(deviceId);
         ProgramEntity pEntity = schInfo.getProgram(pId);
         long version = 0L;
         if (pEntity != null) {
            version = pEntity.getVersion();
         } else {
            version = devInfo.getVersionByProgramId(pId);
         }

         ScheduleInfoEntity schEntity = mgr.getScheduleStatus(deviceId);
         ScheduleUtility.deploySchedule(deviceId, pId, version, schEntity.getScheduleVersion());
      } catch (SQLException var12) {
         this.logger.error(var12.getMessage());
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
      } catch (Exception var13) {
         this.logger.error(var13.getMessage());
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SCHEDULE_DEPLOY);
      }

      tempList.add(deviceId);
      resource.setIds(tempList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public V2DeviceSetSboxLayoutResource setSboxLayout(String deviceId) throws Exception {
      V2DeviceSetSboxLayoutResource resource = new V2DeviceSetSboxLayoutResource();
      Locale locale = SecurityUtils.getLocale();
      deviceId = StrUtils.nvl(deviceId).equals("") ? "" : deviceId;
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      String deviceGroupId = deviceDao.getDeviceGroupIdByDeviceId(deviceId);
      String token = null;
      String loginId = SecurityUtils.getUserContainer().getUser().getUser_id();
      TokenRegistry tr = TokenRegistry.getTokenRegistry();
      token = tr.issueToken(loginId, SecurityUtils.getUserContainer(), "PREMIUM");
      resource.setUserId(loginId);
      resource.setToken(token);
      resource.setLayoutAuthorPath("/" + StrUtils.nvl(CommonConfig.get("layoutauthor.context")) + "/index.jsp");
      resource.setLanguage(String.valueOf(locale));
      resource.setDeviceGroupId(deviceGroupId);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2CommonUpdateResult isConnected(V2CommonIds deviceIds) throws Exception {
      boolean checkFlag = false;
      Iterator var3 = deviceIds.getIds().iterator();

      while(var3.hasNext()) {
         String deviceId = (String)var3.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var9) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      V2CommonUpdateResult resource = new V2CommonUpdateResult();
      List successList = new ArrayList();
      List failList = new ArrayList();
      Iterator var6 = deviceIds.getIds().iterator();

      while(var6.hasNext()) {
         String deviceId = (String)var6.next();
         boolean check = DeviceUtils.isConnected(deviceId);
         if (check) {
            successList.add(deviceId);
         } else {
            failList.add(deviceId);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public void deviceVnc(String deviceId, V2DeviceVncConf body, HttpServletRequest request, HttpServletResponse response) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      String tunnelingIP = "";
      if (CommonConfig.get("repeater.tunnelingIP") != null && !CommonConfig.get("repeater.tunnelingIP").equals("")) {
         tunnelingIP = CommonConfig.get("repeater.tunnelingIP");
      } else {
         tunnelingIP = request.getServerName();
      }

      DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
      Device device = deviceMgr.getDevice(deviceId);
      String vncPassword = SecurityUtils.getVncDefaultPassword();
      if (device != null) {
         vncPassword = device.getVnc_password();
      }

      String jnlpPasswordParam = "";
      if (vncPassword != null && !vncPassword.equals("")) {
         jnlpPasswordParam = "       <param name='PASSWORD' value='" + vncPassword + "'/>\n";
      }

      String jnlpFileName = "javaViewer_" + SecurityUtils.getLoginUserId() + "_" + DateUtils.date2String(SecurityUtils.getLoginUser().getCreate_date(), "yyyyMMddHHmmss") + ".jnlp";
      String realPath = request.getSession().getServletContext().getRealPath("/");
      if (realPath != null && !realPath.endsWith(File.separator)) {
         realPath = realPath + File.separator;
      }

      String topPath = CommonConfig.get("CONTENTS_HOME") + File.separator + "jnlp" + File.separator;
      SecurityUtils.getSafeFile(topPath).mkdirs();
      File jnlpFile = SecurityUtils.getSafeFile(topPath + jnlpFileName);
      jnlpFile.createNewFile();
      String browserHeight = CommonConfig.get("VNC_VIEWER_HEIGHT");
      String browserWidth = CommonConfig.get("VNC_VIEWER_WIDTH");
      if (browserHeight == null) {
         browserHeight = body.getBrowserHeight();
      }

      if (browserWidth == null) {
         browserWidth = body.getBrowserWidth();
      }

      String serverUrl = RequestUtils.getWebUrl(request);
      V2DeviceVncViewerJnlpStringBuilder jnlpBuilder = (new V2DeviceVncViewerJnlpStringBuilder()).addServerUrl(serverUrl).addUrlToken(Base64.encode(SecurityUtils.issueToken().getBytes())).addJnlpFilename(jnlpFileName).addBrowserWidth(browserWidth).addBrowserHeight(browserHeight).addDeviceId(deviceId).addTunnelingIp(tunnelingIP).addJnlpPasswordParam(jnlpPasswordParam);
      FileWriter writer = new FileWriter(jnlpFile);
      PrintWriter fout = new PrintWriter(writer);
      fout.write(jnlpBuilder.build());
      fout.close();
      writer.close();
      this.downloadResponse(topPath, jnlpFileName, request, response);
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public ResponseBody updateCabinetInfo(String deviceId, DeviceLedCabinetResource param) {
      ResponseBody responseBody = new ResponseBody();

      try {
         if (deviceId != null && !deviceId.equals("")) {
            DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
            DeviceDisplayConf deviceDisplayConf = new DeviceDisplayConf();
            DeviceDisplayConfManager displayConfMgr = DeviceDisplayConfManagerImpl.getInstance();
            LedCabinet ledCabinet = new LedCabinet();
            boolean sendSetMo = false;
            if (param.getAbl() != null) {
               ledCabinet.setAbl(param.getAbl());
               sendSetMo = true;
            }

            if (param.getInputSource() != null) {
               ledCabinet.setInput_source(param.getInputSource());
               sendSetMo = true;
            }

            if (param.getGamut() != null) {
               ledCabinet.setGamut(param.getGamut());
               sendSetMo = true;
            }

            if (param.getBacklight() != null) {
               ledCabinet.setBacklight(param.getBacklight());
               sendSetMo = true;
            }

            if (param.getPixelRgbCc() != null) {
               ledCabinet.setPixel_rgb_cc(param.getPixelRgbCc());
               sendSetMo = true;
            }

            if (param.getModuleRgbCc() != null) {
               ledCabinet.setModule_rgb_cc(param.getModuleRgbCc());
               sendSetMo = true;
            }

            if (param.getEdgeCorrection() != null) {
               ledCabinet.setEdge_correction(param.getEdgeCorrection());
               sendSetMo = true;
            }

            DeviceDisplayConf conf;
            if (param.getChildAlarmTemperature() != null) {
               conf = new DeviceDisplayConf();
               conf.setDevice_id(deviceId);
               conf.setChild_alarm_temperature(param.getChildAlarmTemperature());
               displayConfMgr.setDeviceDisplayConf(conf);
            }

            String reuqestId;
            if (deviceDisplayConf.getAuto_brightness() != null && deviceId != null && !deviceId.equals("")) {
               conf = new DeviceDisplayConf();
               reuqestId = UUID.randomUUID().toString();
               conf.setDevice_id(deviceId);
               conf.setAuto_brightness(deviceDisplayConf.getAuto_brightness());
               confManager.reqSetDisplayToDevice(deviceDisplayConf, reuqestId, "ADVANCED_MDC");
            }

            if (sendSetMo) {
               List childIds = new ArrayList();
               reuqestId = UUID.randomUUID().toString();
               if (param.getChildIdList() != null && param.getChildIdList().size() != 0) {
                  ledCabinet.setParent_device_id(deviceId);

                  for(int i = 1; i <= 4; ++i) {
                     childIds.clear();
                     Iterator var12 = param.getChildIdList().iterator();

                     while(var12.hasNext()) {
                        String temp = (String)var12.next();
                        if (temp.split("-")[0].equals(i + "")) {
                           childIds.add(temp);
                        }
                     }

                     if (childIds.size() > 0) {
                        confManager.reqSetCabinetConfToDevice(ledCabinet, childIds, reuqestId, "CABINET_MDC");
                     }
                  }
               } else {
                  ledCabinet.setParent_device_id(deviceId);
                  confManager.reqSetCabinetConfToDevice(ledCabinet, (List)null, reuqestId, "CABINET_MDC");
               }
            }

            responseBody.setStatus("Success");
         } else {
            responseBody.setStatus("Fail");
            responseBody.setErrorMessage("deviceId is null");
         }
      } catch (Exception var15) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var15.getMessage());
      }

      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceResource getDevice(String deviceId, HttpServletRequest request) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      boolean rsm_enabled = false;
      Locale locale = SecurityUtils.getLocale();

      try {
         if ("true".equalsIgnoreCase(CommonConfig.get("rsm.enable"))) {
            rsm_enabled = true;
         }
      } catch (ConfigException var50) {
         this.logger.error("[REST_v2.0][DEVICE SERVICE][getDevice]" + RestExceptionCode.INTERNAL_SERVER_ERROR_CONFIGERROR.getMessage(), var50);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CONFIGERROR);
      }

      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      if (!SecurityUtils.checkReadPermissionWithOrgAndId("Device", deviceId)) {
         this.logger.error("[REST_v2.0][DEVICE SERVICE][getDevice] You do not have read permission. deviceId : " + deviceId);
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_NOT_HAVE, new String[]{"device read"});
      } else {
         V2DeviceResource resource = new V2DeviceResource();
         V2DeviceDataResource datas = new V2DeviceDataResource();

         try {
            DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
            MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
            Device device = deviceDao.getDeviceFromDB(deviceId);
            Device deviceGroupId = deviceDao.getMonitoringViewDevice(deviceId);
            CurrentPlayingEntity playingEntity = motMgr.getPlayingContent(deviceId);
            Map menu = new LinkedHashMap();
            resource.setDeviceSerial(device.getSerial_decimal());
            resource.setDeviceName(device.getDevice_name());
            resource.setDeviceType(device.getDevice_type());
            resource.setDeviceTypeVersion(String.valueOf(device.getDevice_type_version()));
            if (playingEntity != null) {
               resource.setInputSource(String.valueOf(playingEntity.getInputSource()));
            }

            if (StringUtils.isNotEmpty(device.getPre_config_version())) {
               String[] temp = device.getPre_config_version().split(";");
               String preconfigVersion = temp[0];
               if (preconfigVersion != null && preconfigVersion.length() > 0 && !"-".equals(preconfigVersion)) {
                  DevicePreconfig preconfig = this.preconfigInfo.getPreconfigByDeviceId(deviceId);
                  Integer preconfigSuccess = 0;
                  Integer preconfigFailed = 0;

                  try {
                     preconfigSuccess = Integer.valueOf(temp[1]);
                     preconfigFailed = Integer.valueOf(temp[2]);
                  } catch (Exception var49) {
                     preconfigSuccess = 0;
                     preconfigFailed = 0;
                  }

                  if (preconfig == null) {
                     resource.setPreconfigVersion("-");
                  } else {
                     resource.setPreconfigVersion(preconfigVersion);
                  }

                  resource.setPreconfig(preconfig);
                  resource.setPreconfigSuccess(preconfigSuccess);
                  resource.setPreconfigFailed(preconfigFailed);
               } else {
                  resource.setPreconfigVersion("-");
               }
            }

            if (rsm_enabled && device.getRm_rule_version() != null) {
               resource.setRmRuleVersion(device.getRm_rule_version());
            }

            if (device.getError_flag() != null) {
               resource.setErrorFlag(device.getError_flag());
            }

            List groups = DeviceUtils.getGroupNamePath(deviceGroupId.getGroup_id().intValue());
            List groupPathList = new ArrayList();
            Iterator var55 = groups.iterator();

            while(var55.hasNext()) {
               DeviceGroup groupDevice = (DeviceGroup)var55.next();
               V2CommonGroupData groupPath = new V2CommonGroupData();
               groupPath.setGroupId(groupDevice.getGroup_id());
               groupPath.setGroupName(groupDevice.getGroup_name());
               groupPathList.add(groupPath);
            }

            resource.setGroupPathList(groupPathList);
            resource.setDeviceModelName(device.getDevice_model_name());
            resource.setDeviceId(device.getDevice_id());
            resource.setIpAddress(device.getIp_address());
            resource.setLastConnectionTime(device.getLast_connection_time());
            if (!motMgr.isConnected(deviceId)) {
               String time = this.disconnectedTime(device.getMonitoring_interval(), device.getLast_connection_time());
               if (!"".equals(time)) {
                  resource.setDisconnectedTime(time);
               }
            }

            if (!"iPLAYER".equalsIgnoreCase(device.getDevice_type()) && !"APLAYER".equalsIgnoreCase(device.getDevice_type())) {
               if ("WPLAYER".equalsIgnoreCase(device.getDevice_type())) {
                  resource.setFirmwareVersion(StrUtils.nvl(device.getApplication_version()));
                  resource.setPlayerVersion(StrUtils.nvl(device.getPlayer_version()));
               } else {
                  resource.setFirmwareVersion(StrUtils.nvl(device.getApplication_version()));
                  resource.setPlayerVersion(StrUtils.nvl(device.getFirmware_version()));
               }
            } else {
               resource.setFirmwareVersion(StrUtils.nvl(device.getFirmware_version()));
               resource.setPlayerVersion(StrUtils.nvl(device.getApplication_version()));
            }

            if ("RKIOSK".equalsIgnoreCase(device.getDevice_type())) {
               resource.setThirdApplicationVersion(device.getThird_application_version());

               try {
                  resource.setThirdApplicationLogSize(device.getThird_application_log_size());
                  resource.setThirdApplicationUpdateDomain(device.getThird_application_update_domain());
                  Date date = new Date(device.getThird_application_last_updated().getTime());
                  resource.setThirdApplicationLastUpdated(DateUtils.dateTime2TimeStamp(date));
               } catch (Exception var48) {
                  this.logger.error(var48.getMessage());
               }
            }

            long diskAvailable = 0L;
            if (device.getDisk_space_repository() != null && device.getDisk_space_repository() > 0L) {
               diskAvailable = device.getDisk_space_repository();
            }

            resource.setDiskSpaceRepository(diskAvailable);
            resource.setDiskSpaceAvailable(device.getDisk_space_available());
            if (StrUtils.nvl(CommonConfig.get("device.map.enable")).equals("true")) {
               if (device.getMap_location() == null) {
                  menu.put("mapLocation", "-");
               } else {
                  menu.put("mapLocation", device.getMap_location());
               }
            }

            String captureUrl = request.getContextPath() + "/image/img/thumb_img_power.png";
            String thumbUrl = request.getContextPath() + "/image/img/thumb_img_power.png";
            boolean power = DeviceUtils.isConnected(device.getDevice_id());
            if (power && device.getIs_child() && !DeviceUtils.isConnected(device.getDevice_id().split("_")[0])) {
               power = false;
            }

            try {
               DeviceGeneralConf deviceGeneralConf = deviceDao.getDeviceGeneralConf(deviceId);
               HashMap resultMap = DeviceUtils.getV2DeviceStatusInfos(request.getContextPath(), deviceGeneralConf, true, playingEntity, power);
               captureUrl = (String)resultMap.get("captureUrl");
               thumbUrl = (String)resultMap.get("thumbUrl");
            } catch (Exception var47) {
               this.logger.error("", var47);
            }

            resource.setCaptureUrl(captureUrl);
            resource.setThumbUrl(thumbUrl);
            if (device.getScreen_rotation() != null && device.getScreen_rotation() != 0L) {
               resource.setLandscape("portrait");
            } else {
               resource.setLandscape("landscape");
            }

            resource.setHasChild(device.getHas_child());
            String isDefaultContentSchedule = "N";

            try {
               ScheduleInfo sInfo = ScheduleInfoImpl.getInstance();
               isDefaultContentSchedule = sInfo.getProgram(playingEntity.getProgramId()).getIs_default();
            } catch (Exception var46) {
            }

            resource.setPower(motMgr.isConnected(deviceId));
            Timestamp messageScheduleCreateDate = null;
            Timestamp messageScheduleLastModifiedDate = null;
            if (playingEntity != null && playingEntity.getMessageScheduleId() != null) {
               try {
                  MessageInfo mInfo = MessageInfoImpl.getInstance();
                  MessageEntity message = mInfo.getMessage(playingEntity.getMessageScheduleId(), 0);
                  messageScheduleCreateDate = message.getCreate_date();
                  messageScheduleLastModifiedDate = message.getModify_date();
               } catch (Exception var45) {
               }
            }

            List contentsDownloadStatus;
            if (playingEntity != null && playingEntity.getEventScheduleId() != null && !playingEntity.getEventScheduleId().equals("00000000-0000-0000-0000-000000000000")) {
               try {
                  ScheduleInfo sInfo = ScheduleInfoImpl.getInstance();
                  EventScheduleInfo eventInfo = EventScheduleInfoImpl.getInstance();
                  EventScheduleEntity eventEntity = eventInfo.getEventSchedule(playingEntity.getEventScheduleId());
                  resource.setEventScheduleLastModifiedDate(eventEntity.getModify_date());
                  contentsDownloadStatus = sInfo.getEventList(playingEntity.getEventScheduleId());
                  List eventscheduleList = new ArrayList();
                  if (contentsDownloadStatus != null && contentsDownloadStatus.size() > 0) {
                     Iterator var29 = contentsDownloadStatus.iterator();

                     while(var29.hasNext()) {
                        EventEntity event = (EventEntity)var29.next();
                        V2DeviceEventEntity eventObj = new V2DeviceEventEntity();
                        eventObj.setEventName(event.getEvent_name());
                        eventObj.setModifyDate(event.getModify_date());
                        eventscheduleList.add(eventObj);
                     }

                     resource.seteList(eventscheduleList);
                  }
               } catch (Exception var51) {
               }
            }

            if (playingEntity != null) {
               resource.setContentScheduleId(playingEntity.getProgramId());
               resource.setContentScheduleName(playingEntity.getProgramName());
               resource.setIsDefaultContentSchedule(isDefaultContentSchedule);
               resource.setMessageScheduleId(playingEntity.getMessageScheduleId());
               resource.setMessageScheduleName(playingEntity.getMessageScheduleName());
               resource.setMessageScheduleCreateDate(messageScheduleCreateDate);
               resource.setMessageScheduleLastModifiedDate(messageScheduleLastModifiedDate);
               resource.setEventScheduleName(playingEntity.getEventScheduleName());
               resource.setEventScheduleId(playingEntity.getEventScheduleId());
               resource.setContentChannel(playingEntity.getContentChannel());
            }

            datas.setPlayingContentScheduleList(this.getNowPlayingContentList(deviceId));
            MonitoringManager mgr = MonitoringManagerImpl.getInstance();
            AlarmManager alarmManager = AlarmManagerImpl.getInstance();
            List hwSwError = alarmManager.getClientFaultListByDeviceId(device.getDevice_id());
            String error_script;
            String category;
            if (hwSwError != null && hwSwError.size() > 0) {
               contentsDownloadStatus = null;

               try {
                  Collections.sort(hwSwError, new ServerTimeDescCompare());
               } catch (Exception var44) {
                  this.logger.error("[REST_v2.0][DEVICE SERVICE][getDevice] Collections Sort", var44);
               }

               List hwSwErrorLatestList = new ArrayList(hwSwError.subList(0, hwSwError.size() > 5 ? 5 : hwSwError.size()));

               ClientFaultEntity cfe;
               for(Iterator var71 = hwSwErrorLatestList.iterator(); var71.hasNext(); cfe.setServer_time_str(StrUtils.getDiffMin(cfe.getServer_time(), false, locale))) {
                  cfe = (ClientFaultEntity)var71.next();
                  error_script = null;
                  category = cfe.getCategory();
                  String bodyContent;
                  if (cfe.getCode() != null && !category.equalsIgnoreCase("LOG")) {
                     bodyContent = null;
                     if (cfe.getCode().length() == 8) {
                        bodyContent = cfe.getCode().substring(0, 3);
                     } else {
                        bodyContent = cfe.getCode();
                     }

                     error_script = alarmManager.getErrorScript(bodyContent);
                  }

                  if (error_script == null || error_script.equals("") || error_script.equalsIgnoreCase("null")) {
                     error_script = "Invalid Error Code";
                  }

                  bodyContent = cfe.getBody_format();
                  if (bodyContent != null && !StrUtils.nvl(bodyContent).equals("")) {
                     if (category.equalsIgnoreCase("LOG")) {
                        String[] tempFileName = bodyContent.replace('\\', '/').split("/");
                        String url = "servlet/FileLoader?paramPathConfName=JOBS_RESULT_HOME&filepath=" + tempFileName[tempFileName.length - 1] + "&download=D&device_id=" + deviceId;
                        error_script = "<a href='" + url.replace('\\', '/') + "' style='cursor: pointer'>" + tempFileName[tempFileName.length - 1] + "</a>";
                     } else if (cfe.getCode().equals("25001")) {
                        String inputSource = DeviceConstants.getInputSourceName(bodyContent);
                        if (inputSource != null && !inputSource.equals("")) {
                           error_script = error_script + " (" + inputSource + ")";
                        }
                     } else {
                        error_script = error_script + " (" + bodyContent + ")";
                     }
                  }

                  cfe.setBody_format(error_script);
                  SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");

                  try {
                     Date date = sdf.parse(cfe.getClient_time());
                     Timestamp timestamp = new Timestamp(date.getTime());
                     cfe.setClient_time(StrUtils.getDiffMin(timestamp, false, locale));
                  } catch (Exception var43) {
                     this.logger.error("[REST_v2.0][DEVICE SERVICE][getDevice] Client_time", var43);
                     cfe.setClient_time(cfe.getClient_time().replace("T", " "));
                  }
               }

               datas.setErrorList(hwSwErrorLatestList);
            }

            contentsDownloadStatus = this.getV2DownloadContentResources(deviceId, "CONTENT");
            List eventDownloadStatus = this.getV2DownloadContentResources(deviceId, "event");
            datas.setContentDownloadStatus(contentsDownloadStatus);
            datas.setEventDownloadStatus(eventDownloadStatus);
            V2DeviceLogCollectResource logCollect = new V2DeviceLogCollectResource();
            error_script = StrUtils.nvl(CommonConfig.get("device.log_collect"));
            boolean supportCabinetSetting;
            if (error_script != null && error_script.equalsIgnoreCase("true") && device.getSupport_flag() != null && device.getSupport_flag().length() > 3 && device.getSupport_flag().charAt(2) == '1') {
               category = StrUtils.nvl(CommonConfig.get("device.log_collect.count"));
               int supportedCnt = 1;
               supportCabinetSetting = false;
               if (category != null) {
                  supportedCnt = Integer.parseInt(category);
               }

               int activeCnt = deviceDao.getLogProcessingDeviceCnt();
               DeviceLogCollectEntity logEntity = null;
               List deviceLogCollectEntities = deviceDao.getDeviceLogProcessInfo(deviceId);
               Iterator var36 = deviceLogCollectEntities.iterator();

               while(var36.hasNext()) {
                  DeviceLogCollectEntity logCollectEntity = (DeviceLogCollectEntity)var36.next();
                  if ("platform".equalsIgnoreCase(logCollectEntity.getType())) {
                     logEntity = logCollectEntity;
                     break;
                  }
               }

               if (logEntity != null && !logEntity.getStatus().equalsIgnoreCase("END")) {
                  V2DeviceEntityResources entityMap = new V2DeviceEntityResources();
                  entityMap.setScript(logEntity.getCategory_script());
                  entityMap.setStartTime(logEntity.getStart_time());
                  entityMap.setDuration(logEntity.getDuration());
                  entityMap.setSize(logEntity.getPacket_size());
                  Map errorMap = DeviceConstants.getDeviceLogCollectError();
                  if (errorMap != null && errorMap.containsKey(logEntity.getStatus())) {
                     String key = logEntity.getStatus();
                     entityMap.setStatus(key + " (" + (String)errorMap.get(key) + ")");
                  }

                  logCollect.setCollectInfo(entityMap);
               }

               logCollect.setIsEnable(Boolean.valueOf(error_script));
               logCollect.setAvailableCnt(supportedCnt - activeCnt);
               List logCollected = this.getLogCollected(deviceId);
               logCollect.setCollectedLogList(logCollected);
               long freeSizeLong = 0L;
               if (CommonConfig.get("e2e.enable") != null && !CommonConfig.get("e2e.enable").equalsIgnoreCase("false")) {
                  freeSizeLong = 1572864000L;
               } else {
                  String homePath = CommonConfig.get("CONTENTS_HOME");
                  String homeDir = "C:\\";
                  if (homePath != null) {
                     homeDir = homePath.split(":")[0] + ":\\";
                  }

                  File[] roots = File.listRoots();

                  for(int i = 0; roots != null && i < roots.length; ++i) {
                     if (roots[i].getPath().equalsIgnoreCase(homeDir) && roots[i].getTotalSpace() > 0L) {
                        freeSizeLong = roots[i].getFreeSpace();
                     }
                  }
               }

               logCollect.setFreeSizeLong(freeSizeLong);
            } else {
               logCollect.setIsEnable(false);
            }

            resource.setLogCollect(logCollect);
            resource.setCanWriteDevice(userContainer.checkAuthority("Device Write"));
            resource.setCanControlDevice(userContainer.checkAuthority("Device Control"));
            resource.setCanReadDevice(userContainer.checkAuthority("Device Read"));
            resource.setSupportExternalPower(DeviceUtils.isSupportExternalPower(device.getSupport_flag()));
            category = device.getSupport_flag();
            boolean supportUhd = false;
            supportCabinetSetting = false;
            boolean supportWplayer = false;
            if (category != null && category.length() >= 4) {
               if (category.charAt(3) == '1') {
                  supportUhd = true;
               }

               if (category.length() >= 5 && category.charAt(4) == '1') {
                  supportCabinetSetting = true;
               }

               if (("SPLAYER".equals(device.getDevice_type()) || "WPLAYER".equals(device.getDevice_type())) && category.length() >= 6 && category.charAt(5) == '1') {
                  supportWplayer = true;
               }
            }

            resource.setSupportUhd(supportUhd);
            resource.setSupportCabinetSetting(supportCabinetSetting);
            resource.setSupportWplayer(supportWplayer);
            resource.setResult(datas);
            DeviceSecurityConfManager securityDao = DeviceSecurityConfManagerImpl.getInstance("PREMIUM");
            DeviceSecurityConf deviceSecurityConf = securityDao.getDeviceSecurityConf(device.getDevice_id());
            if (null != deviceSecurityConf.getScreen_monitoring_lock()) {
               resource.setScreenMonitoringLock(deviceSecurityConf.getScreen_monitoring_lock());
               resource.setRemoteControlServerLock(deviceSecurityConf.getRemote_control_server_lock() == null ? 0L : deviceSecurityConf.getRemote_control_server_lock());
            }
         } catch (Exception var52) {
            this.logger.error("[REST_v2.0][DEVICE SERVICE][getDevice]", var52);
         }

         return resource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public List getTagList(String deviceId, String type) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      type = type != null ? type : "media";
      String[] deviceIds = new String[]{deviceId};
      ArrayList deviceTagList = new ArrayList();

      try {
         List deviceTags = deviceInfo.getDeviceAndTagListByDeviceIds(deviceIds, "variable".equalsIgnoreCase(type));

         for(int i = 0; i < deviceTags.size(); ++i) {
            V2DeviceTag deviceTag = new V2DeviceTag();
            V2DeviceTag tempTag = (V2DeviceTag)ConvertUtil.convertObject(deviceTags.get(i), deviceTag);
            deviceTagList.add(i, tempTag);
         }

         return deviceTagList;
      } catch (SQLException var10) {
         this.logger.error("[REST_v2.0][DEVICE SERVICE][getTagList]" + RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR.getMessage());
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
      } catch (Exception var11) {
         this.logger.error("[REST_v2.0][DEVICE SERVICE][getTagList]" + RestExceptionCode.INTERNAL_SERVER_ERROR.getMessage(), var11);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR);
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public V2CommonBulkResultResource assignTag(V2DeviceTagAssignment tagInfo) throws Exception {
      boolean checkFlag = false;
      List deviceIds = tagInfo.getDeviceIds();
      Iterator var4 = deviceIds.iterator();

      while(var4.hasNext()) {
         String deviceId = (String)var4.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var22) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
      List tagIdList = new ArrayList();
      List tagConditionList = null;
      String requestId = UUID.randomUUID().toString();
      String tagType = tagInfo.getTagType();
      String tagIds = this.convertString(tagInfo.getTagIds());
      String tagconditionIds = this.convertString(tagInfo.getTagConditionIds());
      if (StrUtils.nvl(tagIds).equals("")) {
         ((List)tagIdList).add("0");
      } else {
         tagIdList = tagInfo.getTagIds();
      }

      if ("VARIABLE".equalsIgnoreCase(tagType)) {
         tagConditionList = new ArrayList();
         if (StrUtils.nvl(tagconditionIds).equals("")) {
            tagConditionList.add("0");
         } else {
            int tagConditionCount = tagInfo.getTagConditionIds().size();

            for(int i = 0; i < tagConditionCount; ++i) {
               tagConditionList.add(tagInfo.getTagConditionIds().get(i));
            }
         }
      }

      V2CommonBulkResultResource resources = new V2CommonBulkResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      Iterator var15 = deviceIds.iterator();

      while(var15.hasNext()) {
         String deviceId = (String)var15.next();

         try {
            this.updateDeviceTag(deviceId, (List)tagIdList, tagConditionList);
            if (DeviceUtils.isConnected(deviceId)) {
               DeviceSystemSetupConf info = new DeviceSystemSetupConf();
               info.setDevice_id(deviceId);
               info.setTag_id_list((List)tagIdList);
               if (tagConditionList != null) {
                  info.setTag_condition_list(tagConditionList);
               }

               confManager.reqSetSystemSetupToDevice(info, requestId);
            }

            String[] arrDeviceId = new String[]{deviceId};
            List deviceTags = deviceInfo.getDeviceAndTagListByDeviceIds(arrDeviceId, "variable".equalsIgnoreCase(tagType));

            for(int i = 0; i < deviceTags.size(); ++i) {
               if (((DeviceTag)deviceTags.get(i)).getTag_id() != null) {
                  V2DeviceTagResource resource = new V2DeviceTagResource();
                  resource.setDeviceId(((DeviceTag)deviceTags.get(i)).getDevice_id());
                  resource.setDeviceName(((DeviceTag)deviceTags.get(i)).getDevice_name());
                  resource.setTagId(String.valueOf(((DeviceTag)deviceTags.get(i)).getTag_id()));
                  resource.setTagType(String.valueOf(((DeviceTag)deviceTags.get(i)).getTag_type()));
                  resource.setTagName(((DeviceTag)deviceTags.get(i)).getTag_name());
                  resource.setTagConditionId(String.valueOf(((DeviceTag)deviceTags.get(i)).getTag_condition_id()));
                  resource.setTagConditionName(((DeviceTag)deviceTags.get(i)).getTag_condition());
                  resource.setTagDescription(((DeviceTag)deviceTags.get(i)).getTag_desc());
                  successList.add(resource);
               }
            }
         } catch (Exception var21) {
            this.logger.error(var21);
            failList.add(deviceId);
         }
      }

      resources.setSuccessList(successList);
      resources.setFailList(failList);
      return resources;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2PageResource checkContent(int startIndex, int pageSize, String searchText, String sortColumn, String sortOrder, HttpServletResponse response) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      new V2PageResource();
      if (StrUtils.nvl(sortColumn).equals("")) {
         sortColumn = "device_name";
      }

      searchText = StrUtils.nvl(searchText);
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
      UserInfo userInfo = UserInfoImpl.getInstance();
      ListManager listMgr = new ListManager(deviceDao, "list");
      listMgr.addSearchInfo("sort", sortColumn);
      listMgr.addSearchInfo("dir", sortOrder);
      listMgr.addSearchInfo("src_name", searchText);
      listMgr.setLstSize(pageSize);
      SelectCondition condition = new SelectCondition();
      condition.setSrc_name(searchText);
      condition.setRole_name(userContainer.getUser().getRole_name());
      condition.setUser_id(userContainer.getUser().getUser_id());
      condition.setOrder_dir(sortOrder);
      condition.setSort_name(sortColumn);
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      List deviceGroupList = null;
      long mngOrgId = userContainer.getUser().getRoot_group_id();
      if (userContainer.getUser().getIs_mu().equalsIgnoreCase("Y")) {
         mngOrgId = userInfo.getCurMngOrgId(userContainer.getUser().getUser_id());
      }

      int orgId = (int)mngOrgId;
      String orgName = userInfo.getOrganGroupName(mngOrgId);
      if (orgId == 0) {
         List deviceOrgIds = DeviceUtils.getOrgGroupIdByUserId(userContainer.getUser().getUser_id());
         if (deviceOrgIds != null && deviceOrgIds.size() > 0) {
            deviceGroupList = groupDao.getAllDeviceGroups(deviceOrgIds);
         }
      }

      if (deviceGroupList == null) {
         deviceGroupList = groupDao.getChildGroupList((int)groupDao.getOrganGroupIdByName(orgName), true);
      }

      listMgr.addSearchInfo("deviceGroupList", deviceGroupList);
      listMgr.setSection("getCheckDeviceListContent");
      Long devOrgGroupId = deviceGroupInfo.getOrganGroupIdByName(orgName);
      condition.setOrg_id(devOrgGroupId);
      listMgr.addSearchInfo("condition", condition);
      PageManager pageMgr = null;
      response.setContentType("application/json;charset=UTF-8");
      List deviceList = listMgr.V2dbexecute(startIndex, pageSize);
      pageMgr = listMgr.getPageManager();
      List list = new ArrayList();

      for(int i = 0; i < deviceList.size(); ++i) {
         DeviceGeneralConf device = (DeviceGeneralConf)deviceList.get(i);
         DeviceGroup dg = null;
         if (device.getGroup_id() != null && device.getGroup_name() != null) {
            dg = new DeviceGroup();
            dg.setGroup_id(device.getGroup_id());
            dg.setGroup_name(device.getGroup_name());
         } else {
            dg = groupDao.getGroupByDeviceId(device.getDevice_id());
         }

         String organName = "";
         if (device.getOrganization() == null) {
            organName = groupDao.getOrgNameByGroupId(dg.getGroup_id());
         } else {
            organName = device.getOrganization();
         }

         V2DeviceNoticeListResource resource = new V2DeviceNoticeListResource();
         resource.setCheck("");
         resource.setDeviceId(device.getDevice_id());
         resource.setDeviceType(device.getDevice_type());
         resource.setDeviceTypeVersion(device.getDevice_type_version());
         resource.setDeviceModelName(device.getDevice_model_name());
         resource.setIpAddress(device.getIp_address());
         resource.setDiskSpaceAvailable(StrUtils.convertToByte(device.getDisk_space_available()));
         resource.setGroupId(dg.getGroup_id());
         resource.setGroupName(dg.getGroup_name());
         resource.setOrganName(organName);
         resource.setLocation(device.getLocation());
         resource.setPower(DeviceUtils.isConnected(device.getDevice_id()));
         resource.setCreateDate(device.getCreate_date());
         if (device.getDevice_name().length() < 17) {
            resource.setDeviceName(device.getDevice_name());
         } else {
            resource.setDeviceName(device.getDevice_name().substring(0, 16) + "...");
         }

         resource.setDeviceFullName(device.getDevice_name());
         resource.setDeviceModelName(device.getDevice_model_name());
         resource.setLastConnectionTime(device.getLast_connection_time());
         if (device.getChild_cnt() != null) {
            resource.setChildCnt(device.getChild_cnt());
         } else {
            resource.setChildCnt(0L);
         }

         resource.setIsChild(device.getIs_child());
         list.add(resource);
      }

      V2PageResource result = V2PageResource.createPageResource(list, pageMgr);
      result.setStartIndex(startIndex);
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2PageResource checkSchedule(int startIndex, int pageSize, String searchText, String sortColumn, String sortOrder, HttpServletResponse response) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      new V2PageResource();
      if (StrUtils.nvl(sortColumn).equals("")) {
         sortColumn = "device_name";
      }

      searchText = StrUtils.nvl(searchText);
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
      UserInfo userInfo = UserInfoImpl.getInstance();
      ListManager listMgr = new ListManager(deviceDao, "list");
      listMgr.addSearchInfo("sort", sortColumn);
      listMgr.addSearchInfo("dir", sortOrder);
      listMgr.addSearchInfo("src_name", searchText);
      listMgr.setLstSize(pageSize);
      SelectCondition condition = new SelectCondition();
      condition.setSrc_name(searchText);
      condition.setRole_name(userContainer.getUser().getRole_name());
      condition.setUser_id(userContainer.getUser().getUser_id());
      condition.setOrder_dir(sortOrder);
      condition.setSort_name(sortColumn);
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      List deviceGroupList = null;
      long mngOrgId = userContainer.getUser().getRoot_group_id();
      if (userContainer.getUser().getIs_mu().equalsIgnoreCase("Y")) {
         mngOrgId = userInfo.getCurMngOrgId(userContainer.getUser().getUser_id());
      }

      int orgId = (int)mngOrgId;
      String orgName = userInfo.getOrganGroupName(mngOrgId);
      if (orgId == 0) {
         List deviceOrgIds = DeviceUtils.getOrgGroupIdByUserId(userContainer.getUser().getUser_id());
         if (deviceOrgIds != null && deviceOrgIds.size() > 0) {
            deviceGroupList = groupDao.getAllDeviceGroups(deviceOrgIds);
         }
      }

      if (deviceGroupList == null) {
         deviceGroupList = groupDao.getChildGroupList((int)groupDao.getOrganGroupIdByName(orgName), true);
      }

      listMgr.addSearchInfo("deviceGroupList", deviceGroupList);
      listMgr.setSection("getCheckDeviceListSchedule");
      Long devOrgGroupId = deviceGroupInfo.getOrganGroupIdByName(orgName);
      condition.setOrg_id(devOrgGroupId);
      listMgr.addSearchInfo("condition", condition);
      PageManager pageMgr = null;
      response.setContentType("application/json;charset=UTF-8");
      List deviceList = listMgr.V2dbexecute(startIndex, pageSize);
      pageMgr = listMgr.getPageManager();
      List list = new ArrayList();

      for(int i = 0; i < deviceList.size(); ++i) {
         DeviceGeneralConf device = (DeviceGeneralConf)deviceList.get(i);
         DeviceGroup dg = null;
         if (device.getGroup_id() != null && device.getGroup_name() != null) {
            dg = new DeviceGroup();
            dg.setGroup_id(device.getGroup_id());
            dg.setGroup_name(device.getGroup_name());
         } else {
            dg = groupDao.getGroupByDeviceId(device.getDevice_id());
         }

         String organName = "";
         if (device.getOrganization() == null) {
            organName = groupDao.getOrgNameByGroupId(dg.getGroup_id());
         } else {
            organName = device.getOrganization();
         }

         V2DeviceNoticeListResource resource = new V2DeviceNoticeListResource();
         resource.setCheck("");
         resource.setDeviceId(device.getDevice_id());
         resource.setDeviceType(device.getDevice_type());
         resource.setDeviceTypeVersion(device.getDevice_type_version());
         resource.setDeviceModelName(device.getDevice_model_name());
         resource.setIpAddress(device.getIp_address());
         resource.setDiskSpaceAvailable(StrUtils.convertToByte(device.getDisk_space_available()));
         resource.setGroupId(dg.getGroup_id());
         resource.setGroupName(dg.getGroup_name());
         resource.setOrganName(organName);
         resource.setLocation(device.getLocation());
         resource.setPower(DeviceUtils.isConnected(device.getDevice_id()));
         resource.setCreateDate(device.getCreate_date());
         if (device.getDevice_name().length() < 17) {
            resource.setDeviceName(device.getDevice_name());
         } else {
            resource.setDeviceName(device.getDevice_name().substring(0, 16) + "...");
         }

         resource.setDeviceFullName(device.getDevice_name());
         resource.setDeviceModelName(device.getDevice_model_name());
         resource.setLastConnectionTime(device.getLast_connection_time());
         if (device.getChild_cnt() != null) {
            resource.setChildCnt(device.getChild_cnt());
         } else {
            resource.setChildCnt(0L);
         }

         resource.setIsChild(device.getIs_child());
         list.add(resource);
      }

      V2PageResource result = V2PageResource.createPageResource(list, pageMgr);
      result.setStartIndex(startIndex);
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2PageResource checkStorage(int startIndex, int pageSize, String searchText, String sortColumn, String sortOrder, HttpServletResponse response) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      new V2PageResource();
      if (StrUtils.nvl(sortColumn).equals("")) {
         sortColumn = "device_name";
      }

      searchText = StrUtils.nvl(searchText);
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
      UserInfo userInfo = UserInfoImpl.getInstance();
      ListManager listMgr = new ListManager(deviceDao, "list");
      listMgr.addSearchInfo("sort", sortColumn);
      listMgr.addSearchInfo("dir", sortOrder);
      listMgr.addSearchInfo("src_name", searchText);
      listMgr.setLstSize(pageSize);
      SelectCondition condition = new SelectCondition();
      condition.setSrc_name(searchText);
      condition.setRole_name(userContainer.getUser().getRole_name());
      condition.setUser_id(userContainer.getUser().getUser_id());
      condition.setOrder_dir(sortOrder);
      condition.setSort_name(sortColumn);
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      List deviceGroupList = null;
      long mngOrgId = userContainer.getUser().getRoot_group_id();
      if (userContainer.getUser().getIs_mu().equalsIgnoreCase("Y")) {
         mngOrgId = userInfo.getCurMngOrgId(userContainer.getUser().getUser_id());
      }

      int orgId = (int)mngOrgId;
      String orgName = userInfo.getOrganGroupName(mngOrgId);
      if (orgId == 0) {
         List deviceOrgIds = DeviceUtils.getOrgGroupIdByUserId(userContainer.getUser().getUser_id());
         if (deviceOrgIds != null && deviceOrgIds.size() > 0) {
            deviceGroupList = groupDao.getAllDeviceGroups(deviceOrgIds);
         }
      }

      if (deviceGroupList == null) {
         deviceGroupList = groupDao.getChildGroupList((int)groupDao.getOrganGroupIdByName(orgName), true);
      }

      listMgr.addSearchInfo("deviceGroupList", deviceGroupList);
      listMgr.setSection("getCheckDeviceListStorage");
      Long devOrgGroupId = deviceGroupInfo.getOrganGroupIdByName(orgName);
      condition.setOrg_id(devOrgGroupId);
      listMgr.addSearchInfo("condition", condition);
      PageManager pageMgr = null;
      response.setContentType("application/json;charset=UTF-8");
      List deviceList = listMgr.V2dbexecute(startIndex, pageSize);
      pageMgr = listMgr.getPageManager();
      List list = new ArrayList();

      for(int i = 0; i < deviceList.size(); ++i) {
         DeviceGeneralConf device = (DeviceGeneralConf)deviceList.get(i);
         DeviceGroup dg = null;
         if (device.getGroup_id() != null && device.getGroup_name() != null) {
            dg = new DeviceGroup();
            dg.setGroup_id(device.getGroup_id());
            dg.setGroup_name(device.getGroup_name());
         } else {
            dg = groupDao.getGroupByDeviceId(device.getDevice_id());
         }

         String organName = "";
         if (device.getOrganization() == null) {
            organName = groupDao.getOrgNameByGroupId(dg.getGroup_id());
         } else {
            organName = device.getOrganization();
         }

         V2DeviceNoticeListResource resource = new V2DeviceNoticeListResource();
         resource.setCheck("");
         resource.setDeviceId(device.getDevice_id());
         resource.setDeviceType(device.getDevice_type());
         resource.setDeviceTypeVersion(device.getDevice_type_version());
         resource.setDeviceModelName(device.getDevice_model_name());
         resource.setIpAddress(device.getIp_address());
         resource.setDiskSpaceAvailable(StrUtils.convertToByte(device.getDisk_space_available()));
         resource.setGroupId(dg.getGroup_id());
         resource.setGroupName(dg.getGroup_name());
         resource.setOrganName(organName);
         resource.setLocation(device.getLocation());
         resource.setPower(DeviceUtils.isConnected(device.getDevice_id()));
         resource.setCreateDate(device.getCreate_date());
         if (device.getDevice_name().length() < 17) {
            resource.setDeviceName(device.getDevice_name());
         } else {
            resource.setDeviceName(device.getDevice_name().substring(0, 16) + "...");
         }

         resource.setDeviceFullName(device.getDevice_name());
         resource.setDeviceModelName(device.getDevice_model_name());
         resource.setLastConnectionTime(device.getLast_connection_time());
         if (device.getChild_cnt() != null) {
            resource.setChildCnt(device.getChild_cnt());
         } else {
            resource.setChildCnt(0L);
         }

         resource.setIsChild(device.getIs_child());
         list.add(resource);
      }

      V2PageResource result = V2PageResource.createPageResource(list, pageMgr);
      result.setStartIndex(startIndex);
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2PageResource checkTimezone(int startIndex, int pageSize, String searchText, String sortColumn, String sortOrder, HttpServletResponse response) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      new V2PageResource();
      if (StrUtils.nvl(sortColumn).equals("")) {
         sortColumn = "device_name";
      }

      searchText = StrUtils.nvl(searchText);
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
      UserInfo userInfo = UserInfoImpl.getInstance();
      ListManager listMgr = new ListManager(deviceDao, "list");
      listMgr.addSearchInfo("sort", sortColumn);
      listMgr.addSearchInfo("dir", sortOrder);
      listMgr.addSearchInfo("src_name", searchText);
      listMgr.setLstSize(pageSize);
      SelectCondition condition = new SelectCondition();
      condition.setSrc_name(searchText);
      condition.setRole_name(userContainer.getUser().getRole_name());
      condition.setUser_id(userContainer.getUser().getUser_id());
      condition.setOrder_dir(sortOrder);
      condition.setSort_name(sortColumn);
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      List deviceGroupList = null;
      long mngOrgId = userContainer.getUser().getRoot_group_id();
      if (userContainer.getUser().getIs_mu().equalsIgnoreCase("Y")) {
         mngOrgId = userInfo.getCurMngOrgId(userContainer.getUser().getUser_id());
      }

      int orgId = (int)mngOrgId;
      String orgName = userInfo.getOrganGroupName(mngOrgId);
      if (orgId == 0) {
         List deviceOrgIds = DeviceUtils.getOrgGroupIdByUserId(userContainer.getUser().getUser_id());
         if (deviceOrgIds != null && deviceOrgIds.size() > 0) {
            deviceGroupList = groupDao.getAllDeviceGroups(deviceOrgIds);
         }
      }

      if (deviceGroupList == null) {
         deviceGroupList = groupDao.getChildGroupList((int)groupDao.getOrganGroupIdByName(orgName), true);
      }

      listMgr.addSearchInfo("deviceGroupList", deviceGroupList);
      listMgr.setSection("getCheckDeviceListTimezone");
      Long devOrgGroupId = deviceGroupInfo.getOrganGroupIdByName(orgName);
      condition.setOrg_id(devOrgGroupId);
      listMgr.addSearchInfo("condition", condition);
      PageManager pageMgr = null;
      response.setContentType("application/json;charset=UTF-8");
      List deviceList = listMgr.V2dbexecute(startIndex, pageSize);
      pageMgr = listMgr.getPageManager();
      List list = new ArrayList();

      for(int i = 0; i < deviceList.size(); ++i) {
         DeviceGeneralConf device = (DeviceGeneralConf)deviceList.get(i);
         DeviceGroup dg = null;
         if (device.getGroup_id() != null && device.getGroup_name() != null) {
            dg = new DeviceGroup();
            dg.setGroup_id(device.getGroup_id());
            dg.setGroup_name(device.getGroup_name());
         } else {
            dg = groupDao.getGroupByDeviceId(device.getDevice_id());
         }

         String organName = "";
         if (device.getOrganization() == null) {
            organName = groupDao.getOrgNameByGroupId(dg.getGroup_id());
         } else {
            organName = device.getOrganization();
         }

         V2DeviceNoticeListResource resource = new V2DeviceNoticeListResource();
         resource.setCheck("");
         resource.setDeviceId(device.getDevice_id());
         resource.setDeviceType(device.getDevice_type());
         resource.setDeviceTypeVersion(device.getDevice_type_version());
         resource.setDeviceModelName(device.getDevice_model_name());
         resource.setIpAddress(device.getIp_address());
         resource.setDiskSpaceAvailable(StrUtils.convertToByte(device.getDisk_space_available()));
         resource.setGroupId(dg.getGroup_id());
         resource.setGroupName(dg.getGroup_name());
         resource.setOrganName(organName);
         resource.setLocation(device.getLocation());
         resource.setPower(DeviceUtils.isConnected(device.getDevice_id()));
         resource.setCreateDate(device.getCreate_date());
         if (device.getDevice_name().length() < 17) {
            resource.setDeviceName(device.getDevice_name());
         } else {
            resource.setDeviceName(device.getDevice_name().substring(0, 16) + "...");
         }

         resource.setDeviceFullName(device.getDevice_name());
         resource.setDeviceModelName(device.getDevice_model_name());
         resource.setLastConnectionTime(device.getLast_connection_time());
         if (device.getChild_cnt() != null) {
            resource.setChildCnt(device.getChild_cnt());
         } else {
            resource.setChildCnt(0L);
         }

         resource.setIsChild(device.getIs_child());
         list.add(resource);
      }

      V2PageResource result = V2PageResource.createPageResource(list, pageMgr);
      result.setStartIndex(startIndex);
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2PageResource getPresetList(V2DevicePresetFilter filter) throws Exception {
      Locale locale = SecurityUtils.getLocale();
      String sortName = "update_time";
      String sortColumn = filter.getSortColumn();
      String sortOrder = filter.getSortOrder();
      int startIndex = 1;
      int pageSize = 30;
      if (filter.getStartIndex() != null) {
         startIndex = filter.getStartIndex();
      }

      if (filter.getPageSize() != null) {
         pageSize = filter.getPageSize();
      }

      if (StrUtils.nvl(sortColumn).equals("")) {
         sortColumn = sortName;
      }

      String orderDir = "desc";
      if (StrUtils.nvl(sortOrder).equals("")) {
         sortOrder = orderDir;
      }

      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      SelectCondition selectCondition = new SelectCondition();
      selectCondition.setSort_name(sortColumn);
      selectCondition.setOrder_dir(sortOrder);
      selectCondition.setStart_index(startIndex - 1);
      selectCondition.setPage_size(pageSize);

      try {
         String organ = SecurityUtils.getLoginUser().getOrganization();
         Long organId = deviceGroupInfo.getOrganGroupIdByName(organ);
         if (organId != null && organId != 0L) {
            selectCondition.setOrg_id(organId);
         }
      } catch (Exception var23) {
         selectCondition.setOrg_id((Long)null);
      }

      Map condition = new HashMap();
      condition.put("condition", selectCondition);
      PagedListInfo info = this.preconfigInfo.getPagedList(startIndex, pageSize, condition, "");
      List preconfigList = info.getPagedResultList();
      int totalCount = info.getTotalRowCount();
      List list = new ArrayList();

      for(int i = 0; i < preconfigList.size(); ++i) {
         V2DevicePreconfigResource resource = new V2DevicePreconfigResource();
         DevicePreconfig preconfig = (DevicePreconfig)preconfigList.get(i);
         resource.setPreconfigId(preconfig.getPreconfig_id());
         resource.setName(preconfig.getName());
         resource.setVersion(preconfig.getVersion());
         resource.setDescription(preconfig.getDescription());
         resource.setCreateDate(preconfig.getCreate_date());
         resource.setUpdateTime(preconfig.getUpdate_time());
         String organName = "Common";
         Long groupId = 0L;
         Long organizationId = 0L;
         if (preconfig.getOrganization_id() != 0L) {
            groupId = preconfig.getOrganization_id();
            organName = deviceGroupInfo.getOrgNameByGroupId(groupId);
            organizationId = userGroupInfo.getOrgGroupIdByName(organName);
         }

         resource.setOrganizationId(organizationId);
         resource.setOrganizationName(organName);
         resource.setCompletedDeployCount(preconfig.getCompleted_count());
         resource.setTotalDeployCount(preconfig.getTotal_count());
         resource.setGroupId(groupId);
         list.add(resource);
      }

      V2PageResource newResource = V2PageResource.createPageResource(list, info, startIndex, pageSize);
      return newResource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DevicePresetResource preconfigitems(String presetId) throws Exception {
      V2DevicePresetResource resource = new V2DevicePresetResource();
      V2DeviceDisplayConf deviceDisplayConf = null;
      V2DeviceTimeConf timeConf = null;
      V2DeviceSystemSetupConf setupConf = null;
      V2DeviceSecurityConf securityConf = null;
      List serviceList = null;
      DevicePreconfig preconfig = null;
      List softwareList = null;
      String type = "PRECONFIG";
      ArrayList organizationList;
      if (presetId != null && !presetId.equalsIgnoreCase("new")) {
         preconfig = this.preconfigInfo.getPreconfigInfo(presetId);
         if (preconfig == null) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"presetId"});
         }

         DeviceControl control = preconfig.getDevice_control();
         if (control != null) {
            V2DevicePresetResource v2DevicePresetResource = this.convertDeviceControl(control);
            deviceDisplayConf = v2DevicePresetResource.getDisplay();
            timeConf = v2DevicePresetResource.getTime();
            timeConf.fillTimerValues();
            timeConf.fillTimeClock();
            timeConf.setTimerTimer1((String)null);
            timeConf.setTimerTimer2((String)null);
            timeConf.setTimerTimer3((String)null);
            timeConf.setTimerTimer4((String)null);
            timeConf.setTimerTimer5((String)null);
            timeConf.setTimerTimer6((String)null);
            timeConf.setTimerTimer7((String)null);
            setupConf = v2DevicePresetResource.getSetup();
            securityConf = v2DevicePresetResource.getSecurity();
            serviceList = v2DevicePresetResource.getDeviceServiceConfs();
            softwareList = control.getSoftwareList();
         }

         List groupMappingList = this.preconfigInfo.getGroupMappingByPreconfig(presetId);
         organizationList = new ArrayList();
         if (groupMappingList != null && groupMappingList.size() > 0) {
            Iterator var14 = groupMappingList.iterator();

            while(var14.hasNext()) {
               Map map = (Map)var14.next();
               V2CommonGroupData groupData = new V2CommonGroupData();
               groupData.setGroupId((Long)map.get("group_id"));
               groupData.setGroupName((String)map.get("group_name"));
               organizationList.add(groupData);
            }

            resource.setGroupMapping(organizationList);
         }
      }

      V2DevicePreconfig v2Preconfig = new V2DevicePreconfig();
      if (preconfig != null) {
         v2Preconfig = this.convertPreconfig(preconfig);
      }

      v2Preconfig.setDeviceControl((V2DevicePresetConfig)null);
      resource.setDeviceServiceConfs(serviceList);
      if (deviceDisplayConf == null) {
         deviceDisplayConf = new V2DeviceDisplayConf();
         deviceDisplayConf.setModelType(type);
      }

      if (timeConf == null) {
         timeConf = new V2DeviceTimeConf();
         timeConf.setModelType(type);
      }

      if (setupConf == null) {
         setupConf = new V2DeviceSystemSetupConf();
         setupConf.setModelType(type);
      }

      if (securityConf == null) {
         securityConf = new V2DeviceSecurityConf();
         securityConf.setModelType(type);
      }

      String userId = SecurityUtils.getLoginUserId();
      organizationList = new ArrayList();
      V2CommonGroupData organizaitonData = new V2CommonGroupData();
      UserInfo userInfo = UserInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      User pUser = userInfo.getAllByUserId(userId);
      organizaitonData.setGroupId(0L);
      organizaitonData.setGroupName("Common");
      organizationList.add(organizaitonData);
      int i;
      if (pUser.getRoot_group_id() == 0L) {
         List list = userGroupInfo.getAllOrganizationGroup();

         for(i = 0; i < list.size(); ++i) {
            organizaitonData = new V2CommonGroupData();
            organizaitonData.setGroupId((Long)((Map)list.get(i)).get("group_id"));
            organizaitonData.setGroupName((String)((Map)list.get(i)).get("group_name"));
            organizationList.add(organizaitonData);
         }
      } else {
         organizaitonData = new V2CommonGroupData();
         organizaitonData.setGroupId(pUser.getRoot_group_id());
         organizaitonData.setGroupName(pUser.getOrganization());
         organizationList.add(organizaitonData);
      }

      resource.setOrganList(organizationList);
      resource.setDisplay(deviceDisplayConf);
      resource.setTime(timeConf);
      resource.setSetup(setupConf);
      resource.setSecurity(securityConf);
      String serviceStr;
      if (serviceList != null && serviceList.size() > 0) {
         V2DeviceServiceConfig serviceConf = new V2DeviceServiceConfig();

         for(i = 0; i < serviceList.size(); ++i) {
            V2DeviceServiceConf service = (V2DeviceServiceConf)serviceList.get(i);
            serviceStr = service.getProtocol() + ";" + service.getHost() + ";" + service.getPort() + ";" + service.getPath();
            if (service.getServiceType().equalsIgnoreCase("firmware_download")) {
               serviceConf.setFirmwareDownload(serviceStr);
            } else if (service.getServiceType().equalsIgnoreCase("contents_download")) {
               serviceConf.setContentsDownload(serviceStr);
            } else if (service.getServiceType().equalsIgnoreCase("pop_upload")) {
               serviceConf.setPopUpload(serviceStr);
            } else if (service.getServiceType().equalsIgnoreCase("screen_capture")) {
               serviceConf.setScreenCapture(serviceStr);
            }
         }

         resource.setServer(serviceConf);
      }

      if (StrUtils.nvl(CommonConfig.get("preconfig.server.enable")).equalsIgnoreCase("TRUE")) {
         resource.setServerConfig(true);
      }

      if (softwareList != null && softwareList.size() > 0) {
         V2DeviceCustomConf softwareCustom = new V2DeviceCustomConf();

         for(i = 0; i < softwareList.size(); ++i) {
            DeviceSoftwareConf software = (DeviceSoftwareConf)softwareList.get(i);
            serviceStr = software.getSoftware_type();
            byte var22 = -1;
            switch(serviceStr.hashCode()) {
            case 1539:
               if (serviceStr.equals("03")) {
                  var22 = 0;
               }
               break;
            case 1540:
               if (serviceStr.equals("04")) {
                  var22 = 1;
               }
            }

            switch(var22) {
            case 0:
               softwareCustom.setCustomLogo(software.getSoftware_id().toString());
               break;
            case 1:
               softwareCustom.setDefaultContent(software.getSoftware_id().toString());
            }
         }

         resource.setCustom(softwareCustom);
      }

      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public V2DevicePresetResource savePreconfig(@Valid V2DevicePreconfigData preconfigData) throws Exception {
      new DevicePreconfig();
      DevicePreconfig preconfig = this.convertPreconfig(preconfigData, (String)null);
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      Long organizationId = preconfig.getOrganization_id();
      String orgName = null;
      if (organizationId == null) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_EMPTY, new String[]{"organizationId"});
      } else {
         orgName = userGroupInfo.getGroupNameByGroupId(organizationId);
         if (orgName.equalsIgnoreCase("Common")) {
            orgName = "ROOT";
         }

         Long orgId = userGroupInfo.getOrgGroupIdByName(orgName);
         if (!orgId.toString().equalsIgnoreCase("0")) {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, orgId);
         }

         Map groupMap = groupDao.getOrgGroupId(orgName);
         Long groupId = (Long)groupMap.get("group_id");
         preconfig.setOrganization_id(groupId);
         preconfig.setOrganization_name(orgName);
         String presetId = UUID.randomUUID().toString().toUpperCase();
         preconfig.setPreconfig_id(presetId);
         preconfig.setVersion(1L);
         if (preconfigData.getName() != null && !preconfigData.getName().equals("")) {
            preconfig.setName(preconfigData.getName());
            preconfig.setDescription(preconfigData.getDescription());
            this.preconfigInfo.addDevicePreconfigInfo(preconfig);
            DeviceControl control = preconfig.getDevice_control();
            if (control == null) {
               control = new DeviceControl();
               control.setDisplay((DeviceDisplayConf)null);
               control.setTime((DeviceTimeConf)null);
               control.setSecurity((DeviceSecurityConf)null);
               control.setSetup((DeviceSystemSetupConf)null);
            } else {
               control.setDisplay(control.getDisplay());
               control.setTime(control.getTime());
               control.setSecurity(control.getSecurity());
               control.setSetup(control.getSetup());
            }

            control.setTargetId(presetId);
            Map softwareMap;
            ArrayList softwareList;
            Iterator var14;
            String type;
            if (control.getServer() != null && control.getServer().size() > 0) {
               softwareMap = control.getServer();
               softwareList = new ArrayList();
               var14 = softwareMap.keySet().iterator();

               while(var14.hasNext()) {
                  type = (String)var14.next();
                  DeviceServiceConf tmpService = new DeviceServiceConf();
                  tmpService.setService_type(type);
                  tmpService.setPreconfig_id(presetId);
                  String[] value = ((String)softwareMap.get(type)).split(";");
                  tmpService.setProtocol(value[0]);
                  tmpService.setHost(value[1]);
                  tmpService.setPort(Integer.valueOf(value[2]));
                  tmpService.setPath(value[3]);
                  softwareList.add(tmpService);
               }

               control.setServiceList(softwareList);
            }

            if (control.getSoftware() != null && control.getSoftware().size() > 0) {
               softwareMap = control.getSoftware();
               softwareList = new ArrayList();
               var14 = softwareMap.keySet().iterator();

               label89:
               while(true) {
                  DeviceSoftwareConf tmpSoftware;
                  label87:
                  while(true) {
                     if (!var14.hasNext()) {
                        control.setSoftwareList(softwareList);
                        break label89;
                     }

                     type = (String)var14.next();
                     tmpSoftware = new DeviceSoftwareConf();
                     tmpSoftware.setPreconfig_id(presetId);
                     tmpSoftware.setSoftware_id(Long.parseLong((String)softwareMap.get(type)));
                     byte var18 = -1;
                     switch(type.hashCode()) {
                     case -1841862173:
                        if (type.equals("software_default_content")) {
                           var18 = 1;
                        }
                        break;
                     case 537456417:
                        if (type.equals("software_custom_logo")) {
                           var18 = 0;
                        }
                     }

                     switch(var18) {
                     case 0:
                        tmpSoftware.setSoftware_type("03");
                        break label87;
                     case 1:
                        tmpSoftware.setSoftware_type("04");
                        break label87;
                     }
                  }

                  softwareList.add(tmpSoftware);
               }
            }

            boolean dbresult = this.updateDeviceControl(control);
            if (dbresult) {
               String groupIds = preconfig.getDevice_group_ids();
               if (groupIds == null || groupIds.length() == 0) {
                  groupIds = "";
                  List mappingList = this.preconfigInfo.getGroupMappingByPreconfig(presetId);
                  Map mapping;
                  if (mappingList != null && mappingList.size() > 0) {
                     for(Iterator var22 = mappingList.iterator(); var22.hasNext(); groupIds = groupIds + mapping.get("GROUP_ID")) {
                        mapping = (Map)var22.next();
                        if (groupIds.length() > 0) {
                           groupIds = groupIds + ",";
                        }
                     }
                  }
               }

               if (groupIds != null && groupIds.length() > 0) {
                  this.saveMappingAndDeploy(presetId, groupIds);
               }
            }

            return this.preconfigitems(presetId);
         } else {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"preset name"});
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public V2DevicePresetResource editPreconfig(@Valid V2DevicePreconfigData preconfigData, String presetId) throws Exception {
      boolean bPublishOnly = preconfigData.getDeviceControl() == null;
      DevicePreconfigInfo preconfigInfo = DevicePreconfigInfoImpl.getInstance();
      new DevicePreconfig();
      DevicePreconfig preconfig = this.convertPreconfig(preconfigData, presetId);
      preconfig.setPreconfig_id(presetId);
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      String orgName = preconfig.getOrganization_name();
      Long organizationId = preconfig.getOrganization_id();
      if (StrUtils.nvl(orgName).equals("")) {
         orgName = userGroupInfo.getGroupNameByGroupId(organizationId);
         if (StrUtils.nvl(orgName).equals("")) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"organizationId"});
         }
      }

      if (orgName.equalsIgnoreCase("Common")) {
         orgName = "ROOT";
      }

      Long orgId = userGroupInfo.getOrgGroupIdByName(orgName);
      if (!orgId.toString().equalsIgnoreCase("0")) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, orgId);
      }

      Map groupMap = groupDao.getOrgGroupId(orgName);
      Long groupId = (Long)groupMap.get("group_id");
      preconfig.setOrganization_id(groupId);
      preconfig.setOrganization_name(orgName);
      boolean dbresult = true;
      if (!bPublishOnly) {
         preconfigInfo.updatePreconfigInfo(preconfig);
         DeviceControl control = preconfig.getDevice_control();
         control.setTargetId(presetId);
         control.setDisplay(control.getDisplay());
         control.setTime(control.getTime());
         control.setSecurity(control.getSecurity());
         control.setSetup(control.getSetup());
         Map softwareMap;
         ArrayList softwareList;
         Iterator var17;
         String type;
         if (control.getServer() == null) {
            control.setServiceList(new ArrayList());
         } else {
            softwareMap = control.getServer();
            softwareList = new ArrayList();
            var17 = softwareMap.keySet().iterator();

            while(var17.hasNext()) {
               type = (String)var17.next();
               DeviceServiceConf tmpService = new DeviceServiceConf();
               tmpService.setService_type(type);
               tmpService.setPreconfig_id(presetId);
               String[] value = ((String)softwareMap.get(type)).split(";");
               tmpService.setProtocol(value[0]);
               tmpService.setHost(value[1]);
               tmpService.setPort(Integer.valueOf(value[2]));
               tmpService.setPath(value[3]);
               softwareList.add(tmpService);
            }

            control.setServiceList(softwareList);
         }

         if (control.getSoftware() != null) {
            softwareMap = control.getSoftware();
            softwareList = new ArrayList();
            var17 = softwareMap.keySet().iterator();

            label97:
            while(true) {
               DeviceSoftwareConf tmpSoftware;
               label95:
               while(true) {
                  if (!var17.hasNext()) {
                     control.setSoftwareList(softwareList);
                     break label97;
                  }

                  type = (String)var17.next();
                  tmpSoftware = new DeviceSoftwareConf();
                  tmpSoftware.setPreconfig_id(presetId);
                  String softwareId = (String)softwareMap.get(type);
                  if (!StrUtils.nvl(softwareId).equals("")) {
                     tmpSoftware.setSoftware_id(Long.parseLong(softwareId));
                  }

                  byte var22 = -1;
                  switch(type.hashCode()) {
                  case -1841862173:
                     if (type.equals("software_default_content")) {
                        var22 = 1;
                     }
                     break;
                  case 537456417:
                     if (type.equals("software_custom_logo")) {
                        var22 = 0;
                     }
                  }

                  switch(var22) {
                  case 0:
                     tmpSoftware.setSoftware_type("03");
                     break label95;
                  case 1:
                     tmpSoftware.setSoftware_type("04");
                     break label95;
                  }
               }

               softwareList.add(tmpSoftware);
            }
         }

         dbresult = this.updateDeviceControl(control);
      }

      if (dbresult) {
         String groupIds = preconfig.getDevice_group_ids();
         if (groupIds == null || groupIds.length() == 0) {
            groupIds = "";
            List mappingList = preconfigInfo.getGroupMappingByPreconfig(presetId);
            Map mapping;
            if (mappingList != null && mappingList.size() > 0) {
               for(Iterator var25 = mappingList.iterator(); var25.hasNext(); groupIds = groupIds + mapping.get("GROUP_ID")) {
                  mapping = (Map)var25.next();
                  if (groupIds.length() > 0) {
                     groupIds = groupIds + ",";
                  }
               }
            }
         }

         if (groupIds != null && groupIds.length() > 0) {
            this.saveMappingAndDeploy(presetId, groupIds);
         }
      }

      return this.preconfigitems(presetId);
   }

   @PreAuthorize("hasAnyAuthority('Device Delete Authority')")
   public V2DevicePresetDeleteResource deletePreconfig(@Valid V2CommonIds presetIds) throws SQLException {
      V2DevicePresetDeleteResource resource = new V2DevicePresetDeleteResource();
      List deletedPresetList = new ArrayList();
      List undeletedPresetList = new ArrayList();
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      boolean result = false;
      if (presetIds != null && presetIds.getIds().size() > 0) {
         for(int i = 0; i < presetIds.getIds().size(); ++i) {
            String presetId = (String)presetIds.getIds().get(i);
            String[] idArr = new String[]{presetId};

            try {
               List deployedDevices = this.preconfigInfo.getDeployStatusByPreconfigId(presetId);
               result = this.preconfigInfo.deletePreconfig(idArr);
               if (result) {
                  deletedPresetList.add(presetId);
               } else {
                  undeletedPresetList.add(presetId);
               }

               try {
                  Iterator var11 = deployedDevices.iterator();

                  while(var11.hasNext()) {
                     Map statusMap = (Map)var11.next();
                     String deviceId = (String)statusMap.get("device_id");
                     if (DeviceUtils.isConnected(deviceId)) {
                        this.preconfigInfo.deployToDevice(deviceDao.getDevice(deviceId));
                     }
                  }
               } catch (Exception var14) {
                  this.logger.error("Failed to deploy preset by deleting", var14);
               }
            } catch (SQLException var15) {
               this.logger.error("Failed to delete preset config", var15);
               undeletedPresetList.add(presetId);
            }
         }

         resource.setPresetIds(presetIds.getIds());
         resource.setSuccessList(deletedPresetList);
         resource.setFailList(undeletedPresetList);
      }

      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public V2DevicePresetResource copyPreconfig(String presetId, @Valid V2DevicePresetCopyParam param) throws Exception {
      UserGroupInfo groupInfo = UserGroupInfoImpl.getInstance();
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      DevicePreconfig origin = this.preconfigInfo.getPreconfigInfo(presetId);
      DevicePreconfig copied = new DevicePreconfig();
      long organizationId = param.getOrganizationId();
      String organizationName = groupInfo.getGroupNameByGroupId(organizationId);
      long organId = groupDao.getOrganGroupIdByName(organizationName);
      String name = param.getName();
      String description = param.getDescription();
      String newPresetId = UUID.randomUUID().toString().toUpperCase();
      copied.setPreconfig_id(newPresetId);
      copied.setVersion(1L);
      copied.setName(name);
      copied.setDescription(description);
      copied.setOrganization_id(organId);
      copied.setOrganization_name(organizationName);
      this.preconfigInfo.addDevicePreconfigInfo(copied);
      DeviceControl copiedcontrol = origin.getDevice_control();
      copiedcontrol.setTargetId(newPresetId);
      this.updateDeviceControl(copiedcontrol);
      return this.preconfigitems(presetId);
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2PageResource checkUpcomingExpiryDateOnPlaylist(int startIndex, int pageSize, String searchText, String sortColumn, String sortOrder) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (StrUtils.nvl(sortOrder).equals("")) {
         sortOrder = "asc;";
      }

      if (StrUtils.nvl(sortColumn).equals("")) {
         sortColumn = "playlist_name";
      }

      ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
      SelectCondition selectCondition = new SelectCondition();
      searchText = StrUtils.nvl(searchText);
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      selectCondition.setSrc_name(searchText);
      selectCondition.setRole_name(userContainer.getUser().getRole_name());
      selectCondition.setUser_id(userContainer.getUser().getUser_id());
      selectCondition.setOrder_dir(sortOrder);
      selectCondition.setSort_name(sortColumn);
      UserInfo userInfo = UserInfoImpl.getInstance();
      long mngOrgId = userContainer.getUser().getRoot_group_id();
      if (userContainer.getUser().getIs_mu().equalsIgnoreCase("Y")) {
         mngOrgId = userInfo.getCurMngOrgId(userContainer.getUser().getUser_id());
      }

      String orgName = userInfo.getOrganGroupName(mngOrgId);
      Long schOrgGroupId = (long)programGroupInfo.getProgramGroupForOrg(orgName);
      selectCondition.setOrg_id(schOrgGroupId);
      PlaylistInterface cmsDao = DAOFactory.getPlaylistInfoImpl("PREMIUM");
      PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl("PREMIUM");
      ContentInfo contentDao = ContentInfoImpl.getInstance();
      PlaylistDao playlistDao = new PlaylistDao();
      Map condition = new HashMap();
      condition.put("condition", selectCondition);
      PagedListInfo info = this.dao.getCheckUpcomingExpiryDatePlaylist(startIndex, pageSize, condition);
      List playlistList = info.getPagedResultList();
      List list = new ArrayList();
      if (playlistList != null) {
         String userDateTimeFormat = SecurityUtils.getUserDateFormat() + " " + SecurityUtils.getUserTimeFormat();
         Locale locale = SecurityUtils.getLocale();

         for(int i = 0; i < playlistList.size(); ++i) {
            Playlist item = (Playlist)playlistList.get(i);
            V2PlaylistResourceResponse resource = new V2PlaylistResourceResponse();
            ContentFile thumbnailFile = null;
            if (item.getPlaylist_type().equals("5")) {
               List ContentList = pInfo.getTagContentListOfPlaylist(item.getPlaylist_id(), item.getVersion_id());
               thumbnailFile = new ContentFile();
               if (ContentList != null && ContentList.size() > 0) {
                  thumbnailFile.setFile_id(((Content)ContentList.get(0)).getThumb_file_id());
                  thumbnailFile.setFile_name(((Content)ContentList.get(0)).getThumb_file_name());
               } else {
                  thumbnailFile.setFile_id("NOIMAGE_THUMBNAIL");
                  thumbnailFile.setFile_name("NOIMAGE_THUMBNAIL.PNG");
               }
            } else {
               thumbnailFile = cmsDao.getThumbFileInfo(item.getPlaylist_id());
            }

            if (thumbnailFile != null) {
               resource.setThumbFileId(thumbnailFile.getFile_id());
               resource.setThumbFileName(thumbnailFile.getFile_name());
            } else {
               Playlist playlistInfo = playlistDao.getPlaylistActiveVerInfo(item.getPlaylist_id());
               if (playlistInfo != null) {
                  Content contentInfo = contentDao.getThumbInfoOfActiveVersion(playlistInfo.getContent_id());
                  resource.setThumbFileId(contentInfo.getThumb_file_id());
                  resource.setThumbFileName(contentInfo.getThumb_file_name());
               }
            }

            resource.setPlaylistId(item.getPlaylist_id());
            resource.setDeviceType(item.getDevice_type());
            resource.setDeviceTypeVersion(item.getDevice_type_version());
            String[] convertToSeconds = item.getPlay_time().split(":");
            if (convertToSeconds.length == 1) {
               resource.setPlayTime(0L);
            } else {
               resource.setPlayTime(Long.valueOf(convertToSeconds[0]) * 3600L + Long.valueOf(convertToSeconds[1]) * 60L + Long.valueOf(convertToSeconds[2]));
            }

            resource.setTotalSize(item.getTotal_size());
            resource.setPlaylistName(item.getPlaylist_name());
            resource.setPlaylistType(item.getPlaylist_type());
            resource.setLastModifiedDate(item.getLast_modified_date());
            resource.setIgnoreTag(item.getIgnore_tag());
            resource.setEvennessPlayback(item.getEvenness_playback());
            resource.setCreatorId(item.getCreator_id());
            list.add(resource);
         }
      }

      V2PageResource newResource = V2PageResource.createPageResource(list, info, pageSize);
      newResource.setStartIndex(startIndex);
      return newResource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2PageResource checkUpcomingExpiryDateOnSchedule(int startIndex, int pageSize, String searchText, String sortColumn, String sortOrder) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (StrUtils.nvl(sortOrder).equals("")) {
         sortOrder = "desc";
      }

      if (StrUtils.nvl(sortColumn).equals("") || sortColumn.equalsIgnoreCase("EXPIRATION_DATE")) {
         sortColumn = "stop_date";
      }

      ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
      SelectCondition selectCondition = new SelectCondition();
      searchText = StrUtils.nvl(searchText);
      selectCondition.setSrc_name(searchText);
      selectCondition.setRole_name(userContainer.getUser().getRole_name());
      selectCondition.setUser_id(userContainer.getUser().getUser_id());
      selectCondition.setOrder_dir(sortOrder);
      selectCondition.setSort_name(sortColumn);
      UserInfo userInfo = UserInfoImpl.getInstance();
      long mngOrgId = userContainer.getUser().getRoot_group_id();
      if (userContainer.getUser().getIs_mu().equalsIgnoreCase("Y")) {
         mngOrgId = userInfo.getCurMngOrgId(userContainer.getUser().getUser_id());
      }

      String orgName = userInfo.getOrganGroupName(mngOrgId);
      Long schOrgGroupId = (long)programGroupInfo.getProgramGroupForOrg(orgName);
      selectCondition.setOrg_id(schOrgGroupId);
      Map condition = new HashMap();
      condition.put("condition", selectCondition);
      PagedListInfo info = this.dao.getCheckUpcomingExpiryDate(startIndex, pageSize, condition);
      List scheduleList = info.getPagedResultList();
      List list = new ArrayList();
      if (scheduleList != null) {
         String userDateTimeFormat = SecurityUtils.getUserDateFormat() + " " + SecurityUtils.getUserTimeFormat();
         Locale locale = SecurityUtils.getLocale();

         for(int i = 0; i < scheduleList.size(); ++i) {
            ScheduleAdminEntity item = (ScheduleAdminEntity)scheduleList.get(i);
            V2UpcommingExpirySchedule schedule = new V2UpcommingExpirySchedule();
            String programId = item.getProgram_id();
            String publish_status = "";

            try {
               if (item.getDevice_group_ids() != null && !item.getDevice_group_ids().equals("")) {
                  Map publishStatusMap = new HashMap();
                  this.getDetailPublishStatus(2, programId, item.getDevice_group_ids(), (StringBuffer)null, publishStatusMap);
                  if (publishStatusMap.get("total") != null && Integer.parseInt((String)publishStatusMap.get("total")) > 0 && publishStatusMap.get("status") != null) {
                     publish_status = (String)publishStatusMap.get("status");
                  }
               } else {
                  publish_status = "-";
               }
            } catch (Exception var30) {
               this.logger.error("", var30);
            }

            schedule.setProgramId(programId);
            schedule.setProgramName(item.getProgram_name());
            schedule.setProgramType(item.getProgram_type());
            schedule.setDeviceType(item.getDevice_type());
            schedule.setDeviceTypeVersion(String.valueOf(item.getDevice_type_version()));
            schedule.setLastModifiedDate(item.getModify_date());
            schedule.setDownloadStatus(publish_status);
            if (item.getStop_date() != null) {
               SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
               Date tempDate = sdf.parse(item.getStop_date());
               SimpleDateFormat t1 = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
               String tempStr = t1.format(tempDate.getTime());
               Timestamp time = Timestamp.valueOf(tempStr);
               schedule.setExpirationDate(time);
            }

            list.add(schedule);
         }
      }

      V2PageResource newResource = V2PageResource.createPageResource(list, info, pageSize);
      newResource.setStartIndex(startIndex);
      return newResource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2PageResource checkUpcomingExpiryDateDevice(int startIndex, int pageSize, String searchText, String sortColumn, String sortOrder) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      V2PageResource result = new V2PageResource();
      if (StrUtils.nvl(sortOrder).equals("")) {
         sortOrder = "desc";
      }

      if (StrUtils.nvl(sortColumn).equals("")) {
         sortColumn = "stop_date";
      }

      ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
      SelectCondition selectCondition = new SelectCondition();
      searchText = StrUtils.nvl(searchText);
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      selectCondition.setSrc_name(searchText);
      selectCondition.setRole_name(userContainer.getUser().getRole_name());
      selectCondition.setUser_id(userContainer.getUser().getUser_id());
      selectCondition.setOrder_dir(sortOrder);
      selectCondition.setSort_name(sortColumn);
      UserInfo userInfo = UserInfoImpl.getInstance();
      long mngOrgId = userContainer.getUser().getRoot_group_id();
      if (userContainer.getUser().getIs_mu().equalsIgnoreCase("Y")) {
         mngOrgId = userInfo.getCurMngOrgId(userContainer.getUser().getUser_id());
      }

      String orgName = userInfo.getOrganGroupName(mngOrgId);
      Long schOrgGroupId = (long)programGroupInfo.getProgramGroupForOrg(orgName);
      selectCondition.setOrg_id(schOrgGroupId);
      Map condition = new HashMap();
      condition.put("condition", selectCondition);
      PagedListInfo info = this.dao.getCheckUpcomingExpiryDateDevice(startIndex, pageSize, condition);
      new ConvertUtil();
      List deviceList = info.getPagedResultList();
      List list = new ArrayList();
      result.setRecordsReturned(deviceList != null ? deviceList.size() : 0);
      result.setRecordsTotal(info.getTotalRowCount());
      result.setRecordsFiltered(info.getTotalRowCount());
      result.setPageSize(pageSize);
      if (sortColumn != null && !sortColumn.equals("")) {
         result.setSortColumn(sortColumn);
         result.setSortOrder(sortOrder);
      }

      if (deviceList != null) {
         for(int i = 0; i < deviceList.size(); ++i) {
            Map map = (Map)deviceList.get(i);
            V2DeviceExpireResource resource = new V2DeviceExpireResource();
            resource.setDeviceGroupId(map.get("device_group_id").toString());
            resource.setDeviceGroupName(map.get("device_group_name").toString());
            resource.setDeviceId(map.get("device_id").toString());
            boolean power = DeviceUtils.isConnected(map.get("device_id").toString());
            resource.setPower(power);
            resource.setDeviceModelName(map.get("device_model_name").toString());
            resource.setDeviceName(map.get("device_name").toString());
            resource.setDeviceType(map.get("device_type").toString());
            resource.setDeviceTypeVersion(map.get("device_type_version").toString());
            resource.setIpAddress(map.get("ip_address") != null ? map.get("ip_address").toString() : "");
            resource.setProgramId(map.get("program_id").toString());
            resource.setProgramName(map.get("program_name").toString());
            resource.setProgramType(map.get("program_type").toString());
            String timeStr = map.get("stop_date").toString();
            if (!timeStr.equals("") && timeStr != null) {
               SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
               Date stopDate = sdf.parse(timeStr);
               Timestamp stopDateTimestamp = new Timestamp(stopDate.getTime());
               resource.setEndStopDate(stopDateTimestamp);
            }

            list.add(resource);
         }
      }

      result = V2PageResource.createPageResource(list, info, pageSize);
      result.setStartIndex(startIndex);
      return result;
   }

   private List getNowPlayingContentList(String deviceId) {
      List nowPlayingContentList = new ArrayList();
      List rtnList = new ArrayList();
      List srcList = null;

      try {
         srcList = this.deviceDao.getMonitoringInfoListByDeviceId(deviceId);
      } catch (SQLException var16) {
         var16.printStackTrace();
      }

      List playingContentList = null;
      CurrentPlayingEntity playingEntity = this.monMgr.getPlayingContent(deviceId);
      boolean isConnected = this.monMgr.isConnected(deviceId);
      if (isConnected && playingEntity != null) {
         if (playingEntity.getActiveType() != null && playingEntity.getActiveType().equalsIgnoreCase("event")) {
            playingContentList = playingEntity.getEventScheduleContentLists();
         } else {
            playingContentList = playingEntity.getContentLists();
         }
      }

      for(int i = 0; srcList != null && i < srcList.size(); ++i) {
         DeviceMonitoring monitoring = (DeviceMonitoring)srcList.get(i);
         boolean check = false;

         for(int j = 0; playingContentList != null && j < playingContentList.size(); ++j) {
            ContentList playingContent = (ContentList)playingContentList.get(j);
            if (monitoring.getFrame_index().toString().equals(playingContent.getFrameIndex()) && !check) {
               check = true;
               Content content = null;

               try {
                  content = this.contentDao.getThumbInfoOfActiveVersion(playingContent.getContentId());
               } catch (SQLException var15) {
                  this.logger.error("", var15);
               }

               if (content != null) {
                  monitoring.setContent_id(playingContent.getContentId());
                  monitoring.setContent_name(StrUtils.nvl(content.getContent_name()));
                  monitoring.setMedia_type(StrUtils.nvl(content.getMedia_type()));
                  monitoring.setVersion_id(content.getVersion_id());
                  monitoring.setCreate_date(content.getCreate_date());
                  monitoring.setThumb_file_id(content.getThumb_file_id());
                  monitoring.setThumb_file_name(content.getThumb_file_name());
                  monitoring.setFrame_name(playingContent.getFrameName());
               }
               break;
            }
         }

         rtnList.add(monitoring);
      }

      Comparator frameNameComparator = new Comparator() {
         public int compare(DeviceMonitoring d1, DeviceMonitoring d2) {
            return d1.getFrame_name() != null && d2.getFrame_name() != null ? d1.getFrame_name().compareTo(d2.getFrame_name()) : 0;
         }
      };
      Collections.sort(rtnList, frameNameComparator);
      Map frame = new HashMap();
      if (isConnected && playingEntity != null) {
         Iterator var19 = rtnList.iterator();

         while(var19.hasNext()) {
            DeviceMonitoring tempMonitoring = (DeviceMonitoring)var19.next();
            if (tempMonitoring.getContent_name() != null && !frame.containsKey(tempMonitoring.getContent_name())) {
               frame.put(tempMonitoring.getContent_name(), tempMonitoring.getContent_name());
               V2DevicePlayContentSchConf playingContentSchedule = new V2DevicePlayContentSchConf();
               playingContentSchedule.setPlayingSchContentId(tempMonitoring.getContent_id());
               playingContentSchedule.setPlayingSchContentName(tempMonitoring.getContent_name());
               playingContentSchedule.setPlayingSchFrameName(tempMonitoring.getFrame_name());
               playingContentSchedule.setPlayingSchMediaType(tempMonitoring.getMedia_type());
               playingContentSchedule.setPlayingSchCreateDate(tempMonitoring.getCreate_date());
               nowPlayingContentList.add(playingContentSchedule);
            }
         }
      }

      return nowPlayingContentList;
   }

   @PreAuthorize("hasAnyAuthority('Device Only Approval Authority')")
   public V2PageResource getUnapprovedDeviceList(V2DeviceFilter filter) throws Exception {
      V2PageResource result = new V2PageResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (StrUtils.nvl(filter.getSortColumn()).equals("")) {
         filter.setSortColumn("create_date");
      }

      if (StrUtils.nvl(filter.getSortOrder()).equals("")) {
         filter.setSortColumn("asc");
      }

      if (StrUtils.nvl(filter.getSearchText()).equals("")) {
         filter.setSearchText("");
      }

      if (filter.getStartIndex() == 0) {
         filter.setStartIndex(1);
      }

      String device_type = null;
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      List unapprovedList = new ArrayList();
      ListManager listMgr = new ListManager(deviceDao, "list");
      listMgr.addSearchInfo("sort", filter.getSortColumn());
      listMgr.addSearchInfo("dir", filter.getSortOrder());
      listMgr.addSearchInfo("device_type", filter.getDeviceType());
      String searchText = filter.getSearchText().toUpperCase();
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      listMgr.addSearchInfo("src_name", searchText);
      listMgr.setLstSize(filter.getPageSize());
      listMgr.setSection("getNonApprovedDevice");
      listMgr.addSearchInfo("license_count", DeviceUtils.getRemainedLicenseCount("iPLAYER"));
      listMgr.addSearchInfo("sPlayer_license_count", DeviceUtils.getRemainedLicenseCount("SPLAYER"));
      listMgr.addSearchInfo("signage_license_count", DeviceUtils.getRemainedLicenseCount("SIGNAGE3"));
      listMgr.addSearchInfo("android_license_count", DeviceUtils.getRemainedLicenseCount("APLAYER"));
      listMgr.addSearchInfo("wplayer_license_count", DeviceUtils.getRemainedLicenseCount("WPLAYER"));
      listMgr.addSearchInfo("lite_license_count", DeviceUtils.getRemainedLicenseCount("LPLAYER"));
      listMgr.addSearchInfo("rms_license_count", DeviceUtils.getRemainedLicenseCount("RMS"));
      PageManager pageMgr = null;
      List deviceList = listMgr.V2dbexecute(filter.getStartIndex(), filter.getPageSize());
      pageMgr = listMgr.getPageManager();
      result.setRecordsReturned(deviceList.size());
      result.setRecordsTotal(pageMgr.getTotalRowCount());
      result.setRecordsFiltered(pageMgr.getTotalRowCount());
      result.setPageSize(pageMgr.getPageSize());
      if (filter.getSortColumn() != null && !filter.getSortColumn().equals("")) {
         result.setSortColumn(filter.getSortColumn());
         result.setSortOrder(filter.getSortOrder());
      }

      User loginUser = SecurityUtils.getUserContainer().getUser();
      long loginUserOrgId = SecurityUtils.getLoginUserOrganizationId();
      String loginUserOrgName = SecurityUtils.getLoginUserOrganization();
      Map preAssignedGroupMap = DBCacheUtils.getPreAssignedGroupMap();
      boolean isFirst = true;
      boolean isServerAdminRole = RoleUtils.isServerAdminRole(loginUser);
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      boolean hasDevicePermission = serverSetupDao.checkPermissionsDeviceByOrgId(loginUserOrgId);
      DeviceGroupInfo deviceGroup = DeviceGroupInfoImpl.getInstance();
      List permissionDeviceGroupList = deviceGroup.getAllAuthorityDeviceGroups(loginUser.getUser_id());
      int index = 0;

      for(int i = 0; i < deviceList.size(); ++i) {
         V2DeviceListResource tempResource = new V2DeviceListResource();
         Map hash = (Map)deviceList.get(i);
         if (!isServerAdminRole) {
            String deviceId = hash.get("device_id").toString();
            Long deviceUnapprovedGroupCode = deviceDao.getDeviceUnapprovedGroupCode(deviceId);
            if (deviceUnapprovedGroupCode != -1L) {
               if (hasDevicePermission) {
                  if (permissionDeviceGroupList.isEmpty()) {
                     break;
                  }

                  if (!permissionDeviceGroupList.contains(deviceUnapprovedGroupCode)) {
                     continue;
                  }
               } else {
                  String deviceOrgName = deviceGroup.getParentOrgNameByGroupId(deviceUnapprovedGroupCode);
                  if (!deviceOrgName.equalsIgnoreCase(loginUserOrgName)) {
                     continue;
                  }
               }

               this.unapprovedDeviceListUtil(tempResource, hash, preAssignedGroupMap, unapprovedList, index);
               ++index;
            }
         } else {
            this.unapprovedDeviceListUtil(tempResource, hash, preAssignedGroupMap, unapprovedList, i);
         }
      }

      result = V2PageResource.createPageResource(unapprovedList, pageMgr);
      result.setStartIndex(filter.getStartIndex());
      return result;
   }

   private void unapprovedDeviceListUtil(V2DeviceListResource tempResource, Map hash, Map preAssignedGroupMap, List unapprovedList, int i) {
      tempResource.setDeviceId(hash.get("device_id").toString());
      tempResource.setDeviceType(hash.get("device_type").toString());
      tempResource.setDeviceTypeVersion(Float.parseFloat(hash.get("device_type_version").toString()));
      tempResource.setDeviceModelName(hash.get("device_model_name").toString());
      tempResource.setIpAddress(StrUtils.nvl((String)hash.get("ip_address")));
      tempResource.setCreateDate((Timestamp)hash.get("create_date"));
      tempResource.setSerialDecimal(StrUtils.nvl((String)hash.get("serial_decimal")));
      String supportFlag = (String)hash.get("support_flag");
      boolean supportUhd = false;
      boolean supportCabinetSetting = false;
      boolean supportWplayer = false;
      if (supportFlag != null && supportFlag.length() >= 4) {
         if (supportFlag.charAt(3) == '1') {
            supportUhd = true;
         }

         if (supportFlag.length() >= 5 && supportFlag.charAt(4) == '1') {
            supportCabinetSetting = true;
         }

         if (("SPLAYER".equals(hash.get("device_type")) || "WPLAYER".equals(hash.get("device_type"))) && supportFlag.length() >= 6 && supportFlag.charAt(5) == '1') {
            supportWplayer = true;
         }
      }

      tempResource.setSupportUhd(supportUhd);
      tempResource.setSupportWplayer(supportWplayer);
      tempResource.setSupportCabinetSetting(supportCabinetSetting);
      String deviceName = StrUtils.nvl((String)hash.get("device_name"));
      tempResource.setDeviceName(deviceName);
      DeviceGroup preAssignedGroup = (DeviceGroup)preAssignedGroupMap.get(hash.get("device_id"));
      tempResource.setPreAssignedGroupId(preAssignedGroup != null ? preAssignedGroup.getGroup_id() : -1L);
      tempResource.setPreAssignedGroupName(preAssignedGroup != null ? preAssignedGroup.getGroup_name() : "");
      unapprovedList.add(i, tempResource);
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public ResponseBody getCabinetList(String deviceId, DeviceFilter params) {
      ResponseBody responseBody = new ResponseBody();

      try {
         LedCabinetConfManager ledInfo = LedCabinetConfManagerImpl.getInstance();
         Map condition = new HashMap();
         condition.put("device_id", deviceId);
         condition.put("sort_name", "");
         condition.put("order_dir", "desc");
         List ledCabinetList = ledInfo.getLedCabinetPagedList(-1, -1, condition);
         int listSize = ledInfo.getLedCabinetCount(deviceId);
         List camelCabinetList = DeviceModelConverter.convertCabinetInfoToCamelStyle(ledCabinetList);
         responseBody.setItems(camelCabinetList);
         responseBody.setStartIndex(params.getStart_index());
         responseBody.setPageSize(params.getPage_size());
         responseBody.setTotalCount(listSize);
         responseBody.setStatus("Success");
      } catch (Exception var9) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var9.getMessage());
      }

      return responseBody;
   }

   private RmServerEntity getPossibleConnectingRCServer(List lists, String deviceId) {
      RmServerEntity rmServerClient = null;
      List rtnList = new ArrayList();
      if (deviceId == null) {
         deviceId = "0";
      }

      String rmServer = "";
      String return_code = null;

      try {
         DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
         DocumentBuilder builder = factory.newDocumentBuilder();
         HttpClient httpclient = new DefaultHttpClient();

         for(int i = 0; i < lists.size(); ++i) {
            if (((RmServerEntity)lists.get(i)).getPrivate_mode()) {
               if (((RmServerEntity)lists.get(i)).getPrivate_ssl()) {
                  rmServer = "https://";
               } else {
                  rmServer = "http://";
               }

               rmServer = rmServer + ((RmServerEntity)lists.get(i)).getPrivate_ip_address() + ":" + ((RmServerEntity)lists.get(i)).getPrivate_port() + "/RMServer/openapi/open?service=RMService.status&deviceId=" + deviceId;
            } else {
               if (((RmServerEntity)lists.get(i)).getUse_ssl()) {
                  rmServer = "https://";
               } else {
                  rmServer = "http://";
               }

               rmServer = rmServer + ((RmServerEntity)lists.get(i)).getIp_address() + ":" + ((RmServerEntity)lists.get(i)).getPort() + "/RMServer/openapi/open?service=RMService.status&deviceId=" + deviceId;
            }

            HttpResponse Rmserver_response;
            NodeList headNodeList;
            NodeList resutNode;
            if (!((RmServerEntity)lists.get(i)).getPrivate_mode() && ((RmServerEntity)lists.get(i)).getUse_ssl() || ((RmServerEntity)lists.get(i)).getPrivate_mode() && ((RmServerEntity)lists.get(i)).getPrivate_ssl()) {
               URL url = new URL(rmServer);
               SecurityUtils.trustAllCertificates();

               try {
                  HttpsURLConnection conn = (HttpsURLConnection)url.openConnection();
                  conn.setConnectTimeout(10000);
                  conn.connect();
                  conn.setInstanceFollowRedirects(true);
                  InputStream in = conn.getInputStream();
                  BufferedReader reader = new BufferedReader(new InputStreamReader(in));
                  Rmserver_response = null;

                  String line;
                  String resultXml;
                  for(resultXml = new String(); (line = reader.readLine()) != null; resultXml = resultXml + line) {
                  }

                  Document doc = builder.parse(new InputSource(new StringReader(resultXml)));
                  doc.getDocumentElement().normalize();
                  NodeList headNodeList = doc.getElementsByTagName("response");
                  Element subItem = (Element)headNodeList.item(0);
                  return_code = subItem.getAttribute("code");
                  if (return_code.equals("0")) {
                     headNodeList = doc.getElementsByTagName("resultValue");
                     String connection = headNodeList.item(0).getFirstChild().getNodeValue();
                     resutNode = null;
                     String[] rmServerResult = connection.split("\\|");
                     if (rmServerResult.length > 2 && rmServerResult.length > 3) {
                        this.logger.error("[Device ServiceImpl][getRmServerVnc] rmServerResult length Error - useSSL : true, privateSSL : true");
                        throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_OPERATION, new String[]{"(error code : 9997)"});
                     }

                     int index = rmServerResult[0].indexOf(":");
                     if (index == -1) {
                        this.logger.error("[Device ServiceImpl][getRmServerVnc] rmServerResult[0] The specific character ':' was not found in the indexOf function. - useSSL : true, privateSSL : true");
                        throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_OPERATION, new String[]{"(error code : 9997)"});
                     }

                     if (index + 1 >= rmServerResult[0].length()) {
                        this.logger.error("[Device ServiceImpl][getRmServerVnc] The length value of rmServerResult[0] is less than the duplicate index checked with indexOf.- useSSL : true, privateSSL : true");
                        throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_OPERATION, new String[]{"(error code : 9997)"});
                     }

                     connection = (String)rmServerResult[0].subSequence(index + 1, rmServerResult[0].length());
                     index = rmServerResult[1].indexOf(":");
                     if (index == -1) {
                        this.logger.error("[Device ServiceImpl][getRmServerVnc] rmServerResult[1] The specific character ':' was not found in the indexOf function.- useSSL : true, privateSSL : true");
                        throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_OPERATION, new String[]{"(error code : 9997)"});
                     }

                     if (index + 1 >= rmServerResult[1].length()) {
                        this.logger.error("[Device ServiceImpl][getRmServerVnc] The length value of rmServerResult[1] is less than the duplicate index checked with indexOf.- useSSL : true, privateSSL : true");
                        throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_OPERATION, new String[]{"(error code : 9997)"});
                     }

                     String deviceChk = (String)rmServerResult[1].subSequence(index + 1, rmServerResult[1].length());
                     Long rcVersion = 1L;
                     if (rmServerResult.length > 2) {
                        index = rmServerResult[2].indexOf(":");
                        if (index == -1) {
                           this.logger.error("[Device ServiceImpl][getRmServerVnc] rmServerResult[2] The specific character ':' was not found in the indexOf function.- useSSL : true, privateSSL : true");
                           throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_OPERATION, new String[]{"(error code : 9997)"});
                        }

                        if (index + 1 >= rmServerResult[2].length()) {
                           this.logger.error("[Device ServiceImpl][getRmServerVnc] The length value of rmServerResult[2] is less than the duplicate index checked with indexOf.- useSSL : true, privateSSL : true");
                           throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_OPERATION, new String[]{"(error code : 9997)"});
                        }

                        String version = (String)rmServerResult[2].subSequence(index + 1, rmServerResult[2].length());
                        if ("2".equals(version)) {
                           rcVersion = 2L;
                        }
                     }

                     RmServerEntity serverClient = (RmServerEntity)lists.get(i);
                     serverClient.setConnection(Long.valueOf(connection));
                     serverClient.setServerDeviceChk(Long.valueOf(deviceChk));
                     serverClient.setVersion(rcVersion);
                     rtnList.add(serverClient);
                  }
               } catch (Exception var40) {
                  this.logger.info("[Device ServiceImpl][getRmServerVnc] SSL time out!");
               }
            } else {
               BufferedReader rd = null;
               InputStreamReader isr = null;

               try {
                  HttpGet httpget = new HttpGet(rmServer);
                  HttpParams httpParams = new BasicHttpParams();
                  HttpConnectionParams.setConnectionTimeout(httpParams, 3000);
                  httpclient = new DefaultHttpClient(httpParams);
                  Rmserver_response = httpclient.execute(httpget);
                  HttpEntity entity = Rmserver_response.getEntity();
                  if (entity != null) {
                     isr = new InputStreamReader(Rmserver_response.getEntity().getContent());
                     rd = new BufferedReader(isr);
                     String line = null;

                     String resultXml;
                     for(resultXml = new String(); (line = rd.readLine()) != null; resultXml = resultXml + line) {
                     }

                     Document doc = builder.parse(new InputSource(new StringReader(resultXml)));
                     doc.getDocumentElement().normalize();
                     headNodeList = doc.getElementsByTagName("response");
                     Element subItem = (Element)headNodeList.item(0);
                     return_code = subItem.getAttribute("code");
                     if (return_code != null && return_code.equals("0")) {
                        resutNode = doc.getElementsByTagName("resultValue");
                        String connection = resutNode.item(0).getFirstChild().getNodeValue();
                        String deviceChk = null;
                        String[] rmServerResult = connection.split("\\|");
                        if (rmServerResult.length > 2 && rmServerResult.length > 3) {
                           this.logger.error("[Device ServiceImpl][getRmServerVnc] rmServerResult length Error - useSSL : false, privateSSL : false");
                           throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_OPERATION, new String[]{"(error code : 9997)"});
                        }

                        int index = rmServerResult[0].indexOf(":");
                        if (index == -1) {
                           this.logger.error("[Device ServiceImpl][getRmServerVnc] rmServerResult[0] The specific character ':' was not found in the indexOf function. - useSSL : false, privateSSL : false");
                           throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_OPERATION, new String[]{"(error code : 9997)"});
                        }

                        if (index + 1 >= rmServerResult[0].length()) {
                           this.logger.error("[Device ServiceImpl][getRmServerVnc] The length value of rmServerResult[0] is less than the duplicate index checked with indexOf.- useSSL : false, privateSSL : false");
                           throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_OPERATION, new String[]{"(error code : 9997)"});
                        }

                        connection = (String)rmServerResult[0].subSequence(index + 1, rmServerResult[0].length());
                        index = rmServerResult[1].indexOf(":");
                        if (index == -1) {
                           this.logger.error("[Device ServiceImpl][getRmServerVnc] rmServerResult[1] The specific character ':' was not found in the indexOf function.- useSSL : false, privateSSL : false");
                           throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_OPERATION, new String[]{"(error code : 9997)"});
                        }

                        if (index + 1 >= rmServerResult[1].length()) {
                           this.logger.error("[Device ServiceImpl][getRmServerVnc] The length value of rmServerResult[1] is less than the duplicate index checked with indexOf.- useSSL : false, privateSSL : false");
                           throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_OPERATION, new String[]{"(error code : 9997)"});
                        }

                        deviceChk = (String)rmServerResult[1].subSequence(index + 1, rmServerResult[1].length());
                        Long rcVersion = 1L;
                        if (rmServerResult.length > 2) {
                           index = rmServerResult[2].indexOf(":");
                           if (index == -1) {
                              this.logger.error("[Device ServiceImpl][getRmServerVnc] rmServerResult[2] The specific character ':' was not found in the indexOf function.- useSSL : false, privateSSL : false");
                              throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_OPERATION, new String[]{"(error code : 9997)"});
                           }

                           if (index + 1 >= rmServerResult[2].length()) {
                              this.logger.error("[Device ServiceImpl][getRmServerVnc] The length value of rmServerResult[2] is less than the duplicate index checked with indexOf.- useSSL : false, privateSSL : false");
                              throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_OPERATION, new String[]{"(error code : 9997)"});
                           }

                           String version = (String)rmServerResult[2].subSequence(index + 1, rmServerResult[2].length());
                           if ("2".equals(version)) {
                              rcVersion = 2L;
                           }
                        }

                        new RmServerEntity();
                        RmServerEntity serverClient = (RmServerEntity)lists.get(i);
                        serverClient.setConnection(Long.valueOf(connection));
                        serverClient.setServerDeviceChk(Long.valueOf(deviceChk));
                        serverClient.setVersion(rcVersion);
                        rtnList.add(serverClient);
                     }
                  }

                  httpget.abort();
               } catch (ClientProtocolException var41) {
                  this.logger.error("[Device ServiceImpl][getRmServerVnc]", var41);
               } catch (IllegalStateException var42) {
                  this.logger.error("[Device ServiceImpl][getRmServerVnc]", var42);
               } catch (ConnectException var43) {
                  this.logger.error("[Device ServiceImpl][getRmServerVnc]", var43);
               } catch (ConnectTimeoutException var44) {
                  this.logger.error("[Device ServiceImpl][getRmServerVnc]", var44);
               } catch (SocketException var45) {
                  this.logger.error("[Device ServiceImpl][getRmServerVnc]", var45);
               } catch (SAXParseException var46) {
                  this.logger.error("[Device ServiceImpl][getRmServerVnc]", var46);
               } catch (Exception var47) {
                  this.logger.error("[Device ServiceImpl][getRmServerVnc]", var47);
               } finally {
                  if (rd != null) {
                     rd.close();
                  }

                  if (isr != null) {
                     isr.close();
                  }

                  httpclient.getConnectionManager().shutdown();
               }
            }
         }
      } catch (Exception var49) {
      }

      if (rtnList.size() < 1) {
         this.logger.error("[Device ServiceImpl][getRmServerVnc] Not connected to the SPLAYER Remote server. Please check RM Server.");
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_CONNECT_FAIL, new String[]{"SPLAYER"});
      } else {
         Collections.sort(rtnList, new Comparator() {
            public int compare(RmServerEntity contact, RmServerEntity another) {
               int result = contact.getConnection().compareTo(another.getConnection());
               if (result == 0) {
                  result = contact.getServerDeviceChk().compareTo(another.getServerDeviceChk());
               }

               return result;
            }
         });
         rmServerClient = (RmServerEntity)rtnList.get(0);
         return rmServerClient;
      }
   }

   private void sendRemoteControlMO(RmServerEntity rmServerClient, String token, String deviceId) {
      StringBuilder ChangeInfoValue = new StringBuilder();
      String use_ssl = null;
      if (rmServerClient.getUse_ssl()) {
         use_ssl = "true";
      } else {
         use_ssl = "false";
      }

      ChangeInfoValue = ChangeInfoValue.append(rmServerClient.getIp_address() + ":" + rmServerClient.getPort() + "@" + use_ssl + "@" + token);
      DeviceConfManager DeviceConfManager = DeviceConfManagerImpl.getInstance();

      try {
         DeviceConfManager.reqSetDeviceConnStatus(deviceId, ".MO.DEVICE_CONF.RM.START", ChangeInfoValue.toString());
      } catch (Exception var8) {
         this.logger.info("[Device ServiceImpl][getRmServerVnc] .MO.DEVICE_CONF.RM.START MO fail!");
         this.logger.error("[Device ServiceImpl][getRmServerVnc]", var8);
      }

   }

   private String sendCommandToRCServer(RmServerEntity rmServerClient, String command, List deviceIds, String token, String authority) {
      String rmServer = null;
      String monitorUrl = null;
      String apiType = "";
      if ("start".equals(command)) {
         apiType = "ready";
      } else if ("stop".equals(command)) {
         apiType = "stop";
      }

      boolean newRCVersion = rmServerClient.getVersion() > 1L;

      try {
         DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
         DocumentBuilder builder = factory.newDocumentBuilder();
         HttpClient httpclient = new DefaultHttpClient();
         if (rmServerClient.getPrivate_mode()) {
            if (rmServerClient.getPrivate_ssl()) {
               if (!newRCVersion) {
                  rmServer = "https://" + rmServerClient.getPrivate_ip_address() + ":" + rmServerClient.getPrivate_port() + "/RMServer/openapi/open?service=RMService." + apiType + "&deviceId=" + (String)deviceIds.get(0) + "&token=" + token + "&authority=" + authority;
               } else {
                  rmServer = "https://" + rmServerClient.getPrivate_ip_address() + ":" + rmServerClient.getPrivate_port() + "/RMServer/openapi/open?service=RMService." + apiType + "&deviceId=" + String.join(",", deviceIds) + "&token=" + token + "&authority=" + authority;
               }
            } else if (!newRCVersion) {
               rmServer = "http://" + rmServerClient.getPrivate_ip_address() + ":" + rmServerClient.getPrivate_port() + "/RMServer/openapi/open?service=RMService." + apiType + "&deviceId=" + (String)deviceIds.get(0) + "&token=" + token + "&authority=" + authority;
            } else {
               rmServer = "http://" + rmServerClient.getPrivate_ip_address() + ":" + rmServerClient.getPrivate_port() + "/RMServer/openapi/open?service=RMService." + apiType + "&deviceId=" + String.join(",", deviceIds) + "&token=" + token + "&authority=" + authority;
            }

            if (rmServerClient.getUse_ssl()) {
               if (newRCVersion) {
                  monitorUrl = "https://" + rmServerClient.getIp_address() + ":" + rmServerClient.getPort() + "/RMServer/remote";
               } else {
                  monitorUrl = "https://" + rmServerClient.getIp_address() + ":" + rmServerClient.getPort() + "/RMServer/remocon.do";
               }
            } else if (newRCVersion) {
               monitorUrl = "http://" + rmServerClient.getIp_address() + ":" + rmServerClient.getPort() + "/RMServer/remote";
            } else {
               monitorUrl = "http://" + rmServerClient.getIp_address() + ":" + rmServerClient.getPort() + "/RMServer/remocon.do";
            }
         } else {
            String use_ssl = null;
            if (rmServerClient.getUse_ssl()) {
               use_ssl = "https://";
            } else {
               use_ssl = "http://";
            }

            if (newRCVersion) {
               monitorUrl = use_ssl + rmServerClient.getIp_address() + ":" + rmServerClient.getPort() + "/RMServer/remote";
               rmServer = use_ssl + rmServerClient.getIp_address() + ":" + rmServerClient.getPort() + "/RMServer/openapi/open?service=RMService." + apiType + "&deviceId=" + String.join(",", deviceIds) + "&token=" + token + "&authority=" + authority;
            } else {
               monitorUrl = use_ssl + rmServerClient.getIp_address() + ":" + rmServerClient.getPort() + "/RMServer/remocon.do";
               rmServer = use_ssl + rmServerClient.getIp_address() + ":" + rmServerClient.getPort() + "/RMServer/openapi/open?service=RMService." + apiType + "&deviceId=" + (String)deviceIds.get(0) + "&token=" + token + "&authority=" + authority;
            }
         }

         String returnCode;
         Document doc;
         if ((rmServer == null || rmServerClient.getPrivate_mode() || !rmServerClient.getUse_ssl()) && (!rmServerClient.getPrivate_mode() || !rmServerClient.getPrivate_ssl())) {
            BufferedReader rd = null;
            InputStreamReader isr = null;

            try {
               HttpClient rmHttpClient = new DefaultHttpClient();
               HttpGet httpget = new HttpGet(rmServer);
               HttpResponse rmServerResponse = rmHttpClient.execute(httpget);
               HttpEntity entity = rmServerResponse.getEntity();
               if (entity != null) {
                  isr = new InputStreamReader(rmServerResponse.getEntity().getContent());
                  rd = new BufferedReader(isr);
                  doc = null;

                  String line;
                  String resultXml;
                  for(resultXml = new String(); (line = rd.readLine()) != null; resultXml = resultXml + line) {
                  }

                  Document doc = builder.parse(new InputSource(new StringReader(resultXml)));
                  doc.getDocumentElement().normalize();
                  NodeList headNodeList = doc.getElementsByTagName("response");
                  Element subItem = (Element)headNodeList.item(0);
                  returnCode = subItem.getAttribute("code");
                  if (returnCode != null) {
                     if (!returnCode.equals("0")) {
                        byte var26 = -1;
                        switch(returnCode.hashCode()) {
                        case 1508416:
                           if (returnCode.equals("1111")) {
                              var26 = 1;
                           }
                           break;
                        case 1537220:
                           if (returnCode.equals("2006")) {
                              var26 = 0;
                           }
                           break;
                        case 1754687:
                           if (returnCode.equals("9998")) {
                              var26 = 2;
                           }
                           break;
                        case 1754688:
                           if (returnCode.equals("9999")) {
                              var26 = 3;
                           }
                        }

                        switch(var26) {
                        case 0:
                           throw new RestServiceException(RestExceptionCode.SERVICE_UNAVAILABLE_CONNECT_TO_SERVER_PLEASE_WAIT, new String[]{"Connect to the server. Please wait."});
                        case 1:
                           throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_CONTROL_ALREADY_RUN, new String[]{"Remote control has been already run"});
                        case 2:
                           throw new RestServiceException(RestExceptionCode.SERVICE_UNAVAILABLE_DEVICE_DISCONNECT, new String[]{"The device is disconnected from the server."});
                        case 3:
                           throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_CONNECT_FAIL, new String[]{"SPLAYER"});
                        default:
                           throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_OPERATION, new String[]{returnCode});
                        }
                     }

                     this.sendRemoteControlMO(rmServerClient, token, (String)deviceIds.get(0));
                  }

                  rd.close();
                  rd = null;
                  isr.close();
                  isr = null;
               }

               httpget.abort();
            } catch (ClientProtocolException var34) {
               this.logger.error("[Device ServiceImpl][getRmServerVnc]", var34);
            } catch (IOException var35) {
               this.logger.error("[Device ServiceImpl][getRmServerVnc]", var35);
            } catch (Exception var36) {
               this.logger.error("[Device ServiceImpl][getRmServerVnc]", var36);
            } finally {
               if (rd != null) {
                  rd.close();
               }

               if (isr != null) {
                  isr.close();
               }

               httpclient.getConnectionManager().shutdown();
            }
         } else {
            URL url = new URL(rmServer);
            SecurityUtils.trustAllCertificates();
            HttpsURLConnection conn = (HttpsURLConnection)url.openConnection();
            conn.setConnectTimeout(10000);

            try {
               conn.connect();
               conn.setInstanceFollowRedirects(true);
               InputStream in = conn.getInputStream();
               BufferedReader reader = new BufferedReader(new InputStreamReader(in));
               String line = null;

               String resultXml;
               for(resultXml = new String(); (line = reader.readLine()) != null; resultXml = resultXml + line) {
               }

               doc = builder.parse(new InputSource(new StringReader(resultXml)));
               doc.getDocumentElement().normalize();
               NodeList headNodeList = doc.getElementsByTagName("response");
               Element subItem = (Element)headNodeList.item(0);
               returnCode = subItem.getAttribute("code");
               if (!apiType.equals("start") || !returnCode.equals("0")) {
                  if (apiType.equals("stop") && returnCode.equals("0")) {
                     return null;
                  }

                  byte var24 = -1;
                  switch(returnCode.hashCode()) {
                  case 1508416:
                     if (returnCode.equals("1111")) {
                        var24 = 1;
                     }
                     break;
                  case 1537220:
                     if (returnCode.equals("2006")) {
                        var24 = 0;
                     }
                     break;
                  case 1754687:
                     if (returnCode.equals("9998")) {
                        var24 = 2;
                     }
                     break;
                  case 1754688:
                     if (returnCode.equals("9999")) {
                        var24 = 3;
                     }
                  }

                  switch(var24) {
                  case 0:
                     throw new RestServiceException(RestExceptionCode.SERVICE_UNAVAILABLE_CONNECT_TO_SERVER_PLEASE_WAIT, new String[]{"Connect to the server. Please wait."});
                  case 1:
                     throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_CONTROL_ALREADY_RUN, new String[]{"Remote control has been already run"});
                  case 2:
                     throw new RestServiceException(RestExceptionCode.SERVICE_UNAVAILABLE_DEVICE_DISCONNECT, new String[]{"The device is disconnected from the server."});
                  case 3:
                     throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_CONNECT_FAIL, new String[]{"SPLAYER"});
                  default:
                     throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_OPERATION, new String[]{returnCode});
                  }
               }

               this.sendRemoteControlMO(rmServerClient, token, (String)deviceIds.get(0));
            } catch (Exception var38) {
               this.logger.info("SSL time out!");
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_OPERATION, new String[]{"(error code : 9997)"});
            }
         }
      } catch (Exception var39) {
      }

      return monitorUrl;
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public V2CommonBulkResultResource getRCServerVnc(V2RemoteControlIds remoteControlIds, String command, HttpServletRequest request) throws Exception {
      List deviceIds = remoteControlIds.getDeviceIds();
      if (deviceIds != null && deviceIds.size() >= 1) {
         V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
         List successList = new ArrayList();
         List failList = new ArrayList();
         List notAvailableList = new ArrayList();
         DeviceSecurityConfManager securityDao = DeviceSecurityConfManagerImpl.getInstance("PREMIUM");
         int screenLockCount = 0;
         int remoteLockCount = 0;
         Iterator var12 = deviceIds.iterator();

         while(true) {
            String deviceId;
            DeviceSecurityConf deviceSecurityConf;
            do {
               if (!var12.hasNext()) {
                  deviceIds.removeAll(notAvailableList);
                  if (deviceIds.isEmpty()) {
                     HashMap details;
                     if (screenLockCount == 0 && remoteLockCount > 0) {
                        details = new HashMap();
                        details.put("reason", "REMOTE_CONTROL_LOCK is ON");
                        throw new RestServiceException(RestExceptionCode.BAD_REQUEST_NO_DEVICE_AVAILABLE_FOR_REMOTE_CONTROL, details);
                     }

                     if (screenLockCount > 0 && remoteLockCount == 0) {
                        details = new HashMap();
                        details.put("reason", "SCREEN_MONITORING_LOCK is ON");
                        throw new RestServiceException(RestExceptionCode.BAD_REQUEST_NO_DEVICE_AVAILABLE_FOR_REMOTE_CONTROL, details);
                     }

                     throw new RestServiceException(RestExceptionCode.BAD_REQUEST_NO_DEVICE_AVAILABLE_FOR_REMOTE_CONTROL);
                  }

                  String token = null;
                  UserContainer userContainer = SecurityUtils.getUserContainer();
                  if (userContainer == null) {
                     this.logger.error("[Device ServiceImpl][getRmServerVnc]" + RestExceptionCode.DATA_NOT_FOUND_FORMAT.generateFormattedMessages("user container"));
                     throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"user container"});
                  }

                  String authority = userContainer.getUser().getUser_id();
                  if (!StrUtils.nvl(CommonConfig.get("saas.no_token.enable")).equalsIgnoreCase("TRUE")) {
                     TokenRegistry tr = TokenRegistry.getTokenRegistry();
                     token = tr.issueToken(authority, userContainer, "RmServer");
                  }

                  authority = "";
                  if (userContainer.checkAuthority("Device Write")) {
                     authority = authority + "DeviceWrite";
                  }

                  if (userContainer.checkAuthority("Device Control")) {
                     authority = authority + "DeviceControl";
                  }

                  if (userContainer.checkAuthority("Device Read")) {
                     authority = authority + "DeviceRead";
                  }

                  RmServerDao rmserverDao = new RmServerDao();
                  List lists = rmserverDao.getRmserverInfoList();
                  RmServerEntity rc = this.getPossibleConnectingRCServer(lists, (String)null);
                  if (rc == null) {
                     throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_CONNECT_FAIL, new String[]{"SPLAYER"});
                  }

                  boolean isNewVersion = rc.getVersion() > 1L;
                  String monitoringUrl = this.sendCommandToRCServer(rc, "start", deviceIds, token, authority);
                  if (monitoringUrl == null) {
                     throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_CONNECT_FAIL, new String[]{"SPLAYER"});
                  }

                  if (isNewVersion) {
                     Iterator var20 = deviceIds.iterator();

                     while(var20.hasNext()) {
                        String deviceId = (String)var20.next();
                        GeneralInfoResource generalInfoResource = new GeneralInfoResource();
                        this.sendRemoteControlMO(rc, token, deviceId);
                        generalInfoResource.setDeviceId(deviceId);
                        generalInfoResource.setUrl(monitoringUrl);
                        generalInfoResource.setToken(token);
                        successList.add(generalInfoResource);
                     }
                  } else {
                     GeneralInfoResource generalInfoResource = new GeneralInfoResource();
                     MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
                     String deviceId = (String)deviceIds.get(0);
                     motMgr.setRMToken(deviceId, token);
                     Device device = this.deviceDao.getDevice(deviceId);
                     CurrentPlayingEntity playingEntity = null;
                     playingEntity = motMgr.getPlayingContent(deviceId);
                     String splayerInfomation = device.getDevice_name() + "|";
                     if (playingEntity != null) {
                        if (playingEntity.getProgramName() == null) {
                           splayerInfomation = splayerInfomation + "|";
                        } else {
                           splayerInfomation = splayerInfomation + playingEntity.getProgramName() + "|";
                        }
                     } else {
                        splayerInfomation = splayerInfomation + "|";
                     }

                     splayerInfomation = splayerInfomation + device.getDevice_model_name() + "|" + device.getDevice_id() + "|" + device.getFirmware_version() + "|" + device.getIp_address() + "|" + device.getGateway() + "|" + device.getTrigger_interval();
                     generalInfoResource.setDeviceId(deviceId);
                     generalInfoResource.setUrl(monitoringUrl + "?deviceId=" + deviceId + "&token=" + token + "&information=" + URLEncoder.encode(splayerInfomation, "UTF-8"));
                     generalInfoResource.setToken(token);
                     successList.add(generalInfoResource);
                     if (deviceIds.size() > 1) {
                        for(int i = 1; i < deviceIds.size(); ++i) {
                           failList.add(deviceIds.get(i));
                        }
                     }
                  }

                  resource.setSuccessList(successList);
                  resource.setFailList(failList);
                  return resource;
               }

               deviceId = (String)var12.next();
               deviceSecurityConf = securityDao.getDeviceSecurityConf(deviceId);
            } while((deviceSecurityConf.getScreen_monitoring_lock() == null || deviceSecurityConf.getScreen_monitoring_lock() != 1L) && (deviceSecurityConf.getRemote_control_server_lock() == null || deviceSecurityConf.getRemote_control_server_lock() != 1L));

            if (deviceSecurityConf.getScreen_monitoring_lock() == 1L) {
               ++screenLockCount;
            } else if (deviceSecurityConf.getRemote_control_server_lock() == 1L) {
               ++remoteLockCount;
            }

            notAvailableList.add(deviceId);
            failList.add(deviceId);
         }
      } else {
         return null;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public GeneralInfoResource getRmServerVnc(String deviceId, String command, HttpServletRequest request) throws Exception {
      GeneralInfoResource resource = new GeneralInfoResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = null;
      String productType = "RmServer";
      String token = null;
      if (deviceId == null) {
         return null;
      } else {
         DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
         Device device = deviceDao.getDevice(deviceId);
         MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
         CurrentPlayingEntity playingEntity = null;
         playingEntity = motMgr.getPlayingContent(deviceId);
         if (!motMgr.isConnected(deviceId)) {
            this.logger.error("[Device ServiceImpl][getRmServerVnc]" + RestExceptionCode.BAD_REQUEST_CONNECT_FAIL.generateFormattedMessages("device"));
            throw new RestServiceException(RestExceptionCode.SERVICE_UNAVAILABLE_DEVICE_DISCONNECT, new String[]{"The device is disconnected from the server."});
         } else {
            HttpSession session = request.getSession();
            session.setMaxInactiveInterval(1800);
            if (userContainer != null) {
               userId = userContainer.getUser().getUser_id();
               if (!StrUtils.nvl(CommonConfig.get("saas.no_token.enable")).equalsIgnoreCase("TRUE")) {
                  TokenRegistry tr = TokenRegistry.getTokenRegistry();
                  token = tr.issueToken(userId, userContainer, "RmServer");
               }

               RmServerDao rmserverDao = new RmServerDao();
               List list = rmserverDao.getRmserverInfoList();
               if (list != null) {
                  RmServerEntity rc = this.getPossibleConnectingRCServer(list, deviceId);
                  if (rc == null) {
                     this.logger.error("[Device ServiceImpl][getRmServerVnc] Not connected to the SPLAYER Remote server. Please check RM Server.");
                     throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_CONNECT_FAIL, new String[]{"SPLAYER"});
                  } else if (rc.getServerDeviceChk() > 0L) {
                     this.logger.error("[Device ServiceImpl][getRmServerVnc] Remote control has been already run");
                     throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_SERVER_CONTROL_ALREADY_RUN, new String[]{"Remote control has been already run"});
                  } else {
                     List deviceIds;
                     if ("start".equals(command)) {
                        String authority = "";
                        if (userContainer.checkAuthority("Device Write")) {
                           authority = authority + "DeviceWrite";
                        }

                        if (userContainer.checkAuthority("Device Control")) {
                           authority = authority + "DeviceControl";
                        }

                        if (userContainer.checkAuthority("Device Read")) {
                           authority = authority + "DeviceRead";
                        }

                        deviceIds = Arrays.asList(deviceId);
                        String monitoringUrl = this.sendCommandToRCServer(rc, "start", deviceIds, token, authority);
                        MonitoringManager deviceInfo = MonitoringManagerImpl.getInstance();
                        deviceInfo.setRMToken(deviceId, token);
                        String splayerInfomation = device.getDevice_name() + "|";
                        if (playingEntity != null) {
                           if (playingEntity.getProgramName() == null) {
                              splayerInfomation = splayerInfomation + "|";
                           } else {
                              splayerInfomation = splayerInfomation + playingEntity.getProgramName() + "|";
                           }
                        } else {
                           splayerInfomation = splayerInfomation + "|";
                        }

                        splayerInfomation = splayerInfomation + device.getDevice_model_name() + "|" + device.getDevice_id() + "|" + device.getFirmware_version() + "|" + device.getIp_address() + "|" + device.getGateway() + "|" + device.getTrigger_interval();
                        resource.setUrl(monitoringUrl + "?deviceId=" + deviceId + "&token=" + token + "&information=" + URLEncoder.encode(splayerInfomation, "UTF-8"));
                        resource.setToken(token);
                     } else if ("stop".equals(command)) {
                        this.logger.error("[Device ServiceImpl][getRmServerVnc][" + deviceId + "] stop RM Server! token : " + token);
                        MonitoringManager deviceInfo = MonitoringManagerImpl.getInstance();
                        token = deviceInfo.getRMToken(deviceId);
                        deviceIds = Arrays.asList(deviceId);
                        this.sendCommandToRCServer(rc, "stop", deviceIds, token, (String)null);
                     }

                     return resource;
                  }
               } else {
                  this.logger.error("Not connected to the Rm server.");
                  return resource;
               }
            } else {
               this.logger.error("[Device ServiceImpl][getRmServerVnc]" + RestExceptionCode.DATA_NOT_FOUND_FORMAT.generateFormattedMessages("user container"));
               throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"user container"});
            }
         }
      }
   }

   public DeviceSystemSetupConfResource getDeviceSetupInfoFromDB(String deviceId) throws SQLException {
      DeviceSystemSetupConfResource systemSetupCamel = new DeviceSystemSetupConfResource();
      DeviceSystemSetupConf systemSetup = this.deviceSetupConf.getDeviceSystemSetupConf(deviceId);
      if (systemSetup != null) {
         systemSetupCamel = DeviceModelConverter.convertSystemSetupToCamelStyle(systemSetup);
         String deviceType = systemSetup.getDevice_type();
         List timzoneList = new ArrayList();
         List timeZoneListItem = this.deviceSetupConf.getTimeZoneMapList(systemSetup.getTime_zone_version());
         int i;
         Map tmpTimezone;
         HashMap timezone;
         if (deviceType != null && deviceType.equalsIgnoreCase("iPLAYER")) {
            for(i = 0; i < timeZoneListItem.size(); ++i) {
               tmpTimezone = (Map)timeZoneListItem.get(i);
               timezone = new HashMap();
               timezone.put("index", tmpTimezone.get("display").toString());
               timezone.put("dst", tmpTimezone.get("is_day_light_saving"));
               timzoneList.add(timezone);
            }
         } else {
            for(i = 0; i < timeZoneListItem.size(); ++i) {
               tmpTimezone = (Map)timeZoneListItem.get(i);
               timezone = new HashMap();
               timezone.put("index", tmpTimezone.get("display").toString());
               timezone.put("dst", true);
               timzoneList.add(timezone);
            }
         }

         systemSetupCamel.setTimeZoneList(timzoneList);
         if (deviceType != null && !deviceType.equalsIgnoreCase("LPLAYER") && systemSetupCamel.getDayLightSaving() != null) {
            String dstManual = systemSetupCamel.getDayLightSavingManual();
            if (dstManual != null && dstManual.length() > 0) {
               String[] dstArr = dstManual.split(";");
               if (dstArr.length > 8) {
                  systemSetupCamel.setDstStartMonth(dstArr[0]);
                  systemSetupCamel.setDstStartWeek(dstArr[1]);
                  systemSetupCamel.setDstStartDay(dstArr[2]);
                  String[] starttimearr = dstArr[3].split(":");
                  String dstStartTime = StrUtils.getLeftFilledString(starttimearr[0], "0", 2) + ":" + StrUtils.getLeftFilledString(starttimearr[1], "0", 2);
                  systemSetupCamel.setDstStartTime(dstStartTime);
                  systemSetupCamel.setDstEndMonth(dstArr[4]);
                  systemSetupCamel.setDstEndWeek(dstArr[5]);
                  systemSetupCamel.setDstEndDay(dstArr[6]);
                  String[] endtimearr = dstArr[7].split(":");
                  String dstEndTime = StrUtils.getLeftFilledString(endtimearr[0], "0", 2) + ":" + StrUtils.getLeftFilledString(endtimearr[1], "0", 2);
                  systemSetupCamel.setDstEndTime(dstEndTime);
                  systemSetupCamel.setDstTimeDifference(dstArr[8]);
               }
            } else {
               systemSetupCamel.setDayLightSavingManual((String)null);
            }
         }
      }

      return systemSetupCamel;
   }

   @PreAuthorize("hasAnyAuthority('Device Customize Authority')")
   public Long uploadCustomizeFile(String softwareType, String swName, MultipartFile softwareFile) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      new ServerLogEntity();
      String device_type = "SPLAYER";
      String device_model = "Default";
      Software software = new Software();
      if (swName.equals("")) {
         swName = softwareFile.getOriginalFilename();
      }

      software.setSoftware_name(swName);
      software.setFile_name(softwareFile.getOriginalFilename());
      software.setFile_size(softwareFile.getSize());
      software.setDevice_type(device_type);
      software.setDevice_model_name(device_model);
      software.setIs_auto_update(false);
      software.setCreator_id(userContainer.getUser().getUser_id());
      software.setUpgrade_version("multi");
      String fileSavePath;
      String savedFileName;
      String[] acceptFilesVideo;
      int var15;
      boolean isFileSuccess;
      String filePath;
      if (softwareType.equalsIgnoreCase("customLogo")) {
         savedFileName = null;
         String[] acceptFiles = new String[]{".jpg", ".jpeg", ".bmp", ".png", ".gif"};
         acceptFilesVideo = new String[]{".avi", ".mpg", ".mpeg", ".mp4", ".vob", ".vro", ".ts", ".trp", ".tp", ".svi", ".mkv", ".wmv", ".asf", ".3gp", ".divx", ".flv", ".m2ts", ".mov", ".rm", ".rmvb", ".mts", ".webm", ".s4ud", ".ps", ".avs", ".um4", ".km4"};
         String[] var14 = acceptFiles;
         var15 = acceptFiles.length;

         int var16;
         String str;
         for(var16 = 0; var16 < var15; ++var16) {
            str = var14[var16];
            if (softwareFile.getOriginalFilename().toLowerCase().indexOf(str) > 0) {
               savedFileName = "picture";
            }
         }

         if (savedFileName == null) {
            var14 = acceptFilesVideo;
            var15 = acceptFilesVideo.length;

            for(var16 = 0; var16 < var15; ++var16) {
               str = var14[var16];
               if (softwareFile.getOriginalFilename().toLowerCase().indexOf(str) > 0) {
                  savedFileName = "video";
               }
            }
         }

         if (savedFileName == null) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SOFTWARE_FILE_UPLOAD_NOT_SUPPORT_FILE);
         }

         if (savedFileName.equals("picture") && softwareFile.getSize() > 52428800L) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_FILE_SIZE_EXCEED, new String[]{"picture"});
         }

         if (savedFileName.equals("video") && softwareFile.getSize() > 157286400L) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_FILE_SIZE_EXCEED, new String[]{"video"});
         }

         fileSavePath = "sw.logo";
         software.setSoftware_type("03");
      } else {
         if (!softwareType.equalsIgnoreCase("defaultContent")) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"softwareType"});
         }

         String[] acceptFiles = new String[]{".wmv", ".vro", ".vob", ".ts", ".trp", ".tp", ".svi", ".mts", ".mpg", ".mpeg", ".mp4", ".mov", ".mkv", ".m2ts", ".flv", ".divx", ".avi", ".asf", ".3gp"};
         isFileSuccess = false;
         acceptFilesVideo = acceptFiles;
         int var21 = acceptFiles.length;

         for(var15 = 0; var15 < var21; ++var15) {
            filePath = acceptFilesVideo[var15];
            if (softwareFile.getOriginalFilename().toLowerCase().indexOf(filePath) > 0) {
               isFileSuccess = true;
            }
         }

         if (!isFileSuccess) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SOFTWARE_FILE_UPLOAD_NOT_SUPPORT_FILE);
         }

         fileSavePath = "sw.default_content";
         software.setSoftware_type("04");
      }

      savedFileName = System.currentTimeMillis() + softwareFile.getOriginalFilename();
      isFileSuccess = FileUploadCommonHelper.saveFileToDisk(savedFileName, softwareFile, fileSavePath);
      if (!isFileSuccess) {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_FILE_UPLOAD_FAIL, new String[]{"software"});
      } else {
         software.setMime_type(ChecksumCRC32.getCRC32Value(savedFileName, fileSavePath));
         software.setFile_path(FileUploadCommonHelper.getWebPath(fileSavePath) + "/" + savedFileName);
         Long software_id = new Long((long)SequenceDB.getNextValue("MI_DMS_INFO_SOFTWARE"));
         software.setSoftware_id(software_id);
         String UPLOAD_HOME = "";
         UPLOAD_HOME = CommonConfig.get("UPLOAD_HOME");
         UPLOAD_HOME = UPLOAD_HOME.replace('/', File.separatorChar);
         FileManagerImpl fileManager;
         File swFile;
         if ("03".equalsIgnoreCase(software.getSoftware_type())) {
            fileManager = FileManagerImpl.getInstance();
            filePath = FileUploadCommonHelper.getAbsoluteDirectoryPath("sw.logo") + File.separator + savedFileName;
            swFile = new File(filePath);
            fileManager.createThumbnailFile(swFile, FileUploadCommonHelper.getAbsoluteDirectoryPath(fileSavePath), savedFileName);
         } else {
            if (!"04".equalsIgnoreCase(software.getSoftware_type())) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"softwareType"});
            }

            fileManager = FileManagerImpl.getInstance();
            filePath = FileUploadCommonHelper.getAbsoluteDirectoryPath("sw.default_content") + File.separator + savedFileName;
            swFile = new File(filePath);
            fileManager.createThumbnailFile(swFile, FileUploadCommonHelper.getAbsoluteDirectoryPath(fileSavePath), savedFileName);
         }

         SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
         boolean result = softwareDao.addDeviceSoftware(software, "P");
         if (!result) {
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_FILE_UPLOAD_FAIL, new String[]{"software"});
         } else {
            return software_id;
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Customize Authority')")
   public void publishCustomizeFile(String softwareId, String groupID) throws Exception {
      Long result = 0L;
      String rsvDate = "NOW";
      String deployAppliedVer = "";
      String appliedType = "GROUP";
      String appliedGroupStr = groupID;
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String var9 = userContainer.getUser().getUser_id();

      try {
         SoftwareManager swMgr = SoftwareManagerImpl.getInstance();
         Software software = swMgr.getSoftware(Long.parseLong(softwareId));
         if (rsvDate.equals("NOW")) {
            String timeStamp = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(Calendar.getInstance().getTime());
            software.setRsv_date(Timestamp.valueOf(timeStamp));
         } else {
            software.setType(1L);
            software.setRsv_date(DateUtils.string2Timestamp(rsvDate, SecurityUtils.getUserDateFormat() + " " + SecurityUtils.getUserTimeFormat()));
         }

         software.setDeploy_applied_ver(deployAppliedVer);
         software.setApplied_type(appliedType);
         if (appliedType.equals("GROUP")) {
            software.setDevice_group(appliedGroupStr);
         }

         software.setSubscriber_id(userContainer.getUser().getUser_id());
         Object[] rtn = swMgr.deploySoftwareToDevices(software, (String)null);
         if (rtn != null && rtn.length > 0) {
            result = (Long)rtn[0];
         }
      } catch (Exception var13) {
         this.logger.error("[SoftwareController] DEPLOY_SAVE fail! softwareId : " + softwareId);
         result = 0L;
         this.logger.error("", var13);
      }

      boolean flag = result > 0L;
      if (!flag) {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SOFTWARE_FILE_DEPLOY);
      }
   }

   public String saveRmRuleFile(MultipartFile file) throws Exception {
      if (file == null) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_NOT_NULL_OR_EMPTY, new String[]{"file data of filter"});
      } else if (!"text/xml".equalsIgnoreCase(file.getContentType())) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"content type of file data of filter"});
      } else {
         String rmRuleFilePath = CommonConfig.get("CONTENTS_HOME") + File.separator + "rm_rule";
         File uploadFileChecker = SecurityUtils.getSafeFile(rmRuleFilePath);
         InputStream is = null;
         FileOutputStream fos = null;
         String originalFilename = file.getOriginalFilename();
         int tmp = originalFilename.lastIndexOf(".");
         String ext = tmp > 0 ? originalFilename.substring(tmp) : ".xml";
         if (!uploadFileChecker.exists()) {
            boolean fSuccess = uploadFileChecker.mkdir();
            if (!fSuccess) {
               this.logger.fatal(fSuccess);
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_RM_RULE_FILE_DIRECTORY_CREATE);
            }
         }

         int index = 0;
         String filename = "rm_rule_" + System.currentTimeMillis();

         StringBuilder var10000;
         for(uploadFileChecker = SecurityUtils.getSafeFile(rmRuleFilePath + File.separator + filename + ext); uploadFileChecker.exists(); uploadFileChecker = SecurityUtils.getSafeFile(var10000.append(index).append(ext).toString())) {
            var10000 = (new StringBuilder()).append(rmRuleFilePath).append(File.separator).append(filename);
            ++index;
         }

         String finalFilename = filename + (index > 0 ? index : "") + ext;

         try {
            is = file.getInputStream();
            fos = new FileOutputStream(rmRuleFilePath + File.separator + finalFilename, false);
            byte[] buf = new byte[1048576];
            boolean var13 = false;

            int binaryRead;
            while((binaryRead = is.read(buf)) != -1) {
               fos.write(buf, 0, binaryRead);
            }
         } catch (Exception var17) {
            this.logger.error("", var17);
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_FILE_DOWNLOAD_FAIL, new String[]{"rm rule"});
         } finally {
            if (is != null) {
               is.close();
            }

            if (fos != null) {
               fos.close();
            }

         }

         return finalFilename;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public V2DeviceReqServiceResource reqCabinetMdc(String deviceId, V2CommonIds cabinetIds) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      V2DeviceReqServiceResource resource = new V2DeviceReqServiceResource();
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      String sessionId = UUID.randomUUID().toString();
      new ArrayList();
      List failList = new ArrayList();
      List childIds = cabinetIds.getIds();
      List retChildIds = new ArrayList();
      if (childIds != null && childIds.size() > 0) {
         boolean parentIsConnected = DeviceUtils.isConnected(deviceId);
         if (!parentIsConnected) {
            resource.setFailList(childIds);
            return resource;
         } else {
            try {
               for(int i = 1; i <= 32; ++i) {
                  retChildIds.clear();
                  Iterator var12 = childIds.iterator();

                  while(var12.hasNext()) {
                     String temp = (String)var12.next();
                     if (temp.split("-")[0].equals(i + "")) {
                        retChildIds.add(temp);
                     }
                  }

                  if (retChildIds.size() > 0) {
                     confMgr.reqGetCabinetConfFromDevice(deviceId, retChildIds, sessionId, "CABINET_MDC");
                  }
               }
            } catch (Exception var14) {
               this.logger.error("", var14);
               failList.addAll(childIds);
            }

            resource.setSuccessList(childIds);
            resource.setFailList(failList);
            resource.setRequestId(sessionId);
            return resource;
         }
      } else {
         this.logger.error("child deviceIds parameter is required.");
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_EMPTY, new String[]{"cabinetIds"});
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2CommonBulkResultResource getCabinetInfo(String deviceId, String requestId) throws Exception {
      V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      List cabinetList = null;
      List successList = new ArrayList();
      List failList = new ArrayList();
      boolean parentIsConnected = DeviceUtils.isConnected(deviceId);
      if (!parentIsConnected) {
         failList.add(deviceId);
         resource.setFailList(failList);
         return resource;
      } else {
         cabinetList = confMgr.getCabinetConfResultSet(deviceId, requestId, "GET_LED_CABINET_CONF", "CABINET_MDC");
         if (cabinetList != null) {
            successList = (List)cabinetList.stream().map(V2DeviceLedCabinetFactory::v2DeviceLedCabinetResourceBuilder).collect(Collectors.toList());
         } else {
            failList.add(deviceId);
         }

         resource.setSuccessList((List)successList);
         resource.setFailList(failList);
         return resource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2CommonBulkResultResource getExternalPowerInfo(String deviceId, String requestId) throws Exception {
      V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      V2DeviceExternalPowerResource deviceExternalPowerResource = null;
      List successList = new ArrayList();
      List failList = new ArrayList();
      boolean parentIsConnected = DeviceUtils.isConnected(deviceId);
      if (!parentIsConnected) {
         failList.add(deviceId);
         resource.setFailList(failList);
         return resource;
      } else {
         deviceExternalPowerResource = confMgr.getExternalPowerConfResultSet(deviceId, requestId, "GET_LED_CABINET_CONF", "EXTERNAL_POWER");
         if (deviceExternalPowerResource != null) {
            successList.add(deviceExternalPowerResource);
         } else {
            failList.add(deviceId);
         }

         resource.setSuccessList(successList);
         resource.setFailList(failList);
         return resource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public V2CommonUpdateResult saveExpiration(V2DeviceLicenseConf body) throws Exception {
      V2CommonUpdateResult resource = new V2CommonUpdateResult();
      boolean checkFlag = false;
      List deviceIds = body.getDeviceIds();
      if (deviceIds != null && deviceIds.size() >= 1) {
         Iterator var5 = deviceIds.iterator();

         while(var5.hasNext()) {
            String deviceId = (String)var5.next();

            try {
               RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
            } catch (Exception var11) {
               checkFlag = true;
               break;
            }
         }

         if (checkFlag) {
            RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
         }

         String calDate = StrUtils.nvl(body.getCalDate());
         if (calDate.toUpperCase().equals("INFINITY")) {
            calDate = "";
         }

         List successList = new ArrayList();
         List failList = new ArrayList();
         Iterator var8 = deviceIds.iterator();

         while(var8.hasNext()) {
            String deviceId = (String)var8.next();
            String message = DeviceUtils.changeExpiration((String[])null, deviceId, calDate);
            if ("device_approval_success".equals(message)) {
               successList.add(deviceId);
            } else {
               failList.add(deviceId);
            }
         }

         resource.setSuccessList(successList);
         resource.setFailList(failList);
         return resource;
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_EMPTY, new String[]{"deviceIds"});
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public V2CommonBulkResultResource saveChannel(V2DeviceSaveChannelConf body, HttpServletRequest request) throws Exception {
      V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
      List deviceIds = body.getDeviceIds();
      if (deviceIds != null && deviceIds.size() >= 1) {
         boolean checkFlag = false;
         Iterator var6 = deviceIds.iterator();

         while(var6.hasNext()) {
            String deviceId = (String)var6.next();

            try {
               RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
            } catch (Exception var22) {
               checkFlag = true;
               break;
            }
         }

         if (checkFlag) {
            RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
         }

         UserContainer userContainer = SecurityUtils.getUserContainer();
         Locale locale = SecurityUtils.getLocale();
         String chkChannel = StrUtils.nvl(body.getChkChannel());
         DeviceDisplayConf info = new DeviceDisplayConf();
         List successList = new ArrayList();
         List failList = new ArrayList();
         if (!chkChannel.equals("")) {
            info.setBasic_direct_channel(chkChannel);
            if (deviceIds != null) {
               DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
               new ServerLogEntity();
               String eventType = "Edit Display Conf.";
               String strMenu = "MENU";
               String strMenuName = "Display";
               String strCommand = "Edit Display Conf.";
               Iterator var18 = deviceIds.iterator();

               while(var18.hasNext()) {
                  String deviceId = (String)var18.next();

                  try {
                     info.setDevice_id(deviceId);
                     confManager.reqSetDisplayToDevice(info, request.getSession().getId(), "LIST");
                     successList.add(deviceId);
                  } catch (Exception var21) {
                     this.logger.error("", var21);
                     failList.add(deviceId);
                  }
               }
            }
         } else {
            successList.addAll(deviceIds);
         }

         resource.setSuccessList(successList);
         resource.setFailList(failList);
         return resource;
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_EMPTY, new String[]{"deviceIds"});
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public V2CommonStatusResource logProcess(String deviceId, V2DeviceLogProcess body, HttpServletRequest request) throws Exception {
      V2DeviceLogFileStatusResource resource = new V2DeviceLogFileStatusResource();
      deviceId = StrUtils.nvl(deviceId).equals("") ? "" : deviceId;
      String script = StrUtils.nvl(body.getScript()).equals("") ? "" : body.getScript();
      String duration = StrUtils.nvl(body.getDuration()).equals("") ? "5" : body.getDuration();
      String packetSize = StrUtils.nvl(body.getPacketSize()).equals("") ? "1" : body.getPacketSize();
      String status = StrUtils.nvl(body.getStatus()).equals("") ? "READY" : body.getStatus();
      String step = StrUtils.nvl(body.getStep()).equals("") ? "0" : body.getStep();
      String chkTimeout = StrUtils.nvl(body.getChkTimeout()).equals("") ? "1" : body.getChkTimeout();
      String reqId = StrUtils.nvl(body.getRequestId());
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Date currentTime = TimeUtil.getCurrentGMTTime();
      Timestamp startTime = DateUtils.dateTime2TimeStamp(currentTime);
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
      String token = sdf.format(startTime);
      resource.setId(deviceId);
      resource.setScript(script);
      DeviceLogCollectEntity logInfo = null;
      List deviceLogCollectEntities = deviceDao.getDeviceLogProcessInfo(deviceId);
      Iterator var19;
      DeviceLogCollectEntity logCollectEntity;
      if ("INIT".equalsIgnoreCase(status) && step.equalsIgnoreCase("0")) {
         var19 = deviceLogCollectEntities.iterator();

         while(var19.hasNext()) {
            logCollectEntity = (DeviceLogCollectEntity)var19.next();
            if (!"END".equalsIgnoreCase(logCollectEntity.getStatus())) {
               resource.setStatus("already collecting");
               resource.setResponse("failed");
               return resource;
            }
         }
      }

      var19 = deviceLogCollectEntities.iterator();

      String deviceResponse;
      while(var19.hasNext()) {
         logCollectEntity = (DeviceLogCollectEntity)var19.next();
         deviceResponse = logCollectEntity.getCategory_script();
         if (script.equals(deviceResponse)) {
            logInfo = logCollectEntity;
            break;
         }

         if (logCollectEntity.getType().equalsIgnoreCase("platform") && !script.equalsIgnoreCase("wplayer") && !script.equalsIgnoreCase("wplayer_api_nopswd") && !script.equalsIgnoreCase("third_application_nopswd")) {
            logInfo = logCollectEntity;
            break;
         }
      }

      String type = this.categoryScript2Type(script);
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      if (!DeviceUtils.isConnected(deviceId)) {
         if (!status.equalsIgnoreCase("END")) {
            status = "DISCONNECT";
         }

         resource.setStatus(status.toLowerCase());
         if (!"platform".equalsIgnoreCase(type)) {
            resource.setResponse("failed");
            return resource;
         } else {
            if (logInfo != null) {
               deviceDao.updateDeviceLogInfo(deviceId, type, script, status, startTime, Integer.parseInt(duration), Integer.parseInt(packetSize), token, (String)null);
            } else {
               deviceDao.addDeviceLogProcessInfo(deviceId, type, script, status, startTime, Integer.parseInt(duration), Integer.parseInt(packetSize), token);
            }

            resource.setResponse("success");
            return resource;
         }
      } else {
         if (step.equalsIgnoreCase("0")) {
            if (logInfo != null) {
               deviceDao.updateDeviceLogInfo(deviceId, type, script, status, startTime, Integer.parseInt(duration), Integer.parseInt(packetSize), token, (String)null);
            } else if (!status.equalsIgnoreCase("END")) {
               deviceDao.addDeviceLogProcessInfo(deviceId, type, script, status, startTime, Integer.parseInt(duration), Integer.parseInt(packetSize), token);
            }

            deviceResponse = UUID.randomUUID().toString();
            confMgr.setDeviceLogProcessing(deviceId, script, deviceResponse);
            resource.setStatus(status.toLowerCase());
            resource.setRequestId(deviceResponse);
         } else if (Integer.valueOf(chkTimeout) >= 60) {
            if (logInfo != null) {
               deviceDao.updateDeviceLogProcessStatus(deviceId, type, script, "END");
            }

            resource.setStatus("finished");
            resource.setResponse("success");
         } else {
            deviceResponse = confMgr.GetDeviceLogProcessingResultSet(deviceId, reqId);
            if (deviceResponse != null) {
               resource.setStatus("finished");
               resource.setResponse(deviceResponse.toLowerCase());
               if (!deviceResponse.equalsIgnoreCase("success")) {
                  deviceDao.updateDeviceLogProcessStatus(deviceId, type, script, "END");
               }
            } else {
               resource.setStatus("waiting");
            }
         }

         return resource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public V2DeletedDevice clearCollected(String deviceId, String categoryScript) throws Exception {
      V2DeletedDevice resource = new V2DeletedDevice();
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      String type = this.categoryScript2Type(categoryScript);
      String DEVICE_LOG_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "device_log";
      String logFileFolderPath = DEVICE_LOG_HOME + File.separator + deviceId;

      try {
         File logFileFolder = SecurityUtils.getSafeFile(logFileFolderPath);
         String fileName = null;
         if ("third_application".equalsIgnoreCase(type)) {
            fileName = "Kiosk";
         } else if ("wplayer".equalsIgnoreCase(type)) {
            fileName = "WPlayer";
         }

         if (DeviceUtils.deleteLogFile(logFileFolder, type, fileName)) {
            this.logger.info("LogFilesFolder and files are cleaned up successfully!");
            resource.setDeviceId(deviceId);
            return resource;
         } else {
            this.logger.error("Failed to delete log file for [ " + deviceId + " ]on the following device.");
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_FILE_DELETE_FAIL, new String[]{"log"});
         }
      } catch (Exception var9) {
         this.logger.error("", var9);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_DELETE_FAIL, new String[]{"log"});
      }
   }

   private String categoryScript2Type(String categoryScript) {
      String type = "platform";
      if (!categoryScript.equalsIgnoreCase("wplayer") && !categoryScript.equalsIgnoreCase("wplayer_api_nopswd")) {
         if (categoryScript.equalsIgnoreCase("third_application_nopswd")) {
            type = "third_application";
         }
      } else {
         type = "wplayer";
      }

      return type;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public List getLogCollected(String deviceId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      String DEVICE_LOG_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "device_log";

      List deviceLogCollectEntities;
      try {
         deviceLogCollectEntities = this.deviceDao.getAllDeviceLogProcess();
         Iterator var4 = deviceLogCollectEntities.iterator();

         while(var4.hasNext()) {
            DeviceLogCollectEntity deviceLogCollectEntity = (DeviceLogCollectEntity)var4.next();
            if (!"platform".equalsIgnoreCase(deviceLogCollectEntity.getType()) && (new Date()).getTime() - deviceLogCollectEntity.getStart_time().getTime() > 60000L) {
               this.deviceDao.updateDeviceLogProcessStatus(deviceLogCollectEntity.getDevice_id(), deviceLogCollectEntity.getType(), deviceLogCollectEntity.getCategory_script(), "END");
            }
         }
      } catch (Exception var18) {
      }

      deviceLogCollectEntities = this.deviceDao.getDeviceLogProcessInfo(deviceId);
      ArrayList list = new ArrayList();

      try {
         Iterator var20 = deviceLogCollectEntities.iterator();

         while(true) {
            DeviceLogCollectEntity logEntity;
            String type;
            String fileName;
            SimpleDateFormat df;
            File[] files;
            do {
               do {
                  File logFileFolder;
                  do {
                     do {
                        if (!var20.hasNext()) {
                           return list;
                        }

                        logEntity = (DeviceLogCollectEntity)var20.next();
                     } while(logEntity == null);

                     type = this.categoryScript2Type(logEntity.getCategory_script());
                     String logPath = DEVICE_LOG_HOME + File.separator + deviceId;
                     logFileFolder = SecurityUtils.getSafeFile(logPath);
                     fileName = logEntity.getFile_name();
                     df = new SimpleDateFormat(SecurityUtils.getUserDateFormat() + " " + SecurityUtils.getUserTimeFormat());
                  } while(!logFileFolder.exists());

                  files = logFileFolder.listFiles();
               } while(files == null);
            } while(files.length == 0);

            for(int i = 0; i < files.length; ++i) {
               if (!files[i].getName().equalsIgnoreCase("key.txt") && (!"platform".equalsIgnoreCase(type) || !files[i].getName().contains("zip")) && ("platform".equalsIgnoreCase(type) || files[i].getName().contains("zip")) && ("platform".equalsIgnoreCase(type) || !StringUtils.isEmpty(fileName) && files[i].getName().equalsIgnoreCase(fileName))) {
                  V2DeviceLogFileResource resource;
                  Date today;
                  String modifiedDate;
                  if (logEntity.getStatus().equalsIgnoreCase("END")) {
                     resource = new V2DeviceLogFileResource();
                     today = new Date(files[i].lastModified());
                     modifiedDate = df.format(today);
                     resource.setFileName(files[i].getName());
                     resource.setModifiedDate(modifiedDate);
                     resource.setSize(files[i].length());
                     resource.setType(type);
                     if (!"platform".equalsIgnoreCase(type)) {
                        resource.setScript(logEntity.getCategory_script());
                     } else {
                        resource.setScript("");
                     }

                     list.add(resource);
                  } else if (files[i].length() >= maxLogFileSize) {
                     resource = new V2DeviceLogFileResource();
                     today = new Date(files[i].lastModified());
                     modifiedDate = df.format(today);
                     resource.setFileName(files[i].getName());
                     resource.setModifiedDate(modifiedDate);
                     resource.setSize(files[i].length());
                     resource.setType(type);
                     list.add(resource);
                  }
               }
            }
         }
      } catch (Exception var17) {
         this.logger.error(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN.getMessage(), var17);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN);
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceWarningRule getWarningRuleInfo(String deviceId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      V2DeviceWarningRule resource = new V2DeviceWarningRule();
      deviceId = StrUtils.nvl(deviceId).equals("") ? "" : deviceId;
      DeviceWarningRuleInfo ruleInfo = DeviceWarningRuleInfoImpl.getInstance();
      DeviceWarningRule warningrule = ruleInfo.getWarningRuleByDeviceId(deviceId);
      if (warningrule != null) {
         List thresholdList = ruleInfo.getWarningRuleElementList();
         List existThresholdList = warningrule.getThresholdList();

         for(int idx1 = 0; idx1 < thresholdList.size(); ++idx1) {
            for(int idx2 = 0; idx2 < existThresholdList.size(); ++idx2) {
               DeviceThreshold orgObj = (DeviceThreshold)thresholdList.get(idx1);
               if (((DeviceThreshold)thresholdList.get(idx1)).getElement_id().equalsIgnoreCase(((DeviceThreshold)existThresholdList.get(idx2)).getElement_id())) {
                  ((DeviceThreshold)thresholdList.get(idx1)).setElement_value(((DeviceThreshold)existThresholdList.get(idx2)).getElement_value());
                  break;
               }
            }
         }

         resource.setThresholds(thresholdList);
         resource.setRuleId(warningrule.getRule_id());
         resource.setRuleName(warningrule.getRule_name());
         resource.setDeviceType(warningrule.getDevice_type());
         resource.setOrganizationId(warningrule.getOrganization_id());
         resource.setOrganizationName(warningrule.getOrganization_name());
         resource.setRuleDescription(warningrule.getRule_description());
         resource.setCreateDate(warningrule.getCreate_date());
         resource.convertListMaptoObject(warningrule.getDevice_group_list(), "group");
         resource.setDeviceGroupIds(warningrule.getDevice_group_ids());
      }

      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2CommonUpdateResult getDeviceInfo(V2CommonIds deviceIds) throws Exception {
      boolean checkFlag = false;
      Iterator var3 = deviceIds.getIds().iterator();

      while(var3.hasNext()) {
         String deviceId = (String)var3.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var12) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      V2CommonUpdateResult resource = new V2CommonUpdateResult();
      List successList = new ArrayList();
      List failList = new ArrayList();
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Iterator var7 = deviceIds.getIds().iterator();

      while(var7.hasNext()) {
         String deviceId = (String)var7.next();

         try {
            Device device = deviceDao.getDevice(deviceId);
            if (device != null) {
               String deviceType = device.getDevice_type();
               if (!deviceType.equalsIgnoreCase("SPLAYER") && !deviceType.equalsIgnoreCase("LPLAYER") && !deviceType.equalsIgnoreCase("WPLAYER")) {
                  failList.add(deviceId);
               } else {
                  successList.add(deviceId);
               }
            } else {
               failList.add(deviceId);
            }
         } catch (Exception var11) {
            this.logger.error("", var11);
            failList.add(deviceId);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public V2DeviceReqServiceResource requestDeviceConversion(V2DeviceConversion body) throws Exception {
      boolean checkFlag = false;
      List deviceIds = body.getDeivceIds();
      String switchTo = body.getType();
      Iterator var5 = deviceIds.iterator();

      while(var5.hasNext()) {
         String deviceId = (String)var5.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var14) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      if (!"0".equals(switchTo) && !"1".equals(switchTo) && !"2".equals(switchTo)) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"The type of the body data"});
      } else {
         V2DeviceReqServiceResource resource = new V2DeviceReqServiceResource();
         List sucessList = new ArrayList();
         List failList = new ArrayList();
         String sessionId = UUID.randomUUID().toString();
         Iterator var9 = deviceIds.iterator();

         while(var9.hasNext()) {
            String deviceId = (String)var9.next();

            try {
               DeviceGeneralConf device = this.deviceDao.getDeviceTypeInfo(deviceId);
               if (device != null) {
                  String deviceType = device.getDevice_type();
                  if (!deviceType.equalsIgnoreCase("SPLAYER") && !deviceType.equalsIgnoreCase("SIGNAGE") && !deviceType.equalsIgnoreCase("LPLAYER") && !deviceType.equalsIgnoreCase("WPLAYER") && !deviceType.equalsIgnoreCase("LEDBOX")) {
                     failList.add(deviceId);
                  } else {
                     this.confManager.reqSetDeviceConnStatus(deviceId, ".MO.DEVICE_CONF.PROCESS.SWITCH_DEV_TYPE", switchTo, sessionId, 25L);
                     sucessList.add(deviceId);
                  }
               } else {
                  failList.add(deviceId);
               }
            } catch (Exception var13) {
               failList.add(deviceId);
            }
         }

         resource.setSuccessList(sucessList);
         resource.setFailList(failList);
         if (sucessList.size() > 0) {
            resource.setRequestId(sessionId);
         }

         return resource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public List getDeviceConversionResult(V2DeviceReqServiceConf body) throws Exception {
      boolean successFlag = false;
      List deviceIds = body.getDeviceIds();
      String sessionId = body.getRequestId();
      List resources = new ArrayList();
      V2CommonStatusResource resource;
      if (deviceIds != null && deviceIds.size() > 0) {
         for(Iterator var6 = deviceIds.iterator(); var6.hasNext(); resources.add(resource)) {
            String deviceId = (String)var6.next();
            resource = new V2CommonStatusResource();

            try {
               Map resultSet = this.confManager.getConnStatusResult(deviceId, sessionId, "SET_DEVICE_COMMON_CONF");
               if (resultSet != null && resultSet.get(".MO.DEVICE_CONF.PROCESS.SWITCH_DEV_TYPE") != null) {
                  resource.setId(deviceId);
                  resource.setStatus("success");
                  resource.setResponse((String)resultSet.get(".MO.DEVICE_CONF.PROCESS.SWITCH_DEV_TYPE"));
                  this.logger.info("[REST_v2.0][DEVICE SERVICE][getDeviceConversionResult] success to read ResultSet - " + deviceId);
                  successFlag = true;
               } else {
                  resource.setId(deviceId);
                  resource.setStatus("fail");
                  this.logger.info("[REST_v2.0][DEVICE SERVICE][getDeviceConversionResult] failed get ResultSet - " + deviceId);
               }
            } catch (Exception var10) {
               this.logger.error("[REST_v2.0][DEVICE SERVICE][getDeviceConversionResult] failed get connection status result", var10);
               resource.setId(deviceId);
               resource.setStatus("fail");
            }
         }
      }

      if (successFlag) {
         DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
         groupDao.updateCacheDeviceGroup();
      }

      return resources;
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public V2DeviceReqServiceResource quickControl(List deviceIds, String menu, String value, String productType) throws Exception {
      boolean flag = false;
      if (deviceIds.size() > 0) {
         Iterator var6 = deviceIds.iterator();

         while(var6.hasNext()) {
            String tempId = (String)var6.next();

            try {
               RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, tempId);
            } catch (Exception var25) {
               flag = true;
               break;
            }
         }
      } else {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, ((String)deviceIds.get(0)).toString());
      }

      if (flag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      DeviceInfo devMgr = DeviceInfoImpl.getInstance();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
      DeviceDisplayConf info = new DeviceDisplayConf();
      if (menu.equals("power")) {
         info.setBasic_power(value);
      } else if (menu.equals("panel")) {
         info.setBasic_panel_status(Long.parseLong(value));
      } else if (menu.equals("safety")) {
         info.setMnt_safety_lock(Long.parseLong(value));
      } else if (menu.equals("remote")) {
         info.setMisc_remocon(Long.parseLong(value));
      } else if (menu.equals("save_source")) {
         info.setBasic_source(Long.parseLong(value));
      } else if (menu.equals("save_sch_channel")) {
         info.setChkSchChannel(Long.parseLong(value));
      } else if (menu.equals("save_sound")) {
         info.setBasic_volume(Long.parseLong(value));
      } else if (menu.equals("save_mute")) {
         info.setBasic_mute(Long.parseLong(value));
      }

      V2DeviceReqServiceResource resource = new V2DeviceReqServiceResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      String requestId = UUID.randomUUID().toString();
      Iterator var29;
      String deviceId;
      if (menu.equals("restart")) {
         int successCount = false;
         var29 = deviceIds.iterator();

         while(var29.hasNext()) {
            deviceId = (String)var29.next();

            try {
               boolean result = false;
               JobManager jobMgr = JobManagerImpl.getInstance();
               int jobId = false;
               int jobId;
               if (productType.equals("PREMIUM")) {
                  jobId = SequenceDB.getNextValue("MI_DMS_INFO_JOB");
               } else {
                  jobId = SequenceDB.getNextValue("MI_DMS_INFO_LITE_JOB");
               }

               if (DeviceUtils.isConnected(deviceId)) {
                  JobEntity job = new JobEntity();
                  job.setJob_id((long)jobId);
                  job.setJob_name("restart_" + jobId);
                  job.setRepeat_type("immediately");
                  job.setUser_id(userContainer.getUser().getUser_id());
                  job.setJob_type("reboot");
                  job.setTarget("System");
                  job.setIs_canceled(false);
                  result = jobMgr.addPremiumJob(job, deviceId);
                  if (result) {
                     Device device = null;
                     device = devMgr.getDevice(deviceId);
                     jobMgr.deployRestartJobSchedule(String.valueOf(jobId), device);
                     motMgr.setDisconnected(deviceId);
                     successList.add(deviceId);
                  } else {
                     failList.add(deviceId);
                  }
               } else {
                  failList.add(deviceId);
               }
            } catch (Exception var22) {
               this.logger.error("[REST_v2.0][DEVICE SERVICE][quickControl] Restart request failed : " + deviceId, var22);
               failList.add(deviceId);
            }
         }

         resource.setSuccessList(successList);
         resource.setFailList(failList);
         return resource;
      } else {
         DeviceConfManager confManager;
         if (menu.equals("clean_storage")) {
            confManager = DeviceConfManagerImpl.getInstance();
            var29 = deviceIds.iterator();

            while(var29.hasNext()) {
               deviceId = (String)var29.next();

               try {
                  if (!DeviceUtils.isConnected(deviceId) && info.getBasic_power() == null) {
                     resource.setFailList(deviceIds);
                     this.logger.error("The device is not connected.");
                     return resource;
                  }

                  confManager.reqGetDevicePredefinedCmd(deviceId, "CLEANUP_STORAGE_DATA", requestId);
                  successList.add(deviceId);
               } catch (Exception var23) {
                  this.logger.error("[REST_v2.0][DEVICE SERVICE][quickControl] Clean storage request failed : " + deviceId, var23);
                  failList.add(deviceId);
               }
            }

            resource.setSuccessList(successList);
            resource.setFailList(failList);
            if (successList.size() > 0) {
               resource.setRequestId(requestId);
            }

            return resource;
         } else {
            confManager = DeviceConfManagerImpl.getInstance();
            WakeOnLan wol = new WakeOnLan();
            Iterator var16 = deviceIds.iterator();

            while(true) {
               while(true) {
                  String deviceId;
                  label148:
                  do {
                     while(var16.hasNext()) {
                        deviceId = (String)var16.next();
                        if (!DeviceUtils.isConnected(deviceId) && info.getBasic_power() == null) {
                           resource.setFailList(deviceIds);
                           this.logger.error("The device is not connected.");
                           return resource;
                        }

                        info.setDevice_id(deviceId);

                        try {
                           confManager.reqSetDisplayToDevice(info, requestId, "ALL_MDC");
                           successList.add(deviceId);
                           continue label148;
                        } catch (NullPointerException var24) {
                           this.logger.error("[REST_v2.0][DEVICE SERVICE][quickControl] This ID is not exist : " + deviceId);
                           failList.add(deviceId);
                        }
                     }

                     resource.setSuccessList(successList);
                     resource.setFailList(failList);
                     if (successList.size() > 0) {
                        resource.setRequestId(requestId);
                     }

                     return resource;
                  } while(info.getBasic_power() == null);

                  if (info.getBasic_power().equals("0")) {
                     DeviceUtils.setDisconnected(deviceId);
                     successList.add(deviceId);
                  } else if (info.getBasic_power().equals("1")) {
                     CurrentPlayingEntity cpe = motMgr.getPlayingContent(deviceId);
                     if (cpe == null || cpe != null && cpe.getInputSource() != 1000) {
                        Device device = devMgr.getDevice(deviceId);
                        wol.wol(device.getIp_address(), device.getSubnet_mask(), device.getDevice_id());
                     }

                     successList.add(deviceId);
                  }
               }
            }
         }
      }
   }

   private List getV2DownloadContentResources(String deviceId, String scheduleType) throws Exception {
      String sort_name = "content_name";
      String order_dir = "asc";
      int pageSize = 10000;
      int startIndex = 1;
      DeviceInfo dInfo = DeviceInfoImpl.getInstance();
      String programId = dInfo.getProgramIdByDeviceId(deviceId);
      Map condition = new HashMap();
      condition.put("device_id", deviceId);
      condition.put("program_id", programId);
      condition.put("sort", sort_name);
      condition.put("dir", order_dir);
      ScheduleInfoDAO schDao = new ScheduleInfoDAO();
      ProgramEntity programEntity = schDao.getProgram(programId);
      SimpleDateFormat sf;
      String event_schedule_id;
      if (programEntity != null) {
         sf = new SimpleDateFormat("yyyy-MM-dd");
         if (programEntity.getLastdeploy_date() != null) {
            event_schedule_id = sf.format(programEntity.getLastdeploy_date());
            condition.put("stop_date", event_schedule_id);
         }
      }

      condition.put("limit", Integer.valueOf(pageSize));
      sf = null;
      PagedListInfo info;
      if (!StrUtils.nvl(scheduleType).equals("event")) {
         info = schDao.getDownloadStatus(startIndex, pageSize, condition);
      } else {
         event_schedule_id = StrUtils.nvl(dInfo.getEventScheduleIdByDeviceId(deviceId));
         condition.put("event_schedule_id", event_schedule_id);
         info = schDao.getDownloadContentPagedListForEventSchedule(startIndex, pageSize, condition);
      }

      List list = info.getPagedResultList();
      List resources = new ArrayList();
      DownloadStatusInfo downloadStatusInfo = DownloadStatusInfoImpl.getInstacne();
      Map contentDownloads = downloadStatusInfo.getContentDownloadsByDeviceId(deviceId);

      for(int i = 0; i < list.size(); ++i) {
         DownloadContentEntity content = (DownloadContentEntity)list.get(i);
         V2DownloadContentResource resource = new V2DownloadContentResource();
         resource.setContentId(content.getContent_id());
         resource.setContentName(content.getContent_name());
         resource.setTotalSize(content.getTotal_size());
         if (contentDownloads != null && contentDownloads.get(content.getContent_id()) != null) {
            resource.setProgress((String)contentDownloads.get(content.getContent_id()));
         } else {
            resource.setProgress("-");
         }

         resource.setIsRuleset(content.getIs_ruleset());
         resources.add(resource);
      }

      return resources;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public ResponseBody getContentsDownloadStatus(String deviceId, String scheduleType) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      List resources = this.getV2DownloadContentResources(deviceId, scheduleType);
      ResponseBody responseBody = new ResponseBody();
      responseBody.setStartIndex(1);
      responseBody.setPageSize(resources.isEmpty() ? 10 : resources.size());
      responseBody.setTotalCount(resources.size());
      responseBody.setItems(resources);
      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Device Delete Authority')")
   public V2DeviceDeleteResource deleteDevice(List deviceIds, HttpServletRequest request, HttpServletResponse response) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      response.setContentType("application/json;charset=UTF-8");
      boolean checkFlag = false;
      Iterator var6 = deviceIds.iterator();

      while(var6.hasNext()) {
         String deviceId = (String)var6.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var23) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      MonitoringManager monMgr = MonitoringManagerImpl.getInstance();
      DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
      boolean flag = true;
      boolean isRedundancy = false;
      boolean isVwtDevice = false;
      V2DeviceDeleteResource resource = new V2DeviceDeleteResource();
      List successfulList = new ArrayList();
      List failedList = new ArrayList();
      Iterator var15 = deviceIds.iterator();

      while(var15.hasNext()) {
         String deviceId = (String)var15.next();
         boolean tempIsRedundancy = deviceDao.isRedundancyDevice(deviceId);
         isRedundancy = isRedundancy || tempIsRedundancy;
         boolean isRedundancyGroupTarget = false;

         try {
            isRedundancyGroupTarget = deviceGroupDao.isRedundancyGroup(Integer.parseInt(deviceDao.getDeviceGroupIdByDeviceId(deviceId)));
         } catch (Exception var22) {
            this.logger.error("", var22);
         }

         isRedundancy = isRedundancy || isRedundancyGroupTarget;
         Device device = deviceDao.getDevice(deviceId);
         if (device != null && device.getVwt_id() != null && !device.getVwt_id().equals("")) {
            isVwtDevice = true;
         }

         if (!isVwtDevice && !isRedundancy) {
            boolean e2eDelResult = true;
            boolean result = false;
            if (CommonConfig.get("e2e.enable") != null && CommonConfig.get("e2e.enable").equalsIgnoreCase("true")) {
               if (!device.getIs_approved()) {
                  result = deviceDao.deleteDevice(deviceId);
               } else {
                  e2eDelResult = this.e2eDeActAPICall(deviceId);
                  if (e2eDelResult) {
                     result = deviceDao.deleteDevice(deviceId);
                  } else {
                     result = false;
                  }
               }
            } else {
               result = deviceDao.deleteDevice(deviceId);
            }

            if (!result) {
               flag = false;
            } else {
               monMgr.connectionReload(deviceId, 0);
               monMgr.scheduleReload(deviceId, 0);
               monMgr.deleteConnectionInfo(deviceId);
               WSCall.setPlayerRequest(deviceId, "agent restart");
               DBCacheUtils.deletePreAssignedGroup(deviceId);
            }
         } else {
            flag = false;
         }

         V2DeletedDevice item = new V2DeletedDevice();
         item.setDeviceId(deviceId);
         if (isVwtDevice) {
            item.setReason(RestExceptionCode.BAD_REQUEST_CANNOT_DELETE_VWL_DEVICE.getMessage());
            item.setReasonCode(RestExceptionCode.BAD_REQUEST_CANNOT_DELETE_VWL_DEVICE.getCode());
         } else if (isRedundancy) {
            item.setReason(RestExceptionCode.BAD_REQUEST_CANNOT_DELETE_REDUNDANCY_DEVICE.getMessage());
            item.setReasonCode(RestExceptionCode.BAD_REQUEST_CANNOT_DELETE_REDUNDANCY_DEVICE.getCode());
         }

         if (flag) {
            deviceGroupDao.updateCacheDeviceGroup();
            item.setResult("device_delete_success");
            successfulList.add(item);
         } else {
            item.setResult("device_delete_fail");
            failedList.add(item);
         }
      }

      resource.setDeviceIds(deviceIds);
      resource.setSuccessList(successfulList);
      resource.setFailList(failedList);
      return resource;
   }

   public void getDetailPublishStatus(int mode, String strProgramId, String strDeviceGroupId, StringBuffer buffer, Map publishStatusMap) throws NumberFormatException, SQLException {
      String published = "published";
      String publishing = "publishing";
      String waiting = "waiting";
      String failed = "failed";
      int total = 0;
      int now = 0;
      DeviceGroupInfo deviceGroupDao = null;
      deviceGroupDao = DeviceGroupInfoImpl.getInstance();
      ScheduleInfoDAO dao = new ScheduleInfoDAO();
      String[] groupIdList = strDeviceGroupId.split(",");
      String[] var15 = groupIdList;
      int var16 = groupIdList.length;

      for(int var17 = 0; var17 < var16; ++var17) {
         String groupId = var15[var17];
         if (groupId != null && groupId.length() > 0) {
            total += deviceGroupDao.getCntDeviceInDeviceGroup(Integer.parseInt(groupId));
            List list = dao.getScheduleDetailPublishStatusList(strProgramId, Long.parseLong(groupId));
            if (list != null && list.size() > 0) {
               int listSize = list.size();
               String oldDeviceId = ((DetailDownloadContentEntity)list.get(0)).getDevice_id();
               if (oldDeviceId != null) {
                  float deviceTotal = 0.0F;
                  float deviceNow = 0.0F;
                  int jj = false;

                  for(int jj = 0; jj < listSize; ++jj) {
                     DetailDownloadContentEntity detailDownloadContentEntity = (DetailDownloadContentEntity)list.get(jj);
                     if (detailDownloadContentEntity != null) {
                        if (detailDownloadContentEntity.getDevice_id() != null && !oldDeviceId.equals(detailDownloadContentEntity.getDevice_id())) {
                           if (deviceTotal > 0.0F && deviceTotal == deviceNow) {
                              ++now;
                           }

                           oldDeviceId = detailDownloadContentEntity.getDevice_id();
                           deviceTotal = 0.0F;
                           deviceNow = 0.0F;
                        }

                        if (detailDownloadContentEntity.getProgress() != null && detailDownloadContentEntity.getProgress().equals("100 %")) {
                           ++deviceNow;
                        }

                        ++deviceTotal;
                     }
                  }

                  if (mode == 2 && deviceTotal > 0.0F && deviceTotal == deviceNow) {
                     ++now;
                  }
               }
            } else {
               now = total;
            }
         }
      }

      if (mode == 2) {
         String status = "";
         if (total == now) {
            status = published;
         } else if (now == 0) {
            ScheduleInfoDAO scheduleDao = new ScheduleInfoDAO();
            if (scheduleDao.getCheckContentPublishCount(strProgramId) > 0) {
               status = publishing;
            } else {
               status = waiting;
            }
         } else {
            status = publishing;
         }

         publishStatusMap.put("status", status);
         publishStatusMap.put("now", now + "");
         publishStatusMap.put("total", total + "");
      }

   }

   private V2DeviceListResource writeData(HttpServletRequest request, String productType, DeviceGeneralConf device) throws Exception {
      V2DeviceListResource resource = new V2DeviceListResource();
      ObjectMapper mapper = new ObjectMapper();
      mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
      mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
      mapper.configure(DeserializationFeature.FAIL_ON_MISSING_CREATOR_PROPERTIES, false);

      try {
         String jsonString = mapper.writeValueAsString(device);

         try {
            Map map = (Map)mapper.readValue(jsonString, new TypeReference() {
            });
            Map map = ConvertUtil.convertMap(map);
            resource = (V2DeviceListResource)mapper.convertValue(map, V2DeviceListResource.class);
            boolean power = DeviceUtils.isConnected(device.getDevice_id());
            if (power && device.getIs_child() && !DeviceUtils.isConnected(device.getDevice_id().split("_")[0])) {
               power = false;
            }

            resource.setPower(power);
            resource.setLastConnectionTime(device.getLast_connection_time());
            int basicSource = 32;
            String captureUrl = request.getContextPath() + "/image/img/thumb_img_power.png";
            String thumbUrl = request.getContextPath() + "/image/img/thumb_img_power.png";
            CurrentPlayingEntity playingEntity = DeviceUtils.getPlayingContent(device.getDevice_id());
            String scheduleName = "";
            String contentName = "";
            boolean panelStatus = false;
            String contentId = "";
            boolean eventMode = false;
            if (playingEntity != null && power) {
               HashMap resultMap = DeviceUtils.getV2DeviceStatusInfos(request.getContextPath(), device, true, playingEntity, power);
               basicSource = playingEntity.getInputSource();
               captureUrl = (String)resultMap.get("captureUrl");
               thumbUrl = (String)resultMap.get("thumbUrl");
               if (playingEntity.getPanelStatus() == 0L) {
                  panelStatus = true;
               }
            }

            resource.setPanelStatus(panelStatus);
            resource.setCaptureUrl(captureUrl);
            resource.setThumbUrl(thumbUrl);
            resource.setBasicSource(basicSource);
            if (device.getChild_cnt() != null) {
               resource.setChildCount(device.getChild_cnt());
            } else {
               resource.setChildCount(0L);
            }

            if (productType.equals("PREMIUM")) {
               resource.setIsChild(device.getIs_child().toString());
            }

            if (!"iPLAYER".equalsIgnoreCase(device.getDevice_type()) && !"APLAYER".equalsIgnoreCase(device.getDevice_type())) {
               if ("WPLAYER".equalsIgnoreCase(device.getDevice_type())) {
                  resource.setFirmwareVersion(StrUtils.nvl(device.getApplication_version()));
                  resource.setPlayerVersion(StrUtils.nvl(device.getPlayer_version()));
               } else {
                  resource.setFirmwareVersion(StrUtils.nvl(device.getApplication_version()));
                  resource.setPlayerVersion(StrUtils.nvl(device.getFirmware_version()));
               }
            } else {
               resource.setFirmwareVersion(StrUtils.nvl(device.getFirmware_version()));
               resource.setPlayerVersion(StrUtils.nvl(device.getApplication_version()));
            }

            if (DeviceUtils.isConnected(device.getDevice_id()) && device.getScreen_rotation() != null && device.getScreen_rotation() != 0L) {
               resource.setLandscape("portrait");
            } else {
               resource.setLandscape("landscape");
            }

            Device devicePre = this.deviceDao.getMonitoringViewDevice(device.getDevice_id());
            String preconfigVersoin = null;
            if (devicePre != null) {
               preconfigVersoin = devicePre.getPre_config_version();
            }

            resource.setPreconfig(preconfigVersoin);
            String[] deviceIds = new String[]{device.getDevice_id()};
            List devMediaTags = this.deviceDao.getDeviceAndTagListByDeviceIds(deviceIds);
            List tempTag = new ArrayList();
            if (devMediaTags != null && devMediaTags.size() > 0 && ((DeviceTag)devMediaTags.get(0)).getTag_id() != null) {
               for(int i = 0; i < devMediaTags.size(); ++i) {
                  V2DeviceTag tempObject = new V2DeviceTag();

                  try {
                     tempTag.add((V2DeviceTag)ConvertUtil.convertObject(devMediaTags.get(i), tempObject));
                  } catch (IllegalArgumentException var35) {
                     this.logger.error("", var35);
                  } catch (IllegalAccessException var36) {
                     this.logger.error("", var36);
                  }
               }

               resource.setMediaTagValueList(tempTag);
            }

            tempTag = new ArrayList();
            List devVarTags = this.deviceDao.getDeviceAndTagListByDeviceIds(deviceIds, true);
            if (devVarTags != null && devVarTags.size() > 0 && ((DeviceTag)devVarTags.get(0)).getTag_id() != null) {
               for(int i = 0; i < devVarTags.size(); ++i) {
                  V2DeviceTag tempObject = new V2DeviceTag();

                  try {
                     tempTag.add((V2DeviceTag)ConvertUtil.convertObject(devVarTags.get(i), tempObject));
                  } catch (IllegalArgumentException var33) {
                     this.logger.error("", var33);
                  } catch (IllegalAccessException var34) {
                     this.logger.error("", var34);
                  }
               }

               resource.setVarTagValueList(tempTag);
            }

            boolean canPlayMDC = true;
            String strOn = "ON";
            String strConnect = "CONNECTED";
            List etcDeviceList = null;

            try {
               etcDeviceList = this.deviceDao.selectExtDeviceInfo(device.getDevice_id(), "MDC");
            } catch (SQLException var32) {
               etcDeviceList = null;
            }

            if (etcDeviceList != null && etcDeviceList.size() > 0) {
               resource.setIsLoopOut(true);
               List sourceList = new ArrayList();

               int k;
               for(k = 0; k < etcDeviceList.size(); ++k) {
                  new DeviceLoopOutEntity();
                  DeviceLoopOutEntity LoopOutEntity = (DeviceLoopOutEntity)etcDeviceList.get(k);
                  if (LoopOutEntity.getSerial_connection_status() != null && !LoopOutEntity.getSerial_connection_status().equalsIgnoreCase(strConnect)) {
                     canPlayMDC = false;
                     break;
                  }

                  if (LoopOutEntity.getPower_status() != null && !LoopOutEntity.getPower_status().equalsIgnoreCase(strOn)) {
                     canPlayMDC = false;
                     break;
                  }

                  if (LoopOutEntity.getPannel_status() != null && !LoopOutEntity.getPannel_status().equalsIgnoreCase(strOn)) {
                     canPlayMDC = false;
                     break;
                  }

                  if (!LoopOutEntity.getInputSource_status().equalsIgnoreCase("MAGICINFO")) {
                     sourceList.add(LoopOutEntity.getInputSource_status());
                  }
               }

               if (canPlayMDC) {
                  if (sourceList != null && sourceList.size() == etcDeviceList.size() - 1) {
                     for(k = 0; k < sourceList.size(); ++k) {
                        String target = (String)sourceList.get(k);

                        for(int j = 0; j < sourceList.size(); ++j) {
                           if (!target.equalsIgnoreCase((String)sourceList.get(j))) {
                              canPlayMDC = false;
                           }
                        }
                     }
                  } else {
                     canPlayMDC = false;
                  }
               }

               resource.setCanPlayMDC(canPlayMDC);
            } else {
               resource.setIsLoopOut(false);
               resource.setCanPlayMDC(false);
            }

            resource.setTunnelingServer(device.getTunneling_server());
            DeviceSecurityConfManager securityDao = DeviceSecurityConfManagerImpl.getInstance("PREMIUM");
            DeviceSecurityConf deviceSecurityConf = securityDao.getDeviceSecurityConf(device.getDevice_id());
            if (null != deviceSecurityConf.getScreen_monitoring_lock()) {
               resource.setScreenMonitoringLock(deviceSecurityConf.getScreen_monitoring_lock());
               resource.setRemoteControlServerLock(deviceSecurityConf.getRemote_control_server_lock() == null ? 0L : deviceSecurityConf.getRemote_control_server_lock());
            }
         } catch (IOException var37) {
            this.logger.error("", var37);
         }
      } catch (JsonProcessingException var38) {
         this.logger.error("", var38);
      }

      return resource;
   }

   public String getLoginUserId() {
      return SecurityUtils.getUserContainer() == null ? "" : SecurityUtils.getUserContainer().getUser().getUser_id();
   }

   public String convertString(List list) {
      String result = "";
      if (!list.isEmpty() && list.size() > 0) {
         StringBuffer strBuf = new StringBuffer();

         for(int i = 0; i < list.size(); ++i) {
            strBuf.append(list.get(i));
            if (i < list.size() - 1) {
               strBuf.append(",");
            }
         }

         result = strBuf.toString();
      }

      return result;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2PageResource getDeviceLedCabinets(String deviceId, Long groupId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      LedCabinetConfManager ledInfo = LedCabinetConfManagerImpl.getInstance();
      Map condition = new HashMap();
      condition.put("device_id", deviceId);
      condition.put("sort_name", "");
      condition.put("order_dir", "desc");
      condition.put("group_id", groupId);
      List ledCabinetList = ledInfo.getLedCabinetPagedList(-1, -1, condition);
      int listSize = ledInfo.getLedCabinetCount(deviceId);
      List list = V2DeviceLedCabinetFactory.v2DeviceLedCabinetWithGroupResource(ledCabinetList);
      boolean parentIsConnected = DeviceUtils.isConnected(deviceId);
      V2DeviceLedCabinetWithGroupResource ledCabinetResource = null;

      for(int i = 0; i < list.size(); ++i) {
         ledCabinetResource = (V2DeviceLedCabinetWithGroupResource)list.get(i);
         List ledList = ledCabinetResource.getCabinets();

         for(int j = 0; j < ledList.size(); ++j) {
            V2DeviceLedCabinetResource ledCabinet = (V2DeviceLedCabinetResource)ledList.get(j);
            if (!parentIsConnected) {
               ledCabinet.setPower("0");
            }

            if (ledCabinet.getIsLODError() != null && ledCabinet.getIsLODError().equalsIgnoreCase("ERROR") || ledCabinet.getIsTemperatureError() != null && ledCabinet.getIsTemperatureError().equalsIgnoreCase("ERROR") || ledCabinet.getIsVoltageError() != null && ledCabinet.getIsVoltageError().equalsIgnoreCase("ERROR") || ledCabinet.getIsICError() != null && ledCabinet.getIsICError().equalsIgnoreCase("ERROR")) {
               ledCabinet.setWarningLevel("ERROR");
            } else {
               ledCabinet.setWarningLevel("");
            }

            if (ledCabinet.getPower() == null || ledCabinet.getPower().equals("0")) {
               ledCabinet.setPower("OFF");
            }
         }

         if (ledCabinetResource.getGroupId() != null) {
            ledCabinetResource.setCabinetGroupStatus(String.valueOf(ledInfo.getErrorLedCabinetCntByGroupId(deviceId, ledCabinetResource.getGroupId())));
         }

         ledCabinetResource.setCabinets(ledList);
      }

      V2PageResource resource = V2PageResource.createPageResource(list, list.size());
      resource.setStartIndex(1);
      resource.setRecordsTotal(listSize);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2DeviceExternalPowerResource getDeviceExternalPower(String deviceId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      DeviceSboxConfManager sboxManager = DeviceSboxConfManagerImpl.getInstance();
      V2DeviceExternalPowerResource deviceExternalPowerResource = sboxManager.getExternalPower(deviceId);
      return deviceExternalPowerResource;
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public V2DeviceReqServiceResource reqExternalPowerMdc(String deviceId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      String value = null;
      V2DeviceReqServiceResource resource = new V2DeviceReqServiceResource();
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      String sessionId = UUID.randomUUID().toString();
      List successList = new ArrayList();
      List failList = new ArrayList();
      boolean parentIsConnected = DeviceUtils.isConnected(deviceId);
      if (!parentIsConnected) {
         failList.add(deviceId);
         resource.setFailList(failList);
         return resource;
      } else {
         try {
            confMgr.reqSboxChildMonitoringNotifyFromDevice(deviceId, sessionId);
            successList.add(deviceId);
         } catch (Exception var10) {
            this.logger.error("", var10);
            failList.add(deviceId);
         }

         resource.setSuccessList(successList);
         resource.setFailList(failList);
         resource.setRequestId(sessionId);
         return resource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public List getDeviceLedCabinetsDetails(String deviceId, List cabinetIds) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      new HashMap();
      LedCabinetConfManager ledCabinetMgr = LedCabinetConfManagerImpl.getInstance();
      new ArrayList();
      List ids = (List)Optional.ofNullable(cabinetIds).orElse(new ArrayList());
      List ledCabinets;
      if (ids.isEmpty()) {
         ledCabinets = ledCabinetMgr.getLedCabinetList(deviceId);
      } else {
         ledCabinets = ledCabinetMgr.getLedCabinetList(deviceId, cabinetIds);
      }

      return (List)ledCabinets.stream().map(V2DeviceLedCabinetFactory::v2DeviceLedCabinetResourceBuilder).collect(Collectors.toList());
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public List updateDeviceCaninetInfo(String deviceId, List cabinetIds, V2DeviceLedCabinetResource resource, String sessionId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
      new DeviceDisplayConf();
      DeviceDisplayConfManager displayConfMgr = DeviceDisplayConfManagerImpl.getInstance();
      boolean sendSetMo = false;
      LedCabinet ledCabinet = V2DeviceLedCabinetFactory.getLedCabinet4UpdateCabinetInfo(resource);
      List res = new ArrayList();
      if (ledCabinet.getAbl() != null || ledCabinet.getInput_source() != null || ledCabinet.getGamut() != null || ledCabinet.getBacklight() != null || ledCabinet.getOn_screen_display() != null || ledCabinet.getPixel_rgb_cc() != null || ledCabinet.getModule_rgb_cc() != null || ledCabinet.getEdge_correction() != null) {
         List ids = (List)Optional.ofNullable(cabinetIds).orElse(new ArrayList());
         if (ids.isEmpty()) {
            ledCabinet.setParent_device_id(deviceId);
            confManager.reqSetCabinetConfToDevice(ledCabinet, (List)null, sessionId, "CABINET_MDC");
         } else {
            ledCabinet.setParent_device_id(deviceId);
            Map idsMap = (Map)ids.stream().collect(Collectors.groupingBy((id) -> {
               return id.split("-")[0];
            }));
            Iterator var13 = idsMap.entrySet().iterator();

            while(var13.hasNext()) {
               Entry e = (Entry)var13.next();
               List childIds = (List)e.getValue();
               confManager.reqSetCabinetConfToDevice(ledCabinet, childIds, sessionId, "CABINET_MDC");
            }
         }

         LedCabinetConfManager ledCabinetMgr = LedCabinetConfManagerImpl.getInstance();
         new ArrayList();
         List ledCabinets;
         if (ids.isEmpty()) {
            ledCabinets = ledCabinetMgr.getLedCabinetList(deviceId);
         } else {
            ledCabinets = ledCabinetMgr.getLedCabinetList(deviceId, cabinetIds);
         }

         res = (List)ledCabinets.stream().map(V2DeviceLedCabinetFactory::v2DeviceLedCabinetResourceBuilder).collect(Collectors.toList());
      }

      return (List)res;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public void scanDeviceCabinets(String deviceId, List scanInfos) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      List ips = new ArrayList();
      List autos = new ArrayList();
      scanInfos.stream().forEach((sx) -> {
         if (!((String)Optional.ofNullable(sx.getCabinetIPAddress()).orElse("")).equals("")) {
            ips.add(sx.getCabinetIPAddress());
         }

         if (!((String)Optional.ofNullable(sx.getAutoSetId()).orElse("")).equals("")) {
            autos.add(sx.getAutoSetId());
         }

      });
      if (scanInfos.size() > 0) {
         long[] sl = new long[scanInfos.size()];
         int i = 0;

         V2DeviceCabinetScanInfo s;
         for(Iterator var7 = scanInfos.iterator(); var7.hasNext(); sl[i++] = s.getChildNumber()) {
            s = (V2DeviceCabinetScanInfo)var7.next();
         }

         String[] cabinetIPArr = (String[])ips.toArray(new String[0]);
         String[] autoSetIDArr = (String[])autos.toArray(new String[0]);
         DeviceUtils.scanChildDevice(deviceId, sl, cabinetIPArr, autoSetIDArr);
      }

   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public void sendCommandOnCabinets(String deviceId, V2DeviceCabinetSendCmdWrapper wrapper) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      String command = wrapper.getCommand();
      String targets = null;
      if (null != wrapper.getCabinets()) {
         targets = (String)wrapper.getCabinets().stream().map((s) -> {
            return deviceId + "_" + s;
         }).collect(Collectors.joining(";"));
      }

      DeviceUtils.sendChildCmd(deviceId, command, targets);
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public void sendCommandToSbox(String deviceId, String cmd, String value) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      String sessionId = UUID.randomUUID().toString();
      DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
      confManager.reqSetSboxCmd(deviceId, (List)null, cmd, value, sessionId);
   }

   public V2DeviceStatusResource getDeviceListByUserId() throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      new LinkedHashMap();
      new LinkedHashMap();
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      V2DeviceStatusResource resource = new V2DeviceStatusResource();
      List deviceList = new ArrayList();
      boolean showAllDeviceType = false;
      if (CommonConfig.get("show.allDeviceType.enable") != null && CommonConfig.get("show.allDeviceType.enable").equalsIgnoreCase("true")) {
         showAllDeviceType = true;
      }

      try {
         String[] var9;
         int var10;
         int var11;
         String deviceType;
         if (!showAllDeviceType) {
            String organization = userContainer.getUser().getOrganization();
            var9 = CommonDataConstants.ALL_DEVICE_ARRAY;
            var10 = var9.length;

            for(var11 = 0; var11 < var10; ++var11) {
               deviceType = var9[var11];
               if (deviceInfo.getAllDeviceCountByOrganization(deviceType, organization) > 0) {
                  deviceList.add(deviceType);
               }
            }

            if (deviceInfo.getAllDeviceCountByOrganization("RSPLAYER4", userContainer.getUser().getOrganization()) > 0) {
               deviceList.add("RSPLAYER4");
            }

            if (deviceInfo.getAllDeviceCountByOrganization("RSPLAYER5", userContainer.getUser().getOrganization()) > 0) {
               deviceList.add("RSPLAYER5");
            }

            if (deviceInfo.getAllDeviceCountByOrganization("RSPLAYER6", userContainer.getUser().getOrganization()) > 0) {
               deviceList.add("RSPLAYER6");
            }

            if (deviceInfo.getAllDeviceCountByOrganization("RSPLAYER7", userContainer.getUser().getOrganization()) > 0) {
               deviceList.add("RSPLAYER7");
            }

            if (deviceInfo.getAllDeviceCountByOrganization("RSPLAYER9", userContainer.getUser().getOrganization()) > 0) {
               deviceList.add("RSPLAYER9");
            }

            if (deviceInfo.getAllDeviceCountByOrganization("RSPLAYER10", userContainer.getUser().getOrganization()) > 0) {
               deviceList.add("RSPLAYER10");
            }
         } else {
            var9 = CommonDataConstants.ALL_DEVICE_ARRAY;
            var10 = var9.length;

            for(var11 = 0; var11 < var10; ++var11) {
               deviceType = var9[var11];
               deviceList.add(deviceType);
            }

            deviceList.add("RSPLAYER4");
            deviceList.add("RSPLAYER5");
            deviceList.add("RSPLAYER6");
            deviceList.add("RSPLAYER7");
            deviceList.add("RSPLAYER9");
            deviceList.add("RSPLAYER10");
         }

         resource.setDetDeviceList(this.convertDeviceType(deviceList));
      } catch (Exception var13) {
         this.logger.error("[REST_v2.0][DEVICE SERVICE][getDeviceListByUserId]", var13);
      }

      return resource;
   }

   private List convertDeviceType(List deviceList) {
      List typeList = new ArrayList();

      for(int i = 0; i < deviceList.size(); ++i) {
         V2DeviceTypeResource typeResource = new V2DeviceTypeResource();
         String typeStr = (String)deviceList.get(i);
         typeResource.setDeviceFullType(typeStr);
         String temp;
         if (typeStr.equals("iPLAYER")) {
            typeResource.setDeviceTypeVersion(CommonDataConstants.TYPE_VERSION_2_0);
         } else {
            temp = typeStr.replaceAll("[^0-9]", "");
            if (temp.matches(".*[0-9].*")) {
               typeResource.setDeviceTypeVersion(Float.valueOf(temp));
            } else {
               typeResource.setDeviceTypeVersion(CommonDataConstants.TYPE_VERSION_1_0);
            }
         }

         temp = typeStr.replaceAll("[0-9]", "");
         typeResource.setDeviceType(temp);
         typeList.add(typeResource);
      }

      return typeList;
   }

   private V2DevicePresetResource convertDeviceControl(DeviceControl control) {
      new DeviceDisplayConf();
      new DeviceTimeConf();
      new DeviceSystemSetupConf();
      new DeviceSecurityConf();
      new ArrayList();
      List v2ServiceList = new ArrayList();
      V2DevicePresetResource resource = new V2DevicePresetResource();
      DeviceDisplayConf deviceDisplayConf = control.getDisplay();
      if (deviceDisplayConf == null) {
         deviceDisplayConf = new DeviceDisplayConf();
      }

      V2DeviceDisplayConf v2DeviceDisplayConf = new V2DeviceDisplayConf(deviceDisplayConf);
      DeviceTimeConf timeConf = control.getTime();
      if (timeConf == null) {
         timeConf = new DeviceTimeConf();
      }

      V2DeviceTimeConf v2TimeConf = new V2DeviceTimeConf(timeConf);
      DeviceSystemSetupConf setupConf = control.getSetup();
      if (setupConf == null) {
         setupConf = new DeviceSystemSetupConf();
      }

      V2DeviceSystemSetupConf v2setupConf = new V2DeviceSystemSetupConf(setupConf);
      DeviceSecurityConf securityConf = control.getSecurity();
      if (securityConf == null) {
         securityConf = new DeviceSecurityConf();
      }

      V2DeviceSecurityConf v2SecurityConf = new V2DeviceSecurityConf(securityConf);
      List serviceList = control.getServiceList();
      if (serviceList == null) {
         serviceList = new ArrayList();
      }

      Iterator var13 = ((List)serviceList).iterator();

      while(var13.hasNext()) {
         DeviceServiceConf deviceServiceConf = (DeviceServiceConf)var13.next();
         V2DeviceServiceConf v2DeviceServiceConf = new V2DeviceServiceConf(deviceServiceConf);
         v2ServiceList.add(v2DeviceServiceConf);
      }

      resource.setDisplay(v2DeviceDisplayConf);
      resource.setTime(v2TimeConf);
      resource.setSetup(v2setupConf);
      resource.setSecurity(v2SecurityConf);
      resource.setDeviceServiceConfs(v2ServiceList);
      return resource;
   }

   private V2DevicePreconfig convertPreconfig(DevicePreconfig preconfig) {
      V2DevicePreconfig v2DevicePreconfig = new V2DevicePreconfig();
      List v2ServiceList = new ArrayList();
      V2DevicePresetConfig v2DeviceControl = new V2DevicePresetConfig();
      V2DeviceGeneralConf v2DeviceGeneralConf = new V2DeviceGeneralConf();
      if (preconfig.getDevice_service_confs() != null) {
         Iterator var6 = preconfig.getDevice_service_confs().iterator();

         while(var6.hasNext()) {
            DeviceServiceConf deviceServiceConf = (DeviceServiceConf)var6.next();
            V2DeviceServiceConf v2DeviceServiceConf = new V2DeviceServiceConf(deviceServiceConf);
            v2ServiceList.add(v2DeviceServiceConf);
         }
      }

      new DeviceGeneralConf();
      if (preconfig.getDevice_control() != null && preconfig.getDevice_control().getGeneral() != null) {
         DeviceGeneralConf deviceGeneralConf = preconfig.getDevice_control().getGeneral();
         v2DeviceGeneralConf = new V2DeviceGeneralConf(deviceGeneralConf);
      }

      new ArrayList();
      List v2SoftwareList = new ArrayList();
      if (preconfig.getDevice_control() != null && preconfig.getDevice_control().getSoftwareList() != null) {
         List softwareList = preconfig.getDevice_control().getSoftwareList();
         Iterator var9 = softwareList.iterator();

         while(var9.hasNext()) {
            DeviceSoftwareConf deviceSoftwareConf = (DeviceSoftwareConf)var9.next();
            V2DeviceSoftwareConf v2DeviceSoftwareConf = new V2DeviceSoftwareConf(deviceSoftwareConf);
            v2SoftwareList.add(v2DeviceSoftwareConf);
         }
      }

      V2DeviceSystemSetupConf v2setupConf = new V2DeviceSystemSetupConf();
      new DeviceSystemSetupConf();
      if (preconfig.getDevice_control() != null && preconfig.getDevice_control().getSetup() != null) {
         DeviceSystemSetupConf setupConf = preconfig.getDevice_control().getSetup();
         v2setupConf = new V2DeviceSystemSetupConf(setupConf);
      }

      V2DeviceDisplayConf v2DeviceDisplayConf = new V2DeviceDisplayConf();
      new DeviceDisplayConf();
      if (preconfig.getDevice_control() != null && preconfig.getDevice_control().getDisplay() != null) {
         DeviceDisplayConf deviceDisplayConf = preconfig.getDevice_control().getDisplay();
         v2DeviceDisplayConf = new V2DeviceDisplayConf(deviceDisplayConf);
      }

      new DeviceTimeConf();
      V2DeviceTimeConf v2TimeConf = new V2DeviceTimeConf();
      if (preconfig.getDevice_control() != null && preconfig.getDevice_control().getTime() != null) {
         DeviceTimeConf timeConf = preconfig.getDevice_control().getTime();
         v2TimeConf = new V2DeviceTimeConf(timeConf);
      }

      new DeviceSecurityConf();
      V2DeviceSecurityConf v2SecurityConf = new V2DeviceSecurityConf();
      if (preconfig.getDevice_control() != null && preconfig.getDevice_control().getSecurity() != null) {
         DeviceSecurityConf securityConf = preconfig.getDevice_control().getSecurity();
         v2SecurityConf = new V2DeviceSecurityConf(securityConf);
      }

      if (preconfig.getDevice_control() != null) {
         v2DeviceControl.setTargetId(preconfig.getDevice_control().getTargetId());
         Map severMap = preconfig.getDevice_control().getServer();
         if (severMap != null) {
            V2DeviceServiceConfig severConf = new V2DeviceServiceConfig();
            severConf.setFirmwareDownload((String)severMap.get("firmware_download"));
            severConf.setContentsDownload((String)severMap.get("contents_download"));
            severConf.setPopUpload((String)severMap.get("pop_upload"));
            severConf.setScreenCapture((String)severMap.get("screen_capture_upload"));
            v2DeviceControl.setServer(severConf);
         }

         V2DeviceCustomConf deviceCustomConf = new V2DeviceCustomConf();
         Map softwareMap = preconfig.getDevice_control().getSoftware();
         if (softwareMap != null) {
            deviceCustomConf.setDefaultContent((String)softwareMap.get("software_default_content"));
            deviceCustomConf.setCustomLogo((String)softwareMap.get("software_custom_logo"));
            v2DeviceControl.setCustom(deviceCustomConf);
         }
      }

      v2DeviceControl.setGeneral(v2DeviceGeneralConf);
      v2DeviceControl.setSetup(v2setupConf);
      v2DeviceControl.setDisplay(v2DeviceDisplayConf);
      v2DeviceControl.setTime(v2TimeConf);
      v2DeviceControl.setSecurity(v2SecurityConf);
      v2DeviceControl.setServiceList(v2ServiceList);
      v2DeviceControl.setSoftwareList(v2SoftwareList);
      if (preconfig.getDevice_control() != null) {
         v2DevicePreconfig.setPresetId(preconfig.getPreconfig_id());
         v2DevicePreconfig.setName(preconfig.getName());
         v2DevicePreconfig.setVersion(preconfig.getVersion());
         v2DevicePreconfig.setDescription(preconfig.getDescription());
         v2DevicePreconfig.setCreateDate(preconfig.getCreate_date());
         v2DevicePreconfig.setUpdateTime(preconfig.getUpdate_time());
         v2DevicePreconfig.setOrganizationId(preconfig.getOrganization_id());
         v2DevicePreconfig.setOrganizationName(preconfig.getOrganization_name());
         v2DevicePreconfig.setDeviceGroupIds(preconfig.getDevice_group_ids());
         v2DevicePreconfig.setCompletedCount(preconfig.getCompleted_count());
         v2DevicePreconfig.setTotalCount(preconfig.getTotal_count());
      }

      v2DevicePreconfig.setDeviceControl(v2DeviceControl);
      v2DevicePreconfig.setDeviceServiceConfs(v2ServiceList);
      return v2DevicePreconfig;
   }

   private DevicePreconfig convertPreconfig(V2DevicePreconfigData preconfigData, String presetId) {
      DevicePreconfig preconfig = new DevicePreconfig();
      List serviceList = new ArrayList();
      DeviceControl deviceControl = new DeviceControl();
      DeviceGeneralConf deviceGeneralConf = new DeviceGeneralConf();
      DevicePreconfig originPreconfig = new DevicePreconfig();
      if (presetId != null) {
         try {
            originPreconfig = this.preconfigInfo.getPreconfigInfo(presetId);
         } catch (SQLException var24) {
            this.logger.error("", var24);
         }
      }

      if (preconfigData.getDeviceServiceConfs() != null) {
         Iterator var8 = preconfigData.getDeviceServiceConfs().iterator();

         while(var8.hasNext()) {
            V2DeviceServiceConf v2DeviceServiceConf = (V2DeviceServiceConf)var8.next();
            DeviceServiceConf deviceServiceConf = new DeviceServiceConf();
            deviceServiceConf.setPreconfig_id(v2DeviceServiceConf.getPresetId());
            deviceServiceConf.setService_type(v2DeviceServiceConf.getServiceType());
            deviceServiceConf.setHost(v2DeviceServiceConf.getHost());
            deviceServiceConf.setPath(v2DeviceServiceConf.getPath());
            deviceServiceConf.setPort(v2DeviceServiceConf.getPort());
            deviceServiceConf.setProtocol(v2DeviceServiceConf.getProtocol());
            ((List)serviceList).add(deviceServiceConf);
         }
      } else if (originPreconfig.getDevice_control() != null && originPreconfig.getDevice_control().getServiceList() != null) {
         serviceList = originPreconfig.getDevice_control().getServiceList();
      } else {
         serviceList = originPreconfig.getDevice_service_confs();
      }

      new V2DeviceGeneralConf();
      if (preconfigData.getDeviceControl() != null) {
         if (preconfigData.getDeviceControl().getGeneral() != null) {
            V2DeviceGeneralConf v2DeviceGeneralConf = preconfigData.getDeviceControl().getGeneral();
            deviceGeneralConf.setDevice_id(v2DeviceGeneralConf.getDeviceId());
            deviceGeneralConf.setGroup_id(v2DeviceGeneralConf.getGroupId());
            deviceGeneralConf.setOrganization(v2DeviceGeneralConf.getOrganization());
            deviceGeneralConf.setGroup_name(v2DeviceGeneralConf.getGroupName());
            deviceGeneralConf.setDevice_name(v2DeviceGeneralConf.getDeviceName());
            deviceGeneralConf.setDevice_type(v2DeviceGeneralConf.getDeviceType());
            deviceGeneralConf.setDevice_type_version(v2DeviceGeneralConf.getDeviceTypeVersion());
            deviceGeneralConf.setDevice_model_code(v2DeviceGeneralConf.getDeviceModelCode());
            deviceGeneralConf.setDevice_model_name(v2DeviceGeneralConf.getDeviceModelName());
            deviceGeneralConf.setSerial_decimal(v2DeviceGeneralConf.getSerialDecimal());
            deviceGeneralConf.setScreen_size(v2DeviceGeneralConf.getScreenSize());
            deviceGeneralConf.setApplication_version(v2DeviceGeneralConf.getApplicationVersion());
            deviceGeneralConf.setRule_version(v2DeviceGeneralConf.getRuleVersion());
            deviceGeneralConf.setResolution(v2DeviceGeneralConf.getResolution());
            deviceGeneralConf.setError_flag(v2DeviceGeneralConf.getErrorFlag());
            deviceGeneralConf.setFirmware_version(v2DeviceGeneralConf.getFirmwareVersion());
            deviceGeneralConf.setOs_image_version(v2DeviceGeneralConf.getOsImageVersion());
            deviceGeneralConf.setPlayer_version(v2DeviceGeneralConf.getPlayerVersion());
            deviceGeneralConf.setVideo_adapter(v2DeviceGeneralConf.getVideoAdapter());
            deviceGeneralConf.setVideo_memory(v2DeviceGeneralConf.getVideoMemory());
            deviceGeneralConf.setVideo_driver(v2DeviceGeneralConf.getVideoDriver());
            deviceGeneralConf.setEwf_state(v2DeviceGeneralConf.getEwfState());
            deviceGeneralConf.setTunneling_server(v2DeviceGeneralConf.getTunnelingServer());
            deviceGeneralConf.setLocation(v2DeviceGeneralConf.getLocation());
            deviceGeneralConf.setThird_party_app_ver(v2DeviceGeneralConf.getThirdPartyAppVer());
            deviceGeneralConf.setCreate_date(v2DeviceGeneralConf.getCreateDate());
            deviceGeneralConf.setLatitude(v2DeviceGeneralConf.getLatitude());
            deviceGeneralConf.setLongitude(v2DeviceGeneralConf.getLongitude());
            deviceGeneralConf.setAltitude(v2DeviceGeneralConf.getAltitude());
            deviceGeneralConf.setNetwork_adapter(v2DeviceGeneralConf.getNetworkAdapter());
            deviceGeneralConf.setNetwork_driver(v2DeviceGeneralConf.getNetworkDriver());
            deviceGeneralConf.setMac_address(v2DeviceGeneralConf.getMacAddress());
            deviceGeneralConf.setIp_address(v2DeviceGeneralConf.getIpAddress());
            deviceGeneralConf.setReboot_flag(v2DeviceGeneralConf.getRebootFlag());
            deviceGeneralConf.setIp_setting_type(v2DeviceGeneralConf.getIpSettingType());
            deviceGeneralConf.setSubnet_mask(v2DeviceGeneralConf.getSubnetMask());
            deviceGeneralConf.setGateway(v2DeviceGeneralConf.getGateway());
            deviceGeneralConf.setDns_server_main(v2DeviceGeneralConf.getDnsServerMain());
            deviceGeneralConf.setDns_server_sub(v2DeviceGeneralConf.getDnsServerSub());
            deviceGeneralConf.setPort(v2DeviceGeneralConf.getPort());
            deviceGeneralConf.setMonitoring_interval(v2DeviceGeneralConf.getMonitoringInterval());
            deviceGeneralConf.setLast_connection_time(v2DeviceGeneralConf.getLastConnectionTime());
            deviceGeneralConf.setCpu_type(v2DeviceGeneralConf.getCpuType());
            deviceGeneralConf.setMem_size(v2DeviceGeneralConf.getMemSize());
            deviceGeneralConf.setHdd_size(v2DeviceGeneralConf.getHddSize());
            deviceGeneralConf.setDisk_space_usage(v2DeviceGeneralConf.getDiskSpaceUsage());
            deviceGeneralConf.setDisk_space_available(v2DeviceGeneralConf.getDiskSpaceAvailable());
            deviceGeneralConf.setMin_disk_space_available(v2DeviceGeneralConf.getMinDiskSpaceAvailable());
            deviceGeneralConf.setMap_id(v2DeviceGeneralConf.getMapId());
            deviceGeneralConf.setPosition_x(v2DeviceGeneralConf.getPositionX());
            deviceGeneralConf.setPosition_y(v2DeviceGeneralConf.getPositionY());
            deviceGeneralConf.setWidth(v2DeviceGeneralConf.getWidth());
            deviceGeneralConf.setHeight(v2DeviceGeneralConf.getHeight());
            deviceGeneralConf.setAngle(v2DeviceGeneralConf.getAngle());
            deviceGeneralConf.setBezel_leftright(v2DeviceGeneralConf.getBezelLeftright());
            deviceGeneralConf.setBezel_topbottom(v2DeviceGeneralConf.getBezelTopbottom());
            deviceGeneralConf.setVwt_id(v2DeviceGeneralConf.getVwtId());
            deviceGeneralConf.setWebcam(v2DeviceGeneralConf.getWebcam());
            deviceGeneralConf.setIcon_error_sw(v2DeviceGeneralConf.getIconErrorSw());
            deviceGeneralConf.setIcon_error_hw(v2DeviceGeneralConf.getIconErrorHw());
            deviceGeneralConf.setIcon_alarm(v2DeviceGeneralConf.getIconAlarm());
            deviceGeneralConf.setIcon_process_content_download(v2DeviceGeneralConf.getIconProcessContentDownload());
            deviceGeneralConf.setIcon_process_log(v2DeviceGeneralConf.getIconProcessLog());
            deviceGeneralConf.setIcon_process_sw_download(v2DeviceGeneralConf.getIconProcessSwDownload());
            deviceGeneralConf.setIcon_memo(v2DeviceGeneralConf.getIconMemo());
            deviceGeneralConf.setIcon_backup(v2DeviceGeneralConf.getIconBackup());
            deviceGeneralConf.setDisk_space_repository(v2DeviceGeneralConf.getDiskSpaceRepository());
            deviceGeneralConf.setSendCleanStorageFlag(v2DeviceGeneralConf.getSendCleanStorageFlag());
            deviceGeneralConf.setChild_cnt(v2DeviceGeneralConf.getChildCnt());
            deviceGeneralConf.setConn_child_cnt(v2DeviceGeneralConf.getConnChildCnt());
            deviceGeneralConf.setIs_child(v2DeviceGeneralConf.getIsChild());
            deviceGeneralConf.setHas_child(v2DeviceGeneralConf.getHasChild());
            deviceGeneralConf.setMdc_update_time(v2DeviceGeneralConf.getMdcUpdateTime());
            deviceGeneralConf.setSupport_flag(v2DeviceGeneralConf.getSupportFlag());
            deviceGeneralConf.setMap_location(v2DeviceGeneralConf.getMapLocation());
            deviceGeneralConf.setRecommend_play(v2DeviceGeneralConf.getRecommendPlay());
         } else if (originPreconfig.getDevice_control() != null) {
            deviceGeneralConf = originPreconfig.getDevice_control().getGeneral();
         }
      } else if (originPreconfig.getDevice_control() != null) {
         deviceGeneralConf = originPreconfig.getDevice_control().getGeneral();
      }

      List softwareList = new ArrayList();
      new ArrayList();
      if (preconfigData.getDeviceControl() != null) {
         if (preconfigData.getDeviceControl().getSoftwareList() != null) {
            List v2SoftwareList = preconfigData.getDeviceControl().getSoftwareList();
            Iterator var11 = v2SoftwareList.iterator();

            while(var11.hasNext()) {
               V2DeviceSoftwareConf v2DeviceSoftwareConf = (V2DeviceSoftwareConf)var11.next();
               DeviceSoftwareConf deviceSoftwareConf = new DeviceSoftwareConf();
               deviceSoftwareConf.setPreconfig_id(v2DeviceSoftwareConf.getPresetId());
               deviceSoftwareConf.setSoftware_id(v2DeviceSoftwareConf.getSoftwareId());
               deviceSoftwareConf.setSoftware_type(v2DeviceSoftwareConf.getSoftwareType());
               ((List)softwareList).add(deviceSoftwareConf);
            }
         }
      } else if (originPreconfig.getDevice_control() != null) {
         softwareList = originPreconfig.getDevice_control().getSoftwareList();
      }

      new V2DeviceSystemSetupConf();
      DeviceSystemSetupConf setupConf = new DeviceSystemSetupConf();
      if (preconfigData.getDeviceControl() != null) {
         if (preconfigData.getDeviceControl().getSetup() != null) {
            V2DeviceSystemSetupConf v2SetupConf = preconfigData.getDeviceControl().getSetup();
            setupConf.setDevice_id(v2SetupConf.getDeviceId());
            setupConf.setDevice_name(v2SetupConf.getDeviceName());
            setupConf.setDevice_model_name(v2SetupConf.getDeviceModelName());
            setupConf.setDevice_type(v2SetupConf.getDeviceType());
            setupConf.setTime_zone_index(v2SetupConf.getTimeZoneIndex());
            setupConf.setDay_light_saving(v2SetupConf.getDayLightSaving());
            setupConf.setDay_light_saving_manual(v2SetupConf.getDayLightSavingManual());
            setupConf.setAuto_time_setting(v2SetupConf.getAutoTimeSetting());
            setupConf.setOn_timer_setting(v2SetupConf.getOnTimerSetting());
            setupConf.setOff_timer_setting(v2SetupConf.getOffTimerSetting());
            setupConf.setMagicinfo_server_url(v2SetupConf.getMagicinfoServerUrl());
            setupConf.setIs_reverse(v2SetupConf.getIsReverse());
            setupConf.setTunneling_server(v2SetupConf.getTunnelingServer());
            setupConf.setTrigger_interval(v2SetupConf.getTriggerInterval());
            setupConf.setFtp_connect_mode(v2SetupConf.getFtpConnectMode());
            setupConf.setError_flag(v2SetupConf.getErrorFlag());
            setupConf.setRepository_path(v2SetupConf.getRepositoryPath());
            setupConf.setScreen_capture_interval(v2SetupConf.getScreenCaptureInterval());
            setupConf.setBg_color(v2SetupConf.getBgColor());
            setupConf.setMonitoring_interval(v2SetupConf.getMonitoringInterval());
            setupConf.setChild_monitoring_interval(v2SetupConf.getChildMonitoringInterval());
            setupConf.setLast_connection_time(v2SetupConf.getLastConnectionTime());
            setupConf.setProxy_setting(v2SetupConf.getProxySetting());
            setupConf.setProxy_setting_authorization(v2SetupConf.getProxySettingAuthorization());
            setupConf.setProxy_exclude_list(v2SetupConf.getProxyExcludeList());
            setupConf.setConnection_limit_time(v2SetupConf.getConnectionLimitTime());
            setupConf.setVwt_id(v2SetupConf.getVwtId());
            setupConf.setMnt_folder_path(v2SetupConf.getMntFolderPath());
            setupConf.setSystem_restart_interval(v2SetupConf.getSystemRestartInterval());
            setupConf.setLog_mnt(v2SetupConf.getLogMnt());
            setupConf.setProof_of_play_mnt(v2SetupConf.getProofOfPlayMnt());
            setupConf.setContent_mnt(v2SetupConf.getContentMnt());
            setupConf.setScreen_rotation(v2SetupConf.getScreenRotation());
            setupConf.setPlay_mode(v2SetupConf.getPlayMode());
            setupConf.setReset_password(v2SetupConf.getResetPassword());
            setupConf.setTime_zone_version(v2SetupConf.getTimeZoneVersion());
            setupConf.setReboot_flag(v2SetupConf.getRebootFlag());
            setupConf.setLast_sent_event(v2SetupConf.getLastSentEvent());
            setupConf.setAuto_ip_set(v2SetupConf.getAutoIpSet());
            setupConf.setAuto_computer_name_set(v2SetupConf.getAutoComputerNameSet());
            setupConf.setComputer_name(v2SetupConf.getComputerName());
            setupConf.setVnc_password(v2SetupConf.getVncPassword());
            setupConf.setUse_mpplayer(v2SetupConf.getUseMpplayer());
            setupConf.setFtp_port(v2SetupConf.getFtpPort());
            setupConf.setTag_id_list(v2SetupConf.getTagIdList());
            setupConf.setTag_value(v2SetupConf.getTagValue());
            setupConf.setTag_condition_list(v2SetupConf.getTagConditionList());
            setupConf.setFiledata_del_size(v2SetupConf.getFiledataDelSize());
            setupConf.setContent_ready_interval(v2SetupConf.getContentReadyInterval());
            setupConf.setPlayer_start_timeout(v2SetupConf.getPlayerStartTimeout());
            setupConf.setDatalink_server(v2SetupConf.getDatalinkServer());
            setupConf.setContents_progress_enable(v2SetupConf.getContentsProgressEnable());
            setupConf.setContents_progress_unit(v2SetupConf.getContentsProgressUnit());
            setupConf.setContents_progress_interval(v2SetupConf.getContentsProgressInterval());
            setupConf.setUrl_launcher(v2SetupConf.getUrlLauncher());
            setupConf.setPin_code(v2SetupConf.getPinCode());
            setupConf.setProtocol_priority(v2SetupConf.getProtocolPriority());
            setupConf.setRm_data_setting(v2SetupConf.getRmDataSetting());
            setupConf.setDevice_type_version(v2SetupConf.getDeviceTypeVersion());
            setupConf.setSwitch_time(v2SetupConf.getSwitchTime());
            setupConf.setBandwidth(v2SetupConf.getBandwidth());
            setupConf.setCpu_type(v2SetupConf.getCpuType());
            setupConf.setStatisticsFileRefresh(v2SetupConf.getStatisticsFileRefresh());
            setupConf.setIcon_error_sw(v2SetupConf.getIconErrorSw());
            setupConf.setIcon_error_hw(v2SetupConf.getIconErrorHw());
            setupConf.setIcon_alarm(v2SetupConf.getIconAlarm());
            setupConf.setIcon_process_content_download(v2SetupConf.getIconProcessContentDownload());
            setupConf.setIcon_process_log(v2SetupConf.getIconProcessLog());
            setupConf.setIcon_process_sw_download(v2SetupConf.getIconProcessSwDownload());
            setupConf.setIcon_memo(v2SetupConf.getIconMemo());
            setupConf.setIcon_backup(v2SetupConf.getIconBackup());
            setupConf.setChild_cnt(v2SetupConf.getChildCnt());
            setupConf.setIs_redundancy(v2SetupConf.getIsRedundancy());
            setupConf.setConn_child_cnt(v2SetupConf.getConnChildCnt());
            setupConf.setIs_child(v2SetupConf.getIsChild());
            setupConf.setHas_child(v2SetupConf.getHasChild());
            setupConf.setPre_config_version(v2SetupConf.getPreConfigVersion());
            setupConf.setRm_rule_version(v2SetupConf.getRmRuleVersion());
            setupConf.setSmart_download(v2SetupConf.getSmartDownload());
            setupConf.setWebcam(v2SetupConf.getWebcam());
            setupConf.setAms_play_mode(v2SetupConf.getAmsPlayMode());
            setupConf.setDownload_server(v2SetupConf.getDownloadServer());
            setupConf.setOnly_dn_server(v2SetupConf.getOnlyDnServer());
            setupConf.setExpiration_date(v2SetupConf.getExpirationDate());
            setupConf.setPlayer_resolution(v2SetupConf.getPlayerResolution());
            setupConf.setMulti_day_light_saving(v2SetupConf.getDeviceId(), v2SetupConf.getDayLightSaving());
            setupConf.setMulti_time_zone_index(v2SetupConf.getDeviceId(), v2SetupConf.getTimeZoneIndex());
         } else if (originPreconfig.getDevice_control() != null) {
            setupConf = originPreconfig.getDevice_control().getSetup();
         }
      } else if (originPreconfig.getDevice_control() != null) {
         setupConf = originPreconfig.getDevice_control().getSetup();
      }

      new V2DeviceDisplayConf();
      DeviceDisplayConf deviceDisplayConf = new DeviceDisplayConf();
      if (preconfigData.getDeviceControl() != null) {
         if (preconfigData.getDeviceControl().getDisplay() != null) {
            V2DeviceDisplayConf v2DeviceDisplayConf = preconfigData.getDeviceControl().getDisplay();
            deviceDisplayConf.setDevice_id(v2DeviceDisplayConf.getDeviceId());
            deviceDisplayConf.setDevice_name(v2DeviceDisplayConf.getDeviceName());
            deviceDisplayConf.setDevice_model_code(v2DeviceDisplayConf.getDeviceModelCode());
            deviceDisplayConf.setDevice_model_name(v2DeviceDisplayConf.getDeviceModelName());
            deviceDisplayConf.setDevice_type(v2DeviceDisplayConf.getDeviceType());
            deviceDisplayConf.setDevice_type_version(v2DeviceDisplayConf.getDeviceTypeVersion());
            deviceDisplayConf.setBasic_power(v2DeviceDisplayConf.getBasicPower());
            deviceDisplayConf.setBasic_volume(v2DeviceDisplayConf.getBasicVolume());
            deviceDisplayConf.setBasic_mute(v2DeviceDisplayConf.getBasicMute());
            deviceDisplayConf.setBasic_source(v2DeviceDisplayConf.getBasicSource());
            deviceDisplayConf.setBasic_direct_channel(v2DeviceDisplayConf.getBasicDirectChannel());
            deviceDisplayConf.setBasic_panel_status(v2DeviceDisplayConf.getBasicPanelStatus());
            deviceDisplayConf.setError_flag(v2DeviceDisplayConf.getErrorFlag());
            deviceDisplayConf.setNetwork_standby_mode(v2DeviceDisplayConf.getNetworkStandbyMode());
            deviceDisplayConf.setSpecialized_picture_mode(v2DeviceDisplayConf.getPvSplPictureMode());
            deviceDisplayConf.setPv_mode(v2DeviceDisplayConf.getPvMode());
            deviceDisplayConf.setPv_contrast(v2DeviceDisplayConf.getPvContrast());
            deviceDisplayConf.setPv_brightness(v2DeviceDisplayConf.getPvBrightness());
            deviceDisplayConf.setPv_sharpness(v2DeviceDisplayConf.getPvSharpness());
            deviceDisplayConf.setPv_color(v2DeviceDisplayConf.getPvColor());
            deviceDisplayConf.setPv_tint(v2DeviceDisplayConf.getPvTint());
            deviceDisplayConf.setPv_colortone(v2DeviceDisplayConf.getPvColortone());
            deviceDisplayConf.setPv_color_temperature(v2DeviceDisplayConf.getPvColorTemperature());
            deviceDisplayConf.setPv_size(v2DeviceDisplayConf.getPvSize());
            deviceDisplayConf.setPv_digitalnr(v2DeviceDisplayConf.getPvDigitalnr());
            deviceDisplayConf.setPv_filmmode(v2DeviceDisplayConf.getPvFilmmode());
            deviceDisplayConf.setPv_video_picture_position_size(v2DeviceDisplayConf.getPvVideoPicturePositionSize());
            deviceDisplayConf.setPv_hdmi_black_level(v2DeviceDisplayConf.getPvHdmiBlackLevel());
            deviceDisplayConf.setPv_mpeg_noise_filter(v2DeviceDisplayConf.getPvMpegNoiseFilter());
            deviceDisplayConf.setPpc_gamma(v2DeviceDisplayConf.getPpcGamma());
            deviceDisplayConf.setPpc_hdmi_black_level(v2DeviceDisplayConf.getPpcHdmiBlackLevel());
            deviceDisplayConf.setAuto_motion_plus(v2DeviceDisplayConf.getAutoMotionPlus());
            deviceDisplayConf.setAuto_motion_plus_judder_reduction(v2DeviceDisplayConf.getAutoMotionPlusJudderReduction());
            deviceDisplayConf.setPpc_magic_bright(v2DeviceDisplayConf.getPpcMagicBright());
            deviceDisplayConf.setPpc_contrast(v2DeviceDisplayConf.getPpcContrast());
            deviceDisplayConf.setPpc_brightness(v2DeviceDisplayConf.getPpcBrightness());
            deviceDisplayConf.setPpc_colortone(v2DeviceDisplayConf.getPpcColortone());
            deviceDisplayConf.setPpc_color_temperature(v2DeviceDisplayConf.getPpcColorTemperature());
            deviceDisplayConf.setPpc_red(v2DeviceDisplayConf.getPpcRed());
            deviceDisplayConf.setPpc_green(v2DeviceDisplayConf.getPpcGreen());
            deviceDisplayConf.setPpc_blue(v2DeviceDisplayConf.getPpcBlue());
            deviceDisplayConf.setPpc_size(v2DeviceDisplayConf.getPpcSize());
            deviceDisplayConf.setTime_current_time(v2DeviceDisplayConf.getTimeCurrentTime());
            deviceDisplayConf.setTime_on_time(v2DeviceDisplayConf.getTimeOnTime());
            deviceDisplayConf.setTime_off_time(v2DeviceDisplayConf.getTimeOffTime());
            deviceDisplayConf.setPip_source(v2DeviceDisplayConf.getPipSource());
            deviceDisplayConf.setPip_size(v2DeviceDisplayConf.getPipSize());
            deviceDisplayConf.setPip_position(v2DeviceDisplayConf.getPipPosition());
            deviceDisplayConf.setPip_swap(v2DeviceDisplayConf.getPipSwap());
            deviceDisplayConf.setPip_control(v2DeviceDisplayConf.getPipControl());
            deviceDisplayConf.setSound_mode(v2DeviceDisplayConf.getSoundMode());
            deviceDisplayConf.setSound_bass(v2DeviceDisplayConf.getSoundBass());
            deviceDisplayConf.setSound_treble(v2DeviceDisplayConf.getSoundTreble());
            deviceDisplayConf.setSound_balance(v2DeviceDisplayConf.getSoundBalance());
            deviceDisplayConf.setSound_srs(v2DeviceDisplayConf.getSoundSrs());
            deviceDisplayConf.setSound_effect(v2DeviceDisplayConf.getSoundEffect());
            deviceDisplayConf.setImage_coarse(v2DeviceDisplayConf.getImageCoarse());
            deviceDisplayConf.setImage_fine(v2DeviceDisplayConf.getImageFine());
            deviceDisplayConf.setImage_hpos(v2DeviceDisplayConf.getImageHpos());
            deviceDisplayConf.setImage_vpos(v2DeviceDisplayConf.getImageVpos());
            deviceDisplayConf.setImage_auto(v2DeviceDisplayConf.getImageAuto());
            deviceDisplayConf.setSb_status(v2DeviceDisplayConf.getSbStatus());
            deviceDisplayConf.setSb_rgain(v2DeviceDisplayConf.getSbRgain());
            deviceDisplayConf.setSb_ggain(v2DeviceDisplayConf.getSbGgain());
            deviceDisplayConf.setSb_bgain(v2DeviceDisplayConf.getSbBgain());
            deviceDisplayConf.setSb_r_offset(v2DeviceDisplayConf.getSbROffset());
            deviceDisplayConf.setSb_g_offset(v2DeviceDisplayConf.getSbGOffset());
            deviceDisplayConf.setSb_b_offset(v2DeviceDisplayConf.getSbBOffset());
            deviceDisplayConf.setSb_gain(v2DeviceDisplayConf.getSbGain());
            deviceDisplayConf.setSb_sharp(v2DeviceDisplayConf.getSbSharp());
            deviceDisplayConf.setMnt_auto(v2DeviceDisplayConf.getMntAuto());
            deviceDisplayConf.setMnt_manual(v2DeviceDisplayConf.getMntManual());
            deviceDisplayConf.setMnt_video_wall(v2DeviceDisplayConf.getMntVideoWall());
            deviceDisplayConf.setMnt_format(v2DeviceDisplayConf.getMntFormat());
            deviceDisplayConf.setMnt_safety_screen_timer(v2DeviceDisplayConf.getMntSafetyScreenTimer());
            deviceDisplayConf.setMnt_safety_screen_run(v2DeviceDisplayConf.getMntSafetyScreenRun());
            deviceDisplayConf.setMnt_pixel_shift(v2DeviceDisplayConf.getMntPixelShift());
            deviceDisplayConf.setMnt_safety_lock(v2DeviceDisplayConf.getMntSafetyLock());
            deviceDisplayConf.setAdvanced_rj45_setting_refresh(v2DeviceDisplayConf.getAdvancedRj45SettingRefresh());
            deviceDisplayConf.setAdvanced_osd_display_type(v2DeviceDisplayConf.getAdvancedOsdDisplayType());
            deviceDisplayConf.setAdvanced_osd_display_type_value(v2DeviceDisplayConf.getAdvancedOsdDisplayTypeValue());
            deviceDisplayConf.setAdvanced_fan_control(v2DeviceDisplayConf.getAdvancedFanControl());
            deviceDisplayConf.setAdvanced_fan_speed(v2DeviceDisplayConf.getAdvancedFanSpeed());
            deviceDisplayConf.setAdvanced_reset(v2DeviceDisplayConf.getAdvancedReset());
            deviceDisplayConf.setAdvanced_auto_power(v2DeviceDisplayConf.getAdvancedAutoPower());
            deviceDisplayConf.setAdvanced_user_auto_color(v2DeviceDisplayConf.getAdvancedUserAutoColor());
            deviceDisplayConf.setAdvanced_stand_by(v2DeviceDisplayConf.getAdvancedStandBy());
            deviceDisplayConf.setMax_power_saving(v2DeviceDisplayConf.getMaxPowerSaving());
            deviceDisplayConf.setBrightness_limit(v2DeviceDisplayConf.getBrightnessLimit());
            deviceDisplayConf.setTouch_control_lock(v2DeviceDisplayConf.getTouchControlLock());
            deviceDisplayConf.setAuto_source_switching(v2DeviceDisplayConf.getAutoSourceSwitching());
            deviceDisplayConf.setWeb_browser_url(v2DeviceDisplayConf.getWebBrowserUrl());
            deviceDisplayConf.setCustom_logo(v2DeviceDisplayConf.getCustomLogo());
            deviceDisplayConf.setScreen_freeze(v2DeviceDisplayConf.getScreenFreeze());
            deviceDisplayConf.setScreen_mute(v2DeviceDisplayConf.getScreenMute());
            deviceDisplayConf.setMisc_remocon(v2DeviceDisplayConf.getMiscRemocon());
            deviceDisplayConf.setMisc_panel_lock(v2DeviceDisplayConf.getMiscPanelLock());
            deviceDisplayConf.setMisc_osd(v2DeviceDisplayConf.getMiscOsd());
            deviceDisplayConf.setMisc_all_lock(v2DeviceDisplayConf.getMiscAllLock());
            deviceDisplayConf.setDiagnosis_display_status(v2DeviceDisplayConf.getDiagnosisDisplayStatus());
            deviceDisplayConf.setDiagnosis_monitor_temperature(v2DeviceDisplayConf.getDiagnosisMonitorTemperature());
            deviceDisplayConf.setDiagnosis_alarm_temperature(v2DeviceDisplayConf.getDiagnosisAlarmTemperature());
            deviceDisplayConf.setDiagnosis_panel_on_time(v2DeviceDisplayConf.getDiagnosisPanelOnTime());
            deviceDisplayConf.setSensor_internal_temperature(v2DeviceDisplayConf.getSensorInternalTemperature());
            deviceDisplayConf.setSensor_internal_humidity(v2DeviceDisplayConf.getSensorInternalHumidity());
            deviceDisplayConf.setSensor_environment_temperature(v2DeviceDisplayConf.getSensorEnvironmentTemperature());
            deviceDisplayConf.setSensor_frontglass_temperature(v2DeviceDisplayConf.getSensorFrontglassTemperature());
            deviceDisplayConf.setSensor_frontglass_humidity(v2DeviceDisplayConf.getSensorFrontglassHumidity());
            deviceDisplayConf.setChkSchChannel(v2DeviceDisplayConf.getChkSchChannel());
            deviceDisplayConf.setWebcam(v2DeviceDisplayConf.getWebcam());
            deviceDisplayConf.setVwt_id(v2DeviceDisplayConf.getVwtId());
            deviceDisplayConf.setIcon_error_hw(v2DeviceDisplayConf.getIconErrorHw());
            deviceDisplayConf.setIcon_error_sw(v2DeviceDisplayConf.getIconErrorSw());
            deviceDisplayConf.setIcon_alarm(v2DeviceDisplayConf.getIconAlarm());
            deviceDisplayConf.setIcon_process_content_download(v2DeviceDisplayConf.getIconProcessContentDownload());
            deviceDisplayConf.setIcon_process_log(v2DeviceDisplayConf.getIconProcessLog());
            deviceDisplayConf.setIcon_process_sw_download(v2DeviceDisplayConf.getIconProcessSwDownload());
            deviceDisplayConf.setIcon_memo(v2DeviceDisplayConf.getIconMemo());
            deviceDisplayConf.setIcon_backup(v2DeviceDisplayConf.getIconBackup());
            deviceDisplayConf.setAuto_brightness(v2DeviceDisplayConf.getAutoBrightness());
            deviceDisplayConf.setChild_alarm_temperature(v2DeviceDisplayConf.getChildAlarmTemperature());
            deviceDisplayConf.setBlack_tone(v2DeviceDisplayConf.getBlackTone());
            deviceDisplayConf.setFlesh_tone(v2DeviceDisplayConf.getFleshTone());
            deviceDisplayConf.setRgb_only_mode(v2DeviceDisplayConf.getRgbOnlyMode());
            deviceDisplayConf.setPicture_enhancer(v2DeviceDisplayConf.getPictureEnhancer());
            deviceDisplayConf.setColor_space(v2DeviceDisplayConf.getColorSpace());
            deviceDisplayConf.setOsd_menu_size(v2DeviceDisplayConf.getOsdMenuSize());
            deviceDisplayConf.setLed_picture_size(v2DeviceDisplayConf.getLedPictureSize());
            deviceDisplayConf.setLed_hdr(v2DeviceDisplayConf.getLedHdr());
            deviceDisplayConf.setLed_hdr_dre(v2DeviceDisplayConf.getLedHdrDre());
            deviceDisplayConf.setEco_sensor(v2DeviceDisplayConf.getEcoSensor());
            deviceDisplayConf.setMin_brightness(v2DeviceDisplayConf.getMinBrightness());
            deviceDisplayConf.setLive_mode(v2DeviceDisplayConf.getLiveMode());
            deviceDisplayConf.setDisplay_output_mode(v2DeviceDisplayConf.getDisplayOutputMode());
            deviceDisplayConf.setCleanup_user_data(v2DeviceDisplayConf.getCleanupUserData());
            deviceDisplayConf.setCleanup_user_data_interval(v2DeviceDisplayConf.getCleanupUserDataInterval());
            deviceDisplayConf.setAuto_save(v2DeviceDisplayConf.getAutoSave());
            deviceDisplayConf.setAuto_power_off(v2DeviceDisplayConf.getAutoPowerOff());
            deviceDisplayConf.setSmtp(v2DeviceDisplayConf.getSmtp());
            deviceDisplayConf.setPrint_server(v2DeviceDisplayConf.getPrintServer());
            deviceDisplayConf.setChild_cnt(v2DeviceDisplayConf.getChildCnt());
            deviceDisplayConf.setConn_child_cnt(v2DeviceDisplayConf.getConnChildCnt());
            deviceDisplayConf.setIs_child(v2DeviceDisplayConf.getIsChild());
            deviceDisplayConf.setHas_child(v2DeviceDisplayConf.getHasChild());
            deviceDisplayConf.setVwl_mode(v2DeviceDisplayConf.getVwlMode());
            deviceDisplayConf.setVwl_position(v2DeviceDisplayConf.getVwlPosition());
            deviceDisplayConf.setVwl_format(v2DeviceDisplayConf.getVwlFormat());
            deviceDisplayConf.setVwl_layout(v2DeviceDisplayConf.getVwlLayout());
         } else if (originPreconfig.getDevice_control() != null) {
            deviceDisplayConf = originPreconfig.getDevice_control().getDisplay();
         }
      } else if (originPreconfig.getDevice_control() != null) {
         deviceDisplayConf = originPreconfig.getDevice_control().getDisplay();
      }

      DeviceTimeConf timeConf = new DeviceTimeConf();
      new V2DeviceTimeConf();
      if (preconfigData.getDeviceControl() != null) {
         if (preconfigData.getDeviceControl().getTime() != null) {
            V2DeviceTimeConf v2TimeConf = preconfigData.getDeviceControl().getTime();
            timeConf.setDevice_id(v2TimeConf.getDeviceId());
            timeConf.setDevice_name(v2TimeConf.getDeviceName());
            timeConf.setDevice_model_code(v2TimeConf.getDeviceModelCode());
            timeConf.setDevice_model_name(v2TimeConf.getDeviceModelName());
            timeConf.setTime_current_time(v2TimeConf.getTimeCurrentTime());
            timeConf.setTime_on_time(v2TimeConf.getTimeOnTime());
            timeConf.setTime_off_time(v2TimeConf.getTimeOffTime());
            timeConf.setTimer_clock(v2TimeConf.getTimerClock());
            timeConf.setTimer_timer1(v2TimeConf.getTimerTimer1());
            timeConf.setTimer_timer2(v2TimeConf.getTimerTimer2());
            timeConf.setTimer_timer3(v2TimeConf.getTimerTimer3());
            timeConf.setTimer_timer4(v2TimeConf.getTimerTimer4());
            timeConf.setTimer_timer5(v2TimeConf.getTimerTimer5());
            timeConf.setTimer_timer6(v2TimeConf.getTimerTimer6());
            timeConf.setTimer_timer7(v2TimeConf.getTimerTimer7());
            timeConf.setTimer_holiday(v2TimeConf.getTimerHoliday());
            timeConf.setHoliday_cnt(v2TimeConf.getHolidayCnt());
            timeConf.setIcon_error_sw(v2TimeConf.getIconErrorSw());
            timeConf.setIcon_error_hw(v2TimeConf.getIconErrorHw());
            timeConf.setIcon_alarm(v2TimeConf.getIconAlarm());
            timeConf.setIcon_process_content_download(v2TimeConf.getIconProcessContentDownload());
            timeConf.setIcon_process_log(v2TimeConf.getIconProcessLog());
            timeConf.setIcon_process_sw_download(v2TimeConf.getIconProcessSwDownload());
            timeConf.setIcon_memo(v2TimeConf.getIconMemo());
            timeConf.setIcon_backup(v2TimeConf.getIconBackup());
            timeConf.setTime_clock_conf(v2TimeConf.getTimeClockConf());
            timeConf.setTimer_conf_timer1(v2TimeConf.getTimerConfTimer1());
            timeConf.setTimer_conf_timer2(v2TimeConf.getTimerConfTimer2());
            timeConf.setTimer_conf_timer3(v2TimeConf.getTimerConfTimer3());
            timeConf.setTimer_conf_timer4(v2TimeConf.getTimerConfTimer4());
            timeConf.setTimer_conf_timer5(v2TimeConf.getTimerConfTimer5());
            timeConf.setTimer_conf_timer6(v2TimeConf.getTimerConfTimer6());
            timeConf.setTimer_conf_timer7(v2TimeConf.getTimerConfTimer7());
            timeConf.setSeparatableDevice(v2TimeConf.isSeparatableDevice());
            timeConf.setReboot_flag(v2TimeConf.getRebootFlag());
            timeConf.setMdc_update_time(v2TimeConf.getMdcUpdateTime());
            timeConf.setChild_cnt(v2TimeConf.getChildCnt());
            timeConf.setConn_child_cnt(v2TimeConf.getConnChildCnt());
            timeConf.setIs_child(v2TimeConf.getIsChild());
            timeConf.setHas_child(v2TimeConf.getHasChild());
            timeConf.setTimer_timer1(timeConf.combinTimerValues(timeConf.getTimer_conf_timer1()));
            timeConf.setTimer_timer2(timeConf.combinTimerValues(timeConf.getTimer_conf_timer2()));
            timeConf.setTimer_timer3(timeConf.combinTimerValues(timeConf.getTimer_conf_timer3()));
            timeConf.setTimer_timer4(timeConf.combinTimerValues(timeConf.getTimer_conf_timer4()));
            timeConf.setTimer_timer5(timeConf.combinTimerValues(timeConf.getTimer_conf_timer5()));
            timeConf.setTimer_timer6(timeConf.combinTimerValues(timeConf.getTimer_conf_timer6()));
            timeConf.setTimer_timer7(timeConf.combinTimerValues(timeConf.getTimer_conf_timer7()));
         } else if (originPreconfig.getDevice_control() != null) {
            timeConf = originPreconfig.getDevice_control().getTime();
         }
      } else if (originPreconfig.getDevice_control() != null) {
         timeConf = originPreconfig.getDevice_control().getTime();
      }

      DeviceSecurityConf securityConf = new DeviceSecurityConf();
      new V2DeviceSecurityConf();
      if (preconfigData.getDeviceControl() != null) {
         if (preconfigData.getDeviceControl().getSecurity() != null) {
            V2DeviceSecurityConf v2SecurityConf = preconfigData.getDeviceControl().getSecurity();
            securityConf.setChild_cnt(v2SecurityConf.getChildCnt());
            securityConf.setConn_child_cnt(v2SecurityConf.getConnChildCnt());
            securityConf.setIs_child(v2SecurityConf.getIsChild());
            securityConf.setHas_child(v2SecurityConf.getHasChild());
            securityConf.setDevice_name(v2SecurityConf.getDeviceName());
            securityConf.setDevice_type(v2SecurityConf.getDeviceType());
            securityConf.setDevice_type_version(v2SecurityConf.getDeviceTypeVersion());
            securityConf.setDevice_id(v2SecurityConf.getDeviceId());
            securityConf.setMdc_update_time(v2SecurityConf.getMdcUpdateTime());
            securityConf.setMnt_safety_lock(v2SecurityConf.getMntSafetyLock());
            securityConf.setMisc_remocon(v2SecurityConf.getMiscRemocon());
            securityConf.setMisc_panel_lock(v2SecurityConf.getMiscPanelLock());
            securityConf.setMisc_all_lock(v2SecurityConf.getMiscAllLock());
            securityConf.setTouch_control_lock(v2SecurityConf.getTouchControlLock());
            securityConf.setMisc_block_usb_port(v2SecurityConf.getMiscBlockUsbPort());
            securityConf.setMisc_block_network_connection(v2SecurityConf.getMiscBlockNetworkConnection());
            securityConf.setMisc_white_list(v2SecurityConf.getMiscWhiteList());
            securityConf.setIs_init_security(v2SecurityConf.getIsInitSecurity());
            securityConf.setCapture_lock(v2SecurityConf.getCaptureLock());
            securityConf.setBluetooth_lock(v2SecurityConf.getBluetoothLock());
            securityConf.setWifi_lock(v2SecurityConf.getWifiLock());
            securityConf.setSource_lock(DeviceUtils.sourceLockListToString(v2SecurityConf.getSourceLock()));
            securityConf.setScreen_monitoring_lock(v2SecurityConf.getScreenMonitoringLock());
            securityConf.setMisc_server_network_setting(v2SecurityConf.getMiscServerNetworkSetting());
         } else if (originPreconfig.getDevice_control() != null) {
            securityConf = originPreconfig.getDevice_control().getSecurity();
         }
      } else if (originPreconfig.getDevice_control() != null) {
         securityConf = originPreconfig.getDevice_control().getSecurity();
      }

      String groupIds;
      if (preconfigData.getDeviceControl() != null) {
         deviceControl.setTargetId(preconfigData.getDeviceControl().getTargetId());
         if (preconfigData.getDeviceControl().getServer() != null) {
            Map serverMap = new HashMap();
            groupIds = preconfigData.getDeviceControl().getServer().getPopUpload();
            String screenCaptureUpload = preconfigData.getDeviceControl().getServer().getScreenCapture();
            String firmwareDownload = preconfigData.getDeviceControl().getServer().getFirmwareDownload();
            String contentsDownload = preconfigData.getDeviceControl().getServer().getContentsDownload();
            if (!StrUtils.nvl(groupIds).equals("")) {
               serverMap.put("pop_upload", groupIds);
            }

            if (!StrUtils.nvl(screenCaptureUpload).equals("")) {
               serverMap.put("screen_capture_upload", screenCaptureUpload);
            }

            if (!StrUtils.nvl(firmwareDownload).equals("")) {
               serverMap.put("firmware_download", firmwareDownload);
            }

            if (!StrUtils.nvl(contentsDownload).equals("")) {
               serverMap.put("contents_download", contentsDownload);
            }

            deviceControl.setServer(serverMap);
         }

         V2DeviceCustomConf deviceCustomConf = preconfigData.getDeviceControl().getCustom();
         if (deviceCustomConf != null) {
            Map softwareMap = new HashMap();
            if (deviceCustomConf.getDefaultContent() != null && !deviceCustomConf.getDefaultContent().equals("")) {
               softwareMap.put("software_default_content", deviceCustomConf.getDefaultContent());
            }

            if (deviceCustomConf.getCustomLogo() != null && !deviceCustomConf.getCustomLogo().equals("")) {
               softwareMap.put("software_custom_logo", deviceCustomConf.getCustomLogo());
            }

            deviceControl.setSoftware(softwareMap);
         }
      } else {
         deviceControl = originPreconfig.getDevice_control();
      }

      if (deviceControl != null) {
         deviceControl.setGeneral(deviceGeneralConf);
         deviceControl.setSetup(setupConf);
         deviceControl.setDisplay(deviceDisplayConf);
         deviceControl.setTime(timeConf);
         deviceControl.setSecurity(securityConf);
         deviceControl.setServiceList((List)serviceList);
         deviceControl.setSoftwareList((List)softwareList);
      }

      if (preconfigData.getDeviceControl() != null) {
         preconfig.setName(preconfigData.getName());
         preconfig.setVersion(preconfigData.getVersion());
         preconfig.setDescription(preconfigData.getDescription());
         preconfig.setCreate_date(preconfigData.getCreateDate());
         preconfig.setUpdate_time(preconfigData.getUpdateTime());
         preconfig.setOrganization_id(preconfigData.getOrganizationId());
         preconfig.setOrganization_name(preconfigData.getOrganizationName());
         preconfig.setCompleted_count(preconfigData.getCompletedCount());
         preconfig.setTotal_count(preconfigData.getTotalCount());
      }

      if (preconfigData.getDeviceGroupIds() != null && preconfigData.getDeviceGroupIds().size() != 0) {
         List tmpGroupIds = (List)Optional.ofNullable(preconfigData.getDeviceGroupIds()).orElse(new ArrayList());
         groupIds = null;
         if (!tmpGroupIds.isEmpty()) {
            groupIds = (String)tmpGroupIds.stream().collect(Collectors.joining(","));
         }

         preconfig.setDevice_group_ids(groupIds);
      }

      preconfig.setDevice_control(deviceControl);
      preconfig.setDevice_service_confs((List)serviceList);
      if (presetId != null) {
         preconfig.setOrganization_id(originPreconfig.getOrganization_id());
         preconfig.setOrganization_name(originPreconfig.getOrganization_name());
      } else {
         preconfig.setOrganization_id(preconfigData.getOrganizationId());
         preconfig.setOrganization_name(preconfigData.getOrganizationName());
      }

      return preconfig;
   }

   private boolean updateDeviceControl(DeviceControl obj) {
      try {
         if (obj != null) {
            String preconfigId = obj.getTargetId();
            DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
            Device device = new Device();
            device.setDefaultDBValues();
            device.setDevice_id(preconfigId);
            device.setCreate_date(new Timestamp(System.currentTimeMillis()));
            device.setDevice_model_code("PRECONFIG");
            device.setDevice_model_name("PRECONFIG");
            device.setDevice_type("PRECONFIG");
            device.setIs_approved((Boolean)null);
            deviceDao.deleteDeviceData(preconfigId);
            boolean deviceResult = deviceDao.addDevice(device);
            if (deviceResult) {
               try {
                  DeviceDisplayConf display = obj.getDisplay();
                  DeviceDisplayConfManager displayDao = DeviceDisplayConfManagerImpl.getInstance();
                  if (display == null) {
                     display = new DeviceDisplayConf();
                  }

                  display.setDevice_id(preconfigId);
                  display.setModelType("PRECONFIG");
                  displayDao.addDeviceDisplayConfData(display);
               } catch (Exception var12) {
                  this.logger.error("[DeviceActivity][Preconfig] fail to add display. preconfig id : " + preconfigId + ". " + var12.getMessage());
               }

               try {
                  DeviceTimeConf time = obj.getTime();
                  if (time == null) {
                     time = new DeviceTimeConf();
                  }

                  time.setDevice_id(preconfigId);
                  time.setModelType("PRECONFIG");
                  DeviceTimeConfManager timeDao = DeviceTimeConfManagerImpl.getInstance();
                  timeDao.deleteFromMiDmsInfoTimeTimer(preconfigId);
                  timeDao.setDeviceLfdNewTimer(time);
               } catch (Exception var11) {
                  this.logger.error("[DeviceActivity][Preconfig] fail to add time. preconfig id : " + preconfigId + ". " + var11.getMessage());
               }

               try {
                  DeviceSystemSetupConf setup = obj.getSetup();
                  if (setup == null) {
                     setup = new DeviceSystemSetupConf();
                  }

                  DeviceSystemSetupConfManager systemSetupDao = DeviceSystemSetupConfManagerImpl.getInstance();
                  setup.setModelType("PRECONFIG");
                  setup.setDevice_id(preconfigId);
                  systemSetupDao.setDeviceSystemSetupConfWithEmpty(setup);
               } catch (Exception var10) {
                  this.logger.error("[DeviceActivity][Preconfig] fail to add setup. preconfig id : " + preconfigId + ". " + var10.getMessage());
               }

               try {
                  DeviceSecurityConf security = obj.getSecurity();
                  if (security == null) {
                     security = new DeviceSecurityConf();
                  }

                  DeviceSecurityConfManager securityDao = DeviceSecurityConfManagerImpl.getInstance();
                  security.setDevice_id(preconfigId);
                  security.setModelType("PRECONFIG");
                  securityDao.setDeviceSecurityConf(security, false);
               } catch (Exception var9) {
                  this.logger.error("[DeviceActivity][Preconfig] fail to add security. preconfig id : " + preconfigId + ". " + var9.getMessage());
               }

               List serviceList = obj.getServiceList();
               this.preconfigInfo.addServiceConfigList(preconfigId, serviceList);
               List softwareList = obj.getSoftwareList();
               this.preconfigInfo.addSoftwareConfigList(preconfigId, softwareList);
            }
         }

         return true;
      } catch (Exception var13) {
         this.logger.error("", var13);
         return false;
      }
   }

   public ModelAndView noticeExport(String exportType, String type, String searchText, String sortColumn, String sortOrder, HttpServletResponse response, String localeData) {
      type = StrUtils.nvl(type).equals("") ? "CONTENT" : type;
      new SecurityUtils();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (StrUtils.nvl(sortColumn).equals("")) {
         sortColumn = "device_name";
      }

      if (StrUtils.nvl(sortOrder).equals("")) {
         sortOrder = "asc";
      }

      if (StrUtils.nvl(searchText).equals("")) {
         searchText = "";
      }

      if (StrUtils.nvl(exportType).equals("")) {
         exportType = "EXCEL";
      }

      SelectCondition condition = new SelectCondition();
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      condition.setSrc_name(searchText);
      condition.setRole_name(userContainer.getUser().getRole_name());
      condition.setUser_id(userContainer.getUser().getUser_id());
      condition.setOrder_dir(sortOrder);
      condition.setSort_name(sortColumn);
      String fileExtension = "xls";
      if (exportType.toUpperCase().equals("PDF")) {
         fileExtension = "pdf";
      }

      Timestamp nowTime = new Timestamp(System.currentTimeMillis());
      Map dataMap = new HashMap();
      if (StrUtils.nvl(localeData).equals("")) {
         String userLocale = SecurityUtils.getUserContainer().getUser().getLocale();
         if (userLocale != null && !userLocale.equalsIgnoreCase("")) {
            localeData = userLocale;
         } else {
            localeData = "en";
         }
      }

      Locale locale = new Locale(localeData);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      Object[] dataList = null;
      String fileName = "NotificationList_" + nowTime.toString() + "." + fileExtension;
      String sheetName = null;
      String[] columnNames = new String[]{"device_type", "device_id", "ip_address", "disk_space_available", "device_name", "device_model_name"};
      String str_device_type = rms.getMessage("MIS_TEXT_DEVICE_TYPE_P", (Object[])null, locale);
      String str_mac_address = rms.getMessage("TABLE_MAC_ADDR_P", (Object[])null, locale);
      String str_ip_address = rms.getMessage("TABLE_IP_ADDR_P", (Object[])null, locale);
      String str_disk_space_available = rms.getMessage("TABLE_DISK_SPACE_AVAILABLE_P", (Object[])null, locale);
      String str_device_name = rms.getMessage("TABLE_DEVICE_NAME_P", (Object[])null, locale);
      String str_device_model_name = rms.getMessage("TABLE_DEVICE_MODEL_NAME_P", (Object[])null, locale);
      String[] fieldNames = new String[]{str_device_type, str_mac_address, str_ip_address, str_disk_space_available, str_device_name, str_device_model_name};

      try {
         DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
         List deviceGroupList = null;
         if (userContainer.getUser().getRoot_group_id() == 0L) {
            List deviceOrgIds = DeviceUtils.getOrgGroupIdByUserId(userContainer.getUser().getUser_id());
            if (deviceOrgIds != null && deviceOrgIds.size() > 0) {
               deviceGroupList = groupDao.getAllDeviceGroups(deviceOrgIds);
            }
         }

         if (deviceGroupList == null) {
            deviceGroupList = groupDao.getChildGroupList((int)groupDao.getOrganGroupIdByName(userContainer.getUser().getOrganization()), true);
         }

         ListManager listMgr = new ListManager(deviceDao, "list");
         listMgr.addSearchInfo("deviceGroupList", deviceGroupList);
         Long schOrgGroupId = null;
         if (type.equalsIgnoreCase("TIMEZONE")) {
            listMgr.setSection("getCheckDeviceListTimezone");
            sheetName = rms.getMessage("MIS_SID_TIME_ZONE_NOT_SET", (Object[])null, locale);
         } else if (type.equalsIgnoreCase("STOREGE")) {
            listMgr.setSection("getCheckDeviceListStorage");
            sheetName = rms.getMessage("MIS_SID_INSUFFICIENT_CAPACITY", (Object[])null, locale);
         } else if (type.equalsIgnoreCase("SCHEDULE")) {
            listMgr.setSection("getCheckDeviceListSchedule");
            sheetName = rms.getMessage("MIS_SID_SCHEDULE_NOT_PUBLISHED", (Object[])null, locale);
         } else if (type.equalsIgnoreCase("CONTENT")) {
            listMgr.setSection("getCheckDeviceListContent");
            sheetName = rms.getMessage("MIS_SID_CONTENT_DOWNLOAD_INCOMPLETE", (Object[])null, locale);
         }

         schOrgGroupId = deviceGroupInfo.getOrganGroupIdByName(userContainer.getUser().getOrganization());
         condition.setOrg_id(schOrgGroupId);
         listMgr.addSearchInfo("condition", condition);
         List list = listMgr.V2dbexecute(1, 1000000);
         int dataListSize = false;
         if (list != null) {
            int dataListSize = list.size();
            dataList = new Object[dataListSize];

            for(int index = 0; index < dataListSize; ++index) {
               DeviceGeneralConf deviceGeneralConf = (DeviceGeneralConf)list.get(index);
               if (deviceGeneralConf.getDevice_type().equalsIgnoreCase("SIGNAGE") || deviceGeneralConf.getDevice_type().equalsIgnoreCase("RSIGNAGE")) {
                  deviceGeneralConf.setDevice_type("Signage Player (" + deviceGeneralConf.getChild_cnt() + ")");
               }

               dataList[index] = deviceGeneralConf;
            }
         }

         dataMap.put("fileName", fileName);
         dataMap.put("sheetName", sheetName);
         dataMap.put("columnNames", columnNames);
         dataMap.put("fieldNames", fieldNames);
         dataMap.put("dataList", dataList);
      } catch (Exception var37) {
         this.logger.error("[REST_v2.0][DEVICE SERVICE][noticeExport] Failed to export file", var37);
      }

      if (exportType.equalsIgnoreCase("PDF")) {
         PdfBuilder pdfView = new PdfBuilder();
         return new ModelAndView(pdfView, dataMap);
      } else {
         this.downloadService = new DeviceStatisticsDownloadService();
         this.downloadService.downloadExcelFile(dataMap, response);
         return null;
      }
   }

   public ModelAndView expiryDatePlaylistExport(String exportType, String searchText, String sortColumn, String sortOrder, HttpServletResponse response, String localeData) {
      new SecurityUtils();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (StrUtils.nvl(sortColumn).equals("")) {
         sortColumn = "playlist_name";
      }

      if (StrUtils.nvl(sortOrder).equals("")) {
         sortOrder = "desc";
      }

      if (StrUtils.nvl(searchText).equals("")) {
         searchText = "";
      }

      if (StrUtils.nvl(exportType).equals("")) {
         exportType = "EXCEL";
      }

      SelectCondition condition = new SelectCondition();
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      condition.setSrc_name(searchText);
      condition.setOrder_dir(sortOrder);
      condition.setSort_name(sortColumn);
      String fileExtension = "xls";
      if (exportType.toUpperCase().equals("PDF")) {
         fileExtension = "pdf";
      }

      Timestamp nowTime = new Timestamp(System.currentTimeMillis());
      Map dataMap = new HashMap();
      if (StrUtils.nvl(localeData).equals("")) {
         String userLocale = SecurityUtils.getUserContainer().getUser().getLocale();
         if (userLocale != null && !userLocale.equalsIgnoreCase("")) {
            localeData = userLocale;
         } else {
            localeData = "en";
         }
      }

      Locale locale = new Locale(localeData);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      Object[] dataList = null;
      String fileName = "upcomingExpiryDatePlayer" + nowTime.toString() + "." + fileExtension;
      String sheetName = rms.getMessage("MIS_SID_SERVER_PLAYLISTS_TO_EXPIRE", (Object[])null, locale);
      String[] columnNames = new String[]{"playlist_name", "playlist_type", "total_size", "play_time", "device_type", "device_type_version", "last_modified_date", "creator_id"};
      String str_playlist_name = rms.getMessage("TABLE_PLAYLIST_NAME_P", (Object[])null, locale);
      String str_playlist_type = rms.getMessage("MIS_SID_PLAYLIST_TYPE", (Object[])null, locale);
      String str_total_size = rms.getMessage("TEXT_TOTAL_SIZE_P", (Object[])null, locale);
      String str_play_time = rms.getMessage("TEXT_PLAY_TIME_P", (Object[])null, locale);
      String str_device_type = rms.getMessage("MIS_TEXT_DEVICE_TYPE_P", (Object[])null, locale);
      String str_device_type_version = rms.getMessage("TABLE_VERSION_P", (Object[])null, locale);
      String str_modify_date = rms.getMessage("COM_TEXT_MODIFY_DATE_P", (Object[])null, locale);
      String str_creator_id = rms.getMessage("TEXT_CREATOR_P", (Object[])null, locale);
      String[] fieldNames = new String[]{str_playlist_name, str_playlist_type, str_total_size, str_play_time, str_device_type, str_device_type_version, str_modify_date, str_creator_id};
      String userId = userContainer.getUser().getUser_id();
      ProgramGroupInfoImpl programGroupInfo = ProgramGroupInfoImpl.getInstance();

      try {
         int programGroupId = programGroupInfo.getProgramGroupForOrg(userContainer.getUser().getOrganization());
         PlaylistInfo playlistDao = PlaylistInfoImpl.getInstance();
         Date date = new Date();
         Calendar c = Calendar.getInstance();
         c.setTime(date);
         SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
         String stopDate = sdf.format(c.getTime());
         List list = playlistDao.getListPlaylistToExpire(-1, -1, userId, (long)programGroupId, stopDate, condition);
         int dataListSize = false;
         if (list != null) {
            int dataListSize = list.size();
            dataList = new Object[dataListSize];

            for(int index = 0; index < dataListSize; ++index) {
               Playlist schedule = (Playlist)list.get(index);
               dataList[index] = schedule;
            }
         }

         dataMap.put("fileName", fileName);
         dataMap.put("sheetName", sheetName);
         dataMap.put("columnNames", columnNames);
         dataMap.put("fieldNames", fieldNames);
         dataMap.put("dataList", dataList);
      } catch (Exception var39) {
         this.logger.error("", var39);
      }

      if (exportType.equalsIgnoreCase("PDF")) {
         PdfBuilder pdfView = new PdfBuilder();
         return new ModelAndView(pdfView, dataMap);
      } else {
         this.downloadService = new DeviceStatisticsDownloadService();
         this.downloadService.downloadExcelFile(dataMap, response);
         return null;
      }
   }

   public ModelAndView expiryDateExport(String exportType, String searchText, String sortColumn, String sortOrder, HttpServletResponse response, String localeData) {
      new SecurityUtils();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (StrUtils.nvl(sortColumn).equals("") || "expiration_date".equalsIgnoreCase(StrUtils.nvl(sortColumn))) {
         sortColumn = "stop_date";
      }

      if (StrUtils.nvl(sortOrder).equals("")) {
         sortOrder = "desc";
      }

      if (StrUtils.nvl(searchText).equals("")) {
         searchText = "";
      }

      if (StrUtils.nvl(exportType).equals("")) {
         exportType = "EXCEL";
      }

      SelectCondition condition = new SelectCondition();
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      condition.setSrc_name(searchText);
      condition.setOrder_dir(sortOrder);
      condition.setSort_name(sortColumn);
      String fileExtension = "xls";
      if (exportType.toUpperCase().equals("PDF")) {
         fileExtension = "pdf";
      }

      Timestamp nowTime = new Timestamp(System.currentTimeMillis());
      Map dataMap = new HashMap();
      if (StrUtils.nvl(localeData).equals("")) {
         String userLocale = SecurityUtils.getUserContainer().getUser().getLocale();
         if (userLocale != null && !userLocale.equalsIgnoreCase("")) {
            localeData = userLocale;
         } else {
            localeData = "en";
         }
      }

      Locale locale = new Locale(localeData);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      ScheduleInfo scheduleDao = ScheduleInfoImpl.getInstance();
      Object[] dataList = null;
      String fileName = "SchedulesToExpire" + nowTime.toString() + "." + fileExtension;
      String sheetName = rms.getMessage("MIS_SID_SERVER_SCHEDULES_TO_EXPIRE", (Object[])null, locale);
      String[] columnNames = new String[]{"program_name", "program_type", "device_type", "device_type_version", "stop_date", "modify_date"};
      String str_program_name = rms.getMessage("TEXT_SCHEDULE_NAME_P", (Object[])null, new Locale(locale.getLanguage()));
      String str_program_type = rms.getMessage("MIS_SID_20_SCHEDULE_TYPE", (Object[])null, new Locale(locale.getLanguage()));
      String str_device_type = rms.getMessage("MIS_TEXT_DEVICE_TYPE_P", (Object[])null, locale);
      String str_device_type_version = rms.getMessage("TABLE_VERSION_P", (Object[])null, locale);
      String str_stop_date = rms.getMessage("COM_EXPIRATION_DATE_KR_DATE", (Object[])null, locale);
      String str_date_modified = rms.getMessage("COM_TEXT_MODIFY_DATE_P", (Object[])null, locale);
      String[] fieldNames = new String[]{str_program_name, str_program_type, str_device_type, str_device_type_version, str_stop_date, str_date_modified};
      String userId = userContainer.getUser().getUser_id();
      ProgramGroupInfoImpl programGroupInfo = ProgramGroupInfoImpl.getInstance();

      try {
         int programGroupId = programGroupInfo.getProgramGroupForOrg(userContainer.getUser().getOrganization());
         PlaylistInfo playlistDao = PlaylistInfoImpl.getInstance();
         Date date = new Date();
         Calendar c = Calendar.getInstance();
         c.setTime(date);
         SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
         String stopDate = sdf.format(c.getTime());
         List list = scheduleDao.getListScheduleToExpire(-1, -1, userId, (long)programGroupId, stopDate, condition);
         int dataListSize = false;
         if (list != null) {
            int dataListSize = list.size();
            dataList = new Object[dataListSize];

            for(int index = 0; index < dataListSize; ++index) {
               ScheduleAdminEntity schedule = (ScheduleAdminEntity)list.get(index);
               dataList[index] = schedule;
            }
         }

         dataMap.put("fileName", fileName);
         dataMap.put("sheetName", sheetName);
         dataMap.put("columnNames", columnNames);
         dataMap.put("fieldNames", fieldNames);
         dataMap.put("dataList", dataList);
      } catch (Exception var38) {
         this.logger.error("", var38);
      }

      if (exportType.equalsIgnoreCase("PDF")) {
         PdfBuilder pdfView = new PdfBuilder();
         return new ModelAndView(pdfView, dataMap);
      } else {
         this.downloadService = new DeviceStatisticsDownloadService();
         this.downloadService.downloadExcelFile(dataMap, response);
         return null;
      }
   }

   public ModelAndView expiryDateDeviceExport(String exportType, String searchText, String sortColumn, String sortOrder, HttpServletResponse response, String localeData) {
      new SecurityUtils();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (StrUtils.nvl(sortColumn).equals("")) {
         sortColumn = "stop_date";
      }

      if (StrUtils.nvl(sortOrder).equals("")) {
         sortOrder = "desc";
      }

      if (StrUtils.nvl(searchText).equals("")) {
         searchText = "";
      }

      if (StrUtils.nvl(exportType).equals("")) {
         exportType = "EXCEL";
      }

      SelectCondition condition = new SelectCondition();
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      condition.setSrc_name(searchText);
      condition.setOrder_dir(sortOrder);
      condition.setSort_name(sortColumn);
      String fileExtension = "xls";
      if (exportType.toUpperCase().equals("PDF")) {
         fileExtension = "pdf";
      }

      Timestamp nowTime = new Timestamp(System.currentTimeMillis());
      Map dataMap = new HashMap();
      if (StrUtils.nvl(localeData).equals("")) {
         String userLocale = SecurityUtils.getUserContainer().getUser().getLocale();
         if (userLocale != null && !userLocale.equalsIgnoreCase("")) {
            localeData = userLocale;
         } else {
            localeData = "en";
         }
      }

      Locale locale = new Locale(localeData);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      ScheduleInfo scheduleDao = ScheduleInfoImpl.getInstance();
      Object[] dataList = null;
      String fileName = "SchedulesToExpireByDevice" + nowTime.toString() + "." + fileExtension;
      String sheetName = rms.getMessage("MIS_SID_SERVER_SCHEDULES_TO_EXPIRE", (Object[])null, locale) + " " + rms.getMessage("TEXT_DEVICE_P", (Object[])null, locale);
      String[] columnNames = new String[]{"device_name", "device_type", "device_type_version", "device_id", "ip_address", "device_model_name", "device_group_name", "device_group_id", "program_name", "stop_date"};
      String str_device_name = rms.getMessage("TABLE_DEVICE_NAME_P", (Object[])null, locale);
      String str_device_type = rms.getMessage("MIS_TEXT_DEVICE_TYPE_P", (Object[])null, locale);
      String str_device_type_version = rms.getMessage("TABLE_VERSION_P", (Object[])null, locale);
      String str_device_id = rms.getMessage("TABLE_MAC_ADDR_P", (Object[])null, locale);
      String str_ip_address = rms.getMessage("COM_SID_IP_ADDRESS", (Object[])null, locale);
      String str_device_model_name = rms.getMessage("TABLE_DEVICE_MODEL_NAME_P", (Object[])null, locale);
      String str_device_group_name = rms.getMessage("MESSAGE_STATISTICS_TABLE_COLUMN_CONNECTION_GROUP_NAME_P", (Object[])null, locale);
      String str_device_group_id = rms.getMessage("COM_SID_SIGANGE_GROUP_CODE", (Object[])null, locale);
      String str_program_name = rms.getMessage("TEXT_SCHEDULE_NAME_P", (Object[])null, new Locale(locale.getLanguage()));
      String str_stop_date = rms.getMessage("COM_EXPIRATION_DATE_KR_DATE", (Object[])null, locale);
      String[] fieldNames = new String[]{str_device_name, str_device_type, str_device_type_version, str_device_id, str_ip_address, str_device_model_name, str_device_group_name, str_device_group_id, str_program_name, str_stop_date};
      String userId = userContainer.getUser().getUser_id();
      ProgramGroupInfoImpl programGroupInfo = ProgramGroupInfoImpl.getInstance();

      try {
         int programGroupId = programGroupInfo.getProgramGroupForOrg(userContainer.getUser().getOrganization());
         Date date = new Date();
         Calendar c = Calendar.getInstance();
         c.setTime(date);
         SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
         String stopDate = sdf.format(c.getTime());
         List list = scheduleDao.getDeviceListByScheduleToExpire(-1, -1, userId, (long)programGroupId, stopDate, condition);
         int dataListSize = false;
         if (list != null) {
            int dataListSize = list.size();
            dataList = new Object[dataListSize];

            for(int index = 0; index < dataListSize; ++index) {
               Map item = (Map)list.get(index);
               AlarmRuleSchedulesToExpireByDevice alarmRuleSchedulesToExpireByDevice = new AlarmRuleSchedulesToExpireByDevice();
               alarmRuleSchedulesToExpireByDevice.setDevice_name(item.get("device_name").toString());
               alarmRuleSchedulesToExpireByDevice.setDevice_type(item.get("device_type").toString());
               alarmRuleSchedulesToExpireByDevice.setDevice_type_version(item.get("device_type_version").toString());
               alarmRuleSchedulesToExpireByDevice.setDevice_id(item.get("device_id").toString());
               alarmRuleSchedulesToExpireByDevice.setIp_address(item.get("ip_address").toString());
               alarmRuleSchedulesToExpireByDevice.setDevice_model_name(item.get("device_model_name").toString());
               alarmRuleSchedulesToExpireByDevice.setDevice_group_name(item.get("device_group_name").toString());
               alarmRuleSchedulesToExpireByDevice.setDevice_group_id(item.get("device_group_id").toString());
               alarmRuleSchedulesToExpireByDevice.setProgram_name(item.get("program_name").toString());
               alarmRuleSchedulesToExpireByDevice.setStop_date(item.get("stop_date").toString());
               dataList[index] = alarmRuleSchedulesToExpireByDevice;
            }
         }

         dataMap.put("fileName", fileName);
         dataMap.put("sheetName", sheetName);
         dataMap.put("columnNames", columnNames);
         dataMap.put("fieldNames", fieldNames);
         dataMap.put("dataList", dataList);
      } catch (Exception var42) {
         this.logger.error("", var42);
      }

      if (exportType.equalsIgnoreCase("PDF")) {
         PdfBuilder pdfView = new PdfBuilder();
         return new ModelAndView(pdfView, dataMap);
      } else {
         this.downloadService = new DeviceStatisticsDownloadService();
         this.downloadService.downloadExcelFile(dataMap, response);
         return null;
      }
   }

   public ModelAndView deviceExport(V2DeviceFilter filter, String exportType, String category, HttpServletResponse response, String localeData) throws Exception {
      new SecurityUtils();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String var10000;
      if (StrUtils.nvl(filter.getProductType()).equals("")) {
         var10000 = "PREMIUM";
      } else {
         filter.getProductType();
      }

      DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
      TagInfo tagDao = TagInfoImpl.getInstance();
      String groupId = StrUtils.nvl(filter.getGroupId()).equals("") ? deviceGroupDao.getOrganGroupIdByName(userContainer.getUser().getOrganization()) + "" : filter.getGroupId();
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, Long.valueOf(groupId));
      String connectionStatus = StrUtils.nvl(filter.getConnectionStatus()).equals("") ? "device_status_view_all" : filter.getConnectionStatus();
      if (StrUtils.nvl(filter.getSortColumn()).equals("")) {
         filter.setSortColumn("device_id");
      }

      String disconnectPeriod = null;
      int lastindex = connectionStatus.lastIndexOf("_");
      if (connectionStatus.substring(0, lastindex).equals("device_status_view_disconnection")) {
         disconnectPeriod = connectionStatus.substring(lastindex + 1);
         connectionStatus = connectionStatus.substring(0, lastindex);
      }

      String expirationDate = "device_status_view_all";
      String customInputVal = "0";
      String groupIds = "";
      if (filter.getGroupIds() != null) {
         groupIds = this.convertString(filter.getGroupIds());
      }

      String tagIds = null;
      if (filter.getTagIds() != null) {
         tagIds = this.convertString(filter.getTagIds());
      }

      String alarmTypes = "";
      if (filter.getAlarmTypes() != null) {
         alarmTypes = this.convertString(filter.getAlarmTypes());
      }

      String functionTypes = "";
      if (filter.getFunctionTypes() != null) {
         functionTypes = this.convertString(filter.getFunctionTypes());
      }

      String searchText = StrUtils.nvl(filter.getSearchText()).equals("") ? "" : filter.getSearchText();
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      String inputSources = "";
      if (filter.getInputSources() != null) {
         inputSources = this.convertString(filter.getInputSources());
      }

      boolean isRoot = false;
      Long rootGroupId = userContainer.getUser().getRoot_group_id();
      if (rootGroupId.equals(0L)) {
         isRoot = true;
      }

      if (StrUtils.nvl(filter.getSearchId()).equals("")) {
         var10000 = "-1";
      } else {
         filter.getSearchId();
      }

      String deviceType = "";
      if (filter.getDeviceType() != null) {
         deviceType = this.convertString(filter.getDeviceType());
      }

      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      ListManager listMgr = new ListManager(deviceDao, "commonlist");
      SelectCondition condition = new SelectCondition();
      condition.setSort_name(filter.getSortColumn());
      condition.setOrder_dir(filter.getSortOrder());
      condition.setGroup_id(Long.valueOf(groupId));
      condition.setSrc_name(searchText);
      condition.setIsRoot(isRoot);
      condition.setStatus_view_mode(connectionStatus);
      if (disconnectPeriod != null) {
         condition.setDisconn_period(disconnectPeriod);
      }

      condition.setExpiration_date(expirationDate);
      condition.setCustom_input_val(customInputVal);
      condition.setFilter_group_ids(groupIds);
      condition.setRole_name(userContainer.getUser().getRole_name());
      condition.setUser_id(userContainer.getUser().getUser_id());
      condition.setTagFilter(tagIds);
      condition.setSourceFilter(inputSources);
      if (StringUtils.isNotBlank(alarmTypes)) {
         condition.setAlarmFiltersByString(alarmTypes);
      }

      if (StringUtils.isNotBlank(functionTypes)) {
         condition.setFunctionFiltersByString(functionTypes);
      }

      if (StringUtils.isNotBlank(searchText)) {
         condition.setCommonSearchKeyword(searchText);
      }

      if (deviceType != null && !deviceType.equals("")) {
         condition.setDevice_type(deviceType);
      }

      if (StringUtils.isNotBlank(condition.getSourceFilter())) {
         List sourceList = new ArrayList();
         String[] arrSourceFilter = condition.getSourceFilter().split(",");
         String[] var31 = arrSourceFilter;
         int var32 = arrSourceFilter.length;

         for(int var33 = 0; var33 < var32; ++var33) {
            String tag = var31[var33];
            sourceList.add(Long.parseLong(tag));
         }

         condition.setSourceFilterList(sourceList);
      }

      String fileExtension = exportType.toUpperCase().equals("PDF") ? "pdf" : "xls";
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      User user = SecurityUtils.getUserContainer().getUser();
      HashMap dataMap = new HashMap();

      try {
         if (StrUtils.nvl(localeData).equals("")) {
            String userLocale = SecurityUtils.getUserContainer().getUser().getLocale();
            if (userLocale != null && !userLocale.equalsIgnoreCase("")) {
               localeData = userLocale;
            } else {
               localeData = "en";
            }
         }

         new Locale(localeData);
         Map exportColumns = new LinkedHashMap();
         exportColumns.put("device_name", "Device Name");
         exportColumns.put("device_id", "Mac Address");
         exportColumns.put("group_name", "Group Name");
         exportColumns.put("ip_address", "IP address");
         exportColumns.put("device_type", "Device Type");
         exportColumns.put("device_type_version", "Device Type Version");
         exportColumns.put("organization", "Organization");
         exportColumns.put("tag", "Tag");
         if (!StringUtils.isEmpty(category) && !"DEVICE_INFORMATION".equals(category)) {
            if ("DEVICE_TIME".equals(category)) {
               exportColumns.put("timer1", "Timer1");
               exportColumns.put("timer2", "Timer2");
               exportColumns.put("timer3", "Timer3");
               exportColumns.put("timer4", "Timer4");
               exportColumns.put("timer5", "Timer5");
               exportColumns.put("timer6", "Timer6");
               exportColumns.put("timer7", "Timer7");
            } else if ("DEVICE_SETUP".equals(category)) {
               exportColumns.put("time_zone_index", "Time Zone");
               exportColumns.put("magicinfo_server_url", "MagicInfo Server URL");
               exportColumns.put("ftp_connect_mode", "FTP Connection Mode");
               exportColumns.put("connection_limit_time", "Connection Time Limit");
               exportColumns.put("bandwidth", "Network Bandwidth Limit");
               exportColumns.put("contents_download_mode", "Content Download Protocol");
               exportColumns.put("trigger_interval", "Trigger Interval");
               exportColumns.put("monitoring_interval", "Monitoring Interval");
               exportColumns.put("screen_capture_interval", "Screen Capture Interval");
               exportColumns.put("system_restart_interval", "System Restart Interval");
               exportColumns.put("screen_rotation", "Screen Rotation");
               exportColumns.put("rm_data_setting", "Device Status Data Settings");
            } else if ("DEVICE_DISPLAY".equals(category)) {
               exportColumns.put("basic_panel_status", "Panel Status");
               exportColumns.put("basic_source", "Source");
               exportColumns.put("basic_volume", "Volume");
               exportColumns.put("basic_mute", "Mute");
               exportColumns.put("diagnosis_panel_on_time", "Panel On Time");
               exportColumns.put("diagnosis_panel_on_time", "Panel On Time");
               exportColumns.put("web_browser_url", "Web Browser");
               exportColumns.put("custom_logo", "Custom Logo");
               exportColumns.put("misc_osd", "OSD");
               exportColumns.put("advanced_osd_display_type", "OSD Display Control");
               exportColumns.put("diagnosis_monitor_temperature", "Current Temperature");
               exportColumns.put("diagnosis_alarm_temperature", "Temperature Control");
               exportColumns.put("advanced_fan_speed", "Fan Speed Setting");
               exportColumns.put("specialized_picture_mode", "Specialized Picture Mode");
               exportColumns.put("pv_contrast", "Contrast");
               exportColumns.put("pv_brightness", "Brightness");
               exportColumns.put("pv_sharpness", "Sharpness");
               exportColumns.put("pv_color", "Color");
               exportColumns.put("pv_tint", "Tint (G/R)");
               exportColumns.put("pv_colortone", "Color Tone");
               exportColumns.put("pv_color_temperature", "Color Temperature");
               exportColumns.put("pv_size", "Picture Size");
               exportColumns.put("pv_digitalnr", "Digital Clean View");
               exportColumns.put("pv_filmmode", "Film Mode");
               exportColumns.put("pv_hdmi_black_level", "HDMI Black Level");
               exportColumns.put("sound_mode", "Sound Mode");
               exportColumns.put("mnt_auto", "Screen Lamp Schedule");
            } else if ("DEVICE_SECURITY".equals(category)) {
               exportColumns.put("mnt_safety_lock", "Safety Lock");
               exportColumns.put("misc_remocon", "Remote Control Lock");
               exportColumns.put("misc_panel_lock", "Button Lock");
               exportColumns.put("misc_all_lock", "All Keys Lock");
               exportColumns.put("touch_control_lock", "Touch Control Lock");
               exportColumns.put("capture_lock", "Screen Capture");
               exportColumns.put("misc_server_network_setting", "Server Network Setting Lock");
               exportColumns.put("misc_block_usb_port", "USB Lock");
               exportColumns.put("bluetooth_lock", "Bluetooth Lock");
               exportColumns.put("wifi_lock", "Wi-Fi Lock");
               exportColumns.put("source_lock", "Source Lock");
               exportColumns.put("screen_monitoring_lock", "Screen Monitoring Lock");
               exportColumns.put("remote_control_server_lock", "Remote Control Server Lock");
               exportColumns.put("misc_block_network_connection", "Network Lock");
               exportColumns.put("misc_white_list", "Whitelist");
            }
         } else {
            exportColumns.put("device_model_name", "Device Model Name");
            exportColumns.put("location", "Location");
            exportColumns.put("serial_decimal", "Device Serial");
            exportColumns.put("application_version", "Applied Version");
            exportColumns.put("os_image_version", "OS Image Version");
            exportColumns.put("player_version", "Player Version");
            exportColumns.put("cpu_type", "CPU Type");
            exportColumns.put("mem_size", "Memory Size");
            exportColumns.put("hdd_size", "Storage Size");
            exportColumns.put("disk_space_usage", "Used Storage Space");
            exportColumns.put("disk_space_repository", "Available Capacity");
            exportColumns.put("screen_size", "Screen Size");
            exportColumns.put("resolution", "Resolution");
            exportColumns.put("ip_setting_type", "IP Setting Type");
            exportColumns.put("subnet_mask", "Subnet Mask");
            exportColumns.put("gateway", "Gateway");
            exportColumns.put("dns_server_main", "Dns Server(Main)");
            exportColumns.put("dns_server_sub", "Dns Server(Sub)");
            exportColumns.put("create_date", "Registered");
            exportColumns.put("last_connection_time", "Last Check Time");
         }

         Timestamp nowTime = new Timestamp(System.currentTimeMillis());
         String fileName = "DeviceList_" + nowTime.toString() + "." + fileExtension;
         String sheetName = "DeviceList";
         String[] columnNames = (String[])exportColumns.keySet().toArray(new String[exportColumns.size()]);
         String[] fieldNames = (String[])exportColumns.values().toArray(new String[exportColumns.size()]);
         listMgr.addSearchInfo("condition", condition);
         listMgr.setLstSize(Integer.valueOf(filter.getPageSize()));
         listMgr.setSection("getApprovedDeviceListWithFilter");
         List deviceList = listMgr.V2dbexecute(filter.getStartIndex(), filter.getPageSize());
         DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
         List dataList = new ArrayList();
         Set keys = exportColumns.keySet();
         DeviceDisplayConfManager displayMgr = DeviceDisplayConfManagerImpl.getInstance();
         DeviceTimeConfManager timeMgr = DeviceTimeConfManagerImpl.getInstance();
         DeviceSecurityConfManager securityMgr = DeviceSecurityConfManagerImpl.getInstance();
         if (deviceList != null) {
            Iterator var47 = deviceList.iterator();

            label217:
            while(var47.hasNext()) {
               DeviceGeneralConf device = (DeviceGeneralConf)var47.next();
               Device dev = null;
               DeviceDisplayConf display = null;
               DeviceSecurityConf security = null;
               List time = null;
               if ("DEVICE_INFORMATION".equals(category) || "DEVICE_SETUP".equals(category)) {
                  dev = deviceDao.getDevice(device.getDevice_id());
               }

               if ("DEVICE_TIME".equals(category)) {
                  time = timeMgr.getDeviceTimeTimerConf(device.getDevice_id());
               }

               if ("DEVICE_DISPLAY".equals(category)) {
                  display = displayMgr.getDeviceDisplayConf(device.getDevice_id(), true);
               }

               if ("DEVICE_SECURITY".equals(category)) {
                  security = securityMgr.getDeviceSecurityConf(device.getDevice_id(), true);
               }

               if (!"iPLAYER".equalsIgnoreCase(device.getDevice_type()) && !"APLAYER".equalsIgnoreCase(device.getDevice_type())) {
                  if ("WPLAYER".equalsIgnoreCase(device.getDevice_type())) {
                     device.setFirmware_version(StrUtils.nvl(device.getApplication_version()));
                     device.setPlayer_version(StrUtils.nvl(device.getPlayer_version()));
                  } else {
                     device.setPlayer_version(StrUtils.nvl(device.getFirmware_version()));
                     device.setFirmware_version(StrUtils.nvl(device.getApplication_version()));
                  }
               } else {
                  device.setFirmware_version(StrUtils.nvl(device.getFirmware_version()));
                  device.setPlayer_version(StrUtils.nvl(device.getApplication_version()));
               }

               Map d = new HashMap();
               Iterator it = keys.iterator();

               while(true) {
                  String key;
                  Object value;
                  while(true) {
                     if (!it.hasNext()) {
                        d.put("organization", groupDao.getOrgNameByGroupId(device.getGroup_id()));
                        d.put("group_name", groupDao.getGroupNameByGroupId(device.getGroup_id()));
                        List tagList = tagDao.getTagValueListByDeviceId(device.getDevice_id());
                        List tagValues = new ArrayList();
                        Iterator var57 = tagList.iterator();

                        while(var57.hasNext()) {
                           Map tag = (Map)var57.next();
                           tagValues.add(tag.get("tag_value"));
                        }

                        d.put("tag", String.join(", ", tagValues));
                        dataList.add(d);
                        continue label217;
                     }

                     key = (String)it.next();
                     value = ClassUtil.getMemberVariable(device, key);
                     if (value == null && dev != null) {
                        value = ClassUtil.getMemberVariable(dev, key);
                     }

                     if (value == null && display != null) {
                        value = ClassUtil.getMemberVariable(display, key);
                     }

                     if (value == null && security != null) {
                        value = ClassUtil.getMemberVariable(security, key);
                     }

                     if (value == null || !"FLIP".equals(device.getDevice_type())) {
                        break;
                     }

                     if (DeviceUtils.isSupported(key, device.getDevice_type())) {
                        if ("misc_block_network_connection".equals(key) || "misc_block_usb_port".equals(key)) {
                           if ((Long)value == 0L) {
                              value = 1L;
                           } else if ((Long)value == 1L) {
                              value = 0L;
                           }
                        }
                        break;
                     }
                  }

                  if (key.startsWith("timer")) {
                     d.put(key, DeviceUtils.convertBeautifiedString(key, time));
                  } else {
                     d.put(key, DeviceUtils.convertBeautifiedString(key, value));
                  }
               }
            }
         }

         dataMap.put("fileName", fileName);
         dataMap.put("sheetName", sheetName);
         dataMap.put("columnNames", columnNames);
         dataMap.put("fieldNames", fieldNames);
         dataMap.put("dataList", dataList.toArray());
      } catch (SQLException var59) {
         this.logger.error("", var59);
      } catch (Exception var60) {
         this.logger.error("", var60);
      }

      if (exportType.equalsIgnoreCase("PDF")) {
         PdfBuilder pdfView = new PdfBuilder();
         return new ModelAndView(pdfView, dataMap);
      } else {
         this.downloadService = new DeviceStatisticsDownloadService();
         this.downloadService.downloadExcelFile(dataMap, response);
         return null;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public void customizeDownload(String param_filepath, HttpServletRequest request, HttpServletResponse response) throws Exception {
      BufferedOutputStream os = null;
      FileInputStream fileIS = null;
      FileChannel fileChannel = null;

      try {
         String filepath;
         try {
            request.setCharacterEncoding("UTF-8");
            BufferedReader br = request.getReader();
            filepath = br.readLine();
            String topPath = CommonConfig.get("UPLOAD_HOME");
            if (filepath == null || filepath.equals("")) {
               filepath = StrUtils.nvl(param_filepath);
            }

            filepath = filepath.replace("\\", "/");
            String[] tmp = filepath.split("/");
            topPath = topPath.replace("\\", "/");
            String realname = filepath;
            if (tmp != null && tmp.length > 1) {
               String var10000 = tmp[tmp.length - 2];
               realname = tmp[tmp.length - 1];
            }

            URLEncoder.encode(realname, "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(realname, "UTF-8") + ";");
            response.setHeader("Content-Type", "application/octet-stream; charset=UTF-8");
            int dotIdx = realname.lastIndexOf(".");
            String extension = realname.substring(dotIdx + 1, realname.length());
            String contentType = FileLoaderServlet.getContentType(extension.toLowerCase());
            response.setContentType(contentType);
            os = new BufferedOutputStream(response.getOutputStream());
            String fullpath = topPath + "/" + filepath;
            fullpath = SecurityUtils.directoryTraversalChecker(fullpath, request.getRemoteAddr());
            File m_file = SecurityUtils.getSafeFile(fullpath);
            fileIS = new FileInputStream(m_file);
            fileChannel = fileIS.getChannel();
            String fileoffset = "0";
            long fileOffsetLong = Long.parseLong(fileoffset);
            int binaryRead;
            if (m_file.length() > 0L) {
               for(ByteBuffer buf = ByteBuffer.allocate(1024); (binaryRead = fileChannel.read(buf, fileOffsetLong)) != -1; fileOffsetLong += (long)binaryRead) {
                  buf.flip();
                  os.write(buf.array(), 0, binaryRead);
                  buf.clear();
               }
            }

            os.close();
            fileChannel.close();
            fileIS.close();
         } catch (FileNotFoundException var30) {
            filepath = request.getParameter("id");
            if (!StringUtil.isEmpty(filepath)) {
               ContentInfo contentInfo = ContentInfoImpl.getInstance();
               contentInfo.deleteFileInfoIfNoExistFile(filepath);
            }
         } catch (Exception var31) {
            Exception e = var31;

            try {
               if (!(e instanceof ClientAbortException)) {
                  response.sendError(609, CMSExceptionCode.APP609[2]);
                  this.logger.error("", e);
               }
            } catch (Exception var29) {
               this.logger.error("", var29);
            }
         }
      } finally {
         if (os != null) {
            os.close();
         }

         if (fileChannel != null) {
            fileChannel.close();
         }

         if (fileIS != null) {
            fileIS.close();
         }

      }

   }

   public ModelAndView ledCabinetsExport(String deviceId, HttpServletResponse response, String localeData) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      SelectCondition condition = new SelectCondition();
      condition.setDevice_id(deviceId);
      Map dataMap = new HashMap();
      if (StrUtils.nvl(localeData).equals("")) {
         String userLocale = SecurityUtils.getUserContainer().getUser().getLocale();
         if (userLocale != null && !userLocale.equalsIgnoreCase("")) {
            localeData = userLocale;
         } else {
            localeData = "en";
         }
      }

      Locale locale = new Locale(localeData);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      if (deviceId == null) {
         return null;
      } else {
         DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
         Device device = deviceInfo.getDevice(deviceId);
         if (device == null) {
            return null;
         } else {
            Timestamp nowTime = new Timestamp(System.currentTimeMillis());
            String fileName = "CabinetList_" + nowTime.toString() + ".xls";
            String sheetName = "CabinetList_" + device.getDevice_name();
            LedCabinetConfManager ledCabinetConfMgr = LedCabinetConfManagerImpl.getInstance();
            ListManager listMgr = new ListManager(ledCabinetConfMgr, "commonlist");
            condition.setSort_name("CABINET_ID");
            condition.setOrder_dir("ASC");
            listMgr.addSearchInfo("condition", condition);
            listMgr.setSection("getLedCabinetList");
            PageManager pageMgr = listMgr.getPageManager();
            List ledCabinets = listMgr.dbexecute();
            String[] fieldNames = new String[]{rms.getMessage("TEXT_GROUP_P", (Object[])null, locale), rms.getMessage("DID_SCHEDULE_ID", (Object[])null, locale), rms.getMessage("TABLE_RESOLUTION_P", (Object[])null, locale), rms.getMessage("MIS_SID_SERVER_PHYSICAL_SIZE", (Object[])null, locale), rms.getMessage("TEXT_ASPECT_RATIO_P", (Object[])null, locale), rms.getMessage("MIS_SID_SERVER_MOBULES", (Object[])null, locale), rms.getMessage("MIS_SID_SERVER_PITCH", (Object[])null, locale), rms.getMessage("DID_LFD_TEMPERATURE", (Object[])null, locale), rms.getMessage("COM_LFD_SID_NON_IC", (Object[])null, locale) + "(FPGA)", rms.getMessage("COM_LFD_SID_NON_IC", (Object[])null, locale) + "(PowerDetectIc)", rms.getMessage("TABLE_POWER_P", (Object[])null, locale) + "(5.0v)", rms.getMessage("TABLE_POWER_P", (Object[])null, locale) + "(3.3v)", rms.getMessage("TABLE_POWER_P", (Object[])null, locale) + "(1.8v)", rms.getMessage("TABLE_POWER_P", (Object[])null, locale) + "(1.2v)", rms.getMessage("TABLE_POWER_P", (Object[])null, locale) + "(4.2v)", rms.getMessage("TABLE_POWER_P", (Object[])null, locale) + "(13v)", "Led Open Detection(M1)", "Led Open Detection(M2)", "Led Open Detection(M3)", "Led Open Detection(M4)", "Led Open Detection(M5)", "Led Open Detection(M6)", rms.getMessage("TABLE_FIRMWARE_VERSION_P", (Object[])null, locale), "FPGA Version", rms.getMessage("MIS_TEXT_LAST_CHECK_TIME_P", (Object[])null, locale), rms.getMessage("MIS_SID_MODULE_PRODUCT_NUMBER", (Object[])null, locale), rms.getMessage("MIS_SID_DATE_OF_MODULE_PRODUCTION", (Object[])null, locale)};
            String[] columnNames = new String[]{"cabinet_group_id", "cabinet_id", "resolution", "phy_size", "aspect_ratio", "modules", "pitch", "current_temparature_with_f", "ic_fpga", "ic_powerDetectIc", "voltage_status_50", "voltage_status_33", "voltage_status_18", "voltage_status_12", "voltage_status_42", "voltage_status_130", "led_open_detection_m1", "led_open_detection_m2", "led_open_detection_m3", "led_open_detection_m4", "led_open_detection_m5", "led_open_detection_m6", "fw_version", "fpga_version", "last_scanned_time_timestamp", "module_product_number", "module_date"};
            int dataListSize = false;
            Object[] dataList = null;
            if (ledCabinets != null) {
               Iterator var21 = ledCabinets.iterator();

               while(var21.hasNext()) {
                  LedCabinet cabinet = (LedCabinet)var21.next();
                  cabinet.convertUserFriendlyFormat();
               }

               int dataListSize = ledCabinets.size();
               dataList = new Object[dataListSize];

               for(int index = 0; index < dataListSize; ++index) {
                  dataList[index] = ledCabinets.get(index);
               }
            }

            dataMap.put("fileName", fileName);
            dataMap.put("sheetName", sheetName);
            dataMap.put("columnNames", columnNames);
            dataMap.put("fieldNames", fieldNames);
            dataMap.put("dataList", dataList);
            this.downloadService = new DeviceStatisticsDownloadService();
            this.downloadService.downloadExcelFile(dataMap, response);
            return null;
         }
      }
   }

   public void downloadResponse(String topPath, String jnlpFileName, HttpServletRequest request, HttpServletResponse response) throws Exception {
      URLEncoder.encode(jnlpFileName, "UTF-8");
      response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(jnlpFileName, "UTF-8") + ";");
      response.setHeader("Content-Type", "application/octet-stream; charset=UTF-8");
      int dotIdx = jnlpFileName.lastIndexOf(".");
      String extension = jnlpFileName.substring(dotIdx + 1, jnlpFileName.length());
      String contentType = FileLoaderServlet.getContentType(extension.toLowerCase());
      response.setContentType(contentType);
      String fullpath = topPath + "/" + jnlpFileName;
      fullpath = SecurityUtils.directoryTraversalChecker(fullpath, request.getRemoteAddr());
      BufferedOutputStream os = null;
      File m_file = null;
      FileInputStream fileIS = null;
      FileChannel fileChannel = null;

      try {
         os = new BufferedOutputStream(response.getOutputStream());
         m_file = SecurityUtils.getSafeFile(fullpath);
         fileIS = new FileInputStream(m_file);
         fileChannel = fileIS.getChannel();
         long fileOffsetLong = 0L;
         int binaryRead;
         if (m_file.length() > 0L) {
            for(ByteBuffer buf = ByteBuffer.allocate(1024); (binaryRead = fileChannel.read(buf, fileOffsetLong)) != -1; fileOffsetLong += (long)binaryRead) {
               buf.flip();
               os.write(buf.array(), 0, binaryRead);
               buf.clear();
            }
         }
      } catch (IOException var20) {
         this.logger.error(var20);
      } finally {
         if (os != null) {
            os.close();
         }

         if (fileChannel != null) {
            fileChannel.close();
         }

         if (fileIS != null) {
            fileIS.close();
         }

      }

   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2CommonBulkResultResource getFlipInfo(V2CommonIds deviceIds) throws Exception {
      boolean checkFlag = false;
      Iterator var3 = deviceIds.getIds().iterator();

      while(var3.hasNext()) {
         String deviceId = (String)var3.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var13) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
      Iterator var7 = deviceIds.getIds().iterator();

      while(var7.hasNext()) {
         String deviceId = (String)var7.next();

         try {
            V2DeviceFlipResource result = new V2DeviceFlipResource();
            DeviceControl deviceControl = DeviceUtils.getDeviceControlInfo(deviceId, true);
            V2FlipResource flipResource = new V2FlipResource();
            flipResource.convert(deviceControl);
            result.setPower(motMgr.isConnected(deviceId));
            result.setDeviceControl(flipResource);
            if (deviceControl != null && deviceControl.getGeneral() != null) {
               result.setDeviceType(deviceControl.getGeneral().getDevice_type());
               result.setDeviceTypeVersion(deviceControl.getGeneral().getDevice_type_version());
            }

            result.setInit(true);
            successList.add(result);
         } catch (Exception var12) {
            this.logger.error("[REST_v2.0][DEVICE SERVICE][getFlipInfo]", var12);
            failList.add(deviceId);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceReqServiceResource reqSetCommonToDevice(V2DeviceFlipConf body) throws Exception {
      List deviceIds = body.getIds();
      boolean checkFlag = false;
      Iterator var4 = deviceIds.iterator();

      String requestId;
      while(var4.hasNext()) {
         requestId = (String)var4.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, requestId);
         } catch (Exception var18) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
      requestId = UUID.randomUUID().toString();
      V2DeviceReqServiceResource resource = new V2DeviceReqServiceResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      Iterator var9 = deviceIds.iterator();

      while(var9.hasNext()) {
         String deviceId = (String)var9.next();
         if (!DeviceUtils.isConnected(deviceId)) {
            resource.setFailList(deviceIds);
            this.logger.error("The device is not connected.");
            return resource;
         }
      }

      Map deviceConf = new HashMap();
      if (!StrUtils.nvl(body.getDeviceName()).equals("")) {
         deviceConf.put("device_name", body.getDeviceName());
      }

      if (!StrUtils.nvl(body.getBasicPower()).equals("")) {
         deviceConf.put("basic_power", body.getBasicPower());
      }

      if (!StrUtils.nvl(body.getCleanupUserData()).equals("")) {
         deviceConf.put("cleanup_user_data", body.getCleanupUserData());
      }

      if (!StrUtils.nvl(body.getCleanupUserDataInterval()).equals("")) {
         deviceConf.put("cleanup_user_data_interval", body.getCleanupUserDataInterval());
      }

      if (!StrUtils.nvl(body.getAutoSave()).equals("")) {
         deviceConf.put("auto_save", body.getAutoSave());
      }

      if (!StrUtils.nvl(body.getAutoPowerOff()).equals("")) {
         deviceConf.put("auto_power_off", body.getAutoPowerOff());
      }

      if (null != body.getSmtp() && !StrUtils.nvl(body.getSmtp().toString()).equals("")) {
         deviceConf.put("smtp", body.getSmtp().toString());
      }

      if (null != body.getPrintServer() && !StrUtils.nvl(body.getPrintServer().toString()).equals("")) {
         deviceConf.put("print_server", body.getPrintServer().toString());
      }

      if (!StrUtils.nvl(body.getProxySetting()).equals("")) {
         deviceConf.put("proxy_setting", body.getProxySetting());
      }

      if (!StrUtils.nvl(body.getMiscBlockNetworkConnection()).equals("")) {
         deviceConf.put("misc_block_network_connection", body.getMiscBlockNetworkConnection());
      }

      if (!StrUtils.nvl(body.getMiscBlockUsbPort()).equals("")) {
         deviceConf.put("misc_block_usb_port", body.getMiscBlockUsbPort());
      }

      if (!StrUtils.nvl(body.getCaptureLock()).equals("")) {
         deviceConf.put("capture_lock", body.getCaptureLock());
      }

      if (!StrUtils.nvl(body.getPinCode()).equals("")) {
         deviceConf.put("pin_code", body.getPinCode());
      }

      if (!StrUtils.nvl(body.getScreenMonitoringLock()).equals("")) {
         deviceConf.put("screen_monitoring_lock", body.getScreenMonitoringLock());
      }

      if (!StrUtils.nvl(body.getRemoteControlServerLock()).equals("")) {
         deviceConf.put("remote_control_server_lock", body.getRemoteControlServerLock());
      }

      boolean sentMo = false;
      if (deviceConf != null && deviceConf.size() > 0) {
         String pinCode = null;
         if (deviceConf.get("pin_code") != null) {
            pinCode = (String)deviceConf.get("pin_code");
         }

         Iterator var12 = deviceIds.iterator();

         while(var12.hasNext()) {
            String deviceId = (String)var12.next();
            if (pinCode != null) {
               try {
                  String key = SecurityUtils.getPincodeEncryptionKey(deviceId);
                  AES256Cipher a256 = new AES256Cipher(key);
                  deviceConf.put("pin_code", a256.aesEncode(pinCode));
               } catch (Exception var16) {
                  this.logger.error("Pin Code Encryption Failed", var16);
               }
            }

            try {
               if (confManager.reqSetCommonConfToDevice(deviceConf, deviceId, requestId) != null) {
                  sentMo = true;
               }
            } catch (Exception var17) {
               this.logger.error("[REST_v2.0][DEVICE SERVICE][reqSetCommonToDevice]", var17);
               failList.add(deviceId);
               continue;
            }

            if (deviceConf.containsKey("cleanup_user_data")) {
               sentMo = true;
               confManager.reqGetDevicePredefinedCmd(deviceId, "CLEANUP_USER_DATA", requestId);
            }

            if (deviceConf.containsKey("remote_control_server_lock")) {
               DeviceSecurityConfManager securityDao = DeviceSecurityConfManagerImpl.getInstance("PREMIUM");
               DeviceSecurityConf securityConf = new DeviceSecurityConf();
               securityConf.setDevice_id(deviceId);
               securityConf.setRemote_control_server_lock(Long.parseLong((String)deviceConf.get("remote_control_server_lock")));
               securityDao.setDeviceSecurityConf(securityConf);
            }

            successList.add(deviceId);
         }

         resource.setSuccessList(successList);
         resource.setFailList(failList);
         if (successList.size() > 0 && sentMo) {
            resource.setRequestId(requestId);
         }

         return resource;
      } else {
         this.logger.info("[REST_v2.0][DEVICE SERVICE][reqSetCommonToDevice] The fix does not exist.");
         resource.setFailList(deviceIds);
         return resource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2CommonBulkResultResource reqGetCommonCurrentStatus(V2CommonIds body) throws Exception {
      List deviceIds = body.getIds();
      boolean checkFlag = false;
      Iterator var4 = deviceIds.iterator();

      while(var4.hasNext()) {
         String deviceId = (String)var4.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var14) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      V2DeviceReqServiceResource resource = new V2DeviceReqServiceResource();
      Iterator var16 = deviceIds.iterator();

      String sessionId;
      while(var16.hasNext()) {
         sessionId = (String)var16.next();
         if (!DeviceUtils.isConnected(sessionId)) {
            resource.setFailList(deviceIds);
            this.logger.error("The device is not connected.");
            return resource;
         }
      }

      DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
      sessionId = UUID.randomUUID().toString();
      List successList = new ArrayList();
      List failList = new ArrayList();
      Map deviceConf = new HashMap();
      Iterator var10 = deviceIds.iterator();

      while(var10.hasNext()) {
         String deviceId = (String)var10.next();

         try {
            confManager.reqGetCommonConfToDevice(deviceConf, deviceId, sessionId);
            successList.add(deviceId);
         } catch (Exception var13) {
            this.logger.error("[REST_v2.0][DEVICE SERVICE][reqGetCommonCurrentStatus] The request failed with unknown error.");
            failList.add(deviceId);
         }
      }

      resource.setRequestId(sessionId);
      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceReqServiceResource reqGetCommonAllStatus(V2CommonIds body) throws Exception {
      List deviceIds = body.getIds();
      boolean checkFlag = false;
      Iterator var4 = deviceIds.iterator();

      while(var4.hasNext()) {
         String deviceId = (String)var4.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var13) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      V2DeviceReqServiceResource resource = new V2DeviceReqServiceResource();
      Iterator var15 = deviceIds.iterator();

      while(var15.hasNext()) {
         String deviceId = (String)var15.next();
         if (!DeviceUtils.isConnected(deviceId)) {
            resource.setFailList(deviceIds);
            this.logger.error("The device is not connected.");
            return resource;
         }
      }

      List successList = new ArrayList();
      List failList = new ArrayList();
      DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
      String sessionId = UUID.randomUUID().toString();
      Iterator var9 = deviceIds.iterator();

      while(var9.hasNext()) {
         String deviceId = (String)var9.next();

         try {
            confManager.reqGetAllCommonConfToDevice("all", deviceId, sessionId);
            successList.add(deviceId);
         } catch (Exception var12) {
            this.logger.error("[REST_v2.0][DEVICE SERVICE][reqGetCommonAllStatus]", var12);
            failList.add(deviceId);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      if (successList.size() > 0) {
         resource.setRequestId(sessionId);
      }

      return resource;
   }

   private String disconnectedTime(Long monitoring_interval, Timestamp lastConnectTime) {
      String result = "";
      if (lastConnectTime == null) {
         return result;
      } else {
         Long interval_min = 0L;
         if (monitoring_interval != null) {
            interval_min = monitoring_interval;
         }

         LocalDateTime local = LocalDateTime.now();
         Timestamp now = Timestamp.valueOf(local);
         long cha = (now.getTime() - lastConnectTime.getTime()) / 1000L;
         cha -= (interval_min + 1L) * 60L;
         if (cha <= 0L) {
            return result;
         } else {
            String text_d = "Days";
            String text_h = "Hour";
            String text_m = "min.";
            String text_s = "sec";
            Long DAY = 86400L;
            Long HH = 3600L;
            Long MIN = 60L;
            long day = 0L;
            long hh = 0L;
            long min = 0L;
            long sec = 0L;
            if (cha >= DAY) {
               day = cha / DAY;
               cha %= DAY;
               result = day + " " + text_d + " ";
            }

            hh = cha / HH;
            cha %= HH;
            result = result + hh + " " + text_h + " ";
            min = cha / MIN;
            sec = cha % MIN;
            result = result + min + " " + text_m + " " + sec + " " + text_s;
            return result;
         }
      }
   }

   private boolean saveMappingAndDeploy(String preconfigId, String groupIds) {
      try {
         int result = false;
         int result = DeviceUtils.setPreconfigGroupMap(preconfigId, groupIds);
         if (result > 0) {
            DeviceUtils.deployPreconfig(preconfigId, groupIds);
            return true;
         } else {
            this.logger.info("[GetDevicePreconfig:setPreconfigGroupMapping] fail to save and deploy preconfig to group.");
            return false;
         }
      } catch (Exception var4) {
         this.logger.error("[GetDevicePreconfig:setPreconfigGroupMapping] fail to set preconfig to group.", var4);
         return false;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public List preconfigDeployStatus(String presetId) {
      DevicePreconfigInfo preconfigInfo = DevicePreconfigInfoImpl.getInstance();
      List list = new ArrayList();
      String var4 = "success";

      try {
         List statusList = preconfigInfo.getDeployStatusByPreconfigId(presetId);
         Iterator var6 = statusList.iterator();

         while(var6.hasNext()) {
            Map statusMap = (Map)var6.next();
            V2DevicePresetDeployStatusResource resource = new V2DevicePresetDeployStatusResource();
            resource.setUpdateTime((Timestamp)statusMap.get("update_time"));
            resource.setDeviceGroupName((String)statusMap.get("device_group_name"));
            resource.setPresetId((String)statusMap.get("preconfig_id"));
            resource.setPublishStatus((Integer)statusMap.get("publish_status"));
            resource.setDeviceId((String)statusMap.get("device_id"));
            resource.setDeviceName((String)statusMap.get("device_name"));
            list.add(resource);
         }
      } catch (Exception var9) {
         this.logger.error("", var9);
      }

      return list;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public String sendPostboot(String deviceId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);

      try {
         if (!DeviceUtils.sendPostboot(deviceId)) {
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR);
         } else {
            return deviceId;
         }
      } catch (Exception var3) {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR);
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DevicePresetResultResource getDevicePreconfigResult(String deviceId, int count) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      DevicePreconfigInfo preconfigInfo = DevicePreconfigInfoImpl.getInstance();
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      V2DevicePresetResultResource resource = new V2DevicePresetResultResource();

      try {
         Map deployStatus = preconfigInfo.getDeployStatusByDeviceId(deviceId);
         if (deployStatus == null) {
            throw new FileNotFoundException();
         }

         Date reportTime = (Date)deployStatus.get("REPORT_TIME");
         Device device = deviceInfo.getDevice(deviceId);
         if (reportTime == null || reportTime.before(device.getBootstrap_time())) {
            throw new FileNotFoundException();
         }

         DevicePreconfig preconfig = preconfigInfo.getPreconfigInfoByDeviceId(deviceId);
         V2DevicePreconfig preconf = new V2DevicePreconfig();
         preconf.setPresetId(preconfig.getPreconfig_id());
         preconf.setName(preconfig.getName());
         preconf.setVersion(preconfig.getVersion());
         preconf.setDescription(preconfig.getDescription());
         preconf.setOrganizationId(preconfig.getOrganization_id());
         preconf.setOrganizationName(preconfig.getOrganization_name());
         preconf.setCreateDate(preconfig.getCreate_date());
         preconf.setUpdateTime(preconfig.getUpdate_time());
         Map preconfigResultMap = preconfigInfo.getPreconfigResultMap(deviceId, preconfig.getVersionForDevice());
         resource.setPreconfig(preconf);
         resource.setPreconfigResult(preconfigResultMap);
      } catch (FileNotFoundException var13) {
         if (count == 0 && DeviceUtils.isConnected(deviceId)) {
            confMgr.reqGetRequestPreconfigResult(deviceId, "");
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"preset file"});
         }

         if (!DeviceUtils.isConnected(deviceId)) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND);
         }
      } catch (Exception var14) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_DEVICE_NO_APPLIED_PRESET_CONFIG);
      }

      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public void logDownload(String deviceId, String script, String param_filepath, HttpServletRequest request, HttpServletResponse response) throws Exception {
      BufferedOutputStream os = null;
      FileInputStream fileIS = null;
      FileChannel fileChannel = null;

      try {
         String filepath;
         try {
            if (StringUtils.isNotEmpty(script)) {
               List deviceLogCollectEntities = this.deviceDao.getDeviceLogProcessInfo(deviceId);
               boolean checked = false;
               Iterator var38 = deviceLogCollectEntities.iterator();

               while(true) {
                  if (var38.hasNext()) {
                     DeviceLogCollectEntity logInfo = (DeviceLogCollectEntity)var38.next();
                     if (!logInfo.getCategory_script().equalsIgnoreCase(script)) {
                        continue;
                     }

                     checked = true;
                  }

                  if (!checked) {
                     return;
                  }
                  break;
               }
            }

            request.setCharacterEncoding("UTF-8");
            BufferedReader br = request.getReader();
            filepath = br.readLine();
            String topPath = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "device_log" + File.separatorChar + "downloads" + File.separatorChar;
            if ((null == script || !script.contains("_nopswd")) && StrUtils.nvl(filepath).equals("")) {
               filepath = makeProtectedZipFiles(deviceId, param_filepath);
            } else {
               filepath = getLogFilePath(deviceId, param_filepath);
               topPath = "";
            }

            filepath = filepath.replace("\\", "/");
            String[] tmp = filepath.split("/");
            topPath = topPath.replace("\\", "/");
            String realname = filepath;
            if (tmp != null && tmp.length > 1) {
               String var10000 = tmp[tmp.length - 2];
               realname = tmp[tmp.length - 1];
            }

            URLEncoder.encode(realname, "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(realname, "UTF-8") + ";");
            response.setHeader("Content-Type", "application/octet-stream; charset=UTF-8");
            int dotIdx = realname.lastIndexOf(".");
            String extension = realname.substring(dotIdx + 1, realname.length());
            String contentType = FileLoaderServlet.getContentType(extension.toLowerCase());
            response.setContentType(contentType);
            os = new BufferedOutputStream(response.getOutputStream());
            String fullpath = topPath + filepath;
            fullpath = SecurityUtils.directoryTraversalChecker(fullpath, request.getRemoteAddr());
            File m_file = SecurityUtils.getSafeFile(fullpath);
            fileIS = new FileInputStream(m_file);
            fileChannel = fileIS.getChannel();
            String fileoffset = "0";
            long fileOffsetLong = Long.parseLong(fileoffset);
            int binaryRead;
            if (m_file.length() > 0L) {
               for(ByteBuffer buf = ByteBuffer.allocate(1024); (binaryRead = fileChannel.read(buf, fileOffsetLong)) != -1; fileOffsetLong += (long)binaryRead) {
                  buf.flip();
                  os.write(buf.array(), 0, binaryRead);
                  buf.clear();
               }
            }

            os.close();
            fileChannel.close();
            fileIS.close();
         } catch (FileNotFoundException var32) {
            filepath = request.getParameter("id");
            if (!StringUtil.isEmpty(filepath)) {
               ContentInfo contentInfo = ContentInfoImpl.getInstance();
               contentInfo.deleteFileInfoIfNoExistFile(filepath);
               return;
            }
         } catch (Exception var33) {
            Exception e = var33;

            try {
               if (!(e instanceof ClientAbortException)) {
                  response.sendError(609, CMSExceptionCode.APP609[2]);
                  this.logger.error("", e);
                  return;
               }
            } catch (Exception var31) {
               this.logger.error(var31);
            }

            return;
         }

      } finally {
         if (os != null) {
            os.close();
         }

         if (fileChannel != null) {
            fileChannel.close();
         }

         if (fileIS != null) {
            fileIS.close();
         }

      }
   }

   private static String getLogFilePath(String deviceId, String fileNames) throws Exception {
      return CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "device_log" + File.separatorChar + deviceId + File.separatorChar + fileNames;
   }

   private static String makeProtectedZipFiles(String deviceId, String fileNames) throws Exception {
      String downloadPath = null;
      String DeviceLogPath = null;
      String zipFilename = "";
      Logger logger = LoggingManagerV2.getLogger(V2DeviceServiceImpl.class);

      try {
         logger.error("[MagicInfo_HTTP] Log Collect - make zip fils");
         DeviceLogPath = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "device_log";
         downloadPath = DeviceLogPath + File.separatorChar + "downloads";
         File downloadPahHome = SecurityUtils.getSafeFile(downloadPath);
         if (!downloadPahHome.exists()) {
            downloadPahHome.mkdir();
         }

         TimeUtil util = new TimeUtil();
         Timestamp startTime = DateUtils.dateTime2TimeStamp(TimeUtil.getCurrentGMTTime());
         SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
         String token = sdf.format(startTime);
         zipFilename = deviceId + "_" + token + ".zip";
         String zipFilePath = downloadPath + File.separator + zipFilename;
         ZipFile zipFile = new ZipFile(zipFilePath);
         ArrayList filesToAdd = new ArrayList();
         String[] logFileList = fileNames.split(",");
         String[] var15 = logFileList;
         int var16 = logFileList.length;

         String password;
         for(int var17 = 0; var17 < var16; ++var17) {
            password = var15[var17];
            String filePath = DeviceLogPath + File.separatorChar + deviceId + File.separatorChar + password;
            filesToAdd.add(new File(filePath));
         }

         String encryption = StrUtils.nvl(CommonConfig.get("device.log_collect.encryption"));
         String keyfilepath = null;
         if (encryption != null && encryption.equalsIgnoreCase("true")) {
            String filePath = DeviceLogPath + File.separatorChar + deviceId + File.separatorChar + "key.txt";
            File keyFile = SecurityUtils.getSafeFile(filePath);
            if (keyFile.exists()) {
               filesToAdd.add(keyFile);
            }
         }

         ZipParameters parameters = new ZipParameters();
         parameters.setCompressionMethod(8);
         parameters.setCompressionLevel(5);
         parameters.setEncryptFiles(true);
         parameters.setEncryptionMethod(99);
         parameters.setAesKeyStrength(3);
         password = token.substring(8, 14);
         logger.error("[MagicInfo_HTTP] zipFilename : " + zipFilename + " , password : " + password);
         parameters.setPassword(password);
         zipFile.addFiles(filesToAdd, parameters);
         if (encryption != null && encryption.equalsIgnoreCase("true") && keyfilepath != null) {
            File delFile = SecurityUtils.getSafeFile((String)keyfilepath);
            delFile.delete();
         }
      } catch (ConfigException var20) {
         logger.error("", var20);
      }

      return zipFilename;
   }

   public V2ServerConfig serverSettings() throws ConfigException {
      V2ServerConfig serverConfig = new V2ServerConfig();
      if (StrUtils.nvl(CommonConfig.get("preconfig.server.enable")).equalsIgnoreCase("TRUE")) {
         serverConfig.setServerConfig(true);
      } else {
         serverConfig.setServerConfig(false);
      }

      return serverConfig;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceServerStatus checkServerStatus(String serverUrl) throws Exception {
      V2DeviceServerStatus resource = new V2DeviceServerStatus();
      String result = "success";

      RestExceptionCode exception;
      try {
         URL url = new URL(serverUrl);
         if (serverUrl.startsWith("https")) {
            String body = "";
            HttpHeaders headers = new HttpHeaders();
            headers.set("Accept", "text/plain;charset=utf-8");
            new org.springframework.http.HttpEntity(headers);
            SSLContext sslContext = SSLContext.getInstance("TLS");
            TrustManager[] trustAllCerts = new TrustManager[]{new X509ExtendedTrustManager() {
               public void checkClientTrusted(X509Certificate[] x509Certificates, String s) {
               }

               public void checkServerTrusted(X509Certificate[] chain, String authType) {
               }

               public X509Certificate[] getAcceptedIssuers() {
                  return new X509Certificate[0];
               }

               public void checkClientTrusted(X509Certificate[] x509Certificates, String s, Socket socket) {
               }

               public void checkServerTrusted(X509Certificate[] x509Certificates, String s, Socket socket) {
               }

               public void checkClientTrusted(X509Certificate[] x509Certificates, String s, SSLEngine sslEngine) {
               }

               public void checkServerTrusted(X509Certificate[] x509Certificates, String s, SSLEngine sslEngine) {
               }
            }};
            sslContext.init((KeyManager[])null, trustAllCerts, (SecureRandom)null);
            HttpsURLConnection.setDefaultSSLSocketFactory(sslContext.getSocketFactory());
            HostnameVerifier allHostsValid = new HostnameVerifier() {
               public boolean verify(String hostname, SSLSession session) {
                  return true;
               }
            };
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
            HttpsURLConnection connection = (HttpsURLConnection)url.openConnection();
            connection.setRequestMethod("GET");
            connection.connect();
            int code = connection.getResponseCode();
            if (code >= 200 && code < 300) {
               result = "success";
            } else {
               result = "fail";
               RestExceptionCode exception = RestExceptionCode.DATA_NOT_FOUND;
               resource.setReason(exception.getMessage());
               resource.setReasonCode(exception.getCode());
            }
         } else {
            HttpURLConnection connection = (HttpURLConnection)url.openConnection();
            connection.setRequestMethod("GET");
            connection.connect();
            int code = connection.getResponseCode();
            if (code >= 200 && code < 300) {
               result = "success";
            } else {
               result = "fail";
               RestExceptionCode exception = RestExceptionCode.DATA_NOT_FOUND;
               resource.setReason(exception.getMessage());
               resource.setReasonCode(exception.getCode());
            }
         }
      } catch (HttpClientErrorException var14) {
         result = "fail";
         exception = RestExceptionCode.BAD_REQUEST_SERVER_URL_KNOWN_HOST;
         resource.setReason(exception.getMessage());
         resource.setReasonCode(exception.getCode());
         this.logger.error("Hennry:", var14);
      } catch (UnknownHostException var15) {
         result = "fail";
         exception = RestExceptionCode.BAD_REQUEST_SERVER_URL_KNOWN_HOST;
         resource.setReason(exception.getMessage());
         resource.setReasonCode(exception.getCode());
         this.logger.error("", var15);
      } catch (MalformedURLException var16) {
         result = "fail";
         exception = RestExceptionCode.BAD_REQUEST_SERVER_URL_NO_PROTOCOL;
         resource.setReason(exception.getMessage());
         resource.setReasonCode(exception.getCode());
         this.logger.error("", var16);
      } catch (Exception var17) {
         result = "fail";
         exception = RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN;
         resource.setReason(exception.getMessage());
         resource.setReasonCode(exception.getCode());
         this.logger.error("", var17);
      }

      resource.setResult(result);
      resource.setServerUrl(serverUrl);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceReqServiceResource getCommonGetResult(V2DeviceReqServiceConf body) throws Exception {
      V2DeviceReqServiceResource resource = new V2DeviceReqServiceResource();
      List deviceIds = body.getDeviceIds();
      boolean checkFlag = false;
      Iterator var5 = deviceIds.iterator();

      String deviceId;
      while(var5.hasNext()) {
         deviceId = (String)var5.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var13) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      var5 = deviceIds.iterator();

      while(var5.hasNext()) {
         deviceId = (String)var5.next();
         if (!DeviceUtils.isConnected(deviceId)) {
            resource.setFailList(deviceIds);
            this.logger.error("The device is not connected.");
            return resource;
         }
      }

      DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
      List successList = new ArrayList();
      List failList = new ArrayList();
      Iterator var8 = deviceIds.iterator();

      while(var8.hasNext()) {
         String deviceId = (String)var8.next();

         try {
            DeviceControl getDeviceControl = confManager.getCommonConfGetResult(deviceId, body.getRequestId());
            if (getDeviceControl == null) {
               this.logger.error("[REST_v2.0][DEVICE SERVICE][getCommonGetResult] There was no response from the device. deviceId : " + deviceId);
               failList.add(deviceId);
            } else {
               V2FlipResource flipResource = new V2FlipResource();
               flipResource.convert(getDeviceControl);
               successList.add(flipResource);
            }
         } catch (Exception var12) {
            this.logger.error("[REST_v2.0][DEVICE SERVICE][getCommonGetResult]", var12);
            failList.add(deviceId);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      if (failList.size() > 0) {
         resource.setRequestId(body.getRequestId());
      }

      return resource;
   }

   private boolean e2eDeActAPICall(String deviceId) throws ConfigException {
      SlmLicenseManager licenseMgr = SlmLicenseManagerImpl.getInstance();
      SlmLicenseDao licenseDao = new SlmLicenseDao();
      boolean deActivationResult;
      boolean res;
      if (CommonConfig.get("e2e.license.system") != null && !CommonConfig.get("e2e.license.system").toUpperCase().equals(ExternalSystemUtils.SYSTEM_PBP)) {
         try {
            deActivationResult = licenseMgr.deActivationProcessForE2E_SLMDirect(deviceId);
            if (!deActivationResult) {
               return false;
            } else {
               res = licenseDao.deleteLicenseInfoForE2EByDeviceId(deviceId);
               if (!res) {
                  this.logger.error("Success deactivation but fail to delete on DB" + deviceId);
               }

               return true;
            }
         } catch (Exception var6) {
            this.logger.error("", var6);
            return false;
         }
      } else {
         try {
            deActivationResult = licenseMgr.deActivationProcessForE2E(deviceId);
            if (!deActivationResult) {
               return false;
            } else {
               res = licenseDao.deleteLicenseInfoForE2EByDeviceId(deviceId);
               if (!res) {
                  this.logger.error("Success deactivation but fail to delete license info on DB" + deviceId);
               }

               return true;
            }
         } catch (Exception var7) {
            this.logger.error("", var7);
            return false;
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public V2DeviceReqServiceResource processCommand(V2CommonIds body, String command, String option) throws Exception {
      DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
      String sessionId = UUID.randomUUID().toString();
      V2DeviceReqServiceResource resource = new V2DeviceReqServiceResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      List deviceIds = body.getIds();
      boolean checkFlag = false;
      Iterator var11 = deviceIds.iterator();

      while(var11.hasNext()) {
         String deviceId = (String)var11.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var17) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      boolean isGetCommand = true;
      boolean isUninstallingThirdApplicationCommand = false;
      if (command.equals("UNINSTALL_THIRD_APPLICATION")) {
         if ("KEEP".equalsIgnoreCase(option)) {
            command = command + ";1";
         } else if ("DELETE".equalsIgnoreCase(option)) {
            command = command + ";0";
         }

         isGetCommand = false;
         isUninstallingThirdApplicationCommand = true;
      }

      Iterator var13 = deviceIds.iterator();

      while(var13.hasNext()) {
         String deviceId = (String)var13.next();

         try {
            if (DeviceUtils.isConnected(deviceId)) {
               if (isGetCommand) {
                  confManager.reqGetDevicePredefinedCmd(deviceId, command, sessionId);
               } else {
                  confManager.reqSetDevicePredefinedCmd(deviceId, command, sessionId);
               }

               if (isUninstallingThirdApplicationCommand) {
                  this.addUninstallingFlagToThirdApplicationVersion(deviceId);
               }

               successList.add(deviceId);
            } else {
               failList.add(deviceId);
            }
         } catch (Exception var16) {
            this.logger.error("", var16);
            failList.add(deviceId);
         }
      }

      resource.setRequestId(sessionId);
      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   void addUninstallingFlagToThirdApplicationVersion(String deviceId) {
      DeviceGeneralConfManager generalDao = DeviceGeneralConfManagerImpl.getInstance();

      try {
         DeviceGeneralConf generalConf = generalDao.getDeviceGeneralConf(deviceId);
         String thirdApplicationVersion = generalConf.getThird_application_version();
         if (null != thirdApplicationVersion && !thirdApplicationVersion.isEmpty() && !thirdApplicationVersion.contains("uninstalling")) {
            thirdApplicationVersion = thirdApplicationVersion + ";uninstalling";
            generalConf.setThird_application_version(thirdApplicationVersion);
            generalDao.setDeviceGeneralConf(generalConf);
         }
      } catch (Exception var5) {
         this.logger.error("", var5);
      }

   }
}
