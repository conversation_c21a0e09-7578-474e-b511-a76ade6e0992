package com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.exception.repository.ContentNotFoundException;
import com.samsung.magicinfo.webauthor2.model.ContentGroup;
import com.samsung.magicinfo.webauthor2.service.ContentService;
import com.samsung.magicinfo.webauthor2.webapi.assembler.ContentGroupResourceAssembler;
import com.samsung.magicinfo.webauthor2.webapi.resource.ContentGroupResource;
import java.util.List;
import javax.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/contentGroup"})
public class ContentGroupController {
  private static final Logger logger = LoggerFactory.getLogger(ContentGroupController.class);
  
  private final ContentService contentService;
  
  private final ContentGroupResourceAssembler contentGroupResourceAssembler;
  
  @Inject
  public ContentGroupController(ContentService contentService, ContentGroupResourceAssembler contentGroupResourceAssembler) {
    this.contentService = contentService;
    this.contentGroupResourceAssembler = contentGroupResourceAssembler;
  }
  
  @GetMapping
  public HttpEntity<List<ContentGroupResource>> getContentGroupList() {
    List<ContentGroup> contentGroupList = this.contentService.getContentGroupResources();
    List<ContentGroupResource> contentGroupResources = this.contentGroupResourceAssembler.toResources(contentGroupList);
    return (HttpEntity<List<ContentGroupResource>>)ResponseEntity.ok(contentGroupResources);
  }
  
  @ExceptionHandler({IllegalArgumentException.class})
  public HttpEntity<String> illegalArgumentException(IllegalArgumentException ex) {
    logger.error(ex.getMessage(), ex);
    return (HttpEntity<String>)ResponseEntity.badRequest().body(ex.getMessage());
  }
  
  @ExceptionHandler({ContentNotFoundException.class})
  public HttpEntity<String> contentNotFoundException(ContentNotFoundException ex) {
    logger.error(ex.getMessage(), (Throwable)ex);
    return (HttpEntity<String>)ResponseEntity.notFound().build();
  }
  
  @ExceptionHandler({Exception.class})
  public HttpEntity<String> internalServerException(Exception ex) {
    logger.error(ex.getMessage(), ex);
    return (HttpEntity<String>)ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex.getMessage());
  }
}
