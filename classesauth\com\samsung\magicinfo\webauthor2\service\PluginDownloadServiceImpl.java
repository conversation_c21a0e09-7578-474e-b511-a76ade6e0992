package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.exception.repository.ContentNotFoundException;
import com.samsung.magicinfo.webauthor2.exception.service.DownloaderException;
import com.samsung.magicinfo.webauthor2.exception.service.FileItemValidationException;
import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.model.SupportFileItemWrapper;
import com.samsung.magicinfo.webauthor2.model.VerificationResponse;
import com.samsung.magicinfo.webauthor2.service.download.LFDFileXmlFactory;
import com.samsung.magicinfo.webauthor2.util.FileNameValidator;
import com.samsung.magicinfo.webauthor2.util.UserData;
import com.samsung.magicinfo.webauthor2.xml.lfd.SupportFileItem;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import javax.inject.Inject;
import javax.servlet.ServletContext;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
public class PluginDownloadServiceImpl implements PluginDownloadService {
  private static final Logger logger = LoggerFactory.getLogger(PluginDownloadServiceImpl.class);
  
  private ServletContext servletContext;
  
  private UserData userData;
  
  private ContentService contentService;
  
  private RemoteContentService remoteContentService;
  
  private LFDFileXmlFactory lfdFileXmlFactory;
  
  private FileNameValidator fileNameValidator;
  
  public static final String DEFAULT_WORKING_DIRECTORY_ROOT = "insertContents";
  
  public static final String PLUGIN_PREVIEW_DIRECTORY_ROOT = "preview";
  
  public static final String TEMPORARY_WORKING_DIRECTORY = "WebAuthor";
  
  public static final int MAX_NAME_LENGHT = 64;
  
  public static final int MIN_NAME_LENGHT = 2;
  
  @Inject
  public PluginDownloadServiceImpl(ServletContext servletContext, UserData userData, ContentService contentService, RemoteContentService remoteContentService, LFDFileXmlFactory lfdFileXmlFactory, FileNameValidator fileNameValidator) {
    this.servletContext = servletContext;
    this.userData = userData;
    this.contentService = contentService;
    this.remoteContentService = remoteContentService;
    this.lfdFileXmlFactory = lfdFileXmlFactory;
    this.fileNameValidator = fileNameValidator;
  }
  
  public Page<Content> getPluginResources(String searchText, Pageable pageable) {
    List<MediaType> mediaTypes = new ArrayList<>();
    mediaTypes.add(MediaType.PLUGIN_EFFECT);
    mediaTypes.add(MediaType.LFD);
    return this.contentService.getContentResources(searchText, pageable, DeviceType.WPLAYER, mediaTypes);
  }
  
  public Path getPluginZip(String contentId) throws DownloaderException {
    String timestamp = Long.toString((new Date()).getTime());
    Path workingDirectory = createTemporaryDirectoryStructure(contentId + "_" + timestamp);
    Content pluginLFDContent = this.contentService.getContent(contentId);
    if (pluginLFDContent.getType() != MediaType.PLUGIN_EFFECT || pluginLFDContent.getType() != MediaType.LFD)
      throw new ContentNotFoundException("404", "Content not found id ==  " + contentId); 
    String lfdXml = this.remoteContentService.getXmlFileContents(pluginLFDContent.getFileId(), pluginLFDContent.getFileName());
    Path downloadedThumbnail = this.remoteContentService.getContentFileFromMagicInfoServer(workingDirectory, pluginLFDContent.getThumbnailId(), pluginLFDContent.getThumbnailName());
    List<SupportFileItem> supportFileItems = this.lfdFileXmlFactory.unmarshalSupportFileItems(lfdXml);
    List<SupportFileItemWrapper> downloadedItems = downloadSupportFileItems(workingDirectory, supportFileItems);
    Path pluginZip = getZipFilePath(pluginLFDContent.getName());
    try {
      addFilesToZip(downloadedThumbnail, downloadedItems, pluginZip);
    } catch (IOException ex) {
      logger.error("Error while adding files to Zip", ex.getMessage());
      throw new DownloaderException("Error while creating Zip package.");
    } 
    cleanup(workingDirectory);
    return pluginZip;
  }
  
  public void downloadToRepository(String contentId) {
    try {
      Path pluginRepoDir = Paths.get(this.servletContext.getRealPath("preview"), new String[] { contentId });
      if (Files.exists(pluginRepoDir, new java.nio.file.LinkOption[0])) {
        logger.debug("NOT Downloading - Plugin with id {} exists at path: {} ", contentId, pluginRepoDir.toString());
        return;
      } 
      logger.debug("Downloading plugin with id {} to path: {}", contentId, pluginRepoDir.toString());
      Content pluginLFDContent = this.contentService.getContent(contentId);
      if (pluginLFDContent.getType() != MediaType.PLUGIN_EFFECT && pluginLFDContent.getType() != MediaType.HTML)
        throw new ContentNotFoundException("404", "Content not found id: " + contentId + " with MediaType: LFD or HTML."); 
      String lfdXml = this.remoteContentService.getXmlFileContents(pluginLFDContent.getFileId(), pluginLFDContent.getFileName());
      List<SupportFileItem> supportFileItems = this.lfdFileXmlFactory.unmarshalSupportFileItems(lfdXml);
      Files.createDirectories(pluginRepoDir, (FileAttribute<?>[])new FileAttribute[0]);
      List<SupportFileItemWrapper> list = downloadSupportFileItems(pluginRepoDir.getParent(), supportFileItems);
    } catch (IOException ex) {
      logger.error("Error downloading Plugin with id " + contentId, ex.getMessage());
      throw new DownloaderException("Error downloading Plugin with id " + contentId);
    } 
  }
  
  public VerificationResponse isNameValid(String name, String nameBeforeEdit) {
    try {
      validateCsssClassName(name);
    } catch (FileItemValidationException ex) {
      return new VerificationResponse(false, ex.getMessage());
    } 
    if (!isPluginNameUnique(name, nameBeforeEdit))
      return new VerificationResponse(false, "FileNameNotUnique"); 
    return new VerificationResponse(true, "Valid");
  }
  
  private Path createTemporaryDirectoryStructure(String relativePath) {
    try {
      Path tempDir = Paths.get(FileUtils.getTempDirectoryPath(), new String[0]).resolve("insertContents" + File.separator + relativePath);
      if (Files.exists(tempDir, new java.nio.file.LinkOption[0]))
        FileUtils.deleteQuietly(tempDir.toFile()); 
      Files.createDirectories(tempDir, (FileAttribute<?>[])new FileAttribute[0]);
      return tempDir;
    } catch (IOException e) {
      throw new UploaderException(500, "Error during file structure initialization");
    } 
  }
  
  public List<SupportFileItemWrapper> downloadSupportFileItems(Path workingDirectory, List<SupportFileItem> items) {
    List<SupportFileItemWrapper> result = new ArrayList<>();
    for (SupportFileItem item : items) {
      SupportFileItemWrapper wrapper = new SupportFileItemWrapper(item);
      String relativePath = item.getKeyPathLocation();
      Path relativefull = Paths.get(relativePath.replace("\\", "/"), new String[0]);
      Path relative = relativefull.subpath(1, relativefull.getNameCount() - 1);
      wrapper.setPathToFile(this.remoteContentService.getContentFileFromMagicInfoServer(workingDirectory, relative.toString(), item.getFileId(), item.getPureFileItem()));
      wrapper.setRelativePath(relativefull.subpath(2, relativefull.getNameCount()));
      result.add(wrapper);
    } 
    return result;
  }
  
  private Path getZipFilePath(String contentId) {
    Path zipFileDir = Paths.get(this.servletContext.getRealPath("insertContents"), new String[] { this.userData.getUserId() + "_" + 
          Long.toString((new Date()).getTime()) });
    try {
      Files.createDirectories(zipFileDir, (FileAttribute<?>[])new FileAttribute[0]);
    } catch (IOException ex) {
      logger.error("Error creating Plugin Zip", ex.getMessage());
    } 
    return Paths.get(zipFileDir.toString(), new String[] { contentId + ".zip" });
  }
  
  private Path getPluginRepositoryPath(String contentId) {
    Path zipFileDir = Paths.get(this.servletContext.getRealPath("preview"), new String[] { contentId });
    try {
      Files.createDirectories(zipFileDir, (FileAttribute<?>[])new FileAttribute[0]);
    } catch (IOException ex) {
      logger.error("Error creating Plugin Zip", ex.getMessage());
    } 
    return Paths.get(zipFileDir.toString(), new String[] { contentId + ".zip" });
  }
  
  private void addFilesToZip(Path thumbnail, List<SupportFileItemWrapper> items, Path pluginZip) throws IOException {
    try (ZipOutputStream zos = new ZipOutputStream(Files.newOutputStream(pluginZip, new java.nio.file.OpenOption[0]))) {
      ZipEntry entry = new ZipEntry(thumbnail.getFileName().toString());
      zos.putNextEntry(entry);
      try (InputStream in = new FileInputStream(thumbnail.toFile())) {
        IOUtils.copy(in, zos);
      } 
      zos.closeEntry();
      Set<String> setOfFolders = new HashSet<>();
      for (SupportFileItemWrapper item : items) {
        if (item.getRelativePath().getNameCount() > 1)
          setOfFolders.add(item.getRelativePath().getParent().toString() + "/"); 
        entry = new ZipEntry(item.getRelativePath().toString());
        zos.putNextEntry(entry);
        try (InputStream in = new FileInputStream(item.getPathToFile().toFile())) {
          IOUtils.copy(inputStream, zos);
        } 
        zos.closeEntry();
      } 
      for (String folder : setOfFolders) {
        zos.putNextEntry(new ZipEntry(folder));
        zos.closeEntry();
      } 
      zos.close();
      if (Files.notExists(pluginZip, new java.nio.file.LinkOption[0]))
        throw new FileNotFoundException("Can't find plugin file."); 
    } 
  }
  
  public void cleanup(Path dir) {
    if (dir != null && Files.exists(dir, new java.nio.file.LinkOption[0]))
      FileUtils.deleteQuietly(dir.toFile()); 
  }
  
  public void validateCsssClassName(String name) {
    if (name.equals(null)) {
      logger.error("Classname is null");
      throw new FileItemValidationException(500, "NullFileName");
    } 
    if (name.length() < 2) {
      logger.error("Classname is too Short");
      throw new FileItemValidationException(500, "FileNameTooShort");
    } 
    if (name.length() > 64) {
      logger.error("Classname is too Long");
      throw new FileItemValidationException(500, "FileNameTooLong");
    } 
    if (this.fileNameValidator.filenameContainsSpecialCharactersAndDot(name)) {
      logger.error("Invalid characters in classname");
      throw new FileItemValidationException(500, "SpecialCharFileName");
    } 
  }
  
  public boolean isPluginNameUnique(String name, String nameBeforeEdit) {
    Page<Content> page;
    List<MediaType> mediaTypes = new ArrayList<>();
    mediaTypes.add(MediaType.PLUGIN_EFFECT);
    mediaTypes.add(MediaType.LFD);
    PageRequest pageRequest = new PageRequest(0, 50);
    do {
      page = this.contentService.getContentResources(name, (Pageable)pageRequest, DeviceType.WPLAYER, mediaTypes);
      if (page.getTotalElements() == 0L)
        return true; 
      for (Content content : page.getContent()) {
        if (name.equals(content.getName()) && 
          !nameBeforeEdit.equals(content.getName()))
          return false; 
      } 
      Pageable pageable = page.nextPageable();
    } while (page.hasNext());
    return true;
  }
}
