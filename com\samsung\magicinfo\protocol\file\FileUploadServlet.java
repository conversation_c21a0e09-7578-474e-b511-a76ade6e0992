package com.samsung.magicinfo.protocol.file;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.exception.CMSExceptionCode;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.RequestUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.role.dao.AbilityDao;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.Logger;

public class FileUploadServlet extends HttpServlet {
   private static final long serialVersionUID = 7407938464743598200L;
   private Logger logger = LoggingManagerV2.getLogger(FileUploadHelper.class);

   public FileUploadServlet() {
      super();
   }

   public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");
      this.doPost(request, response);
   }

   public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");
      this.logger.debug("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$");
      this.logger.debug("request 1 : " + request.getContextPath());
      this.logger.debug("request 2 : " + request.getServletPath());
      this.logger.debug("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$");

      try {
         String CONTENTS_HOME;
         String userId;
         String password;
         String contentID;
         String fileID;
         String contentCategory;
         ContentInfoImpl cmsDao;
         AbilityDao fileCmsHome;
         List fileCmsFile;
         label438: {
            CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
            userId = SecurityUtils.getUserIdFromRequest(request);
            password = SecurityUtils.getTokenFromRequest(request);
            contentID = StrUtils.nvl(request.getHeader("CID"));
            fileID = StrUtils.nvl(request.getHeader("FILE_ID"));
            contentCategory = StrUtils.nvl(request.getHeader("category"));
            if (contentID != null && !contentID.equals("")) {
               cmsDao = ContentInfoImpl.getInstance();

               try {
                  fileCmsHome = new AbilityDao();
                  fileCmsFile = fileCmsHome.getAllAbilityListByUserId(userId);
                  boolean flagContentWriteAbility = false;

                  for(int i = 0; i < fileCmsFile.size(); ++i) {
                     String abilityName = (String)((Map)fileCmsFile.get(i)).get("ABILITY_NAME");
                     if (abilityName.equals("Content Write Authority")) {
                        flagContentWriteAbility = true;
                        break;
                     }
                  }

                  if (flagContentWriteAbility) {
                     break label438;
                  }

                  response.sendError(611, CMSExceptionCode.APP611[2]);
                  return;
               } catch (Exception var58) {
                  this.logger.error("[MagicInfo_FileUploadServelet]" + CMSExceptionCode.APP610[2]);
                  response.setHeader("code", CMSExceptionCode.APP610[0]);
                  response.setHeader("message", CMSExceptionCode.APP610[2]);
                  response.sendError(602, CMSExceptionCode.APP610[2]);
                  return;
               }
            }

            this.logger.error("[MagicInfo_FileUploadServelet]" + CMSExceptionCode.APP602[2]);
            response.setHeader("code", CMSExceptionCode.APP602[0]);
            response.setHeader("message", CMSExceptionCode.APP602[2]);
            response.sendError(602, CMSExceptionCode.APP602[2]);
            return;
         }

         fileCmsHome = null;
         fileCmsFile = null;
         String filePath = null;
         String fileName = null;
         Long fileSize = 0L;

         try {
            File fileCmsHome = SecurityUtils.getSafeFile(CONTENTS_HOME);
            boolean fSuccess;
            if (!fileCmsHome.exists()) {
               fSuccess = fileCmsHome.mkdir();
               if (!fSuccess) {
                  this.logger.error("mkdir Fail");
               }
            }

            filePath = CONTENTS_HOME + File.separator + fileID;
            File fileCmsFile = SecurityUtils.getSafeFile(filePath);
            if (!fileCmsFile.exists()) {
               fSuccess = fileCmsFile.mkdir();
               if (!fSuccess) {
                  this.logger.error("mkdir Fail");
               }
            }

            fileName = cmsDao.getFileName(fileID);
            fileSize = cmsDao.getFileSize(fileID);
         } catch (Exception var57) {
            this.logger.error("[MagicInfo_FileUploadServelet]" + CMSExceptionCode.APP613[2]);
            response.setHeader("code", CMSExceptionCode.APP613[0]);
            response.setHeader("message", CMSExceptionCode.APP613[2]);
            response.sendError(602, CMSExceptionCode.APP613[2]);
            return;
         }

         File tmpFile = SecurityUtils.getSafeFile(filePath + File.separator + fileName);
         Long tmpLen = tmpFile.length();
         if (tmpLen >= fileSize) {
            this.logger.error("[MagicInfo_FileUploadServelet]" + filePath + File.separator + fileName + ":" + CMSExceptionCode.APP607[2]);
         } else {
            try {
               InputStream is = request.getInputStream();
               Throwable var18 = null;

               try {
                  FileOutputStream fos = new FileOutputStream(SecurityUtils.directoryTraversalChecker(filePath + File.separator + fileName, (String)null), true);
                  Throwable var20 = null;

                  try {
                     byte[] buf = new byte[1024];
                     boolean var22 = false;

                     int binaryRead;
                     while((binaryRead = is.read(buf)) != -1) {
                        fos.write(buf, 0, binaryRead);
                     }
                  } catch (Throwable var52) {
                     var20 = var52;
                     throw var52;
                  } finally {
                     if (fos != null) {
                        if (var20 != null) {
                           try {
                              fos.close();
                           } catch (Throwable var51) {
                              var20.addSuppressed(var51);
                           }
                        } else {
                           fos.close();
                        }
                     }

                  }
               } catch (Throwable var54) {
                  var18 = var54;
                  throw var54;
               } finally {
                  if (is != null) {
                     if (var18 != null) {
                        try {
                           is.close();
                        } catch (Throwable var50) {
                           var18.addSuppressed(var50);
                        }
                     } else {
                        is.close();
                     }
                  }

               }
            } catch (Exception var56) {
               this.logger.error(var56);
            }

            this.logger.info("file write finish : " + filePath + File.separator + fileName);
         }

         String strHost = request.getServerName();
         String strPort = String.valueOf(request.getServerPort());
         if ("80".equals(strPort)) {
            strPort = "";
         } else {
            strPort = ":" + strPort;
         }

         String strContextPath = request.getContextPath();
         String url = RequestUtils.getProtocolScheme(request) + strHost + strPort + strContextPath;
         response.setHeader("REDIRECT_URL", url + "/login.htm?cmd=NotNormal&user_id=" + userId + "&password=" + password);
         (new StringBuilder()).append(contentID).append("&directory_name=").append(contentCategory).toString();
         String pstr;
         if (Long.parseLong(contentCategory) == 0L) {
            pstr = contentID + "&group_id=" + contentCategory + "&group_type=" + "UNGROUPED";
         } else {
            pstr = contentID + "&group_id=" + contentCategory + "&group_type=" + "GROUPED";
         }

         response.setHeader("CID", pstr);
         response.setHeader("FILE_ID", fileID);
      } catch (Exception var59) {
         response.sendError(600, var59.toString());
         response.setHeader("code", "600");
         response.setHeader("message", "error : " + var59.getMessage());
         this.logger.error("", var59);
      }

   }

   public void copyFile(String srcPath, String dstPath) {
      FileInputStream fis = null;
      FileOutputStream fos = null;
      boolean var5 = false;

      try {
         fis = new FileInputStream(srcPath);
         fos = new FileOutputStream(dstPath);

         int data;
         while((data = fis.read()) != -1) {
            fos.write(data);
         }

         System.out.println("[FileUploaderServlet:copy file success !!!");
      } catch (Exception var19) {
         System.out.println("[FileUploaderServlet:copy file fail !!!");
      } finally {
         if (fis != null) {
            try {
               fis.close();
            } catch (IOException var18) {
               this.logger.error("", var18);
            }
         }

         if (fos != null) {
            try {
               fos.close();
            } catch (IOException var17) {
               this.logger.error("", var17);
            }
         }

      }

   }
}
