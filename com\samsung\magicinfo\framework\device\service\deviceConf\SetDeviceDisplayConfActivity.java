package com.samsung.magicinfo.framework.device.service.deviceConf;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManagerImpl;
import com.samsung.magicinfo.framework.monitoring.entity.CurrentPlayingEntity;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.protocol.exception.ServiceException;
import com.samsung.magicinfo.protocol.rmql.RMQL;
import com.samsung.magicinfo.protocol.rmql.RMQLException;
import com.samsung.magicinfo.protocol.rmql.ResultSet;
import com.samsung.magicinfo.protocol.servicemanager.ClientOpActivity;
import com.samsung.magicinfo.protocol.servicestatus.ServiceStatusManager;
import com.samsung.magicinfo.protocol.servicestatus.ServiceStatusManagerImpl;
import com.samsung.magicinfo.protocol.util.RMQLInstanceCreator;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import org.apache.logging.log4j.Logger;

public class SetDeviceDisplayConfActivity extends ClientOpActivity {
   boolean isLiteDevice = false;
   private Logger logger = LoggingManagerV2.getLogger(this.getClass());

   public SetDeviceDisplayConfActivity() {
      super();
   }

   protected void doPreprocess(HashMap params) {
   }

   protected Object response(ResultSet rs) throws ServiceException {
      try {
         DeviceDisplayConf info = (DeviceDisplayConf)this.ctxt.getServiceParamMap().get("device_display_conf");
         int deviceModelCode = 0;
         if (this.ctxt.getDevice() != null && !this.ctxt.getDevice().getDevice_model_name().equalsIgnoreCase("DEFAULT")) {
            deviceModelCode = Integer.parseInt(this.ctxt.getDevice().getDevice_model_code());
         }

         String mode = (String)this.ctxt.getServiceParamMap().get("device_display_view_mode");
         String[] childIndex = (String[])((String[])this.ctxt.getServiceParamMap().get("childIndex"));
         boolean hasChild = false;
         ArrayList arrList;
         int i;
         if (childIndex != null) {
            hasChild = true;
            if (childIndex[0].equals("0")) {
               arrList = new ArrayList();

               for(i = 1; i <= childIndex.length; ++i) {
                  arrList.add(i + "");
               }

               arrList.toArray(childIndex);
            }
         }

         this.isLiteDevice = this.ctxt.getDevice().getDevice_model_name().toUpperCase().startsWith("LITE_");
         arrList = null;
         DeviceDisplayConfManager displayDao = DeviceDisplayConfManagerImpl.getInstance("PREMIUM");

         for(i = 0; !hasChild || i < childIndex.length; ++i) {
            String targetChildIndex = null;
            if (hasChild) {
               targetChildIndex = childIndex[i];
            }

            DeviceDisplayConf rt_info = new DeviceDisplayConf();
            if (hasChild) {
               rt_info.setDevice_id(this.ctxt.getDevice().getDevice_id() + "_" + childIndex[i]);
            } else {
               rt_info.setDevice_id(this.ctxt.getDevice().getDevice_id());
            }

            MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
            CurrentPlayingEntity playingEntity = motMgr.getPlayingContent(rt_info.getDevice_id());
            if (playingEntity == null) {
               playingEntity = new CurrentPlayingEntity();
            }

            if (mode.toUpperCase().equals("MOBILE_MDC")) {
               if (rs.getString(".MO.DISPLAY_CONF.BASIC.POWER", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.BASIC.POWER", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setBasic_power(info.getBasic_power());
               }

               if (rs.getString(".MO.DISPLAY_CONF.BASIC.VOLUME", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.BASIC.VOLUME", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setBasic_volume(info.getBasic_volume());
               }

               if (rs.getString(".MO.DISPLAY_CONF.BASIC.MUTE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.BASIC.MUTE", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setBasic_mute(info.getBasic_mute());
               }

               if (rs.getString(".MO.DISPLAY_CONF.BASIC.SOURCE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.BASIC.SOURCE", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setBasic_source(info.getBasic_source());
                  if (!this.isLiteDevice && !this.ctxt.getDevice().getDevice_model_code().equals("8000") && !this.ctxt.getDevice().getDevice_model_code().equals(String.valueOf(9999)) && !this.ctxt.getDevice().getDevice_model_code().equals(String.valueOf(9998))) {
                     playingEntity.setInputSource(info.getBasic_source().intValue());
                  }
               }

               if (rs.getString(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS").equals("SUCCESS")) {
                  rt_info.setBasic_panel_status(info.getBasic_panel_status());
                  if (!this.isLiteDevice && !this.ctxt.getDevice().getDevice_model_code().equals("8000") && !this.ctxt.getDevice().getDevice_model_code().equals(String.valueOf(9999)) && !this.ctxt.getDevice().getDevice_model_code().equals(String.valueOf(9998))) {
                     playingEntity.setPanelStatus(info.getBasic_panel_status());
                  }
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SPECIALIZED_PICTURE_MODE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SPECIALIZED_PICTURE_MODE", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setSpecialized_picture_mode(info.getSpecialized_picture_mode());
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.MODE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.MODE", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setPv_mode(info.getPv_mode());
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.CONTRAST", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.CONTRAST", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setPv_contrast(info.getPv_contrast());
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.BRIGHTNESS", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.BRIGHTNESS", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setPv_brightness(info.getPv_brightness());
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SHARPNESS", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SHARPNESS", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setPv_sharpness(info.getPv_sharpness());
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setPv_color(info.getPv_color());
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.TINT", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.TINT", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setPv_tint(info.getPv_tint());
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TONE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TONE", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setPv_colortone(info.getPv_colortone());
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TEMPERATURE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TEMPERATURE", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setPv_color_temperature(info.getPv_color_temperature());
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.MAGIC_BRIGHT", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.MAGIC_BRIGHT", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setPpc_magic_bright(info.getPpc_magic_bright());
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_CONTRAST", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_CONTRAST", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setPpc_contrast(info.getPpc_contrast());
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setPpc_brightness(info.getPpc_brightness());
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PC_COLOR_TONE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PC_COLOR_TONE", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setPpc_colortone(info.getPpc_colortone());
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.COLOR_TEMPERATURE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.COLOR_TEMPERATURE", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setPpc_color_temperature(info.getPpc_color_temperature());
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_RED", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_RED", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setPpc_red(info.getPpc_red());
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_GREEN", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_GREEN", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setPpc_green(info.getPpc_green());
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BLUE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BLUE", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setPpc_blue(info.getPpc_blue());
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PICTURE_SIZE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PICTURE_SIZE", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setPpc_size(info.getPpc_size());
               }

               displayDao.setDeviceDisplayConf(rt_info);
               displayDao.setDeviceDisplayExtConf(rt_info);
            } else if (mode.toUpperCase().equals("ADVANCED_MDC")) {
               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.AUTO_BRIGHTNESS", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.AUTO_BRIGHTNESS", targetChildIndex).equals("SUCCESS")) {
                  rt_info.setAuto_brightness(info.getAuto_brightness());
               }

               displayDao.setDeviceDisplayConf(rt_info);
            } else {
               if (!mode.toUpperCase().equals("ONLY_EXT_MDC")) {
                  if (rs.getString(".MO.DISPLAY_CONF.BASIC.POWER", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.BASIC.POWER", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setBasic_power(info.getBasic_power());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.BASIC.VOLUME", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.BASIC.VOLUME", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setBasic_volume(info.getBasic_volume());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.BASIC.MUTE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.BASIC.MUTE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setBasic_mute(info.getBasic_mute());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.BASIC.SOURCE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.BASIC.SOURCE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setBasic_source(info.getBasic_source());
                     if (!this.isLiteDevice && !this.ctxt.getDevice().getDevice_model_code().equals("8000") && !this.ctxt.getDevice().getDevice_model_code().equals(String.valueOf(9999)) && !this.ctxt.getDevice().getDevice_model_code().equals(String.valueOf(9998))) {
                        playingEntity.setInputSource(info.getBasic_source().intValue());
                     }
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.BASIC.WEB_BROWSER_URL", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.BASIC.WEB_BROWSER_URL", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setWeb_browser_url(info.getWeb_browser_url());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.BASIC.CUSTOM_LOGO", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.BASIC.CUSTOM_LOGO", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setCustom_logo(info.getCustom_logo());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS").equals("SUCCESS")) {
                     rt_info.setBasic_panel_status(info.getBasic_panel_status());
                     if (!this.isLiteDevice && !this.ctxt.getDevice().getDevice_model_code().equals("8000") && !this.ctxt.getDevice().getDevice_model_code().equals(String.valueOf(9999)) && !this.ctxt.getDevice().getDevice_model_code().equals(String.valueOf(9998))) {
                        playingEntity.setPanelStatus(info.getBasic_panel_status());
                     }
                  }

                  if (!this.isLiteDevice && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPpc_brightness(info.getPpc_brightness());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.BASIC.SCREEN_MUTE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.BASIC.SCREEN_MUTE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setScreen_mute(info.getScreen_mute());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.BASIC.FREEZE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.BASIC.FREEZE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setScreen_freeze(info.getScreen_freeze());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.TIME.ON_TIME", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.TIME.ON_TIME", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setTime_on_time(info.getTime_on_time());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.TIME.OFF_TIME", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.TIME.OFF_TIME", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setTime_off_time(info.getTime_off_time());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.MISC.OSD", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.MISC.OSD", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setMisc_osd(info.getMisc_osd());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.MISC.OSD_MENU_SIZE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.MISC.OSD_MENU_SIZE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setOsd_menu_size(info.getOsd_menu_size());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.ALARM_TEMPERATURE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.ALARM_TEMPERATURE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setDiagnosis_alarm_temperature(info.getDiagnosis_alarm_temperature());
                  }

                  if (deviceModelCode > 23 && rs.getString(".MO.DISPLAY_CONF.BASIC.DIRECT_CHANNEL", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.BASIC.DIRECT_CHANNEL", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setBasic_direct_channel(info.getBasic_direct_channel());
                     playingEntity.setDirectChannel(info.getBasic_direct_channel());
                  }

                  displayDao.setDeviceDisplayConf(rt_info);
               }

               if (mode.toUpperCase().equals("ONLY_EXT_MDC") || mode.toUpperCase().equals("ALL_MDC")) {
                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SPECIALIZED_PICTURE_MODE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SPECIALIZED_PICTURE_MODE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setSpecialized_picture_mode(info.getSpecialized_picture_mode());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.MODE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.MODE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPv_mode(info.getPv_mode());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.CONTRAST", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.CONTRAST", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPv_contrast(info.getPv_contrast());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.BRIGHTNESS", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.BRIGHTNESS", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPv_brightness(info.getPv_brightness());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SHARPNESS", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SHARPNESS", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPv_sharpness(info.getPv_sharpness());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPv_color(info.getPv_color());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.TINT", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.TINT", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPv_tint(info.getPv_tint());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TONE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TONE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPv_colortone(info.getPv_colortone());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TEMPERATURE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TEMPERATURE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPv_color_temperature(info.getPv_color_temperature());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SIZE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SIZE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPv_size(info.getPv_size());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.DIGITAL_NR", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.DIGITAL_NR", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPv_digitalnr(info.getPv_digitalnr());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.FILM_MODE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.FILM_MODE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPv_filmmode(info.getPv_filmmode());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.MPEG_NOISE_FILTER", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.MPEG_NOISE_FILTER", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPv_mpeg_noise_filter(info.getPv_mpeg_noise_filter());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.MAGIC_BRIGHT", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.MAGIC_BRIGHT", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPpc_magic_bright(info.getPpc_magic_bright());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_CONTRAST", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_CONTRAST", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPpc_contrast(info.getPpc_contrast());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPpc_brightness(info.getPpc_brightness());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PC_COLOR_TONE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PC_COLOR_TONE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPpc_colortone(info.getPpc_colortone());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.COLOR_TEMPERATURE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.COLOR_TEMPERATURE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPpc_color_temperature(info.getPpc_color_temperature());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_RED", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_RED", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPpc_red(info.getPpc_red());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_GREEN", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_GREEN", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPpc_green(info.getPpc_green());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BLUE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BLUE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPpc_blue(info.getPpc_blue());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PICTURE_SIZE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PICTURE_SIZE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPpc_size(info.getPpc_size());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SOUND.MODE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.SOUND.MODE").equals("SUCCESS")) {
                     rt_info.setSound_mode(info.getSound_mode());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SOUND.BASS", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.SOUND.BASS", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setSound_bass(info.getSound_bass());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SOUND.TREBLE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.SOUND.TREBLE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setSound_treble(info.getSound_treble());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SOUND.BALANCE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.SOUND.BALANCE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setSound_balance(info.getSound_balance());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SOUND.SRS", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.SOUND.SRS", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setSound_srs(info.getSound_srs());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.STATE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.STATE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setSb_status(info.getSb_status());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_GAIN", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_GAIN", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setSb_gain(info.getSb_gain());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_SHARPNESS", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_SHARPNESS", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setSb_sharp(info.getSb_sharp());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.AUTO", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.AUTO").equals("SUCCESS")) {
                     rt_info.setMnt_auto(info.getMnt_auto());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.MANUAL", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.MANUAL", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setMnt_manual(info.getMnt_manual());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_TIMER", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_TIMER", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setMnt_safety_screen_timer(info.getMnt_safety_screen_timer());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.AUTO_SOURCE_SWITCHING", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.AUTO_SOURCE_SWITCHING", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setAuto_source_switching(info.getAuto_source_switching());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.MAX_POWER_SAVING", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.MAX_POWER_SAVING", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setMax_power_saving(info.getMax_power_saving());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.BRIGHTNESS_LIMIT", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.BRIGHTNESS_LIMIT", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setBrightness_limit(info.getBrightness_limit());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.TOUCH_CONTROL_LOCK", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.TOUCH_CONTROL_LOCK", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setTouch_control_lock(info.getTouch_control_lock());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.BLACK_TONE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.BLACK_TONE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setBlack_tone(info.getBlack_tone());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.FLESH_TONE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.FLESH_TONE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setFlesh_tone(info.getFlesh_tone());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.RGB_ONLY_MODE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.RGB_ONLY_MODE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setRgb_only_mode(info.getRgb_only_mode());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.PICTURE_ENHANCER", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.PICTURE_ENHANCER", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setPicture_enhancer(info.getPicture_enhancer());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.COLOR_SPACE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.COLOR_SPACE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setColor_space(info.getColor_space());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.LED_HDR", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.LED_HDR", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setLed_hdr(info.getLed_hdr());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.LED_HDR_DRE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.LED_HDR_DRE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setLed_hdr_dre(info.getLed_hdr_dre());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.LED_PICTURE_SIZE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.LED_PICTURE_SIZE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setLed_picture_size(info.getLed_picture_size());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.AUTO_MOTION_PLUS", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.AUTO_MOTION_PLUS", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setAuto_motion_plus(info.getAuto_motion_plus());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.AUTO_MOTION_PLUS_JUDDER_REDUCTION", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.AUTO_MOTION_PLUS_JUDDER_REDUCTION", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setAuto_motion_plus_judder_reduction(info.getAuto_motion_plus_judder_reduction());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.ECO_SENSOR", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.ECO_SENSOR", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setEco_sensor(info.getEco_sensor());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.MIN_BRIGHTNESS", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.MIN_BRIGHTNESS", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setMin_brightness(info.getMin_brightness());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.LIVE_MODE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.LIVE_MODE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setLive_mode(info.getLive_mode());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.DISPLAY_OUTPUT_MODE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.DISPLAY_OUTPUT_MODE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setDisplay_output_mode(info.getDisplay_output_mode());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.INSTALL_ENVIRONMENT", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.INSTALL_ENVIRONMENT", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setInstall_environment(info.getInstall_environment());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.DEHUMIDIFY", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.DEHUMIDIFY", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setDehumidify(info.getDehumidify());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_OPTION", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_OPTION", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setDimming_option(info.getDimming_option());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_NIGHT_TIME_OVERRIDE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_NIGHT_TIME_OVERRIDE", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setDimming_night_time_override(info.getDimming_night_time_override());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_ECO_SENSOR", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_ECO_SENSOR", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setDimming_eco_sensor(info.getDimming_eco_sensor());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_SUNRISE_SUNSET", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_SUNRISE_SUNSET", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setDimming_sunrise_sunset(info.getDimming_sunrise_sunset());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_SUNRISE_SUNSET_TIMES", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_SUNRISE_SUNSET_TIMES", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setDimming_sunrise_sunset_times(info.getDimming_sunrise_sunset_times());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_BRIGHTNESS_OUTPUT", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.DIMMING_BRIGHTNESS_OUTPUT", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setDimming_brightness_output(info.getDimming_brightness_output());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.WEB.COMMAND", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.WEB.COMMAND", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setWeb_command(info.getWeb_command());
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.WEB.URL", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.WEB.URL", targetChildIndex).equals("SUCCESS")) {
                     rt_info.setWeb_url(info.getWeb_url());
                  }

                  if (deviceModelCode > 23) {
                     if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_GAIN", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_GAIN", targetChildIndex).equals("SUCCESS")) {
                        rt_info.setSb_rgain(info.getSb_rgain());
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_GAIN", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_GAIN", targetChildIndex).equals("SUCCESS")) {
                        rt_info.setSb_ggain(info.getSb_ggain());
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_GAIN", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_GAIN", targetChildIndex).equals("SUCCESS")) {
                        rt_info.setSb_bgain(info.getSb_bgain());
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_OFFSET", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_OFFSET", targetChildIndex).equals("SUCCESS")) {
                        rt_info.setSb_r_offset(info.getSb_r_offset());
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_OFFSET", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_OFFSET", targetChildIndex).equals("SUCCESS")) {
                        rt_info.setSb_g_offset(info.getSb_g_offset());
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_OFFSET", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_OFFSET", targetChildIndex).equals("SUCCESS")) {
                        rt_info.setSb_b_offset(info.getSb_b_offset());
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_RUN", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_RUN", targetChildIndex).equals("SUCCESS")) {
                        rt_info.setMnt_safety_screen_run(info.getMnt_safety_screen_run());
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.PIXEL_SHIFT", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.PIXEL_SHIFT", targetChildIndex).equals("SUCCESS")) {
                        rt_info.setMnt_pixel_shift(info.getMnt_pixel_shift());
                     }
                  }

                  if (deviceModelCode > 55 && deviceModelCode < 7000) {
                     if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.VIDEO_PICTURE_POSITION_SIZE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.VIDEO_PICTURE_POSITION_SIZE", targetChildIndex).equals("SUCCESS")) {
                        rt_info.setPv_video_picture_position_size(info.getPv_video_picture_position_size());
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.HDMI_BLACK_LEVEL", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.HDMI_BLACK_LEVEL", targetChildIndex).equals("SUCCESS")) {
                        rt_info.setPv_hdmi_black_level(info.getPv_hdmi_black_level());
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.GAMMA", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.GAMMA", targetChildIndex).equals("SUCCESS")) {
                        rt_info.setPpc_gamma(info.getPpc_gamma());
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.HDMI_BLACK_LEVEL", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.HDMI_BLACK_LEVEL", targetChildIndex).equals("SUCCESS")) {
                        rt_info.setPpc_hdmi_black_level(info.getPpc_hdmi_black_level());
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.RJ45_SETTING_REFRESH", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.RJ45_SETTING_REFRESH", targetChildIndex).equals("SUCCESS")) {
                        rt_info.setAdvanced_rj45_setting_refresh(info.getAdvanced_rj45_setting_refresh());
                     }

                     try {
                        if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.OSD_DISPLAY_TYPE", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.OSD_DISPLAY_TYPE", targetChildIndex).equals("SUCCESS")) {
                           DeviceDisplayConfManager dInfo = DeviceDisplayConfManagerImpl.getInstance();
                           DeviceDisplayConf originalDdf = dInfo.getDeviceDisplayConf(rt_info.getDevice_id());
                           if (originalDdf.getAdvanced_osd_display_type() != null) {
                              String[] originalArr = originalDdf.getAdvanced_osd_display_type().split(";");
                              String[] changedArr = info.getAdvanced_osd_display_type().split(";");
                              if (originalArr.length >= 4 && changedArr.length == 2) {
                                 originalArr[Integer.parseInt(changedArr[0])] = changedArr[1];
                                 String temp = "";

                                 for(int k = 0; k < originalArr.length; ++k) {
                                    if (!temp.equals("")) {
                                       temp = temp + ";";
                                    }

                                    temp = temp + originalArr[k];
                                 }

                                 rt_info.setAdvanced_osd_display_type(temp);
                              }
                           }
                        }
                     } catch (Exception var19) {
                        this.logger.error("", var19);
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.FAN_CONTROL", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.FAN_CONTROL", targetChildIndex).equals("SUCCESS")) {
                        rt_info.setAdvanced_fan_control(info.getAdvanced_fan_control());
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.FAN_SPEED", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.FAN_SPEED", targetChildIndex).equals("SUCCESS")) {
                        rt_info.setAdvanced_fan_speed(info.getAdvanced_fan_speed());
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.RESET", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.RESET", targetChildIndex).equals("SUCCESS")) {
                        rt_info.setAdvanced_reset(info.getAdvanced_reset());
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.AUTO_POWER", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.AUTO_POWER", targetChildIndex).equals("SUCCESS")) {
                        rt_info.setAdvanced_auto_power(info.getAdvanced_auto_power());
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.USER_AUTO_COLOR", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.USER_AUTO_COLOR", targetChildIndex).equals("SUCCESS")) {
                        rt_info.setAdvanced_user_auto_color(info.getAdvanced_user_auto_color());
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.STAND_BY", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.STAND_BY", targetChildIndex).equals("SUCCESS")) {
                        rt_info.setAdvanced_stand_by(info.getAdvanced_stand_by());
                     }

                     if (!this.isLiteDevice && rs.getString(".MO.DISPLAY_CONF.ADVANCED.NETWORK_STAND_BY", targetChildIndex) != null && rs.getString(".MO.DISPLAY_CONF.ADVANCED.NETWORK_STAND_BY", targetChildIndex).equals("SUCCESS")) {
                        rt_info.setNetwork_standby_mode(info.getNetwork_standby_mode());
                     }
                  }

                  displayDao.setDeviceDisplayExtConf(rt_info);
               }
            }

            if (!hasChild) {
               break;
            }
         }
      } catch (RMQLException var20) {
         this.logger.error("", var20);
      } catch (SQLException var21) {
         this.logger.error("", var21);
      }

      String session_id = (String)this.ctxt.getServiceParamMap().get("session_id");
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      statusManager.setResultset(session_id, this.ctxt.getServiceID(), this.ctxt.getDevice().getDevice_id(), rs);
      return true;
   }

   protected RMQL setRMQL(Device device, Long service_id, HashMap params) throws Exception {
      RMQL rmql = null;
      if (device != null) {
         rmql = RMQLInstanceCreator.getInstance(device, "SET", service_id);
      }

      if (params == null) {
         this.logger.error("Error because of null params");
         return null;
      } else {
         DeviceDisplayConf info = (DeviceDisplayConf)params.get("device_display_conf");
         String mode = (String)params.get("device_display_view_mode");
         int deviceModelCode = 0;
         if (device != null && !device.getDevice_model_code().equalsIgnoreCase("DEFAULT")) {
            deviceModelCode = Integer.parseInt(device.getDevice_model_code());
         }

         this.isLiteDevice = this.ctxt.getDevice().getDevice_type().equals("LPLAYER");
         String[] childIndex = null;
         if (params != null) {
            childIndex = (String[])((String[])params.get("childIndex"));
         }

         MonitoringManager monMgr;
         if (mode.toUpperCase().equals("MOBILE_MDC")) {
            if (info.getBasic_power() != null) {
               rmql.addMO(".MO.DISPLAY_CONF.BASIC.POWER", childIndex, info.getBasic_power());
            }

            if (info.getBasic_volume() != null) {
               rmql.addMO(".MO.DISPLAY_CONF.BASIC.VOLUME", childIndex, info.getBasic_volume().toString());
            }

            if (info.getBasic_mute() != null) {
               rmql.addMO(".MO.DISPLAY_CONF.BASIC.MUTE", childIndex, info.getBasic_mute().toString());
            }

            if (info.getBasic_source() != null) {
               rmql.addMO(".MO.DISPLAY_CONF.BASIC.SOURCE", childIndex, info.getBasic_source().toString());
            }

            if (info.getBasic_panel_status() != null) {
               rmql.addMO(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS", childIndex, info.getBasic_panel_status().toString());
               if (device != null) {
                  monMgr = MonitoringManagerImpl.getInstance();
                  monMgr.setPanelStatus(device.getDevice_id(), info.getBasic_panel_status());
               }
            }

            if (!this.isLiteDevice) {
               if (info.getSpecialized_picture_mode() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.SPECIALIZED_PICTURE_MODE", childIndex, info.getSpecialized_picture_mode().toString());
               }

               if (info.getPv_mode() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.MODE", childIndex, info.getPv_mode().toString());
               }

               if (info.getPv_contrast() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.CONTRAST", childIndex, info.getPv_contrast().toString());
               }

               if (info.getPv_brightness() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.BRIGHTNESS", childIndex, info.getPv_brightness().toString());
               }

               if (info.getPv_sharpness() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.SHARPNESS", childIndex, info.getPv_sharpness().toString());
               }

               if (info.getPv_color() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR", childIndex, info.getPv_color().toString());
               }

               if (info.getPv_tint() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.TINT", childIndex, info.getPv_tint().toString());
               }

               if (info.getPv_colortone() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TONE", childIndex, info.getPv_colortone().toString());
               }

               if (info.getPv_color_temperature() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TEMPERATURE", childIndex, info.getPv_color_temperature().toString());
               }

               if (info.getPpc_magic_bright() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.MAGIC_BRIGHT", childIndex, info.getPpc_magic_bright().toString());
               }

               if (info.getPpc_contrast() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.RGB_CONTRAST", childIndex, info.getPpc_contrast().toString());
               }

               if (info.getPpc_brightness() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS", childIndex, info.getPpc_brightness().toString());
               }

               if (info.getPpc_colortone() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.PC_COLOR_TONE", childIndex, info.getPpc_colortone().toString());
               }

               if (info.getPpc_color_temperature() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.COLOR_TEMPERATURE", childIndex, info.getPpc_color_temperature().toString());
               }

               if (info.getPpc_red() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.RGB_RED", childIndex, info.getPpc_red().toString());
               }

               if (info.getPpc_green() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.RGB_GREEN", childIndex, info.getPpc_green().toString());
               }

               if (info.getPpc_blue() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BLUE", childIndex, info.getPpc_blue().toString());
               }

               if (info.getPpc_size() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.PICTURE_SIZE", childIndex, info.getPpc_size().toString());
               }
            }
         } else if (mode.toUpperCase().equals("ADVANCED_MDC")) {
            if (info.getAuto_brightness() != null) {
               rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.AUTO_BRIGHTNESS", childIndex, info.getAuto_brightness());
            }
         } else {
            if (!mode.toUpperCase().equals("ONLY_EXT_MDC")) {
               if (info.getBasic_power() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.BASIC.POWER", childIndex, info.getBasic_power());
               }

               if (info.getBasic_volume() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.BASIC.VOLUME", childIndex, info.getBasic_volume().toString());
               }

               if (info.getBasic_mute() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.BASIC.MUTE", childIndex, info.getBasic_mute().toString());
               }

               if (info.getBasic_source() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.BASIC.SOURCE", childIndex, info.getBasic_source().toString());
               }

               if (info.getWeb_browser_url() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.BASIC.WEB_BROWSER_URL", childIndex, info.getWeb_browser_url());
               }

               if (info.getCustom_logo() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.BASIC.CUSTOM_LOGO", childIndex, info.getCustom_logo());
               }

               if (info.getScreen_mute() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.BASIC.SCREEN_MUTE", childIndex, info.getScreen_mute().toString());
               }

               if (info.getScreen_freeze() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.BASIC.FREEZE", childIndex, info.getScreen_freeze().toString());
               }

               if (info.getChkSchChannel() != null) {
                  rmql.addMO(".MO.DEVICE_CONF.PROCESS.CHANNEL_NO", childIndex, info.getChkSchChannel().toString());
               }

               if (device != null && info.getBasic_panel_status() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS", childIndex, info.getBasic_panel_status().toString());
                  monMgr = MonitoringManagerImpl.getInstance();
                  monMgr.setPanelStatus(device.getDevice_id(), info.getBasic_panel_status());
               }

               if (!this.isLiteDevice) {
                  if (info.getPpc_brightness() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS", childIndex, info.getPpc_brightness().toString());
                  }

                  if (info.getTime_off_time() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.TIME.OFF_TIME", childIndex, info.getTime_off_time());
                  }

                  if (info.getTime_on_time() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.TIME.ON_TIME", childIndex, info.getTime_on_time());
                  }
               }

               if (info.getMisc_osd() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.MISC.OSD", childIndex, info.getMisc_osd().toString());
               }

               if (info.getOsd_menu_size() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.MISC.OSD_MENU_SIZE", childIndex, info.getOsd_menu_size().toString());
               }

               if (info.getDiagnosis_alarm_temperature() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.DIAGNOSIS.ALARM_TEMPERATURE", childIndex, info.getDiagnosis_alarm_temperature().toString());
               }

               if (deviceModelCode > 23 && info.getBasic_direct_channel() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.BASIC.DIRECT_CHANNEL", childIndex, info.getBasic_direct_channel());
               }
            }

            if (mode.toUpperCase().equals("ONLY_EXT_MDC") || mode.toUpperCase().equals("ALL_MDC")) {
               if (!this.isLiteDevice) {
                  if (info.getSpecialized_picture_mode() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.SPECIALIZED_PICTURE_MODE", childIndex, info.getSpecialized_picture_mode().toString());
                  }

                  if (info.getPv_mode() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.MODE", childIndex, info.getPv_mode().toString());
                  }

                  if (info.getPv_contrast() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.CONTRAST", childIndex, info.getPv_contrast().toString());
                  }

                  if (info.getPv_brightness() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.BRIGHTNESS", childIndex, info.getPv_brightness().toString());
                  }

                  if (info.getPv_sharpness() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.SHARPNESS", childIndex, info.getPv_sharpness().toString());
                  }

                  if (info.getPv_color() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR", childIndex, info.getPv_color().toString());
                  }

                  if (info.getPv_tint() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.TINT", childIndex, info.getPv_tint().toString());
                  }

                  if (info.getPv_colortone() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TONE", childIndex, info.getPv_colortone().toString());
                  }

                  if (info.getPv_color_temperature() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TEMPERATURE", childIndex, info.getPv_color_temperature().toString());
                  }

                  if (info.getPv_size() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.SIZE", childIndex, info.getPv_size().toString());
                  }

                  if (info.getPv_digitalnr() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.DIGITAL_NR", childIndex, info.getPv_digitalnr().toString());
                  }

                  if (info.getPv_filmmode() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.FILM_MODE", childIndex, info.getPv_filmmode().toString());
                  }

                  if (info.getPv_mpeg_noise_filter() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.MPEG_NOISE_FILTER", childIndex, info.getPv_mpeg_noise_filter().toString());
                  }

                  if (info.getPpc_magic_bright() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.MAGIC_BRIGHT", childIndex, info.getPpc_magic_bright().toString());
                  }

                  if (info.getPpc_contrast() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.RGB_CONTRAST", childIndex, info.getPpc_contrast().toString());
                  }

                  if (info.getPpc_brightness() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS", childIndex, info.getPpc_brightness().toString());
                  }

                  if (info.getPpc_colortone() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.PC_COLOR_TONE", childIndex, info.getPpc_colortone().toString());
                  }

                  if (info.getPpc_color_temperature() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.COLOR_TEMPERATURE", childIndex, info.getPpc_color_temperature().toString());
                  }

                  if (info.getPpc_red() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.RGB_RED", childIndex, info.getPpc_red().toString());
                  }

                  if (info.getPpc_green() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.RGB_GREEN", childIndex, info.getPpc_green().toString());
                  }

                  if (info.getPpc_blue() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BLUE", childIndex, info.getPpc_blue().toString());
                  }

                  if (info.getPpc_size() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.PICTURE_SIZE", childIndex, info.getPpc_size().toString());
                  }

                  if (info.getSound_mode() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.SOUND.MODE", childIndex, info.getSound_mode().toString());
                  }

                  if (info.getSound_bass() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.SOUND.BASS", childIndex, info.getSound_bass().toString());
                  }

                  if (info.getSound_treble() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.SOUND.TREBLE", childIndex, info.getSound_treble().toString());
                  }

                  if (info.getSound_balance() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.SOUND.BALANCE", childIndex, info.getSound_balance().toString());
                  }

                  if (info.getSound_srs() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.SOUND.SRS", childIndex, info.getSound_srs().toString());
                  }

                  if (info.getImage_coarse() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.IMAGE.COARSE", childIndex, info.getImage_coarse().toString());
                  }

                  if (info.getImage_fine() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.IMAGE.FINE", childIndex, info.getImage_fine().toString());
                  }

                  if (info.getImage_hpos() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.IMAGE.H_POSITION", childIndex, info.getImage_hpos().toString());
                  }

                  if (info.getImage_vpos() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.IMAGE.V_POSITION", childIndex, info.getImage_vpos().toString());
                  }

                  if (info.getImage_auto() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.IMAGE.AUTO", childIndex, info.getImage_auto().toString());
                  }

                  if (info.getSb_status() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.SIGNAL_BALANCE.STATE", childIndex, info.getSb_status().toString());
                  }

                  if (info.getSb_gain() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_GAIN", childIndex, info.getSb_gain().toString());
                  }

                  if (info.getSb_sharp() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_SHARPNESS", childIndex, info.getSb_sharp().toString());
                  }
               }

               if (info.getMnt_auto() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.MAINTENANCE.AUTO", childIndex, info.getMnt_auto());
               }

               if (info.getMnt_manual() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.MAINTENANCE.MANUAL", childIndex, info.getMnt_manual().toString());
               }

               if (info.getMnt_safety_screen_timer() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_TIMER", childIndex, info.getMnt_safety_screen_timer());
               }

               if (info.getAuto_source_switching() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.AUTO_SOURCE_SWITCHING", childIndex, info.getAuto_source_switching());
               }

               if (info.getMax_power_saving() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.MAX_POWER_SAVING", childIndex, info.getMax_power_saving().toString());
               }

               if (info.getBrightness_limit() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.BRIGHTNESS_LIMIT", childIndex, info.getBrightness_limit().toString());
               }

               if (info.getTouch_control_lock() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.TOUCH_CONTROL_LOCK", childIndex, info.getTouch_control_lock().toString());
               }

               if (info.getBlack_tone() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.BLACK_TONE", childIndex, info.getBlack_tone().toString());
               }

               if (info.getFlesh_tone() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.FLESH_TONE", childIndex, info.getFlesh_tone().toString());
               }

               if (info.getRgb_only_mode() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.RGB_ONLY_MODE", childIndex, info.getRgb_only_mode().toString());
               }

               if (info.getPicture_enhancer() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.PICTURE_ENHANCER", childIndex, info.getPicture_enhancer().toString());
               }

               if (info.getColor_space() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.COLOR_SPACE", childIndex, info.getColor_space().toString());
               }

               if (info.getLed_hdr() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.LED_HDR", childIndex, info.getLed_hdr());
               }

               if (info.getLed_hdr_dre() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.LED_HDR_DRE", childIndex, info.getLed_hdr_dre().toString());
               }

               if (info.getLed_picture_size() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.LED_PICTURE_SIZE", childIndex, info.getLed_picture_size());
               }

               if (info.getAuto_motion_plus() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.AUTO_MOTION_PLUS", childIndex, info.getAuto_motion_plus().toString());
               }

               if (info.getAuto_motion_plus_judder_reduction() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.AUTO_MOTION_PLUS_JUDDER_REDUCTION", childIndex, info.getAuto_motion_plus_judder_reduction().toString());
               }

               if (info.getEco_sensor() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.ECO_SENSOR", childIndex, info.getEco_sensor().toString());
               }

               if (info.getMin_brightness() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.MIN_BRIGHTNESS", childIndex, info.getMin_brightness().toString());
               }

               if (info.getLive_mode() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.LIVE_MODE", childIndex, info.getLive_mode().toString());
               }

               if (info.getDisplay_output_mode() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.DISPLAY_OUTPUT_MODE", childIndex, info.getDisplay_output_mode().toString());
               }

               if (info.getInstall_environment() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.INSTALL_ENVIRONMENT", childIndex, info.getInstall_environment().toString());
               }

               if (info.getDehumidify() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.DEHUMIDIFY", childIndex, info.getDehumidify().toString());
               }

               if (info.getDimming_option() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.DIMMING_OPTION", childIndex, info.getDimming_option().toString());
               }

               if (info.getDimming_night_time_override() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.DIMMING_NIGHT_TIME_OVERRIDE", childIndex, info.getDimming_night_time_override().toString());
               }

               if (info.getDimming_eco_sensor() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.DIMMING_ECO_SENSOR", childIndex, info.getDimming_eco_sensor());
               }

               if (info.getDimming_sunrise_sunset() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.DIMMING_SUNRISE_SUNSET", childIndex, info.getDimming_sunrise_sunset());
               }

               if (info.getDimming_sunrise_sunset_times() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.DIMMING_SUNRISE_SUNSET_TIMES", childIndex, info.getDimming_sunrise_sunset_times());
               }

               if (info.getDimming_brightness_output() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.DIMMING_BRIGHTNESS_OUTPUT", childIndex, info.getDimming_brightness_output());
               }

               if (info.getWeb_command() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.WEB.COMMAND", childIndex, info.getWeb_command().toString());
               }

               if (info.getWeb_option() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.WEB.OPTION", childIndex, info.getWeb_option().toString());
               }

               if (info.getWeb_url() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.WEB.URL", childIndex, info.getWeb_url());
               }

               if (info.getWeb_end_time() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.WEB.END_TIME", childIndex, info.getWeb_end_time());
               }

               if (info.getWeb_duration() != null) {
                  rmql.addMO(".MO.DISPLAY_CONF.WEB.DURATION", childIndex, info.getWeb_duration().toString());
               }

               if (deviceModelCode > 23) {
                  if (!this.isLiteDevice) {
                     if (info.getSb_rgain() != null) {
                        rmql.addMO(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_GAIN", childIndex, info.getSb_rgain().toString());
                     }

                     if (info.getSb_ggain() != null) {
                        rmql.addMO(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_GAIN", childIndex, info.getSb_ggain().toString());
                     }

                     if (info.getSb_bgain() != null) {
                        rmql.addMO(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_GAIN", childIndex, info.getSb_bgain().toString());
                     }

                     if (info.getSb_r_offset() != null) {
                        rmql.addMO(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_OFFSET", childIndex, info.getSb_r_offset().toString());
                     }

                     if (info.getSb_g_offset() != null) {
                        rmql.addMO(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_OFFSET", childIndex, info.getSb_g_offset().toString());
                     }

                     if (info.getSb_b_offset() != null) {
                        rmql.addMO(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_OFFSET", childIndex, info.getSb_b_offset().toString());
                     }
                  }

                  if (info.getMnt_safety_screen_run() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_RUN", childIndex, info.getMnt_safety_screen_run().toString());
                  }

                  if (info.getMnt_pixel_shift() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.MAINTENANCE.PIXEL_SHIFT", childIndex, info.getMnt_pixel_shift());
                  }
               }

               if (deviceModelCode > 55 && deviceModelCode < 7000) {
                  if (!this.isLiteDevice) {
                     if (info.getPv_video_picture_position_size() != null) {
                        rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.VIDEO_PICTURE_POSITION_SIZE", childIndex, info.getPv_video_picture_position_size());
                     }

                     if (info.getPv_hdmi_black_level() != null) {
                        rmql.addMO(".MO.DISPLAY_CONF.PICTURE_VIDEO.HDMI_BLACK_LEVEL", childIndex, info.getPv_hdmi_black_level().toString());
                     }

                     if (info.getPpc_gamma() != null) {
                        rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.GAMMA", childIndex, info.getPpc_gamma().toString());
                     }

                     if (info.getPpc_hdmi_black_level() != null) {
                        rmql.addMO(".MO.DISPLAY_CONF.PICTURE_PC.HDMI_BLACK_LEVEL", childIndex, info.getPpc_hdmi_black_level().toString());
                     }

                     if (info.getAdvanced_user_auto_color() != null) {
                        rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.USER_AUTO_COLOR", childIndex, info.getAdvanced_user_auto_color().toString());
                     }
                  }

                  if (info.getAdvanced_rj45_setting_refresh() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.RJ45_SETTING_REFRESH", childIndex, info.getAdvanced_rj45_setting_refresh().toString());
                  }

                  if (info.getAdvanced_osd_display_type() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.OSD_DISPLAY_TYPE", childIndex, info.getAdvanced_osd_display_type());
                  }

                  if (info.getAdvanced_fan_control() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.FAN_CONTROL", childIndex, info.getAdvanced_fan_control().toString());
                  }

                  if (!this.isLiteDevice && info.getAdvanced_fan_speed() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.FAN_SPEED", childIndex, info.getAdvanced_fan_speed().toString());
                  }

                  if (info.getAdvanced_reset() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.RESET", childIndex, info.getAdvanced_reset().toString());
                  }

                  if (info.getAdvanced_auto_power() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.AUTO_POWER", childIndex, info.getAdvanced_auto_power().toString());
                  }

                  if (info.getAdvanced_stand_by() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.STAND_BY", childIndex, info.getAdvanced_stand_by().toString());
                  }

                  if (!this.isLiteDevice && info.getNetwork_standby_mode() != null) {
                     rmql.addMO(".MO.DISPLAY_CONF.ADVANCED.NETWORK_STAND_BY", childIndex, info.getNetwork_standby_mode());
                  }
               }
            }
         }

         if (rmql != null && rmql.getMOList() != null) {
            int cnt = rmql.getMOList().size();
            if (cnt == 0) {
               rmql.addMO(".MO.DISPLAY_CONF.BASIC.POWER", childIndex, "1");
            }
         }

         return rmql;
      }
   }
}
