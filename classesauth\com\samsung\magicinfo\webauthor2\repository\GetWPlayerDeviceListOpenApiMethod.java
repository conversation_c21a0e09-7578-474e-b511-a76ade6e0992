package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceData;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceListResponseData;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceListResultListData;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.web.client.RestTemplate;

public class GetWPlayerDeviceListOpenApiMethod extends OpenApiMethod<List<DeviceData>, DeviceListResponseData> {
  private final String userId;
  
  private final String token;
  
  public GetWPlayerDeviceListOpenApiMethod(RestTemplate restTemplate, String userId, String token) {
    super(restTemplate);
    this.userId = userId;
    this.token = token;
  }
  
  protected String getOpenApiClassName() {
    return "PremiumDeviceService";
  }
  
  protected String getOpenApiMethodName() {
    return "getDeviceList";
  }
  
  protected Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("userId", this.userId);
    vars.put("token", this.token);
    vars.put("condition", "<DeviceCondition><startPos>1</startPos><pageSize>1</pageSize></DeviceCondition>");
    vars.put("deviceType", "WPLAYER");
    return vars;
  }
  
  Class<DeviceListResponseData> getResponseClass() {
    return DeviceListResponseData.class;
  }
  
  List<DeviceData> convertResponseData(DeviceListResponseData responseData) {
    List<DeviceData> resultList;
    DeviceListResultListData resultListData = responseData.getResponseClass();
    if (resultListData != null && resultListData.getResultList() != null) {
      resultList = resultListData.getResultList();
    } else {
      resultList = new ArrayList<>();
    } 
    return resultList;
  }
}
