package com.samsung.magicinfo.framework.device.deviceInfo.dao;

import com.samsung.common.db.PagedListInfo;
import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DaoTools;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.LedCabinet;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import java.sql.SQLException;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.logging.log4j.Logger;

public class LedCabinetConfDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(LedCabinetConfDao.class);

   public LedCabinetConfDao() {
      super();
   }

   public LedCabinet getLedCabinet(String parentDeviceId, Long cabinetGroupId, Long cabinetId) throws SQLException {
      return ((LedCabinetConfDaoMapper)this.getMapper()).getLedCabinet(parentDeviceId, cabinetGroupId, cabinetId);
   }

   public List getLedCabinetList(String parentDeviceId, Long groupId) throws SQLException {
      return ((LedCabinetConfDaoMapper)this.getMapper()).getLedCabinetList(parentDeviceId, groupId, "asc", "", (String)null, -1, -1);
   }

   public List getLedCabinetList(String parentDeviceId, List childIds) throws SQLException {
      return ((LedCabinetConfDaoMapper)this.getMapper()).getLedCabinetListByIdList(parentDeviceId, childIds);
   }

   public List getLedCabinetGroupIds(String parentDeviceId) throws SQLException {
      return ((LedCabinetConfDaoMapper)this.getMapper()).getLedCabinetGroupIds(parentDeviceId);
   }

   public List getLedCabinetGroupLayoutInfo(String parentDeviceId) throws SQLException {
      return ((LedCabinetConfDaoMapper)this.getMapper()).getLedCabinetGroupLayoutInfo(parentDeviceId);
   }

   public int getLedCabinetListCount(String parentDeviceId) throws SQLException {
      return ((LedCabinetConfDaoMapper)this.getMapper()).getLedCabinetListCount(parentDeviceId, (Long)null);
   }

   public int getLedCabinetListCount(String parentDeviceId, Long cabinetGroupId) throws SQLException {
      return ((LedCabinetConfDaoMapper)this.getMapper()).getLedCabinetListCount(parentDeviceId, cabinetGroupId);
   }

   public boolean addLedCabinet(LedCabinet ledCabinet) throws SQLException {
      boolean ret = ((LedCabinetConfDaoMapper)this.getMapper()).addLedCabinet(ledCabinet);
      return ret;
   }

   public boolean addLedCabinetList(List ledCabinets) throws SQLException {
      boolean ret = true;
      if (ledCabinets != null && ledCabinets.size() > 0) {
         LedCabinet cabinet;
         for(Iterator var3 = ledCabinets.iterator(); var3.hasNext(); ret &= ((LedCabinetConfDaoMapper)this.getMapper()).addLedCabinet(cabinet)) {
            cabinet = (LedCabinet)var3.next();
         }

         return ret;
      } else {
         return true;
      }
   }

   public boolean setLedCabinetPowerOff(String parentDeviceId, Long cabinetGroupId, Long cabinetId) throws SQLException {
      return ((LedCabinetConfDaoMapper)this.getMapper()).setLedCabinetPowerOff(parentDeviceId, cabinetGroupId, cabinetId);
   }

   public boolean setLedCabinet(LedCabinet ledCabinet) throws SQLException {
      return ((LedCabinetConfDaoMapper)this.getMapper()).setLedCabinet(ledCabinet);
   }

   public boolean deleteLedCabinets(String parentDeviceId) throws SQLException {
      return ((LedCabinetConfDaoMapper)this.getMapper()).deleteLedCabinets(parentDeviceId);
   }

   public boolean deleteLedCabinet(String parentDeviceId, Long cabinetGroupId, Long cabinetId) throws SQLException {
      return ((LedCabinetConfDaoMapper)this.getMapper()).deleteLedCabinet(parentDeviceId, cabinetGroupId, cabinetId);
   }

   public PagedListInfo getLedCabinetList(int startPos, int pageSize, Map map) throws SQLException {
      SelectCondition condition = (SelectCondition)map.get("condition");
      String device_id = condition.getDevice_id();
      String sort = condition.getSort_name();
      String dir = condition.getOrder_dir();
      String src = condition.getSrc_name() != null ? condition.getSrc_name().toUpperCase() : "";
      List result = ((LedCabinetConfDaoMapper)this.getMapper()).getLedCabinetList(device_id, (Long)null, dir, src, sort, DaoTools.offsetStartPost(startPos), pageSize);
      int size = ((LedCabinetConfDaoMapper)this.getMapper()).getLedCabinetListCount(device_id, (Long)null);
      return new PagedListInfo(result, size);
   }

   public List getLedCabinetPagedList(int startPos, int pageSize, Map map) throws SQLException {
      String device_id = (String)map.get("device_id");
      String sort = (String)map.get("sort_name");
      String dir = (String)map.get("order_dir");
      Long groupId = (Long)map.get("groupId");
      String src = "";
      List result = ((LedCabinetConfDaoMapper)this.getMapper()).getLedCabinetList(device_id, groupId, dir, src, sort, DaoTools.offsetStartPost(startPos), pageSize);
      return result;
   }

   public boolean setLedCabinetGroupInfo(String parentDeviceId, Long cabinetGroupId, String groupResolution, Long positionX, Long positionY) throws SQLException {
      return ((LedCabinetConfDaoMapper)this.getMapper()).getLedCabinet(parentDeviceId, cabinetGroupId, 0L) == null ? ((LedCabinetConfDaoMapper)this.getMapper()).addLedCabinetGroupInfo(parentDeviceId, cabinetGroupId, groupResolution, positionX, positionY) : ((LedCabinetConfDaoMapper)this.getMapper()).setLedCabinetGroupInfo(parentDeviceId, cabinetGroupId, groupResolution, positionX, positionY);
   }

   public int getErrorLedCabinetCntByGroupId(String parentDeviceId, Long groupId) throws SQLException {
      return ((LedCabinetConfDaoMapper)this.getMapper()).getErrorLedCabinetCntByGroupId(parentDeviceId, groupId);
   }

   public int getLedCabinetCountByGroup(String parentDeviceId, Long groupId) throws SQLException {
      return ((LedCabinetConfDaoMapper)this.getMapper()).getLedCabinetCountByGroup(parentDeviceId, groupId);
   }
}
