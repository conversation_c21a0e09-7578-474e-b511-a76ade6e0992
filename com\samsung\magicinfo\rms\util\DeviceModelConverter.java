package com.samsung.magicinfo.rms.util;

import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSecurityConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemSetupConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTimeConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.LedCabinet;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManagerImpl;
import com.samsung.magicinfo.framework.edge.db.EdgeServerDao;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.restapi.device.model.V2DeviceTimeCurrentDate;
import com.samsung.magicinfo.restapi.device.model.V2DeviceTimeconfResource;
import com.samsung.magicinfo.restapi.edge.model.EdgeServer;
import com.samsung.magicinfo.restapi.edge.model.EdgeStatus;
import com.samsung.magicinfo.rms.model.DeviceDisplayConfResource;
import com.samsung.magicinfo.rms.model.DeviceDownloadServerResource;
import com.samsung.magicinfo.rms.model.DeviceGeneralConfResource;
import com.samsung.magicinfo.rms.model.DeviceLedCabinetResource;
import com.samsung.magicinfo.rms.model.DeviceMonitoringResource;
import com.samsung.magicinfo.rms.model.DevicePeripheralsResource;
import com.samsung.magicinfo.rms.model.DevicePrintServerResource;
import com.samsung.magicinfo.rms.model.DeviceSMTPResource;
import com.samsung.magicinfo.rms.model.DeviceSecurityConfResource;
import com.samsung.magicinfo.rms.model.DeviceSystemSetupConfResource;
import com.samsung.magicinfo.rms.model.DeviceTimeconfResource;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;

public class DeviceModelConverter {
   static Logger logger = LoggingManagerV2.getLogger(DeviceModelConverter.class);

   public DeviceModelConverter() {
      super();
   }

   public static List convertMonitoringInfoToCamelStyle(List deviceListInfo) {
      int size = deviceListInfo.size();
      List deviceListInfoCamel = new ArrayList(size);
      MonitoringManager monMgr = MonitoringManagerImpl.getInstance();

      for(int i = 0; i < size; ++i) {
         DeviceMonitoringResource deviceInfoCamel = new DeviceMonitoringResource();
         DeviceMonitoring tempMonitoring = (DeviceMonitoring)deviceListInfo.get(i);
         String captureUrl = "";
         String thumbUrl = "";
         String thumbFileName = "thumb_img_power";

         try {
            HashMap resultMap = DeviceUtils.getRmsStatusInfos("", tempMonitoring, true);
            captureUrl = (String)resultMap.get("captureUrl");
            thumbFileName = (String)resultMap.get("thumbFileName");
            thumbUrl = (String)resultMap.get("thumbUrl");
         } catch (Exception var11) {
            logger.error("", var11);
         }

         deviceInfoCamel.setCaptureUrl(captureUrl);
         deviceInfoCamel.setThumbFileUrl(thumbUrl);
         deviceInfoCamel.setThumbFileName(thumbFileName);
         deviceInfoCamel.setDeviceId(tempMonitoring.getDevice_id());
         deviceInfoCamel.setDeviceName(tempMonitoring.getDevice_name());
         deviceInfoCamel.setDeviceType(tempMonitoring.getDevice_type());
         deviceInfoCamel.setDeviceTypeVersion(tempMonitoring.getDevice_type_version());
         deviceInfoCamel.setIpAddress(tempMonitoring.getIp_address());
         deviceInfoCamel.setTunnelingServer(tempMonitoring.getTunneling_server());
         deviceInfoCamel.setGroupId(tempMonitoring.getGroup_id());
         deviceInfoCamel.setProgramId(tempMonitoring.getProgram_id());
         deviceInfoCamel.setDescription(tempMonitoring.getDescription());
         deviceInfoCamel.setProgramName(tempMonitoring.getProgram_name());
         deviceInfoCamel.setFrameIndex(tempMonitoring.getFrame_index());
         deviceInfoCamel.setFrameName(tempMonitoring.getFrame_name());
         deviceInfoCamel.setX(tempMonitoring.getX());
         deviceInfoCamel.setY(tempMonitoring.getY());
         deviceInfoCamel.setWidth(tempMonitoring.getWidth());
         deviceInfoCamel.setHeight(tempMonitoring.getHeight());
         deviceInfoCamel.setDefaultContentId(tempMonitoring.getDefault_content_id());
         deviceInfoCamel.setContentId(tempMonitoring.getContent_id());
         deviceInfoCamel.setContentName(tempMonitoring.getContent_name());
         deviceInfoCamel.setMediaType(tempMonitoring.getMedia_type());
         deviceInfoCamel.setVersionId(tempMonitoring.getVersion_id());
         deviceInfoCamel.setCreateDate(tempMonitoring.getCreate_date());
         deviceInfoCamel.setThumbFileId(tempMonitoring.getThumb_file_id());
         deviceInfoCamel.setDeviceModelCode(tempMonitoring.getDevice_model_code());
         deviceInfoCamel.setDeviceModelName(tempMonitoring.getDevice_model_name());
         deviceInfoCamel.setExpirationDate(tempMonitoring.getExpiration_date());
         deviceInfoCamel.setVwtId(tempMonitoring.getVwt_id());
         deviceInfoCamel.setIsRedundancy(tempMonitoring.getIs_redundancy());
         deviceInfoCamel.setDiskSpaceRepository(tempMonitoring.getDisk_space_repository());
         deviceInfoCamel.setGroupName(tempMonitoring.getGroup_name());
         deviceInfoCamel.setMinPriority(tempMonitoring.getMin_priority());
         deviceInfoCamel.setFirmwareVersion(tempMonitoring.getFirmware_version());
         deviceInfoCamel.setWebcam(tempMonitoring.getWebcam());
         deviceInfoCamel.setIconErrorSw(tempMonitoring.getIcon_error_sw());
         deviceInfoCamel.setIconErrorHw(tempMonitoring.getIcon_error_hw());
         deviceInfoCamel.setIconAlarm(tempMonitoring.getIcon_alarm());
         deviceInfoCamel.setIconProcessContentDownload(tempMonitoring.getIcon_process_content_download());
         deviceInfoCamel.setIconProcessLog(tempMonitoring.getIcon_process_log());
         deviceInfoCamel.setIconProcessSwDownload(tempMonitoring.getIcon_process_sw_download());
         deviceInfoCamel.setIconMemo(tempMonitoring.getIcon_memo());
         deviceInfoCamel.setIconBackup(tempMonitoring.getIcon_backup());
         deviceInfoCamel.setScreenRotation(tempMonitoring.getScreen_rotation());
         deviceInfoCamel.setChildCnt(tempMonitoring.getChild_cnt());
         deviceInfoCamel.setConnChildCnt(tempMonitoring.getConn_child_cnt());
         deviceInfoCamel.setIsChild(tempMonitoring.getIs_child());
         deviceInfoCamel.setHasChild(tempMonitoring.getHas_child());
         deviceInfoCamel.setPower(monMgr.isConnected(tempMonitoring.getDevice_id()));
         deviceInfoCamel.setRecommendPlay(tempMonitoring.getRecommend_play());
         deviceInfoCamel.setMapLocation(tempMonitoring.getMap_location());
         deviceInfoCamel.setDeviceSeries(tempMonitoring.getDevice_series());
         deviceInfoCamel.setDeviceHwPlatform(tempMonitoring.getDevice_hw_platform());
         deviceInfoCamel.setRmRuleVersion(tempMonitoring.getRm_rule_version());
         deviceListInfoCamel.add(i, deviceInfoCamel);
      }

      return deviceListInfoCamel;
   }

   public static DeviceDisplayConfResource convertDisplayInfoToCamelStyle(DeviceDisplayConf deviceDisplayConf) {
      DeviceDisplayConfResource displayResource = new DeviceDisplayConfResource();
      if (deviceDisplayConf == null) {
         return displayResource;
      } else {
         displayResource.setDeviceId(deviceDisplayConf.getDevice_id());
         displayResource.setDeviceName(deviceDisplayConf.getDevice_name());
         displayResource.setDeviceModelCode(deviceDisplayConf.getDevice_model_code());
         displayResource.setDeviceModelName(deviceDisplayConf.getDevice_model_name());
         displayResource.setDeviceType(deviceDisplayConf.getDevice_type());
         displayResource.setDeviceTypeVersion(deviceDisplayConf.getDevice_type_version());
         displayResource.setMdcUpdateTime(deviceDisplayConf.getMdc_update_time());
         displayResource.setBasicPower(deviceDisplayConf.getBasic_power());
         displayResource.setBasicPanelStatus(deviceDisplayConf.getBasic_panel_status());
         displayResource.setBasicSource(deviceDisplayConf.getBasic_source());
         displayResource.setBasicMute(deviceDisplayConf.getBasic_mute());
         displayResource.setBasicVolume(deviceDisplayConf.getBasic_volume());
         displayResource.setBasicDirectChannel(deviceDisplayConf.getBasic_direct_channel());
         displayResource.setNetworkStandbyMode(deviceDisplayConf.getNetwork_standby_mode());
         displayResource.setPvSplPictureMode(deviceDisplayConf.getSpecialized_picture_mode());
         displayResource.setPvBrightness(deviceDisplayConf.getPv_brightness());
         displayResource.setPvColor(deviceDisplayConf.getPv_color());
         displayResource.setPvColorTemperature(deviceDisplayConf.getPv_color_temperature());
         displayResource.setPvColortone(deviceDisplayConf.getPv_colortone());
         displayResource.setPvContrast(deviceDisplayConf.getPv_contrast());
         displayResource.setPvContrast(deviceDisplayConf.getPv_contrast());
         displayResource.setPvDigitalnr(deviceDisplayConf.getPv_digitalnr());
         displayResource.setPvFilmmode(deviceDisplayConf.getPv_filmmode());
         displayResource.setPvHdmiBlackLevel(deviceDisplayConf.getPv_hdmi_black_level());
         displayResource.setPvMode(deviceDisplayConf.getPv_mode());
         displayResource.setPvSharpness(deviceDisplayConf.getPv_sharpness());
         displayResource.setPvSize(deviceDisplayConf.getPv_size());
         displayResource.setPvTint(deviceDisplayConf.getPv_tint());
         displayResource.setPvVideoPicturePositionSize(deviceDisplayConf.getPv_video_picture_position_size());
         displayResource.setPvMpegNoiseFilter(deviceDisplayConf.getPv_mpeg_noise_filter());
         displayResource.setPpcBlue(deviceDisplayConf.getPpc_blue());
         displayResource.setPpcBrightness(deviceDisplayConf.getPpc_brightness());
         displayResource.setPpcColorTemperature(deviceDisplayConf.getPpc_color_temperature());
         displayResource.setPpcColortone(deviceDisplayConf.getPpc_colortone());
         displayResource.setPpcContrast(deviceDisplayConf.getPpc_contrast());
         displayResource.setPpcGamma(deviceDisplayConf.getPpc_gamma());
         displayResource.setPpcGreen(deviceDisplayConf.getPpc_green());
         displayResource.setPpcHdmiBlackLevel(deviceDisplayConf.getPpc_hdmi_black_level());
         displayResource.setPpcMagicBright(deviceDisplayConf.getPpc_magic_bright());
         displayResource.setPpcRed(deviceDisplayConf.getPpc_red());
         displayResource.setPpcSize(deviceDisplayConf.getPpc_size());
         displayResource.setTimeCurrentTime(deviceDisplayConf.getTime_current_time());
         displayResource.setTimeOnTime(deviceDisplayConf.getTime_on_time());
         displayResource.setTimeOffTime(deviceDisplayConf.getTime_off_time());
         displayResource.setPipControl(deviceDisplayConf.getPip_control());
         displayResource.setPipPosition(deviceDisplayConf.getPip_position());
         displayResource.setPipSize(deviceDisplayConf.getPip_size());
         displayResource.setPipSource(deviceDisplayConf.getPip_source());
         displayResource.setPipSwap(deviceDisplayConf.getPip_swap());
         displayResource.setSoundBalance(deviceDisplayConf.getSound_balance());
         displayResource.setSoundBass(deviceDisplayConf.getSound_bass());
         displayResource.setSoundEffect(deviceDisplayConf.getSound_effect());
         displayResource.setSoundMode(deviceDisplayConf.getSound_mode());
         displayResource.setSoundSrs(deviceDisplayConf.getSound_srs());
         displayResource.setSoundTreble(deviceDisplayConf.getSound_treble());
         displayResource.setImageAuto(deviceDisplayConf.getImage_auto());
         displayResource.setImageCoarse(deviceDisplayConf.getImage_coarse());
         displayResource.setImageFine(deviceDisplayConf.getImage_fine());
         displayResource.setImageHpos(deviceDisplayConf.getImage_hpos());
         displayResource.setImageVpos(deviceDisplayConf.getImage_vpos());
         displayResource.setSbBoffset(deviceDisplayConf.getSb_b_offset());
         displayResource.setSbBgain(deviceDisplayConf.getSb_bgain());
         displayResource.setSbGoffset(deviceDisplayConf.getSb_g_offset());
         displayResource.setSbGain(deviceDisplayConf.getSb_gain());
         displayResource.setSbGgain(deviceDisplayConf.getSb_ggain());
         displayResource.setSbRoffset(deviceDisplayConf.getSb_r_offset());
         displayResource.setSbRgain(deviceDisplayConf.getSb_rgain());
         displayResource.setSbSharp(deviceDisplayConf.getSb_sharp());
         displayResource.setSbStatus(deviceDisplayConf.getSb_status());
         displayResource.setMntAuto(DeviceModelUtils.setDisplayMntAutoResource(deviceDisplayConf.getMnt_auto()));
         displayResource.setMntFormat(deviceDisplayConf.getMnt_format());
         displayResource.setMntManual(deviceDisplayConf.getMnt_manual());
         displayResource.setMntPixelShift(DeviceModelUtils.setDisplayMntPixelShiftResource(deviceDisplayConf.getMnt_pixel_shift()));
         displayResource.setMntSafetyLock(deviceDisplayConf.getMnt_safety_lock());
         displayResource.setMntSafetyScreenRun(deviceDisplayConf.getMnt_safety_screen_run());
         displayResource.setMntSafetyScreenTimer(DeviceModelUtils.setDisplayMntSafetyScreenTimerResource(deviceDisplayConf.getMnt_safety_screen_timer()));
         displayResource.setMaxPowerSaving(deviceDisplayConf.getMax_power_saving());
         displayResource.setBrightnessLimit(deviceDisplayConf.getBrightness_limit());
         displayResource.setTouchControlLock(deviceDisplayConf.getTouch_control_lock());
         displayResource.setWebBrowserUrl(DeviceModelUtils.setDisplayWebBrowserUrlResource(deviceDisplayConf.getWeb_browser_url()));
         displayResource.setCustomLogo(DeviceModelUtils.setDisplayCustomLogoResource(deviceDisplayConf.getCustom_logo()));
         displayResource.setScreenMute(deviceDisplayConf.getScreen_mute());
         displayResource.setScreenFreeze(deviceDisplayConf.getScreen_freeze());
         displayResource.setMntVideoWall(deviceDisplayConf.getMnt_video_wall());
         displayResource.setAdvancedAutoPower(deviceDisplayConf.getAdvanced_auto_power());
         displayResource.setAdvancedFanControl(deviceDisplayConf.getAdvanced_fan_control());
         if (deviceDisplayConf.getAdvanced_fan_speed() != null && deviceDisplayConf.getAdvanced_fan_speed() < 0L) {
            displayResource.setAdvancedFanSpeed(0L);
         } else {
            displayResource.setAdvancedFanSpeed(deviceDisplayConf.getAdvanced_fan_speed());
         }

         if (deviceDisplayConf.getDevice_model_code() != null && Integer.valueOf(deviceDisplayConf.getDevice_model_code()) > 55) {
            displayResource.setAdvancedOsdDisplayType(DeviceModelUtils.setDisplayAdvOsdDisplayTypeResource(deviceDisplayConf.getAdvanced_osd_display_type()));
            displayResource.setAutoSourceSwitching(DeviceModelUtils.setDisplayAutoSourceSwitchResource(deviceDisplayConf.getAuto_source_switching()));
            displayResource.setAdvancedReset(deviceDisplayConf.getAdvanced_reset());
            displayResource.setAdvancedRj45SettingRefresh(deviceDisplayConf.getAdvanced_rj45_setting_refresh());
            displayResource.setAdvancedStandBy(deviceDisplayConf.getAdvanced_stand_by());
            displayResource.setAdvancedUserAutoColor(deviceDisplayConf.getAdvanced_user_auto_color());
         }

         if (deviceDisplayConf.getOsd_menu_size() != null) {
            displayResource.setOsdMenuSize(deviceDisplayConf.getOsd_menu_size());
         }

         displayResource.setMiscAllLock(deviceDisplayConf.getMisc_all_lock());
         displayResource.setMiscOsd(deviceDisplayConf.getMisc_osd());
         displayResource.setMiscPanelLock(deviceDisplayConf.getMisc_panel_lock());
         displayResource.setMiscRemocon(deviceDisplayConf.getMisc_remocon());
         displayResource.setDiagnosisAlarmTemperature(deviceDisplayConf.getDiagnosis_alarm_temperature());
         displayResource.setDiagnosisDisplayStatus(deviceDisplayConf.getDiagnosis_display_status());
         displayResource.setDiagnosisMonitorTemperature(deviceDisplayConf.getDiagnosis_monitor_temperature());
         if (deviceDisplayConf.getDiagnosis_panel_on_time() != null) {
            String[] panelTime = deviceDisplayConf.getDiagnosis_panel_on_time().split(";");
            int panelOnTime = 0;
            if (panelTime.length == 2) {
               if (!panelTime[1].equals("0")) {
                  panelOnTime = Integer.valueOf(panelTime[0]) * 256 + Integer.valueOf(panelTime[1]);
               }

               if (panelOnTime != 0) {
                  panelOnTime /= 6;
               }
            }

            displayResource.setDiagnosisPanelOnTime(String.valueOf(panelOnTime));
         }

         displayResource.setChkSchChannel(deviceDisplayConf.getChkSchChannel());
         displayResource.setWebcam(deviceDisplayConf.getWebcam());
         displayResource.setChildCnt(deviceDisplayConf.getChild_cnt());
         displayResource.setConnChildCnt(deviceDisplayConf.getConn_child_cnt());
         displayResource.setIsChild(deviceDisplayConf.getIs_child());
         displayResource.setHasChild(deviceDisplayConf.getHas_child());
         displayResource.setVwtId(deviceDisplayConf.getVwt_id());
         displayResource.setVwlFormat(deviceDisplayConf.getVwl_format());
         displayResource.setVwlLayout(deviceDisplayConf.getVwl_layout());
         displayResource.setVwlMode(deviceDisplayConf.getVwl_mode());
         displayResource.setVwlPosition(deviceDisplayConf.getVwl_position());
         displayResource.setBlackTone(deviceDisplayConf.getBlack_tone());
         displayResource.setFleshTone(deviceDisplayConf.getFlesh_tone());
         displayResource.setRgbOnlyMode(deviceDisplayConf.getRgb_only_mode());
         displayResource.setLedHdr(deviceDisplayConf.getLed_hdr());
         displayResource.setLedHdrDre(deviceDisplayConf.getLed_hdr_dre());
         displayResource.setLedPictureSize(deviceDisplayConf.getLed_picture_size());
         displayResource.setAutoMotionPlus(deviceDisplayConf.getAuto_motion_plus());
         displayResource.setAutoMotionPlusJudderReduction(deviceDisplayConf.getAuto_motion_plus_judder_reduction());
         displayResource.setEcoSensor(deviceDisplayConf.getEco_sensor());
         displayResource.setColorSpace(deviceDisplayConf.getColor_space());
         displayResource.setPictureEnhancer(deviceDisplayConf.getPicture_enhancer());
         displayResource.setSensorInternalTemperature(deviceDisplayConf.getSensor_internal_temperature());
         displayResource.setSensorInternalHumidity(deviceDisplayConf.getSensor_internal_humidity());
         displayResource.setSensorEnvironmentTemperature(deviceDisplayConf.getSensor_environment_temperature());
         displayResource.setSensorFrontglassTemperature(deviceDisplayConf.getSensor_frontglass_temperature());
         displayResource.setSensorFrontglassHumidity(deviceDisplayConf.getSensor_frontglass_humidity());
         displayResource.setErrorFlag(deviceDisplayConf.getError_flag());
         displayResource.setAdvancedOsdDisplayTypeValue(deviceDisplayConf.getAdvanced_osd_display_type_value());
         displayResource.setAutoBrightness(deviceDisplayConf.getAuto_brightness());
         displayResource.setChildAlarmTemperature(deviceDisplayConf.getChild_alarm_temperature());
         displayResource.setMinBrightness(deviceDisplayConf.getMin_brightness());
         displayResource.setLiveMode(deviceDisplayConf.getLive_mode());
         displayResource.setDisplayOutputMode(deviceDisplayConf.getDisplay_output_mode());
         displayResource.setCleanupUserData(deviceDisplayConf.getCleanup_user_data());
         displayResource.setCleanupUserDataInterval(deviceDisplayConf.getCleanup_user_data_interval());
         displayResource.setAutoSave(deviceDisplayConf.getAuto_save());
         displayResource.setAutoPowerOff(deviceDisplayConf.getAuto_power_off());
         displayResource.setSmtp(deviceDisplayConf.getSmtp());
         displayResource.setPrintServer(deviceDisplayConf.getPrint_server());
         return displayResource;
      }
   }

   public static DeviceDisplayConfResource newConvertDisplayInfoToCamelStyle(DeviceDisplayConf deviceDisplayConf) {
      long inputSource = 0L;
      DeviceDisplayConfResource displayResource = new DeviceDisplayConfResource();
      if (deviceDisplayConf == null) {
         return displayResource;
      } else {
         MonitoringManager mgr = MonitoringManagerImpl.getInstance();
         ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
         displayResource.setDeviceId(deviceDisplayConf.getDevice_id());
         displayResource.setDeviceName(deviceDisplayConf.getDevice_name());
         displayResource.setDeviceModelCode(deviceDisplayConf.getDevice_model_code());
         displayResource.setDeviceModelName(deviceDisplayConf.getDevice_model_name());
         displayResource.setDeviceType(deviceDisplayConf.getDevice_type());
         displayResource.setDeviceTypeVersion(deviceDisplayConf.getDevice_type_version());
         displayResource.setMdcUpdateTime(deviceDisplayConf.getMdc_update_time());
         String devicePower = "true";

         try {
            Map infoMap = serverSetupDao.getServerInfoByOrgId(0L);
            devicePower = infoMap.get("DEVICE_POWER").toString();
         } catch (Exception var10) {
            devicePower = "true";
         }

         if (!devicePower.equals("false")) {
            if (mgr.isConnected(deviceDisplayConf.getDevice_id())) {
               displayResource.setBasicPower("0");
               if (deviceDisplayConf.getBasic_power() != null) {
                  displayResource.setBasicPower(deviceDisplayConf.getBasic_power());
               }
            }

            displayResource.setBasicPanelStatus(deviceDisplayConf.getBasic_panel_status());
         }

         if (deviceDisplayConf.getBasic_source() != null && deviceDisplayConf.getBasic_source() > 0L) {
            displayResource.setBasicSource(deviceDisplayConf.getBasic_source());
            inputSource = deviceDisplayConf.getBasic_source();
         }

         boolean pcMode = false;
         switch((int)inputSource) {
         case 20:
         case 24:
         case 30:
         case 32:
         case 34:
         case 36:
         case 37:
         case 38:
         case 50:
         case 52:
         case 80:
            pcMode = true;
            break;
         default:
            pcMode = false;
         }

         if (pcMode) {
            if (!deviceDisplayConf.getDevice_type().equals("SIG_CHILD")) {
               displayResource.setPpcMagicBright(deviceDisplayConf.getPpc_magic_bright());
            }

            if (DeviceModelUtils.isSupport(deviceDisplayConf.getDevice_type(), "lamp_control")) {
               displayResource.setMntManual(deviceDisplayConf.getMnt_manual());
            }

            displayResource.setPpcContrast(deviceDisplayConf.getPpc_contrast());
            displayResource.setPpcBrightness(deviceDisplayConf.getPpc_brightness());
            displayResource.setPpcColortone(deviceDisplayConf.getPpc_colortone());
            displayResource.setPpcColorTemperature(deviceDisplayConf.getPpc_color_temperature());
            displayResource.setPpcSize(deviceDisplayConf.getPpc_size());
            displayResource.setPpcGamma(deviceDisplayConf.getPpc_gamma());
            displayResource.setPpcHdmiBlackLevel(deviceDisplayConf.getPpc_hdmi_black_level());
            if (deviceDisplayConf.getPv_digitalnr() != null && !deviceDisplayConf.getDevice_type().equals("SIG_CHILD")) {
               displayResource.setPvDigitalnr(deviceDisplayConf.getPv_digitalnr());
            }

            if (deviceDisplayConf.getPv_filmmode() != null && !deviceDisplayConf.getDevice_type().equals("SIG_CHILD")) {
               displayResource.setPvFilmmode(deviceDisplayConf.getPv_filmmode());
            }

            if (deviceDisplayConf.getDevice_type().equals("SIG_CHILD") && (inputSource == 20L || inputSource == 30L)) {
               displayResource.setImageCoarse(deviceDisplayConf.getImage_coarse());
               displayResource.setImageFine(deviceDisplayConf.getImage_fine());
               displayResource.setImageHpos(deviceDisplayConf.getImage_hpos());
               displayResource.setImageVpos(deviceDisplayConf.getImage_vpos());
               displayResource.setImageAuto(deviceDisplayConf.getImage_auto());
            }
         } else {
            displayResource.setPvSplPictureMode(deviceDisplayConf.getSpecialized_picture_mode());
            if (deviceDisplayConf.getPv_mode() != null && deviceDisplayConf.getPv_mode() >= 0L) {
               displayResource.setPvMode(deviceDisplayConf.getPv_mode());
            }

            if (DeviceModelUtils.isSupport(deviceDisplayConf.getDevice_type(), "lamp_control")) {
               displayResource.setMntManual(deviceDisplayConf.getMnt_manual());
            }

            displayResource.setPvContrast(deviceDisplayConf.getPv_contrast());
            displayResource.setPvBrightness(deviceDisplayConf.getPv_brightness());
            displayResource.setPvSharpness(deviceDisplayConf.getPv_sharpness());
            displayResource.setPvColor(deviceDisplayConf.getPv_color());
            displayResource.setPvTint(deviceDisplayConf.getPv_tint());
            displayResource.setPvColortone(deviceDisplayConf.getPv_colortone());
            displayResource.setPvColorTemperature(deviceDisplayConf.getPv_color_temperature());
            displayResource.setPvSize(deviceDisplayConf.getPv_size());
            if (deviceDisplayConf.getPv_digitalnr() != null && !deviceDisplayConf.getDevice_type().equals("SIG_CHILD")) {
               displayResource.setPvDigitalnr(deviceDisplayConf.getPv_digitalnr());
            }

            displayResource.setPvHdmiBlackLevel(deviceDisplayConf.getPv_hdmi_black_level());
            if (deviceDisplayConf.getAuto_motion_plus() != null) {
               displayResource.setAutoMotionPlus(deviceDisplayConf.getAuto_motion_plus());
            }

            if (deviceDisplayConf.getAuto_motion_plus_judder_reduction() != null) {
               displayResource.setAutoMotionPlusJudderReduction(deviceDisplayConf.getAuto_motion_plus_judder_reduction());
            }

            if (deviceDisplayConf.getPv_mpeg_noise_filter() != null && deviceDisplayConf.getPv_mpeg_noise_filter() >= 0L) {
               displayResource.setPvMpegNoiseFilter(deviceDisplayConf.getPv_mpeg_noise_filter());
            }
         }

         if (deviceDisplayConf.getPv_filmmode() != null && !deviceDisplayConf.getDevice_type().equals("SIG_CHILD")) {
            displayResource.setPvFilmmode(deviceDisplayConf.getPv_filmmode());
         }

         displayResource.setBasicMute(deviceDisplayConf.getBasic_mute());
         displayResource.setBasicVolume(deviceDisplayConf.getBasic_volume());
         displayResource.setBasicDirectChannel(deviceDisplayConf.getBasic_direct_channel());
         if (!"N".equals(deviceDisplayConf.getNetwork_standby_mode())) {
            displayResource.setNetworkStandbyMode(deviceDisplayConf.getNetwork_standby_mode());
         }

         if (DeviceModelUtils.isSupport(deviceDisplayConf.getDevice_type(), "sound_mode") && deviceDisplayConf.getSound_mode() != null) {
            displayResource.setSoundMode(deviceDisplayConf.getSound_mode());
         }

         if (DeviceModelUtils.isSupport(deviceDisplayConf.getDevice_type(), "sound_srs") && deviceDisplayConf.getSound_srs() != null) {
            displayResource.setSoundSrs(deviceDisplayConf.getSound_srs());
         }

         if (inputSource == 20L || inputSource == 30L) {
            if (deviceDisplayConf.getSb_status() != null && !deviceDisplayConf.getDevice_type().equals("SIG_CHILD")) {
               displayResource.setSbStatus(deviceDisplayConf.getSb_status());
            }

            displayResource.setSbRgain(deviceDisplayConf.getSb_rgain());
            displayResource.setSbGgain(deviceDisplayConf.getSb_ggain());
            displayResource.setSbBgain(deviceDisplayConf.getSb_bgain());
            if (deviceDisplayConf.getSb_r_offset() != null && !deviceDisplayConf.getDevice_type().equals("SIG_CHILD")) {
               displayResource.setSbRoffset(deviceDisplayConf.getSb_r_offset());
            }

            if (deviceDisplayConf.getSb_g_offset() != null && !deviceDisplayConf.getDevice_type().equals("SIG_CHILD")) {
               displayResource.setSbGoffset(deviceDisplayConf.getSb_g_offset());
            }

            if (deviceDisplayConf.getSb_b_offset() != null && !deviceDisplayConf.getDevice_type().equals("SIG_CHILD")) {
               displayResource.setSbBoffset(deviceDisplayConf.getSb_b_offset());
            }

            if (deviceDisplayConf.getSb_sharp() != null && deviceDisplayConf.getSb_sharp() >= 0L) {
               displayResource.setSbSharp(deviceDisplayConf.getSb_sharp());
            }

            if (deviceDisplayConf.getSb_gain() != null && deviceDisplayConf.getSb_gain() >= 0L && !deviceDisplayConf.getDevice_type().equals("SIG_CHILD")) {
               displayResource.setSbGain(deviceDisplayConf.getSb_gain());
            }
         }

         if (DeviceModelUtils.isSupport(deviceDisplayConf.getDevice_type(), "screen_lamp_schedule") && deviceDisplayConf.getMnt_auto() != null) {
            displayResource.setMntAuto(DeviceModelUtils.setNewDisplayMntAutoResource(deviceDisplayConf.getMnt_auto()));
         }

         if (DeviceModelUtils.isSupport(deviceDisplayConf.getDevice_type(), "safety_screen_timer") && deviceDisplayConf.getMnt_safety_screen_timer() != null) {
            displayResource.setMntSafetyScreenTimer(DeviceModelUtils.setNewDisplayMntSafetyScreenTimerResource(deviceDisplayConf.getMnt_safety_screen_timer()));
         }

         if (DeviceModelUtils.isSupport(deviceDisplayConf.getDevice_type(), "safety_screen_run") && deviceDisplayConf.getMnt_safety_screen_run() != null) {
            displayResource.setMntSafetyScreenRun(deviceDisplayConf.getMnt_safety_screen_run());
         }

         if (DeviceModelUtils.isSupport(deviceDisplayConf.getDevice_type(), "pixel_shift") && deviceDisplayConf.getMnt_pixel_shift() != null) {
            displayResource.setMntPixelShift(DeviceModelUtils.setDisplayMntPixelShiftResource(deviceDisplayConf.getMnt_pixel_shift()));
         }

         if (deviceDisplayConf.getDevice_model_code() != null && Integer.valueOf(deviceDisplayConf.getDevice_model_code()) > 55) {
            if (deviceDisplayConf.getBlack_tone() != null && deviceDisplayConf.getBlack_tone() >= 0L) {
               displayResource.setBlackTone(deviceDisplayConf.getBlack_tone());
            }

            if (deviceDisplayConf.getFlesh_tone() != null && deviceDisplayConf.getFlesh_tone() >= -15L) {
               displayResource.setFleshTone(deviceDisplayConf.getFlesh_tone());
            }

            if (deviceDisplayConf.getRgb_only_mode() != null && deviceDisplayConf.getRgb_only_mode() >= 0L) {
               displayResource.setRgbOnlyMode(deviceDisplayConf.getRgb_only_mode());
            }

            displayResource.setColorSpace(deviceDisplayConf.getColor_space());
            displayResource.setAdvancedOsdDisplayType(DeviceModelUtils.setDisplayAdvOsdDisplayTypeResource(deviceDisplayConf.getAdvanced_osd_display_type()));
            displayResource.setAutoSourceSwitching(DeviceModelUtils.setDisplayAutoSourceSwitchResource(deviceDisplayConf.getAuto_source_switching()));
            displayResource.setAdvancedReset(deviceDisplayConf.getAdvanced_reset());
            displayResource.setAdvancedRj45SettingRefresh(deviceDisplayConf.getAdvanced_rj45_setting_refresh());
            if (deviceDisplayConf.getAdvanced_stand_by() != null && deviceDisplayConf.getAdvanced_stand_by() >= 0L) {
               displayResource.setAdvancedStandBy(deviceDisplayConf.getAdvanced_stand_by());
            }

            displayResource.setAdvancedUserAutoColor(deviceDisplayConf.getAdvanced_user_auto_color());
            displayResource.setPictureEnhancer(deviceDisplayConf.getPicture_enhancer());
            if (deviceDisplayConf.getEco_sensor() != null && deviceDisplayConf.getEco_sensor() >= 0L) {
               displayResource.setEcoSensor(deviceDisplayConf.getEco_sensor());
               if (deviceDisplayConf.getMin_brightness() != null && deviceDisplayConf.getMin_brightness() >= 0L) {
                  displayResource.setMinBrightness(deviceDisplayConf.getMin_brightness());
               }
            }

            if (deviceDisplayConf.getLive_mode() != null && deviceDisplayConf.getLive_mode() >= 0L) {
               displayResource.setLiveMode(deviceDisplayConf.getLive_mode());
            }

            displayResource.setAdvancedReset((Long)null);
         }

         displayResource.setPvVideoPicturePositionSize(deviceDisplayConf.getPv_video_picture_position_size());
         displayResource.setPpcBlue(deviceDisplayConf.getPpc_blue());
         displayResource.setPpcGreen(deviceDisplayConf.getPpc_green());
         displayResource.setPpcRed(deviceDisplayConf.getPpc_red());
         displayResource.setTimeCurrentTime(deviceDisplayConf.getTime_current_time());
         displayResource.setTimeOnTime(deviceDisplayConf.getTime_on_time());
         displayResource.setTimeOffTime(deviceDisplayConf.getTime_off_time());
         displayResource.setPipControl(deviceDisplayConf.getPip_control());
         displayResource.setPipPosition(deviceDisplayConf.getPip_position());
         displayResource.setPipSize(deviceDisplayConf.getPip_size());
         displayResource.setPipSource(deviceDisplayConf.getPip_source());
         displayResource.setPipSwap(deviceDisplayConf.getPip_swap());
         displayResource.setSoundBalance(deviceDisplayConf.getSound_balance());
         displayResource.setSoundBass(deviceDisplayConf.getSound_bass());
         displayResource.setSoundEffect(deviceDisplayConf.getSound_effect());
         displayResource.setSoundTreble(deviceDisplayConf.getSound_treble());
         displayResource.setMntFormat(deviceDisplayConf.getMnt_format());
         displayResource.setMntSafetyLock(deviceDisplayConf.getMnt_safety_lock());
         if (deviceDisplayConf.getMax_power_saving() != null && deviceDisplayConf.getMax_power_saving() >= 0L) {
            displayResource.setMaxPowerSaving(deviceDisplayConf.getMax_power_saving());
         }

         if (deviceDisplayConf.getBrightness_limit() != null && deviceDisplayConf.getBrightness_limit() >= 0L) {
            displayResource.setBrightnessLimit(deviceDisplayConf.getBrightness_limit());
         }

         displayResource.setTouchControlLock(deviceDisplayConf.getTouch_control_lock());
         displayResource.setWebBrowserUrl(DeviceModelUtils.setDisplayWebBrowserUrlResource(deviceDisplayConf.getWeb_browser_url()));
         displayResource.setCustomLogo(DeviceModelUtils.setDisplayCustomLogoResource(deviceDisplayConf.getCustom_logo()));
         if (deviceDisplayConf.getScreen_mute() != null && deviceDisplayConf.getScreen_mute() >= 0L) {
            displayResource.setScreenMute(deviceDisplayConf.getScreen_mute());
         }

         if (deviceDisplayConf.getScreen_freeze() != null && deviceDisplayConf.getScreen_freeze() >= 0L) {
            displayResource.setScreenFreeze(deviceDisplayConf.getScreen_freeze());
         }

         displayResource.setMntVideoWall(deviceDisplayConf.getMnt_video_wall());
         displayResource.setAdvancedAutoPower(deviceDisplayConf.getAdvanced_auto_power());
         displayResource.setAdvancedFanControl(deviceDisplayConf.getAdvanced_fan_control());
         if (deviceDisplayConf.getAdvanced_fan_speed() != null && deviceDisplayConf.getAdvanced_fan_speed() >= 0L) {
            displayResource.setAdvancedFanSpeed(deviceDisplayConf.getAdvanced_fan_speed());
         } else {
            displayResource.setAdvancedFanSpeed(-1L);
         }

         if (deviceDisplayConf.getOsd_menu_size() != null) {
            displayResource.setOsdMenuSize(deviceDisplayConf.getOsd_menu_size());
         }

         displayResource.setMiscAllLock(deviceDisplayConf.getMisc_all_lock());
         displayResource.setMiscOsd(deviceDisplayConf.getMisc_osd());
         displayResource.setMiscPanelLock(deviceDisplayConf.getMisc_panel_lock());
         displayResource.setMiscRemocon(deviceDisplayConf.getMisc_remocon());
         displayResource.setDiagnosisAlarmTemperature(deviceDisplayConf.getDiagnosis_alarm_temperature());
         displayResource.setDiagnosisDisplayStatus(deviceDisplayConf.getDiagnosis_display_status());
         displayResource.setDiagnosisMonitorTemperature(deviceDisplayConf.getDiagnosis_monitor_temperature());
         if (DeviceModelUtils.isSupport(deviceDisplayConf.getDevice_type(), "panel_on_time") && deviceDisplayConf.getDiagnosis_panel_on_time() != null) {
            String[] panelTime = deviceDisplayConf.getDiagnosis_panel_on_time().split(";");
            int panelOnTime = 0;
            if (panelTime.length == 2) {
               if (!panelTime[1].equals("0")) {
                  panelOnTime = Integer.valueOf(panelTime[0]) * 256 + Integer.valueOf(panelTime[1]);
               }

               if (panelOnTime != 0) {
                  panelOnTime /= 6;
               }
            }

            displayResource.setDiagnosisPanelOnTime(String.valueOf(panelOnTime));
         }

         displayResource.setLedHdr(deviceDisplayConf.getLed_hdr());
         displayResource.setLedHdrDre(deviceDisplayConf.getLed_hdr_dre());
         displayResource.setLedPictureSize(deviceDisplayConf.getLed_picture_size());
         displayResource.setSensorInternalTemperature(deviceDisplayConf.getSensor_internal_temperature());
         displayResource.setSensorInternalHumidity(deviceDisplayConf.getSensor_internal_humidity());
         displayResource.setSensorEnvironmentTemperature(deviceDisplayConf.getSensor_environment_temperature());
         displayResource.setSensorFrontglassTemperature(deviceDisplayConf.getSensor_frontglass_temperature());
         displayResource.setSensorFrontglassHumidity(deviceDisplayConf.getSensor_frontglass_humidity());
         displayResource.setErrorFlag(deviceDisplayConf.getError_flag());
         displayResource.setAdvancedOsdDisplayTypeValue(deviceDisplayConf.getAdvanced_osd_display_type_value());
         displayResource.setAutoBrightness(deviceDisplayConf.getAuto_brightness());
         displayResource.setChildAlarmTemperature(deviceDisplayConf.getChild_alarm_temperature());
         displayResource.setDisplayOutputMode(deviceDisplayConf.getDisplay_output_mode());
         displayResource.setCleanupUserData(deviceDisplayConf.getCleanup_user_data());
         displayResource.setCleanupUserDataInterval(deviceDisplayConf.getCleanup_user_data_interval());
         displayResource.setAutoSave(deviceDisplayConf.getAuto_save());
         displayResource.setAutoPowerOff(deviceDisplayConf.getAuto_power_off());
         displayResource.setSmtp(deviceDisplayConf.getSmtp());
         displayResource.setPrintServer(deviceDisplayConf.getPrint_server());
         displayResource.setInstallEnvironment(deviceDisplayConf.getInstall_environment());
         displayResource.setDehumidify(DeviceModelUtils.setDehumidify(deviceDisplayConf.getDehumidify()));
         displayResource.setDimmingOption(deviceDisplayConf.getDimming_option());
         displayResource.setDimmingNightTimeOverride(deviceDisplayConf.getDimming_night_time_override());
         displayResource.setDimmingEcoSensor(DeviceModelUtils.setDimmingEcoSensor(deviceDisplayConf.getDimming_eco_sensor()));
         displayResource.setDimmingSunriseSunset(DeviceModelUtils.setDimmingSunriseSunset(deviceDisplayConf.getDimming_sunrise_sunset()));
         displayResource.setDimmingSunriseSunsetTimes(DeviceModelUtils.setDimmingSunriseSunsetTimes(deviceDisplayConf.getDimming_sunrise_sunset_times()));
         displayResource.setDimmingBrightnessOutput(DeviceModelUtils.setDimmingBrightnessOutput(deviceDisplayConf.getDimming_brightness_output()));
         return displayResource;
      }
   }

   public static DeviceSecurityConfResource convertSecurityInfoToCamelStyle(DeviceSecurityConf deviceSecurityConf) {
      DeviceSecurityConfResource securityResource = new DeviceSecurityConfResource();
      if (deviceSecurityConf == null) {
         return securityResource;
      } else {
         securityResource.setDeviceId(deviceSecurityConf.getDevice_id());
         securityResource.setDeviceType(deviceSecurityConf.getDevice_type());
         securityResource.setMntSafetyLock(deviceSecurityConf.getMnt_safety_lock());
         securityResource.setMiscAllLock(deviceSecurityConf.getMisc_all_lock());
         securityResource.setMiscPanelLock(deviceSecurityConf.getMisc_panel_lock());
         securityResource.setMiscRemocon(deviceSecurityConf.getMisc_remocon());
         securityResource.setIsInitSecurity(deviceSecurityConf.getIs_init_security());
         securityResource.setTouchControlLock(deviceSecurityConf.getTouch_control_lock());
         securityResource.setMiscBlockUsbPort(deviceSecurityConf.getMisc_block_usb_port());
         securityResource.setMiscBlockNetworkConnection(deviceSecurityConf.getMisc_block_network_connection());
         securityResource.setMiscWhiteList(deviceSecurityConf.getMisc_white_list());
         securityResource.setMiscServerNetworkSetting(deviceSecurityConf.getMisc_server_network_setting());
         securityResource.setBluetoothLock(deviceSecurityConf.getBluetooth_lock());
         securityResource.setWifiLock(deviceSecurityConf.getWifi_lock());
         securityResource.setSourceLock(DeviceUtils.stringToSourceLockList(deviceSecurityConf.getSource_lock()));
         securityResource.setScreenMonitoringLock(deviceSecurityConf.getScreen_monitoring_lock());
         if (null != deviceSecurityConf.getScreen_monitoring_lock()) {
            securityResource.setRemoteControlServerLock(deviceSecurityConf.getRemote_control_server_lock() == null ? 0L : deviceSecurityConf.getRemote_control_server_lock());
         }

         securityResource.setCaptureLock(deviceSecurityConf.getCapture_lock());
         return securityResource;
      }
   }

   public static HashMap convertDisplayInfoToCamelStyleWithoutNull(DeviceDisplayConf deviceDisplayConf) {
      HashMap result = new HashMap();
      if (deviceDisplayConf.getDevice_id() != null) {
         result.put("deviceId", deviceDisplayConf.getDevice_id());
      }

      if (deviceDisplayConf.getDevice_name() != null) {
         result.put("deviceName", deviceDisplayConf.getDevice_name());
      }

      if (deviceDisplayConf.getDevice_model_code() != null) {
         result.put("deviceModelCode", deviceDisplayConf.getDevice_model_code());
      }

      if (deviceDisplayConf.getDevice_model_name() != null) {
         result.put("deviceModelName", deviceDisplayConf.getDevice_model_name());
      }

      if (deviceDisplayConf.getDevice_type() != null) {
         result.put("deviceType", deviceDisplayConf.getDevice_type());
      }

      if (deviceDisplayConf.getDevice_type_version() != null) {
         result.put("deviceTypeVersion", deviceDisplayConf.getDevice_type_version());
      }

      if (deviceDisplayConf.getBasic_power() != null) {
         result.put("basicPower", deviceDisplayConf.getBasic_power());
      }

      if (deviceDisplayConf.getBasic_panel_status() != null) {
         result.put("basicPanelStatus", deviceDisplayConf.getBasic_panel_status());
      }

      if (deviceDisplayConf.getBasic_source() != null) {
         result.put("basicSource", deviceDisplayConf.getBasic_source());
      }

      if (deviceDisplayConf.getBasic_mute() != null) {
         result.put("basicMute", deviceDisplayConf.getBasic_mute());
      }

      if (deviceDisplayConf.getBasic_volume() != null) {
         result.put("basicVolume", deviceDisplayConf.getBasic_volume());
      }

      if (deviceDisplayConf.getBasic_direct_channel() != null) {
         result.put("basicDirectChannel", deviceDisplayConf.getBasic_direct_channel());
      }

      if (deviceDisplayConf.getNetwork_standby_mode() != null) {
         result.put("networkStandbyMode", deviceDisplayConf.getNetwork_standby_mode());
      }

      if (deviceDisplayConf.getPv_brightness() != null) {
         result.put("pvBrightness", deviceDisplayConf.getPv_brightness());
      }

      if (deviceDisplayConf.getSpecialized_picture_mode() != null) {
         result.put("pvSplPictureMode", deviceDisplayConf.getSpecialized_picture_mode());
      }

      if (deviceDisplayConf.getPv_color() != null) {
         result.put("pvColor", deviceDisplayConf.getPv_color());
      }

      if (deviceDisplayConf.getPv_color_temperature() != null) {
         result.put("pvColorTemperature", deviceDisplayConf.getPv_color_temperature());
      }

      if (deviceDisplayConf.getPv_colortone() != null) {
         result.put("pvColortone", deviceDisplayConf.getPv_colortone());
      }

      if (deviceDisplayConf.getPv_contrast() != null) {
         result.put("pvContrast", deviceDisplayConf.getPv_contrast());
      }

      if (deviceDisplayConf.getPv_digitalnr() != null) {
         result.put("pvDigitalnr", deviceDisplayConf.getPv_digitalnr());
      }

      if (deviceDisplayConf.getPv_filmmode() != null) {
         result.put("pvFilmmode", deviceDisplayConf.getPv_filmmode());
      }

      if (deviceDisplayConf.getPv_hdmi_black_level() != null) {
         result.put("pvHdmiBlackLevel", deviceDisplayConf.getPv_hdmi_black_level());
      }

      if (deviceDisplayConf.getPv_mode() != null) {
         result.put("pvMode", deviceDisplayConf.getPv_mode());
      }

      if (deviceDisplayConf.getPv_sharpness() != null) {
         result.put("pvSharpness", deviceDisplayConf.getPv_sharpness());
      }

      if (deviceDisplayConf.getPv_size() != null) {
         result.put("pvSize", deviceDisplayConf.getPv_size());
      }

      if (deviceDisplayConf.getPv_tint() != null) {
         result.put("pvTint", deviceDisplayConf.getPv_tint());
      }

      if (deviceDisplayConf.getPv_video_picture_position_size() != null) {
         result.put("pvVideoPicturePositionSize", deviceDisplayConf.getPv_video_picture_position_size());
      }

      if (deviceDisplayConf.getPpc_blue() != null) {
         result.put("ppcBlue", deviceDisplayConf.getPpc_blue());
      }

      if (deviceDisplayConf.getPpc_brightness() != null) {
         result.put("ppcBrightness", deviceDisplayConf.getPpc_brightness());
      }

      if (deviceDisplayConf.getPpc_color_temperature() != null) {
         result.put("ppcColorTemperature", deviceDisplayConf.getPpc_color_temperature());
      }

      if (deviceDisplayConf.getPpc_colortone() != null) {
         result.put("ppcColortone", deviceDisplayConf.getPpc_colortone());
      }

      if (deviceDisplayConf.getPpc_contrast() != null) {
         result.put("ppcContrast", deviceDisplayConf.getPpc_contrast());
      }

      if (deviceDisplayConf.getPpc_gamma() != null) {
         result.put("ppcGamma", deviceDisplayConf.getPpc_gamma());
      }

      if (deviceDisplayConf.getPpc_green() != null) {
         result.put("ppcGreen", deviceDisplayConf.getPpc_green());
      }

      if (deviceDisplayConf.getPpc_hdmi_black_level() != null) {
         result.put("ppcHdmiBlackLevel", deviceDisplayConf.getPpc_hdmi_black_level());
      }

      if (deviceDisplayConf.getPpc_magic_bright() != null) {
         result.put("ppcMagicBright", deviceDisplayConf.getPpc_magic_bright());
      }

      if (deviceDisplayConf.getPpc_red() != null) {
         result.put("ppcRed", deviceDisplayConf.getPpc_red());
      }

      if (deviceDisplayConf.getPpc_size() != null) {
         result.put("ppcSize", deviceDisplayConf.getPpc_size());
      }

      if (deviceDisplayConf.getTime_current_time() != null) {
         result.put("timeCurrentTime", deviceDisplayConf.getTime_current_time());
      }

      if (deviceDisplayConf.getTime_on_time() != null) {
         result.put("timeOnTime", deviceDisplayConf.getTime_on_time());
      }

      if (deviceDisplayConf.getTime_off_time() != null) {
         result.put("timeOffTime", deviceDisplayConf.getTime_off_time());
      }

      if (deviceDisplayConf.getPip_control() != null) {
         result.put("pipControl", deviceDisplayConf.getPip_control());
      }

      if (deviceDisplayConf.getPip_position() != null) {
         result.put("pipPosition", deviceDisplayConf.getPip_position());
      }

      if (deviceDisplayConf.getPip_size() != null) {
         result.put("pipSize", deviceDisplayConf.getPip_size());
      }

      if (deviceDisplayConf.getPip_source() != null) {
         result.put("pipSource", deviceDisplayConf.getPip_source());
      }

      if (deviceDisplayConf.getPip_swap() != null) {
         result.put("pipSwap", deviceDisplayConf.getPip_swap());
      }

      if (deviceDisplayConf.getSound_balance() != null) {
         result.put("soundBalance", deviceDisplayConf.getSound_balance());
      }

      if (deviceDisplayConf.getSound_bass() != null) {
         result.put("soundBass", deviceDisplayConf.getSound_bass());
      }

      if (deviceDisplayConf.getSound_effect() != null) {
         result.put("soundEffect", deviceDisplayConf.getSound_effect());
      }

      if (deviceDisplayConf.getSound_mode() != null) {
         result.put("soundMode", deviceDisplayConf.getSound_mode());
      }

      if (deviceDisplayConf.getSound_srs() != null) {
         result.put("soundSrs", deviceDisplayConf.getSound_srs());
      }

      if (deviceDisplayConf.getSound_treble() != null) {
         result.put("soundTreble", deviceDisplayConf.getSound_treble());
      }

      if (deviceDisplayConf.getImage_auto() != null) {
         result.put("imageAuto", deviceDisplayConf.getImage_auto());
      }

      if (deviceDisplayConf.getImage_coarse() != null) {
         result.put("imageCoarse", deviceDisplayConf.getImage_coarse());
      }

      if (deviceDisplayConf.getImage_fine() != null) {
         result.put("imageFine", deviceDisplayConf.getImage_fine());
      }

      if (deviceDisplayConf.getImage_hpos() != null) {
         result.put("imageHpos", deviceDisplayConf.getImage_hpos());
      }

      if (deviceDisplayConf.getImage_vpos() != null) {
         result.put("imageVpos", deviceDisplayConf.getImage_vpos());
      }

      if (deviceDisplayConf.getSb_b_offset() != null) {
         result.put("sbBoffset", deviceDisplayConf.getSb_b_offset());
      }

      if (deviceDisplayConf.getSb_bgain() != null) {
         result.put("sbBgain", deviceDisplayConf.getSb_bgain());
      }

      if (deviceDisplayConf.getSb_g_offset() != null) {
         result.put("sbGoffset", deviceDisplayConf.getSb_g_offset());
      }

      if (deviceDisplayConf.getSb_gain() != null) {
         result.put("sbGain", deviceDisplayConf.getSb_gain());
      }

      if (deviceDisplayConf.getSb_ggain() != null) {
         result.put("sbGgain", deviceDisplayConf.getSb_ggain());
      }

      if (deviceDisplayConf.getSb_r_offset() != null) {
         result.put("sbRoffset", deviceDisplayConf.getSb_r_offset());
      }

      if (deviceDisplayConf.getSb_rgain() != null) {
         result.put("sbRgain", deviceDisplayConf.getSb_rgain());
      }

      if (deviceDisplayConf.getSb_sharp() != null) {
         result.put("sbSharp", deviceDisplayConf.getSb_sharp());
      }

      if (deviceDisplayConf.getSb_status() != null) {
         result.put("sbStatus", deviceDisplayConf.getSb_status());
      }

      if (deviceDisplayConf.getMnt_auto() != null) {
         result.put("mntAuto", deviceDisplayConf.getMnt_auto());
      }

      if (deviceDisplayConf.getMnt_format() != null) {
         result.put("mntFormat", deviceDisplayConf.getMnt_format());
      }

      if (deviceDisplayConf.getMnt_manual() != null) {
         result.put("mntManual", deviceDisplayConf.getMnt_manual());
      }

      if (deviceDisplayConf.getMnt_pixel_shift() != null) {
         result.put("mntPixelShift", deviceDisplayConf.getMnt_pixel_shift());
      }

      if (deviceDisplayConf.getMnt_safety_lock() != null) {
         result.put("mntSafetyLock", deviceDisplayConf.getMnt_safety_lock());
      }

      if (deviceDisplayConf.getMnt_safety_screen_run() != null) {
         result.put("mntSafetyScreenRun", deviceDisplayConf.getMnt_safety_screen_run());
      }

      if (deviceDisplayConf.getMnt_safety_screen_timer() != null) {
         result.put("mntSafetyScreenTimer", deviceDisplayConf.getMnt_safety_screen_timer());
      }

      if (deviceDisplayConf.getMnt_video_wall() != null) {
         result.put("mntVideoWall", deviceDisplayConf.getMnt_video_wall());
      }

      if (deviceDisplayConf.getAdvanced_auto_power() != null) {
         result.put("advancedAutoPower", deviceDisplayConf.getAdvanced_auto_power());
      }

      if (deviceDisplayConf.getAdvanced_fan_control() != null) {
         result.put("advancedFanControl", deviceDisplayConf.getAdvanced_fan_control());
      }

      if (deviceDisplayConf.getAdvanced_fan_speed() != null) {
         result.put("advancedFanSpeed", deviceDisplayConf.getAdvanced_fan_speed());
      }

      if (deviceDisplayConf.getAdvanced_osd_display_type() != null) {
         result.put("advancedOsdDisplayType", deviceDisplayConf.getAdvanced_osd_display_type());
      }

      if (deviceDisplayConf.getAdvanced_reset() != null) {
         result.put("advancedReset", deviceDisplayConf.getAdvanced_reset());
      }

      if (deviceDisplayConf.getAdvanced_rj45_setting_refresh() != null) {
         result.put("advancedRj45SettingRefresh", deviceDisplayConf.getAdvanced_rj45_setting_refresh());
      }

      if (deviceDisplayConf.getAdvanced_stand_by() != null) {
         result.put("advancedStandBy", deviceDisplayConf.getAdvanced_stand_by());
      }

      if (deviceDisplayConf.getAdvanced_user_auto_color() != null) {
         result.put("advancedUserAutoColor", deviceDisplayConf.getAdvanced_user_auto_color());
      }

      if (deviceDisplayConf.getMisc_all_lock() != null) {
         result.put("miscAllLock", deviceDisplayConf.getMisc_all_lock());
      }

      if (deviceDisplayConf.getMisc_osd() != null) {
         result.put("miscOsd", deviceDisplayConf.getMisc_osd());
      }

      if (deviceDisplayConf.getMisc_panel_lock() != null) {
         result.put("miscPanelLock", deviceDisplayConf.getMisc_panel_lock());
      }

      if (deviceDisplayConf.getMisc_remocon() != null) {
         result.put("miscRemocon", deviceDisplayConf.getMisc_remocon());
      }

      if (deviceDisplayConf.getDiagnosis_alarm_temperature() != null) {
         result.put("diagnosisAlarmTemperature", deviceDisplayConf.getDiagnosis_alarm_temperature());
      }

      if (deviceDisplayConf.getDiagnosis_display_status() != null) {
         result.put("diagnosisDisplayStatus", deviceDisplayConf.getDiagnosis_display_status());
      }

      if (deviceDisplayConf.getDiagnosis_monitor_temperature() != null) {
         result.put("diagnosisMonitorTemperature", deviceDisplayConf.getDiagnosis_monitor_temperature());
      }

      if (deviceDisplayConf.getDiagnosis_panel_on_time() != null) {
         result.put("diagnosisPanelOnTime", deviceDisplayConf.getDiagnosis_panel_on_time());
      }

      if (deviceDisplayConf.getChkSchChannel() != null) {
         result.put("chkSchChannel", deviceDisplayConf.getChkSchChannel());
      }

      if (deviceDisplayConf.getWebcam() != null) {
         result.put("webcam", deviceDisplayConf.getWebcam());
      }

      if (deviceDisplayConf.getChild_cnt() != null) {
         result.put("childCnt", deviceDisplayConf.getChild_cnt());
      }

      if (deviceDisplayConf.getConn_child_cnt() != null) {
         result.put("connChildCnt", deviceDisplayConf.getConn_child_cnt());
      }

      if (deviceDisplayConf.getIs_child() != null) {
         result.put("isChild", deviceDisplayConf.getIs_child());
      }

      if (deviceDisplayConf.getHas_child() != null) {
         result.put("hasChild", deviceDisplayConf.getHas_child());
      }

      if (deviceDisplayConf.getVwt_id() != null) {
         result.put("vwtId", deviceDisplayConf.getVwt_id());
      }

      if (deviceDisplayConf.getVwl_format() != null) {
         result.put("vwlFormat", deviceDisplayConf.getVwl_format());
      }

      if (deviceDisplayConf.getVwl_layout() != null) {
         result.put("vwlLayout", deviceDisplayConf.getVwl_layout());
      }

      if (deviceDisplayConf.getVwl_mode() != null) {
         result.put("vwlMode", deviceDisplayConf.getVwl_mode());
      }

      if (deviceDisplayConf.getVwl_position() != null) {
         result.put("vwlPosition", deviceDisplayConf.getVwl_position());
      }

      if (deviceDisplayConf.getMax_power_saving() != null) {
         result.put("maxPowerSaving", deviceDisplayConf.getMax_power_saving());
      }

      if (deviceDisplayConf.getBrightness_limit() != null) {
         result.put("brightnessLimit", deviceDisplayConf.getBrightness_limit());
      }

      if (deviceDisplayConf.getTouch_control_lock() != null) {
         result.put("touchControlLock", deviceDisplayConf.getTouch_control_lock());
      }

      if (deviceDisplayConf.getWeb_browser_url() != null) {
         result.put("webBrowserUrl", deviceDisplayConf.getWeb_browser_url());
      }

      if (deviceDisplayConf.getCustom_logo() != null) {
         result.put("customLogo", deviceDisplayConf.getCustom_logo());
      }

      if (deviceDisplayConf.getAuto_source_switching() != null) {
         result.put("autoSourceSwitching", deviceDisplayConf.getAuto_source_switching());
      }

      return result;
   }

   public static DeviceSystemSetupConfResource convertSystemSetupToCamelStyle(DeviceSystemSetupConf systemSetup) throws SQLException {
      DeviceSystemSetupConfResource systemSetupCamel = new DeviceSystemSetupConfResource();
      boolean rmsDevice = false;

      try {
         if (systemSetup.getDevice_type().equals("RSPLAYER") || systemSetup.getDevice_type().equals("RIPLAYER")) {
            rmsDevice = true;
         }
      } catch (Exception var17) {
         logger.error("", var17);
      }

      systemSetupCamel.setDeviceId(systemSetup.getDevice_id());
      systemSetupCamel.setDeviceName(systemSetup.getDevice_name());
      systemSetupCamel.setDeviceModelName(systemSetup.getDevice_model_name());
      systemSetupCamel.setDeviceType(systemSetup.getDevice_type());
      systemSetupCamel.setDeviceTypeVersion(systemSetup.getDevice_type_version());
      systemSetupCamel.setTimeZoneIndex(systemSetup.getTime_zone_index());
      systemSetupCamel.setDayLightSaving(systemSetup.getDay_light_saving());
      systemSetupCamel.setDayLightSavingManual(systemSetup.getDay_light_saving_manual());
      systemSetupCamel.setAutoTimeSetting(systemSetup.getAuto_time_setting());
      systemSetupCamel.setOnTimerSetting(systemSetup.getOn_timer_setting());
      systemSetupCamel.setOffTimerSetting(systemSetup.getOff_timer_setting());
      systemSetupCamel.setMagicinfoServerUrl(systemSetup.getMagicinfo_server_url());
      systemSetupCamel.setIsReverse(systemSetup.getIs_reverse());
      systemSetupCamel.setTunnelingServer(systemSetup.getTunneling_server());
      systemSetupCamel.setTriggerInterval(systemSetup.getTrigger_interval());
      systemSetupCamel.setRepositoryPath(systemSetup.getRepository_path());
      systemSetupCamel.setScreenCaptureInterval(systemSetup.getScreen_capture_interval());
      systemSetupCamel.setMonitoringInterval(systemSetup.getMonitoring_interval());
      systemSetupCamel.setChildMonitoringInterval(systemSetup.getChild_monitoring_interval());
      systemSetupCamel.setLastConnectionTime(systemSetup.getLast_connection_time());
      systemSetupCamel.setProxySetting(systemSetup.getProxy_setting());
      systemSetupCamel.setProxyException(systemSetup.getProxy_exclude_list());
      systemSetupCamel.setProtocolPriority(systemSetup.getProtocol_priority());
      systemSetupCamel.setVwtId(systemSetup.getVwt_id());
      systemSetupCamel.setMntFolderPath(systemSetup.getMnt_folder_path());
      if (systemSetup.getSystem_restart_interval() != null) {
         systemSetupCamel.setSystemRestartInterval(systemSetup.getSystem_restart_interval().toLowerCase());
      }

      systemSetupCamel.setLogMnt(systemSetup.getLog_mnt());
      systemSetupCamel.setContentMnt(systemSetup.getContent_mnt());
      systemSetupCamel.setPlayMode(systemSetup.getPlay_mode());
      systemSetupCamel.setTimeZoneVersion(systemSetup.getTime_zone_version());
      systemSetupCamel.setRebootFlag(systemSetup.getReboot_flag());
      systemSetupCamel.setLastSentEvent(systemSetup.getLast_sent_event());
      systemSetupCamel.setAutoIpSet(systemSetup.getAuto_ip_set());
      systemSetupCamel.setAutoComputerNameSet(systemSetup.getAuto_computer_name_set());
      systemSetupCamel.setVncPassword(systemSetup.getVnc_password());
      systemSetupCamel.setFtpPort(systemSetup.getFtp_port());
      systemSetupCamel.setDatalinkServer(systemSetup.getDatalink_server());
      systemSetupCamel.setFiledataDelSize(systemSetup.getFiledata_del_size());
      systemSetupCamel.setContentReadyInterval(systemSetup.getContent_ready_interval());
      systemSetupCamel.setPlayerStartTimeout(systemSetup.getPlayer_start_timeout());
      systemSetupCamel.setDatalinkServer(systemSetup.getDatalink_server());
      systemSetupCamel.setCpuType(systemSetup.getCpu_type());
      systemSetupCamel.setStatisticsFileRefresh(systemSetup.getStatisticsFileRefresh());
      systemSetupCamel.setChildCnt(systemSetup.getChild_cnt());
      systemSetupCamel.setIsRedundancy(systemSetup.getIs_redundancy());
      systemSetupCamel.setIsChild(systemSetup.getIs_child());
      systemSetupCamel.setExpirationDate(systemSetup.getExpiration_date());
      String device_id = systemSetup.getDevice_id();
      systemSetupCamel.setMultiTimeZoneIndex(device_id, systemSetup.getMulti_time_zone_index(device_id));
      systemSetupCamel.setMultiDayLightSaving(device_id, systemSetup.getMulti_day_light_saving(device_id));
      if (systemSetup.getUrl_launcher() != null) {
         setUrlLauncherSettings(systemSetup, systemSetupCamel);
      }

      if (!rmsDevice) {
         systemSetupCamel.setFtpConnectMode(systemSetup.getFtp_connect_mode());
         systemSetupCamel.setConnectionLimitTime(systemSetup.getConnection_limit_time());
         systemSetupCamel.setBandwidth(systemSetup.getBandwidth());
         if (!systemSetup.getSwitch_time().equals("0") && systemSetup.getSwitch_time() != null) {
            systemSetupCamel.setSwitchTime(systemSetup.getSwitch_time());
         }

         systemSetupCamel.setProofOfPlayMnt(systemSetup.getProof_of_play_mnt());
         if (!systemSetup.getDevice_type().equals("LPLAYER")) {
            DeviceSystemSetupConfManager dao = DeviceSystemSetupConfManagerImpl.getInstance("PREMIUM");
            String tagIdString = dao.getTagIdListString(systemSetup.getDevice_id());
            String tagValueString = dao.getTagValueListString(systemSetup.getDevice_id());
            List tagList = null;
            if (tagIdString != null && tagValueString != null && tagIdString.length() > 0 && tagValueString.length() > 0) {
               tagList = new ArrayList();
               String[] idtmp = tagIdString.split(",");
               String[] valuetmp = tagValueString.split(",");
               if (idtmp.length == valuetmp.length) {
                  for(int i = 0; i < idtmp.length; ++i) {
                     HashMap tag = new HashMap();
                     tag.put("id", idtmp[i]);
                     tag.put("value", valuetmp[i]);
                     tagList.add(tag);
                  }
               }
            }

            systemSetupCamel.setTagList(tagList);
            systemSetupCamel.setTagIdList(tagIdString);
         }

         systemSetupCamel.setContentsProgressEnable(systemSetup.getContents_progress_enable());
         systemSetupCamel.setContentsProgressUnit(systemSetup.getContents_progress_unit());
         systemSetupCamel.setContentsProgressInterval(systemSetup.getContents_progress_interval());
         systemSetupCamel.setContentsDownloadMode(systemSetup.getContents_download_mode());
         systemSetupCamel.setSmartDownload(systemSetup.getSmart_download());
         EdgeServerDao edgeServerDao = new EdgeServerDao();
         List edgeServerList = null;
         ArrayList downloadLists = new ArrayList();

         try {
            edgeServerList = edgeServerDao.selectRegisteredAllEdge();
         } catch (Exception var16) {
            logger.error(var16.getMessage());
         }

         if (edgeServerList != null && edgeServerList.size() > 0) {
            int connectedCnt = false;
            if (!systemSetup.getDevice_type().equalsIgnoreCase("LPLAYER")) {
               List edgeServerNames = new ArrayList();
               if (systemSetup.getEdgeServer() != null && !systemSetup.getEdgeServer().equals("")) {
                  edgeServerNames = (List)Arrays.stream(systemSetup.getEdgeServer().split("@")).map((s) -> {
                     String[] ar = s.split(";");
                     return ar[0];
                  }).collect(Collectors.toList());
               }

               Iterator var25 = edgeServerList.iterator();

               label108:
               while(true) {
                  int connectedCnt;
                  EdgeServer edgeServer;
                  do {
                     do {
                        if (!var25.hasNext()) {
                           systemSetupCamel.setDownloadServer(downloadLists);
                           break label108;
                        }

                        edgeServer = (EdgeServer)var25.next();
                        connectedCnt = 0;
                     } while(edgeServer.getStatus() == EdgeStatus.NEED_LICENSE);
                  } while(edgeServer.getStatus() == EdgeStatus.NEED_REGISTRATION);

                  boolean isSelected = ((List)edgeServerNames).contains(edgeServer.getHostName());
                  DeviceDownloadServerResource dsResource = new DeviceDownloadServerResource();
                  dsResource.setHostName(edgeServer.getHostName());
                  dsResource.setIpAddress(edgeServer.getIpAddress());
                  List deviceIdByHostName = edgeServerDao.getDeviceIdByHostName(edgeServer.getHostName());
                  Iterator var14 = deviceIdByHostName.iterator();

                  while(var14.hasNext()) {
                     String deviceId = (String)var14.next();
                     if (DeviceUtils.isConnected(deviceId)) {
                        ++connectedCnt;
                     }
                  }

                  dsResource.setSelected(isSelected);
                  dsResource.setConnectedDevices(connectedCnt);
                  downloadLists.add(dsResource);
               }
            }

            Boolean onlyEdgeServer = false;
            if (systemSetup.getEdgeServer() != null && !systemSetup.getEdgeServer().equals("")) {
               if (systemSetup.getOnly_dn_server() == null) {
                  onlyEdgeServer = false;
               } else {
                  onlyEdgeServer = systemSetup.getOnly_dn_server();
               }
            }

            systemSetupCamel.setOnlyEdgeServer(onlyEdgeServer);
         }

         if (systemSetup.getDevice_type().equals("iPLAYER") || systemSetup.getDevice_type().equals("SPLAYER") && systemSetup.getDevice_type_version() >= CommonDataConstants.TYPE_VERSION_2_0) {
            systemSetupCamel.setWebcam(systemSetup.getWebcam());
            systemSetupCamel.setAmsPlayMode(systemSetup.getAms_play_mode());
         }

         systemSetupCamel.setPlayerResolution(systemSetup.getPlayer_resolution());
         systemSetupCamel.setScreenRotation(systemSetup.getScreen_rotation());
         if (systemSetup.getDevice_type().equals("iPLAYER")) {
            systemSetupCamel.setScreenRotation(systemSetup.getScreen_rotation());
            systemSetupCamel.setComputerName(systemSetup.getComputer_name());
            systemSetupCamel.setUseMpplayer(systemSetup.getUse_mpplayer());
            systemSetupCamel.setResetPassword(systemSetup.getReset_password());
            systemSetupCamel.setBgColor(systemSetup.getBg_color());
         }
      }

      systemSetupCamel.setPinCode(systemSetup.getPin_code());
      return systemSetupCamel;
   }

   public static DeviceSystemSetupConfResource newConvertSystemSetupToCamelStyle(DeviceSystemSetupConf systemSetup) throws SQLException {
      DeviceSystemSetupConfResource systemSetupCamel = new DeviceSystemSetupConfResource();
      boolean rmsDevice = false;

      try {
         if (systemSetup.getDevice_type().equals("RSPLAYER") || systemSetup.getDevice_type().equals("RIPLAYER") || systemSetup.getDevice_type().equals("RLEDBOX") || systemSetup.getDevice_type().equals("RSIGNAGE") || systemSetup.getDevice_type().equals("RKIOSK")) {
            rmsDevice = true;
         }
      } catch (Exception var22) {
         logger.error("", var22);
      }

      String dateFormat = SecurityUtils.getUserContainer().getUser().getDate_format();
      if (dateFormat == null) {
         dateFormat = "yyyy-MM-dd";
      }

      systemSetup.initParamsForJson();
      systemSetupCamel.setDeviceId(systemSetup.getDevice_id());
      systemSetupCamel.setDeviceName(systemSetup.getDevice_name());
      systemSetupCamel.setDeviceModelName(systemSetup.getDevice_model_name());
      systemSetupCamel.setDeviceType(systemSetup.getDevice_type());
      systemSetupCamel.setDeviceTypeVersion(systemSetup.getDevice_type_version());
      systemSetupCamel.setTimeZoneIndex(systemSetup.getTime_zone_index());
      systemSetupCamel.setDayLightSaving(systemSetup.getDay_light_saving());
      systemSetupCamel.setDayLightSavingManual(systemSetup.getDay_light_saving_manual());
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      deviceDao.getVwtIdByDeviceId(systemSetup.getDevice_id());
      if (systemSetup.getMagicinfo_server_url() != null) {
         systemSetupCamel.setMagicinfoServerUrl(systemSetup.getMagicinfo_server_url());
      }

      systemSetupCamel.setTriggerInterval(systemSetup.getTrigger_interval());
      systemSetupCamel.setScreenRotation(systemSetup.getScreen_rotation());
      if (systemSetup.getDevice_type().equals("iPLAYER") || systemSetup.getDevice_type().equals("WPLAYER")) {
         systemSetupCamel.setLogMnt(systemSetup.getLog_mnt());
      }

      if (systemSetup.getDevice_type().equals("iPLAYER")) {
         systemSetupCamel.setTunnelingServer(systemSetup.getTunneling_server());
         systemSetupCamel.setVncPassword(StrUtils.nvl(systemSetup.getVnc_password()));
         systemSetupCamel.setRepositoryPath(systemSetup.getRepository_path());
         systemSetupCamel.setMntFolderPath(systemSetup.getMnt_folder_path());
         systemSetupCamel.setContentMnt(systemSetup.getContent_mnt());
         systemSetupCamel.setAutoIpSet(systemSetup.getAuto_ip_set());
         systemSetupCamel.setAutoComputerNameSet(systemSetup.getAuto_computer_name_set());
         systemSetupCamel.setFiledataDelSize(systemSetup.getFiledata_del_size());
         systemSetupCamel.setContentReadyInterval(systemSetup.getContent_ready_interval());
         systemSetupCamel.setPlayerStartTimeout(systemSetup.getPlayer_start_timeout());
         systemSetupCamel.setComputerName(systemSetup.getComputer_name());
         systemSetupCamel.setUseMpplayer(systemSetup.getUse_mpplayer());
         systemSetupCamel.setResetPassword(systemSetup.getReset_password());
         systemSetupCamel.setBgColor(systemSetup.getBg_color());
      }

      systemSetupCamel.setScreenCaptureInterval(systemSetup.getScreen_capture_interval());
      systemSetupCamel.setMonitoringInterval(systemSetup.getMonitoring_interval());
      systemSetupCamel.setChildMonitoringInterval(systemSetup.getChild_monitoring_interval());
      String[] arrProofOfPlay;
      if (systemSetup.getProxy_setting() != null) {
         String[] arrProxy = DeviceUtils.splitter(systemSetup.getProxy_setting(), 5);
         if (systemSetup.getProxy_setting_authorization() != null) {
            try {
               arrProofOfPlay = systemSetup.getProxy_setting_authorization().split(":");
               if (arrProofOfPlay.length >= 2) {
                  arrProxy[3] = arrProofOfPlay[0];
                  arrProxy[4] = arrProofOfPlay[1];
               }
            } catch (Exception var21) {
               logger.error("proxy_setting_authorization failed.", var21);
            }
         }

         systemSetupCamel.setProxyAddress(arrProxy[1]);
         systemSetupCamel.setProxyPort(arrProxy[2]);
         systemSetupCamel.setProxyUserId(arrProxy[3]);
         systemSetupCamel.setProxyPassword(arrProxy[4]);
         systemSetupCamel.setProxyException(systemSetup.getProxy_exclude_list());
         systemSetupCamel.setProxySetting(systemSetup.getProxy_setting());
      }

      systemSetupCamel.setVwtId(systemSetup.getVwt_id());
      if (systemSetup.getSystem_restart_interval() != null) {
         systemSetupCamel.setSystemRestartInterval(systemSetup.getSystem_restart_interval().toLowerCase());
      }

      systemSetupCamel.setPlayMode(systemSetup.getPlay_mode());
      systemSetupCamel.setTimeZoneVersion(systemSetup.getTime_zone_version());
      systemSetupCamel.setFtpPort(systemSetup.getFtp_port());
      systemSetupCamel.setDatalinkServer(systemSetup.getDatalink_server());
      systemSetupCamel.setStatisticsFileRefresh(systemSetup.getStatisticsFileRefresh());
      systemSetupCamel.setIsRedundancy(systemSetup.getIs_redundancy());
      String device_id = systemSetup.getDevice_id();
      systemSetupCamel.setMultiTimeZoneIndex(device_id, systemSetup.getMulti_time_zone_index(device_id));
      systemSetupCamel.setMultiDayLightSaving(device_id, systemSetup.getMulti_day_light_saving(device_id));
      if (systemSetup.getUrl_launcher() != null && rmsDevice) {
         setUrlLauncherSettings(systemSetup, systemSetupCamel);
      }

      if (!rmsDevice) {
         systemSetupCamel.setProtocolPriority(systemSetup.getProtocol_priority());
         systemSetupCamel.setFtpConnectMode(systemSetup.getFtp_connect_mode());
         systemSetupCamel.setConnectionLimitTime(systemSetup.getConnection_limit_time());
         systemSetupCamel.setBandwidth(systemSetup.getBandwidth());
         if (systemSetup.getSwitch_time() != null && !systemSetup.getSwitch_time().equals("0")) {
            systemSetupCamel.setSwitchTime(systemSetup.getSwitch_time());
         }

         String tagValueString;
         if (systemSetup.getProof_of_play_mnt() != null && !systemSetup.getProof_of_play_mnt().equals("")) {
            arrProofOfPlay = DeviceUtils.splitter(systemSetup.getProof_of_play_mnt(), 2);
            if (arrProofOfPlay.length == 2) {
               systemSetupCamel.setProofOfPlayMnt(systemSetup.getProof_of_play_mnt());
               systemSetupCamel.setProofValidityPeriod(arrProofOfPlay[0]);
               systemSetupCamel.setProofValiditySize(arrProofOfPlay[1]);
            }

            Timestamp statistcsLastRequestTime = deviceDao.getStatisticsFileRequestTime(systemSetup.getDevice_id());
            if (statistcsLastRequestTime != null) {
               tagValueString = (new SimpleDateFormat(dateFormat + " HH:mm:ss")).format(statistcsLastRequestTime);
               systemSetupCamel.setStatisticsFileRequestTime(tagValueString);
            }

            systemSetupCamel.setStatisticsFileRefresh("1");
         }

         ArrayList downloadLists;
         if (!systemSetup.getDevice_type().equals("LPLAYER")) {
            DeviceSystemSetupConfManager dao = DeviceSystemSetupConfManagerImpl.getInstance("PREMIUM");
            String tagIdString = dao.getTagIdListString(systemSetup.getDevice_id());
            tagValueString = dao.getTagValueListString(systemSetup.getDevice_id());
            downloadLists = null;
            if (tagIdString != null && tagValueString != null && tagIdString.length() > 0 && tagValueString.length() > 0) {
               downloadLists = new ArrayList();
               String[] idtmp = tagIdString.split(",");
               String[] valuetmp = tagValueString.split(",");
               if (idtmp.length == valuetmp.length) {
                  for(int i = 0; i < idtmp.length; ++i) {
                     HashMap tag = new HashMap();
                     tag.put("id", idtmp[i]);
                     tag.put("value", valuetmp[i]);
                     downloadLists.add(tag);
                  }
               }
            }

            systemSetupCamel.setTagList(downloadLists);
            systemSetupCamel.setTagIdList(tagIdString);
         }

         systemSetupCamel.setContentsProgressEnable(systemSetup.getContents_progress_enable());
         systemSetupCamel.setContentsProgressUnit(systemSetup.getContents_progress_unit());
         systemSetupCamel.setContentsProgressInterval(systemSetup.getContents_progress_interval());
         Boolean isBackupDevice = deviceDao.isRedundancyDevice(systemSetup.getDevice_id());
         if (!isBackupDevice) {
            systemSetupCamel.setContentsDownloadMode(systemSetup.getContents_download_mode());
         }

         systemSetupCamel.setIsBackupDevice(isBackupDevice);
         systemSetupCamel.setSmartDownload(systemSetup.getSmart_download());
         EdgeServerDao edgeServerDao = new EdgeServerDao();
         List edgeServerList = null;
         downloadLists = new ArrayList();

         try {
            edgeServerList = edgeServerDao.selectRegisteredAllEdge();
         } catch (Exception var20) {
            logger.error(var20.getMessage());
         }

         if (edgeServerList != null && edgeServerList.size() > 0) {
            int connectedCnt = false;
            if (!systemSetup.getDevice_type().equalsIgnoreCase("LPLAYER")) {
               List edgeServerNames = new ArrayList();
               if (systemSetup.getEdgeServer() != null && !systemSetup.getEdgeServer().equals("")) {
                  edgeServerNames = (List)Arrays.stream(systemSetup.getEdgeServer().split("@")).map((s) -> {
                     String[] ar = s.split(";");
                     return ar[0];
                  }).collect(Collectors.toList());
               }

               Iterator var33 = edgeServerList.iterator();

               label160:
               while(true) {
                  int connectedCnt;
                  EdgeServer edgeServer;
                  do {
                     do {
                        if (!var33.hasNext()) {
                           systemSetupCamel.setDownloadServer(downloadLists);
                           break label160;
                        }

                        edgeServer = (EdgeServer)var33.next();
                        connectedCnt = 0;
                     } while(edgeServer.getStatus() == EdgeStatus.NEED_LICENSE);
                  } while(edgeServer.getStatus() == EdgeStatus.NEED_REGISTRATION);

                  boolean isSelected = ((List)edgeServerNames).contains(edgeServer.getHostName());
                  DeviceDownloadServerResource dsResource = new DeviceDownloadServerResource();
                  dsResource.setHostName(edgeServer.getHostName());
                  dsResource.setIpAddress(edgeServer.getIpAddress());
                  List deviceIdByHostName = edgeServerDao.getDeviceIdByHostName(edgeServer.getHostName());
                  Iterator var18 = deviceIdByHostName.iterator();

                  while(var18.hasNext()) {
                     String deviceId = (String)var18.next();
                     if (DeviceUtils.isConnected(deviceId)) {
                        ++connectedCnt;
                     }
                  }

                  dsResource.setSelected(isSelected);
                  dsResource.setConnectedDevices(connectedCnt);
                  downloadLists.add(dsResource);
               }
            }

            Boolean onlyEdgeServer = false;
            if (systemSetup.getEdgeServer() != null && !systemSetup.getEdgeServer().equals("")) {
               if (systemSetup.getOnly_dn_server() == null) {
                  onlyEdgeServer = false;
               } else {
                  onlyEdgeServer = systemSetup.getOnly_dn_server();
               }
            }

            systemSetupCamel.setOnlyEdgeServer(onlyEdgeServer);
         }

         if (systemSetup.getDevice_type().equals("iPLAYER") || systemSetup.getDevice_type().equals("SPLAYER") && systemSetup.getDevice_type_version() >= CommonDataConstants.TYPE_VERSION_2_0) {
            systemSetupCamel.setAmsPlayMode(systemSetup.getAms_play_mode());
         }

         systemSetupCamel.setPlayerResolution(systemSetup.getPlayer_resolution());
      }

      if (systemSetup.getPin_code() != null) {
         systemSetupCamel.setPinCode(systemSetup.getPin_code());
      }

      if (systemSetup.getCabinet_group_setting() != null) {
         systemSetupCamel.setCabinetGroupSetting(DeviceUtils.convertCabinetGroupSetting(systemSetup.getCabinet_group_setting()));
      }

      if (systemSetup.getThird_application_update_domain() != null) {
         systemSetupCamel.setThirdApplicationUpdateDomain(systemSetup.getThird_application_update_domain());
      }

      return systemSetupCamel;
   }

   private static void setUrlLauncherSettings(DeviceSystemSetupConf systemSetup, DeviceSystemSetupConfResource systemSetupCamel) {
      logger.debug(systemSetup.getUrl_launcher());
      String[] urlArr = systemSetup.getUrl_launcher().split(";");
      if (urlArr.length >= 2) {
         systemSetupCamel.setUrlLauncherTimeout(urlArr[0]);
         systemSetupCamel.setUrlLauncherAddress(urlArr[1]);
      } else if (urlArr.length >= 1) {
         systemSetupCamel.setUrlLauncherTimeout(urlArr[0]);
         systemSetupCamel.setUrlLauncherAddress("");
      } else {
         logger.warn("urlArr is empty");
      }

   }

   public static DeviceTimeconfResource convertTimeinfoToCamelStyle(DeviceTimeConf timeConf) {
      DeviceTimeconfResource timeConfCamel = new DeviceTimeconfResource();
      timeConfCamel.setDeviceId(timeConf.getDevice_id());
      timeConfCamel.setDeviceName(timeConf.getDevice_model_name());
      timeConfCamel.setDeviceModelCode(timeConf.getDevice_model_code());
      timeConfCamel.setDeviceModelName(timeConf.getDevice_model_name());
      timeConfCamel.setTimeCurrentTime(timeConf.getTime_current_time());
      timeConfCamel.setTimeOnTime(timeConf.getTime_on_time());
      timeConfCamel.setTimeOffTime(timeConf.getTime_off_time());
      timeConfCamel.setTimerClock(timeConf.getTimer_clock());
      timeConfCamel.setTimerTimer1(timeConf.getTimer_timer1());
      timeConfCamel.setTimerTimer2(timeConf.getTimer_timer2());
      timeConfCamel.setTimerConfTimer1(timeConf.getTimer_conf_timer1());
      timeConfCamel.setTimerConfTimer2(timeConf.getTimer_conf_timer2());
      timeConfCamel.setTimerConfTimer3(timeConf.getTimer_conf_timer3());
      timeConfCamel.setTimerConfTimer4(timeConf.getTimer_conf_timer4());
      timeConfCamel.setTimerConfTimer5(timeConf.getTimer_conf_timer5());
      timeConfCamel.setTimerConfTimer6(timeConf.getTimer_conf_timer6());
      timeConfCamel.setTimerConfTimer7(timeConf.getTimer_conf_timer7());
      timeConfCamel.setTimer_holiday(timeConf.getTimer_holiday());
      timeConfCamel.setHolidayCnt(timeConf.getHoliday_cnt());
      timeConfCamel.setMdcUpdateTime(timeConf.getMdc_update_time());
      return timeConfCamel;
   }

   public static V2DeviceTimeconfResource newConvertTimeinfoToCamelStyle(DeviceTimeConf timeConf) {
      V2DeviceTimeconfResource timeConfCamel = new V2DeviceTimeconfResource();
      timeConfCamel.setDeviceId(timeConf.getDevice_id());
      timeConfCamel.setDeviceName(timeConf.getDevice_model_name());
      timeConfCamel.setDeviceModelCode(timeConf.getDevice_model_code());
      timeConfCamel.setDeviceModelName(timeConf.getDevice_model_name());
      timeConfCamel.setTimeCurrentTime(timeConf.getTime_current_time());
      timeConfCamel.setTimeOnTime(timeConf.getTime_on_time());
      timeConfCamel.setTimeOffTime(timeConf.getTime_off_time());
      V2DeviceTimeCurrentDate timeClock = new V2DeviceTimeCurrentDate();
      if (timeConf.getTimer_clock() != null && !timeConf.getTimer_clock().equals("")) {
         String[] strTemp = timeConf.getTimer_clock().split(";");
         if (strTemp.length > 0) {
            timeClock.setClockDay(strTemp[0]);
            timeClock.setClockH(strTemp[1]);
            timeClock.setClockM(strTemp[2]);
            timeClock.setClockMon(strTemp[3]);
            timeClock.setClockY1(strTemp[4]);
            timeClock.setClockY2(strTemp[5]);
            timeClock.setClockAmpm(strTemp[6]);
         }
      }

      timeConfCamel.setTimeClockConf(timeClock);
      timeConfCamel.setTimerTimer1(timeConf.getTimer_timer1());
      timeConfCamel.setTimerTimer2(timeConf.getTimer_timer2());
      List list = new ArrayList();
      list.add(0, timeConf.getTimer_conf_timer1());
      list.add(1, timeConf.getTimer_conf_timer2());
      list.add(2, timeConf.getTimer_conf_timer3());
      list.add(3, timeConf.getTimer_conf_timer4());
      list.add(4, timeConf.getTimer_conf_timer5());
      list.add(5, timeConf.getTimer_conf_timer6());
      list.add(6, timeConf.getTimer_conf_timer7());
      timeConfCamel.setTimerConfTimerList(list);
      timeConfCamel.setTimer_holiday(timeConf.getTimer_holiday());
      timeConfCamel.setHolidayCnt(timeConf.getHoliday_cnt());
      timeConfCamel.setMdcUpdateTime(timeConf.getMdc_update_time());
      return timeConfCamel;
   }

   public static DeviceGeneralConfResource convertGeneralInfoToCamelStyle(DeviceGeneralConf generalInfo) {
      DeviceGeneralConfResource DeviceGeneralConf = new DeviceGeneralConfResource();
      DeviceGeneralConf.setDeviceId(generalInfo.getDevice_id());
      DeviceGeneralConf.setGroupId(generalInfo.getGroup_id());
      DeviceGeneralConf.setGroupName(generalInfo.getGroup_name());
      DeviceGeneralConf.setDeviceName(generalInfo.getDevice_name());
      DeviceGeneralConf.setDeviceType(generalInfo.getDevice_type());
      DeviceGeneralConf.setDeviceTypeVersion(generalInfo.getDevice_type_version());
      DeviceGeneralConf.setDeviceModelCode(generalInfo.getDevice_model_code());
      DeviceGeneralConf.setDeviceModelName(generalInfo.getDevice_model_name());
      DeviceGeneralConf.setSerialDecimal(generalInfo.getSerial_decimal());
      DeviceGeneralConf.setScreenSize(generalInfo.getScreen_size());
      DeviceGeneralConf.setRuleVersion(generalInfo.getRule_version());
      DeviceGeneralConf.setResolution(generalInfo.getResolution());
      DeviceGeneralConf.setOsImageVersion(generalInfo.getOs_image_version());
      if ("iPLAYER".equals(generalInfo.getDevice_type())) {
         DeviceGeneralConf.setFirmwareVersion(generalInfo.getFirmware_version());
         if (generalInfo.getApplication_version() != null) {
            DeviceGeneralConf.setApplicationVersion(generalInfo.getApplication_version());
         }
      } else {
         DeviceGeneralConf.setFirmwareVersion(generalInfo.getApplication_version());
         if (generalInfo.getPlayer_version() != null) {
            DeviceGeneralConf.setApplicationVersion(generalInfo.getPlayer_version());
         }
      }

      DeviceGeneralConf.setVideoAdapter(generalInfo.getVideo_adapter());
      DeviceGeneralConf.setVideoMemory(generalInfo.getVideo_memory());
      DeviceGeneralConf.setVideoDriver(generalInfo.getVideo_driver());
      if (generalInfo.getDevice_type() != null && generalInfo.getDevice_type().equalsIgnoreCase("iPLAYER")) {
         DeviceGeneralConf.setEwfState(generalInfo.getEwf_state());
      } else {
         DeviceGeneralConf.setEwfState((Boolean)null);
      }

      DeviceGeneralConf.setTunnelingServer(generalInfo.getTunneling_server());
      DeviceGeneralConf.setLocation(generalInfo.getLocation());
      DeviceGeneralConf.setThirdPartyAppVer(generalInfo.getThird_party_app_ver());
      if (generalInfo.getCreate_date() != null) {
         DeviceGeneralConf.setCreateDate(generalInfo.getCreate_date());
      }

      DeviceGeneralConf.setLatitude(generalInfo.getLatitude());
      DeviceGeneralConf.setLongitude(generalInfo.getLongitude());
      DeviceGeneralConf.setNetworkAdapter(generalInfo.getNetwork_adapter());
      DeviceGeneralConf.setNetworkDriver(generalInfo.getNetwork_driver());
      DeviceGeneralConf.setMacAddress(generalInfo.getMac_address());
      DeviceGeneralConf.setIpAddress(generalInfo.getIp_address());
      DeviceGeneralConf.setRebootFlag(generalInfo.getReboot_flag());
      DeviceGeneralConf.setIpSettingType(generalInfo.getIp_setting_type());
      DeviceGeneralConf.setSubnetMask(generalInfo.getSubnet_mask());
      DeviceGeneralConf.setGateway(generalInfo.getGateway());
      DeviceGeneralConf.setDnsServerMain(generalInfo.getDns_server_main());
      DeviceGeneralConf.setDnsServerSub(generalInfo.getDns_server_sub());
      DeviceGeneralConf.setPort(generalInfo.getPort());
      DeviceGeneralConf.setMonitoringInterval(generalInfo.getMonitoring_interval());
      if (generalInfo.getLast_connection_time() != null) {
         DeviceGeneralConf.setLastConnectionTime(generalInfo.getLast_connection_time());
      }

      DeviceGeneralConf.setCpuType(generalInfo.getCpu_type());
      DeviceGeneralConf.setMemSize(generalInfo.getMem_size());
      DeviceGeneralConf.setHddSize(generalInfo.getHdd_size());
      DeviceGeneralConf.setDiskSpaceUsage(generalInfo.getDisk_space_usage());
      DeviceGeneralConf.setDiskSpaceAvailable(generalInfo.getDisk_space_available());
      DeviceGeneralConf.setMinDiskSpaceAvailable(generalInfo.getMin_disk_space_available());
      DeviceGeneralConf.setDiskSpaceRepository(generalInfo.getDisk_space_repository());
      DeviceGeneralConf.setMapId(generalInfo.getMap_id());
      DeviceGeneralConf.setPositionX(generalInfo.getPosition_x());
      DeviceGeneralConf.setPositionY(generalInfo.getPosition_y());
      DeviceGeneralConf.setWidth(generalInfo.getWidth());
      DeviceGeneralConf.setHeight(generalInfo.getHeight());
      DeviceGeneralConf.setAngle(generalInfo.getAngle());
      DeviceGeneralConf.setBezelLeftright(generalInfo.getBezel_leftright());
      DeviceGeneralConf.setBezelTopbottom(generalInfo.getBezel_topbottom());
      DeviceGeneralConf.setVwtId(generalInfo.getVwt_id());
      DeviceGeneralConf.setWebcam(generalInfo.getWebcam());
      DeviceGeneralConf.setSendCleanStorageFlag(generalInfo.getSendCleanStorageFlag());
      DeviceGeneralConf.setChildCnt(generalInfo.getChild_cnt());
      DeviceGeneralConf.setConnChildCnt(generalInfo.getConn_child_cnt());
      DeviceGeneralConf.setIsChild(generalInfo.getIs_child());
      DeviceGeneralConf.setHasChild(generalInfo.getHas_child());
      if (generalInfo.getMdc_update_time() != null) {
         DeviceGeneralConf.setMdcUpdateTime(generalInfo.getMdc_update_time());
      }

      DeviceGeneralConf.setSupportFlag(generalInfo.getSupport_flag());
      return DeviceGeneralConf;
   }

   public static DeviceGeneralConfResource newConvertGeneralInfoToCamelStyle(DeviceGeneralConf generalInfo) {
      DeviceGeneralConfResource DeviceGeneralConf = new DeviceGeneralConfResource();
      MonitoringManager monMgr = MonitoringManagerImpl.getInstance();
      DeviceGeneralConf.setPower(monMgr.isConnected(generalInfo.getDevice_id()));
      DeviceGeneralConf.setDeviceId(generalInfo.getDevice_id());
      DeviceGeneralConf.setGroupId(generalInfo.getGroup_id());
      DeviceGeneralConf.setGroupName(generalInfo.getGroup_name());
      DeviceGeneralConf.setDeviceName(generalInfo.getDevice_name());
      DeviceGeneralConf.setDeviceType(generalInfo.getDevice_type());
      DeviceGeneralConf.setDeviceTypeVersion(generalInfo.getDevice_type_version());
      DeviceGeneralConf.setDeviceModelCode(generalInfo.getDevice_model_code());
      DeviceGeneralConf.setDeviceModelName(generalInfo.getDevice_model_name());
      DeviceGeneralConf.setSerialDecimal(generalInfo.getSerial_decimal());
      DeviceGeneralConf.setScreenSize(generalInfo.getScreen_size());
      DeviceGeneralConf.setRuleVersion(generalInfo.getRule_version());
      DeviceGeneralConf.setResolution(generalInfo.getResolution());
      DeviceGeneralConf.setOsImageVersion(generalInfo.getOs_image_version());
      DeviceGeneralConf.setApplicationVersion(generalInfo.getApplication_version());
      DeviceGeneralConf.setFirmwareVersion(generalInfo.getFirmware_version());
      if (!"iPLAYER".equalsIgnoreCase(generalInfo.getDevice_type()) && !"APLAYER".equalsIgnoreCase(generalInfo.getDevice_type())) {
         DeviceGeneralConf.setFirmwareVersion(StrUtils.nvl(generalInfo.getApplication_version()));
         if (!StringUtils.isEmpty(generalInfo.getPlayer_version())) {
            DeviceGeneralConf.setPlayerVersion(StrUtils.nvl(generalInfo.getPlayer_version()));
         } else {
            DeviceGeneralConf.setPlayerVersion(StrUtils.nvl(generalInfo.getFirmware_version()));
         }
      } else {
         DeviceGeneralConf.setFirmwareVersion(StrUtils.nvl(generalInfo.getFirmware_version()));
         DeviceGeneralConf.setPlayerVersion(StrUtils.nvl(generalInfo.getApplication_version()));
      }

      DeviceGeneralConf.setVideoAdapter(generalInfo.getVideo_adapter());
      DeviceGeneralConf.setVideoMemory(generalInfo.getVideo_memory());
      DeviceGeneralConf.setVideoDriver(generalInfo.getVideo_driver());
      if (generalInfo.getDevice_type() != null && generalInfo.getDevice_type().equalsIgnoreCase("iPLAYER")) {
         DeviceGeneralConf.setEwfState(generalInfo.getEwf_state());
      } else {
         DeviceGeneralConf.setEwfState((Boolean)null);
      }

      DeviceGeneralConf.setTunnelingServer(generalInfo.getTunneling_server());
      DeviceGeneralConf.setLocation(generalInfo.getLocation());
      DeviceGeneralConf.setThirdPartyAppVer(generalInfo.getThird_party_app_ver());
      if (generalInfo.getCreate_date() != null) {
         DeviceGeneralConf.setCreateDate(generalInfo.getCreate_date());
      }

      DeviceGeneralConf.setLatitude(generalInfo.getLatitude());
      DeviceGeneralConf.setLongitude(generalInfo.getLongitude());
      DeviceGeneralConf.setNetworkAdapter(generalInfo.getNetwork_adapter());
      DeviceGeneralConf.setNetworkDriver(generalInfo.getNetwork_driver());
      DeviceGeneralConf.setMacAddress(generalInfo.getMac_address());
      DeviceGeneralConf.setIpAddress(generalInfo.getIp_address());
      DeviceGeneralConf.setRebootFlag(generalInfo.getReboot_flag());
      DeviceGeneralConf.setIpSettingType(generalInfo.getIp_setting_type());
      DeviceGeneralConf.setSubnetMask(generalInfo.getSubnet_mask());
      DeviceGeneralConf.setGateway(generalInfo.getGateway());
      DeviceGeneralConf.setDnsServerMain(generalInfo.getDns_server_main());
      DeviceGeneralConf.setDnsServerSub(generalInfo.getDns_server_sub());
      DeviceGeneralConf.setPort(generalInfo.getPort());
      DeviceGeneralConf.setMonitoringInterval(generalInfo.getMonitoring_interval());
      if (generalInfo.getLast_connection_time() != null) {
         DeviceGeneralConf.setLastConnectionTime(generalInfo.getLast_connection_time());
      }

      DeviceGeneralConf.setCpuType(generalInfo.getCpu_type());
      DeviceGeneralConf.setMemSize(generalInfo.getMem_size());
      DeviceGeneralConf.setHddSize(generalInfo.getHdd_size());
      DeviceGeneralConf.setDiskSpaceUsage(generalInfo.getDisk_space_usage());
      DeviceGeneralConf.setDiskSpaceAvailable(generalInfo.getDisk_space_available());
      DeviceGeneralConf.setMinDiskSpaceAvailable(generalInfo.getMin_disk_space_available());
      DeviceGeneralConf.setDiskSpaceRepository(generalInfo.getDisk_space_repository());
      DeviceGeneralConf.setMapId(generalInfo.getMap_id());
      DeviceGeneralConf.setPositionX(generalInfo.getPosition_x());
      DeviceGeneralConf.setPositionY(generalInfo.getPosition_y());
      DeviceGeneralConf.setWidth(generalInfo.getWidth());
      DeviceGeneralConf.setHeight(generalInfo.getHeight());
      DeviceGeneralConf.setAngle(generalInfo.getAngle());
      DeviceGeneralConf.setBezelLeftright(generalInfo.getBezel_leftright());
      DeviceGeneralConf.setBezelTopbottom(generalInfo.getBezel_topbottom());
      DeviceGeneralConf.setVwtId(generalInfo.getVwt_id());
      DeviceGeneralConf.setWebcam(generalInfo.getWebcam());
      DeviceGeneralConf.setSendCleanStorageFlag(generalInfo.getSendCleanStorageFlag());
      DeviceGeneralConf.setChildCnt(generalInfo.getChild_cnt());
      DeviceGeneralConf.setConnChildCnt(generalInfo.getConn_child_cnt());
      DeviceGeneralConf.setIsChild(generalInfo.getIs_child());
      DeviceGeneralConf.setHasChild(generalInfo.getHas_child());
      if (generalInfo.getMdc_update_time() != null) {
         DeviceGeneralConf.setMdcUpdateTime(generalInfo.getMdc_update_time());
      }

      DeviceGeneralConf.setSupportFlag(generalInfo.getSupport_flag());
      DeviceGeneralConf.setThirdApplicationVersion(generalInfo.getThird_application_version());

      try {
         if (null != generalInfo.getThird_application_last_updated()) {
            Date date = new Date(generalInfo.getThird_application_last_updated().getTime());
            DeviceGeneralConf.setThirdApplicationLastUpdated(DateUtils.dateTime2TimeStamp(date));
         }
      } catch (Exception var15) {
         logger.error(var15.getMessage());
      }

      if (null != generalInfo.getThird_application_log_size()) {
         DeviceGeneralConf.setThirdApplicationLogSize(generalInfo.getThird_application_log_size());
      }

      String peripherals = generalInfo.getPeripherals();
      if (null != peripherals) {
         try {
            List peripheralsResourceList = new ArrayList();
            String[] split = peripherals.split(",");
            String[] var6 = split;
            int var7 = split.length;

            for(int var8 = 0; var8 < var7; ++var8) {
               String peripheral = var6[var8];
               String type = "USB";
               String port = "";
               String[] data = peripheral.split(":");
               String deviceName;
               if (data[0].equals("QR/Bar Code Reader") && data[1].equals("USB")) {
                  deviceName = "QR/Bar Code Reader";
               } else if (data[0].equals("Card Reader") && data[1].equals("USB")) {
                  deviceName = "Card Reader";
               } else if (data[0].equals("NFC Reader") && data[1].equals("USB")) {
                  deviceName = "NFC Reader";
               } else if (data[0].equals("Serial Device") && data[1].equals("RS232C")) {
                  deviceName = "Serial Device";
                  type = "RS232C";
                  port = data[2];
               } else {
                  deviceName = data[0];
               }

               DevicePeripheralsResource devicePeripheralsResource = new DevicePeripheralsResource(deviceName, type, port);
               peripheralsResourceList.add(devicePeripheralsResource);
            }

            DeviceGeneralConf.setPeripherals(peripheralsResourceList);
         } catch (Exception var16) {
            logger.error("", var16);
            return null;
         }
      }

      return DeviceGeneralConf;
   }

   public static DeviceDisplayConf convertDisplayConfToSnakeStyle(DeviceDisplayConfResource param) {
      DeviceDisplayConf displayConf = new DeviceDisplayConf();
      displayConf.setDevice_id(param.getDeviceId());
      displayConf.setDevice_name(param.getDeviceName());
      displayConf.setDevice_model_code(param.getDeviceModelCode());
      displayConf.setDevice_model_name(param.getDeviceModelName());
      displayConf.setDevice_type(param.getDeviceType());
      displayConf.setDevice_type_version(param.getDeviceTypeVersion());
      displayConf.setBasic_power(param.getBasicPower());
      displayConf.setBasic_volume(param.getBasicVolume());
      displayConf.setBasic_mute(param.getBasicMute());
      displayConf.setBasic_source(param.getBasicSource());
      displayConf.setBasic_direct_channel(param.getBasicDirectChannel());
      displayConf.setBasic_panel_status(param.getBasicPanelStatus());
      displayConf.setNetwork_standby_mode(param.getNetworkStandbyMode());
      displayConf.setSpecialized_picture_mode(param.getPvSplPictureMode());
      displayConf.setPv_mode(param.getPvMode());
      displayConf.setPv_contrast(param.getPvContrast());
      displayConf.setPv_brightness(param.getPvBrightness());
      displayConf.setPv_sharpness(param.getPvSharpness());
      displayConf.setPv_color(param.getPvColor());
      displayConf.setPv_tint(param.getPvTint());
      displayConf.setPv_colortone(param.getPvColortone());
      displayConf.setPv_color_temperature(param.getPvColorTemperature());
      displayConf.setPv_size(param.getPvSize());
      displayConf.setPv_digitalnr(param.getPvDigitalnr());
      displayConf.setPv_filmmode(param.getPvFilmmode());
      displayConf.setPv_video_picture_position_size(param.getPvVideoPicturePositionSize());
      displayConf.setPv_hdmi_black_level(param.getPvHdmiBlackLevel());
      displayConf.setPpc_gamma(param.getPpcGamma());
      displayConf.setPpc_hdmi_black_level(param.getPpcHdmiBlackLevel());
      displayConf.setPpc_magic_bright(param.getPpcMagicBright());
      displayConf.setPpc_contrast(param.getPpcContrast());
      displayConf.setPpc_brightness(param.getPpcBrightness());
      displayConf.setPpc_colortone(param.getPpcColortone());
      displayConf.setPpc_color_temperature(param.getPpcColorTemperature());
      displayConf.setPpc_red(param.getPpcRed());
      displayConf.setPpc_blue(param.getPpcBlue());
      displayConf.setPpc_size(param.getPpcSize());
      displayConf.setTime_current_time(param.getTimeCurrentTime());
      displayConf.setTime_on_time(param.getTimeOnTime());
      displayConf.setTime_off_time(param.getTimeOffTime());
      displayConf.setPip_source(param.getPipSource());
      displayConf.setPip_size(param.getPipSource());
      displayConf.setPip_position(param.getPipPosition());
      displayConf.setPip_swap(param.getPipSwap());
      displayConf.setPip_control(param.getPipControl());
      displayConf.setSound_mode(param.getSoundMode());
      displayConf.setSound_bass(param.getSoundBass());
      displayConf.setSound_treble(param.getSoundTreble());
      displayConf.setSound_balance(param.getSoundBalance());
      displayConf.setSound_srs(param.getSoundSrs());
      displayConf.setSound_effect(param.getSoundEffect());
      displayConf.setImage_coarse(param.getImageCoarse());
      displayConf.setImage_fine(param.getImageFine());
      displayConf.setImage_hpos(param.getImageHpos());
      displayConf.setImage_vpos(param.getImageHpos());
      displayConf.setImage_auto(param.getImageAuto());
      displayConf.setSb_status(param.getSbStatus());
      displayConf.setSb_rgain(param.getSbRgain());
      displayConf.setSb_ggain(param.getSbGgain());
      displayConf.setSb_rgain(param.getSbRgain());
      displayConf.setSb_bgain(param.getSbBgain());
      displayConf.setSb_r_offset(param.getSbRoffset());
      displayConf.setSb_g_offset(param.getSbGoffset());
      displayConf.setSb_b_offset(param.getSbBoffset());
      displayConf.setSb_gain(param.getSbGain());
      displayConf.setSb_sharp(param.getSbSharp());
      displayConf.setMnt_auto(DeviceModelUtils.getDisplayMntAutoString(param.getMntAuto()));
      displayConf.setMnt_manual(param.getMntManual());
      displayConf.setMnt_video_wall(param.getMntVideoWall());
      displayConf.setMnt_format(param.getMntFormat());
      displayConf.setMnt_safety_screen_timer(DeviceModelUtils.getDisplayMntSafetyScreenTimerString(param.getMntSafetyScreenTimer()));
      displayConf.setMnt_safety_screen_run(param.getMntSafetyScreenRun());
      displayConf.setMnt_pixel_shift(DeviceModelUtils.getDisplayMntPixelShiftString(param.getMntPixelShift()));
      displayConf.setMnt_safety_lock(param.getMntSafetyLock());
      displayConf.setAdvanced_auto_power(param.getAdvancedAutoPower());
      displayConf.setAdvanced_fan_control(param.getAdvancedFanControl());
      displayConf.setAdvanced_fan_speed(param.getAdvancedFanSpeed());
      displayConf.setAdvanced_osd_display_type(DeviceModelUtils.getDisplayAdvOsdDisplayTypeString(param.getAdvancedOsdDisplayType()));
      displayConf.setAdvanced_reset(param.getAdvancedReset());
      displayConf.setAdvanced_rj45_setting_refresh(param.getAdvancedRj45SettingRefresh());
      displayConf.setAdvanced_stand_by(param.getAdvancedStandBy());
      displayConf.setAdvanced_user_auto_color(param.getAdvancedUserAutoColor());
      displayConf.setAuto_source_switching(DeviceModelUtils.getDisplayAutoSourceSwitchString(param.getAutoSourceSwitching()));
      displayConf.setMisc_all_lock(param.getMiscAllLock());
      displayConf.setMisc_osd(param.getMiscOsd());
      displayConf.setMisc_panel_lock(param.getMiscPanelLock());
      displayConf.setMisc_remocon(param.getMiscRemocon());
      displayConf.setDiagnosis_alarm_temperature(param.getDiagnosisAlarmTemperature());
      displayConf.setDiagnosis_display_status(param.getDiagnosisDisplayStatus());
      displayConf.setDiagnosis_monitor_temperature(param.getDiagnosisMonitorTemperature());
      displayConf.setDiagnosis_alarm_temperature(param.getDiagnosisAlarmTemperature());
      displayConf.setChkSchChannel(param.getChkSchChannel());
      displayConf.setWebcam(param.getWebcam());
      displayConf.setVwt_id(param.getVwtId());
      displayConf.setChild_cnt(param.getChildCnt());
      displayConf.setConn_child_cnt(param.getConnChildCnt());
      displayConf.setIs_child(param.getIsChild());
      displayConf.setHas_child(param.getHasChild());
      displayConf.setVwl_format(param.getVwlFormat());
      displayConf.setVwl_layout(param.getVwlLayout());
      displayConf.setVwl_mode(param.getVwlMode());
      displayConf.setVwl_position(param.getVwlPosition());
      displayConf.setMax_power_saving(param.getMaxPowerSaving());
      displayConf.setBrightness_limit(param.getBrightnessLimit());
      displayConf.setTouch_control_lock(param.getTouchControlLock());
      displayConf.setWeb_browser_url(DeviceModelUtils.getDisplayWebBrowserUrlString(param.getWebBrowserUrl()));
      displayConf.setCustom_logo(DeviceModelUtils.getDisplayCustomLogoString(param.getCustomLogo()));
      displayConf.setScreen_freeze(param.getScreenFreeze());
      displayConf.setScreen_mute(param.getScreenMute());
      displayConf.setBlack_tone(param.getBlackTone());
      displayConf.setFlesh_tone(param.getFleshTone());
      displayConf.setRgb_only_mode(param.getRgbOnlyMode());
      displayConf.setLed_hdr(param.getLedHdr());
      displayConf.setLed_hdr_dre(param.getLedHdrDre());
      displayConf.setLed_picture_size(param.getLedPictureSize());
      displayConf.setAuto_motion_plus(param.getAutoMotionPlus());
      displayConf.setAuto_motion_plus_judder_reduction(param.getAutoMotionPlusJudderReduction());
      displayConf.setEco_sensor(param.getEcoSensor());
      displayConf.setColor_space(param.getColorSpace());
      displayConf.setPicture_enhancer(param.getPictureEnhancer());
      displayConf.setSensor_internal_temperature(param.getSensorInternalTemperature());
      displayConf.setSensor_internal_humidity(param.getSensorInternalHumidity());
      displayConf.setSensor_environment_temperature(param.getSensorEnvironmentTemperature());
      displayConf.setSensor_frontglass_temperature(param.getSensorFrontglassTemperature());
      displayConf.setSensor_frontglass_humidity(param.getSensorFrontglassHumidity());
      displayConf.setError_flag(param.getErrorFlag());
      displayConf.setAdvanced_osd_display_type_value(param.getAdvancedOsdDisplayTypeValue());
      displayConf.setAuto_brightness(param.getAutoBrightness());
      displayConf.setChild_alarm_temperature(param.getChildAlarmTemperature());
      displayConf.setMin_brightness(param.getMinBrightness());
      displayConf.setLive_mode(param.getLiveMode());
      displayConf.setDisplay_output_mode(param.getDisplayOutputMode());
      displayConf.setCleanup_user_data(param.getCleanupUserData());
      displayConf.setCleanup_user_data_interval(param.getCleanupUserDataInterval());
      displayConf.setAuto_save(param.getAutoSave());
      displayConf.setAuto_power_off(param.getAutoPowerOff());
      displayConf.setSmtp(convertDeviceSMTPToString(param.getSmtp()));
      displayConf.setPrint_server(convertDevicePrintServerToString(param.getPrintServer()));
      displayConf.setWeb_command(param.getWeb_command());
      displayConf.setWeb_url(param.getWeb_url());
      displayConf.setOsd_menu_size(param.getOsdMenuSize());
      return displayConf;
   }

   public static String convertDeviceSMTPToString(DeviceSMTPResource resource) {
      if (null == resource) {
         return null;
      } else {
         StringBuilder stringBuilder = new StringBuilder();
         stringBuilder.append(resource.getServerName()).append(";");
         stringBuilder.append(resource.getPort()).append(";");
         stringBuilder.append(resource.isSsl()).append(";");
         stringBuilder.append("0;0;0;0;0;0");
         return stringBuilder.toString();
      }
   }

   public static String convertDevicePrintServerToString(DevicePrintServerResource resource) {
      if (null == resource) {
         return null;
      } else {
         StringBuilder stringBuilder = new StringBuilder();
         stringBuilder.append(resource.getIp()).append(";");
         stringBuilder.append(resource.getPort()).append(";");
         stringBuilder.append("0;0");
         return stringBuilder.toString();
      }
   }

   public static DeviceDisplayConf newConvertDisplayConfToSnakeStyle(DeviceDisplayConfResource param) {
      DeviceDisplayConf displayConf = new DeviceDisplayConf();
      displayConf.setDevice_id(param.getDeviceId());
      displayConf.setDevice_name(param.getDeviceName());
      displayConf.setDevice_model_code(param.getDeviceModelCode());
      displayConf.setDevice_model_name(param.getDeviceModelName());
      displayConf.setDevice_type(param.getDeviceType());
      displayConf.setDevice_type_version(param.getDeviceTypeVersion());
      displayConf.setBasic_power(param.getBasicPower());
      displayConf.setBasic_volume(param.getBasicVolume());
      displayConf.setBasic_mute(param.getBasicMute());
      displayConf.setBasic_source(param.getBasicSource());
      displayConf.setBasic_direct_channel(param.getBasicDirectChannel());
      displayConf.setBasic_panel_status(param.getBasicPanelStatus());
      displayConf.setNetwork_standby_mode(param.getNetworkStandbyMode());
      displayConf.setSpecialized_picture_mode(param.getPvSplPictureMode());
      if (param.getPvMode() != null && param.getPvMode() != -1L) {
         displayConf.setPv_mode(param.getPvMode());
      }

      displayConf.setPv_contrast(param.getPvContrast());
      displayConf.setPv_brightness(param.getPvBrightness());
      displayConf.setPv_sharpness(param.getPvSharpness());
      displayConf.setPv_color(param.getPvColor());
      displayConf.setPv_tint(param.getPvTint());
      displayConf.setPv_colortone(param.getPvColortone());
      displayConf.setPv_color_temperature(param.getPvColorTemperature());
      displayConf.setPv_size(param.getPvSize());
      displayConf.setPv_digitalnr(param.getPvDigitalnr());
      displayConf.setPv_filmmode(param.getPvFilmmode());
      displayConf.setPv_video_picture_position_size(param.getPvVideoPicturePositionSize());
      displayConf.setPv_hdmi_black_level(param.getPvHdmiBlackLevel());
      displayConf.setPpc_gamma(param.getPpcGamma());
      displayConf.setPpc_hdmi_black_level(param.getPpcHdmiBlackLevel());
      displayConf.setPpc_magic_bright(param.getPpcMagicBright());
      displayConf.setPpc_contrast(param.getPpcContrast());
      displayConf.setPpc_brightness(param.getPpcBrightness());
      displayConf.setPpc_colortone(param.getPpcColortone());
      displayConf.setPpc_color_temperature(param.getPpcColorTemperature());
      displayConf.setPpc_red(param.getPpcRed());
      displayConf.setPpc_blue(param.getPpcBlue());
      displayConf.setPpc_size(param.getPpcSize());
      displayConf.setTime_current_time(param.getTimeCurrentTime());
      displayConf.setTime_on_time(param.getTimeOnTime());
      displayConf.setTime_off_time(param.getTimeOffTime());
      displayConf.setPip_source(param.getPipSource());
      displayConf.setPip_size(param.getPipSource());
      displayConf.setPip_position(param.getPipPosition());
      displayConf.setPip_swap(param.getPipSwap());
      displayConf.setPip_control(param.getPipControl());
      displayConf.setSound_mode(param.getSoundMode());
      displayConf.setSound_bass(param.getSoundBass());
      displayConf.setSound_treble(param.getSoundTreble());
      displayConf.setSound_balance(param.getSoundBalance());
      displayConf.setSound_srs(param.getSoundSrs());
      displayConf.setSound_effect(param.getSoundEffect());
      displayConf.setImage_coarse(param.getImageCoarse());
      displayConf.setImage_fine(param.getImageFine());
      displayConf.setImage_hpos(param.getImageHpos());
      displayConf.setImage_vpos(param.getImageHpos());
      displayConf.setImage_auto(param.getImageAuto());
      displayConf.setSb_status(param.getSbStatus());
      displayConf.setSb_rgain(param.getSbRgain());
      displayConf.setSb_ggain(param.getSbGgain());
      displayConf.setSb_rgain(param.getSbRgain());
      displayConf.setSb_bgain(param.getSbBgain());
      displayConf.setSb_r_offset(param.getSbRoffset());
      displayConf.setSb_g_offset(param.getSbGoffset());
      displayConf.setSb_b_offset(param.getSbBoffset());
      displayConf.setSb_gain(param.getSbGain());
      displayConf.setSb_sharp(param.getSbSharp());
      displayConf.setMnt_auto(DeviceModelUtils.getNewDisplayMntAutoString(param.getMntAuto()));
      displayConf.setMnt_manual(param.getMntManual());
      displayConf.setMnt_video_wall(param.getMntVideoWall());
      displayConf.setMnt_format(param.getMntFormat());
      displayConf.setMnt_safety_screen_timer(DeviceModelUtils.getNewDisplayMntSafetyScreenTimerString(param.getMntSafetyScreenTimer()));
      displayConf.setMnt_safety_screen_run(param.getMntSafetyScreenRun());
      displayConf.setMnt_pixel_shift(DeviceModelUtils.getDisplayMntPixelShiftString(param.getMntPixelShift()));
      displayConf.setMnt_safety_lock(param.getMntSafetyLock());
      displayConf.setAdvanced_auto_power(param.getAdvancedAutoPower());
      displayConf.setAdvanced_fan_control(param.getAdvancedFanControl());
      displayConf.setAdvanced_fan_speed(param.getAdvancedFanSpeed());
      displayConf.setAdvanced_osd_display_type(DeviceModelUtils.getDisplayAdvOsdDisplayTypeString(param.getAdvancedOsdDisplayType()));
      displayConf.setAdvanced_reset(param.getAdvancedReset());
      displayConf.setAdvanced_rj45_setting_refresh(param.getAdvancedRj45SettingRefresh());
      displayConf.setAdvanced_stand_by(param.getAdvancedStandBy());
      displayConf.setAdvanced_user_auto_color(param.getAdvancedUserAutoColor());
      displayConf.setAuto_source_switching(DeviceModelUtils.getDisplayAutoSourceSwitchString(param.getAutoSourceSwitching()));
      displayConf.setMisc_all_lock(param.getMiscAllLock());
      displayConf.setMisc_osd(param.getMiscOsd());
      displayConf.setMisc_panel_lock(param.getMiscPanelLock());
      displayConf.setMisc_remocon(param.getMiscRemocon());
      displayConf.setDiagnosis_alarm_temperature(param.getDiagnosisAlarmTemperature());
      displayConf.setDiagnosis_display_status(param.getDiagnosisDisplayStatus());
      displayConf.setDiagnosis_monitor_temperature(param.getDiagnosisMonitorTemperature());
      displayConf.setDiagnosis_alarm_temperature(param.getDiagnosisAlarmTemperature());
      displayConf.setChkSchChannel(param.getChkSchChannel());
      displayConf.setWebcam(param.getWebcam());
      displayConf.setVwt_id(param.getVwtId());
      displayConf.setChild_cnt(param.getChildCnt());
      displayConf.setConn_child_cnt(param.getConnChildCnt());
      displayConf.setIs_child(param.getIsChild());
      displayConf.setHas_child(param.getHasChild());
      displayConf.setVwl_format(param.getVwlFormat());
      displayConf.setVwl_layout(param.getVwlLayout());
      displayConf.setVwl_mode(param.getVwlMode());
      displayConf.setVwl_position(param.getVwlPosition());
      displayConf.setMax_power_saving(param.getMaxPowerSaving());
      displayConf.setBrightness_limit(param.getBrightnessLimit());
      displayConf.setTouch_control_lock(param.getTouchControlLock());
      displayConf.setWeb_browser_url(DeviceModelUtils.getDisplayWebBrowserUrlString(param.getWebBrowserUrl()));
      displayConf.setCustom_logo(DeviceModelUtils.getDisplayCustomLogoString(param.getCustomLogo()));
      displayConf.setScreen_freeze(param.getScreenFreeze());
      displayConf.setScreen_mute(param.getScreenMute());
      displayConf.setBlack_tone(param.getBlackTone());
      displayConf.setFlesh_tone(param.getFleshTone());
      displayConf.setRgb_only_mode(param.getRgbOnlyMode());
      displayConf.setLed_hdr(param.getLedHdr());
      displayConf.setLed_hdr_dre(param.getLedHdrDre());
      displayConf.setLed_picture_size(param.getLedPictureSize());
      displayConf.setAuto_motion_plus(param.getAutoMotionPlus());
      displayConf.setAuto_motion_plus_judder_reduction(param.getAutoMotionPlusJudderReduction());
      displayConf.setEco_sensor(param.getEcoSensor());
      displayConf.setColor_space(param.getColorSpace());
      displayConf.setPicture_enhancer(param.getPictureEnhancer());
      displayConf.setSensor_internal_temperature(param.getSensorInternalTemperature());
      displayConf.setSensor_internal_humidity(param.getSensorInternalHumidity());
      displayConf.setSensor_environment_temperature(param.getSensorEnvironmentTemperature());
      displayConf.setSensor_frontglass_temperature(param.getSensorFrontglassTemperature());
      displayConf.setSensor_frontglass_humidity(param.getSensorFrontglassHumidity());
      displayConf.setError_flag(param.getErrorFlag());
      displayConf.setAdvanced_osd_display_type_value(param.getAdvancedOsdDisplayTypeValue());
      displayConf.setAuto_brightness(param.getAutoBrightness());
      displayConf.setChild_alarm_temperature(param.getChildAlarmTemperature());
      displayConf.setMin_brightness(param.getMinBrightness());
      displayConf.setLive_mode(param.getLiveMode());
      displayConf.setDisplay_output_mode(param.getDisplayOutputMode());
      displayConf.setCleanup_user_data(param.getCleanupUserData());
      displayConf.setCleanup_user_data_interval(param.getCleanupUserDataInterval());
      displayConf.setAuto_save(param.getAutoSave());
      displayConf.setAuto_power_off(param.getAutoPowerOff());
      displayConf.setSmtp(convertDeviceSMTPToString(param.getSmtp()));
      displayConf.setPrint_server(convertDevicePrintServerToString(param.getPrintServer()));
      displayConf.setWeb_command(param.getWeb_command());
      displayConf.setWeb_url(param.getWeb_url());
      displayConf.setOsd_menu_size(param.getOsdMenuSize());
      displayConf.setInstall_environment(param.getInstallEnvironment());
      displayConf.setDehumidify(DeviceModelUtils.convertObjectToMoString(param.getDehumidify(), "[enable]"));
      displayConf.setDimming_option(param.getDimmingOption());
      displayConf.setDimming_night_time_override(param.getDimmingNightTimeOverride());
      displayConf.setDimming_eco_sensor(DeviceModelUtils.convertObjectToMoString(param.getDimmingEcoSensor(), "[maxValue];[minValue]"));
      displayConf.setDimming_sunrise_sunset(DeviceModelUtils.convertObjectToMoString(param.getDimmingSunriseSunset(), "[latitude];[longitude];[rampTime]"));
      displayConf.setDimming_brightness_output(DeviceModelUtils.convertObjectToMoString(param.getDimmingBrightnessOutput(), "[defaultValue];[maxValue];[minValue]"));
      return displayConf;
   }

   public static DeviceSecurityConf convertSecurityConfToSnakeStyle(DeviceSecurityConfResource param) {
      DeviceSecurityConf securityConf = new DeviceSecurityConf();
      securityConf.setMnt_safety_lock(param.getMntSafetyLock());
      securityConf.setMisc_remocon(param.getMiscRemocon());
      securityConf.setMisc_panel_lock(param.getMiscPanelLock());
      securityConf.setMisc_all_lock(param.getMiscAllLock());
      securityConf.setTouch_control_lock(param.getTouchControlLock());
      securityConf.setMisc_block_usb_port(param.getMiscBlockUsbPort());
      securityConf.setMisc_block_network_connection(param.getMiscBlockNetworkConnection());
      securityConf.setMisc_white_list(param.getMiscWhiteList());
      securityConf.setCapture_lock(param.getCaptureLock());
      securityConf.setWifi_lock(param.getWifiLock());
      securityConf.setSource_lock(DeviceUtils.sourceLockListToString(param.getSourceLock()));
      securityConf.setScreen_monitoring_lock(param.getScreenMonitoringLock());
      securityConf.setRemote_control_server_lock(param.getRemoteControlServerLock());
      securityConf.setBluetooth_lock(param.getBluetoothLock());
      securityConf.setMisc_server_network_setting(param.getMiscServerNetworkSetting());
      securityConf.setIs_init_security(param.getIsInitSecurity());
      return securityConf;
   }

   public static DeviceSystemSetupConf convertSystemSetupConfToSnakeStyle(DeviceSystemSetupConfResource param) {
      DeviceSystemSetupConf systemConf = new DeviceSystemSetupConf();
      systemConf.setDevice_id(param.getDeviceId());
      systemConf.setDevice_name(param.getDeviceName());
      systemConf.setDevice_model_name(param.getDeviceModelName());
      systemConf.setDevice_type(param.getDeviceType());
      systemConf.setDevice_type_version(param.getDeviceTypeVersion());
      systemConf.setTime_zone_index(param.getTimeZoneIndex());
      systemConf.setDay_light_saving(param.getDayLightSaving());
      String dstManual = null;
      if (param.getDayLightSaving() != null && param.getDayLightSaving() && param.isDstManualChanged()) {
         dstManual = "";
         dstManual = dstManual + param.getDstStartMonth() + ";";
         dstManual = dstManual + param.getDstStartWeek() + ";";
         dstManual = dstManual + param.getDstStartDay() + ";";
         dstManual = dstManual + param.getDstStartTime() + ";";
         dstManual = dstManual + param.getDstEndMonth() + ";";
         dstManual = dstManual + param.getDstEndWeek() + ";";
         dstManual = dstManual + param.getDstEndDay() + ";";
         dstManual = dstManual + param.getDstEndTime() + ";";
         dstManual = dstManual + param.getDstTimeDifference();
      }

      systemConf.setDay_light_saving_manual(dstManual);
      systemConf.setAuto_time_setting(param.getAutoTimeSetting());
      systemConf.setOn_timer_setting(param.getOnTimerSetting());
      systemConf.setOff_timer_setting(param.getOffTimerSetting());
      systemConf.setMagicinfo_server_url(param.getMagicinfoServerUrl());
      systemConf.setIs_reverse(param.getIsReverse());
      systemConf.setTunneling_server(param.getTunnelingServer());
      systemConf.setTrigger_interval(param.getTriggerInterval());
      systemConf.setFtp_connect_mode(param.getFtpConnectMode());
      systemConf.setRepository_path(param.getRepositoryPath());
      systemConf.setScreen_capture_interval(param.getScreenCaptureInterval());
      systemConf.setBg_color(param.getBgColor());
      systemConf.setMonitoring_interval(param.getMonitoringInterval());
      systemConf.setChild_monitoring_interval(param.getChildMonitoringInterval());
      systemConf.setLast_connection_time(param.getLastConnectionTime());
      systemConf.setProxy_setting(param.getProxySetting());
      systemConf.setProxy_exclude_list(param.getProxyException());
      systemConf.setPin_code(param.getPinCode());
      systemConf.setConnection_limit_time(param.getConnectionLimitTime());
      systemConf.setVwt_id(param.getVwtId());
      systemConf.setMnt_folder_path(param.getMntFolderPath());
      systemConf.setSystem_restart_interval(param.getSystemRestartInterval());
      systemConf.setLog_mnt(param.getLogMnt());
      systemConf.setProof_of_play_mnt(param.getProofOfPlayMnt());
      systemConf.setContent_mnt(param.getContentMnt());
      systemConf.setScreen_rotation(param.getScreenRotation());
      systemConf.setPlay_mode(param.getPlayMode());
      systemConf.setReset_password(param.getResetPassword());
      systemConf.setTime_zone_version(param.getTimeZoneVersion());
      systemConf.setReboot_flag(param.getRebootFlag());
      systemConf.setLast_sent_event(param.getLastSentEvent());
      systemConf.setAuto_ip_set(param.getAutoIpSet());
      systemConf.setAuto_computer_name_set(param.getAutoComputerNameSet());
      systemConf.setComputer_name(param.getComputerName());
      systemConf.setVnc_password(param.getVncPassword());
      systemConf.setUse_mpplayer(param.getUseMpplayer());
      systemConf.setFtp_port(param.getFtpPort());
      List tagList = null;
      if (param.getTagIdList() != null) {
         tagList = new ArrayList();
         if (param.getTagIdList().length() > 0) {
            String[] tmp = param.getTagIdList().split(",");

            for(int i = 0; i < tmp.length; ++i) {
               tagList.add(tmp[i]);
            }
         } else {
            tagList.add("0");
         }
      }

      systemConf.setTag_id_list(tagList);
      systemConf.setTag_value(param.getTagValue());
      systemConf.setFiledata_del_size(param.getFiledataDelSize());
      systemConf.setContent_ready_interval(param.getContentReadyInterval());
      systemConf.setPlayer_start_timeout(param.getPlayerStartTimeout());
      systemConf.setDatalink_server(param.getDatalinkServer());
      systemConf.setContents_progress_enable(param.getContentsProgressEnable());
      systemConf.setContents_progress_unit(param.getContentsProgressUnit());
      systemConf.setContents_progress_interval(param.getContentsProgressInterval());
      systemConf.setSwitch_time(param.getSwitchTime());
      systemConf.setBandwidth(param.getBandwidth());
      systemConf.setCpu_type(param.getCpuType());
      systemConf.setStatisticsFileRefresh(param.getStatisticsFileRefresh());
      systemConf.setChild_cnt(param.getChildCnt());
      systemConf.setIs_redundancy(param.getIsRedundancy());
      systemConf.setConn_child_cnt(param.getConnChildCnt());
      systemConf.setIs_child(param.getIsChild());
      systemConf.setHas_child(param.getHasChild());
      systemConf.setWebcam(param.getWebcam());
      systemConf.setAms_play_mode(param.getAmsPlayMode());
      List tmpEdgeServer = param.getEdgeServers();
      String edgeServerString = null;
      if (tmpEdgeServer != null && tmpEdgeServer.size() > 0) {
         edgeServerString = "";

         for(int i = 0; i < tmpEdgeServer.size(); ++i) {
            DeviceDownloadServerResource tmp = (DeviceDownloadServerResource)tmpEdgeServer.get(i);
            if (tmp.isSelected()) {
               if (edgeServerString.length() > 0) {
                  edgeServerString = edgeServerString + "@";
               }

               edgeServerString = edgeServerString + tmp.getHostName() + ";" + tmp.getIpAddress();
               if (!"".equals(StrUtils.nvl(tmp.getFtpPort()))) {
                  edgeServerString = edgeServerString + ";" + tmp.getFtpPort();
               }
            }
         }

         if (StrUtils.isNotEmpty(edgeServerString)) {
            edgeServerString = edgeServerString + "!" + ((Boolean)Optional.ofNullable(param.getOnlyEdgeServer()).orElse(false)).toString();
         }
      }

      systemConf.setDownload_server(edgeServerString);
      systemConf.setOnly_dn_server(param.getOnlyEdgeServer());
      systemConf.setExpiration_date(param.getExpirationDate());
      systemConf.setPlayer_resolution(param.getPlayerResolution());
      systemConf.setSmart_download(param.getSmartDownload());
      String device_id = param.getDeviceId();
      systemConf.setMulti_time_zone_index(device_id, param.getMultiTimeZoneIndex(device_id));
      systemConf.setMulti_day_light_saving(device_id, param.getMultiDayLightSaving(device_id));
      if (param.getUrlLauncherAddress() != null && param.getUrlLauncherTimeout() != null) {
         systemConf.setUrl_launcher(param.getUrlLauncherTimeout() + ";" + param.getUrlLauncherAddress());
      }

      return systemConf;
   }

   public static DeviceSystemSetupConf newConvertSystemSetupConfToSnakeStyle(DeviceSystemSetupConfResource param) {
      DeviceSystemSetupConf systemConf = new DeviceSystemSetupConf();
      systemConf.setDevice_id(param.getDeviceId());
      systemConf.setDevice_name(param.getDeviceName());
      systemConf.setDevice_model_name(param.getDeviceModelName());
      systemConf.setDevice_type(param.getDeviceType());
      systemConf.setDevice_type_version(param.getDeviceTypeVersion());
      systemConf.setTime_zone_index(param.getTimeZoneIndex());
      systemConf.setDay_light_saving(param.getDayLightSaving());
      String dstManual = null;
      if (param.getDayLightSaving() != null && param.getDayLightSaving() && param.isDstManualChanged()) {
         dstManual = "";
         dstManual = dstManual + param.getDstStartMonth() + ";";
         dstManual = dstManual + param.getDstStartWeek() + ";";
         dstManual = dstManual + param.getDstStartDay() + ";";
         dstManual = dstManual + param.getDstStartTime() + ";";
         dstManual = dstManual + param.getDstEndMonth() + ";";
         dstManual = dstManual + param.getDstEndWeek() + ";";
         dstManual = dstManual + param.getDstEndDay() + ";";
         dstManual = dstManual + param.getDstEndTime() + ";";
         dstManual = dstManual + param.getDstTimeDifference();
      }

      systemConf.setDay_light_saving_manual(dstManual);
      systemConf.setAuto_time_setting(param.getAutoTimeSetting());
      systemConf.setOn_timer_setting(param.getOnTimerSetting());
      systemConf.setOff_timer_setting(param.getOffTimerSetting());
      systemConf.setMagicinfo_server_url(param.getMagicinfoServerUrl());
      systemConf.setIs_reverse(param.getIsReverse());
      systemConf.setTunneling_server(param.getTunnelingServer());
      systemConf.setTrigger_interval(param.getTriggerInterval());
      systemConf.setFtp_connect_mode(param.getFtpConnectMode());
      systemConf.setRepository_path(param.getRepositoryPath());
      systemConf.setScreen_capture_interval(param.getScreenCaptureInterval());
      systemConf.setBg_color(param.getBgColor());
      systemConf.setMonitoring_interval(param.getMonitoringInterval());
      systemConf.setChild_monitoring_interval(param.getChildMonitoringInterval());
      systemConf.setLast_connection_time(param.getLastConnectionTime());
      systemConf.setProxy_setting(param.getProxySetting());
      systemConf.setProxy_exclude_list(param.getProxyException());
      systemConf.setPin_code(param.getPinCode());
      systemConf.setConnection_limit_time(param.getConnectionLimitTime());
      systemConf.setVwt_id(param.getVwtId());
      systemConf.setMnt_folder_path(param.getMntFolderPath());
      systemConf.setSystem_restart_interval(param.getSystemRestartInterval());
      systemConf.setLog_mnt(param.getLogMnt());
      systemConf.setProof_of_play_mnt(param.getProofOfPlayMnt());
      systemConf.setContent_mnt(param.getContentMnt());
      systemConf.setScreen_rotation(param.getScreenRotation());
      systemConf.setPlay_mode(param.getPlayMode());
      systemConf.setReset_password(param.getResetPassword());
      systemConf.setTime_zone_version(param.getTimeZoneVersion());
      systemConf.setReboot_flag(param.getRebootFlag());
      systemConf.setLast_sent_event(param.getLastSentEvent());
      systemConf.setAuto_ip_set(param.getAutoIpSet());
      systemConf.setAuto_computer_name_set(param.getAutoComputerNameSet());
      systemConf.setComputer_name(param.getComputerName());
      systemConf.setVnc_password(param.getVncPassword());
      systemConf.setUse_mpplayer(param.getUseMpplayer());
      systemConf.setFtp_port(param.getFtpPort());
      List tagList = null;
      if (param.getTagIdList() != null) {
         tagList = new ArrayList();
         if (param.getTagIdList().length() > 0) {
            String[] tmp = param.getTagIdList().split(",");

            for(int i = 0; i < tmp.length; ++i) {
               tagList.add(tmp[i]);
            }
         } else {
            tagList.add("0");
         }
      }

      systemConf.setTag_id_list(tagList);
      systemConf.setTag_value(param.getTagValue());
      systemConf.setFiledata_del_size(param.getFiledataDelSize());
      systemConf.setContent_ready_interval(param.getContentReadyInterval());
      systemConf.setPlayer_start_timeout(param.getPlayerStartTimeout());
      systemConf.setDatalink_server(param.getDatalinkServer());
      systemConf.setContents_progress_enable(param.getContentsProgressEnable());
      systemConf.setContents_progress_unit(param.getContentsProgressUnit());
      systemConf.setContents_progress_interval(param.getContentsProgressInterval());
      systemConf.setSwitch_time(param.getSwitchTime());
      systemConf.setBandwidth(param.getBandwidth());
      systemConf.setCpu_type(param.getCpuType());
      systemConf.setStatisticsFileRefresh(param.getStatisticsFileRefresh());
      systemConf.setChild_cnt(param.getChildCnt());
      systemConf.setIs_redundancy(param.getIsRedundancy());
      systemConf.setConn_child_cnt(param.getConnChildCnt());
      systemConf.setIs_child(param.getIsChild());
      systemConf.setHas_child(param.getHasChild());
      systemConf.setWebcam(param.getWebcam());
      systemConf.setAms_play_mode(param.getAmsPlayMode());
      List tmpEdgeServer = param.getEdgeServers();
      String edgeServerString = null;
      if (tmpEdgeServer != null && tmpEdgeServer.size() > 0) {
         edgeServerString = "";

         for(int i = 0; i < tmpEdgeServer.size(); ++i) {
            DeviceDownloadServerResource tmp = (DeviceDownloadServerResource)tmpEdgeServer.get(i);
            if (tmp.isSelected()) {
               if (edgeServerString.length() > 0) {
                  edgeServerString = edgeServerString + "@";
               }

               edgeServerString = edgeServerString + tmp.getHostName() + ";" + tmp.getIpAddress();
               if (!"".equals(StrUtils.nvl(tmp.getFtpPort()))) {
                  edgeServerString = edgeServerString + ";" + tmp.getFtpPort();
               }
            }
         }

         edgeServerString = edgeServerString + "!" + ((Boolean)Optional.ofNullable(param.getOnlyEdgeServer()).orElse(false)).toString();
      }

      systemConf.setDownload_server(edgeServerString);
      systemConf.setOnly_dn_server(param.getOnlyEdgeServer());
      systemConf.setExpiration_date(param.getExpirationDate());
      systemConf.setPlayer_resolution(param.getPlayerResolution());
      String device_id = param.getDeviceId();
      systemConf.setMulti_time_zone_index(device_id, param.getMultiTimeZoneIndex(device_id));
      systemConf.setMulti_day_light_saving(device_id, param.getMultiDayLightSaving(device_id));
      if (param.getUrlLauncherAddress() != null && param.getUrlLauncherTimeout() != null) {
         systemConf.setUrl_launcher(param.getUrlLauncherTimeout() + ";" + param.getUrlLauncherAddress());
      }

      systemConf.setPin_code(param.getPinCode());
      systemConf.setSmart_download(param.getSmartDownload());
      systemConf.setContents_download_mode(param.getContentsDownloadMode());
      systemConf.setProtocol_priority(param.getProtocolPriority());
      systemConf.setThird_application_update_domain(param.getThirdApplicationUpdateDomain());
      return systemConf;
   }

   public static HashMap convertGeneralInfoToCamelStyleWithoutNull(DeviceGeneralConf deviceGeneralConf) {
      HashMap result = new HashMap();
      if (deviceGeneralConf.getDevice_name() != null) {
         result.put("deviceName", deviceGeneralConf.getDevice_name());
      }

      return result;
   }

   public static HashMap convertSystemSetupToCamelStyleWithoutNull(DeviceSystemSetupConf systemSetup) {
      HashMap result = new HashMap();
      if (systemSetup.getTime_zone_index() != null) {
         result.put("timeZoneIndex", systemSetup.getTime_zone_index());
      }

      if (systemSetup.getDay_light_saving() != null) {
         result.put("dayLightSaving", systemSetup.getDay_light_saving());
      }

      if (systemSetup.getDay_light_saving_manual() != null) {
         result.put("dayLightSavingManual", systemSetup.getDay_light_saving_manual());
      }

      if (systemSetup.getAuto_time_setting() != null) {
         result.put("autoTimeSetting", systemSetup.getAuto_time_setting());
      }

      if (systemSetup.getOn_timer_setting() != null) {
         result.put("onTimerSetting", systemSetup.getOn_timer_setting());
      }

      if (systemSetup.getOff_timer_setting() != null) {
         result.put("offTimerSetting", systemSetup.getOff_timer_setting());
      }

      if (systemSetup.getMagicinfo_server_url() != null) {
         result.put("magicinfoServerUrl", systemSetup.getMagicinfo_server_url());
      }

      if (systemSetup.getIs_reverse() != null) {
         result.put("isReverse", systemSetup.getIs_reverse());
      }

      if (systemSetup.getTunneling_server() != null) {
         result.put("tunnelingServer", systemSetup.getTunneling_server());
      }

      if (systemSetup.getTrigger_interval() != null) {
         result.put("triggerInterval", systemSetup.getTrigger_interval());
      }

      if (systemSetup.getFtp_connect_mode() != null) {
         result.put("ftpConnectMode", systemSetup.getFtp_connect_mode());
      }

      if (systemSetup.getRepository_path() != null) {
         result.put("repositoryPath", systemSetup.getRepository_path());
      }

      if (systemSetup.getScreen_capture_interval() != null) {
         result.put("screenCaptureInterval", systemSetup.getScreen_capture_interval());
      }

      if (systemSetup.getBg_color() != null) {
         result.put("bgColor", systemSetup.getBg_color());
      }

      if (systemSetup.getMonitoring_interval() != null) {
         result.put("monitoringInterval", systemSetup.getMonitoring_interval());
      }

      if (systemSetup.getChild_monitoring_interval() != null) {
         result.put("childMonitoringInterval", systemSetup.getChild_monitoring_interval());
      }

      if (systemSetup.getLast_connection_time() != null) {
         result.put("lastConnectionTime", systemSetup.getLast_connection_time());
      }

      if (systemSetup.getProxy_setting() != null) {
         result.put("proxySetting", systemSetup.getProxy_setting());
      }

      if (systemSetup.getConnection_limit_time() != null) {
         result.put("connectionLimitTime", systemSetup.getConnection_limit_time());
      }

      if (systemSetup.getVwt_id() != null) {
         result.put("vwtId", systemSetup.getVwt_id());
      }

      if (systemSetup.getMnt_folder_path() != null) {
         result.put("mntFolderPath", systemSetup.getMnt_folder_path());
      }

      if (systemSetup.getSystem_restart_interval() != null) {
         result.put("systemRestartInterval", systemSetup.getSystem_restart_interval());
      }

      if (systemSetup.getLog_mnt() != null) {
         result.put("logMnt", systemSetup.getLog_mnt());
      }

      if (systemSetup.getProof_of_play_mnt() != null) {
         result.put("proofOfPlayMnt", systemSetup.getProof_of_play_mnt());
      }

      if (systemSetup.getContent_mnt() != null) {
         result.put("contentMnt", systemSetup.getContent_mnt());
      }

      if (systemSetup.getScreen_rotation() != null) {
         result.put("screenRotation", systemSetup.getScreen_rotation());
      }

      if (systemSetup.getPlay_mode() != null) {
         result.put("playMode", systemSetup.getPlay_mode());
      }

      if (systemSetup.getReset_password() != null) {
         result.put("resetPassword", systemSetup.getReset_password());
      }

      if (systemSetup.getTime_zone_version() != null) {
         result.put("timeZoneVersion", systemSetup.getTime_zone_version());
      }

      if (systemSetup.getReboot_flag() != null) {
         result.put("rebootFlag", systemSetup.getReboot_flag());
      }

      if (systemSetup.getLast_sent_event() != null) {
         result.put("lastSentEvent", systemSetup.getLast_sent_event());
      }

      if (systemSetup.getAuto_ip_set() != null) {
         result.put("autoIpSet", systemSetup.getAuto_ip_set());
      }

      if (systemSetup.getAuto_computer_name_set() != null) {
         result.put("autoComputerNameSet", systemSetup.getAuto_computer_name_set());
      }

      if (systemSetup.getComputer_name() != null) {
         result.put("computerName", systemSetup.getComputer_name());
      }

      if (systemSetup.getVnc_password() != null) {
         result.put("vncPassword", systemSetup.getVnc_password());
      }

      if (systemSetup.getUse_mpplayer() != null) {
         result.put("useMpplayer", systemSetup.getUse_mpplayer());
      }

      if (systemSetup.getFtp_port() != null) {
         result.put("ftpPort", systemSetup.getFtp_port());
      }

      if (systemSetup.getTag_id_list() != null) {
         result.put("tagIdList", systemSetup.getTag_id_list());
      }

      if (systemSetup.getTag_value() != null) {
         result.put("tagValue", systemSetup.getTag_value());
      }

      if (systemSetup.getDatalink_server() != null) {
         result.put("datalinkServer", systemSetup.getDatalink_server());
      }

      if (systemSetup.getFiledata_del_size() != null) {
         result.put("fildataDelSize", systemSetup.getFiledata_del_size());
      }

      if (systemSetup.getContent_ready_interval() != null) {
         result.put("contentReadyInterval", systemSetup.getContent_ready_interval());
      }

      if (systemSetup.getPlayer_start_timeout() != null) {
         result.put("playerStartTimeout", systemSetup.getPlayer_start_timeout());
      }

      if (systemSetup.getDatalink_server() != null) {
         result.put("datalinkServer", systemSetup.getDatalink_server());
      }

      if (systemSetup.getContents_progress_enable() != null) {
         result.put("contentsProgressEnable", systemSetup.getContents_progress_enable());
      }

      if (systemSetup.getContents_progress_unit() != null) {
         result.put("contentsProgressUnit", systemSetup.getContents_progress_unit());
      }

      if (systemSetup.getContents_progress_interval() != null) {
         result.put("contentsProgressInterval", systemSetup.getContents_progress_interval());
      }

      if (systemSetup.getSwitch_time() != null) {
         result.put("switchTime", systemSetup.getSwitch_time());
      }

      if (systemSetup.getBandwidth() != null) {
         result.put("bandWidth", systemSetup.getBandwidth());
      }

      if (systemSetup.getCpu_type() != null) {
         result.put("cpuType", systemSetup.getCpu_type());
      }

      if (systemSetup.getStatisticsFileRefresh() != null) {
         result.put("statisticsFileRefresh", systemSetup.getStatisticsFileRefresh());
      }

      if (systemSetup.getChild_cnt() != null) {
         result.put("childCnt", systemSetup.getChild_cnt());
      }

      if (systemSetup.getIs_redundancy() != null) {
         result.put("isRedundancy", systemSetup.getIs_redundancy());
      }

      if (systemSetup.getIs_child() != null) {
         result.put("isChild", systemSetup.getIs_child());
      }

      if (systemSetup.getWebcam() != null) {
         result.put("webCam", systemSetup.getWebcam());
      }

      if (systemSetup.getAms_play_mode() != null) {
         result.put("amsPlayMode", systemSetup.getAms_play_mode());
      }

      if (systemSetup.getEdgeServer() != null) {
         result.put("downloadServer", systemSetup.getEdgeServer());
      }

      if (systemSetup.getOnly_dn_server() != null) {
         result.put("onlyDnServer", systemSetup.getOnly_dn_server());
      }

      if (systemSetup.getExpiration_date() != null) {
         result.put("expirationDate", systemSetup.getExpiration_date());
      }

      if (systemSetup.getPlayer_resolution() != null) {
         result.put("playerResolution", systemSetup.getPlayer_resolution());
      }

      String device_id = systemSetup.getDevice_id();
      if (systemSetup.getMulti_time_zone_index(device_id) != null) {
         result.put("multiTimeZoneIndex", systemSetup.getMulti_time_zone_index(device_id));
      }

      if (systemSetup.getMulti_day_light_saving(device_id) != null) {
         result.put("multiDayLightSaving", systemSetup.getMulti_day_light_saving(device_id));
      }

      return result;
   }

   public static String change12Hto24H(String hour, String type) {
      int numHour = Integer.parseInt(hour);
      if (type.equals("0") && numHour < 12) {
         numHour += 12;
      }

      return StrUtils.strZeroPlus(numHour);
   }

   public static List convertCabinetInfoToCamelStyle(List cabinetList) {
      int size = cabinetList.size();
      List newCabinetList = new ArrayList();
      List cabinetGroupList = new ArrayList();
      long tmpGroupId = 1L;
      if (cabinetList != null && cabinetList.size() > 0) {
         tmpGroupId = ((LedCabinet)cabinetList.get(0)).getCabinet_group_id();

         for(int i = 0; i < cabinetList.size(); ++i) {
            LedCabinet cabinet1 = (LedCabinet)cabinetList.get(i);
            DeviceLedCabinetResource tmpCabinet = new DeviceLedCabinetResource();
            tmpGroupId = cabinet1.getCabinet_group_id();
            tmpCabinet.setAbl(cabinet1.getAbl());
            tmpCabinet.setAspectRatio(cabinet1.getAspect_ratio());
            tmpCabinet.setAutoSource(cabinet1.getAuto_source());
            tmpCabinet.setBacklight(cabinet1.getBacklight());
            tmpCabinet.setCabinetGroupId(cabinet1.getCabinet_group_id());
            tmpCabinet.setCabinetId(cabinet1.getCabinet_id());
            tmpCabinet.setCurrentTemperature(cabinet1.getCurrent_temperature());
            tmpCabinet.setEdgeCorrection(cabinet1.getEdge_correction());
            tmpCabinet.setFault(cabinet1.getFault());
            tmpCabinet.setFpgaVersion(cabinet1.getFpga_version());
            tmpCabinet.setFwVersion(cabinet1.getFw_version());
            tmpCabinet.setGamut(cabinet1.getGamut());
            tmpCabinet.setHdbt(cabinet1.getHdbt());
            tmpCabinet.setIc(cabinet1.getIc());
            tmpCabinet.setInputSource(cabinet1.getInput_source());
            tmpCabinet.setLastScannedTime(cabinet1.getLast_scanned_time());
            tmpCabinet.setLuminance(cabinet1.getLuminance());
            tmpCabinet.setModuleRgbCc(cabinet1.getModule_rgb_cc());
            tmpCabinet.setModules(cabinet1.getModules());
            tmpCabinet.setOpenDetection(cabinet1.getOpen_detection());
            tmpCabinet.setParentDeviceId(cabinet1.getParent_device_id());
            tmpCabinet.setPhySize(cabinet1.getPhy_size());
            tmpCabinet.setPitch(cabinet1.getPitch());
            tmpCabinet.setPixelRgbCc(cabinet1.getPixel_rgb_cc());
            tmpCabinet.setPower(cabinet1.getPower());
            tmpCabinet.setResolution(cabinet1.getResolution());
            tmpCabinet.setVoltageStatus(cabinet1.getVoltage_status());
            newCabinetList.add(tmpCabinet);
            HashMap tmp;
            if (i + 1 < cabinetList.size()) {
               if (tmpGroupId != ((LedCabinet)cabinetList.get(i + 1)).getCabinet_group_id()) {
                  tmp = new HashMap();
                  tmp.put("cabinetGroupId", tmpGroupId);
                  tmp.put("cabinetList", newCabinetList);
                  cabinetGroupList.add(tmp);
                  newCabinetList = new ArrayList();
               }
            } else {
               tmp = new HashMap();
               tmp.put("cabinetGroupId", tmpGroupId);
               tmp.put("cabinetList", newCabinetList);
               cabinetGroupList.add(tmp);
            }
         }
      }

      return cabinetGroupList;
   }
}
