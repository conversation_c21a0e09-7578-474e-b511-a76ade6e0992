package com.samsung.magicinfo.webauthor2.model.datalink;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

public class StaticDLKData extends DLKData {
  private final Value value;
  
  private final TagList tagList;
  
  @JsonCreator
  public StaticDLKData(@JsonProperty("value") Value value, @JsonProperty("tagList") TagList tagList) {
    super(DLKDataType.Static);
    this.value = value;
    this.tagList = tagList;
  }
  
  public Value getValue() {
    return this.value;
  }
  
  public TagList getTagList() {
    return this.tagList;
  }
}
