package com.samsung.magicinfo.cms.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Pattern;

@JsonInclude(Include.NON_NULL)
public class PlaylistItemResource {
   @ApiModelProperty(
      example = "00000000-0000-0000-0000-000000000000"
   )
   @Pattern(
      regexp = "^[0-9A-Fa-f]{8}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{12}$",
      message = "[PlaylistItemResource][playlistId] Not UUID pattern."
   )
   private String playlistId = "";
   @ApiModelProperty(
      example = "1"
   )
   private Long versionId = 0L;
   @ApiModelProperty(
      example = "00000000-0000-0000-0000-000000000000",
      required = true
   )
   @Pattern(
      regexp = "^[0-9A-Fa-f]{8}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{12}$",
      message = "[PlaylistItemResource][contentId] Not UUID pattern."
   )
   private String contentId = "";
   @ApiModelProperty(
      example = "Content"
   )
   private String contentName = "";
   @ApiModelProperty(
      example = "00000000-0000-0000-0000-000000000000"
   )
   private String thumbFileId = "";
   private String thumbFileName = "";
   private String thumbFilePath;
   private String resolution;
   @ApiModelProperty(
      example = "0"
   )
   @Pattern(
      regexp = "^[0-9]*$",
      message = "[PlaylistItemResource][playTime] Only number is available."
   )
   private String playTime = "";
   private boolean hasDefaultPlayTime = false;
   @ApiModelProperty(
      example = "1"
   )
   private Long contentOrder = 0L;
   @JsonIgnore
   private String effectInName = "";
   @JsonIgnore
   private Long effectInDuration = 0L;
   @JsonIgnore
   private String effectInDirection = "-1";
   @JsonIgnore
   private String effectOutName = "";
   @JsonIgnore
   private Long effectOutDuration = 0L;
   @JsonIgnore
   private String effectOutDirection = "-1";
   @ApiModelProperty(
      example = "1"
   )
   private Long contentDuration = 0L;
   @Pattern(
      regexp = "^[0-9]*$",
      message = "[PlaylistItemResource][contentDurationMilli] Only number is available."
   )
   private String contentDurationMilli = "";
   @ApiModelProperty(
      example = "2016-01-01 00:00:00"
   )
   @Pattern(
      regexp = "^\\d{4}-(0[1-9]|1[0-2])-([0-2]\\d|3[01]) (0\\d|1[01]):[0-5]\\d:[0-5]\\d$",
      message = "[PlaylistItemResource][startDate] Not yyyy-mm-dd hh:mm:ss pattern."
   )
   private String startDate = null;
   @ApiModelProperty(
      example = "2016-01-01 00:00:00"
   )
   @Pattern(
      regexp = "^\\d{4}-(0[1-9]|1[0-2])-([0-2]\\d|3[01]) (0\\d|1[01]):[0-5]\\d:[0-5]\\d$",
      message = "[PlaylistItemResource][expiredDate] Not yyyy-mm-dd hh:mm:ss pattern."
   )
   private String expiredDate = null;
   @ApiModelProperty(
      example = "IMAGE"
   )
   private String mediaType;
   @JsonIgnore
   private String gender = "";
   @JsonIgnore
   private String age = "";
   @JsonIgnore
   private String amsRecogType = "";
   @JsonIgnore
   private String syncPlayId = "0";
   @JsonIgnore
   private int randomCount = 0;
   @JsonIgnore
   private Long effectInDelayDuration = 0L;
   @JsonIgnore
   private Long effectOutDelayDuration = 0L;
   @JsonIgnore
   private String effectInDelayDirection = "";
   @JsonIgnore
   private String effectOutDelayDirection = "";
   @JsonIgnore
   private Long effectInDelayDiv = 1L;
   @JsonIgnore
   private Long effectOutDelayDiv = 1L;
   @JsonIgnore
   private String startTime = null;
   @JsonIgnore
   private String expiredTime = null;
   @JsonIgnore
   private String repeatType = "";
   @JsonIgnore
   private Long playWeight = 1L;
   @JsonIgnore
   private String isIndependentPlay = "N";
   @JsonIgnore
   private boolean contiguous = false;
   private String tagMatchType;
   private String tagList;
   private boolean isSubPlaylist;
   private Long syncPlayGroupOrder;
   private String tagValue;

   public boolean isSubPlaylist() {
      return this.isSubPlaylist;
   }

   public void setSubPlaylist(boolean isSubPlaylist) {
      this.isSubPlaylist = isSubPlaylist;
   }

   public String getTagMatchType() {
      return this.tagMatchType;
   }

   public void setTagMatchType(String tagMatchType) {
      this.tagMatchType = tagMatchType;
   }

   public String getTagList() {
      return this.tagList;
   }

   public void setTagList(String tagList) {
      this.tagList = tagList;
   }

   public String getTagValue() {
      return this.tagValue;
   }

   public void setTagValue(String tagValue) {
      this.tagValue = tagValue;
   }

   public PlaylistItemResource() {
      super();
   }

   public String getPlayTime() {
      return this.playTime;
   }

   public void setPlayTime(String playTime) {
      this.playTime = playTime;
   }

   public boolean isHasDefaultPlayTime() {
      return this.hasDefaultPlayTime;
   }

   public void setHasDefaultPlayTime(boolean hasDefaultPlayTime) {
      this.hasDefaultPlayTime = hasDefaultPlayTime;
   }

   public String getPlaylistId() {
      return this.playlistId;
   }

   public void setPlaylistId(String playlistId) {
      this.playlistId = playlistId;
   }

   public Long getVersionId() {
      return this.versionId;
   }

   public void setVersionId(Long versionId) {
      this.versionId = versionId;
   }

   public String getContentId() {
      return this.contentId;
   }

   public void setContentId(String contentId) {
      this.contentId = contentId;
   }

   public Long getContentOrder() {
      return this.contentOrder;
   }

   public void setContentOrder(Long contentOrder) {
      this.contentOrder = contentOrder;
   }

   public String getContentName() {
      return this.contentName;
   }

   public void setContentName(String contentName) {
      this.contentName = contentName;
   }

   public String getThumbFileId() {
      return this.thumbFileId;
   }

   public void setThumbFileId(String thumbFileId) {
      this.thumbFileId = thumbFileId;
   }

   public String getThumbFileName() {
      return this.thumbFileName;
   }

   public void setThumbFileName(String thumbFileName) {
      this.thumbFileName = thumbFileName;
   }

   public String getResolution() {
      return this.resolution;
   }

   public void setResolution(String resolution) {
      this.resolution = resolution;
   }

   public String getEffectInName() {
      return this.effectInName;
   }

   public void setEffectInName(String effectInName) {
      this.effectInName = effectInName;
   }

   public Long getEffectInDuration() {
      return this.effectInDuration;
   }

   public void setEffectInDuration(Long effectInDuration) {
      this.effectInDuration = effectInDuration;
   }

   public String getEffectInDirection() {
      return this.effectInDirection;
   }

   public void setEffectInDirection(String effectInDirection) {
      this.effectInDirection = effectInDirection;
   }

   public String getEffectOutName() {
      return this.effectOutName;
   }

   public void setEffectOutName(String effectOutName) {
      this.effectOutName = effectOutName;
   }

   public Long getEffectOutDuration() {
      return this.effectOutDuration;
   }

   public void setEffectOutDuration(Long effectOutDuration) {
      this.effectOutDuration = effectOutDuration;
   }

   public String getEffectOutDirection() {
      return this.effectOutDirection;
   }

   public void setEffectOutDirection(String effectOutDirection) {
      this.effectOutDirection = effectOutDirection;
   }

   public Long getContentDuration() {
      return this.contentDuration;
   }

   public void setContentDuration(Long contentDuration) {
      this.contentDuration = contentDuration;
   }

   public String getContentDurationMilli() {
      return this.contentDurationMilli;
   }

   public void setContentDurationMilli(String contentDurationMilli) {
      this.contentDurationMilli = contentDurationMilli;
   }

   public String getStartDate() {
      return this.startDate;
   }

   public void setStartDate(String startDate) {
      this.startDate = startDate;
   }

   public String getExpiredDate() {
      return this.expiredDate;
   }

   public void setExpiredDate(String expiredDate) {
      this.expiredDate = expiredDate;
   }

   public String getMediaType() {
      return this.mediaType;
   }

   public void setMediaType(String mediaType) {
      this.mediaType = mediaType;
   }

   public String getGender() {
      return this.gender;
   }

   public void setGender(String gender) {
      this.gender = gender;
   }

   public String getAge() {
      return this.age;
   }

   public void setAge(String age) {
      this.age = age;
   }

   public String getAmsRecogType() {
      return this.amsRecogType;
   }

   public void setAmsRecogType(String amsRecogType) {
      this.amsRecogType = amsRecogType;
   }

   public String getSyncPlayId() {
      return this.syncPlayId;
   }

   public void setSyncPlayId(String syncPlayId) {
      this.syncPlayId = syncPlayId;
   }

   public int getRandomCount() {
      return this.randomCount;
   }

   public void setRandomCount(int randomCount) {
      this.randomCount = randomCount;
   }

   public Long getEffectInDelayDuration() {
      return this.effectInDelayDuration;
   }

   public void setEffectInDelayDuration(Long effectInDelayDuration) {
      this.effectInDelayDuration = effectInDelayDuration;
   }

   public Long getEffectOutDelayDuration() {
      return this.effectOutDelayDuration;
   }

   public void setEffectOutDelayDuration(Long effectOutDelayDuration) {
      this.effectOutDelayDuration = effectOutDelayDuration;
   }

   public String getEffectInDelayDirection() {
      return this.effectInDelayDirection;
   }

   public void setEffectInDelayDirection(String effectInDelayDirection) {
      this.effectInDelayDirection = effectInDelayDirection;
   }

   public String getEffectOutDelayDirection() {
      return this.effectOutDelayDirection;
   }

   public void setEffectOutDelayDirection(String effectOutDelayDirection) {
      this.effectOutDelayDirection = effectOutDelayDirection;
   }

   public Long getEffectInDelayDiv() {
      return this.effectInDelayDiv;
   }

   public void setEffectInDelayDiv(Long effectInDelayDiv) {
      this.effectInDelayDiv = effectInDelayDiv;
   }

   public Long getEffectOutDelayDiv() {
      return this.effectOutDelayDiv;
   }

   public void setEffectOutDelayDiv(Long effectOutDelayDiv) {
      this.effectOutDelayDiv = effectOutDelayDiv;
   }

   public String getStartTime() {
      return this.startTime;
   }

   public void setStartTime(String startTime) {
      this.startTime = startTime;
   }

   public String getExpiredTime() {
      return this.expiredTime;
   }

   public void setExpiredTime(String expiredTime) {
      this.expiredTime = expiredTime;
   }

   public String getRepeatType() {
      return this.repeatType;
   }

   public void setRepeatType(String repeatType) {
      this.repeatType = repeatType;
   }

   public Long getPlayWeight() {
      return this.playWeight;
   }

   public void setPlayWeight(Long playWeight) {
      this.playWeight = playWeight;
   }

   public String getIsIndependentPlay() {
      return this.isIndependentPlay;
   }

   public void setIsIndependentPlay(String isIndependentPlay) {
      this.isIndependentPlay = isIndependentPlay;
   }

   public boolean isContiguous() {
      return this.contiguous;
   }

   public void setContiguous(boolean contiguous) {
      this.contiguous = contiguous;
   }

   public String getThumbFilePath() {
      return this.thumbFilePath;
   }

   public void setThumbFilePath(String thumbFilePath) {
      this.thumbFilePath = thumbFilePath;
   }

   public Long getSyncPlayGroupOrder() {
      return this.syncPlayGroupOrder;
   }

   public void setSyncPlayGroupOrder(Long syncPlayGroupOrder) {
      this.syncPlayGroupOrder = syncPlayGroupOrder;
   }
}
