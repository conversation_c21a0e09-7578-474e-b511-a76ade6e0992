package com.samsung.magicinfo.protocol.util;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.setup.dao.InsightServerDao;
import com.samsung.magicinfo.framework.setup.entity.DatalinkServerEntity;
import com.samsung.magicinfo.framework.setup.entity.ExternalServerEntity;
import com.samsung.magicinfo.framework.setup.entity.InsightServerEntity;
import com.samsung.magicinfo.framework.setup.entity.RmServerEntity;
import com.samsung.magicinfo.framework.setup.manager.DatalinkServerImpl;
import com.samsung.magicinfo.framework.setup.manager.RmServerImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.restapi.edge.model.EdgeServer;
import com.samsung.magicinfo.restapi.edge.service.V2EdgeService;
import com.samsung.magicinfo.restapi.edge.service.V2EdgeServiceImpl;
import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.SocketException;
import java.net.URL;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.sql.SQLException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.KeyManager;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.apache.logging.log4j.Logger;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import org.xml.sax.SAXParseException;

public class ExternalServerMonitoringJob implements Job {
   private static Logger logger;
   ServerSetupInfo serverSetupDao;
   UserInfo userDao;
   private static int defaultErrCount;
   ResourceBundleMessageSource rms;

   public ExternalServerMonitoringJob() {
      super();
      logger = LoggingManagerV2.getLogger(ExternalServerMonitoringJob.class);
      this.serverSetupDao = ServerSetupInfoImpl.getInstance();
      this.userDao = UserInfoImpl.getInstance();
      defaultErrCount = 3;
      this.rms = new ResourceBundleMessageSource();
   }

   public void execute(JobExecutionContext jobexecutioncontext) throws JobExecutionException {
      logger.error("External Server Monitoring START");
      Integer errCount = 0;
      Map serverInfoMap = null;
      this.rms.setBasename("resource/messages");
      Object var6 = null;

      Integer errThresholdCnt;
      try {
         serverInfoMap = this.serverSetupDao.getServerInfoByOrgId(0L);
         errThresholdCnt = Integer.parseInt(serverInfoMap.get("ext_server_err_chk").toString());
      } catch (Exception var10) {
         logger.error("SQL or Parsing Exception!");
         errThresholdCnt = defaultErrCount;
      }

      List orgList;
      try {
         orgList = this.userDao.getOrganization();
      } catch (SQLException var9) {
         logger.info("ExternalServerMonitoringJob getOrganization Exception!");
         orgList = null;
      }

      this.monitorRemoteControlServer(serverInfoMap, errThresholdCnt, orgList);
      this.monitorDatalinkServer(serverInfoMap, errThresholdCnt, orgList);
      this.monitorEdgeServer(serverInfoMap, errThresholdCnt, orgList);
      this.monitorInsightServer(errThresholdCnt, orgList);
   }

   private void monitorEdgeServer(Map serverInfoMap, Integer errThresholdCnt, List orgList) {
      Integer errCount = 0;
      String message = this.rms.getMessage("MESSAGE_DEVICE_DISCONNECT_DEVICE_P", (Object[])null, new Locale("en"));
      V2EdgeService v2EdgeService = (V2EdgeServiceImpl)BeanUtils.getBean("V2EdgeServiceImpl");
      String serverType = "DOWNLOAD";

      try {
         List edgeServers = v2EdgeService.getAllEdgeServers();
         Calendar cal = Calendar.getInstance();
         cal.setTime(new Date());
         cal.add(12, -61);

         for(int i = 0; i < edgeServers.size(); ++i) {
            EdgeServer edgeServer = (EdgeServer)edgeServers.get(i);
            Date hitLastDate = edgeServer.getHitLastDate();
            if (this.serverSetupDao.isExistExternalServer(serverType, edgeServer.getIpAddress()) == 0) {
               this.serverSetupDao.addExternalServerForMonitoring(serverType, edgeServer.getIpAddress(), errThresholdCnt + 1);
            } else if (hitLastDate != null && !hitLastDate.before(cal.getTime())) {
               this.serverSetupDao.updateExternalServerStatus(serverType, edgeServer.getIpAddress(), true);
            } else {
               this.serverSetupDao.updateExternalServerStatus(serverType, edgeServer.getIpAddress(), false);
               errCount = this.serverSetupDao.getExternalServerErrCount(serverType, edgeServer.getIpAddress());
               if (serverInfoMap.get("EXT_SERVER_DN_MON_ENABLE") != null && (Boolean)serverInfoMap.get("EXT_SERVER_DN_MON_ENABLE") && errCount >= errThresholdCnt) {
                  this.sendMailToUserEdge(this.userDao, orgList, message, serverType, i, edgeServer);
               }
            }
         }
      } catch (SQLException var13) {
         logger.error("Edge Server Monitoring Failed");
      } catch (Exception var14) {
         var14.printStackTrace();
      }

      logger.info("Edge Server Monitoring END");
   }

   private void monitorRemoteControlServer(Map serverInfoMap, Integer errThresholdCnt, List orgList) {
      Integer errCount = 0;
      String message = this.rms.getMessage("MESSAGE_DEVICE_DISCONNECT_DEVICE_P", (Object[])null, new Locale("en"));
      String serverType = "RM";
      RmServerImpl rmDao = RmServerImpl.getInstance();
      List rmServerEntityList = null;

      try {
         rmServerEntityList = rmDao.getRmServerList();

         for(int i = 0; i < rmServerEntityList.size(); ++i) {
            RmServerEntity rmEntity = (RmServerEntity)rmServerEntityList.get(i);
            if (this.serverSetupDao.isExistExternalServer(serverType, rmEntity.getIp_address()) == 0) {
               this.serverSetupDao.addExternalServerForMonitoring(serverType, rmEntity.getIp_address(), errThresholdCnt + 1);
            }

            try {
               boolean serverStatus = this.getRMServerStatus(rmEntity);
               this.serverSetupDao.updateExternalServerStatus(serverType, rmEntity.getIp_address(), serverStatus);
               errCount = this.serverSetupDao.getExternalServerErrCount(serverType, rmEntity.getIp_address());
            } catch (Exception var17) {
               logger.error("RM Monitoring Fail " + rmEntity.getIp_address());
               this.serverSetupDao.updateExternalServerStatus(serverType, rmEntity.getIp_address(), false);
               errCount = this.serverSetupDao.getExternalServerErrCount(serverType, rmEntity.getIp_address());
            } finally {
               if (serverInfoMap.get("EXT_SERVER_RM_MON_ENABLE") != null && (Boolean)serverInfoMap.get("EXT_SERVER_RM_MON_ENABLE") && errCount >= errThresholdCnt) {
                  this.sendMailToAlarmUser(serverType, orgList, message, rmEntity);
               }

            }
         }
      } catch (Exception var19) {
         logger.error("RM Monitoring failed to retrieve RM server list");
      }

   }

   private void monitorDatalinkServer(Map serverInfoMap, Integer errThresholdCnt, List orgList) {
      Integer errCount = 0;
      String message = this.rms.getMessage("MESSAGE_DEVICE_DISCONNECT_DEVICE_P", (Object[])null, new Locale("en"));
      String serverType = "DATALINK";
      DatalinkServerImpl datalinkDao = DatalinkServerImpl.getInstance();
      List datalinkServerEntityList = null;

      try {
         datalinkServerEntityList = datalinkDao.getDatalinkServerList();

         for(int i = 0; i < datalinkServerEntityList.size(); ++i) {
            DatalinkServerEntity datalinkEntity = (DatalinkServerEntity)datalinkServerEntityList.get(i);
            if (this.serverSetupDao.isExistExternalServer(serverType, datalinkEntity.getIp_address()) == 0) {
               this.serverSetupDao.addExternalServerForMonitoring(serverType, datalinkEntity.getIp_address(), errThresholdCnt + 1);
            }

            try {
               boolean serverStatus = this.getDataLinkServerStatus(datalinkEntity);
               this.serverSetupDao.updateExternalServerStatus(serverType, datalinkEntity.getIp_address(), serverStatus);
               errCount = this.serverSetupDao.getExternalServerErrCount(serverType, datalinkEntity.getIp_address());
            } catch (Exception var17) {
               logger.error("DataLink Monitoring Fail" + datalinkEntity.getIp_address());
               this.serverSetupDao.updateExternalServerStatus("DataLink", datalinkEntity.getIp_address(), false);
               errCount = this.serverSetupDao.getExternalServerErrCount(serverType, datalinkEntity.getIp_address());
            } finally {
               if (serverInfoMap.get("EXT_SERVER_DL_MON_ENABLE") != null && (Boolean)serverInfoMap.get("EXT_SERVER_DL_MON_ENABLE") && errCount >= errThresholdCnt) {
                  this.sendMailToAlarmUser(serverType, orgList, message, datalinkEntity);
               }

            }
         }
      } catch (Exception var19) {
         logger.error("Datalink Monitoring failed to retrieve datalink server list");
      }

   }

   private void monitorInsightServer(Integer errThresholdCnt, List orgList) {
      Integer errCount = 0;
      String message = this.rms.getMessage("MESSAGE_DEVICE_DISCONNECT_DEVICE_P", (Object[])null, new Locale("en"));
      InsightServerDao insightServerDao = new InsightServerDao();
      final InsightServerEntity insightServerEntity = insightServerDao.getInsightServerInfo();
      String serverType = "INSIGHT";

      try {
         label113: {
            if (null == insightServerEntity || !insightServerEntity.isUse_server()) {
               return;
            }

            boolean var16 = false;

            ExternalServerEntity externalServerEntity;
            label97: {
               try {
                  var16 = true;
                  boolean serverStatus = this.getInsightServerStatus(insightServerEntity);
                  if (this.serverSetupDao.isExistExternalServer(serverType, insightServerEntity.getIp()) == 0) {
                     this.serverSetupDao.addExternalServerForMonitoring(serverType, insightServerEntity.getIp(), errThresholdCnt + 1);
                  }

                  this.serverSetupDao.updateExternalServerStatus(serverType, insightServerEntity.getIp(), serverStatus);
                  errCount = this.serverSetupDao.getExternalServerErrCount(serverType, insightServerEntity.getIp());
                  var16 = false;
                  break label97;
               } catch (Exception var17) {
                  logger.error("Insight Server Monitoring Fail" + insightServerEntity.getIp());
                  this.serverSetupDao.updateExternalServerStatus(serverType, insightServerEntity.getIp(), false);
                  errCount = this.serverSetupDao.getExternalServerErrCount(serverType, insightServerEntity.getIp());
                  var16 = false;
               } finally {
                  if (var16) {
                     if (errCount >= errThresholdCnt) {
                        ExternalServerEntity externalServerEntity = new ExternalServerEntity() {
                           public String getIp_address() {
                              return insightServerEntity.getIp();
                           }

                           public String getServer_name() {
                              return "Insight Server";
                           }
                        };
                        this.sendMailToAlarmUser(serverType, orgList, message, externalServerEntity);
                     }

                  }
               }

               if (errCount >= errThresholdCnt) {
                  externalServerEntity = new ExternalServerEntity() {
                     public String getIp_address() {
                        return insightServerEntity.getIp();
                     }

                     public String getServer_name() {
                        return "Insight Server";
                     }
                  };
                  this.sendMailToAlarmUser(serverType, orgList, message, externalServerEntity);
               }
               break label113;
            }

            if (errCount >= errThresholdCnt) {
               externalServerEntity = new ExternalServerEntity() {
                  public String getIp_address() {
                     return insightServerEntity.getIp();
                  }

                  public String getServer_name() {
                     return "Insight Server";
                  }
               };
               this.sendMailToAlarmUser(serverType, orgList, message, externalServerEntity);
            }
         }
      } catch (SQLException var19) {
         logger.error("Insight Server Monitoring Failed");
      } catch (Exception var20) {
         var20.printStackTrace();
      }

      logger.info("Insight Server Monitoring END");
   }

   private void sendMailToUserEdge(UserInfo userDao, List orgList, String message, String serverType, int i, EdgeServer edgeServer) throws SQLException {
      this.serverSetupDao.setLastErrTime(serverType, edgeServer.getIpAddress());
      message = message.replaceAll("The device", edgeServer.getHostName());
      List mailingUserList = userDao.getAlarmUserListByOrgIdAndType(0L, "DN_SERVER_ERROR");
      if (mailingUserList.size() > 0) {
         String toUserIdList = "";

         for(int k = 0; k < mailingUserList.size(); ++k) {
            if (k > 0) {
               toUserIdList = toUserIdList + ",";
            }

            toUserIdList = toUserIdList + ((Map)mailingUserList.get(k)).get("USER_ID");
         }

         MailUtil.makeMail("DN_SERVER_ERROR", message, toUserIdList, 0L, "");
      }

   }

   private void sendMailToAlarmUser(String serverType, List orgList, String message, ExternalServerEntity externalServerEntity) throws SQLException {
      this.serverSetupDao.setLastErrTime(serverType, externalServerEntity.getIp_address());
      message = message.replaceAll("The device", externalServerEntity.getServer_name());
      String serverErrorPrefix = "";
      byte var8 = -1;
      switch(serverType.hashCode()) {
      case -1722570076:
         if (serverType.equals("DATALINK")) {
            var8 = 1;
         }
         break;
      case 2619:
         if (serverType.equals("RM")) {
            var8 = 0;
         }
      }

      switch(var8) {
      case 0:
         serverErrorPrefix = "RM";
         message = message.replaceAll("The device", "Remote Control Server " + externalServerEntity.getServer_name());
         break;
      case 1:
         serverErrorPrefix = "DL";
         message = message.replaceAll("The device", "Datalink Server " + externalServerEntity.getServer_name());
      }

      List mailingUserList = this.userDao.getAlarmUserListByOrgIdAndType(0L, serverErrorPrefix + "_SERVER_ERROR");
      if (mailingUserList.size() > 0) {
         String toUserIdList = "";

         for(int k = 0; k < mailingUserList.size(); ++k) {
            if (k > 0) {
               toUserIdList = toUserIdList + ",";
            }

            toUserIdList = toUserIdList + ((Map)mailingUserList.get(k)).get("USER_ID");
         }

         MailUtil.makeMail(message, message, toUserIdList, 0L, "");
      }

   }

   private boolean getRMServerStatus(RmServerEntity rmEntity) throws Exception {
      DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
      DocumentBuilder builder = factory.newDocumentBuilder();
      HttpClient httpclient = new DefaultHttpClient();
      String return_code = null;
      String rmserver_status = null;
      String rmServer = null;
      if (rmEntity.getUse_ssl()) {
         rmServer = "https://";
      } else {
         rmServer = "http://";
      }

      if (rmEntity.getPrivate_mode()) {
         if (rmEntity.getPrivate_ssl()) {
            rmServer = "https://" + rmEntity.getPrivate_ip_address() + ":" + rmEntity.getPrivate_port() + "/RMServer/openapi/open?service=RMService.status&deviceId=0";
         } else {
            rmServer = "http://" + rmEntity.getPrivate_ip_address() + ":" + rmEntity.getPrivate_port() + "/RMServer/openapi/open?service=RMService.status&deviceId=0";
         }

         if (rmEntity.getUse_ssl()) {
            rmServer = "https://" + rmEntity.getIp_address() + ":" + rmEntity.getPort() + "/RMServer/openapi/open?service=RMService.status&deviceId=0";
         } else {
            rmServer = "http://" + rmEntity.getIp_address() + ":" + rmEntity.getPort() + "/RMServer/openapi/open?service=RMService.status&deviceId=0";
         }
      } else {
         String use_ssl = null;
         if (rmEntity.getUse_ssl()) {
            use_ssl = "https://";
         } else {
            use_ssl = "http://";
         }

         rmServer = use_ssl + rmEntity.getIp_address() + ":" + rmEntity.getPort() + "/RMServer/openapi/open?service=RMService.status&deviceId=0";
      }

      String line;
      String resultXml;
      Document doc;
      NodeList headNodeList;
      Element subItem;
      boolean var18;
      boolean var60;
      if (rmEntity.getUse_ssl()) {
         URL url = new URL(rmServer);
         SecurityUtils.trustAllCertificates();
         HttpsURLConnection conn = (HttpsURLConnection)url.openConnection();
         conn.setConnectTimeout(30000);
         InputStream in = null;
         InputStreamReader isr = null;
         BufferedReader reader = null;

         try {
            conn.connect();
            conn.setInstanceFollowRedirects(true);
            in = conn.getInputStream();
            isr = new InputStreamReader(in);
            reader = new BufferedReader(isr);
            line = null;

            for(resultXml = new String(); (line = reader.readLine()) != null; resultXml = resultXml + line) {
            }

            reader.close();
            if (resultXml.length() != 0 && !resultXml.equals("") && !resultXml.isEmpty()) {
               doc = builder.parse(new InputSource(new StringReader(resultXml)));
               doc.getDocumentElement().normalize();
               headNodeList = doc.getElementsByTagName("response");
               subItem = (Element)headNodeList.item(0);
               return_code = subItem.getAttribute("code");
               if (return_code.equals("0")) {
                  var18 = true;
                  return var18;
               }

               var18 = false;
               return var18;
            }

            var60 = false;
         } catch (Exception var41) {
            logger.error("SSL time out!");
            boolean var59 = false;
            return var59;
         } finally {
            reader.close();
            isr.close();
            in.close();
         }

         return var60;
      } else {
         BufferedReader rd = null;

         boolean var10;
         try {
            new URL(rmServer);
            HttpPost httpget = new HttpPost(rmServer);
            HttpParams httpParams = new BasicHttpParams();
            HttpConnectionParams.setConnectionTimeout(httpParams, 30000);
            httpclient = new DefaultHttpClient(httpParams);
            HttpResponse Rmserver_response = httpclient.execute(httpget);
            HttpEntity entity = Rmserver_response.getEntity();
            boolean var58;
            if (Rmserver_response.getStatusLine().getStatusCode() != 200) {
               var58 = false;
               return var58;
            }

            if (entity == null) {
               var58 = false;
               return var58;
            }

            rd = new BufferedReader(new InputStreamReader(Rmserver_response.getEntity().getContent()));
            line = null;

            for(resultXml = new String(); (line = rd.readLine()) != null; resultXml = resultXml + line) {
            }

            if (resultXml.length() == 0 || resultXml.equals("") || resultXml.isEmpty()) {
               var60 = false;
               return var60;
            }

            doc = builder.parse(new InputSource(new StringReader(resultXml)));
            doc.getDocumentElement().normalize();
            headNodeList = doc.getElementsByTagName("response");
            subItem = (Element)headNodeList.item(0);
            return_code = subItem.getAttribute("code");
            if (return_code.equals("0")) {
               httpget.abort();
               var18 = true;
               return var18;
            }

            httpget.abort();
            var18 = false;
            return var18;
         } catch (ClientProtocolException var43) {
            logger.error("Client Protocol Exception!");
            var10 = false;
         } catch (IllegalStateException var44) {
            logger.error("Illegal State Exception!");
            var10 = false;
            return var10;
         } catch (ConnectException var45) {
            logger.error("Connection Exception!");
            var10 = false;
            return var10;
         } catch (ConnectTimeoutException var46) {
            logger.error("Connection timeout Exception!");
            var10 = false;
            return var10;
         } catch (SocketException var47) {
            logger.error("Socket exception");
            var10 = false;
            return var10;
         } catch (SAXParseException var48) {
            logger.error("XML parsing error!");
            var10 = false;
            return var10;
         } catch (Exception var49) {
            logger.error("Unknown host");
            var10 = false;
            return var10;
         } finally {
            httpclient.getConnectionManager().shutdown();
            if (rd != null) {
               rd.close();
            }

         }

         return var10;
      }
   }

   private boolean getDataLinkServerStatus(DatalinkServerEntity datalinkEntity) throws Exception {
      String dlServer = "";
      if (datalinkEntity.getUse_ssl()) {
         dlServer = "https://";
      } else {
         dlServer = "http://";
      }

      if (datalinkEntity.getPrivate_mode()) {
         dlServer = dlServer + datalinkEntity.getPrivate_ip_address() + ":" + datalinkEntity.getPrivate_web_port() + "/DataLink";
      } else {
         dlServer = dlServer + datalinkEntity.getIp_address() + ":" + datalinkEntity.getPort() + "/DataLink";
      }

      URL url;
      if (datalinkEntity.getUse_ssl()) {
         url = new URL(dlServer);
         HttpsURLConnection conn = (HttpsURLConnection)url.openConnection();
         conn.setHostnameVerifier(new HostnameVerifier() {
            public boolean verify(String hostname, SSLSession session) {
               return true;
            }
         });
         SSLContext context = SSLContext.getInstance("TLS");
         context.init((KeyManager[])null, new TrustManager[]{new X509TrustManager() {
            public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
            }

            public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
               FileInputStream fis = null;

               try {
                  String keyStoreIdentityPath = "";
                  String keyStoreIdentityPassword = "";
                  keyStoreIdentityPath = CommonConfig.get("keystore.identity.path");
                  keyStoreIdentityPassword = CommonConfig.get("keystore.identity.password");
                  KeyStore trustStore = KeyStore.getInstance("JKS");
                  fis = new FileInputStream(keyStoreIdentityPath);
                  trustStore.load(fis, keyStoreIdentityPassword.toCharArray());
                  fis.close();
                  TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
                  tmf.init(trustStore);
                  TrustManager[] tms = tmf.getTrustManagers();
                  ((X509TrustManager)tms[0]).checkServerTrusted(chain, authType);
               } catch (KeyStoreException var23) {
                  ExternalServerMonitoringJob.logger.error("External Server Monitoring - KeyStore Exception");
               } catch (NoSuchAlgorithmException var24) {
                  ExternalServerMonitoringJob.logger.error("External Server Monitoring - No Such Algorithm Exception");
               } catch (IOException var25) {
                  ExternalServerMonitoringJob.logger.error("External Server Monitoring - Input Output Exception");
               } catch (ConfigException var26) {
                  ExternalServerMonitoringJob.logger.error("", var26);
               } finally {
                  try {
                     fis.close();
                  } catch (IOException var22) {
                     ExternalServerMonitoringJob.logger.error("External Server Monitoring - FIS IOException");
                  }

               }

            }

            public X509Certificate[] getAcceptedIssuers() {
               return null;
            }
         }}, (SecureRandom)null);
         conn.setSSLSocketFactory(context.getSocketFactory());
         conn.setConnectTimeout(30000);

         try {
            conn.connect();
            conn.setInstanceFollowRedirects(true);
            int code = conn.getResponseCode();
            return code == 200;
         } catch (Exception var7) {
            return false;
         }
      } else {
         try {
            url = new URL(dlServer);
            HttpURLConnection connection = (HttpURLConnection)url.openConnection();
            connection.setRequestMethod("GET");
            connection.connect();
            int code = connection.getResponseCode();
            return code == 200;
         } catch (Exception var8) {
            return false;
         }
      }
   }

   private boolean getInsightServerStatus(InsightServerEntity insightServerEntity) throws Exception {
      String insightServerURL = "";
      boolean ssl = false;
      if (insightServerEntity.getHttp_port() > 0) {
         insightServerURL = "http://" + insightServerEntity.getIp() + ":" + insightServerEntity.getHttp_port();
      } else if (insightServerEntity.getHttps_port() > 0) {
         insightServerURL = "https://" + insightServerEntity.getIp() + ":" + insightServerEntity.getHttps_port();
         ssl = true;
      }

      if (insightServerURL.isEmpty()) {
         return false;
      } else {
         URL url;
         if (ssl) {
            url = new URL(insightServerURL);
            HttpsURLConnection conn = (HttpsURLConnection)url.openConnection();
            conn.setHostnameVerifier((hostname, session) -> {
               return true;
            });
            SSLContext context = SSLContext.getInstance("TLS");
            context.init((KeyManager[])null, new TrustManager[]{new X509TrustManager() {
               public void checkClientTrusted(X509Certificate[] chain, String authType) {
               }

               public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                  FileInputStream fis = null;

                  try {
                     String keyStoreIdentityPath = "";
                     String keyStoreIdentityPassword = "";
                     keyStoreIdentityPath = CommonConfig.get("keystore.identity.path");
                     keyStoreIdentityPassword = CommonConfig.get("keystore.identity.password");
                     KeyStore trustStore = KeyStore.getInstance("JKS");
                     fis = new FileInputStream(keyStoreIdentityPath);
                     trustStore.load(fis, keyStoreIdentityPassword.toCharArray());
                     fis.close();
                     TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
                     tmf.init(trustStore);
                     TrustManager[] tms = tmf.getTrustManagers();
                     ((X509TrustManager)tms[0]).checkServerTrusted(chain, authType);
                  } catch (KeyStoreException var23) {
                     ExternalServerMonitoringJob.logger.error("External Server Monitoring - KeyStore Exception");
                  } catch (NoSuchAlgorithmException var24) {
                     ExternalServerMonitoringJob.logger.error("External Server Monitoring - No Such Algorithm Exception");
                  } catch (IOException var25) {
                     ExternalServerMonitoringJob.logger.error("External Server Monitoring - Input Output Exception");
                  } catch (ConfigException var26) {
                     ExternalServerMonitoringJob.logger.error("", var26);
                  } finally {
                     if (fis != null) {
                        try {
                           fis.close();
                        } catch (IOException var22) {
                           ExternalServerMonitoringJob.logger.error("External Server Monitoring - FIS IOException");
                        }
                     }

                  }

               }

               public X509Certificate[] getAcceptedIssuers() {
                  return null;
               }
            }}, (SecureRandom)null);
            conn.setSSLSocketFactory(context.getSocketFactory());
            conn.setConnectTimeout(30000);

            try {
               conn.connect();
               conn.setInstanceFollowRedirects(true);
               int code = conn.getResponseCode();
               return code == 200;
            } catch (Exception var8) {
               return false;
            }
         } else {
            try {
               url = new URL(insightServerURL);
               HttpURLConnection connection = (HttpURLConnection)url.openConnection();
               connection.setRequestMethod("GET");
               connection.connect();
               int code = connection.getResponseCode();
               return code == 200 || code == 400;
            } catch (Exception var9) {
               return false;
            }
         }
      }
   }
}
