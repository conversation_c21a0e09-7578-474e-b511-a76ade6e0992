package com.samsung.magicinfo.protocol.util;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseStatusEntity;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManagerImpl;
import java.io.UnsupportedEncodingException;
import java.security.GeneralSecurityException;
import java.sql.Timestamp;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.logging.log4j.Logger;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

public class DailyLicenseJob implements Job {
   Logger logger = LoggingManagerV2.getLogger(DailyLicenseJob.class);

   public DailyLicenseJob() {
      super();
   }

   public void execute(JobExecutionContext arg0) throws JobExecutionException {
      this.logger.info("[MagicInfo_DailyLicenseJob] Start DailyLicenseJob");
      SlmLicenseManagerImpl slmLicense = SlmLicenseManagerImpl.getInstance();

      try {
         if (slmLicense.hasMigrationLicense() >= 0L) {
            this.logger.info("[MagicInfo_DailyLicenseJob] Migration License is activated. No devices has been deleted.");
            return;
         }

         List list;
         Iterator var4;
         String productCode;
         if (!slmLicense.licenseChk()) {
            list = slmLicense.getInvaildLicenseList();
            if (list != null && list.size() > 0) {
               var4 = list.iterator();

               while(var4.hasNext()) {
                  Map licenses = (Map)var4.next();
                  productCode = (String)licenses.get("productCode");
                  if (productCode != null && !productCode.equals("")) {
                     SlmLicenseStatusEntity license = slmLicense.getLicenseStaus(productCode);
                     if (license == null) {
                        slmLicense.addLicenseStatus(productCode);
                     } else {
                        try {
                           String parseKey = slmLicense.decrypt(license.getExpired_key());
                           String[] licenseInfo = parseKey.split("\\|");
                           if (licenseInfo.length == 3) {
                              Timestamp now = new Timestamp(System.currentTimeMillis());
                              Timestamp expried = new Timestamp(Long.valueOf(licenseInfo[2]));
                              slmLicense.chkSlmLicenseStatus(productCode, now, expried);
                           } else {
                              slmLicense.addLicenseStatus(productCode);
                           }
                        } catch (GeneralSecurityException | UnsupportedEncodingException var12) {
                           this.logger.error("", var12);
                        }
                     }
                  }
               }
            }
         }

         list = slmLicense.getListLicenseStatus();
         if (list != null && list.size() > 0) {
            var4 = list.iterator();

            while(var4.hasNext()) {
               SlmLicenseStatusEntity license = (SlmLicenseStatusEntity)var4.next();
               productCode = license.getProduct_code();
               int licenseCount = slmLicense.getLicenseCountByProductCode(productCode);
               List deviceIdList = slmLicense.getDeviceListWithProductCode(productCode);
               if (deviceIdList == null) {
                  slmLicense.delLicenseStatus(productCode);
               } else if (deviceIdList != null && deviceIdList.size() > 0 && licenseCount >= deviceIdList.size()) {
                  slmLicense.delLicenseStatus(productCode);
               }
            }
         }
      } catch (Exception var13) {
         this.logger.error("[MagicInfo_DailyLicenseJob] fail! DailyLicenseJob e: " + var13.getMessage());
      }

      this.logger.info("[MagicInfo_DailyLicenseJob] End DailyLicenseJob");
   }
}
