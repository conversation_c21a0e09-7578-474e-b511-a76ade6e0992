package com.samsung.magicinfo.framework.device.deviceInfo.dao;

import com.google.common.collect.Lists;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DBCacheUtils;
import com.samsung.common.utils.DaoTools;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.magicinfo.framework.device.constants.DeviceConstants;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.Logger;

public class DeviceDisplayConfDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(DeviceDisplayConfDao.class);

   public DeviceDisplayConfDao() {
      super();
   }

   public DeviceDisplayConf getDeviceDisplayConf(String deviceId) throws SQLException {
      try {
         DeviceDisplayConf ret = DBCacheUtils.getDeviceDisplayConf(deviceId);
         return ret;
      } catch (Exception var4) {
         DeviceDisplayConf deviceDisplayConf = ((DeviceDisplayConfDaoMapper)this.getMapper()).getDeviceDisplayConf(deviceId);
         if (deviceDisplayConf != null) {
            DBCacheUtils.setDeviceDisplayConf(deviceDisplayConf, deviceDisplayConf.getDevice_id());
         }

         return deviceDisplayConf;
      }
   }

   public List getListDeviceDisplayConf(List deviceIds) throws SQLException {
      return ((DeviceDisplayConfDaoMapper)this.getMapper()).getListDeviceDisplayConf(deviceIds);
   }

   public boolean addDeviceDisplayConf(DeviceDisplayConf displayConf) throws SQLException {
      boolean ret = ((DeviceDisplayConfDaoMapper)this.getMapper()).addDeviceDisplayConf(displayConf);
      if (displayConf != null) {
         DBCacheUtils.setDeviceDisplayConf(displayConf, displayConf.getDevice_id());
      }

      return ret;
   }

   public boolean addDeviceDisplayConf(List displayConfs) throws SQLException {
      boolean ret = ((DeviceDisplayConfDaoMapper)this.getMapper()).addDeviceDisplayConfList(displayConfs);
      if (displayConfs != null) {
         Iterator var3 = displayConfs.iterator();

         while(var3.hasNext()) {
            DeviceDisplayConf df = (DeviceDisplayConf)var3.next();
            DBCacheUtils.setDeviceDisplayConf(df, df.getDevice_id());
         }
      }

      return ret;
   }

   public boolean addDeviceDisplayExtConf(DeviceDisplayConf displayConf) throws SQLException {
      boolean ret = ((DeviceDisplayConfDaoMapper)this.getMapper()).addDeviceDisplayExtConf(displayConf);
      if (displayConf != null) {
         DBCacheUtils.setDeviceDisplayConf(displayConf, displayConf.getDevice_id());
      }

      return ret;
   }

   public int cntDeviceDisplayConfByDeviceId(String deviceId) throws SQLException {
      return ((DeviceDisplayConfDaoMapper)this.getMapper()).cntDeviceDisplayConfByDeviceId(deviceId);
   }

   public PagedListInfo getDeviceDisplayConfList(int startPos, int pageSize, Map map) throws SQLException {
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      SelectCondition condition = (SelectCondition)map.get("condition");
      String device_id = condition.getDevice_id();
      String sort = condition.getSort_name();
      Long group_id = condition.getGroup_id();
      String dir = condition.getOrder_dir();
      String src = condition.getSrc_name() != null ? condition.getSrc_name().toUpperCase() : "";
      boolean isRoot = condition.getIsRoot();
      String roleName = condition.getRole_name();
      String userId = condition.getUser_id();
      boolean isDeviceGroupAuth = false;
      if (DeviceUtils.isDeviceGroupAuth(roleName, userId)) {
         isDeviceGroupAuth = true;
      }

      String[] deviceTypeFilter = null;
      if (condition.getDevice_type() != null) {
         deviceTypeFilter = condition.getDevice_type().split(",");
      }

      String[] filterGroupId = null;
      if (isRoot && condition.getFilter_group_ids() != null && !condition.getFilter_group_ids().equals("")) {
         filterGroupId = condition.getFilter_group_ids().split(",");
      }

      String tagFilter = condition.getTagFilter();
      List tagFilterIdList = null;
      if (StringUtils.isNotBlank(tagFilter)) {
         tagFilterIdList = new ArrayList();
         String[] tagFilterIds = tagFilter.split(",");
         String[] var20 = tagFilterIds;
         int var21 = tagFilterIds.length;

         for(int var22 = 0; var22 < var21; ++var22) {
            String tagFilterId = var20[var22];
            tagFilterIdList.add(Long.parseLong(tagFilterId));
         }
      }

      List groupList1 = null;
      List groupList2 = null;
      if (filterGroupId != null && filterGroupId.length > 0) {
         groupList1 = groupDao.getChildGroupList(condition.getGroup_id().intValue(), true);
      } else if (group_id != null && group_id == 0L) {
         List deviceOrgIds = DeviceUtils.getOrgGroupIdByUserId(condition.getUser_id());
         if (deviceOrgIds != null && deviceOrgIds.size() > 0) {
            groupList2 = groupDao.getAllDeviceGroups(deviceOrgIds);
         }
      } else if (group_id != null && group_id.intValue() != 0) {
         DeviceGroup group = groupDao.getGroup(group_id.intValue());
         if (isRoot || group.getP_group_id().intValue() == 0) {
            groupList2 = groupDao.getChildGroupList(condition.getGroup_id().intValue(), true);
         }
      }

      String string_from_dao = condition.getStatus_view_mode();
      Integer deviceExpirationDate = null;
      if (condition.getExpiration_date() != null && !condition.getExpiration_date().equals("device_status_view_all")) {
         if (condition.getExpiration_date().equals("device_expiration_date_seven")) {
            deviceExpirationDate = 7;
         } else if (condition.getExpiration_date().equals("device_expiration_date_halfmonth")) {
            deviceExpirationDate = 15;
         } else if (condition.getExpiration_date().equals("device_expiration_date_month")) {
            deviceExpirationDate = 30;
         } else if (condition.getExpiration_date().equals("device_expiration_date_custom") && condition.getCustom_input_val() != null && !condition.getCustom_input_val().equals("")) {
            deviceExpirationDate = Integer.parseInt(condition.getCustom_input_val());
         }
      }

      if (sort != null && !sort.equals("")) {
         if (sort.equalsIgnoreCase("volume")) {
            sort = "basic_volume";
         } else if (sort.equalsIgnoreCase("mute")) {
            sort = "basic_mute";
         } else if (sort.equalsIgnoreCase("source")) {
            sort = "basic_source";
         } else if (sort.equalsIgnoreCase("panel_status")) {
            sort = "basic_panel_status";
         } else if (sort.equalsIgnoreCase("remote")) {
            sort = "misc_remocon";
         } else if (sort.equalsIgnoreCase("safety_lock")) {
            sort = "mnt_safety_lock";
         } else if (sort.equalsIgnoreCase("panel_lock")) {
            sort = "misc_panel_lock";
         } else if (sort.equalsIgnoreCase("osd")) {
            sort = "misc_osd";
         } else if (sort.equalsIgnoreCase("all_lock")) {
            sort = "misc_all_lock";
         } else if (sort.equalsIgnoreCase("monitor_temperature")) {
            sort = "diagnosis_monitor_temperature";
         } else if (sort.equalsIgnoreCase("alarm_temperature")) {
            sort = "diagnosis_alarm_temperature";
         } else if (sort.equalsIgnoreCase("panel_on_time")) {
            sort = "diagnosis_panel_on_time";
         }
      }

      List result = new ArrayList();
      Integer totCnt = 0;
      condition.setRm_device_types(CommonDataConstants.RM_DEVICE_TYPE_ARRAY);
      if (groupList2 != null && groupList2.size() > 0) {
         List list = Lists.partition(groupList2, 1500);

         for(int i = 0; i < list.size(); ++i) {
            result.addAll(((DeviceDisplayConfDaoMapper)this.getMapper()).getDeviceDisplayConfList(DaoTools.offsetStartPost(startPos), pageSize, groupList1, (List)list.get(i), string_from_dao, device_id, sort, group_id, dir, src, isRoot, 999999, deviceTypeFilter, filterGroupId, isDeviceGroupAuth, userId, deviceExpirationDate, tagFilterIdList, condition, this.getConstants()));
            totCnt = totCnt + ((DeviceDisplayConfDaoMapper)this.getMapper()).getDeviceDisplayConfListCount(groupList1, (List)list.get(i), string_from_dao, device_id, group_id, src, isRoot, 999999, deviceTypeFilter, filterGroupId, isDeviceGroupAuth, userId, deviceExpirationDate, tagFilterIdList, condition, this.getConstants());
         }
      } else {
         result.addAll(((DeviceDisplayConfDaoMapper)this.getMapper()).getDeviceDisplayConfList(DaoTools.offsetStartPost(startPos), pageSize, groupList1, groupList2, string_from_dao, device_id, sort, group_id, dir, src, isRoot, 999999, deviceTypeFilter, filterGroupId, isDeviceGroupAuth, userId, deviceExpirationDate, tagFilterIdList, condition, this.getConstants()));
         totCnt = ((DeviceDisplayConfDaoMapper)this.getMapper()).getDeviceDisplayConfListCount(groupList1, groupList2, string_from_dao, device_id, group_id, src, isRoot, 999999, deviceTypeFilter, filterGroupId, isDeviceGroupAuth, userId, deviceExpirationDate, tagFilterIdList, condition, this.getConstants());
      }

      return new PagedListInfo(result, totCnt);
   }

   public List getDeviceDisplayConfFilterList(SelectCondition condition) throws SQLException {
      String deviceId = condition.getDevice_id();
      String sort = condition.getSort_name();
      Long group_id = condition.getGroup_id();
      String dir = condition.getOrder_dir();
      String src = condition.getSrc_name().toUpperCase();
      boolean isRoot = condition.getIsRoot();
      List groupList = null;
      if (group_id != null && group_id.intValue() != 0 && isRoot) {
         DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
         groupList = groupDao.getChildGroupList(condition.getGroup_id().intValue(), true);
      }

      return ((DeviceDisplayConfDaoMapper)this.getMapper()).getDeviceDisplayConfFilterList(deviceId, sort, group_id, dir, src, isRoot, groupList, 999999);
   }

   public boolean setDeviceDisplayConf(DeviceDisplayConf info, boolean runDBQuery) throws SQLException {
      if (info != null) {
         info.setMdc_update_time(new Timestamp(System.currentTimeMillis()));
         if (info.getDevice_id() != null && !info.getDevice_id().equals("")) {
            DBCacheUtils.setDeviceDisplayConf(info, info.getDevice_id());
            if (runDBQuery) {
               DBCacheUtils.runDBQueryThread(this.getMapper(), info);
            }

            return true;
         } else {
            return false;
         }
      } else {
         this.logger.error("Error because of null info");
         return false;
      }
   }

   public boolean setDeviceDisplayExtConf(DeviceDisplayConf info, boolean runDBQuery) throws SQLException {
      if (info != null) {
         info.setMdc_update_time(new Timestamp(System.currentTimeMillis()));
         if (info.getDevice_id() != null && !info.getDevice_id().equals("")) {
            DBCacheUtils.setDeviceDisplayConf(info, info.getDevice_id());
            if (runDBQuery) {
               DBCacheUtils.runDBQueryThread(this.getMapper(), info);
            }

            return true;
         } else {
            return false;
         }
      } else {
         this.logger.error("Error because of null info");
         return false;
      }
   }

   public Map getConstants() {
      return DeviceConstants.getConstantsMap();
   }

   public boolean checkSameVWLLayout(String deviceId) throws SQLException {
      int layout_result = ((DeviceDisplayConfDaoMapper)this.getMapper()).checkSameVWLLayout(deviceId);
      return layout_result == 1;
   }

   public int checkDifferentVWLPosition(String deviceId) throws SQLException {
      int position_result = ((DeviceDisplayConfDaoMapper)this.getMapper()).checkDifferentVWLPosition(deviceId);
      return position_result;
   }

   public DeviceDisplayConf getDeviceDisplayConf(String deviceId, Boolean cache) throws SQLException {
      DeviceDisplayConf deviceDisplayConf = null;

      try {
         if (cache) {
            deviceDisplayConf = this.getDeviceDisplayConf(deviceId);
         } else {
            deviceDisplayConf = ((DeviceDisplayConfDaoMapper)this.getMapper()).getDeviceDisplayConf(deviceId);
         }
      } catch (Exception var5) {
         this.logger.error(var5.toString());
      }

      return deviceDisplayConf;
   }

   public boolean setDeviceDisplayConfData(DeviceDisplayConf info) throws SQLException {
      try {
         ((DeviceDisplayConfDaoMapper)this.getMapper()).setDeviceDisplayConf(info);
         ((DeviceDisplayConfDaoMapper)this.getMapper()).setDeviceDisplayExtConf(info);
         return true;
      } catch (Exception var3) {
         this.logger.error(var3.toString());
         return false;
      }
   }

   public boolean addDeviceDisplayConfData(DeviceDisplayConf displayConf) throws SQLException {
      try {
         return ((DeviceDisplayConfDaoMapper)this.getMapper()).addDeviceDisplayConfAll(displayConf);
      } catch (Exception var3) {
         this.logger.error(var3.toString());
         return false;
      }
   }
}
