<?xml version="1.0" encoding="UTF-8"?> 
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.samsung.magicinfo.framework.ruleset.dao.RuleSetDaoMapper">
	
	<select id="getRuleSetList" resultType="com.samsung.magicinfo.framework.ruleset.entity.RuleSet">
		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.startIndex)" />
        <bind name="safe_limit" value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.pageSize)" />	  	
	
		SELECT * FROM (
			SELECT DISTINCT ON (A.RULESET_ID)
				A.RULESET_ID, A.VERSION, A.NAME, A.IS_DELETED, A.CREATE_DATE, <PERSON><PERSON>_DATE,
				<PERSON><PERSON>DEVICE_T<PERSON>, A<PERSON>_<PERSON>_VERSION,
			  	A.CREATOR, A.<PERSON>_RULE, A.SYNC_ENABLE, A.SYNC_DURATION, A.IS_TEMP, A.NUMBER_OF_RULES,
			  	B.GROUP_ID, C.PROGRAM_ID
		  	FROM 
		  		MI_RULE_INFO_RULESET A LEFT OUTER JOIN MI_CDS_INFO_SCHEDULE C ON A.RULESET_ID = C.CONTENT_ID,
		  		MI_RULE_MAP_RULESET_GROUP B
			WHERE
				A.RULESET_ID = B.RULESET_ID	  	
		  		<include refid="queryConditionCheck" /> 
		  	GROUP BY 
		  		A.RULESET_ID, A.VERSION,NAME, A.IS_DELETED, A.CREATE_DATE, A.MODIFY_DATE,
		  		A.DEVICE_TYPE, A.DEVICE_TYPE_VERSION,
		  		A.CREATOR, A.MULTI_RULE, A.SYNC_ENABLE, A.SYNC_DURATION, A.IS_TEMP, A.NUMBER_OF_RULES,
		  		B.GROUP_ID, C.PROGRAM_ID
		) SUB
		
  		<choose>
            <when test="condition.sortName != null and condition.sortName != ''">
                <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(condition.orderDir)" />
                <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(condition.sortName)" />
                ORDER BY
                    ${safe_sortColumn} ${safe_sortOrder}
            </when>
            <otherwise>ORDER BY MODIFY_DATE</otherwise>
        </choose>
		LIMIT ${safe_limit} OFFSET ${safe_startPos}        
	</select> 
	
	<select id="getRuleSetList" resultType="com.samsung.magicinfo.framework.ruleset.entity.RuleSet" databaseId="mssql">
		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.startIndex)" />
        <bind name="safe_limit" value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.startIndex + condition.pageSize)" />	  	
		SELECT * FROM (
			SELECT 
				A.RULESET_ID, A.VERSION, A.NAME, A.IS_DELETED, A.CREATE_DATE, A.MODIFY_DATE,
				A.DEVICE_TYPE, A.DEVICE_TYPE_VERSION,
			  	A.CREATOR, A.MULTI_RULE, A.SYNC_ENABLE, A.SYNC_DURATION, A.IS_TEMP, A.NUMBER_OF_RULES,
			  	B.GROUP_ID, C.PROGRAM_ID,
				ROW_NUMBER() OVER( ORDER BY 
				<choose>
		            <when test="condition.sortName != null and condition.sortName != ''">
		                <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(condition.orderDir)" />
		                <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(condition.sortName)" />
		                
		                <choose>
		                	<when test="safe_sortColumn.equalsIgnoreCase('program_id') or safe_sortColumn.equalsIgnoreCase('group_id')">
		                		${safe_sortColumn} ${safe_sortOrder}
		                	</when>
		                	<otherwise>
		                		A.${safe_sortColumn} ${safe_sortOrder}
		                	</otherwise>
		                </choose>
		            </when>
		            <otherwise>A.MODIFY_DATE</otherwise>
		        </choose> 
				) AS ROWNUM
			FROM 
		  		MI_RULE_INFO_RULESET A OUTER APPLY (SELECT TOP 1 * FROM MI_CDS_INFO_SCHEDULE WHERE CONTENT_ID = A.RULESET_ID) AS C,
		  		MI_RULE_MAP_RULESET_GROUP B
		  	
		  	WHERE
				A.RULESET_ID = B.RULESET_ID	  	
		  		<include refid="queryConditionCheck" /> 
			GROUP BY 
				A.RULESET_ID, A.VERSION, A.NAME, A.IS_DELETED, A.CREATE_DATE, A.MODIFY_DATE,
		  		A.DEVICE_TYPE, A.DEVICE_TYPE_VERSION,
		  		A.CREATOR, A.MULTI_RULE, A.SYNC_ENABLE, A.SYNC_DURATION, A.IS_TEMP, A.NUMBER_OF_RULES,
		  		B.GROUP_ID, C.PROGRAM_ID
		) SUB
		WHERE ROWNUM > ${safe_startPos} AND ROWNUM &lt;= ${safe_limit}
		ORDER BY ROWNUM    
	</select>
	
	<select id="getConditionList" resultType="com.samsung.magicinfo.framework.ruleset.entity.Condition">
		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.startIndex)" />
        <bind name="safe_limit" value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.pageSize)" />	  	
	
		SELECT 
			A.*
	  	FROM 
	  		MI_RULE_INFO_CONDITION A
		WHERE
			1=1
			<if test="condition.orgId != null">
			AND A.ORGANIZATION_ID = #{condition.orgId}
			</if>			
			<if test="is_public != null">
				AND A.IS_PUBLIC = #{is_public}
			</if>
			<if test="condition.srcName != null and condition.srcName != ''">
            	<bind name="srcUpper" value="condition.srcName.toUpperCase()"/>
            	<bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
            	AND (UPPER(A.CONDITION_NAME) 
            	LIKE #{srcNamePattern} 
            	ESCAPE '^')
			</if> 	
	  	GROUP BY 
	  		A.CONDITION_ID, A.CONDITION_NAME, A.ORGANIZATION_ID, A.CREATOR, A.TYPE, A.SIGN, 
			A.MODIFY_DATE
  		<choose>
            <when test="condition.sortName != null and condition.sortName != ''">
                <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(condition.orderDir)" />
                <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(condition.sortName)" />
                ORDER BY
                    ${safe_sortColumn} ${safe_sortOrder}
            </when>
            <otherwise>ORDER BY MODIFY_DATE</otherwise>
        </choose>
		LIMIT ${safe_limit} OFFSET ${safe_startPos}        
	</select> 
	
	<select id="getConditionList" resultType="com.samsung.magicinfo.framework.ruleset.entity.Condition" databaseId="mssql">
		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.startIndex)" />
        <bind name="safe_limit" value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.startIndex + condition.pageSize)" />	  	
		SELECT * FROM (
			SELECT 
				A.*,
				ROW_NUMBER() OVER( ORDER BY 
				<choose>
		            <when test="condition.sortName != null and condition.sortName != ''">
		                <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(condition.orderDir)" />
		                <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(condition.sortName)" />
	                    ${safe_sortColumn} ${safe_sortOrder}
		            </when>
		            <otherwise>A.MODIFY_DATE</otherwise>
		        </choose> 
				) AS ROWNUM
			FROM 
		  		MI_RULE_INFO_CONDITION A
		  	WHERE 1=1 
				<if test="condition.orgId != null">
					AND A.ORGANIZATION_ID = #{condition.orgId}
				</if>		
				<if test="is_public != null">
					AND A.IS_PUBLIC = #{is_public}
				</if>
				<if test="condition.srcName != null and condition.srcName != ''">
	            	<bind name="srcUpper" value="condition.srcName.toUpperCase()"/>
	            	<bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
	            	AND (UPPER(A.CONDITION_NAME) 
	            	LIKE #{srcNamePattern} 
	            	ESCAPE '^')
				</if>
			GROUP BY 
				A.CONDITION_ID, A.CONDITION_NAME, A.ORGANIZATION_ID, A.CREATOR, A.TYPE,
		  		A.SIGN, A.VALUE, A.CREATE_DATE, A.MODIFY_DATE, A.SOURCE, A.VALUE_TYPE,
		  		A.SERVER_ADDRESS, A.VALUE_LOCATION, A.DATALINK_VIEW, A.TAG_MATCH_TYPE,
		  		A.TAG_MATCH_COLUMN, A.TAGS, A.IS_PUBLIC, A.REPEAT_TYPE, A.IS_INVERT,
		  		A.DESCRIPTION, A.DATALINK_POLLING_INTERVAL		
		) SUB
		WHERE ROWNUM > ${safe_startPos} AND ROWNUM &lt;= ${safe_limit}
		ORDER BY ROWNUM    
	</select>
	
	<select id="getConditionListTotalCount" resultType="int">
		SELECT 
			COUNT(A.CONDITION_ID)
	  	FROM 
	  		MI_RULE_INFO_CONDITION A
		WHERE 1=1
			<if test="condition.orgId != null">
			AND A.ORGANIZATION_ID = #{condition.orgId}
			</if>
			<if test="is_public != null">
				AND A.IS_PUBLIC = #{is_public}
			</if>
			<if test="condition.srcName != null and condition.srcName != ''">
            	<bind name="srcUpper" value="condition.srcName.toUpperCase()"/>
            	<bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
            	AND (UPPER(A.CONDITION_NAME) 
            	LIKE #{srcNamePattern} 
            	ESCAPE '^')
			</if>
	</select>
	
	<select id="getCondition" resultType="com.samsung.magicinfo.framework.ruleset.entity.Condition">
		SELECT 
			*
		FROM
			MI_RULE_INFO_CONDITION
		WHERE
			CONDITION_ID = #{condition_id}
	</select>
	
	<select id="getAllDeletedRulesetIds" resultType="String">
		SELECT RULESET_ID FROM MI_RULE_INFO_RULESET
		WHERE IS_DELETED = <include refid="utils.true"/>
		<if test="organizationId != null and organizationId != 0">
			AND ORGANIZATION_ID = #{organizationId}
		</if>
	</select>

	<select id="getRuleSetListTotalCount" resultType="int">
		SELECT COUNT(*) FROM (
			SELECT DISTINCT 
				A.RULESET_ID
		  	FROM 
		  		MI_RULE_INFO_RULESET A LEFT OUTER JOIN MI_CDS_INFO_SCHEDULE C ON A.RULESET_ID = C.CONTENT_ID,
		  		MI_RULE_MAP_RULESET_GROUP B
			WHERE
				A.RULESET_ID = B.RULESET_ID
				<include refid="queryConditionCheck" />
		) SUB
	</select>
	
	<sql id="queryConditionCheck">
		<if test="condition.orgId != null">
			AND A.ORGANIZATION_ID = #{condition.orgId}
		</if>
		<choose>
			<when test="condition.groupIds != null">
				AND B.GROUP_ID IN
				<foreach item="item" index="index" collection="condition.groupIds" open="(" separator="," close=")">
					#{item}
				</foreach>
			</when>
			<otherwise>
				<if test="condition.groupId != null">
					<choose>
						<when test="condition.groupId == 0">
							AND B.GROUP_ID IN (SELECT GROUP_ID FROM MI_RULE_INFO_RULESET_GROUP)
						</when>
						<otherwise>
							AND B.GROUP_ID = #{condition.groupId}
						</otherwise>
					</choose>
				</if>
			</otherwise>
		</choose>
	  	<if test="condition.srcName != null and condition.srcName != ''">
           	<bind name="srcUpper" value="condition.srcName.toUpperCase()"/>
           	<bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
           	AND (UPPER(A.NAME) 
           	LIKE #{srcNamePattern} 
           	ESCAPE '^')
		</if> 
		<if test="condition.deviceType != null and condition.deviceType.equals('SPLAYER')">
           	AND (A.DEVICE_TYPE = #{condition.deviceType}
           			AND A.DEVICE_TYPE_VERSION &lt;= #{condition.deviceTypeVersion})
		</if> 
	  	<choose>
            <when test="condition.isDeleted != null and condition.isDeleted == true">
                AND A.IS_DELETED = <include refid="utils.true"/> 
            </when>
            <otherwise>AND A.IS_DELETED = <include refid="utils.false"/> </otherwise>
        </choose>
        <if test="condition.includeTemp == null or condition.includeTemp == false">
        	AND A.IS_TEMP = <include refid="utils.false"/>
        </if>
        <if test="condition.creatorIds != null">
			AND A.CREATOR IN
			<foreach item="item" index="index" collection="condition.creatorIds" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="condition.isUsed != null or condition.isNotUsed or condition.isTemp">
			
			<trim prefix="AND (" prefixOverrides="OR" suffix=")">
				<if test="condition.isUsed != null and condition.isUsed == true">
					OR C.PROGRAM_ID IS NOT NULL
				</if>
				<if test="condition.isNotUsed != null and condition.isNotUsed == true">
					OR (C.PROGRAM_ID IS NULL AND A.IS_TEMP = <include refid="utils.false"/>)
				</if>
				<if test="condition.isTemp != null and condition.isTemp == true">
					OR A.IS_TEMP = <include refid="utils.true"/>
				</if>
			</trim>
		</if>
	</sql>
	
	<select id="getRuleset" resultType="com.samsung.magicinfo.framework.ruleset.entity.RuleSet">
		SELECT DISTINCT ON (A.RULESET_ID)
			A.*, CONTENTS.CONTENT_NAME AS DEFAULT_PLAY_NAME, B.GROUP_ID, C.GROUP_NAME, D.PROGRAM_ID
		FROM
			MI_RULE_INFO_RULESET A
			LEFT JOIN MI_CMS_INFO_CONTENT CONTENTS ON A.DEFAULT_PLAY = CONTENTS.CONTENT_ID
			LEFT OUTER JOIN MI_CDS_INFO_SCHEDULE D ON A.RULESET_ID = D.CONTENT_ID, 
			MI_RULE_MAP_RULESET_GROUP B, MI_RULE_INFO_RULESET_GROUP C
		WHERE
			A.RULESET_ID = B.RULESET_ID AND B.GROUP_ID = C.GROUP_ID	
			AND A.RULESET_ID = #{ruleset_id}
	</select>
	
	<select id="getRuleset" resultType="com.samsung.magicinfo.framework.ruleset.entity.RuleSet" databaseId="mssql">
		SELECT 
			A.*, CONTENTS.CONTENT_NAME AS DEFAULT_PLAY_NAME, B.GROUP_ID, C.GROUP_NAME, D.PROGRAM_ID
		FROM
			MI_RULE_INFO_RULESET A
			LEFT JOIN MI_CMS_INFO_CONTENT CONTENTS ON A.DEFAULT_PLAY = CONTENTS.CONTENT_ID 
			OUTER APPLY (SELECT TOP 1 * FROM MI_CDS_INFO_SCHEDULE WHERE CONTENT_ID = A.RULESET_ID) AS D,
			MI_RULE_MAP_RULESET_GROUP B, MI_RULE_INFO_RULESET_GROUP C
		WHERE
			A.RULESET_ID = B.RULESET_ID AND B.GROUP_ID = C.GROUP_ID	
			AND A.RULESET_ID = #{ruleset_id}
	</select>
	
	<select id="getConditionsInRuleset" resultType="com.samsung.magicinfo.framework.ruleset.entity.Condition">
		SELECT 
			*
		FROM
			MI_RULE_INFO_CONDITION
		WHERE 
			CONDITION_ID IN ( SELECT CONDITION_ID FROM MI_RULE_MAP_RULESET_CONDITION WHERE RULESET_ID = #{ruleset_id})
	</select>
	
	<select id="getPublicConditions" resultType="com.samsung.magicinfo.framework.ruleset.entity.Condition">
		SELECT 
			*
		FROM
			MI_RULE_INFO_CONDITION
		WHERE 
			<if test="organization_id != null">
				ORGANIZATION_ID = #{organization_id} AND
			</if>
			IS_PUBLIC = <include refid="utils.true"/> 
	</select>
	
	<select id="getContentsInRuleset" resultType="map">
		SELECT
			D.CONTENTS_ID, D.CONTENTS_TYPE
		FROM
			MI_RULE_INFO_RULESET A,
			MI_RULE_MAP_RULESET_RESULT B,
			MI_RULE_INFO_RESULT C,
			MI_RULE_MAP_RESULT_CONTENT D
		WHERE
			A.RULESET_ID = B.RULESET_ID AND
			B.RESULT_ID = C.RESULT_ID AND
			C.RESULT_ID = D.RESULT_ID AND
			A.RULESET_ID = #{ruleset_id}
	</select>
	
	<select id="getContentIdsInResult" resultType="String">
		SELECT 
			CONTENTS_ID
		FROM
			MI_RULE_MAP_RESULT_CONTENT
		WHERE
			RESULT_ID = #{result_id}
	</select>
	
	<insert id="addRuleset">
         INSERT INTO MI_RULE_INFO_RULESET (
        	ruleset_id, version, is_deleted, name, create_date, modify_date, 
            creator, multi_rule, sync_enable, sync_duration, default_play, 
            file_id, organization_id, device_type, device_type_version, rule_tree, description, is_temp, number_of_rules)
  		 VALUES (#{ruleset.ruleset_id}, 1, <include refid="utils.false"/>, #{ruleset.name}, 
  		 <include refid="utils.currentTimestamp"/>, <include refid="utils.currentTimestamp"/>, #{ruleset.creator}, #{ruleset.multi_rule}, 
  		 #{ruleset.sync_enable}, #{ruleset.sync_duration}, #{ruleset.default_play}, #{ruleset.file_id}, #{ruleset.organization_id},
  		 #{ruleset.device_type}, #{ruleset.device_type_version}, #{ruleset.rule_tree}, #{ruleset.description}, #{ruleset.is_temp}, #{ruleset.number_of_rules});
    </insert>
    
    <update id="setDeleteStatus">
    	UPDATE MI_RULE_INFO_RULESET
		SET 
			is_deleted = #{is_deleted},
			modify_date = <include refid="utils.currentTimestamp"/>
		 WHERE 
		 	RULESET_ID IN 
    		<foreach item="ruleset_id" collection="ruleset_ids" open="(" separator="," close=")">
            	#{ruleset_id}
       		</foreach>
    </update>
    
    <delete id="deleteRuleset">
    	DELETE FROM MI_RULE_INFO_RULESET
    	WHERE 
		 	RULESET_ID IN 
    		<foreach item="ruleset_id" collection="ruleset_ids" open="(" separator="," close=")">
            	#{ruleset_id}
       		</foreach>
    </delete>
    
    <insert id="addRulesetGroupMapping">
         INSERT INTO MI_RULE_MAP_RULESET_GROUP (RULESET_ID, GROUP_ID)
         VALUES (#{ruleset_id}, #{group_id});
    </insert>
    
    <insert id="addRulesetGroup">
		INSERT INTO MI_RULE_INFO_RULESET_GROUP (group_id, p_group_id, group_depth, group_name, description)
        VALUES (#{group.group_id}, #{group.p_group_id}, #{group.group_depth}, #{group.group_name}, #{group.description});
    </insert>
    
    <update id="updateRulesetGroup">
    	UPDATE MI_RULE_INFO_RULESET_GROUP SET p_group_id=#{group.p_group_id}, group_depth=#{group.group_depth}, group_name=#{group.group_name}, description=#{group.description} WHERE GROUP_ID = #{group.group_id}
    </update>
    
    <delete id="deleteRulesetGroup">
    	DELETE FROM MI_RULE_INFO_RULESET_GROUP WHERE GROUP_ID IN
    	<foreach item="group" collection="groups" open="(" separator="," close=")">
			#{group.group_id}
  		</foreach>
    </delete>
    
    <insert id="addRulesetOrganization">
		INSERT INTO MI_RULE_INFO_RULESET_GROUP (GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME, DESCRIPTION)
		VALUES ( #{groupId}, 0, 1, #{organName}, 'Organization' )
	</insert>
	
	<insert id="addRulesetDefaultGroup">
		INSERT INTO MI_RULE_INFO_RULESET_GROUP ( GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME, DESCRIPTION)
		VALUES ( #{groupId}, #{organId}, 2, #{groupName}, 'Default Group')
	</insert>
    
    <insert id="addCondition">
         INSERT INTO MI_RULE_INFO_CONDITION (
            condition_id, condition_name, organization_id, creator, create_date, modify_date, 
            sign, source, type, value, value_type, server_address, value_location, 
            datalink_view, tag_match_type, tag_match_column, tags, is_public, description, repeat_type, is_invert, datalink_polling_interval) 
         VALUES ( #{condition.condition_id}, #{condition.condition_name}, #{condition.organization_id}, #{condition.creator}, <include refid="utils.currentTimestamp"/>, <include refid="utils.currentTimestamp"/>, 
            #{condition.sign}, #{condition.source}, #{condition.type}, #{condition.value}, #{condition.value_type}, #{condition.server_address}, #{condition.value_location}, 
            #{condition.datalink_view}, #{condition.tag_match_type}, #{condition.tag_match_column}, #{condition.tags}, #{condition.is_public}, #{condition.description},
            #{condition.repeat_type}, #{condition.is_invert}, #{condition.datalink_polling_interval});
    </insert>

	<insert id="addRulesetConditionMapping">
         INSERT INTO MI_RULE_MAP_RULESET_CONDITION (ruleset_id, condition_id)
         VALUES (#{ruleset_id}, #{condition_id});
    </insert>
    
    <insert id="addResultContentMapping">
         INSERT INTO MI_RULE_MAP_RESULT_CONTENT (result_id, contents_id, contents_type)
   		 VALUES
   		 <foreach item="contents_id" collection="contents_id_list" open="(" separator="),(" close=")">
   		  #{result_id}, #{contents_id}, #{contents_type}
   		  </foreach>
    </insert>
    
    <insert id="addRulesetResultMapping">
    	INSERT INTO MI_RULE_MAP_RULESET_RESULT (ruleset_id, result_id)
    	VALUES (#{ruleset_id}, #{result_id});
    </insert>
	
    <update id="updateRuleset">
		UPDATE MI_RULE_INFO_RULESET
		SET 
			<if test="ruleset.name != null">
				name = #{ruleset.name}, 
			</if>
			<if test="ruleset.multi_rule != null">
		    multi_rule = #{ruleset.multi_rule},
		    </if>
		    <if test="ruleset.sync_enable != null">
		    sync_enable = #{ruleset.sync_enable}, 
		    </if>
		    <if test="ruleset.sync_duration != null">
		    sync_duration = #{ruleset.sync_duration},
		    </if>
		    <if test="ruleset.default_play != null"> 
		    default_play = #{ruleset.default_play},
		    </if>
		    <if test="ruleset.file_id != null"> 
		    file_id = #{ruleset.file_id}, 
		    </if>
		    <if test="ruleset.organization_id != null">
		    organization_id = #{ruleset.organization_id},
		    </if>
		    <if test="ruleset.rule_tree != null">
		    rule_tree = #{ruleset.rule_tree},
		    </if>
		    <if test="ruleset.description != null">
		    description = #{ruleset.description},
		    </if>
		    <if test="ruleset.is_temp != null">
		    is_temp = #{ruleset.is_temp},
		    </if>
		    <if test="ruleset.number_of_rules != null">
		    number_of_rules = #{ruleset.number_of_rules},
		    </if>
			<if test="ruleset.creator != null">
			creator = #{ruleset.creator},
			</if>
		    version = version + 1 ,
		    modify_date = <include refid="utils.currentTimestamp"/>
		 WHERE 
		 	ruleset_id = #{ruleset.ruleset_id};
	</update>
	
	<update id="updateRulesetGroupMapping">
		UPDATE MI_RULE_MAP_RULESET_GROUP
			SET GROUP_ID = #{group_id}
			WHERE RULESET_ID = #{ruleset_id};
	</update>
	
	<update id="updateCondition">
		UPDATE mi_rule_info_condition
	   	SET 
	   		sign=#{condition.sign}, source=#{condition.source}, type=#{condition.type}, value=#{condition.value}, value_type=#{condition.value_type}, 
	       	server_address=#{condition.server_address}, value_location=#{condition.value_location}, datalink_view=#{condition.datalink_view}, tag_match_type=#{condition.tag_match_type}, 
	       	tag_match_column=#{condition.tag_match_column}, tags=#{condition.tags}, condition_name=#{condition.condition_name}, 
	       	modify_date=<include refid="utils.currentTimestamp"/>, description=#{condition.description},
	       	repeat_type=#{condition.repeat_type}, is_invert=#{condition.is_invert}, datalink_polling_interval=#{condition.datalink_polling_interval}
	 	WHERE condition_id = #{condition.condition_id};	 		
	</update>
	
	<select id="getConditionIdsInRuleset" resultType="String">
		SELECT DISTINCT CONDITION_ID FROM MI_RULE_MAP_RULESET_CONDITION
    		WHERE RULESET_ID = #{ruleset_id}
	</select>
	
	<select id="getResultIdsInRuleset" resultType="String">
		SELECT DISTINCT RESULT_ID FROM MI_RULE_MAP_RULESET_RESULT
    		WHERE RULESET_ID = #{ruleset_id}
	</select>
	
	<delete id="deleteRulesetConditionMap">
		DELETE FROM MI_RULE_MAP_RULESET_CONDITION
    		WHERE RULESET_ID = #{ruleset_id}
    </delete>
    
    <delete id="deleteRulesetResultMap">
		DELETE FROM MI_RULE_MAP_RULESET_RESULT
    		WHERE RULESET_ID = #{ruleset_id}
    </delete>
    
    <delete id="deletRulesetConditionMap" databaseId="mssql">
		DELETE FROM MI_RULE_MAP_RULESET_CONDITION
      		WHERE RULESET_ID = #{ruleset_id}
    </delete>
    
    <delete id="deleteConditions">
    	DELETE FROM MI_RULE_INFO_CONDITION 
    	WHERE 
    	CONDITION_ID IN 
    	<foreach item="condition_id" collection="condition_ids" open="(" separator="," close=")">
            	#{condition_id}
       	</foreach>
    </delete>
    
    <delete id="deleteRulesetGroupMapping">
    	DELETE FROM MI_RULE_MAP_RULESET_GROUP 
    	WHERE RULESET_ID = #{ruleset_id}
    </delete>
    
	<select id="getChildGroupList" resultType="com.samsung.magicinfo.framework.ruleset.entity.RulesetGroup">
        SELECT
            *
        FROM MI_RULE_INFO_RULESET_GROUP
        WHERE P_GROUP_ID = #{groupId}
        ORDER BY GROUP_NAME ASC
    </select>
    
    <select resultType="java.lang.Long" id="getOrgGroupIdByName">
        SELECT
            GROUP_ID
        FROM MI_RULE_INFO_RULESET_GROUP
        WHERE GROUP_NAME = #{groupName} AND GROUP_DEPTH &lt; 2
    </select>
    
    <select id="getGroupById" resultType="com.samsung.magicinfo.framework.ruleset.entity.RulesetGroup">
    	SELECT *
    	FROM MI_RULE_INFO_RULESET_GROUP
    	WHERE GROUP_ID = #{groupId}
    </select>
    
    <select resultType="java.lang.Integer" id="getCountGroupedRuleset">
        SELECT
        	COUNT(A.RULESET_ID)
        FROM 
	  		MI_RULE_INFO_RULESET A,
	  		MI_RULE_MAP_RULESET_GROUP B
		WHERE
			A.RULESET_ID = B.RULESET_ID
			AND IS_DELETED = <include refid="utils.false"/>
        
		<if test="map.groupIds != null">
			AND B.GROUP_ID IN
			<foreach item="item" index="index" collection="map.groupIds" open="(" separator="," close=")">
       			#{item}
			</foreach>
		</if>
        <if test="map.group_id != null">
            <bind name="safe_groupId" value="@com.samsung.common.utils.DaoTools@safeNumeric(map.group_id)" />
            AND B.GROUP_ID = ${safe_groupId}
        </if>
    </select>
    
    <select id="getCountRulesetByOrganization" resultType="java.lang.Long">
    	SELECT COUNT(*) FROM MI_RULE_INFO_RULESET WHERE IS_DELETED = <include refid="utils.false"/> AND ORGANIZATION_ID = #{organizationId}
    </select>
    
    <insert id="addResult">
         INSERT INTO MI_RULE_INFO_RESULT (
        	result_id, result_name, organization_id, creator, create_date, modify_date, 
            contents_type, filter_separator, filter_src_type, filter_dst_type, filter_expression, 
            is_public, type, filter_sign, filter_value_1, filter_value_2, filter_value_3, filter_value_4, description, default_duration, device_type, device_type_version, filter_expression_type)      
  		 VALUES (#{result.result_id}, #{result.result_name}, #{result.organization_id}, #{result.creator}, 
  		 <include refid="utils.currentTimestamp"/>, <include refid="utils.currentTimestamp"/>, 
  		 #{result.contents_type}, #{result.filter_separator}, 
  		 #{result.filter_src_type}, #{result.filter_dst_type}, #{result.filter_expression}, 
  		 #{result.is_public}, #{result.type},  #{result.filter_sign}, #{result.filter_value_1}, #{result.filter_value_2}, #{result.filter_value_3}, #{result.filter_value_4},
  		 #{result.description}, #{result.default_duration}, #{result.device_type}, #{result.device_type_version}, #{result.filter_expression_type});
    </insert>
  
  	<select id="getPublicResultList" resultType="com.samsung.magicinfo.framework.ruleset.entity.Result">
		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.startIndex)" />
        <bind name="safe_limit" value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.pageSize)" />	  	
	
		SELECT 
			A.*, B.GROUP_NAME AS ORGANIZATION_NAME
	  	FROM 
	  		MI_RULE_INFO_RESULT A,
	  		MI_USER_INFO_GROUP B
		WHERE
			A.ORGANIZATION_ID = B.GROUP_ID AND
			A.IS_PUBLIC = <include refid="utils.true"/>   	
			<if test="condition.srcName != null and condition.srcName != ''">
            	<bind name="srcUpper" value="condition.srcName.toUpperCase()"/>
            	<bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
            	AND (UPPER(A.RESULT_NAME) 
            	LIKE #{srcNamePattern} 
            	ESCAPE '^')
			</if> 
			<if test="condition.deviceType != null and condition.deviceType.equals('SPLAYER')">
               AND (A.DEVICE_TYPE = #{condition.deviceType} AND A.DEVICE_TYPE_VERSION &lt;= #{condition.deviceTypeVersion})
        	</if>		
	  	GROUP BY 
	  		A.RESULT_ID, A.RESULT_NAME, A.ORGANIZATION_ID, A.CREATOR, A.TYPE, A.CONTENTS_TYPE, 
			A.MODIFY_DATE, B.GROUP_NAME	
  		<choose>
            <when test="condition.sortName != null and condition.sortName != ''">
                <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(condition.orderDir)" />
                <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(condition.sortName)" />
                ORDER BY
                    ${safe_sortColumn} ${safe_sortOrder}
            </when>
            <otherwise>ORDER BY MODIFY_DATE</otherwise>
        </choose>
		LIMIT ${safe_limit} OFFSET ${safe_startPos}        
	</select> 
	
	<select id="getPublicResultList" resultType="com.samsung.magicinfo.framework.ruleset.entity.Result" databaseId="mssql">
		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.startIndex)" />
        <bind name="safe_limit" value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.startIndex + condition.pageSize)" />	  	
		SELECT * FROM (
			SELECT 
				A.RESULT_ID, A.RESULT_NAME, A.ORGANIZATION_ID, A.CREATOR, A.TYPE, A.CONTENTS_TYPE, 
				A.MODIFY_DATE, A.FILTER_EXPRESSION, B.GROUP_NAME AS ORGANIZATION_NAME,
				ROW_NUMBER() OVER( ORDER BY 
				<choose>
		            <when test="condition.sortName != null and condition.sortName != ''">
		                <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(condition.orderDir)" />
		                <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(condition.sortName)" />
	                    ${safe_sortColumn} ${safe_sortOrder}
		            </when>
		            <otherwise>A.MODIFY_DATE</otherwise>
		        </choose> 
				) AS ROWNUM
			FROM 
		  		MI_RULE_INFO_RESULT A	  			
		  	WHERE
				A.ORGANIZATION_ID = B.GROUP_ID AND
				A.IS_PUBLIC = <include refid="utils.true"/> 	
				<if test="condition.srcName != null and condition.srcName != ''">
	            	<bind name="srcUpper" value="condition.srcName.toUpperCase()"/>
	            	<bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
	            	AND (UPPER(A.RESULT_NAME) 
	            	LIKE #{srcNamePattern} 
	            	ESCAPE '^')
				</if> 
				<if test="condition.deviceType != null and condition.deviceType.equals('SPLAYER')">
               	AND (A.DEVICE_TYPE = #{condition.deviceType} AND A.DEVICE_TYPE_VERSION &lt;= #{condition.deviceTypeVersion})
        		</if>	
			GROUP BY 
				A.RESULT_ID, A.RESULT_NAME, A.ORGANIZATION_ID, A.CREATOR, A.TYPE, A.CONTENTS_TYPE, 
				A.MODIFY_DATE, A.FILTER_EXPRESSION	
		) SUB
		WHERE ROWNUM > ${safe_startPos} AND ROWNUM &lt;= ${safe_limit}
		ORDER BY ROWNUM    
	</select>
	
	<select id="getPublicResultListTotalCount" resultType="int">
		SELECT 
			COUNT(A.RESULT_ID)
	  	FROM 
	  		MI_RULE_INFO_RESULT A,
			MI_USER_INFO_GROUP B
		WHERE
			A.ORGANIZATION_ID = B.GROUP_ID AND
			A.IS_PUBLIC = <include refid="utils.true"/>	  
			<if test="condition.srcName != null and condition.srcName != ''">
            	<bind name="srcUpper" value="condition.srcName.toUpperCase()"/>
            	<bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
            	AND (UPPER(A.RESULT_NAME) 
            	LIKE #{srcNamePattern} 
            	ESCAPE '^')
			</if> 
	</select>
	
	<select id="getPublicResults" resultType="com.samsung.magicinfo.framework.ruleset.entity.Result">
		SELECT 
			*
		FROM
			MI_RULE_INFO_RESULT
		WHERE 
			<if test="organization_id != null">
				ORGANIZATION_ID = #{organization_id} AND
			</if>
			IS_PUBLIC = <include refid="utils.true"/> 
	</select>
	
	<select id="getResult" resultType="com.samsung.magicinfo.framework.ruleset.entity.Result">
		SELECT 
			*
		FROM
			MI_RULE_INFO_RESULT
		WHERE
			RESULT_ID = #{result_id}
	</select>  
	
	<update id="updateResult">
		UPDATE MI_RULE_INFO_RESULT
	   	SET 
	   		result_name=#{result.result_name}, contents_type=#{result.contents_type}, type=#{result.type}, filter_separator=#{result.filter_separator}, filter_src_type=#{result.filter_src_type}, 
	       	filter_dst_type=#{result.filter_dst_type}, filter_expression=#{result.filter_expression}, filter_sign=#{result.filter_sign}, 
	       	modify_date=<include refid="utils.currentTimestamp"/>, description=#{result.description}, default_duration=#{result.default_duration}, device_type=#{result.device_type}, device_type_version=#{result.device_type_version} 
	 	WHERE result_id = #{result.result_id};	
	</update>
	
	<delete id="deleteResultContentMapping">
		DELETE FROM MI_RULE_MAP_RESULT_CONTENT
    	WHERE RESULT_ID = #{result_id}
	</delete>

    <delete id="deleteResult">
		DELETE FROM MI_RULE_INFO_RESULT
		WHERE RESULT_ID = #{result_id}
    </delete>
    
    <delete id="deleteResults">
    	DELETE FROM MI_RULE_INFO_RESULT 
    	WHERE 
    	RESULT_ID IN 
    	<foreach item="result_id" collection="result_ids" open="(" separator="," close=")">
            	#{result_id}
       	</foreach>
    </delete>
    
	 <select id="getDeviceListByCondition"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device">
		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(start)" />
		<bind name="safe_limit" value="@com.samsung.common.utils.DaoTools@safeNumeric(length)" />
		SELECT A.DEVICE_ID, A.DEVICE_NAME, C.GROUP_NAME, A.IP_ADDRESS, A.DEVICE_MODEL_NAME, A.SCREEN_ROTATION, A.SCREEN_SIZE
		<include refid="queryDeviceListByCondition" />
		LIMIT ${safe_limit} OFFSET ${safe_startPos}
	</select>
	
	<select id="getCountOfDevicesByCondition" resultType="int">
		SELECT COUNT(A.DEVICE_ID)
		<include refid="queryDeviceListByCondition" />
	</select>

	<select id="getDeviceListByCondition"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device"
		databaseId="mssql">
		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(start)" />
		<bind name="safe_limit" value="@com.samsung.common.utils.DaoTools@safeNumeric(start + length)" />
		SELECT * FROM (
		SELECT A.DEVICE_ID, A.DEVICE_NAME, C.GROUP_NAME, A.IP_ADDRESS, A.DEVICE_MODEL_NAME, A.SCREEN_ROTATION, A.SCREEN_SIZE,
		ROW_NUMBER() OVER(ORDER BY A.DEVICE_ID ASC ) as
		RowNum
		<include refid="queryDeviceListByCondition" />
		) as SubQuery
		WHERE RowNum > ${safe_startPos} and RowNum &lt;= ${safe_limit}
		ORDER BY RowNum
	</select>
	
	<select id="getRulesetUsingContents" resultType="com.samsung.magicinfo.framework.ruleset.entity.RuleSet">
		SELECT A.*, D.GROUP_ID
		FROM 
			MI_RULE_INFO_RULESET A,
			MI_RULE_MAP_RULESET_RESULT B,
			MI_RULE_MAP_RESULT_CONTENT C,
			MI_RULE_MAP_RULESET_GROUP D
		WHERE
			A.RULESET_ID = B.RULESET_ID 
			AND B.RESULT_ID = C.RESULT_ID
			AND A.RULESET_ID = D.RULESET_ID
			AND C.CONTENTS_ID = #{contents_id}
	</select>
	
	<select id="getRulesetUsingSubPlaylist" resultType="com.samsung.magicinfo.framework.ruleset.entity.RuleSet">
		SELECT
			A.*, D.GROUP_ID
		FROM 
			MI_RULE_INFO_RULESET A,
			MI_RULE_MAP_RULESET_RESULT B,
			MI_RULE_MAP_RESULT_CONTENT C,
			MI_RULE_MAP_RULESET_GROUP D
		WHERE
			A.RULESET_ID = B.RULESET_ID 
			AND B.RESULT_ID = C.RESULT_ID
			AND A.RULESET_ID = D.RULESET_ID
			AND C.CONTENTS_ID IN
			(
				SELECT DISTINCT SUBS.PLAYLIST_ID
					FROM MI_CMS_MAP_PLAYLIST_SUB_PLAYLIST SUBS, MI_CMS_INFO_PLAYLIST_VERSION VERSIONS
				WHERE
					SUBS.CONTENT_ID = #{playlistId} AND VERSIONS.PLAYLIST_ID = SUBS.PLAYLIST_ID AND VERSIONS.VERSION_ID = SUBS.VERSION_ID
					AND VERSIONS.IS_ACTIVE = 'Y'
			)
	</select>
	
	<sql id="queryDeviceListByCondition">
		FROM
		MI_DMS_INFO_DEVICE A,
		MI_DMS_MAP_GROUP_DEVICE B,
		MI_DMS_INFO_GROUP C
		WHERE
		A.DEVICE_ID = B.DEVICE_ID AND
		B.GROUP_ID = C.GROUP_ID AND		
		 A.IS_APPROVED = <include refid="utils.true"/> 
		<foreach item="condition" collection="conditions">
			<if test= "condition.groupIds != null and condition.groupIds[0] != 0">
				AND C.GROUP_ID IN
		        <foreach item="groupId" collection="condition.groupIds" open="(" separator="," close=")">
		            #{groupId}
		        </foreach>				 
			</if>
           	<if test="condition.isInvert != null and condition.isInvert == true">
               	<choose>
               		<when
						test="condition.type != null and condition.type.equals('device_nm')">					
						AND A.DEVICE_NAME NOT LIKE #{condition.value} ESCAPE '^'
					</when>
					<when
						test="condition.type != null and condition.type.equals('device_nm')">					
						AND A.DEVICE_NAME NOT LIKE #{condition.value} ESCAPE '^'
					</when>
					<when
						test="condition.type != null and condition.type.equals('dev_mdnm')">					
						AND A.DEVICE_MODEL_NAME NOT LIKE #{condition.value} ESCAPE '^'
					</when>
					<when
						test="condition.type != null and condition.type.equals('screen_rotation') and condition.value != null and !condition.value.equals('')">
						AND A.SCREEN_ROTATION != CAST(#{condition.value} AS NUMERIC)
					</when>
					<when
						test="condition.type != null and condition.type.equals('screen_size')">
						<bind name="safe_size"
							value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.value)" />
						AND
						<choose>
							<when test="condition.sign !=null and safe_size != null and !safe_size.equals('')">
								<choose>
									<when test="condition.sign.equals('gt')">
										(CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END &lt;= CAST(${safe_size} AS NUMERIC))
										AND (CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END &gt;= 0)
									</when>
									<when test="condition.sign.equals('ge')">
										(CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END &lt; CAST(${safe_size} AS NUMERIC))
										AND (CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END &gt;= 0)
									</when>
									<when test="condition.sign.equals('lt')">
										(CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END &gt;= CAST(${safe_size} AS NUMERIC))
										AND (CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END &gt;= 0)

									</when>
									<when test="condition.sign.equals('le')">
										(CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END &gt; CAST(${safe_size} AS NUMERIC))
										AND (CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END &gt;= 0)
									</when>
									<otherwise>
										(CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END != CAST(${safe_size} AS NUMERIC))
										AND (CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END &gt;= 0)
									</otherwise>
								</choose>
							</when>
							<otherwise>
								(CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END = CAST(${safe_size} AS NUMERIC))
								AND (CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END &gt;= 0)
							</otherwise>
						</choose>
					</when>
				</choose>
           	</if>
           	<if test="condition.isInvert != null and condition.isInvert == false">
            	<choose>
					<when
						test="condition.type != null and condition.type.equals('device_nm')">					
						AND A.DEVICE_NAME LIKE #{condition.value} ESCAPE '^'
					</when>
					<when
						test="condition.type != null and condition.type.equals('dev_mdnm')">					
						AND A.DEVICE_MODEL_NAME LIKE #{condition.value} ESCAPE '^'
					</when>
					<when
						test="condition.type != null and condition.type.equals('screen_rotation') and condition.value != null and !condition.value.equals('')">
						AND A.SCREEN_ROTATION = CAST(#{condition.value} AS NUMERIC)
					</when>
					<when
						test="condition.type != null and condition.type.equals('screen_size')">
						<bind name="safe_size"
							value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.value)" />
						AND
						<choose>
							<when test="condition.sign !=null and safe_size != null and !safe_size.equals('')">
								<choose>
									<when test="condition.sign.equals('gt')">
										(CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END &gt; CAST(${safe_size} AS NUMERIC))
										AND (CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END &gt;= 0)
									</when>
									<when test="condition.sign.equals('ge')">
										(CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END &gt;= CAST(${safe_size} AS NUMERIC))
										AND (CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END &gt;= 0)
									</when>
									<when test="condition.sign.equals('lt')">
										(CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END &lt; CAST(${safe_size} AS NUMERIC))
										AND (CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END &gt;= 0)

									</when>
									<when test="condition.sign.equals('le')">
										(CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END &lt;= CAST(${safe_size} AS NUMERIC))
										AND (CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END &gt;= 0)
									</when>
									<otherwise>
										(CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END = CAST(${safe_size} AS NUMERIC))
										AND (CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END &gt;= 0)
									</otherwise>
								</choose>
							</when>
							<otherwise>
								(CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END != CAST(${safe_size} AS NUMERIC))
								AND (CASE WHEN A.SCREEN_SIZE~E'^\\d+$' THEN A.SCREEN_SIZE::integer ELSE -1 END &gt;= 0)
							</otherwise>
						</choose>
					</when>
				</choose>
           	</if>        				
		</foreach>
	</sql>	
	<sql id="queryDeviceListByCondition" databaseId="mssql">
		FROM
		MI_DMS_INFO_DEVICE A,
		MI_DMS_MAP_GROUP_DEVICE B,
		MI_DMS_INFO_GROUP C
		WHERE
		A.DEVICE_ID = B.DEVICE_ID AND
		B.GROUP_ID = C.GROUP_ID AND
		A.IS_APPROVED = <include refid="utils.true"/> 
		<foreach item="condition" collection="conditions">
			<if test= "condition.groupIds != null and condition.groupIds[0] != 0">
					AND C.GROUP_ID IN
			        <foreach item="groupId" collection="condition.groupIds" open="(" separator="," close=")">
			            #{groupId}
			        </foreach>				 
			</if>
			<choose>
            	<when test="condition.isInvert != null and condition.isInvert == true">
            		<choose>
						<when
							test="condition.type != null and condition.type.equals('device_nm')">					
							AND A.DEVICE_NAME COLLATE Korean_WanSung_CS_AS NOT LIKE #{condition.value} ESCAPE '^'
						</when>
						<when
							test="condition.type != null and condition.type.equals('dev_mdnm')">					
							AND A.DEVICE_MODEL_NAME COLLATE Korean_WanSung_CS_AS NOT LIKE #{condition.value} ESCAPE '^'
						</when>
						<when
							test="condition.type != null and condition.type.equals('screen_rotation') and condition.value != null and !condition.value.equals('')">
							AND A.SCREEN_ROTATION != TRY_CAST(#{condition.value} AS NUMERIC)
						</when>
						<when
							test="condition.type != null and condition.type.equals('screen_size')">
							<bind name="safe_size"
								value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.value)" />
							AND
							<choose>
								<when test="condition.sign != null and safe_size != null and !safe_size.equals('')">
									<choose>
										<when test="condition.sign.equals('gt')">
											(TRY_CAST(A.SCREEN_SIZE AS NUMERIC) &lt;= TRY_CAST(${safe_size} AS NUMERIC))
										</when>
										<when test="condition.sign.equals('ge')">
											(TRY_CAST(A.SCREEN_SIZE AS NUMERIC) &lt; TRY_CAST(${safe_size} AS NUMERIC))
										</when>
										<when test="condition.sign.equals('lt')">
											(TRY_CAST(A.SCREEN_SIZE AS NUMERIC) &gt;= TRY_CAST(${safe_size} AS NUMERIC))
										</when>
										<when test="condition.sign.equals('le')">
											(TRY_CAST(A.SCREEN_SIZE AS NUMERIC) &gt; TRY_CAST(${safe_size} AS NUMERIC))
										</when>
										<otherwise>
											(TRY_CAST(A.SCREEN_SIZE AS NUMERIC) != TRY_CAST(${safe_size} AS NUMERIC))
										</otherwise>
									</choose>
								</when>
								<otherwise>
									(TRY_CAST(A.SCREEN_SIZE AS NUMERIC) != TRY_CAST(${safe_size} AS NUMERIC))
								</otherwise>
							</choose>
						</when>
					</choose>
            	</when>
            	<when test="condition.isInvert != null and condition.isInvert == false">
            		<choose>
						<when
							test="condition.type != null and condition.type.equals('device_nm')">					
							AND A.DEVICE_NAME COLLATE Korean_WanSung_CS_AS LIKE #{condition.value} ESCAPE '^'
						</when>
						<when
							test="condition.type != null and condition.type.equals('dev_mdnm')">					
							AND A.DEVICE_MODEL_NAME COLLATE Korean_WanSung_CS_AS LIKE #{condition.value} ESCAPE '^'
						</when>
						<when
							test="condition.type != null and condition.type.equals('screen_rotation') and condition.value != null and !condition.value.equals('')">
							AND A.SCREEN_ROTATION = TRY_CAST(#{condition.value} AS NUMERIC)
						</when>
						<when
							test="condition.type != null and condition.type.equals('screen_size')">
							<bind name="safe_size"
								value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.value)" />
							AND
							<choose>
								<when test="condition.sign != null and safe_size != null and !safe_size.equals('')">
									<choose>
										<when test="condition.sign.equals('gt')">
											(TRY_CAST(A.SCREEN_SIZE AS NUMERIC) &gt; TRY_CAST(${safe_size} AS NUMERIC))
										</when>
										<when test="condition.sign.equals('ge')">
											(TRY_CAST(A.SCREEN_SIZE AS NUMERIC) &gt;= TRY_CAST(${safe_size} AS NUMERIC))
										</when>
										<when test="condition.sign.equals('lt')">
											(TRY_CAST(A.SCREEN_SIZE AS NUMERIC) &lt; TRY_CAST(${safe_size} AS NUMERIC))
										</when>
										<when test="condition.sign.equals('le')">
											(TRY_CAST(A.SCREEN_SIZE AS NUMERIC) &lt;= TRY_CAST(${safe_size} AS NUMERIC))
										</when>
										<otherwise>
											(TRY_CAST(A.SCREEN_SIZE AS NUMERIC) = TRY_CAST(${safe_size} AS NUMERIC))
										</otherwise>
									</choose>
								</when>
								<otherwise>
									(TRY_CAST(A.SCREEN_SIZE AS NUMERIC) = TRY_CAST(${safe_size} AS NUMERIC))
								</otherwise>
							</choose>
						</when>
					</choose>
            	</when>
        	</choose>			
		</foreach>
	</sql>
	
	<select id="getResultList" resultType="com.samsung.magicinfo.framework.ruleset.entity.Result">
		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.startIndex)" />
        <bind name="safe_limit" value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.pageSize)" />	  	
	
		SELECT 
			A.*
	  	FROM 
	  		MI_RULE_INFO_RESULT A
		WHERE
			1=1
			<if test="condition.orgId != null">
				AND A.ORGANIZATION_ID = #{condition.orgId}
			</if>		
			<if test="is_public != null">
				AND A.IS_PUBLIC = #{is_public}
			</if> 	
			<if test="condition.srcName != null and condition.srcName != ''">
            	<bind name="srcUpper" value="condition.srcName.toUpperCase()"/>
            	<bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
            	AND (UPPER(A.RESULT_NAME) 
            	LIKE #{srcNamePattern} 
            	ESCAPE '^')
			</if> 
			<if test="condition.deviceType != null and condition.deviceType.equals('SPLAYER')">
               AND (A.DEVICE_TYPE = #{condition.deviceType} AND A.DEVICE_TYPE_VERSION &lt;= #{condition.deviceTypeVersion})
        	</if>		
	  	GROUP BY 
	  		A.RESULT_ID, A.RESULT_NAME, A.ORGANIZATION_ID, A.CREATOR, A.TYPE, A.CONTENTS_TYPE, 
			A.MODIFY_DATE
  		<choose>
            <when test="condition.sortName != null and condition.sortName != ''">
                <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(condition.orderDir)" />
                <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(condition.sortName)" />
                ORDER BY
                    ${safe_sortColumn} ${safe_sortOrder}
            </when>
            <otherwise>ORDER BY MODIFY_DATE</otherwise>
        </choose>
		LIMIT ${safe_limit} OFFSET ${safe_startPos}        
	</select> 
	
	<select id="getResultList" resultType="com.samsung.magicinfo.framework.ruleset.entity.Result" databaseId="mssql">
		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.startIndex)" />
        <bind name="safe_limit" value="@com.samsung.common.utils.DaoTools@safeNumeric(condition.startIndex + condition.pageSize)" />	  	
		SELECT * FROM (
			SELECT 
				A.RESULT_ID, A.RESULT_NAME, A.ORGANIZATION_ID, A.CREATOR, A.CREATE_DATE, A.MODIFY_DATE, A.CONTENTS_TYPE,
				A.TYPE, A.IS_PUBLIC, A.FILTER_SIGN, A.FILTER_SEPARATOR, A.FILTER_EXPRESSION, A.FILTER_SRC_TYPE,
				A.FILTER_DST_TYPE, A.FILTER_VALUE_1, A.FILTER_VALUE_2, A.FILTER_VALUE_3, A.FILTER_VALUE_4, A.DESCRIPTION,
				A.DEVICE_TYPE, A.DEVICE_TYPE_VERSION, A.DEFAULT_DURATION, A.FILTER_EXPRESSION_TYPE,
				ROW_NUMBER() OVER( ORDER BY 
				<choose>
		            <when test="condition.sortName != null and condition.sortName != ''">
		                <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(condition.orderDir)" />
		                <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(condition.sortName)" />
	                    A.${safe_sortColumn} ${safe_sortOrder}
		            </when>
		            <otherwise>A.MODIFY_DATE</otherwise>
		        </choose> 
				) AS ROWNUM
			FROM 
		  		MI_RULE_INFO_RESULT A
		  	WHERE
		  		1=1
				<if test="condition.orgId != null">
				AND	A.ORGANIZATION_ID = #{condition.orgId}
				</if>		
				<if test="is_public != null">
					AND A.IS_PUBLIC = #{is_public}
				</if>
				<if test="condition.srcName != null and condition.srcName != ''">
	            	<bind name="srcUpper" value="condition.srcName.toUpperCase()"/>
	            	<bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
	            	AND (UPPER(A.RESULT_NAME) 
	            	LIKE #{srcNamePattern} 
	            	ESCAPE '^')
				</if> 
				<if test="condition.deviceType != null and condition.deviceType.equals('SPLAYER')">
               	AND (A.DEVICE_TYPE = #{condition.deviceType} AND A.DEVICE_TYPE_VERSION &lt;= #{condition.deviceTypeVersion})
        		</if>	
			GROUP BY 
				A.RESULT_ID, A.RESULT_NAME, A.ORGANIZATION_ID, A.CREATOR, A.CREATE_DATE, A.MODIFY_DATE, A.CONTENTS_TYPE,
				A.TYPE, A.IS_PUBLIC, A.FILTER_SIGN, A.FILTER_SEPARATOR, A.FILTER_EXPRESSION, A.FILTER_SRC_TYPE,
				A.FILTER_DST_TYPE, A.FILTER_VALUE_1, A.FILTER_VALUE_2, A.FILTER_VALUE_3, A.FILTER_VALUE_4, A.DESCRIPTION,
				A.DEVICE_TYPE, A.DEVICE_TYPE_VERSION, A.DEFAULT_DURATION, A.FILTER_EXPRESSION_TYPE	
		) SUB
		WHERE ROWNUM > ${safe_startPos} AND ROWNUM &lt;= ${safe_limit}
		ORDER BY ROWNUM    
	</select>
	
	<select id="getResultListTotalCount" resultType="int">
		SELECT 
			COUNT(A.RESULT_ID)
	  	FROM 
	  		MI_RULE_INFO_RESULT A			
		WHERE 1=1
			<if test="condition.orgId != null">
				AND A.ORGANIZATION_ID = #{condition.orgId}
			</if>		
			<if test="is_public != null">
				AND A.IS_PUBLIC = #{is_public}
			</if> 
			<if test="condition.srcName != null and condition.srcName != ''">
            	<bind name="srcUpper" value="condition.srcName.toUpperCase()"/>
            	<bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
            	AND (UPPER(A.RESULT_NAME) 
            	LIKE #{srcNamePattern} 
            	ESCAPE '^')
			</if> 
	</select>
	
	<select id="getRulesetListByGroupId" resultType="com.samsung.magicinfo.framework.ruleset.entity.RuleSet">
		SELECT 
			A.*, CONTENTS.CONTENT_NAME AS DEFAULT_PLAY_NAME, B.GROUP_ID, C.GROUP_NAME
		FROM
			MI_RULE_INFO_RULESET A
			LEFT JOIN MI_CMS_INFO_CONTENT CONTENTS ON A.DEFAULT_PLAY = CONTENTS.CONTENT_ID, 
			MI_RULE_MAP_RULESET_GROUP B, MI_RULE_INFO_RULESET_GROUP C
		WHERE
			A.RULESET_ID = B.RULESET_ID AND B.GROUP_ID = C.GROUP_ID
			<if test="groupId != 0">
				AND B.GROUP_ID = #{groupId}
			</if>
	</select>
	
	<select id="getKeywords" 
	resultType="com.samsung.magicinfo.framework.ruleset.entity.ResultKeyword">
		SELECT 
			keyword_id, organization_id, keyword_name, keyword_values
	  	FROM 
	  		MI_RULE_INFO_KEYWORD		
		WHERE
			<if test="organization_id != null">
				ORGANIZATION_ID = #{organization_id}
			</if>
	</select>
		
	<insert id="addKeyword">
         INSERT INTO MI_RULE_INFO_KEYWORD (keyword_id, keyword_name, keyword_values, organization_id, create_date, modify_date) 
         VALUES (#{keyword.keyword_id}, #{keyword.keyword_name}, #{keyword.keyword_values}, #{keyword.organization_id}, <include refid="utils.currentTimestamp"/>, <include refid="utils.currentTimestamp"/>);
    </insert> 
    
    <update id="updateKeyword">
    	UPDATE MI_RULE_INFO_KEYWORD SET KEYWORD_NAME=#{keyword.keyword_name}, keyword_values=#{keyword.keyword_values}, modify_date=<include refid="utils.currentTimestamp"/>
    	WHERE KEYWORD_ID = #{keyword.keyword_id}
    </update>
    
    <delete id="deleteKeywords">
    	DELETE FROM MI_RULE_INFO_KEYWORD 
    	WHERE 
    	KEYWORD_ID IN 
    	<foreach item="keyword_id" collection="keyword_ids" open="(" separator="," close=")">
            	#{keyword_id}
       	</foreach>
    </delete>
    
    <select id="hasContentInRuleset" resultType="int">
    	SELECT
			COUNT(*)
		FROM
			MI_RULE_INFO_RULESET A,
			MI_RULE_INFO_RESULT B,
			MI_RULE_MAP_RULESET_RESULT C,
			MI_RULE_MAP_RESULT_CONTENT D
		WHERE
			A.RULESET_ID = C.RULESET_ID
			AND B.RESULT_ID = C.RESULT_ID
			AND B.RESULT_ID = D.RESULT_ID
			AND A.RULESET_ID = #{rulesetId}
			AND D.CONTENTS_ID = #{contentId}
    </select>
    
    <select id="getRulesetContentDownloadStatus" resultType="com.samsung.magicinfo.framework.scheduler.entity.DownloadContentEntity">
		SELECT I.CONTENT_ID, I.CONTENT_NAME, H.PROGRESS, G.TOTAL_SIZE FROM
		(
			SELECT A.RULESET_ID
			FROM MI_RULE_INFO_RULESET A, MI_CDS_INFO_SCHEDULE B
			WHERE A.RULESET_ID = B.CONTENT_ID AND A.RULESET_ID = #{rulesetId}
		) SUB,
		MI_RULE_MAP_RULESET_RESULT C,
		MI_RULE_MAP_RESULT_CONTENT D,
		MI_CMS_INFO_PLAYLIST E,
		MI_CMS_MAP_PLAYLIST_CONTENT F,
		MI_CMS_INFO_CONTENT_VERSION G,
		MI_CMS_INFO_CONTENT I LEFT OUTER JOIN MI_CDS_DOWNLOAD_STATUS H ON I.CONTENT_ID = H.CONTENT_ID AND H.DEVICE_ID = #{deviceId}
		
		WHERE
			SUB.RULESET_ID = C.RULESET_ID
			AND C.RESULT_ID = D.RESULT_ID
			AND D.CONTENTS_TYPE = 'PLAYLIST'
			AND D.CONTENTS_ID = E.PLAYLIST_ID
			AND E.PLAYLIST_ID = F.PLAYLIST_ID
			AND G.CONTENT_ID = F.CONTENT_ID
			AND G.IS_ACTIVE = 'Y'
			AND G.CONTENT_ID = I.CONTENT_ID
		
		UNION
		
		SELECT I.CONTENT_ID, I.CONTENT_NAME, H.PROGRESS, G.TOTAL_SIZE FROM
		(
			SELECT A.RULESET_ID
			FROM MI_RULE_INFO_RULESET A, MI_CDS_INFO_SCHEDULE B
			WHERE A.RULESET_ID = B.CONTENT_ID AND A.RULESET_ID = #{rulesetId}
		) SUB,
		MI_RULE_MAP_RULESET_RESULT C,
		MI_RULE_MAP_RESULT_CONTENT D,
		MI_CMS_INFO_CONTENT_VERSION G,
		MI_CMS_INFO_CONTENT I LEFT OUTER JOIN MI_CDS_DOWNLOAD_STATUS H ON I.CONTENT_ID = H.CONTENT_ID AND H.DEVICE_ID = #{deviceId}
		WHERE
			SUB.RULESET_ID = C.RULESET_ID
			AND C.RESULT_ID = D.RESULT_ID
			AND D.CONTENTS_TYPE = 'CONTENT'
			AND D.CONTENTS_ID = G.CONTENT_ID
			AND G.IS_ACTIVE = 'Y'
			AND G.CONTENT_ID = I.CONTENT_ID
    </select>

	<select id="getRulesetGroupBySearch" resultType="com.samsung.magicinfo.framework.ruleset.entity.RulesetGroup">
		WITH RECURSIVE GROUP_IDS AS (
		SELECT *
		FROM MI_RULE_INFO_RULESET_GROUP
		WHERE P_GROUP_ID = 0
		<if test="organizationName != 'ROOT'">
			AND GROUP_ID = (SELECT GROUP_ID FROM MI_RULE_INFO_RULESET_GROUP WHERE GROUP_NAME = #{organizationName} AND DESCRIPTION = 'Organization')
		</if>
		UNION ALL
		SELECT CHILD_GROUP.*
		FROM MI_RULE_INFO_RULESET_GROUP CHILD_GROUP
		JOIN GROUP_IDS ON CHILD_GROUP.P_GROUP_ID = GROUP_IDS.GROUP_ID
		)
		SELECT * FROM GROUP_IDS
		<if test="searchText != null and searchText.length() > 0">
			<bind name="searchText" value="'%' + searchText + '%'" />
			WHERE GROUP_NAME LIKE #{searchText} ESCAPE '^'
		</if>
	</select>

	<select id="getRulesetGroupBySearch" resultType="com.samsung.magicinfo.framework.ruleset.entity.RulesetGroup" databaseId="mssql">
		WITH GROUP_IDS AS (
		SELECT *
		FROM MI_RULE_INFO_RULESET_GROUP
		WHERE P_GROUP_ID = 0
		<if test="organizationName != 'ROOT'">
			AND GROUP_ID = (SELECT GROUP_ID FROM MI_RULE_INFO_RULESET_GROUP WHERE GROUP_NAME = #{organizationName} AND DESCRIPTION = 'Organization')
		</if>
		UNION ALL
		SELECT CHILD_GROUP.*
		FROM MI_RULE_INFO_RULESET_GROUP CHILD_GROUP
		JOIN GROUP_IDS ON CHILD_GROUP.P_GROUP_ID = GROUP_IDS.GROUP_ID
		)
		SELECT * FROM GROUP_IDS
		<if test="searchText != null and searchText.length() > 0">
			<bind name="searchText" value="'%' + searchText + '%'" />
			WHERE GROUP_NAME LIKE #{searchText} ESCAPE '^'
		</if>
	</select>


	<select id="getRulesetGroupTotalCount" resultType="java.lang.Integer">

		SELECT COUNT(GROUP_ID) FROM MI_RULE_INFO_RULESET_GROUP WHERE P_GROUP_ID != -1

	</select>

	<select id="getParentsGroupList" resultType="com.samsung.magicinfo.restapi.common.model.V2ParentsGroup">
		WITH RECURSIVE GROUP_IDS AS (
		SELECT GROUPS.*
		FROM MI_RULE_INFO_RULESET_GROUP GROUPS
		WHERE GROUP_ID = #{pGroupId}
		UNION ALL
		SELECT PARENT_GROUP.*
		FROM MI_RULE_INFO_RULESET_GROUP PARENT_GROUP
		JOIN GROUP_IDS ON PARENT_GROUP.GROUP_ID = GROUP_IDS.P_GROUP_ID
		)
		SELECT GROUP_IDS.GROUP_ID as groupId,
		GROUP_IDS.P_GROUP_ID as pGroupId,
		GROUP_IDS.GROUP_DEPTH as groupDepth,
		GROUP_IDS.GROUP_NAME as groupName
		FROM GROUP_IDS
		WHERE P_GROUP_ID > -1
		ORDER BY group_depth asc
	</select>

	<select id="getParentsGroupList" resultType="com.samsung.magicinfo.restapi.common.model.V2ParentsGroup" databaseId="mssql">
		WITH GROUP_IDS AS (
		SELECT GROUPS.*
		FROM MI_RULE_INFO_RULESET_GROUP GROUPS
		WHERE GROUP_ID = #{pGroupId}
		UNION ALL
		SELECT PARENT_GROUP.*
		FROM MI_RULE_INFO_RULESET_GROUP PARENT_GROUP
		JOIN GROUP_IDS ON PARENT_GROUP.GROUP_ID = GROUP_IDS.P_GROUP_ID
		)
		SELECT GROUP_IDS.GROUP_ID as groupId,
		GROUP_IDS.P_GROUP_ID as pGroupId,
		GROUP_IDS.GROUP_DEPTH as groupDepth,
		GROUP_IDS.GROUP_NAME as groupName
		FROM GROUP_IDS
		WHERE P_GROUP_ID > -1
		ORDER BY group_depth asc
	</select>


</mapper>
