package com.samsung.magicinfo.webauthor2.model.datalink;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

public class ValueLocation {
  private final String value;
  
  private final ValueLocationType valueLocationType;
  
  @JsonCreator
  public ValueLocation(@JsonProperty("value") String value, @JsonProperty("valueLocationType") ValueLocationType valueLocationType) {
    this.value = value;
    this.valueLocationType = valueLocationType;
  }
  
  public String getValue() {
    return this.value;
  }
  
  public ValueLocationType getValueLocationType() {
    return this.valueLocationType;
  }
}
