package com.samsung.magicinfo.cms.service;

import com.samsung.common.exception.ExceptionCode;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.ContentUtils;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.DeleteContentUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.cms.model.DashboardPlaylistResource;
import com.samsung.magicinfo.cms.model.DeletedPlaylistResource;
import com.samsung.magicinfo.cms.model.PlaylistFilter;
import com.samsung.magicinfo.cms.model.PlaylistItemResource;
import com.samsung.magicinfo.cms.model.PlaylistResource;
import com.samsung.magicinfo.cms.model.SyncPlaylistGroupResource;
import com.samsung.magicinfo.cms.model.SyncPlaylistItemResource;
import com.samsung.magicinfo.cms.model.SyncPlaylistResource;
import com.samsung.magicinfo.framework.common.DAOFactory;
import com.samsung.magicinfo.framework.common.LeftMenuGroupTreeDao;
import com.samsung.magicinfo.framework.content.constants.ContentConstants;
import com.samsung.magicinfo.framework.content.dao.ContentDao;
import com.samsung.magicinfo.framework.content.dao.PlaylistDao;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.entity.PlaylistContent;
import com.samsung.magicinfo.framework.content.entity.PlaylistLog;
import com.samsung.magicinfo.framework.content.entity.SyncPlaylist;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.playlist.manager.common.PlaylistInterface;
import com.samsung.magicinfo.framework.playlist.manager.common.PlaylistLogInterface;
import com.samsung.magicinfo.framework.role.manager.AbilityInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.common.ScheduleInterface;
import com.samsung.magicinfo.framework.setup.entity.ContentTagEntity;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfo;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.protocol.util.TimeUtil;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.Map.Entry;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("PlaylistService")
@Transactional
public class PlaylistServiceImpl implements PlaylistService {
   protected final Log logger = LogFactory.getLog(this.getClass());
   PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl("PREMIUM");
   PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
   ContentInfo cInfo = ContentInfoImpl.getInstance();
   PlaylistLog pLog = new PlaylistLog();
   PlaylistLogInterface logInfo = DAOFactory.getPlaylistLogInfoImpl("PREMIUM");
   PlaylistDao pDao = new PlaylistDao();
   ContentDao contentTreeDao = new ContentDao();
   LeftMenuGroupTreeDao treeDao = new LeftMenuGroupTreeDao();

   public PlaylistServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Playlist Manage Authority')")
   public boolean canReadUnshared() {
      return true;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public ResponseBody getDashboardPlaylistInfo() throws SQLException {
      ResponseBody responsebody = new ResponseBody();

      try {
         UserContainer userContainer = SecurityUtils.getUserContainer();
         DashboardPlaylistResource data = new DashboardPlaylistResource();
         PlaylistDao playlistDao = new PlaylistDao();
         Map condition = new HashMap();
         condition.put("listType", "ALL");
         condition.put("creatorID", SecurityUtils.getLoginUserId());
         int totalCount = playlistDao.getPlaylistListCnt(condition);
         int usedCount = this.playlistInfo.getUsedPlaylistCount(Long.parseLong("0"));
         if (userContainer != null) {
            usedCount = this.playlistInfo.getUsedPlaylistCount(userContainer.getUser().getRoot_group_id());
         }

         data.setTotalCount(totalCount);
         data.setUsedCount(usedCount);
         responsebody.setItems(data);
         responsebody.setStatus("Success");
         return responsebody;
      } catch (Exception var8) {
         this.logger.error("", var8);
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var8.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public ResponseBody listAllPlaylist(int startIndex, int pageSize) throws Exception {
      ResponseBody responsebody = new ResponseBody();

      try {
         PlaylistFilter filter = new PlaylistFilter();
         UserContainer userContainer = SecurityUtils.getUserContainer();
         String groupType = filter.getListType();
         String groupId = filter.getGroupId();
         String sortColumn = filter.getSortColumn();
         String sortOrder = filter.getSortOrder();
         String deviceType = filter.getDeviceType();
         String searchText = filter.getSearchText();
         String endDate = filter.getEndDate();
         String startDate = filter.getStartDate();
         ContentInfo contentDao = ContentInfoImpl.getInstance();
         boolean canEditOthers = true;
         Boolean canReadUnshared = true;
         String userId = SecurityUtils.getLoginUserId();
         Map conditionMap = new HashMap();
         conditionMap.put("deviceType", deviceType);
         conditionMap.put("sortColumn", sortColumn);
         conditionMap.put("sortOrder", sortOrder);
         conditionMap.put("searchText", searchText);
         conditionMap.put("isSelect", (Object)null);
         conditionMap.put("selId", (Object)null);
         conditionMap.put("searchCreator", "");
         conditionMap.put("creatorID", userId);
         conditionMap.put("searchID", "-1");
         conditionMap.put("startDate", startDate);
         conditionMap.put("endDate", endDate);
         conditionMap.put("listType", groupType);

         try {
            canReadUnshared = this.canReadUnshared();
         } catch (AccessDeniedException var27) {
            this.logger.error(var27.getMessage().toString());
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var27.getMessage());
            return responsebody;
         }

         conditionMap.put("canReadUnshared", canReadUnshared);
         if (groupType.equalsIgnoreCase("GROUPED")) {
            conditionMap.put("groupID", groupId);
            conditionMap.put("listType", groupType);
         } else {
            conditionMap.put("listType", groupType);
         }

         List playlistList = this.pDao.getPlaylistListPage(conditionMap, startIndex, pageSize);
         List list = new ArrayList();
         new ArrayList();

         int totalCount;
         for(totalCount = 0; totalCount < playlistList.size(); ++totalCount) {
            Playlist playlist = (Playlist)playlistList.get(totalCount);
            PlaylistResource resource = new PlaylistResource();
            Content content = contentDao.getContentActiveVerInfo(playlist.getContent_id());
            playlist.setMainContent(content);
            if (content != null) {
               resource.setPlaylistId(playlist.getPlaylist_id());
               resource.setPlaylistName(playlist.getPlaylist_name().replaceAll("<", "&lt"));
               resource.setDeviceType(playlist.getDevice_type());
               resource.setDeviceTypeVersion(playlist.getDevice_type_version() + "");
               resource.setContentCount(playlist.getContent_count());
               String[] convertToSeconds = playlist.getPlay_time().split(":");
               if (convertToSeconds.length == 1) {
                  resource.setPlayTime(0L);
               } else {
                  resource.setPlayTime(Long.valueOf(convertToSeconds[0]) * 3600L + Long.valueOf(convertToSeconds[1]) * 60L + Long.valueOf(convertToSeconds[2]));
               }

               resource.setTotalSize(playlist.getTotal_size());
               resource.setLastModifiedDate(playlist.getLast_modified_date().toString());
               resource.setShareFlag(playlist.getShare_flag());
               resource.setGroupName(playlist.getGroup_name());
               resource.setMetaData(playlist.getPlaylist_meta_data());
               resource.setCreatorId(playlist.getCreator_id());
               resource.setPlaylistType(playlist.getPlaylist_type());
               resource.setVersionId(playlist.getVersion_id());
               resource.setThumbFileName(content.getThumb_file_name());
               resource.setThumbFilePath("/servlet/ContentThumbnail?thumb_id=" + content.getThumb_file_id() + "&thumb_filename=" + URLEncoder.encode(content.getThumb_file_name(), "UTF-8") + "_MEDIUM.PNG");
               list.add(resource);
            }
         }

         totalCount = this.pDao.getPlaylistListCnt(conditionMap);
         responsebody.setTotalCount(totalCount);
         responsebody.setItems(list);
         responsebody.setStatus("Success");
         return responsebody;
      } catch (Exception var28) {
         this.logger.error(var28.getMessage());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var28.getMessage());
         return responsebody;
      }
   }

   public boolean isPlaylistManageAuthority(String userId) {
      AbilityInfoImpl abilityInfo = AbilityInfoImpl.getInstance();

      try {
         List abilityList = abilityInfo.getAllAbilityListByUserId(userId);
         Iterator it = abilityList.iterator();

         while(it.hasNext()) {
            Map abilityMap = (Map)it.next();
            String abilityValue = (String)abilityMap.get("ability_name");
            if (abilityValue.equalsIgnoreCase("Playlist Manage Authority")) {
               return true;
            }
         }
      } catch (SQLException var7) {
         var7.printStackTrace();
      }

      return false;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority', 'Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody listPlaylist(PlaylistFilter filter) throws Exception {
      ResponseBody responsebody = new ResponseBody();

      try {
         int startIndex = filter.getStartIndex();
         int pageSize = filter.getPageSize();
         String groupType = filter.getListType();
         String groupId = filter.getGroupId();
         String sortColumn = filter.getSortColumn();
         String sortOrder = filter.getSortOrder();
         String deviceType = filter.getDeviceType();
         String playlistType = filter.getPlaylistType();
         String searchText = filter.getSearchText();
         String endDate = filter.getEndDate();
         String startDate = filter.getStartDate();
         String category = filter.getCategory();
         ContentInfo contentDao = ContentInfoImpl.getInstance();
         UserContainer userContainer = SecurityUtils.getUserContainer();
         boolean canEditOthers = true;
         boolean canReadUnshared = false;
         String userId = SecurityUtils.getLoginUserId();
         Map conditionMap = new HashMap();
         conditionMap.put("device_type", deviceType);
         conditionMap.put("playlist_type", playlistType);
         conditionMap.put("sortColumn", sortColumn);
         conditionMap.put("sortOrder", sortOrder);
         conditionMap.put("searchText", searchText);
         conditionMap.put("isSelect", (Object)null);
         conditionMap.put("selId", (Object)null);
         conditionMap.put("searchCreator", "");
         conditionMap.put("creatorID", userId);
         conditionMap.put("searchID", "-1");
         conditionMap.put("startDate", startDate);
         conditionMap.put("endDate", endDate);
         conditionMap.put("listType", groupType);
         conditionMap.put("category", category);

         try {
            canReadUnshared = this.isPlaylistManageAuthority(userId);
         } catch (AccessDeniedException var33) {
            this.logger.error(var33.getMessage().toString());
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var33.getMessage());
            return responsebody;
         }

         conditionMap.put("canReadUnshared", canReadUnshared);
         if (groupType.equalsIgnoreCase("GROUPED")) {
            conditionMap.put("groupID", groupId);
            conditionMap.put("listType", groupType);
         } else {
            conditionMap.put("groupID", groupId);
            conditionMap.put("listType", groupType);
         }

         List playlistList = this.pDao.getPlaylistListPage(conditionMap, startIndex, pageSize);
         List list = new ArrayList();

         int totalCount;
         for(totalCount = 0; totalCount < playlistList.size(); ++totalCount) {
            Playlist playlist = (Playlist)playlistList.get(totalCount);
            Playlist playlistDetail = this.pInfo.getPlaylistActiveVerInfo(playlist.getPlaylist_id());
            PlaylistResource resource = new PlaylistResource();
            PlaylistDao playlistDao = new PlaylistDao();
            ContentFile thumbnailFile = null;
            if (playlist.getPlaylist_type().equals("5")) {
               List ContentList = this.pInfo.getTagContentListOfPlaylist(playlist.getPlaylist_id(), playlist.getVersion_id());
               new ContentFile();
               if (ContentList != null && ContentList.size() > 0) {
                  resource.setThumbFileName(((Content)ContentList.get(0)).getThumb_file_name());
                  resource.setThumbFilePath("/servlet/ContentThumbnail?thumb_id=" + ((Content)ContentList.get(0)).getThumb_file_id() + "&thumb_filename=" + URLEncoder.encode(((Content)ContentList.get(0)).getThumb_file_name(), "UTF-8") + "_MEDIUM.PNG");
               } else {
                  resource.setThumbFileName("NOIMAGE_THUMBNAIL.PNG");
                  resource.setThumbFilePath("/servlet/ContentThumbnail?thumb_id=NOIMAGE_THUMBNAIL&thumb_filename=NOIMAGE_THUMBNAIL.PNG_MEDIUM.PNG");
               }
            } else {
               thumbnailFile = this.pInfo.getThumbFileInfo(playlist.getPlaylist_id());
               if (thumbnailFile != null) {
                  resource.setThumbFileName(thumbnailFile.getFile_name());
                  resource.setThumbFilePath("/servlet/ContentThumbnail?thumb_id=" + thumbnailFile.getFile_id() + "&thumb_filename=" + URLEncoder.encode(thumbnailFile.getFile_name(), "UTF-8") + "_MEDIUM.PNG");
               } else {
                  Playlist playlistInfo = playlistDao.getPlaylistActiveVerInfo(playlist.getPlaylist_id());
                  if (playlistInfo != null) {
                     Content contentInfo = contentDao.getThumbInfoOfActiveVersion(playlistInfo.getContent_id());
                     if (contentInfo != null) {
                        String playlist_name = contentInfo.getThumb_file_id() + "|" + contentInfo.getThumb_file_name();
                        resource.setThumbFileName(playlist_name);
                     } else {
                        List tmpList = this.pInfo.getContentListOfPlaylist(playlist.getPlaylist_id(), playlist.getVersion_id());
                        if (tmpList != null && tmpList.size() > 0) {
                           Content playlistContent = (Content)tmpList.get(0);
                           if (playlistContent != null) {
                              resource.setThumbFileName(playlistContent.getThumb_file_name());
                              resource.setThumbFilePath("/servlet/ContentThumbnail?thumb_id=" + playlistContent.getThumb_file_id() + "&thumb_filename=" + URLEncoder.encode(playlistContent.getThumb_file_name(), "UTF-8") + "_MEDIUM.PNG");
                           }
                        }
                     }
                  }
               }
            }

            resource.setPlaylistId(playlist.getPlaylist_id());
            resource.setPlaylistName(playlist.getPlaylist_name().replaceAll("<", "&lt"));
            resource.setDeviceType(playlist.getDevice_type());
            resource.setDeviceTypeVersion(playlist.getDevice_type_version() + "");
            resource.setContentCount(playlist.getContent_count());
            String[] convertToSeconds = playlist.getPlay_time().split(":");
            if (convertToSeconds.length == 1) {
               resource.setPlayTime(0L);
            } else {
               resource.setPlayTime(Long.valueOf(convertToSeconds[0]) * 3600L + Long.valueOf(convertToSeconds[1]) * 60L + Long.valueOf(convertToSeconds[2]));
            }

            resource.setTotalSize(playlist.getTotal_size());
            resource.setLastModifiedDate(TimeUtil.getGMTTime(Timestamp.valueOf(playlist.getLast_modified_date().toString())));
            resource.setShareFlag(playlist.getShare_flag());
            resource.setGroupName(playlist.getGroup_name());
            resource.setMetaData(playlist.getPlaylist_meta_data());
            resource.setCreatorId(playlist.getCreator_id());
            resource.setPlaylistType(playlist.getPlaylist_type());
            resource.setVersionId(playlist.getVersion_id());
            resource.setGroupId(playlistDetail.getGroup_id().toString());
            list.add(resource);
         }

         totalCount = this.pDao.getPlaylistListCnt(conditionMap);
         responsebody.setTotalCount(totalCount);
         responsebody.setItems(list);
         responsebody.setStatus("Success");
         return responsebody;
      } catch (Exception var34) {
         this.logger.error(var34.getMessage());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var34.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public ResponseBody getActivePlaylistInfo(String playlistId) throws SQLException {
      ResponseBody responsebody = new ResponseBody();

      try {
         List contentResourceList = new ArrayList();
         PlaylistResource resource = new PlaylistResource();
         Playlist playlist = this.pInfo.getPlaylistActiveVerInfo(playlistId);
         PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
         String plGroupName = this.pInfo.getGroupName(playlist.getGroup_id());
         resource.setPlaylistName(playlist.getPlaylist_name());
         resource.setPlaylistType(playlist.getPlaylist_type());
         resource.setDeviceType(playlist.getDevice_type());
         resource.setDeviceTypeVersion(playlist.getDevice_type_version() + "");
         String[] convertToSeconds = playlist.getPlay_time().split(":");
         if (convertToSeconds.length == 1) {
            resource.setPlayTime(0L);
         } else {
            resource.setPlayTime(Long.valueOf(convertToSeconds[0]) * 3600L + Long.valueOf(convertToSeconds[1]) * 60L + Long.valueOf(convertToSeconds[2]));
         }

         resource.setLastModifiedDate(TimeUtil.getGMTTime(playlist.getLast_modified_date()));
         resource.setVersionId(playlist.getVersion_id());
         resource.setShuffleFlag(playlist.getIs_shuffle());
         resource.setShareFlag(playlist.getShare_flag());
         resource.setGroupId(playlist.getGroup_id().toString());
         resource.setGroupName(plGroupName);
         resource.setMetaData(playlist.getPlaylist_meta_data());
         resource.setCreatorId(playlist.getCreator_id());
         resource.setTotalSize(playlist.getTotal_size());
         CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
         resource.setCategoryList(categoryInfo.getCategoryWithPlaylistId(playlistId));
         List contentList = this.pInfo.getContentListOfPlaylist(playlist.getPlaylist_id(), playlist.getVersion_id());
         List playlistContentList = this.pInfo.getContentList(playlistId, playlist.getVersion_id());
         int syncPlayGroupCount = 0;
         if (playlistContentList.size() != 0) {
            long tmpSyncPlayGroupId = -1L;

            for(int i = 0; i < playlistContentList.size(); ++i) {
               PlaylistContent tmpPlaylistContent = (PlaylistContent)playlistContentList.get(i);
               Content content = this.cInfo.getContentAndFileActiveVerInfo(tmpPlaylistContent.getContent_id());
               Content tmpContent = (Content)contentList.get(i);
               PlaylistItemResource contentResource = new PlaylistItemResource();
               if (content != null) {
                  if (content.getContent_id() != null) {
                     contentResource.setContentId(content.getContent_id());
                  }

                  if (content.getContent_name() != null) {
                     contentResource.setContentName(content.getContent_name());
                  }

                  if (content.getThumb_file_id() != null) {
                     contentResource.setThumbFileId(content.getThumb_file_id());
                  }

                  if (content.getThumb_file_name() != null) {
                     contentResource.setThumbFileName(content.getThumb_file_name());
                  }

                  contentResource.setThumbFilePath("/servlet/ContentThumbnail?thumb_id=" + content.getThumb_file_id() + "&thumb_filename=" + URLEncoder.encode(content.getThumb_file_name(), "UTF-8") + "_MEDIUM.PNG");
                  if (content.getMedia_type() != null) {
                     contentResource.setMediaType(content.getMedia_type());
                  }

                  contentResource.setPlayTime(String.valueOf(tmpContent.getContent_duration()));
                  if (tmpContent.getPlay_time() != null && !tmpContent.getPlay_time().equalsIgnoreCase("")) {
                     contentResource.setHasDefaultPlayTime(true);
                  } else {
                     contentResource.setHasDefaultPlayTime(false);
                  }

                  contentResource.setStartDate(DateUtils.timestamp2StringDate(tmpPlaylistContent.getStart_date()));
                  contentResource.setExpiredDate(DateUtils.timestamp2StringDate(tmpPlaylistContent.getExpired_date()));
               } else {
                  Playlist playlistContent = this.pInfo.getPlaylistActiveVerInfo(tmpPlaylistContent.getContent_id());
                  if (playlistContent != null) {
                     if (playlistContent.getPlaylist_id() != null) {
                        contentResource.setContentId(playlistContent.getPlaylist_id());
                     }

                     if (playlistContent.getPlaylist_name() != null) {
                        contentResource.setContentName(playlistContent.getPlaylist_name());
                     }

                     ContentFile thumbFile = playlistInfo.getThumbFileInfo(playlistContent.getPlaylist_id());
                     if (thumbFile != null) {
                        if (thumbFile.getFile_id() != null) {
                           contentResource.setThumbFileId(thumbFile.getFile_id());
                        }

                        if (thumbFile.getFile_name() != null) {
                           contentResource.setThumbFileName(thumbFile.getFile_name());
                        }

                        contentResource.setThumbFilePath("/servlet/ContentThumbnail?thumb_id=" + thumbFile.getFile_id() + "&thumb_filename=" + URLEncoder.encode(thumbFile.getFile_name(), "UTF-8") + "_MEDIUM.PNG");
                     }

                     if (playlistContent.getPlay_time() != null) {
                        contentResource.setPlayTime(playlistContent.getPlay_time());
                     }

                     contentResource.setSubPlaylist(true);
                  }
               }

               if (playlist.getPlaylist_type() != null && playlist.getPlaylist_type().equalsIgnoreCase("3")) {
                  long syncPlayGroupId = Long.parseLong(tmpPlaylistContent.getSync_play_id());
                  contentResource.setSyncPlayGroupOrder(syncPlayGroupId);
                  if (tmpSyncPlayGroupId != syncPlayGroupId) {
                     tmpSyncPlayGroupId = syncPlayGroupId;
                     ++syncPlayGroupCount;
                  }
               }

               List tagPlaylistContentList = this.pInfo.getTagContentListOfPlaylist(playlist.getPlaylist_id(), playlist.getVersion_id());
               List tagMap = this.pInfo.getContentTag(playlistId, playlist.getVersion_id(), tmpContent.getContent_id(), Integer.parseInt(Long.toString((long)tmpContent.getContent_order())));
               if (tagMap != null && tagMap.size() > 0) {
                  String matchType = (String)((Map)tagMap.get(0)).get("match_type");
                  contentResource.setTagMatchType(matchType);
                  String tagList = "";
                  String tagVal = "";

                  for(int k = 0; k < tagMap.size(); ++k) {
                     if (k > 0) {
                        tagVal = tagVal + ",";
                        tagList = tagList + ",";
                     }

                     tagList = tagList + ((Map)tagMap.get(k)).get("tag_id");
                     tagVal = tagVal + ((Map)tagMap.get(k)).get("tag_value");
                  }

                  contentResource.setTagList(tagList);
                  contentResource.setTagValue(tagVal);
               }

               contentResourceList.add(contentResource);
               resource.setContentCount(playlist.getContent_count());
            }
         } else {
            PlaylistItemResource contentResource = new PlaylistItemResource();
            contentResource.setThumbFilePath("/servlet/ContentThumbnail?thumb_id=NOIMAGE_THUMBNAIL&thumb_filename=NOIMAGE_THUMBNAIL.PNG_MEDIUM.PNG");
            contentResourceList.add(contentResource);
         }

         resource.setSyncPlayGroupCount(syncPlayGroupCount);
         resource.setContentList(contentResourceList);
         responsebody.setItems(resource);
         responsebody.setStatus("Success");
         return responsebody;
      } catch (Exception var26) {
         this.logger.error(var26.getMessage());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var26.getMessage());
         return responsebody;
      }
   }

   private boolean isValidContent(String deviceType, String deviceTypeVersion, Content content) throws SQLException {
      if ("WPLAYER".equals(deviceType)) {
         return this.isWplayerSupportMediaType(content);
      } else {
         Long playlistPriority = CommonUtils.getMinPriorityByDeviceinfo(deviceType, deviceTypeVersion);
         Long contentPriority = this.getContentPriority(content);
         return contentPriority <= playlistPriority;
      }
   }

   private Long getContentPriority(Content content) throws SQLException {
      String deviceType = null;
      String deviceTypeVersion = null;
      if (content.getDevice_type() != null && !content.getDevice_type().equals("")) {
         deviceType = content.getDevice_type();
         deviceTypeVersion = content.getDevice_type_version() + "";
      } else {
         Map typeMap = ContentUtils.getContentDeviceTypeAndVersion(content);
         deviceType = typeMap.get("deviceType").toString();
         deviceTypeVersion = typeMap.get("deviceTypeVersion").toString();
      }

      return CommonUtils.getMinPriorityByDeviceinfo(deviceType, deviceTypeVersion);
   }

   private boolean isWplayerSupportMediaType(Content content) throws SQLException {
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      List WPLAYER_SUPPORT_FILE_TYPE_LIST = cInfo.getFileTypeByDeviceTypeAndVersion("WPLAYER", 1.0F);
      String fileType = content.getMain_file_Extension();
      String deviceType = content.getDevice_type();
      if (fileType != null && !fileType.equals("")) {
         if (fileType.equalsIgnoreCase("LFD") || fileType.equalsIgnoreCase("DLK") || fileType.equalsIgnoreCase("LFT")) {
            return deviceType.equalsIgnoreCase("WPLAYER");
         }

         if (WPLAYER_SUPPORT_FILE_TYPE_LIST.contains(fileType)) {
            return true;
         }
      } else {
         String mediaType = content.getMedia_type();
         if (mediaType.equalsIgnoreCase("TLFD") && deviceType.equalsIgnoreCase("WPLAYER")) {
            return true;
         }
      }

      return false;
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public ResponseBody createPlaylist(PlaylistResource params) throws SQLException {
      ResponseBody responsebody = new ResponseBody();

      try {
         String playlistId = UUID.randomUUID().toString();
         String userId = SecurityUtils.getLoginUserId();
         List contentList = new ArrayList();
         boolean addSubPlaylist = false;
         Long totalPlayTime = 0L;
         Map totalSizeMapByContentId = new HashMap();
         List playlistItemList = params.getContentList();
         ArrayList tagArrayList = new ArrayList();
         String productType;
         if (playlistItemList != null && playlistItemList.size() > 0) {
            for(int i = 0; i < playlistItemList.size(); ++i) {
               PlaylistContent pContent = new PlaylistContent();
               PlaylistItemResource playlistItem = (PlaylistItemResource)playlistItemList.get(i);
               Content content = this.cInfo.getContentActiveVerInfo(playlistItem.getContentId());
               if (content == null) {
                  Playlist subPlaylist = this.playlistInfo.getPlaylistActiveVerInfo(playlistItem.getContentId());
                  if (subPlaylist != null) {
                     pContent = new PlaylistContent();
                     pContent.setContent_id(playlistItem.getContentId());
                     pContent.setIs_sub_playlist(true);
                     pContent.setContent_id(playlistItem.getContentId());
                     pContent.setSync_play_id("0");
                     pContent.setContent_order(new Long((long)(i + 1)));
                     pContent.setContent_duration((Long)null);
                     pContent.setExpired_date((Timestamp)null);
                     pContent.setExpired_time((String)null);
                     pContent.setStart_date((Timestamp)null);
                     pContent.setStart_time((String)null);
                     pContent.setContiguous(false);
                     contentList.add(pContent);
                     addSubPlaylist = true;
                  }
               } else {
                  productType = "PREMIUM";
                  if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"}) && playlistItem.getTagList() != null && !playlistItem.getTagList().equals("")) {
                     ContentTagEntity tag = new ContentTagEntity();
                     tag.setContent_id(playlistItem.getContentId());
                     tag.setContent_order(i + 1);
                     tag.setMatch_type(playlistItem.getTagMatchType());
                     ArrayList tagIdList = new ArrayList();
                     String[] tempTagArr = null;
                     tempTagArr = playlistItem.getTagList().split(",");

                     for(int k = 0; k < tempTagArr.length; ++k) {
                        tagIdList.add(Integer.parseInt(tempTagArr[k]));
                     }

                     tag.setTag_id_list(tagIdList);
                     tagArrayList.add(tag);
                  }

                  if (!this.isValidContent(params.getDeviceType(), params.getDeviceTypeVersion(), content)) {
                     responsebody.setStatus("Fail");
                     responsebody.setErrorCode(ExceptionCode.RES911[0]);
                     responsebody.setErrorMessage(ExceptionCode.RES911[2]);
                     return responsebody;
                  }

                  totalSizeMapByContentId.put(content.getContent_id(), content.getTotal_size());
                  pContent.setContent_id(content.getContent_id());
                  pContent.setPlaylist_id(playlistId);
                  pContent.setContent_order(new Long((long)(i + 1)));
                  pContent.setSync_play_id("0");
                  pContent.setContent_duration(Long.valueOf(playlistItem.getPlayTime()));
                  if (playlistItem.getExpiredDate() != null) {
                     pContent.setExpired_date(DateUtils.string2Timestamp(playlistItem.getExpiredDate(), "yyyy-MM-dd"));
                  }

                  if (playlistItem.getStartDate() != null) {
                     pContent.setStart_date(DateUtils.string2Timestamp(playlistItem.getStartDate(), "yyyy-MM-dd"));
                  }

                  pContent.setGender(playlistItem.getGender());
                  pContent.setAge(playlistItem.getAge());
                  pContent.setAms_recog_type(playlistItem.getAmsRecogType());
                  pContent.setRandom_count(playlistItem.getRandomCount());
                  pContent.setStart_time(playlistItem.getStartTime());
                  pContent.setExpired_time(playlistItem.getExpiredTime());
                  pContent.setRepeat_type(playlistItem.getRepeatType());
                  pContent.setPlay_weight(playlistItem.getPlayWeight());
                  pContent.setIs_independent_play(playlistItem.getIsIndependentPlay());
                  pContent.setContiguous(playlistItem.isContiguous());
                  if (pContent.getContent_duration() != null) {
                     totalPlayTime = totalPlayTime + pContent.getContent_duration();
                  }

                  contentList.add(pContent);
               }
            }
         }

         Long totalSize = 0L;
         Iterator it = totalSizeMapByContentId.entrySet().iterator();

         while(it.hasNext()) {
            Entry entry = (Entry)it.next();
            if (entry.getValue() != null) {
               totalSize = totalSize + (Long)entry.getValue();
            }
         }

         User user = SecurityUtils.getLoginUser();
         Playlist pl = new Playlist();
         pl.setOrganization_id(user.getRoot_group_id());
         pl.setPlaylist_id(playlistId);
         pl.setPlaylist_name(params.getPlaylistName());
         pl.setPlaylist_meta_data(params.getMetaData());
         pl.setDevice_type(params.getDeviceType());
         pl.setDevice_type_version(Float.parseFloat(params.getDeviceTypeVersion()));
         pl.setTotal_size(totalSize);
         pl.setPlay_time(ContentUtils.getPlayTimeFormattedStr(totalPlayTime));
         productType = params.getGroupId();
         if (productType != null && productType.length() != 0) {
            pl.setGroup_id(new Long(productType));
         } else {
            pl.setGroup_id(this.pInfo.getRootId(userId));
         }

         pl.setCreator_id(userId);
         pl.setShare_flag(params.getShareFlag());
         pl.setIs_shuffle(params.getShuffleFlag());
         pl.setPlaylist_type(params.getPlaylistType());
         pl.setAms_mode("");
         pl.setAms_direct_play(false);
         pl.setIs_vwl("N");
         pl.setArr_content_list(contentList);
         pl.setTagList(tagArrayList);
         if (addSubPlaylist) {
            pl.setHas_sub_playlist(true);
         } else {
            pl.setHas_sub_playlist(false);
         }

         if (pl.getGroup_id() == 0L) {
            pl.setGroup_id(this.pInfo.getRootId(userId));
         }

         pl.setContent_count(contentList.size());
         if (this.pInfo.addPlaylist(pl) > 0) {
         }

         params.setPlaylistId(playlistId);
         responsebody.setItems(params);
         responsebody.setStatus("Success");
         return responsebody;
      } catch (Exception var20) {
         this.logger.error("", var20);
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var20.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public ResponseBody editPlaylist(String playlistId, PlaylistResource params) throws SQLException {
      ResponseBody responsebody = new ResponseBody();

      try {
         new LinkedHashMap();
         String productType = "PREMIUM";
         String playlistType = "0";
         String sessionId = UUID.randomUUID().toString();
         Long totalPlayTime = 0L;
         Long totalSize = 0L;
         boolean amsDirect = true;
         boolean isChanged = false;
         boolean addSubPlaylist = false;
         Map totalSizeMapByContentId = new HashMap();
         List contentList = new ArrayList();
         Long curActiveVersion = this.pInfo.getPlaylistActiveVersionId(playlistId);
         Playlist playlist = this.pInfo.getPlaylistVerInfo(playlistId, curActiveVersion);
         List playlistContentList = this.pInfo.getContentList(playlistId, curActiveVersion);
         List playlistItemList = params.getContentList();
         ArrayList tagArrayList = new ArrayList();
         if (playlistItemList != null && playlistItemList.size() > 0) {
            for(int i = 0; i < playlistItemList.size(); ++i) {
               PlaylistContent pContent = new PlaylistContent();
               PlaylistItemResource playlistItem = (PlaylistItemResource)playlistItemList.get(i);
               Content content = this.cInfo.getContentActiveVerInfo(playlistItem.getContentId());
               if (content == null) {
                  Playlist subPlaylist = this.playlistInfo.getPlaylistActiveVerInfo(playlistItem.getContentId());
                  if (subPlaylist != null) {
                     pContent = new PlaylistContent();
                     pContent.setContent_id(playlistItem.getContentId());
                     pContent.setIs_sub_playlist(true);
                     pContent.setContent_id(playlistItem.getContentId());
                     pContent.setSync_play_id("0");
                     pContent.setContent_order(new Long((long)(i + 1)));
                     pContent.setContent_duration((Long)null);
                     pContent.setExpired_date((Timestamp)null);
                     pContent.setExpired_time((String)null);
                     pContent.setStart_date((Timestamp)null);
                     pContent.setStart_time((String)null);
                     pContent.setContiguous(false);
                     contentList.add(pContent);
                     addSubPlaylist = true;
                  }
               } else {
                  if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"}) && playlistItem.getTagList() != null && !playlistItem.getTagList().equals("")) {
                     ContentTagEntity tag = new ContentTagEntity();
                     tag.setContent_id(playlistItem.getContentId());
                     tag.setContent_order(i + 1);
                     tag.setMatch_type(playlistItem.getTagMatchType());
                     ArrayList tagIdList = new ArrayList();
                     String[] tempTagArr = null;
                     tempTagArr = playlistItem.getTagList().split(",");

                     for(int k = 0; k < tempTagArr.length; ++k) {
                        tagIdList.add(Integer.parseInt(tempTagArr[k]));
                     }

                     tag.setTag_id_list(tagIdList);
                     tagArrayList.add(tag);
                  }

                  if (!this.isValidContent(params.getDeviceType(), params.getDeviceTypeVersion(), content)) {
                     responsebody.setStatus("Fail");
                     responsebody.setErrorCode(ExceptionCode.RES911[0]);
                     responsebody.setErrorMessage(ExceptionCode.RES911[2]);
                     return responsebody;
                  }

                  totalSizeMapByContentId.put(content.getContent_id(), content.getTotal_size());
                  pContent.setContent_id(content.getContent_id());
                  pContent.setPlaylist_id(playlistId);
                  pContent.setContent_order(new Long((long)(i + 1)));
                  pContent.setSync_play_id("0");
                  pContent.setContent_duration(Long.valueOf(playlistItem.getPlayTime()));
                  pContent.setContent_duration_milli(content.getPlay_time_milli());
                  if (playlistItem.getExpiredDate() != null && !playlistItem.getExpiredDate().equals("")) {
                     pContent.setExpired_date(DateUtils.string2Timestamp(playlistItem.getExpiredDate(), "yyyy-MM-dd"));
                  }

                  if (playlistItem.getStartDate() != null && !playlistItem.getStartDate().equals("")) {
                     pContent.setStart_date(DateUtils.string2Timestamp(playlistItem.getStartDate(), "yyyy-MM-dd"));
                  }

                  if (playlistContentList.size() > i) {
                     PlaylistContent playlistContent = (PlaylistContent)playlistContentList.get(i);
                     pContent.setEffect_in_name(playlistContent.getEffect_in_name());
                     pContent.setEffect_in_duration(playlistContent.getEffect_in_duration());
                     pContent.setEffect_in_direction(playlistContent.getEffect_in_direction());
                     pContent.setEffect_out_name(playlistContent.getEffect_out_name());
                     pContent.setEffect_out_duration(playlistContent.getEffect_out_duration());
                     pContent.setEffect_out_direction(playlistContent.getEffect_out_direction());
                     pContent.setEffect_in_delay_duration(playlistContent.getEffect_in_delay_duration());
                     pContent.setEffect_in_delay_direction(playlistContent.getEffect_in_delay_direction());
                     pContent.setEffect_in_delay_div(playlistContent.getEffect_in_delay_div());
                     pContent.setEffect_out_delay_duration(playlistContent.getEffect_out_delay_duration());
                     pContent.setEffect_out_delay_direction(playlistContent.getEffect_out_delay_direction());
                     pContent.setEffect_out_delay_div(playlistContent.getEffect_out_delay_div());
                  }

                  pContent.setGender(playlistItem.getGender());
                  pContent.setAge(playlistItem.getAge());
                  pContent.setAms_recog_type(playlistItem.getAmsRecogType());
                  pContent.setRandom_count(playlistItem.getRandomCount());
                  pContent.setStart_time(playlistItem.getStartTime());
                  pContent.setExpired_time(playlistItem.getExpiredTime());
                  pContent.setRepeat_type(playlistItem.getRepeatType());
                  pContent.setPlay_weight(playlistItem.getPlayWeight());
                  pContent.setIs_independent_play(playlistItem.getIsIndependentPlay());
                  pContent.setContiguous(playlistItem.isContiguous());
                  if (pContent.getContent_duration() != null) {
                     totalPlayTime = totalPlayTime + pContent.getContent_duration();
                  }

                  contentList.add(pContent);
               }
            }
         }

         Iterator it = totalSizeMapByContentId.entrySet().iterator();

         while(it.hasNext()) {
            Entry entry = (Entry)it.next();
            if (entry.getValue() != null) {
               totalSize = totalSize + (Long)entry.getValue();
            }
         }

         new Playlist();
         String playlistName = params.getPlaylistName();
         String metaData = params.getMetaData();
         int shareFlag = params.getShareFlag();
         playlist.setPlaylist_name(playlistName);
         playlist.setPlaylist_meta_data(params.getMetaData());
         playlist.setTotal_size(totalSize);
         playlist.setPlay_time(ContentUtils.getPlayTimeFormattedStr(totalPlayTime));
         String groupId = params.getGroupId();
         if (groupId != null && groupId.length() != 0) {
            playlist.setGroup_id(new Long(groupId));
         } else {
            playlist.setGroup_id(this.pInfo.getRootId(SecurityUtils.getLoginUserId()));
         }

         playlist.setCreator_id(playlist.getCreator_id());
         playlist.setShare_flag(shareFlag);
         playlist.setArr_content_list(contentList);
         playlist.setIs_shuffle(params.getShuffleFlag());
         playlist.setContent_count(contentList.size());
         playlist.setAms_mode(params.getAmsMode());
         playlist.setAms_direct_play(params.getAmsDirectPlay());
         playlist.setPlaylist_type(playlistType);
         playlist.setTagList(tagArrayList);
         if (addSubPlaylist) {
            playlist.setHas_sub_playlist(true);
         } else {
            playlist.setHas_sub_playlist(false);
         }

         if (this.pInfo.addPlaylist(playlist) > 0) {
         }

         boolean ret = false;
         if (playlistId != null) {
            if ((playlistName.length() > 0 || metaData.length() > 0) && this.pInfo.setPlaylistInfo(playlistId, playlistName, metaData, shareFlag) > 0) {
               ret = true;
            }

            if (groupId.length() > 0 && this.pInfo.setPlaylistGroup(playlistId, new Long(groupId)) > 0) {
               ret = true;
            }
         }

         this.cInfo.setContentUnlockBySessionID(sessionId);
         params.setPlaylistId(playlistId);
         ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
         scheduleInfo.setPlaylistTrigger(playlistId);
         EventInfo eInfo = EventInfoImpl.getInstance();
         eInfo.setPlaylistTrigger(playlistId);
         responsebody.setItems(params);
         responsebody.setStatus("Success");
         return responsebody;
      } catch (Exception var29) {
         this.logger.error("", var29);
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var29.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public ResponseBody deletePlaylist(String playlistId, Boolean force) throws SQLException {
      ResponseBody responsebody = new ResponseBody();

      try {
         DeletedPlaylistResource result = new DeletedPlaylistResource();
         String sessionId = UUID.randomUUID().toString();
         String userId = SecurityUtils.getLoginUserId();
         String[] aval = null;
         if (playlistId.length() > 0) {
            aval = playlistId.split(",");
         }

         int cnt = 0;
         ArrayList cannotDelPlaylistList = new ArrayList();
         ArrayList contentNameList = new ArrayList();
         ArrayList refScheduleList = new ArrayList();
         if (aval != null) {
            int i = 0;
            if (aval != null && i < aval.length) {
               if (this.pInfo.isDeletablePlaylist(aval[i], userId, sessionId)) {
                  if (this.pInfo.deletePlaylist(aval[i], userId, sessionId) > 0) {
                  }
               } else if (force != null && force) {
                  DeleteContentUtils.checkPlaylistFromSchedule(aval[i], SecurityUtils.getLoginUser().getUserIP());
                  if (this.pInfo.deletePlaylist(aval[i], userId, sessionId) > 0) {
                  }
               } else {
                  Map tmpmap = new HashMap();
                  contentNameList.add(StrUtils.cutCharLen(this.pInfo.getPlaylistName(aval[i]), 25));
                  cannotDelPlaylistList.add(tmpmap);
                  this.setRefScheduleList(aval[i], refScheduleList, "PREMIUM");
                  ++cnt;
               }

               if (cnt > 0) {
                  result.setContentNameList(contentNameList);
                  result.setRefScheduleList(refScheduleList);
                  result.setStatus("undelete");
               } else {
                  result.setStatus("success");
               }

               responsebody.setItems(result);
               responsebody.setStatus("Success");
               return responsebody;
            }
         }
      } catch (Exception var14) {
         this.logger.error("", var14);
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var14.getMessage());
         return responsebody;
      }

      responsebody.setStatus("Success");
      return responsebody;
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public ResponseBody permanentlyDeletePlaylist(String playlistId, Boolean force) throws SQLException {
      ResponseBody responsebody = new ResponseBody();

      try {
         LinkedHashMap result = new LinkedHashMap();
         String sessionId = UUID.randomUUID().toString();
         String userId = SecurityUtils.getLoginUserId();
         int nCannotDelPlaylist = false;
         new ArrayList();
         ArrayList refScheduleList = new ArrayList();
         if (this.pInfo.isDeletablePlaylist(playlistId, userId, sessionId)) {
            this.pInfo.deletePlaylist(playlistId, userId, sessionId);
            if (this.pInfo.deletePlaylistCompletely(playlistId) <= 0) {
            }

            result.put("status", "success");
            responsebody.setItems(result);
         } else if (force != null && force) {
            DeleteContentUtils.checkPlaylistFromSchedule(playlistId, SecurityUtils.getLoginUser().getUserIP());
            this.pInfo.deletePlaylist(playlistId, userId, sessionId);
            if (this.pInfo.deletePlaylistCompletely(playlistId) <= 0) {
            }

            result.put("status", "success");
            responsebody.setItems(result);
         } else {
            this.setRefScheduleList(playlistId, refScheduleList, "PREMIUM");
            result.put("refScheduleList", refScheduleList);
            result.put("status", "undelete");
            responsebody.setItems(result);
         }

         responsebody.setStatus("Success");
         return responsebody;
      } catch (Exception var10) {
         this.logger.error("", var10);
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var10.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public ResponseBody copyPlaylist(String playlistId, PlaylistResource params) throws SQLException {
      ResponseBody responsebody = new ResponseBody();

      try {
         String newPlaylistId = UUID.randomUUID().toString();
         String playlistName = params.getPlaylistName();
         String groupId = params.getGroupId();
         int shareFlag = params.getShareFlag();
         String metaData = params.getMetaData();
         String userId = SecurityUtils.getLoginUserId();
         Long curActiveVersion = this.pInfo.getPlaylistActiveVersionId(playlistId);
         List tagArrayList = this.pInfo.getPlaylistContentTagEntityList(playlistId, curActiveVersion);
         Playlist playlist = this.pInfo.getPlaylistVerInfo(playlistId, curActiveVersion);
         List pList = this.pInfo.getContentList(playlistId, curActiveVersion);
         boolean ret = false;
         if (playlist == null) {
            responsebody.setStatus("Fail");
            responsebody.setErrorCode(ExceptionCode.RES908[0]);
            responsebody.setErrorMessage(ExceptionCode.RES908[2]);
            return responsebody;
         } else {
            playlist.setPlaylist_id(newPlaylistId);
            playlist.setPlaylist_name(playlistName);
            playlist.setGroup_id(Long.valueOf(groupId));
            playlist.setTagList(tagArrayList);
            playlist.setPlaylist_meta_data(metaData);
            playlist.setShare_flag(shareFlag);
            playlist.setVersion_id(1L);
            if (groupId != null && groupId.length() != 0) {
               playlist.setGroup_id(new Long(groupId));
            } else {
               playlist.setGroup_id(this.pInfo.getRootId(userId));
            }

            playlist.setCreator_id(userId);
            playlist.setArr_content_list(pList);
            if (playlist.getPlaylist_type().equals("3")) {
               List syncPlaylistList = this.pInfo.getSyncGroupInfo(playlistId, curActiveVersion);

               for(int i = 0; i < syncPlaylistList.size(); ++i) {
                  ((SyncPlaylist)syncPlaylistList.get(i)).setPlaylist_id(newPlaylistId);
                  ((SyncPlaylist)syncPlaylistList.get(i)).setVersion_id(1L);
               }

               playlist.setSync_status_list(syncPlaylistList);
            }

            if (this.pInfo.addPlaylist(playlist) > 0) {
               params.setPlaylistId(newPlaylistId);
               responsebody.setItems(params);
               responsebody.setStatus("Success");
               return responsebody;
            } else {
               responsebody.setErrorCode(ExceptionCode.RES912[0]);
               responsebody.setErrorMessage(ExceptionCode.RES912[2]);
               responsebody.setStatus("Fail");
               return responsebody;
            }
         }
      } catch (Exception var17) {
         this.logger.error("", var17);
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var17.getMessage());
         return responsebody;
      }
   }

   public void setRefScheduleList(String playlistId, ArrayList refScheduleList, String productType) {
      ScheduleInterface sInfo = DAOFactory.getScheduleInfoImpl(productType);

      try {
         List programList = sInfo.getProgramByPlaylistId(playlistId);
         if (programList != null && programList.size() != 0) {
            String programName = "";
            Map map = null;
            List scheduleList = new ArrayList();
            String scheduleId = "";
            String scheduleName = "";

            for(int j = 0; j < programList.size(); ++j) {
               Map tmpmap = new HashMap();
               map = (Map)programList.get(j);
               scheduleId = (String)map.get("program_id");
               scheduleName = (String)map.get("program_name");
               tmpmap.put("scheduleId", scheduleId);
               tmpmap.put("scheduleName", scheduleName);
               scheduleList.add(tmpmap);
            }

            refScheduleList.add(scheduleList);
         } else {
            refScheduleList.add((Object)null);
         }
      } catch (SQLException var13) {
         this.logger.error("", var13);
      }

   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public ResponseBody createSyncPlaylist(SyncPlaylistResource params) throws SQLException {
      ResponseBody responsebody = new ResponseBody();

      try {
         String playlistId = UUID.randomUUID().toString();
         String groupId = StrUtils.nvl(params.getGroupId()).equals("") ? "0" : params.getGroupId();
         String playlistName = StrUtils.nvl(params.getPlaylistName()).equals("") ? "NO_TITLE" : params.getPlaylistName();
         String shareFlag = params.getShareFlag() == 1 ? "1" : "0";
         String deviceType = params.getDeviceType();
         String deviceTypeVersion = params.getDeviceTypeVersion();
         String metaData = StrUtils.nvl(params.getMetaData()).equals("") ? "" : params.getMetaData();
         String productType = params.getProductType();
         long defaultContentDuration = params.getDefaultContentDuration();
         int contentCountInEachGroup = params.getContentCountInEachGroup();
         String playlistType = "3";
         List contentGroups = params.getContentGroups();
         Long targetDevicePriority = CommonUtils.getMinPriorityByDeviceinfo(deviceType, deviceTypeVersion);
         Long syncPlaylistPriority = CommonUtils.getMinPriorityByDeviceinfo("SPLAYER", "3.0");
         if (syncPlaylistPriority > targetDevicePriority) {
            responsebody.setStatus("Fail");
            responsebody.setErrorCode(ExceptionCode.RES913[0]);
            responsebody.setErrorMessage(ExceptionCode.RES913[2]);
            return responsebody;
         } else if (contentGroups != null && contentGroups.size() > 0) {
            int contentGroupCount = contentGroups.size();

            int i;
            SyncPlaylistGroupResource contentGroup;
            int j;
            for(i = 0; i < contentGroupCount; ++i) {
               contentGroup = (SyncPlaylistGroupResource)contentGroups.get(i);
               j = contentGroup.getContents().size();
               if (j != contentCountInEachGroup) {
                  responsebody.setStatus("Fail");
                  responsebody.setErrorCode(ExceptionCode.RES914[0]);
                  responsebody.setErrorMessage(ExceptionCode.RES914[2]);
                  return responsebody;
               }
            }

            SyncPlaylistItemResource content;
            long contentOrder1;
            for(i = 0; i < contentGroupCount; ++i) {
               contentGroup = (SyncPlaylistGroupResource)contentGroups.get(i);

               for(j = 0; j < contentCountInEachGroup; ++j) {
                  content = (SyncPlaylistItemResource)contentGroup.getContents().get(j);
                  contentOrder1 = content.getContentOrder();

                  for(int k = 0; k < contentCountInEachGroup; ++k) {
                     if (k != j) {
                        SyncPlaylistItemResource itemResource2 = (SyncPlaylistItemResource)contentGroup.getContents().get(k);
                        long contentOrder2 = itemResource2.getContentOrder();
                        if (contentOrder1 == contentOrder2) {
                           responsebody.setStatus("Fail");
                           responsebody.setErrorCode(ExceptionCode.RES918[0]);
                           responsebody.setErrorMessage(ExceptionCode.RES918[2]);
                           return responsebody;
                        }
                     }
                  }
               }
            }

            int i;
            SyncPlaylistGroupResource contentsGroup;
            for(i = 0; i < contentCountInEachGroup; ++i) {
               for(i = 0; i < contentGroupCount; ++i) {
                  contentsGroup = (SyncPlaylistGroupResource)contentGroups.get(i);
                  content = (SyncPlaylistItemResource)contentsGroup.getContents().get(i);
                  contentOrder1 = ContentUtils.getPlayTimeStr(content.getContentDuration());
                  if (contentOrder1 <= 0L) {
                     responsebody.setStatus("Fail");
                     responsebody.setErrorCode(ExceptionCode.RES915[0]);
                     responsebody.setErrorMessage(ExceptionCode.RES915[2]);
                     return responsebody;
                  }
               }
            }

            List contentIds = new ArrayList();
            List contentOrders = new ArrayList();

            for(i = 0; i < contentGroups.size(); ++i) {
               contentsGroup = (SyncPlaylistGroupResource)contentGroups.get(i);

               for(int j = 0; j < contentCountInEachGroup; ++j) {
                  SyncPlaylistItemResource content = (SyncPlaylistItemResource)contentsGroup.getContents().get(j);
                  String contentId = content.getContentId();
                  contentIds.add(contentId);
                  contentOrders.add(String.valueOf(j + 1));
               }
            }

            ContentInfo cmsDao = ContentInfoImpl.getInstance();
            List content_list = new ArrayList();
            boolean addSubPlaylist = false;
            Long totalPlayTime = 0L;
            Map syncDurationMap = new HashMap();
            Map contentsMap = new HashMap();
            Map totalSizeMapByContentId = new HashMap();
            if (contentIds.size() > 0) {
               SyncPlaylistGroupResource contentGroup;
               int contentCount;
               int j;
               SyncPlaylistItemResource paramContent;
               Content content;
               int i;
               for(i = 0; i < contentGroups.size(); ++i) {
                  contentGroup = (SyncPlaylistGroupResource)contentGroups.get(i);
                  contentCount = contentGroup.getContents().size();

                  for(j = 0; j < contentCount; ++j) {
                     paramContent = (SyncPlaylistItemResource)contentGroup.getContents().get(j);
                     String contentId = paramContent.getContentId();
                     content = cmsDao.getContentActiveVerInfo(contentId);
                     if (content == null) {
                        responsebody.setStatus("Fail");
                        responsebody.setErrorCode(ExceptionCode.RES917[0]);
                        responsebody.setErrorMessage(ExceptionCode.RES917[2]);
                        return responsebody;
                     }

                     contentsMap.put(contentId, content);
                  }
               }

               for(i = 0; i < contentCountInEachGroup; ++i) {
                  long minContentDuration = 0L;

                  String syncStatus;
                  for(j = 0; j < contentGroups.size(); ++j) {
                     syncStatus = ((SyncPlaylistGroupResource)contentGroups.get(j)).getSyncStatus();
                     if (syncStatus.equalsIgnoreCase("Y")) {
                        SyncPlaylistGroupResource splContentsGroup = (SyncPlaylistGroupResource)contentGroups.get(j);
                        SyncPlaylistItemResource splContent = (SyncPlaylistItemResource)splContentsGroup.getContents().get(i);
                        Content content = (Content)contentsMap.get(splContent.getContentId());
                        long requestedContentDuration = ContentUtils.getPlayTimeStr(splContent.getContentDuration());
                        long originalContentDuration = ContentUtils.getPlayTimeStr(content.getPlay_time());
                        if (minContentDuration == 0L || minContentDuration > requestedContentDuration) {
                           minContentDuration = requestedContentDuration;
                        }

                        if (minContentDuration > originalContentDuration) {
                           minContentDuration = originalContentDuration;
                        }
                     }
                  }

                  String contentOrderInGroup = String.valueOf(i + 1);
                  syncStatus = String.valueOf(minContentDuration);
                  syncDurationMap.put(contentOrderInGroup, syncStatus);
               }

               for(i = 0; i < contentGroups.size(); ++i) {
                  contentGroup = (SyncPlaylistGroupResource)contentGroups.get(i);
                  contentCount = contentGroup.getContents().size();

                  for(j = 0; j < contentCount; ++j) {
                     paramContent = (SyncPlaylistItemResource)contentGroup.getContents().get(j);
                     PlaylistContent pContent = new PlaylistContent();
                     content = (Content)contentsMap.get(paramContent.getContentId());
                     if (content != null) {
                        if (!this.isValidContent(params.getDeviceType(), params.getDeviceTypeVersion(), content)) {
                           responsebody.setStatus("Fail");
                           responsebody.setErrorCode(ExceptionCode.RES911[0]);
                           responsebody.setErrorMessage(ExceptionCode.RES911[2]);
                           return responsebody;
                        }

                        totalSizeMapByContentId.put(content.getContent_id(), content.getTotal_size());
                        pContent.setContent_id(content.getContent_id());
                        pContent.setPlaylist_id(playlistId);
                        pContent.setSync_play_id(String.valueOf(i));
                        long contentOrder = paramContent.getContentOrder();
                        pContent.setContent_order(contentOrder);
                        content.setContent_duration((long)DateUtils.changeFormatTimeToSecond(paramContent.getContentDuration()));
                        Long content_duration = this.getContentDuration(content, (int)contentOrder, (String)null, (List)null, productType, deviceType);
                        pContent.setContent_duration(content_duration);
                        String syncStatus = contentGroup.getSyncStatus();
                        if (syncStatus != null && !syncStatus.equals("") && syncStatus.equals("Y") && syncDurationMap.containsKey(String.valueOf(pContent.getContent_order()))) {
                           String time = syncDurationMap.get(String.valueOf(pContent.getContent_order())).toString();
                           String[] timeArr = time.split(":");
                           if (totalPlayTime == 0L) {
                              for(int d = 1; d <= syncDurationMap.size(); ++d) {
                                 totalPlayTime = totalPlayTime + Long.valueOf((String)syncDurationMap.get(String.valueOf(d)));
                              }
                           }

                           if (timeArr.length == 1) {
                              pContent.setContent_duration(Long.valueOf(timeArr[0]));
                              pContent.setContent_duration_milli("");
                           } else {
                              pContent.setContent_duration(Long.valueOf(timeArr[0]));
                              pContent.setContent_duration_milli(timeArr[1]);
                           }
                        }

                        content_list.add(pContent);
                     } else {
                        Playlist playlist = this.playlistInfo.getPlaylistActiveVerInfo(paramContent.getContentId());
                        if (playlist != null) {
                           totalSizeMapByContentId.put(playlist.getPlaylist_id(), playlist.getTotal_size());
                           PlaylistContent subPlaylist = new PlaylistContent();
                           subPlaylist.setContent_duration((Long)null);
                           subPlaylist.setExpired_date((Timestamp)null);
                           subPlaylist.setExpired_time((String)null);
                           subPlaylist.setStart_date((Timestamp)null);
                           subPlaylist.setStart_time((String)null);
                           subPlaylist.setContiguous(false);
                           subPlaylist.setIs_sub_playlist(true);
                           subPlaylist.setContent_id(paramContent.getContentId());
                           subPlaylist.setSync_play_id("0");
                           subPlaylist.setContent_order(new Long((long)(i + 1)));
                           content_list.add(subPlaylist);
                           addSubPlaylist = true;
                        }
                     }
                  }
               }
            }

            Long totalSize = 0L;
            Iterator it = totalSizeMapByContentId.entrySet().iterator();

            while(it.hasNext()) {
               Entry entry = (Entry)it.next();
               if (entry.getValue() != null) {
                  totalSize = totalSize + (Long)entry.getValue();
               }
            }

            Playlist pl = new Playlist();
            pl.setPlaylist_id(playlistId);
            pl.setPlaylist_name(playlistName);
            pl.setPlaylist_meta_data(metaData);
            pl.setDevice_type(deviceType);
            pl.setDevice_type_version(Float.parseFloat(deviceTypeVersion));
            pl.setTotal_size(totalSize);
            pl.setPlay_time(ContentUtils.getPlayTimeFormattedStr(totalPlayTime));
            if (groupId != null && groupId.length() != 0) {
               pl.setGroup_id(new Long(groupId));
            } else {
               pl.setGroup_id(this.pInfo.getRootId(this.getLoginUserId()));
            }

            pl.setCreator_id(this.getLoginUserId());
            pl.setShare_flag(Integer.parseInt(shareFlag));
            pl.setIs_shuffle("N");
            if (addSubPlaylist) {
               pl.setHas_sub_playlist(true);
            } else {
               pl.setHas_sub_playlist(false);
            }

            pl.setPlaylist_type(playlistType);
            pl.setAms_mode("");
            pl.setAms_direct_play(false);
            pl.setIs_vwl("N");
            pl.setDefault_content_duration(defaultContentDuration);
            pl.setArr_content_list(content_list);
            ArrayList syncPlaylistList = new ArrayList();

            for(int i = 0; i < contentGroups.size(); ++i) {
               SyncPlaylist sPlaylist = new SyncPlaylist();
               sPlaylist.setPlaylist_id(playlistId);
               sPlaylist.setSync_play_id(Integer.toString(i));
               sPlaylist.setIs_sync(((SyncPlaylistGroupResource)contentGroups.get(i)).getSyncStatus());
               syncPlaylistList.add(sPlaylist);
            }

            pl.setSync_status_list(syncPlaylistList);
            if (pl.getGroup_id() == 0L) {
               pl.setGroup_id(this.pInfo.getRootId(this.getLoginUserId()));
            }

            pl.setContent_count(content_list.size());
            if (this.pInfo.addPlaylist(pl) > 0) {
            }

            SyncPlaylistResource resource = this.getSyncPlaylistResource(pl);
            resource.setProductType(productType);
            resource.setContentCountInEachGroup(contentCountInEachGroup);
            responsebody.setItems(resource);
            responsebody.setStatus("Success");
            return responsebody;
         } else {
            responsebody.setStatus("Fail");
            responsebody.setErrorCode(ExceptionCode.RES916[0]);
            responsebody.setErrorMessage(ExceptionCode.RES916[2]);
            return responsebody;
         }
      } catch (Exception var41) {
         this.logger.error("", var41);
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var41.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public ResponseBody updateSyncPlaylist(String playlistId, SyncPlaylistResource params) throws SQLException {
      ResponseBody responsebody = new ResponseBody();

      try {
         String groupId = params.getGroupId();
         String playlistName = params.getPlaylistName();
         String shareFlag = params.getShareFlag() == 0 ? "0" : "1";
         String deviceType = params.getDeviceType();
         String deviceTypeVersion = params.getDeviceTypeVersion();
         String metaData = params.getMetaData();
         String productType = params.getProductType();
         String playlistType = "3";
         long defaultContentDuration = params.getDefaultContentDuration();
         int contentCountInEachGroup = params.getContentCountInEachGroup();
         List contentGroups = params.getContentGroups();
         String ignoreTag = "0";
         String evennessPlayback = "0";
         Long curActiveVersion = this.pInfo.getPlaylistActiveVersionId(playlistId);
         String sessionId = UUID.randomUUID().toString();
         ContentInfo cmsDao = ContentInfoImpl.getInstance();
         boolean addSubPlaylist = false;
         Map syncDurationMap = new HashMap();
         Playlist playlist = this.pInfo.getPlaylistVerInfo(playlistId, curActiveVersion);
         if (playlist == null) {
            responsebody.setStatus("Fail");
            responsebody.setErrorCode(ExceptionCode.RES905[0]);
            responsebody.setErrorMessage(ExceptionCode.RES905[2]);
            return responsebody;
         } else {
            Long totalPlayTime = 0L;
            Long totalSize = 0L;
            List content_list = new ArrayList();
            Map totalSizeMapByContentId = new HashMap();
            if (contentGroups != null && contentGroups.size() > 0) {
               int contentGroupCount = contentGroups.size();

               int i;
               SyncPlaylistGroupResource contentGroup;
               int j;
               for(i = 0; i < contentGroupCount; ++i) {
                  contentGroup = (SyncPlaylistGroupResource)contentGroups.get(i);
                  j = contentGroup.getContents().size();
                  if (j != contentCountInEachGroup) {
                     responsebody.setStatus("Fail");
                     responsebody.setErrorCode(ExceptionCode.RES914[0]);
                     responsebody.setErrorMessage(ExceptionCode.RES914[2]);
                     return responsebody;
                  }
               }

               SyncPlaylistItemResource content;
               long minContentDuration;
               int i;
               SyncPlaylistItemResource paramContent;
               for(i = 0; i < contentGroupCount; ++i) {
                  contentGroup = (SyncPlaylistGroupResource)contentGroups.get(i);

                  for(j = 0; j < contentCountInEachGroup; ++j) {
                     content = (SyncPlaylistItemResource)contentGroup.getContents().get(j);
                     minContentDuration = content.getContentOrder();

                     for(i = 0; i < contentCountInEachGroup; ++i) {
                        if (i != j) {
                           paramContent = (SyncPlaylistItemResource)contentGroup.getContents().get(i);
                           long contentOrder2 = paramContent.getContentOrder();
                           if (minContentDuration == contentOrder2) {
                              responsebody.setStatus("Fail");
                              responsebody.setErrorCode(ExceptionCode.RES918[0]);
                              responsebody.setErrorMessage(ExceptionCode.RES918[2]);
                              return responsebody;
                           }
                        }
                     }
                  }
               }

               for(i = 0; i < contentCountInEachGroup; ++i) {
                  for(int j = 0; j < contentGroupCount; ++j) {
                     SyncPlaylistGroupResource contentsGroup = (SyncPlaylistGroupResource)contentGroups.get(j);
                     content = (SyncPlaylistItemResource)contentsGroup.getContents().get(i);
                     minContentDuration = ContentUtils.getPlayTimeStr(content.getContentDuration());
                     if (minContentDuration <= 0L) {
                        responsebody.setStatus("Fail");
                        responsebody.setErrorCode(ExceptionCode.RES915[0]);
                        responsebody.setErrorMessage(ExceptionCode.RES915[2]);
                        return responsebody;
                     }
                  }
               }

               String logContentIds = "";
               List contentIds = new ArrayList();
               List contentOrders = new ArrayList();

               String contentOrderInGroup;
               for(j = 0; j < contentGroups.size(); ++j) {
                  SyncPlaylistGroupResource contentsGroup = (SyncPlaylistGroupResource)contentGroups.get(j);

                  for(int j = 0; j < contentCountInEachGroup; ++j) {
                     SyncPlaylistItemResource content = (SyncPlaylistItemResource)contentsGroup.getContents().get(j);
                     contentOrderInGroup = content.getContentId();
                     contentIds.add(contentOrderInGroup);
                     contentOrders.add(String.valueOf(j + 1));
                     if (!logContentIds.isEmpty()) {
                        logContentIds = logContentIds + ",";
                     }

                     logContentIds = logContentIds + contentOrderInGroup;
                  }
               }

               Map contentsMap = new HashMap();

               int i;
               SyncPlaylistGroupResource contentGroup;
               int contentCount;
               String contentId;
               for(i = 0; i < contentGroups.size(); ++i) {
                  contentGroup = (SyncPlaylistGroupResource)contentGroups.get(i);
                  contentCount = contentGroup.getContents().size();

                  for(i = 0; i < contentCount; ++i) {
                     paramContent = (SyncPlaylistItemResource)contentGroup.getContents().get(i);
                     contentId = paramContent.getContentId();
                     Content content = cmsDao.getContentActiveVerInfo(contentId);
                     if (content == null) {
                        responsebody.setStatus("Fail");
                        responsebody.setErrorCode(ExceptionCode.RES917[0]);
                        responsebody.setErrorMessage(ExceptionCode.RES917[2]);
                        return responsebody;
                     }

                     contentsMap.put(contentId, content);
                  }
               }

               Content tempContent;
               for(i = 0; i < contentCountInEachGroup; ++i) {
                  minContentDuration = 0L;

                  String syncStatus;
                  for(i = 0; i < contentGroups.size(); ++i) {
                     syncStatus = ((SyncPlaylistGroupResource)contentGroups.get(i)).getSyncStatus();
                     if (syncStatus.equalsIgnoreCase("Y")) {
                        SyncPlaylistGroupResource splContentsGroup = (SyncPlaylistGroupResource)contentGroups.get(i);
                        SyncPlaylistItemResource splContent = (SyncPlaylistItemResource)splContentsGroup.getContents().get(i);
                        tempContent = (Content)contentsMap.get(splContent.getContentId());
                        long requestedContentDuration = ContentUtils.getPlayTimeStr(splContent.getContentDuration());
                        long originalContentDuration = ContentUtils.getPlayTimeStr(tempContent.getPlay_time());
                        if (minContentDuration == 0L || minContentDuration > requestedContentDuration) {
                           minContentDuration = requestedContentDuration;
                        }

                        if (minContentDuration > originalContentDuration) {
                           minContentDuration = originalContentDuration;
                        }
                     }
                  }

                  contentOrderInGroup = String.valueOf(i + 1);
                  syncStatus = String.valueOf(minContentDuration);
                  syncDurationMap.put(contentOrderInGroup, syncStatus);
               }

               for(i = 0; i < contentGroups.size(); ++i) {
                  contentGroup = (SyncPlaylistGroupResource)contentGroups.get(i);
                  contentCount = contentGroup.getContents().size();

                  for(i = 0; i < contentCount; ++i) {
                     paramContent = (SyncPlaylistItemResource)contentGroup.getContents().get(i);
                     contentId = paramContent.getContentId();
                     PlaylistContent pContent = null;
                     tempContent = null;
                     Content content = (Content)contentsMap.get(contentId);
                     if (content == null) {
                        Playlist subPlaylist = this.playlistInfo.getPlaylistActiveVerInfo(contentId);
                        if (subPlaylist != null) {
                           pContent = new PlaylistContent();
                           pContent.setContent_id(contentId);
                           pContent.setIs_sub_playlist(true);
                           pContent.setSync_play_id("0");
                           pContent.setContent_order(new Long((long)(i + 1)));
                           pContent.setContent_duration((Long)null);
                           pContent.setExpired_date((Timestamp)null);
                           pContent.setExpired_time((String)null);
                           pContent.setStart_date((Timestamp)null);
                           pContent.setStart_time((String)null);
                           pContent.setContiguous(false);
                           content_list.add(pContent);
                           addSubPlaylist = true;
                        }
                     } else {
                        List pList = this.pInfo.getContentList(playlistId, curActiveVersion);
                        boolean foundContent = false;

                        int groupOrder;
                        for(groupOrder = 0; groupOrder < pList.size(); ++groupOrder) {
                           PlaylistContent tempContent = (PlaylistContent)pList.get(groupOrder);
                           String contentOrder = (String)contentOrders.get(i);
                           if (contentId.equals(tempContent.getContent_id()) && (long)Integer.parseInt(contentOrder) == tempContent.getContent_order()) {
                              foundContent = true;
                              pContent = tempContent;
                              content.setContent_duration(tempContent.getContent_duration());
                              break;
                           }
                        }

                        if (!foundContent) {
                           pContent = new PlaylistContent();
                        }

                        groupOrder = contentGroup.getGroupOrder();
                        long contentOrder = paramContent.getContentOrder();
                        totalSizeMapByContentId.put(content.getContent_id(), content.getTotal_size());
                        pContent.setContent_id(content.getContent_id());
                        pContent.setSync_play_id(String.valueOf(groupOrder));
                        pContent.setContent_order(contentOrder);
                        pContent.setPlaylist_id(playlistId);
                        Long content_duration = 0L;
                        content_duration = this.getSyncContentDuration(content, (int)contentOrder, (String)null, pList, productType, deviceType);
                        pContent.setContent_duration(content_duration);
                        String syncStatus = contentGroup.getSyncStatus();
                        if (syncStatus != null && !syncStatus.equals("") && syncStatus.equals("Y") && syncDurationMap.containsKey(String.valueOf(pContent.getContent_order()))) {
                           String time = syncDurationMap.get(String.valueOf(pContent.getContent_order())).toString();
                           String[] timeArr = time.split(":");
                           if (totalPlayTime == 0L) {
                              for(int d = 1; d <= syncDurationMap.size(); ++d) {
                                 totalPlayTime = totalPlayTime + Long.valueOf((String)syncDurationMap.get(String.valueOf(d)));
                              }
                           }

                           if (timeArr.length == 1) {
                              pContent.setContent_duration(Long.valueOf(timeArr[0]));
                              pContent.setContent_duration_milli("");
                           } else {
                              pContent.setContent_duration(Long.valueOf(timeArr[0]));
                              pContent.setContent_duration_milli(timeArr[1]);
                           }
                        }

                        content_list.add(pContent);
                     }
                  }
               }

               Iterator it = totalSizeMapByContentId.entrySet().iterator();

               while(it.hasNext()) {
                  Entry entry = (Entry)it.next();
                  if (entry.getValue() != null) {
                     totalSize = totalSize + (Long)entry.getValue();
                  }
               }

               new Playlist();
               playlist.setPlaylist_id(playlistId);
               playlist.setPlaylist_name(playlistName);
               playlist.setPlaylist_meta_data(metaData);
               playlist.setTotal_size(totalSize);
               playlist.setPlay_time(ContentUtils.getPlayTimeFormattedStr(totalPlayTime));
               if (groupId != null && groupId.length() != 0) {
                  playlist.setGroup_id(new Long(groupId));
               } else {
                  playlist.setGroup_id(this.pInfo.getRootId(this.getLoginUserId()));
               }

               playlist.setCreator_id(this.getLoginUserId());
               playlist.setShare_flag(Integer.parseInt(shareFlag));
               playlist.setArr_content_list(content_list);
               playlist.setIs_shuffle("N");
               playlist.setContent_count(content_list.size());
               playlist.setAms_mode("");
               playlist.setAms_direct_play(false);
               playlist.setPlaylist_type(playlistType);
               playlist.setDefault_content_duration(defaultContentDuration);
               if (addSubPlaylist) {
                  playlist.setHas_sub_playlist(true);
               } else {
                  playlist.setHas_sub_playlist(false);
               }

               if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
                  ArrayList syncPlaylistList = new ArrayList();

                  for(i = 0; i < contentGroups.size(); ++i) {
                     SyncPlaylist sPlaylist = new SyncPlaylist();
                     sPlaylist.setPlaylist_id(playlistId);
                     sPlaylist.setSync_play_id(Integer.toString(i));
                     sPlaylist.setIs_sync(((SyncPlaylistGroupResource)contentGroups.get(i)).getSyncStatus());
                     syncPlaylistList.add(sPlaylist);
                  }

                  playlist.setSync_status_list(syncPlaylistList);
               }

               if (this.pInfo.addPlaylist(playlist) > 0) {
               }

               boolean ret = false;
               if (playlistId != null) {
                  if ((playlistName.length() > 0 || metaData.length() > 0 || shareFlag.length() > 0) && this.pInfo.setPlaylistInfo(playlistId, playlistName, metaData, Integer.parseInt(shareFlag), Integer.parseInt(ignoreTag), Integer.parseInt(evennessPlayback)) > 0) {
                     ret = true;
                  }

                  if (groupId.length() > 0 && this.pInfo.setPlaylistGroup(playlistId, new Long(groupId)) > 0) {
                     ret = true;
                  }
               }

               ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
               scheduleInfo.setPlaylistTrigger(playlist.getPlaylist_id());
               EventInfo eInfo = EventInfoImpl.getInstance();
               eInfo.setPlaylistTrigger(playlist.getPlaylist_id());
               cmsDao.setContentUnlockBySessionID(sessionId);
               SyncPlaylistResource resource = this.getSyncPlaylistResource(playlist);
               resource.setProductType(productType);
               resource.setContentCountInEachGroup(contentCountInEachGroup);
               responsebody.setItems(resource);
               responsebody.setStatus("Success");
               return responsebody;
            } else {
               responsebody.setStatus("Fail");
               responsebody.setErrorCode(ExceptionCode.RES916[0]);
               responsebody.setErrorMessage(ExceptionCode.RES916[2]);
               return responsebody;
            }
         }
      } catch (Exception var51) {
         this.logger.error("", var51);
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var51.getMessage());
         return responsebody;
      }
   }

   private SyncPlaylistResource getSyncPlaylistResource(Playlist playlist) {
      SyncPlaylistResource resource = new SyncPlaylistResource();
      resource.setPlaylistId(playlist.getPlaylist_id());
      resource.setPlaylistName(playlist.getPlaylist_name());
      resource.setGroupId(String.valueOf(playlist.getGroup_id()));
      resource.setDeviceType(playlist.getDevice_type());
      resource.setDeviceTypeVersion(String.valueOf(playlist.getDevice_type_version()));
      resource.setMetaData(playlist.getPlaylist_meta_data());
      resource.setShareFlag(playlist.getShare_flag());
      resource.setDefaultContentDuration(playlist.getDefault_content_duration());
      if (playlist.getArr_content_list() != null) {
         List contents = playlist.getArr_content_list();
         List syncStatusList = playlist.getSync_status_list();
         List contentGroupResources = new ArrayList();

         int i;
         for(i = 0; i < syncStatusList.size(); ++i) {
            SyncPlaylist syncStatus = (SyncPlaylist)syncStatusList.get(i);
            SyncPlaylistGroupResource contentGroupResource = new SyncPlaylistGroupResource();
            List itemResources = new ArrayList();
            contentGroupResource.setGroupOrder(Integer.parseInt(syncStatus.getSync_play_id()));
            contentGroupResource.setSyncStatus(syncStatus.getIs_sync());
            contentGroupResource.setContents(itemResources);
            contentGroupResources.add(contentGroupResource);
         }

         for(i = 0; i < contents.size(); ++i) {
            PlaylistContent content = (PlaylistContent)contents.get(i);
            SyncPlaylistItemResource itemResource = new SyncPlaylistItemResource();
            itemResource.setContentId(content.getContent_id());
            itemResource.setContentDuration(DateUtils.changeSecondToFormatTime(content.getContent_duration() != null && content.getContent_duration() <= 2147483647L ? content.getContent_duration().intValue() : 0));
            itemResource.setContentOrder(content.getContent_order());
            int contentGroupIndex = Integer.parseInt(content.getSync_play_id());
            ((SyncPlaylistGroupResource)contentGroupResources.get(contentGroupIndex)).getContents().add(itemResource);
         }

         resource.setContentGroups(contentGroupResources);
      }

      return resource;
   }

   private Long getContentDuration(Content content, int beforeOrder, String selEffectList, List contentList, String productType, String deviceType) {
      Long content_duration = 0L;
      if (content != null && content.getPlay_time() != null && !content.getPlay_time().equals("-") && !content.getPlay_time().equals("")) {
         if (productType.equalsIgnoreCase("PREMIUM")) {
            content_duration = ContentUtils.getPlayTimeStr(content.getPlay_time());
         }

         return content_duration;
      } else {
         Map contentDurationMap = this.getContentDurationByEffectList(selEffectList);
         if (contentDurationMap.containsKey(beforeOrder + "")) {
            content_duration = Long.valueOf((String)contentDurationMap.get(beforeOrder + ""));
            return content_duration;
         } else {
            if (contentList != null) {
               Iterator var9 = contentList.iterator();

               while(var9.hasNext()) {
                  PlaylistContent playlistContent = (PlaylistContent)var9.next();
                  if (playlistContent.getContent_id().equalsIgnoreCase(content.getContent_id()) && playlistContent.getContent_order() == (long)beforeOrder) {
                     content_duration = playlistContent.getContent_duration();
                     return content_duration;
                  }
               }
            }

            content_duration = this.getDefaultContentDuration(productType, deviceType, content.getContent_duration());
            return content_duration;
         }
      }
   }

   private Map getContentDurationByEffectList(String effectList) {
      Map map = new HashMap();
      if (effectList != null && !effectList.equalsIgnoreCase("")) {
         String[] arrEffectList = effectList.split(",");
         String[] var4 = arrEffectList;
         int var5 = arrEffectList.length;

         for(int var6 = 0; var6 < var5; ++var6) {
            String effect = var4[var6];
            String[] arrEffectItem = effect.split("↑");
            map.put(arrEffectItem[8], arrEffectItem[7]);
         }
      }

      return map;
   }

   private Long getDefaultContentDuration(String productType, String deviceType, Long contentDuration) {
      if (contentDuration == 0L && CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
         if (deviceType.equalsIgnoreCase("iPLAYER")) {
            contentDuration = ContentConstants.CONTENT_DURATION;
         } else if (deviceType.equalsIgnoreCase("SPLAYER")) {
            contentDuration = ContentConstants.LITE_CONTENT_DURATION;
         } else if (!deviceType.equalsIgnoreCase("S2PLAYER") && !deviceType.equalsIgnoreCase("S3PLAYER")) {
            contentDuration = ContentConstants.LITE_CONTENT_DURATION;
         } else {
            contentDuration = ContentConstants.LITE_CONTENT_DURATION;
         }
      }

      return contentDuration;
   }

   public String getLoginUserId() {
      return SecurityUtils.getUserContainer() == null ? "" : SecurityUtils.getUserContainer().getUser().getUser_id();
   }

   private Long getSyncContentDuration(Content content, int beforeOrder, String selEffectList, List contentList, String productType, String deviceType) {
      Long content_duration = 30L;
      Map contentDurationMap = this.getContentDurationByEffectList(selEffectList);
      if (contentDurationMap.containsKey(beforeOrder + "")) {
         content_duration = Long.valueOf((String)contentDurationMap.get(beforeOrder + ""));
         return content_duration;
      } else {
         if (contentList != null) {
            Iterator var9 = contentList.iterator();

            while(var9.hasNext()) {
               PlaylistContent playlistContent = (PlaylistContent)var9.next();
               if (playlistContent.getContent_id().equalsIgnoreCase(content.getContent_id()) && playlistContent.getContent_order() == (long)beforeOrder) {
                  if (content.getPlay_time() != null && !content.getPlay_time().equals("")) {
                     content_duration = ContentUtils.getPlayTimeStr(content.getPlay_time());
                  } else {
                     content_duration = playlistContent.getContent_duration();
                  }

                  return content_duration;
               }
            }
         }

         content_duration = this.getDefaultContentDuration(productType, deviceType, ContentUtils.getPlayTimeStr(content.getPlay_time()));
         return content_duration;
      }
   }
}
