package com.samsung.magicinfo.webauthor2.repository.model.datalink;

import java.io.Serializable;
import java.util.List;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;

public class ConvertTableResultListData implements Serializable {
  @XmlElement
  private Integer totalCount;
  
  @XmlElementWrapper(name = "resultList")
  @XmlElement(name = "ConvertTable")
  private List<ConvertTableData> resultList;
  
  public Integer getTotalCount() {
    return this.totalCount;
  }
  
  public List<ConvertTableData> getResultList() {
    return this.resultList;
  }
}
