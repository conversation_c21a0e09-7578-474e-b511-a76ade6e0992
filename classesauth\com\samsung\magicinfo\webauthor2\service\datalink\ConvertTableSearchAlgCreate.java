package com.samsung.magicinfo.webauthor2.service.datalink;

import com.samsung.magicinfo.webauthor2.model.datalink.ConvertTable;
import com.samsung.magicinfo.webauthor2.xml.dlkinfo.ConvertTableMapType;
import java.util.ArrayList;
import java.util.List;

public class ConvertTableSearchAlgCreate extends ConvertTableSearchAlg<List<ConvertTableMapType>> {
  private List<ConvertTableMapType> convertTableMaps = new ArrayList<>();
  
  protected void actionOnConvertTable(ConvertTable convertTable, int pageNumb, String elementName, int splitGroupId, String splitGroupName, int dataIndex, String convertTableName) {
    this.convertTableMaps.add(new ConvertTableMapType(pageNumb, elementName, splitGroupId, splitGroupName, dataIndex, convertTableName));
  }
  
  public List<ConvertTableMapType> getResult() {
    return this.convertTableMaps;
  }
}
