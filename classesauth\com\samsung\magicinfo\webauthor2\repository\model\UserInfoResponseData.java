package com.samsung.magicinfo.webauthor2.repository.model;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "response")
public class UserInfoResponseData implements Serializable {
  private static final long serialVersionUID = -8890212460832794297L;
  
  @XmlAttribute
  private String code;
  
  @XmlElement(name = "responseClass")
  private UserInfo user;
  
  public String getCode() {
    return this.code;
  }
  
  public UserInfo getUser() {
    return this.user;
  }
  
  public String toString() {
    return "UserInfoResponseData{code='" + this.code + '\'' + ", user=" + this.user + '}';
  }
}
