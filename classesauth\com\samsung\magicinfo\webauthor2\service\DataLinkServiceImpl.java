package com.samsung.magicinfo.webauthor2.service;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.exception.repository.CannotGetDataLinkDescriptorException;
import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.DataLinkServer;
import com.samsung.magicinfo.webauthor2.model.DataLinkTable;
import com.samsung.magicinfo.webauthor2.model.DataLinkTableRow;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.model.datalink.DataLinkDescriptor;
import com.samsung.magicinfo.webauthor2.repository.OpenAPIDataLinkRepository;
import com.samsung.magicinfo.webauthor2.repository.datalink.servers.DataLinkRepository;
import com.samsung.magicinfo.webauthor2.repository.model.DatalinkServerEntityData;
import com.samsung.magicinfo.webauthor2.repository.model.DatalinkTableEntityData;
import com.samsung.magicinfo.webauthor2.repository.model.dlk.DLKTableData;
import com.samsung.magicinfo.webauthor2.repository.model.dlk.DLKTableRowData;
import com.samsung.magicinfo.webauthor2.service.datalink.DLKXmlFactory;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.inject.Inject;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class DataLinkServiceImpl implements DataLinkService {
  private OpenAPIDataLinkRepository misDataLinkRepository;
  
  private DataLinkRepository serverDataLinkRepository;
  
  private UserData userData;
  
  private DLKXmlFactory dlkXmlFactory;
  
  private ContentService contentService;
  
  private RemoteContentService remoteContentService;
  
  private static final Logger logger = LoggerFactory.getLogger(DataLinkServiceImpl.class);
  
  @Inject
  public DataLinkServiceImpl(OpenAPIDataLinkRepository misDataLinkRepository, DataLinkRepository serverDataLinkRepository, UserData userData, DLKXmlFactory dlkXmlFactory, ContentService contentService, RemoteContentService remoteContentService) {
    this.misDataLinkRepository = misDataLinkRepository;
    this.serverDataLinkRepository = serverDataLinkRepository;
    this.userData = userData;
    this.dlkXmlFactory = dlkXmlFactory;
    this.contentService = contentService;
    this.remoteContentService = remoteContentService;
  }
  
  public List<DataLinkServer> getDataLinkServerList() {
    updateDataLinkServerList();
    return this.userData.getDataLinkServers();
  }
  
  public List<DataLinkServer> getDataLinkServerListCached() {
    return this.userData.getDataLinkServers();
  }
  
  public List<DataLinkTable> getDataLinkServerTables(String serverName, String dataType) {
    DataLinkServer dataLinkServer = updateDataLinkTableList(serverName);
    return dataLinkServer.getTables(dataType);
  }
  
  public List<DataLinkTable> getDataLinkServerTablesForService(String serverName, String serviceName, String dataType) {
    DataLinkServer dataLinkServer = updateDataLinkTableList(serverName);
    return dataLinkServer.getTablesForService(serviceName, dataType);
  }
  
  public List<String> getDataLinkServerServices(String serverName, String dataType) {
    DataLinkServer dataLinkServer = updateDataLinkTableList(serverName);
    return dataLinkServer.getServiceList(dataType);
  }
  
  public List<DataLinkTableRow> getDataLinkServerTableRows(String serverName, String dynaName) {
    DataLinkServer dataLinkServer = this.userData.getDataLinkServer(serverName);
    DataLinkTable dataLinkTable = dataLinkServer.getTable(dynaName);
    List<DLKTableRowData> tableRowsData = this.serverDataLinkRepository.getDataTableInfo(dataLinkServer, dataLinkTable);
    return DataLinkTableRow.fromData(tableRowsData);
  }
  
  public DataLinkDescriptor getDataLinkDescriptor(String contentId) {
    Content content = this.contentService.getContent(contentId);
    if (content == null || content.getType() != MediaType.DLK)
      return null; 
    String xmlDLK = this.remoteContentService.getXmlFileContents(content.getFileId(), content.getFileName());
    String dlkName = FilenameUtils.getBaseName(content.getFileName());
    if (Strings.isNullOrEmpty(xmlDLK))
      throw new CannotGetDataLinkDescriptorException(contentId); 
    DataLinkDescriptor dataLinkDescriptor = this.dlkXmlFactory.unmarshal(xmlDLK);
    dataLinkDescriptor.setDlkName(dlkName);
    return dataLinkDescriptor;
  }
  
  private void updateDataLinkServerList() {
    List<DatalinkServerEntityData> datalinkServerEntityList = this.misDataLinkRepository.getDataLinkServerEntities();
    List<DataLinkServer> dataLinkServers = DataLinkServer.fromData(datalinkServerEntityList);
    storeServerList(dataLinkServers);
    int requiredOpenApiVersionMajor = 7000;
    Boolean getTableListFromMisServer = this.userData.isSessionMisOpenApiVersionHigherOrEqualThan(requiredOpenApiVersionMajor);
    if (getTableListFromMisServer.booleanValue())
      for (DatalinkServerEntityData server : datalinkServerEntityList) {
        String serverName = server.getServerName();
        Map<String, DataLinkTable> dataLinkTables = new HashMap<>();
        Set<String> serviceNamesSet = new HashSet<>();
        List<String> serviceNames = new ArrayList<>();
        Map<String, List<DataLinkTable>> dataLinkServices = new HashMap<>();
        for (DatalinkTableEntityData data : server.getTableList()) {
          DataLinkTable dataLinkTable = DataLinkTable.fromDatalinkTableEntity(serverName, data);
          dataLinkTables.put(dataLinkTable.getDynaName(), dataLinkTable);
          serviceNamesSet.add((dataLinkTable.getSvrcName() == null) ? dataLinkTable.getName() : dataLinkTable.getSvrcName());
        } 
        for (String name : (String[])serviceNamesSet.<String>toArray(new String[serviceNamesSet.size()])) {
          serviceNames.add(name);
          List<DataLinkTable> matchingTables = new ArrayList<>();
          Iterator<Map.Entry<String, DataLinkTable>> it = dataLinkTables.entrySet().iterator();
          while (it.hasNext()) {
            Map.Entry<String, DataLinkTable> pair = it.next();
            if (((DataLinkTable)pair.getValue()).getSvrcName() != null && ((DataLinkTable)pair.getValue()).getSvrcName().equals(name)) {
              matchingTables.add(pair.getValue());
              continue;
            } 
            if (((DataLinkTable)pair.getValue()).getSvrcName() == null && ((DataLinkTable)pair.getValue()).getName().equals(name))
              matchingTables.add(pair.getValue()); 
          } 
          dataLinkServices.put(name, matchingTables);
        } 
        DataLinkServer dataLinkServer = this.userData.getDataLinkServer(serverName);
        dataLinkServer.setTablesMap(dataLinkTables);
        dataLinkServer.setServicesMap(dataLinkServices);
        dataLinkServer.setServiceList(serviceNames);
      }  
  }
  
  private DataLinkServer updateDataLinkTableList(String serverName) {
    DataLinkServer dataLinkServer = this.userData.getDataLinkServer(serverName);
    int requiredOpenApiVersionMajor = 7000;
    Boolean getTableListFromMisServer = this.userData.isSessionMisOpenApiVersionHigherOrEqualThan(requiredOpenApiVersionMajor);
    if (!getTableListFromMisServer.booleanValue()) {
      Map<String, DataLinkTable> dataLinkTables = new HashMap<>();
      Set<String> serviceNamesSet = new HashSet<>();
      List<String> serviceNames = new ArrayList<>();
      Map<String, List<DataLinkTable>> dataLinkServices = new HashMap<>();
      List<DLKTableData> tableListFromDatalinkServer = this.serverDataLinkRepository.getDataTableList(dataLinkServer);
      for (DLKTableData data : tableListFromDatalinkServer) {
        DataLinkTable dataLinkTable = DataLinkTable.fromData(serverName, data);
        dataLinkTables.put(dataLinkTable.getDynaName(), dataLinkTable);
        serviceNamesSet.add((dataLinkTable.getSvrcName() == null) ? dataLinkTable.getName() : dataLinkTable.getSvrcName());
      } 
      for (String name : (String[])serviceNamesSet.<String>toArray(new String[serviceNamesSet.size()])) {
        serviceNames.add(name);
        List<DataLinkTable> matchingTables = new ArrayList<>();
        Iterator<Map.Entry<String, DataLinkTable>> it = dataLinkTables.entrySet().iterator();
        while (it.hasNext()) {
          Map.Entry<String, DataLinkTable> pair = it.next();
          if (((DataLinkTable)pair.getValue()).getSvrcName() != null && ((DataLinkTable)pair.getValue()).getSvrcName().equals(name)) {
            matchingTables.add(pair.getValue());
            continue;
          } 
          if (((DataLinkTable)pair.getValue()).getSvrcName() == null && ((DataLinkTable)pair.getValue()).getName().equals(name))
            matchingTables.add(pair.getValue()); 
        } 
        dataLinkServices.put(name, matchingTables);
      } 
      dataLinkServer.setTablesMap(dataLinkTables);
      dataLinkServer.setServicesMap(dataLinkServices);
      dataLinkServer.setServiceList(serviceNames);
    } 
    return dataLinkServer;
  }
  
  private void storeServerList(List<DataLinkServer> dataLinkServers) {
    HashMap<String, DataLinkServer> serverHashMap = new HashMap<>();
    for (DataLinkServer dataLinkServer : dataLinkServers)
      serverHashMap.put(dataLinkServer.getServerName(), dataLinkServer); 
    this.userData.updateDataLinkServers(serverHashMap);
  }
}
