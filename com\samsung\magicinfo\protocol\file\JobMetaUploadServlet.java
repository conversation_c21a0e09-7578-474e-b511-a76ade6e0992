package com.samsung.magicinfo.protocol.file;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.exception.ExceptionCode;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.device.job.manager.JobManager;
import com.samsung.magicinfo.framework.device.job.manager.JobManagerImpl;
import java.io.File;
import java.io.IOException;
import java.util.UUID;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.Logger;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

public class JobMetaUploadServlet extends HttpServlet {
   private static final long serialVersionUID = 6016570895243834143L;
   private Logger logger = LoggingManagerV2.getLogger(JobMetaUploadServlet.class);

   public JobMetaUploadServlet() {
      super();
   }

   public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");
      this.doPost(request, response);
   }

   public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");

      try {
         String JOBS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "jobs_home";
         File homeDir = SecurityUtils.getSafeFile(JOBS_HOME);
         if (!homeDir.exists()) {
            boolean fSuccess = homeDir.mkdirs();
            if (!fSuccess) {
               this.logger.error("mkdir Fail");
            }
         }

         CommonsMultipartResolver multiPartResolver = new CommonsMultipartResolver();
         MultipartHttpServletRequest multiRequest = multiPartResolver.resolveMultipart(request);
         String fileName = multiRequest.getParameter("FileName");
         String fileSize = multiRequest.getParameter("FileSize");
         String hash = multiRequest.getParameter("Hash");
         multiPartResolver.cleanupMultipart(multiRequest);
         JobManager jobMgr = JobManagerImpl.getInstance();
         String fileId = jobMgr.getFileIDByHash(fileName, Long.parseLong(fileSize), hash);
         if (fileId == null) {
            fileId = UUID.randomUUID().toString().toUpperCase();
            boolean rt = jobMgr.addFileId(fileId, fileName, Long.parseLong(fileSize), hash);
            if (!rt) {
               response.sendError(500, ExceptionCode.HTTP500[2]);
            }
         }

         response.addHeader("FileID", fileId);
      } catch (Exception var13) {
         response.sendError(600, var13.toString());
         this.logger.error(var13);
      }

   }
}
