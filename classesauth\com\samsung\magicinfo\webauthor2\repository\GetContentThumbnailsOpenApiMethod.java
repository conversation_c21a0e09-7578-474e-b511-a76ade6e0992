package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.model.ContentThumbnailBasic;
import com.samsung.magicinfo.webauthor2.repository.model.ContentThumbnailListResponseData;
import com.samsung.magicinfo.webauthor2.repository.model.ContentThumbnailResultListData;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

public class GetContentThumbnailsOpenApiMethod extends OpenApiMethod<List<ContentThumbnailBasic>, ContentThumbnailListResponseData> {
  private static final Logger logger = LoggerFactory.getLogger(GetContentThumbnailsOpenApiMethod.class);
  
  private final String userId;
  
  private final String token;
  
  private final String contentId;
  
  private final String resolution;
  
  public GetContentThumbnailsOpenApiMethod(RestTemplate restTemplate, String userId, String token, String contentId, String resolution) {
    super(restTemplate);
    this.userId = userId;
    this.token = token;
    this.contentId = contentId;
    this.resolution = resolution;
  }
  
  protected String getOpenApiClassName() {
    return "CommonContentService";
  }
  
  protected String getOpenApiMethodName() {
    return "getContentThumbnail";
  }
  
  protected Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("token", this.token);
    vars.put("content_id", this.contentId);
    vars.put("mode", this.resolution);
    return vars;
  }
  
  Class<ContentThumbnailListResponseData> getResponseClass() {
    return ContentThumbnailListResponseData.class;
  }
  
  List<ContentThumbnailBasic> convertResponseData(ContentThumbnailListResponseData responseData) {
    ContentThumbnailResultListData resultListData = responseData.getResponseClass();
    List<ContentThumbnailBasic> resultArray = new ArrayList<>();
    if (resultListData == null) {
      logger.info("GetContentThumbnailsOpenApiMethod, responseData.getResponseClass() == NULL");
      return new ArrayList<>();
    } 
    List<String> resultList = resultListData.getResultList();
    if (resultList == null) {
      logger.info("GetContentThumbnailsOpenApiMethod, resultListData.getResultList() == NULL");
      return resultArray;
    } 
    for (String thumbnailPath : resultList) {
      ContentThumbnailBasic thumbnailBasic = new ContentThumbnailBasic();
      String response = thumbnailPath;
      response = response.replace("MagicInfo/servlet/ContentThumbnail?thumb_id=", "");
      response = response.replace("&thumb_filename=", "/");
      String[] values = response.split("/");
      thumbnailBasic.setFileId(values[0]);
      thumbnailBasic.setFileName(values[1]);
      resultArray.add(thumbnailBasic);
    } 
    return resultArray;
  }
}
