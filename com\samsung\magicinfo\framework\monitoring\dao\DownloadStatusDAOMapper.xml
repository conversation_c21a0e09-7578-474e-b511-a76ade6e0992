<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.samsung.magicinfo.framework.monitoring.dao.DownloadStatusDAOMapper">

	<select id="countStatus" resultType="int">
		SELECT COUNT(DEVICE_ID) FROM MI_CDS_DOWNLOAD_STATUS WHERE DEVICE_ID = #{deviceId} AND CONTENT_ID = #{contentId} <if test="activeType != null and !activeType.equals('')"> AND ACTIVE_TYPE = #{activeType}</if>
	</select>
	
	<insert id="addStatus">
		INSERT INTO MI_CDS_DOWNLOAD_STATUS (PROGRESS, DEVICE_ID, CONTENT_ID, ACTIVE_TYPE) VALUES (#{progress}, #{deviceId}, #{contentId}, #{activeType})
	</insert>
	
	<update id="updateStatus">
		UPDATE MI_CDS_DOWNLOAD_STATUS SET PROGRESS = #{progress} WHERE DEVICE_ID = #{deviceId} AND CONTENT_ID = #{contentId} AND ACTIVE_TYPE = #{activeType}
	</update>
	
	<select id="getDownloadProgress" resultType="java.lang.String">
		SELECT PROGRESS FROM MI_CDS_DOWNLOAD_STATUS WHERE DEVICE_ID = #{deviceId} AND CONTENT_ID = #{contentId} AND ACTIVE_TYPE = #{activeType}
	</select>
	
	<select id="getCntDownloadStatusByDeviceId" resultType="int">
		SELECT COUNT(DEVICE_ID) FROM MI_CDS_DOWNLOAD_STATUS WHERE DEVICE_ID = #{deviceId} AND CONTENT_ID = #{contentId}
	</select>
	
	<delete id="deleteAllDownloadStatusByDeviceId">
		DELETE FROM MI_CDS_DOWNLOAD_STATUS WHERE DEVICE_ID = #{deviceId}
	</delete>
	<delete id="deleteDownloadStatus">
		DELETE FROM MI_CDS_DOWNLOAD_STATUS WHERE DEVICE_ID = #{deviceId} AND ACTIVE_TYPE = #{activeType}
	</delete>
	
	<delete id="deleteDownloadStatusByProgramId">
		DELETE FROM MI_CDS_DOWNLOAD_STATUS WHERE PROGRAM_ID = #{programId}
	</delete>

	<delete id="deleteDownloadStatusByProgramIdAndDeviceId">
		DELETE FROM MI_CDS_DOWNLOAD_STATUS WHERE DEVICE_ID = #{deviceId}
	</delete>
	
	<delete id="deleteDownloadStatusDetailByDeviceId">
		DELETE FROM MI_CDS_DOWNLOAD_STATUS_DETAIL WHERE DEVICE_ID = #{deviceId}
	</delete>

	<delete id="deleteDownloadStatusDetailByProgramId">
		DELETE FROM MI_CDS_DOWNLOAD_STATUS_DETAIL WHERE PROGRAM_ID = #{programId}
	</delete>
	
	<delete id="deleteVWLDownloadStatus">
		DELETE FROM MI_CDS_INFO_VWL_DOWNLOAD_STATUS WHERE CONSOLE_ID = #{consoleId}
	</delete>
	
	<delete id="deleteProgramStatus">
		DELETE FROM MI_CDS_INFO_VWL_PROGRAM_STATUS WHERE CONSOLE_ID = #{consoleId}
	</delete>

	<update id="updateStatusData">
		UPDATE MI_CDS_DOWNLOAD_STATUS_DETAIL SET PROGRESS = #{progress} WHERE DEVICE_ID = #{deviceId} AND CONTENT_ID = #{contentId}
	</update>
	
	<insert id="addScheduleDeployStatus">
		INSERT INTO MI_CDS_MAP_PROGRAM_DEVICE_STATUS (PROGRAM_ID, DEVICE_ID, STATUS) VALUES (#{programId}, #{deviceId}, #{status})
	</insert>
	<update id="updateScheduleDeployStatus">
		UPDATE MI_CDS_MAP_PROGRAM_DEVICE_STATUS SET STATUS = #{status} WHERE DEVICE_ID = #{deviceId} AND PROGRAM_ID = #{programId}
	</update>
	<update id="updateScheduleDeployStatusByDeviceId">
		UPDATE MI_CDS_MAP_PROGRAM_DEVICE_STATUS SET STATUS = #{status} WHERE DEVICE_ID = #{deviceId}
	</update>
	<delete id="deleteScheduleDeployStatus">
		DELETE FROM MI_CDS_MAP_PROGRAM_DEVICE_STATUS WHERE DEVICE_ID = #{deviceId} AND PROGRAM_ID = #{programId}
	</delete>
	<delete id="deleteScheduleDeployStatusByDeviceId">
		DELETE FROM MI_CDS_MAP_PROGRAM_DEVICE_STATUS WHERE DEVICE_ID = #{deviceId}
	</delete>
	<delete id="deleteScheduleDeployStatusByProgramId">
		DELETE FROM MI_CDS_MAP_PROGRAM_DEVICE_STATUS WHERE PROGRAM_ID = #{programId}
	</delete>

	<select id="selectScheduleDeployStatus" resultType="int">
		SELECT COUNT(PROGRAM_ID) FROM MI_CDS_MAP_PROGRAM_DEVICE_STATUS WHERE DEVICE_ID = #{deviceId} AND PROGRAM_ID = #{programId}
	</select>
	<select id="getProgramIdByDeviceIdFromScheduleDeployStatus"  resultType="java.lang.String">
		SELECT DISTINCT PROGRAM_ID FROM MI_CDS_MAP_PROGRAM_DEVICE_STATUS WHERE DEVICE_ID = #{deviceId}
	</select>
	<select id="getTotalDownloadProgress" resultType="java.util.Map">
		SELECT PROGRESS FROM MI_CDS_DOWNLOAD_STATUS WHERE DEVICE_ID = #{deviceId}
	</select>
	
	<insert id="initDownloadStatus">
		<if test="downloadStatus != null and downloadStatus.device != null and downloadStatus.contentList != null">
			INSERT INTO MI_CDS_DOWNLOAD_STATUS (DEVICE_ID, CONTENT_ID, ACTIVE_TYPE, PROGRAM_ID, GROUP_ID) VALUES 
			<foreach collection="downloadStatus.device" item="device" separator="," >
				<foreach collection="downloadStatus.contentList" item="contentId" open="(" separator="),(" close=")">
					#{device.device_id}, #{contentId}, #{downloadStatus.activeType}, #{downloadStatus.programId}, #{device.group_id}
				</foreach>
			</foreach>
		</if>
	</insert>

	<insert id="addDownloadStatus">
		INSERT INTO MI_CDS_DOWNLOAD_STATUS (DEVICE_ID, CONTENT_ID, ACTIVE_TYPE, PROGRAM_ID) VALUES
		(#{deviceId}, #{contentId}, #{activeType}, #{programId})
	</insert>
	
	<insert id="addScheduleDeployStatusWithDeviceList">
		<if test="deviceList != null and deviceList.size() > 0">
			INSERT INTO MI_CDS_MAP_PROGRAM_DEVICE_STATUS (PROGRAM_ID, DEVICE_ID, STATUS) VALUES
			<foreach collection="deviceList" item="device" open="(" separator="),(" close=")">
					#{programId}, #{device.device_id}, #{status}
			</foreach>
		</if>
	</insert>

	<select id="getDownloadStatusListByProgramId" resultType="java.util.Map">
		SELECT DEVICE_ID,
		(SELECT COUNT(CONTENT_ID) FROM MI_CDS_DOWNLOAD_STATUS WHERE DEVICE_ID = STATUS.DEVICE_ID AND PROGRAM_ID = #{programId} <if test="activeType != null and !activeType.equals('')"> AND ACTIVE_TYPE = #{activeType}</if>) AS TOTAL,
		(SELECT COUNT(CONTENT_ID) FROM MI_CDS_DOWNLOAD_STATUS WHERE DEVICE_ID = STATUS.DEVICE_ID AND PROGRESS = '100 %' AND PROGRAM_ID = #{programId} <if test="activeType != null and !activeType.equals('')"> AND ACTIVE_TYPE = #{activeType}</if>) AS COMPLETE,
		(SELECT COUNT(CONTENT_ID) FROM MI_CDS_DOWNLOAD_STATUS WHERE DEVICE_ID = STATUS.DEVICE_ID AND PROGRESS != '100 %' AND PROGRAM_ID = #{programId} <if test="activeType != null and !activeType.equals('')"> AND ACTIVE_TYPE = #{activeType}</if>) AS DOWNLOAD
		FROM MI_CDS_DOWNLOAD_STATUS STATUS
		WHERE PROGRAM_ID = #{programId} <if test="activeType != null and !activeType.equals('')"> AND ACTIVE_TYPE = #{activeType}</if>
		GROUP BY DEVICE_ID
	</select>
	
	<select id="getProgressInfoByDeviceId" resultType="java.util.Map">
		SELECT DISTINCT(STATUS.DEVICE_ID), DEVICES.DEVICE_NAME, GROUPS.GROUP_NAME AS DEVICE_GROUP_NAME, 
		(SELECT COUNT(CONTENT_ID) FROM MI_CDS_DOWNLOAD_STATUS TOTAL_COUNT WHERE PROGRAM_ID = #{programId} AND TOTAL_COUNT.DEVICE_ID = STATUS.DEVICE_ID <if test="activeType != null and !activeType.equals('')"> AND ACTIVE_TYPE = #{activeType}</if>) AS TOTAL,
		(SELECT COUNT(CONTENT_ID) FROM MI_CDS_DOWNLOAD_STATUS TOTAL_COUNT WHERE PROGRAM_ID = #{programId} AND TOTAL_COUNT.DEVICE_ID = STATUS.DEVICE_ID AND TOTAL_COUNT.PROGRESS = '100 %' <if test="activeType != null and !activeType.equals('')"> AND ACTIVE_TYPE = #{activeType}</if>) AS COMPLETE
		FROM MI_CDS_DOWNLOAD_STATUS STATUS
		LEFT JOIN MI_DMS_INFO_DEVICE DEVICES ON STATUS.DEVICE_ID = DEVICES.DEVICE_ID
		LEFT JOIN MI_DMS_INFO_GROUP GROUPS ON STATUS.GROUP_ID = GROUPS.GROUP_ID
		WHERE GROUP_NAME IS NOT NULL
		<if test="activeType != null and !activeType.equals('')">
			AND ACTIVE_TYPE = #{activeType}
		</if>
		AND STATUS.DEVICE_ID IN
		<foreach collection="deviceList" item="deviceId" open="(" separator="," close=")">
			#{deviceId}
		</foreach>
		
	</select>

	<delete id="deleteDownloadStatusWithoutContentIdsByDeviceId">
		DELETE FROM MI_CDS_DOWNLOAD_STATUS
		WHERE DEVICE_ID = #{deviceId}
		<foreach collection="contentIds" item="contentId" open="AND (" separator=" OR " close=")">
			CONTENT_ID != #{contentId}
		</foreach>
	</delete>

	<delete id="deleteDownloadStatusWithContentIdsByDeviceIdAndActiveType">
		DELETE FROM MI_CDS_DOWNLOAD_STATUS
		WHERE DEVICE_ID = #{deviceId} AND active_type = #{activeType}
		<if test="contentIds != null and contentIds.size() > 0">
			<foreach collection="contentIds" item="contentId" open="AND (" separator=" OR " close=")">
				CONTENT_ID = #{contentId}
			</foreach>
		</if>
	</delete>

	<select id="getProgramStatusByProgramId" resultType="com.samsung.magicinfo.framework.monitoring.entity.ProgramStatus">
		SELECT * FROM MI_CDS_MAP_PROGRAM_DEVICE_STATUS
		WHERE PROGRAM_ID = #{programId}
	</select>

	<select id="getProgramStatusByDeviceId" resultType="com.samsung.magicinfo.framework.monitoring.entity.ProgramStatus">
		SELECT * FROM MI_CDS_MAP_PROGRAM_DEVICE_STATUS
		WHERE DEVICE_ID = #{deviceId}
	</select>

	<!-- DF211004-00267 -->
	<select id="getStatusByProgramIdAndDeviceId" resultType="java.lang.String">
		SELECT STATUS FROM MI_CDS_MAP_PROGRAM_DEVICE_STATUS
		WHERE DEVICE_ID = #{deviceId} AND PROGRAM_ID = #{programId}
	</select>

	<select id="getProgramStatusDetailByDeviceId" resultType="com.samsung.magicinfo.framework.monitoring.entity.ProgramStatusDetail">
		SELECT * FROM MI_CDS_DOWNLOAD_STATUS_DETAIL
		WHERE DEVICE_ID = #{deviceId}
	</select>

	<delete id="deleteDownloadStatusWithoutDeviceIdNProgramId">
		DELETE FROM MI_CDS_MAP_PROGRAM_DEVICE_STATUS
		WHERE DEVICE_ID != #{deviceId} AND PROGRAM_ID != #{programId}
	</delete>

	<insert id="addDownloadStatusWithProgramIdNGroupId">
		INSERT INTO MI_CDS_DOWNLOAD_STATUS (DEVICE_ID, CONTENT_ID, PROGRESS, ACTIVE_TYPE, PROGRAM_ID, GROUP_ID)
		VALUES (#{deviceId}, #{contentId}, #{progress}, #{activeType}, #{programId}, #{groupId})
	</insert>

	<update id="updateStatusByAllTypes">
		UPDATE MI_CDS_DOWNLOAD_STATUS SET PROGRESS = #{progress} WHERE DEVICE_ID = #{deviceId} AND CONTENT_ID = #{contentId}
	</update>

	<select id="getDownloadStatusesByProgramId" resultType="com.samsung.magicinfo.framework.scheduler.entity.DownloadStatusesVO">
		SELECT STATUS.STATUS, STATUS.DEVICE_ID, DOWNLOADS.CONTENT_ID, DOWNLOADS.PROGRESS, DOWNLOADS.ACTIVE_TYPE
		FROM MI_CDS_MAP_PROGRAM_DEVICE_STATUS status
		LEFT JOIN MI_CDS_DOWNLOAD_STATUS DOWNLOADS ON STATUS.DEVICE_ID = DOWNLOADS.DEVICE_ID
		WHERE STATUS.PROGRAM_ID = #{programId}
	</select>
</mapper>
			