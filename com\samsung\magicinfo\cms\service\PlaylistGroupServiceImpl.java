package com.samsung.magicinfo.cms.service;

import com.samsung.common.exception.ExceptionCode;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.cms.model.PlaylistGroupResource;
import com.samsung.magicinfo.framework.common.DAOFactory;
import com.samsung.magicinfo.framework.common.LeftMenuGroupTreeDao;
import com.samsung.magicinfo.framework.content.dao.ContentDao;
import com.samsung.magicinfo.framework.content.dao.PlaylistDao;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.content.entity.PlaylistLog;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.playlist.manager.common.PlaylistInterface;
import com.samsung.magicinfo.framework.playlist.manager.common.PlaylistLogInterface;
import com.samsung.magicinfo.framework.user.entity.User;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("PlaylistGroupService")
@Transactional
public class PlaylistGroupServiceImpl implements PlaylistGroupService {
   protected final Log logger = LogFactory.getLog(this.getClass());
   UserContainer userContainer = SecurityUtils.getUserContainer();
   PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl("PREMIUM");
   ContentInfo cInfo = ContentInfoImpl.getInstance();
   PlaylistLog pLog = new PlaylistLog();
   PlaylistLogInterface logInfo = DAOFactory.getPlaylistLogInfoImpl("PREMIUM");
   PlaylistDao pDao = new PlaylistDao();
   ContentDao contentTreeDao = new ContentDao();
   LeftMenuGroupTreeDao treeDao = new LeftMenuGroupTreeDao();
   PlaylistInfo playlistGroupDao = PlaylistInfoImpl.getInstance();

   public PlaylistGroupServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public ResponseBody listDefaultGroup() throws Exception {
      ResponseBody responsebody = new ResponseBody();

      try {
         User user = SecurityUtils.getLoginUser();
         String userId = null;
         if (user == null) {
            throw new AccessDeniedException("no userId");
         } else {
            userId = user.getUser_id();
            List lists = null;
            ArrayList groupList = new ArrayList();
            lists = this.playlistGroupDao.getGroupList(userId);
            if (lists != null) {
               for(int i = 0; i < lists.size(); ++i) {
                  Group tmp = (Group)lists.get(i);
                  if (tmp.getP_group_id() <= 0L) {
                     PlaylistGroupResource groupResource = new PlaylistGroupResource();
                     groupResource.setCreateDate(tmp.getCreate_date());
                     groupResource.setCreatorId(tmp.getCreator_id());
                     groupResource.setGroupId(tmp.getGroup_id());
                     groupResource.setGroupDepth(tmp.getGroup_depth());
                     groupResource.setGroupName(tmp.getGroup_name());
                     groupResource.setIndex(tmp.getIndex());
                     groupResource.setOrganizationId(tmp.getOrganization_id());
                     groupResource.setParentGroupId(tmp.getP_group_id());
                     groupList.add(groupResource);
                  }
               }
            }

            responsebody.setItems(groupList);
            responsebody.setStatus("Success");
            return responsebody;
         }
      } catch (SQLException var9) {
         this.logger.error("", var9);
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var9.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public ResponseBody getGroup(String groupId) throws Exception {
      ResponseBody responsebody = new ResponseBody();
      long id = Long.valueOf(groupId);
      Group group = null;

      try {
         group = this.playlistGroupDao.getGroupInfo(id);
         PlaylistGroupResource groupResource = new PlaylistGroupResource();
         groupResource.setCreateDate(group.getCreate_date());
         groupResource.setCreatorId(group.getCreator_id());
         groupResource.setGroupId(group.getGroup_id());
         groupResource.setGroupDepth(group.getGroup_depth());
         groupResource.setGroupName(group.getGroup_name());
         groupResource.setIndex(group.getIndex());
         groupResource.setOrganizationId(group.getOrganization_id());
         groupResource.setParentGroupId(group.getP_group_id());
         responsebody.setItems(groupResource);
         responsebody.setStatus("Success");
      } catch (SQLException var7) {
         this.logger.error("", var7);
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var7.getMessage());
      }

      return responsebody;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public ResponseBody listChildGroup(String groupId) throws Exception {
      ResponseBody responsebody = new ResponseBody();
      List lists = null;

      try {
         List groupList = new ArrayList();
         User user = SecurityUtils.getLoginUser();
         String userId = null;
         if (user == null) {
            throw new AccessDeniedException("no userId");
         }

         userId = user.getUser_id();
         if (groupId != null && !groupId.equals("0") && !groupId.equals("")) {
            lists = this.playlistGroupDao.getChildGroupList(Long.valueOf(groupId), false, userId);
            if (lists != null) {
               for(int i = 0; i < lists.size(); ++i) {
                  Group tmp = (Group)lists.get(i);
                  PlaylistGroupResource groupResource = new PlaylistGroupResource();
                  groupResource.setCreateDate(tmp.getCreate_date());
                  groupResource.setCreatorId(tmp.getCreator_id());
                  groupResource.setGroupId(tmp.getGroup_id());
                  groupResource.setGroupDepth(tmp.getGroup_depth());
                  groupResource.setGroupName(tmp.getGroup_name());
                  groupResource.setIndex(tmp.getIndex());
                  groupResource.setOrganizationId(tmp.getOrganization_id());
                  groupResource.setParentGroupId(tmp.getP_group_id());
                  groupList.add(groupResource);
               }
            }
         }

         responsebody.setItems(groupList);
         responsebody.setStatus("Success");
      } catch (SQLException var10) {
         this.logger.error("", var10);
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var10.getMessage());
      }

      return responsebody;
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public ResponseBody createGroup(Group group) throws Exception {
      ResponseBody responsebody = new ResponseBody();
      String table = "MI_CMS_INFO_PLAYLIST_GROUP";
      String menu = "MyPlaylist";

      try {
         String parentGroupId = group.getP_group_id().toString();
         String newName = group.getGroup_name();
         long depth = this.treeDao.get_GroupDepth(parentGroupId, table);
         Long root_group_id = null;
         root_group_id = this.contentTreeDao.getRoot_GroupId(parentGroupId);
         User user = SecurityUtils.getLoginUser();
         String userId = null;
         if (user != null) {
            userId = user.getUser_id();
            boolean var12 = this.treeDao.checkNodeName(newName, "", Integer.parseInt(parentGroupId), table, "ContentPlaylist", userId);
            if (var12) {
               responsebody.setErrorCode(ExceptionCode.RES904[0]);
               responsebody.setErrorMessage(ExceptionCode.RES904[2]);
               responsebody.setStatus("Fail");
               return responsebody;
            } else {
               long groupId = (long)this.treeDao.setGroupTreeCreate(menu, table, parentGroupId, newName, depth + "", root_group_id, userId);
               group = this.playlistGroupDao.getGroupInfo(groupId);
               PlaylistGroupResource groupResource = new PlaylistGroupResource();
               groupResource.setCreateDate(group.getCreate_date());
               groupResource.setCreatorId(group.getCreator_id());
               groupResource.setGroupId(group.getGroup_id());
               groupResource.setGroupDepth(group.getGroup_depth());
               groupResource.setGroupName(group.getGroup_name());
               groupResource.setIndex(group.getIndex());
               groupResource.setOrganizationId(group.getOrganization_id());
               groupResource.setParentGroupId(group.getP_group_id());
               responsebody.setItems(groupResource);
               responsebody.setStatus("Success");
               return responsebody;
            }
         } else {
            throw new AccessDeniedException("no userId");
         }
      } catch (SQLException var16) {
         this.logger.error("", var16);
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var16.getMessage());
         return responsebody;
      }
   }
}
