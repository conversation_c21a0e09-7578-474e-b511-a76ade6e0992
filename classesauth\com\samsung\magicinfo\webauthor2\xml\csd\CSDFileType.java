package com.samsung.magicinfo.webauthor2.xml.csd;

import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@XmlRootElement(name = "Content")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CSDFileType", propOrder = {"user", "title", "displayWidth", "displayHeight", "playerType", "playerTypeVersion", "category", "dirName", "meta", "isShareContents", "promThumbailType", "fileList"})
public class CSDFileType {
  @XmlAttribute(name = "cid")
  private String cid;
  
  @XmlElement(name = "User")
  private String user;
  
  @XmlElement(name = "Title")
  private String title;
  
  @XmlElement(name = "DisplayWidth")
  private String displayWidth;
  
  @XmlElement(name = "DisplayHeight")
  private String displayHeight;
  
  @XmlElement(name = "PlayerType")
  private String playerType;
  
  @XmlElement(name = "PlayerTypeVersion")
  private String playerTypeVersion;
  
  @XmlElement(name = "Category")
  private String category;
  
  @XmlElement(name = "DirName")
  private String dirName;
  
  @XmlElement(name = "Meta")
  private String meta;
  
  @XmlElement(name = "IsShareContents")
  private String isShareContents;
  
  @XmlElement(name = "PROMThumbnailType")
  private String promThumbailType;
  
  @XmlElement(name = "TransferFile")
  private List<CSDTransferFileType> fileList;
  
  public String getCid() {
    return this.cid;
  }
  
  public void setCid(String cid) {
    this.cid = cid;
  }
  
  public String getUser() {
    return this.user;
  }
  
  public void setUser(String user) {
    this.user = user;
  }
  
  public String getTitle() {
    return this.title;
  }
  
  public void setTitle(String title) {
    this.title = title;
  }
  
  public String getDisplayWidth() {
    return this.displayWidth;
  }
  
  public void setDisplayWidth(String displayWidth) {
    this.displayWidth = displayWidth;
  }
  
  public String getDisplayHeight() {
    return this.displayHeight;
  }
  
  public void setDisplayHeight(String displayHeight) {
    this.displayHeight = displayHeight;
  }
  
  public String getPlayerType() {
    return this.playerType;
  }
  
  public void setPlayerType(String playerType) {
    this.playerType = playerType;
  }
  
  public String getPlayerTypeVersion() {
    return this.playerTypeVersion;
  }
  
  public void setPlayerTypeVersion(String playerTypeVersion) {
    this.playerTypeVersion = playerTypeVersion;
  }
  
  public String getCategory() {
    return this.category;
  }
  
  public void setCategory(String category) {
    this.category = category;
  }
  
  public String getDirName() {
    return this.dirName;
  }
  
  public void setDirName(String dirName) {
    this.dirName = dirName;
  }
  
  public String getMeta() {
    return this.meta;
  }
  
  public void setMeta(String meta) {
    this.meta = meta;
  }
  
  public String getIsShareContents() {
    return this.isShareContents;
  }
  
  public void setIsShareContents(String isShareContents) {
    this.isShareContents = isShareContents;
  }
  
  public String getPromThumbailType() {
    return this.promThumbailType;
  }
  
  public void setPromThumbailType(String promThumbailType) {
    this.promThumbailType = promThumbailType;
  }
  
  public List<CSDTransferFileType> getFileList() {
    return this.fileList;
  }
  
  public void setFileList(List<CSDTransferFileType> fileList) {
    this.fileList = fileList;
  }
}
