package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.ConvertTable;
import com.samsung.magicinfo.webauthor2.repository.OpenAPIConvertTableRepository;
import com.samsung.magicinfo.webauthor2.repository.model.datalink.ConvertTableData;
import java.util.List;
import javax.inject.Inject;
import org.springframework.stereotype.Service;

@Service
public class ConvertTableServiceImpl implements ConvertTableService {
  private OpenAPIConvertTableRepository repository;
  
  @Inject
  public ConvertTableServiceImpl(OpenAPIConvertTableRepository repository) {
    this.repository = repository;
  }
  
  public List<ConvertTable> getConvertTableList() {
    List<ConvertTableData> convertTablesDataList = this.repository.getConvertTableDataList();
    return ConvertTable.fromData(convertTablesDataList);
  }
  
  public String addConvertTable(ConvertTable convertTable) {
    convertTable.updateDate();
    convertTable.updateConvertDataFieldsInList();
    return this.repository.addConvertTableData(convertTable.toData());
  }
  
  public String delete(String convertTableName) {
    return this.repository.deleteConvertTable(convertTableName);
  }
  
  public String modify(ConvertTable oldConvertTable, ConvertTable newConvertTable) {
    newConvertTable.setCreatedDate(oldConvertTable.getCreatedDate());
    newConvertTable.updateConvertDataFieldsInList();
    oldConvertTable.updateConvertDataFieldsInList();
    return this.repository.modifyConvertTableData(oldConvertTable.toData(), newConvertTable.toData());
  }
}
