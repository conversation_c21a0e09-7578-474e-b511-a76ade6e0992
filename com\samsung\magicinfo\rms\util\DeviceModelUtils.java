package com.samsung.magicinfo.rms.util;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.rms.model.Dehumidify;
import com.samsung.magicinfo.rms.model.DeviceDisplayConfSubResource;
import com.samsung.magicinfo.rms.model.DeviceDisplayCustomLogoResource;
import com.samsung.magicinfo.rms.model.DimmingBrightnessOutput;
import com.samsung.magicinfo.rms.model.DimmingEcoSensor;
import com.samsung.magicinfo.rms.model.DimmingSunriseSunset;
import com.samsung.magicinfo.rms.model.DimmingSunriseSunsetTimes;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import org.apache.logging.log4j.Logger;

public class DeviceModelUtils {
   public static final String DELIMITER_SEMICOLON = ";";
   public static final String DELIMITER_COLON = ":";
   public static final String REG_EXP_NUMBER = "(^[0-9]*$)";
   public static final String REG_EXP_FLOAT = "\\d*([.]?\\d+)";
   protected static Logger logger = LoggingManagerV2.getLogger(DeviceModelUtils.class);
   public static String PERIOD_DEVICE_ERROR_WARNING = "ERROR_WARNING";
   public static String PERIOD_RM_MONITORING = "RM_MONITORING";
   public static String OFFSET_RM_MONITORING_DATA = "OFFSET_RM_MONITORING_DATA";

   public DeviceModelUtils() {
      super();
   }

   public static DeviceDisplayConfSubResource setDisplayMntAutoResource(String mntAuto) {
      DeviceDisplayConfSubResource sub = null;

      try {
         if (mntAuto != null) {
            String[] mntAutoArr = mntAuto.split(";");
            if (mntAutoArr != null && mntAutoArr.length == 8) {
               sub = new DeviceDisplayConfSubResource();
               sub.setMntAutoIsEnable("2");
               sub.setMntAutoMaxTime(change12Hto24H(mntAutoArr[0], mntAutoArr[2]) + ":" + StrUtils.strZeroPlus(Integer.parseInt(mntAutoArr[1])));
               sub.setMntAutoMaxValue(mntAutoArr[3]);
               sub.setMntAutoMinTime(change12Hto24H(mntAutoArr[4], mntAutoArr[6]) + ":" + StrUtils.strZeroPlus(Integer.parseInt(mntAutoArr[5])));
               sub.setMntAutoMinValue(mntAutoArr[7]);
            } else if (mntAutoArr != null && mntAutoArr.length == 9) {
               sub = new DeviceDisplayConfSubResource();
               sub.setMntAutoIsEnable(mntAutoArr[0]);
               sub.setMntAutoMaxTime(change12Hto24H(mntAutoArr[1], mntAutoArr[3]) + ":" + StrUtils.strZeroPlus(Integer.parseInt(mntAutoArr[2])));
               sub.setMntAutoMaxValue(mntAutoArr[4]);
               sub.setMntAutoMinTime(change12Hto24H(mntAutoArr[5], mntAutoArr[7]) + ":" + StrUtils.strZeroPlus(Integer.parseInt(mntAutoArr[6])));
               sub.setMntAutoMinValue(mntAutoArr[8]);
            }
         }

         return sub;
      } catch (Exception var3) {
         logger.error(var3.getMessage());
         return null;
      }
   }

   public static DeviceDisplayConfSubResource setNewDisplayMntAutoResource(String mntAuto) {
      DeviceDisplayConfSubResource sub = null;

      try {
         if (mntAuto != null) {
            String[] mntAutoArr = mntAuto.split(";");
            if (mntAutoArr != null && mntAutoArr.length == 8) {
               sub = new DeviceDisplayConfSubResource();
               sub.setMntAutoIsEnable("2");
               sub.setMntAutoMaxTime(newChange24Hto12H(mntAutoArr[0]) + ":" + StrUtils.strZeroPlus(Integer.parseInt(mntAutoArr[1])) + ("1".equals(mntAutoArr[2]) ? "AM" : "PM"));
               sub.setMntAutoMaxValue(mntAutoArr[3]);
               sub.setMntAutoMinTime(newChange24Hto12H(mntAutoArr[4]) + ":" + StrUtils.strZeroPlus(Integer.parseInt(mntAutoArr[5])) + ("1".equals(mntAutoArr[6]) ? "AM" : "PM"));
               sub.setMntAutoMinValue(mntAutoArr[7]);
            } else if (mntAutoArr != null && mntAutoArr.length == 9) {
               sub = new DeviceDisplayConfSubResource();
               sub.setMntAutoIsEnable(mntAutoArr[0]);
               sub.setMntAutoMaxTime(newChange24Hto12H(mntAutoArr[1]) + ":" + StrUtils.strZeroPlus(Integer.parseInt(mntAutoArr[2])) + ("1".equals(mntAutoArr[3]) ? "AM" : "PM"));
               sub.setMntAutoMaxValue(mntAutoArr[4]);
               sub.setMntAutoMinTime(newChange24Hto12H(mntAutoArr[5]) + ":" + StrUtils.strZeroPlus(Integer.parseInt(mntAutoArr[6])) + ("1".equals(mntAutoArr[7]) ? "AM" : "PM"));
               sub.setMntAutoMinValue(mntAutoArr[8]);
            }
         }

         return sub;
      } catch (Exception var3) {
         logger.error(var3.getMessage());
         return null;
      }
   }

   public static String getDisplayMntAutoString(DeviceDisplayConfSubResource sub) {
      String result = null;

      try {
         if (sub != null && sub.isMntAutoChanged()) {
            String enable = sub.getMntAutoIsEnable();
            String maxValue = sub.getMntAutoMaxValue();
            String minValue = sub.getMntAutoMinValue();
            String maxTime = change24Hto12H(sub.getMntAutoMaxTime());
            String minTime = change24Hto12H(sub.getMntAutoMinTime());
            if (enable != null && !enable.equals("2")) {
               result = enable + ";" + maxTime + ";" + maxValue + ";" + minTime + ";" + minValue;
            } else {
               result = maxTime + ";" + maxValue + ";" + minTime + ";" + minValue;
            }
         }

         return result;
      } catch (Exception var7) {
         logger.error(var7.getMessage());
         return null;
      }
   }

   public static String getNewDisplayMntAutoString(DeviceDisplayConfSubResource sub) {
      String result = null;

      try {
         if (sub != null && sub.isMntAutoChanged()) {
            String enable = sub.getMntAutoIsEnable();
            String maxValue = sub.getMntAutoMaxValue();
            String minValue = sub.getMntAutoMinValue();
            String maxTime = changeMntTimeConverter(sub.getMntAutoMaxTime());
            String minTime = changeMntTimeConverter(sub.getMntAutoMinTime());
            if (enable != null && !enable.equals("2")) {
               result = enable + ";" + maxTime + ";" + maxValue + ";" + minTime + ";" + minValue;
            } else {
               result = maxTime + ";" + maxValue + ";" + minTime + ";" + minValue;
            }
         }

         return result;
      } catch (Exception var7) {
         logger.error(var7.getMessage());
         return null;
      }
   }

   private static String changeMntTimeConverter(String time) {
      if (time != null && !time.equals("") && time.length() >= 5) {
         String[] arr = time.split(":");
         if (arr.length < 2) {
            logger.error("changeMntTimeConverter error " + time);
            return "0;0;1";
         } else {
            new Integer("0");
            String mm = "";
            String ampm = "";

            Integer hh;
            try {
               hh = Integer.parseInt(arr[0]);
               mm = arr[1].substring(0, arr[1].length() - 2);
               mm = mm.trim();
               ampm = arr[1].substring(arr[1].length() - 2, arr[1].length()).toUpperCase();
            } catch (Exception var6) {
               logger.error("changeMntTimeConverter error " + time);
               logger.error(var6.getMessage());
               return "0;0;1";
            }

            return hh + ";" + Integer.parseInt(mm) + ";" + ("AM".equals(ampm) ? "1" : "0");
         }
      } else {
         logger.error("changeMntTimeConverter error " + time);
         return "0;0;1";
      }
   }

   public static DeviceDisplayConfSubResource setDisplayMntPixelShiftResource(String pixelShift) {
      DeviceDisplayConfSubResource sub = null;

      try {
         if (pixelShift != null) {
            sub = new DeviceDisplayConfSubResource();
            String[] pixelShiftArr = DeviceUtils.splitter(pixelShift, 4);
            if (pixelShiftArr != null && pixelShiftArr.length == 4) {
               sub.setPixelShiftEnable(pixelShiftArr[0]);
               sub.setPixelShiftH(pixelShiftArr[1]);
               sub.setPixelShiftV(pixelShiftArr[2]);
               sub.setPixelShiftTime(pixelShiftArr[3]);
            }
         }

         return sub;
      } catch (Exception var3) {
         logger.error(var3.getMessage());
         return null;
      }
   }

   public static String getDisplayMntPixelShiftString(DeviceDisplayConfSubResource sub) {
      String result = null;

      try {
         if (sub != null && sub.isPixelShiftChanged()) {
            result = "";
            result = result + sub.getPixelShiftEnable() + ";" + sub.getPixelShiftH() + ";";
            result = result + sub.getPixelShiftV() + ";" + sub.getPixelShiftTime();
         }

         return result;
      } catch (Exception var3) {
         logger.error(var3.getMessage());
         return null;
      }
   }

   public static DeviceDisplayConfSubResource setDisplayMntSafetyScreenTimerResource(String safetyScrTimer) {
      DeviceDisplayConfSubResource sub = null;

      try {
         if (safetyScrTimer != null) {
            sub = new DeviceDisplayConfSubResource();
            String[] safetyScreenTimerArr = DeviceUtils.splitter(safetyScrTimer, 7);
            String timerOptions = "0";
            if (safetyScreenTimerArr[0].equals("0")) {
               timerOptions = "0";
            } else if (!safetyScreenTimerArr[0].equals("3") && !safetyScreenTimerArr[0].equals("4") && !safetyScreenTimerArr[0].equals("5") && !safetyScreenTimerArr[0].equals("6") && !safetyScreenTimerArr[0].equals("9") && !safetyScreenTimerArr[0].equals("10") && !safetyScreenTimerArr[0].equals("16") && !safetyScreenTimerArr[0].equals("17")) {
               if (safetyScreenTimerArr[0].equals("131") || safetyScreenTimerArr[0].equals("132") || safetyScreenTimerArr[0].equals("133") || safetyScreenTimerArr[0].equals("134") || safetyScreenTimerArr[0].equals("144") || safetyScreenTimerArr[0].equals("145")) {
                  timerOptions = "2";
               }
            } else {
               timerOptions = "1";
            }

            sub.setScrSafeTimer(timerOptions);
            sub.setScrSafeMode(safetyScreenTimerArr[0]);
            if (timerOptions.equals("1")) {
               sub.setScrSafePeriod(safetyScreenTimerArr[1]);
               sub.setScrSafeTime(safetyScreenTimerArr[2]);
            } else if (timerOptions.equals("2")) {
               sub.setScrSafeStartTime(change12Hto24H(safetyScreenTimerArr[1], safetyScreenTimerArr[3]) + ":" + StrUtils.strZeroPlus(Integer.parseInt(safetyScreenTimerArr[2])));
               sub.setScrSafeEndTime(change12Hto24H(safetyScreenTimerArr[4], safetyScreenTimerArr[6]) + ":" + StrUtils.strZeroPlus(Integer.parseInt(safetyScreenTimerArr[5])));
            }
         }

         return sub;
      } catch (Exception var4) {
         logger.error(var4.getMessage());
         return null;
      }
   }

   public static DeviceDisplayConfSubResource setNewDisplayMntSafetyScreenTimerResource(String safetyScrTimer) {
      DeviceDisplayConfSubResource sub = null;

      try {
         if (safetyScrTimer != null) {
            sub = new DeviceDisplayConfSubResource();
            String[] safetyScreenTimerArr = DeviceUtils.splitter(safetyScrTimer, 7);
            String timerOptions = "0";
            if (safetyScreenTimerArr[0].equals("0")) {
               timerOptions = "0";
            } else if (!safetyScreenTimerArr[0].equals("3") && !safetyScreenTimerArr[0].equals("4") && !safetyScreenTimerArr[0].equals("5") && !safetyScreenTimerArr[0].equals("6") && !safetyScreenTimerArr[0].equals("9") && !safetyScreenTimerArr[0].equals("10") && !safetyScreenTimerArr[0].equals("16") && !safetyScreenTimerArr[0].equals("17")) {
               if (safetyScreenTimerArr[0].equals("131") || safetyScreenTimerArr[0].equals("132") || safetyScreenTimerArr[0].equals("133") || safetyScreenTimerArr[0].equals("134") || safetyScreenTimerArr[0].equals("144") || safetyScreenTimerArr[0].equals("145")) {
                  timerOptions = "2";
               }
            } else {
               timerOptions = "1";
            }

            sub.setScrSafeTimer(timerOptions);
            sub.setScrSafeMode(safetyScreenTimerArr[0]);
            if (timerOptions.equals("1")) {
               sub.setScrSafePeriod(safetyScreenTimerArr[1]);
               sub.setScrSafeTime(safetyScreenTimerArr[2]);
            } else if (timerOptions.equals("2")) {
               sub.setScrSafeStartTime(newChange24Hto12H(safetyScreenTimerArr[1]) + ":" + StrUtils.strZeroPlus(Integer.parseInt(safetyScreenTimerArr[2])) + ("1".equals(safetyScreenTimerArr[3]) ? "AM" : "PM"));
               sub.setScrSafeEndTime(newChange24Hto12H(safetyScreenTimerArr[4]) + ":" + StrUtils.strZeroPlus(Integer.parseInt(safetyScreenTimerArr[5])) + ("1".equals(safetyScreenTimerArr[6]) ? "AM" : "PM"));
            }
         }

         return sub;
      } catch (Exception var4) {
         logger.error(var4.getMessage());
         return null;
      }
   }

   public static String getDisplayMntSafetyScreenTimerString(DeviceDisplayConfSubResource sub) {
      String result = null;

      try {
         if (sub != null && sub.isScrSafeChanged()) {
            if (sub.getScrSafeTimer().equals("0")) {
               result = "0;0;0";
            } else if (sub.getScrSafeTimer().equals("1")) {
               result = sub.getScrSafeMode() + ";" + sub.getScrSafePeriod() + ";" + sub.getScrSafeTime();
            } else if (sub.getScrSafeTimer().equals("2")) {
               result = sub.getScrSafeMode() + ";" + change24Hto12H(sub.getScrSafeStartTime()) + ";" + change24Hto12H(sub.getScrSafeEndTime());
            }
         }

         return result;
      } catch (Exception var3) {
         logger.error(var3.getMessage());
         return null;
      }
   }

   public static String getNewDisplayMntSafetyScreenTimerString(DeviceDisplayConfSubResource sub) {
      String result = null;

      try {
         if (sub != null && sub.isScrSafeChanged()) {
            if (sub.getScrSafeTimer().equals("0")) {
               result = "0;0;0";
            } else if (sub.getScrSafeTimer().equals("1")) {
               result = sub.getScrSafeMode() + ";" + sub.getScrSafePeriod() + ";" + sub.getScrSafeTime();
            } else if (sub.getScrSafeTimer().equals("2")) {
               result = sub.getScrSafeMode() + ";" + changeMntTimeConverter(sub.getScrSafeStartTime()) + ";" + changeMntTimeConverter(sub.getScrSafeEndTime());
            }
         }

         return result;
      } catch (Exception var3) {
         logger.error(var3.getMessage());
         return null;
      }
   }

   public static DeviceDisplayConfSubResource setDisplayAdvOsdDisplayTypeResource(String osdDisplayType) {
      DeviceDisplayConfSubResource sub = null;

      try {
         if (osdDisplayType != null) {
            sub = new DeviceDisplayConfSubResource();
            String[] arr = osdDisplayType.split(";");
            if (arr != null && arr.length == 4) {
               sub.setAdvSourceOsd(arr[0]);
               sub.setAdvNotOptimimumModeOsd(arr[1]);
               sub.setAdvNoSignalOsd(arr[2]);
               sub.setAdvMdcOsd(arr[3]);
            }

            if (arr != null && arr.length == 5) {
               sub.setAdvSourceOsd(arr[0]);
               sub.setAdvNotOptimimumModeOsd(arr[1]);
               sub.setAdvNoSignalOsd(arr[2]);
               sub.setAdvMdcOsd(arr[3]);
               sub.setAdvDownloadStatusOsd(arr[4]);
            }
         }

         return sub;
      } catch (Exception var3) {
         logger.error(var3.getMessage());
         return null;
      }
   }

   public static String getDisplayAdvOsdDisplayTypeString(DeviceDisplayConfSubResource sub) {
      String result = null;

      try {
         if (sub != null && sub.getAdvOsdChanged() != null) {
            result = sub.getAdvOsdChanged() + ";" + sub.getAdvOsdChangedStatus();
         }

         return result;
      } catch (Exception var3) {
         logger.error(var3.getMessage());
         return null;
      }
   }

   public static DeviceDisplayConfSubResource setDisplayAutoSourceSwitchResource(String autoSourceSwitch) {
      DeviceDisplayConfSubResource sub = null;

      try {
         if (autoSourceSwitch != null) {
            sub = new DeviceDisplayConfSubResource();
            String[] arr = autoSourceSwitch.split(";");
            if (arr.length >= 4) {
               sub.setAutoSourceSwitching(arr[0]);
               sub.setAutoSourceRecovery(arr[1]);
               sub.setAutoSourcePrimary(arr[2]);
               sub.setAutoSourceSecondary(arr[3]);
            }

            if (arr.length == 5) {
               sub.setAutoSourceDefaultInput(arr[4]);
            }
         }

         return sub;
      } catch (Exception var3) {
         logger.error(var3.getMessage());
         return null;
      }
   }

   public static String getDisplayAutoSourceSwitchString(DeviceDisplayConfSubResource sub) {
      String result = null;

      try {
         if (sub != null && sub.isAutoSourceChanged()) {
            result = sub.getAutoSourceSwitching() + ";" + sub.getAutoSourceRecovery() + ";" + sub.getAutoSourcePrimary() + ";" + sub.getAutoSourceSecondary();
            if (null != sub.getAutoSourceDefaultInput() && !sub.getAutoSourceDefaultInput().isEmpty()) {
               result = result + ";" + sub.getAutoSourceDefaultInput();
            }
         }

         return result;
      } catch (Exception var3) {
         logger.error(var3.getMessage());
         return null;
      }
   }

   public static DeviceDisplayCustomLogoResource setDisplayCustomLogoResource(String customLogo) {
      DeviceDisplayCustomLogoResource resource = null;

      try {
         if (customLogo != null) {
            String[] arr = customLogo.split(";");
            if (arr != null && arr.length >= 2) {
               resource = new DeviceDisplayCustomLogoResource();
               resource.setType(arr[0]);
               resource.setDisplayTime(arr[1]);
               resource.setFileType(arr[2]);
            }
         }

         return resource;
      } catch (Exception var3) {
         logger.error(var3.getMessage());
         return null;
      }
   }

   public static String getDisplayCustomLogoString(DeviceDisplayCustomLogoResource resource) {
      String result = null;

      try {
         if (resource != null && resource.isChanged()) {
            result = resource.getType() + ";" + resource.getDisplayTime() + ";" + resource.getFileType();
         }

         return result;
      } catch (Exception var3) {
         logger.error(var3.getMessage());
         return null;
      }
   }

   public static DeviceDisplayConfSubResource setDisplayWebBrowserUrlResource(String webBrowserUrl) {
      DeviceDisplayConfSubResource sub = null;

      try {
         if (webBrowserUrl != null) {
            sub = new DeviceDisplayConfSubResource();
            String[] arr = webBrowserUrl.split(";");
            if (arr != null && (arr.length == 3 || arr.length == 4)) {
               sub.setWebBrowserInterval(arr[0]);
               sub.setWebBrowserZoom(arr[1]);
               sub.setWebBrowserHomepage(arr[2]);
               if (arr.length == 4) {
                  sub.setWebBrowserPageurl(arr[3]);
               } else {
                  sub.setWebBrowserPageurl("");
               }
            }
         }

         return sub;
      } catch (Exception var3) {
         logger.error(var3.getMessage());
         return null;
      }
   }

   public static String getDisplayWebBrowserUrlString(DeviceDisplayConfSubResource sub) {
      String result = null;

      try {
         if (sub != null && sub.isWebBrowserChanged()) {
            result = sub.getWebBrowserInterval() + ";" + sub.getWebBrowserZoom() + ";" + sub.getWebBrowserHomepage() + ";" + sub.getWebBrowserPageurl();
         }

         return result;
      } catch (Exception var3) {
         logger.error(var3.getMessage());
         return null;
      }
   }

   public static String change24Hto12H(String time) {
      try {
         String[] arr = time.split(":");
         if (arr.length == 2) {
            int hour = Integer.parseInt(arr[0]);
            int min = Integer.parseInt(arr[1]);
            String ampm = "1";
            if (hour >= 12) {
               if (hour > 12) {
                  hour -= 12;
               }

               if (hour == 0) {
                  hour = 12;
               }

               ampm = "0";
            }

            return hour + ";" + min + ";" + ampm;
         } else {
            return "0;0;1";
         }
      } catch (Exception var5) {
         return "0;0;1";
      }
   }

   public static String newChange24Hto12H(String hour) {
      int h = Integer.parseInt(hour);
      if (h > 12) {
         h -= 12;
         return "0" + h;
      } else {
         return h == 0 ? "12" : hour;
      }
   }

   public static String change12Hto24H(String hour, String type) {
      int numHour = Integer.parseInt(hour);
      if (type.equals("0") && numHour < 12) {
         numHour += 12;
      }

      return StrUtils.strZeroPlus(numHour);
   }

   public static boolean isSupport(String deviceType, String functionName) {
      Map supportMap = new HashMap();
      supportMap.put("LEDBOX", getSupportMap("LEDBOX"));
      supportMap.put("RLEDBOX", getSupportMap("RLEDBOX"));
      supportMap.put("SIGNAGE", getSupportMap("SIGNAGE"));
      supportMap.put("RSIGNAGE", getSupportMap("RSIGNAGE"));
      if (deviceType != null && !deviceType.equals("")) {
         Map map = (Map)supportMap.get(deviceType);
         return map != null && map.containsKey(functionName) ? (Boolean)map.get(functionName) : true;
      } else {
         return true;
      }
   }

   public static Map getSupportMap(String deviceType) {
      Map supportMap = new HashMap();
      byte var3 = -1;
      switch(deviceType.hashCode()) {
      case -2053337120:
         if (deviceType.equals("LEDBOX")) {
            var3 = 0;
         }
         break;
      case -1488719358:
         if (deviceType.equals("SIGNAGE")) {
            var3 = 2;
         }
         break;
      case -312192656:
         if (deviceType.equals("RSIGNAGE")) {
            var3 = 3;
         }
         break;
      case 2002487986:
         if (deviceType.equals("RLEDBOX")) {
            var3 = 1;
         }
      }

      switch(var3) {
      case 0:
      case 1:
         supportMap.put("basic_volume", false);
         supportMap.put("basic_mute", false);
         supportMap.put("panel_on_time", false);
         supportMap.put("sound_srs", false);
         supportMap.put("sound_mode", false);
         supportMap.put("screen_lamp_schedule", false);
         supportMap.put("safety_screen_run", false);
         supportMap.put("safety_screen_timer", false);
         supportMap.put("pixel_shift", false);
         supportMap.put("lamp_control", false);
         supportMap.put("sound_reset", false);
         break;
      case 2:
      case 3:
         supportMap.put("picture_reset", false);
      }

      return supportMap;
   }

   public static boolean isCheckNumber(String value) {
      return StrUtils.isNotEmpty(value) && Pattern.matches("(^[0-9]*$)", value);
   }

   public static boolean isCheckFloat(String value) {
      return StrUtils.isNotEmpty(value) && Pattern.matches("\\d*([.]?\\d+)", value);
   }

   public static Dehumidify setDehumidify(String dehumidify) {
      Dehumidify resource = null;

      try {
         if (null == dehumidify) {
            return null;
         } else {
            String[] arrValue = dehumidify.split(";");
            if (null != arrValue && arrValue.length >= 2) {
               resource = new Dehumidify();
               if (isCheckNumber(arrValue[0])) {
                  resource.setEnable(Long.parseLong(arrValue[0]));
               }

               String[] arrTime = arrValue[1].split(":");
               if (null != arrTime && arrTime.length >= 2) {
                  if (isCheckNumber(arrTime[0])) {
                     resource.setHour(Long.parseLong(arrTime[0]));
                  }

                  if (isCheckNumber(arrTime[1])) {
                     resource.setMinute(Long.parseLong(arrTime[1]));
                  }

                  return resource;
               } else {
                  return resource;
               }
            } else {
               return null;
            }
         }
      } catch (Exception var4) {
         logger.error(var4.getMessage());
         return null;
      }
   }

   public static DimmingEcoSensor setDimmingEcoSensor(String moString) {
      DimmingEcoSensor resource = null;

      try {
         if (moString != null) {
            String[] arr = moString.split(";");
            if (arr != null && arr.length > 1) {
               resource = new DimmingEcoSensor();
               if (isCheckNumber(arr[0])) {
                  resource.setMaxValue(Long.parseLong(arr[0]));
               }

               if (isCheckNumber(arr[1])) {
                  resource.setMinValue(Long.parseLong(arr[1]));
               }
            }
         }

         return resource;
      } catch (Exception var3) {
         logger.error(var3.getMessage());
         return null;
      }
   }

   public static DimmingSunriseSunset setDimmingSunriseSunset(String moString) {
      DimmingSunriseSunset resource = null;

      try {
         if (moString != null) {
            String[] arr = moString.split(";");
            if (arr != null && arr.length > 2) {
               resource = new DimmingSunriseSunset();
               if (isCheckFloat(arr[0])) {
                  resource.setLatitude(Float.parseFloat(arr[0]));
               }

               if (isCheckFloat(arr[1])) {
                  resource.setLongitude(Float.parseFloat(arr[1]));
               }

               if (isCheckNumber(arr[2])) {
                  resource.setRampTime(Long.parseLong(arr[2]));
               }
            }
         }

         return resource;
      } catch (Exception var3) {
         logger.error(var3.getMessage());
         return null;
      }
   }

   public static List setDimmingSunriseSunsetTimes(String moString) {
      List list = new ArrayList();
      DimmingSunriseSunsetTimes resource = null;

      try {
         if (moString != null) {
            String[] arrList = moString.split(";");
            String[] var4 = arrList;
            int var5 = arrList.length;

            for(int var6 = 0; var6 < var5; ++var6) {
               String dayLine = var4[var6];
               String[] arr = dayLine.split(":");
               if (arr != null && arr.length > 5) {
                  resource = new DimmingSunriseSunsetTimes();
                  if (isCheckNumber(arr[0])) {
                     resource.setMonth(Long.parseLong(arr[0]));
                  }

                  if (isCheckNumber(arr[1])) {
                     resource.setDay(Long.parseLong(arr[1]));
                  }

                  if (isCheckNumber(arr[2])) {
                     resource.setSunriseHour(Long.parseLong(arr[2]));
                  }

                  if (isCheckNumber(arr[3])) {
                     resource.setSunriseMinute(Long.parseLong(arr[3]));
                  }

                  if (isCheckNumber(arr[4])) {
                     resource.setSunsetHour(Long.parseLong(arr[4]));
                  }

                  if (isCheckNumber(arr[5])) {
                     resource.setSunsetMinute(Long.parseLong(arr[5]));
                  }

                  list.add(resource);
               }
            }
         }

         return list;
      } catch (Exception var9) {
         logger.error(var9.getMessage());
         return null;
      }
   }

   public static DimmingBrightnessOutput setDimmingBrightnessOutput(String moString) {
      DimmingBrightnessOutput resource = null;

      try {
         if (moString != null) {
            String[] arr = moString.split(";");
            if (arr != null && arr.length > 2) {
               resource = new DimmingBrightnessOutput();
               if (isCheckNumber(arr[0])) {
                  resource.setDefaultValue(Long.parseLong(arr[0]));
               }

               if (isCheckNumber(arr[1])) {
                  resource.setMaxValue(Long.parseLong(arr[1]));
               }

               if (isCheckNumber(arr[2])) {
                  resource.setMinValue(Long.parseLong(arr[2]));
               }
            }
         }

         return resource;
      } catch (Exception var3) {
         logger.error(var3.getMessage());
         return null;
      }
   }

   public static String convertObjectToMoString(Object resource, String moValueFormat) {
      if (null != resource && null != moValueFormat) {
         String result = null;
         Field[] field = resource.getClass().getDeclaredFields();
         Field[] var4 = field;
         int var5 = field.length;

         for(int var6 = 0; var6 < var5; ++var6) {
            Field f = var4[var6];
            f.setAccessible(true);
            String name = f.getName();
            if (moValueFormat.contains(name)) {
               try {
                  Object objval = f.get(resource) == null ? "" : f.get(resource);
                  String value = String.valueOf(objval);
                  moValueFormat = moValueFormat.replace("[" + name + "]", value);
               } catch (Exception var11) {
                  logger.error(var11);
               }
            }
         }

         if (!moValueFormat.contains("[") && !moValueFormat.contains("]")) {
            result = moValueFormat;
         }

         return result;
      } else {
         return null;
      }
   }
}
