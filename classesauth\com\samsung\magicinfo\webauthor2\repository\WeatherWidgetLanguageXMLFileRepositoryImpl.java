package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.model.weather.Language;
import com.samsung.magicinfo.webauthor2.model.weather.Languages;
import java.io.File;
import java.util.List;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

@Service
public class WeatherWidgetLanguageXMLFileRepositoryImpl implements WeatherWidgetLanguageXMLFileRepository {
  private static final String LANGUAGES_DEFINITION_FILE = "accuweather/AccuLanguageInfo.xml";
  
  private static final Logger logger = LoggerFactory.getLogger(WeatherWidgetLanguageXMLFileRepositoryImpl.class);
  
  private List<Language> languageList = null;
  
  public List<Language> getLanguagesList() {
    if (this.languageList == null || this.languageList.isEmpty())
      initializeLanguageList(); 
    return this.languageList;
  }
  
  private void initializeLanguageList() {
    try {
      logger.debug("WeatherWidget file repository called for language list initialization");
      this.languageList = null;
      JAXBContext jaxbContext = JAXBContext.newInstance(new Class[] { Languages.class });
      Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
      File xmlFile = (new ClassPathResource("accuweather/AccuLanguageInfo.xml")).getFile();
      Languages languages = (Languages)jaxbUnmarshaller.unmarshal(xmlFile);
      if (!languages.getLanguages().isEmpty()) {
        this.languageList = languages.getLanguages();
        for (int i = 0; i < this.languageList.size(); i++)
          ((Language)this.languageList.get(i)).setIndex(i); 
      } 
    } catch (JAXBException|java.io.IOException ex) {
      logger.error("Couldn't parse language List data: " + ex.getMessage());
    } 
  }
}
