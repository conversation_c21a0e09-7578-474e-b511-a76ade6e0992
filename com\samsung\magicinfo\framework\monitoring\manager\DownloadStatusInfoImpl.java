package com.samsung.magicinfo.framework.monitoring.manager;

import com.samsung.common.cache.CacheFactory;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.monitoring.dao.DownloadStatusDAO;
import com.samsung.magicinfo.framework.monitoring.entity.ProgramStatus;
import com.samsung.magicinfo.framework.scheduler.entity.DownloadStatusAndCountInfoVO;
import com.samsung.magicinfo.framework.scheduler.entity.DownloadStatusesVO;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class DownloadStatusInfoImpl implements DownloadStatusInfo {
   Logger logger = LoggingManagerV2.getLogger(DownloadStatusInfoImpl.class);
   private static DownloadStatusInfoImpl instance = null;
   private static DownloadStatusDAO dao = null;

   public DownloadStatusInfoImpl() {
      super();
   }

   public static DownloadStatusInfoImpl getInstacne() {
      Class var0;
      if (instance == null) {
         var0 = DownloadStatusInfoImpl.class;
         synchronized(DownloadStatusInfoImpl.class) {
            instance = new DownloadStatusInfoImpl();
         }
      }

      if (dao == null) {
         var0 = DownloadStatusDAO.class;
         synchronized(DownloadStatusDAO.class) {
            dao = new DownloadStatusDAO();
         }
      }

      return instance;
   }

   public static DownloadStatusInfoImpl getInstacne(SqlSession session) {
      dao = new DownloadStatusDAO(session);
      if (instance == null) {
         Class var1 = DownloadStatusInfoImpl.class;
         synchronized(DownloadStatusInfoImpl.class) {
            instance = new DownloadStatusInfoImpl();
         }
      }

      return instance;
   }

   public void deleteDownloadStatusInCache(String deviceId) {
      this.deleteDownloadStatusInCache(deviceId, (List)null);
   }

   public void deleteDownloadStatusInCache(String deviceId, List contentIds) {
      try {
         if (contentIds != null && contentIds.size() > 0) {
            Map downloadStatus = null;
            Object obj = CacheFactory.getCache().get("DOWNLOAD_STATUS_BY_DEVICE" + deviceId);
            if (obj != null) {
               downloadStatus = (Map)obj;
               Iterator var5 = contentIds.iterator();

               while(var5.hasNext()) {
                  String contentId = (String)var5.next();
                  downloadStatus.remove(contentId);
               }

               CacheFactory.getCache().set("DOWNLOAD_STATUS_BY_DEVICE" + deviceId, downloadStatus);
            }
         } else {
            CacheFactory.getCache().delete("DOWNLOAD_STATUS_BY_DEVICE" + deviceId);
         }
      } catch (Exception var7) {
         this.logger.error("[DownloadStatusInfoImpl][" + deviceId + "] error to delete download status in cache e : " + var7.getMessage());
      }

   }

   public void deleteDownloadStatusWithoutContentIds(String deviceId, List contentIds) throws SQLException {
      if (contentIds != null) {
         dao.deleteDownloadStatusWithoutContentIdsByDeviceId(deviceId, contentIds);
      }
   }

   public void deleteDownloadStatus(String programId, SqlSession session) throws SQLException {
      dao.deleteDownloadStatus(programId, session);
   }

   public void deleteDownloadStatus(String programId) throws SQLException {
      dao.deleteDownloadStatus(programId);
   }

   public void deleteDownloadStatusByDeviceId(String deviceId) throws SQLException {
      dao.deleteDownloadStatusByDeviceId(deviceId);
   }

   public String getStatusByProgramIdAndDeviceId(String deviceId, String programId) throws SQLException {
      return dao.getStatusByProgramIdAndDeviceId(deviceId, programId);
   }

   public void deleteDownloadStatus(String deviceId, String activeType) throws SQLException {
      dao.deleteDownloadStatus(deviceId, activeType);
   }

   public void initDownloadStatus(String programId, List deviceList, List contentList, boolean needToDeleteProgress, String activeType, String downloadType) throws SQLException {
      Map downloadStatus = new HashMap();
      downloadStatus.put("programId", programId);
      downloadStatus.put("contentList", contentList);
      downloadStatus.put("device", deviceList);
      downloadStatus.put("needToDeleteProgress", needToDeleteProgress);
      downloadStatus.put("activeType", activeType);
      downloadStatus.put("downloadType", downloadType);
      dao.initDownloadStatus(downloadStatus);
   }

   public void addDownloadStatus(String programId, List deviceList, List contentList) throws SQLException {
      dao.addDownloadStatus(programId, deviceList, contentList);
   }

   public Object[] getDownloadStatusListByProgramId(String programId) throws SQLException {
      List downloadStatuses = dao.getDownloadStatusesByProgramId(programId);
      Map downloadMap = new HashMap();

      String deviceId;
      DownloadStatusAndCountInfoVO downloadStatusAndCountInfoVO;
      for(Iterator var4 = downloadStatuses.iterator(); var4.hasNext(); downloadMap.put(deviceId, downloadStatusAndCountInfoVO)) {
         DownloadStatusesVO downloadStatusesVO = (DownloadStatusesVO)var4.next();
         deviceId = downloadStatusesVO.getDevice_id();
         downloadStatusAndCountInfoVO = downloadMap.get(deviceId) != null ? (DownloadStatusAndCountInfoVO)downloadMap.get(deviceId) : new DownloadStatusAndCountInfoVO(deviceId, downloadStatusesVO.getStatus());
         if (StringUtils.isEmpty(downloadStatusesVO.getContent_id()) || StringUtils.isEmpty(downloadStatusesVO.getProgress()) || StringUtils.isEmpty(downloadStatusesVO.getActive_type())) {
            downloadStatusAndCountInfoVO.setTotalCount(downloadStatusAndCountInfoVO.getTotalCount() + 1L);
         }

         if ("content".equals(downloadStatusesVO.getActive_type())) {
            downloadStatusAndCountInfoVO.setTotalCount(downloadStatusAndCountInfoVO.getTotalCount() + 1L);
            downloadStatusAndCountInfoVO.setCompleteCount("100 %".equals(downloadStatusesVO.getProgress()) ? downloadStatusAndCountInfoVO.getCompleteCount() + 1L : downloadStatusAndCountInfoVO.getCompleteCount());
            downloadStatusAndCountInfoVO.setFailCount(!"100 %".equals(downloadStatusesVO.getProgress()) ? downloadStatusAndCountInfoVO.getFailCount() + 1L : downloadStatusAndCountInfoVO.getFailCount());
         }

         if (downloadStatusesVO.getContent_id() == null && downloadStatusesVO.getStatus().equals("SUCCESS")) {
            downloadStatusAndCountInfoVO.setCompleteCount(downloadStatusAndCountInfoVO.getCompleteCount() + 1L);
         }
      }

      Object[] rtn = new Object[2];
      if (downloadMap.size() == 0) {
         rtn[0] = 4;
      } else {
         int completeCount = downloadMap.entrySet().stream().filter((download) -> {
            return ((DownloadStatusAndCountInfoVO)download.getValue()).getCompleteCount() == ((DownloadStatusAndCountInfoVO)download.getValue()).getTotalCount();
         }).toArray().length;
         int successStatusCount = downloadMap.entrySet().stream().filter((download) -> {
            return "SUCCESS".equals(((DownloadStatusAndCountInfoVO)download.getValue()).getStatus());
         }).toArray().length;
         int failCount = downloadMap.entrySet().stream().filter((download) -> {
            return ((DownloadStatusAndCountInfoVO)download.getValue()).getCompleteCount() != ((DownloadStatusAndCountInfoVO)download.getValue()).getTotalCount();
         }).toArray().length;
         List deviceList = (List)downloadMap.entrySet().stream().map((download) -> {
            return ((DownloadStatusAndCountInfoVO)download.getValue()).getDevice_id();
         }).collect(Collectors.toList());
         if (successStatusCount > 0) {
            if (completeCount == downloadMap.size()) {
               rtn[0] = 0;
            } else {
               rtn[0] = 1;
            }
         } else {
            rtn[0] = 2;
         }

         Map map = new HashMap();
         map.put("deviceCount", downloadMap.size());
         map.put("completeCount", completeCount);
         map.put("failCount", failCount);
         map.put("deviceList", deviceList);
         rtn[1] = map;
      }

      return rtn;
   }

   public List getProgressInfoByDeviceId(String programId, List deviceList) throws SQLException {
      return dao.getProgressInfoByDeviceId(programId, deviceList, "content");
   }

   public int getCntDownloadStatusByDeviceId(String device_id, String content_id) throws SQLException {
      return dao.getCntDownloadStatusByDeviceId(device_id, content_id);
   }

   public int updateScheduleDeployStatusByDeviceId(String deviceId, String status) throws SQLException {
      return dao.updateScheduleDeployStatusByDeviceId(deviceId, status);
   }

   public void deleteScheduleDeployStatusByDeviceId(String deviceId) throws SQLException {
      dao.deleteScheduleDeployStatusByDeviceId(deviceId);
   }

   public void addScheduleDeployStatusWithDevices(String programId, List deviceList, String status) throws SQLException {
      dao.addScheduleDeployStatusWithDevices(programId, deviceList, status);
   }

   public void deleteDownloadStatusByProgramId(String programId) throws SQLException {
      dao.deleteDownloadStatusByProgramId(programId);
   }

   public String getDownloadProgress(String device_id, String content_id, String activeType) throws SQLException {
      String rtn = dao.getDownloadProgress(device_id, content_id, activeType);
      return rtn != null && !rtn.equals("") ? rtn : "-";
   }

   public void updateProgressByDeviceIdsAndContentId(String deviceId, String contentId, String progress) throws SQLException {
      int count = dao.countStatus(deviceId, contentId, (String)null);
      if (count < 1) {
         DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
         DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
         String programId = deviceInfo.getProgramIdByDeviceId(deviceId);
         DeviceGroup deviceGroup = deviceGroupInfo.getGroupByDeviceId(deviceId);
         dao.addDownloadStatusWithProgramIdNGroupId(programId, deviceId, contentId, "content", deviceGroup.getGroup_id(), progress);
      } else {
         dao.updateStatus(deviceId, contentId, progress);
      }

   }

   public Map getContentDownloadsByDeviceId(String deviceId) {
      Map downloadStatus = null;

      try {
         Object downloadStatusObj = CacheFactory.getCache().get("DOWNLOAD_STATUS_BY_DEVICE" + deviceId);
         downloadStatus = (Map)downloadStatusObj;
      } catch (Exception var4) {
         this.logger.error("[ContentDownloadManager][" + deviceId + "] fail to get cache");
      }

      return downloadStatus;
   }

   public void updateContentDownloadsProgressByDeviceId(String deviceId, Map downloadStatus) {
      try {
         CacheFactory.getCache().set("DOWNLOAD_STATUS_BY_DEVICE" + deviceId, downloadStatus);
      } catch (Exception var4) {
         this.logger.error("[ContentDownloadManager][" + deviceId + "] fail to cache update with content progress ");
      }

   }

   public void updateContentDownloadsProgressByDeviceIdAndContentIds(String deviceId, List contentIds, String progress) {
      try {
         Map downloadStatus = null;
         Object obj = CacheFactory.getCache().get("DOWNLOAD_STATUS_BY_DEVICE" + deviceId);
         if (obj != null) {
            downloadStatus = (Map)obj;
         } else {
            downloadStatus = new HashMap();
         }

         Iterator var6 = contentIds.iterator();

         while(var6.hasNext()) {
            String contentId = (String)var6.next();
            ((Map)downloadStatus).put(contentId, progress);
         }

         CacheFactory.getCache().set("DOWNLOAD_STATUS_BY_DEVICE" + deviceId, downloadStatus);
      } catch (Exception var8) {
         this.logger.error("[ContentDownloadManager][" + deviceId + "] fail to cache update with content progress ");
      }

   }

   public int getContentScheduleStatus(String programId) {
      List programStatuses = dao.getProgramStatusByProgramId(programId);
      if (!programStatuses.isEmpty()) {
         Map counted = (Map)programStatuses.stream().collect(Collectors.groupingBy(ProgramStatus::getStatus, Collectors.counting()));

         try {
            if (!counted.isEmpty()) {
               if (counted.get("WAITING") != null && (Long)counted.get("WAITING") == (long)programStatuses.size()) {
                  return 2;
               }

               if (counted.get("SUCCESS") != null && (Long)counted.get("SUCCESS") == (long)programStatuses.size()) {
                  return 0;
               }

               return 1;
            }
         } catch (Exception var5) {
         }

         return 2;
      } else {
         return 4;
      }
   }

   public void deleteDownloadStatusDetailByDeviceId(String deviceId) throws SQLException {
      dao.deleteDownloadStatusDetailByDeviceId(deviceId);
   }

   public void updateScheduleDeployStatusWithDevices(String programId, List devices, String status) throws SQLException {
      List programStatusList = dao.getProgramStatusByProgramId(programId);
      if (CollectionUtils.isNotEmpty(programStatusList)) {
         List deviceIds = (List)devices.stream().filter((d) -> {
            return programStatusList.stream().filter((programStatus) -> {
               return programStatus.getDevice_id().equals(d.getDevice_id());
            }).findFirst().isPresent();
         }).map((d) -> {
            return d.getDevice_id();
         }).collect(Collectors.toList());
         Iterator var6;
         if (CollectionUtils.isNotEmpty(deviceIds)) {
            var6 = deviceIds.iterator();

            while(var6.hasNext()) {
               String deviceId = (String)var6.next();
               dao.addScheduleDeployStatus(programId, deviceId, status);
            }
         }

         var6 = programStatusList.iterator();

         while(var6.hasNext()) {
            ProgramStatus programStatus = (ProgramStatus)var6.next();
            dao.updateScheduleDeployStatus(programId, programStatus.getDevice_id(), status);
         }
      }

   }

   public void addScheduleDeployStatus(String programId, List deviceList) throws SQLException {
      dao.addScheduleDeployStatus(programId, deviceList);
   }

   public List getDownloadStatusesByProgramId(String programId) throws SQLException {
      return dao.getDownloadStatusesByProgramId(programId);
   }
}
