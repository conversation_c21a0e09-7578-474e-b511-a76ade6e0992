package com.samsung.magicinfo.restapi.setting.model.config;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

public class V2GeneralSettings extends V2GeneralCommon {
   @JsonProperty("useNotificationCount")
   @ApiModelProperty(
      dataType = "boolean",
      example = "false",
      value = "Option to show the labels of notification count in the left menu."
   )
   private Boolean notification_enable;
   @JsonProperty("useBackupPlay")
   @ApiModelProperty(
      dataType = "boolean",
      example = "false",
      value = "Option to enable/disable Backup Play for Device"
   )
   private Boolean redundancy_enable;
   @JsonProperty("useDevicePowerControl")
   @ApiModelProperty(
      dataType = "boolean",
      example = "false",
      value = "Option to enable/disable Device Power Control for Device"
   )
   private Boolean device_power;
   @JsonProperty("useSignUp")
   @ApiModelProperty(
      dataType = "boolean",
      example = "false",
      value = "Option to enable/disable SignUp on login page"
   )
   private Boolean sign_up_enable;
   @JsonProperty("signinFailureCount")
   @ApiModelProperty(
      dataType = "int",
      example = "3",
      value = "Option to set the count for checking signin failure"
   )
   private Integer signin_failure_count;
   @JsonProperty("signinBlockDuration")
   @ApiModelProperty(
      dataType = "int",
      example = "3",
      value = "Option to set the duration for blocking signin"
   )
   private Integer signin_block_duration;
   @JsonIgnore
   @JsonProperty("sessionNeverExpired")
   private Boolean session_never_expired;
   @JsonProperty("tokenLifetime")
   @ApiModelProperty(
      dataType = "long",
      example = "30",
      value = "Option to set the lifetime for token (30 - 480 min)"
   )
   private Long token_lifetime;
   @JsonProperty("insufficientCapacity")
   @ApiModelProperty(
      dataType = "int",
      example = "30",
      value = "Option to set the insufficient capacity (10 - 5000 MB)"
   )
   private Integer insufficient_capacity;
   @JsonProperty("userNeedAuth")
   @ApiModelProperty(
      dataType = "boolean",
      example = "false",
      value = "Option to enable/disable password requirement for all user"
   )
   private Boolean user_need_auth;
   @JsonProperty("myAccountNeedAuth")
   @ApiModelProperty(
      dataType = "boolean",
      example = "false",
      value = "Option to enable/disable password requirement for my account: My information and withdraw membership"
   )
   private Boolean my_account_need_auth;
   @JsonProperty("useDbEncryption")
   @ApiModelProperty(
      dataType = "boolean",
      example = "false",
      value = "Option to enable/disable encryption for DB access information"
   )
   private Boolean db_encryption;
   @JsonProperty("isE2E")
   @ApiModelProperty(
      example = "false",
      required = false
   )
   private Boolean isE2E;
   @JsonProperty("e2eLicenseSystem")
   @ApiModelProperty(
      example = "",
      required = false
   )
   private String e2eLicenseSystem;

   public V2GeneralSettings() {
      super();
   }

   public Boolean getNotification_enable() {
      return this.notification_enable;
   }

   public void setNotification_enable(Boolean notification_enable) {
      this.notification_enable = notification_enable;
   }

   public Boolean getRedundancy_enable() {
      return this.redundancy_enable;
   }

   public void setRedundancy_enable(Boolean redundancy_enable) {
      this.redundancy_enable = redundancy_enable;
   }

   public Boolean getDevice_power() {
      return this.device_power;
   }

   public void setDevice_power(Boolean device_power) {
      this.device_power = device_power;
   }

   public Boolean getSign_up_enable() {
      return this.sign_up_enable;
   }

   public void setSign_up_enable(Boolean sign_up_enable) {
      this.sign_up_enable = sign_up_enable;
   }

   public Integer getSignin_failure_count() {
      return this.signin_failure_count;
   }

   public void setSignin_failure_count(Integer signin_failure_count) {
      this.signin_failure_count = signin_failure_count;
   }

   public Integer getSignin_block_duration() {
      return this.signin_block_duration;
   }

   public void setSignin_block_duration(Integer signin_block_duration) {
      this.signin_block_duration = signin_block_duration;
   }

   @JsonIgnore
   public Boolean getSession_never_expired() {
      return this.session_never_expired;
   }

   @JsonIgnore
   public void setSession_never_expired(Boolean session_never_expired) {
      this.session_never_expired = session_never_expired;
   }

   public Long getToken_lifetime() {
      return this.token_lifetime;
   }

   public void setToken_lifetime(Long token_lifetime) {
      this.token_lifetime = token_lifetime;
   }

   public Integer getInsufficient_capacity() {
      return this.insufficient_capacity;
   }

   public void setInsufficient_capacity(Integer insufficient_capacity) {
      this.insufficient_capacity = insufficient_capacity;
   }

   public void setUser_need_auth(Boolean user_need_auth) {
      this.user_need_auth = user_need_auth;
   }

   public void setMy_account_need_auth(Boolean my_account_need_auth) {
      this.my_account_need_auth = my_account_need_auth;
   }

   public Boolean getDb_encryption() {
      return this.db_encryption;
   }

   public void setDb_encryption(Boolean db_encryption) {
      this.db_encryption = db_encryption;
   }

   public Boolean getIsE2E() {
      return this.isE2E;
   }

   public void setIsE2E(Boolean isE2E) {
      this.isE2E = isE2E;
   }

   public String getE2eLicenseSystem() {
      return this.e2eLicenseSystem;
   }

   public void setE2eLicenseSystem(String e2eLicenseSystem) {
      this.e2eLicenseSystem = e2eLicenseSystem;
   }
}
