package com.samsung.magicinfo.framework.monitoring.service;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.LedCabinet;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSboxConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSboxConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.LedCabinetConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.LedCabinetConfManagerImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocInfo;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocInfoImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManager;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManagerImpl;
import com.samsung.magicinfo.framework.device.warningRule.entity.DeviceThreshold;
import com.samsung.magicinfo.framework.device.warningRule.manager.DeviceWarningRuleInfo;
import com.samsung.magicinfo.framework.device.warningRule.manager.DeviceWarningRuleInfoImpl;
import com.samsung.magicinfo.protocol.exception.ServiceException;
import com.samsung.magicinfo.protocol.rmql.RMQL;
import com.samsung.magicinfo.protocol.rmql.RMQLDriver;
import com.samsung.magicinfo.protocol.rmql.ResultSet;
import com.samsung.magicinfo.protocol.servicemanager.ServiceOpActivity;
import com.samsung.magicinfo.protocol.util.RMQLDriverUtil;
import com.samsung.magicinfo.protocol.util.RMQLInstanceCreator;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;

public class SboxMonitoringServiceActivity extends ServiceOpActivity {
   private Logger logger = LoggingManagerV2.getLogger(SboxMonitoringServiceActivity.class);

   public SboxMonitoringServiceActivity() {
      super();
   }

   public Object process(HashMap params) throws ServiceException {
      Device sboxDevice = null;
      ResultSet rs = (ResultSet)params.get("resultset");

      String eventPath;
      try {
         String deviceId = rs.getAttribute("DEVICE_ID");
         eventPath = rs.getAttribute("MO_EVENT");
         String eventID = rs.getAttribute("EVENT_ID");
         if (deviceId == null) {
            throw new ServiceException("No device_sn found");
         } else if (eventPath == null) {
            throw new ServiceException("No mo_event found");
         } else if (eventID == null) {
            throw new ServiceException("No event_id found");
         } else {
            DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
            String deviceGroupId = deviceDao.getDeviceGroupIdByDeviceId(deviceId);
            LedCabinetConfManager ledCabinetConfMgr = LedCabinetConfManagerImpl.getInstance();
            DeviceWarningRuleInfo deviceWarningRuleDao = DeviceWarningRuleInfoImpl.getInstance();
            List deviceThresholds = deviceWarningRuleDao.getWarningRuleDetailsByDeviceGroupId(Long.valueOf(deviceGroupId));
            int element_IC_Th = -1;
            int element_LOD_Th = -1;
            int element_POWER_Th = -1;
            int element_Temperture_Th = -1;
            int childCount;
            if (deviceThresholds != null) {
               for(childCount = 0; childCount < deviceThresholds.size(); ++childCount) {
                  if (((DeviceThreshold)deviceThresholds.get(childCount)).getElement_id().equals("IC")) {
                     element_IC_Th = Integer.valueOf(((DeviceThreshold)deviceThresholds.get(childCount)).getElement_value());
                  } else if (((DeviceThreshold)deviceThresholds.get(childCount)).getElement_id().equals("LOD")) {
                     element_LOD_Th = Integer.valueOf(((DeviceThreshold)deviceThresholds.get(childCount)).getElement_value());
                  } else if (((DeviceThreshold)deviceThresholds.get(childCount)).getElement_id().equals("POWER")) {
                     element_LOD_Th = Integer.valueOf(((DeviceThreshold)deviceThresholds.get(childCount)).getElement_value());
                  } else if (((DeviceThreshold)deviceThresholds.get(childCount)).getElement_id().equals("TEMPERATURE")) {
                     element_Temperture_Th = Integer.valueOf(((DeviceThreshold)deviceThresholds.get(childCount)).getElement_value());
                  }
               }
            }

            this.logger.info("[SBOX] SBOX deviceId " + deviceId);
            String[] powerArr = null;
            String[] groupInfo = null;
            String[] childIdArr = null;
            String[] childTypeArr = null;
            String[] voltageStatusArr = null;
            String[] hdbtArr = null;
            String[] lodArr = null;
            String[] productNumberArr = null;
            String[] moduleDateArr = null;
            String[] temperatureArr = null;
            String[] resolutionArr = null;
            String[] phySizeArr = null;
            String[] aspectRatioArr = null;
            String[] modulesArr = null;
            String[] pitchArr = null;
            String[] luminanceArr = null;
            String[] ablArr = null;
            String[] autoSourceArr = null;
            String[] icArr = null;
            String[] fwVerArr = null;
            String[] fpgaVerArr = null;
            String[] faultArr = null;
            String[] xyPositionArr = null;
            String groupResolution = null;
            String[] groupPositionArr = null;
            Date lastScannedTime = null;
            if (eventPath != null && eventPath.equals(".MO.MONITORING_INFO.SBOX_CHILD")) {
               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.GROUP") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.GROUP").equals("")) {
                  groupInfo = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.GROUP").split(":");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.GROUP_RESOLUTION") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.GROUP_RESOLUTION").equals("")) {
                  groupResolution = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.GROUP_RESOLUTION");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.GROUP_POSITION") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.GROUP_POSITION").equals("")) {
                  groupPositionArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.GROUP_POSITION").split(",");
               }

               if (groupInfo == null || groupInfo.length != 2) {
                  throw new ServiceException("Invalid MO (.MO.MONITORING_INFO.SBOX_CHILD.GROUP)" + deviceId);
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.ID_LIST") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.ID_LIST").equals("")) {
                  childIdArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.ID_LIST").split(";");
               }

               childCount = childIdArr.length;
               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.CABINET_TYPE") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.CABINET_TYPE").equals("")) {
                  childTypeArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.CABINET_TYPE").split(";");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.POWER") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.POWER").equals("")) {
                  powerArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.POWER").split(";");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.HDBT") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.HDBT").equals("")) {
                  hdbtArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.HDBT").split(";");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.LOD") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.LOD").equals("")) {
                  lodArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.LOD").split(";");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.MODULE_PRODUCT_NUMBER") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.MODULE_PRODUCT_NUMBER").equals("")) {
                  productNumberArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.MODULE_PRODUCT_NUMBER").split(";");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.MODULE_DATE") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.MODULE_DATE").equals("")) {
                  moduleDateArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.MODULE_DATE").split(";");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.MONITOR_TEMPERATURE") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.MONITOR_TEMPERATURE").equals("")) {
                  temperatureArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.MONITOR_TEMPERATURE").split(";");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.LAST_SCANNED_TIME") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.LAST_SCANNED_TIME").equals("")) {
                  lastScannedTime = rs.getDate(".MO.MONITORING_INFO.SBOX_CHILD.LAST_SCANNED_TIME");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.VOLTAGE_STATUS") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.VOLTAGE_STATUS").equals("")) {
                  voltageStatusArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.VOLTAGE_STATUS").split(";");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.RESOLUTION") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.RESOLUTION").equals("")) {
                  resolutionArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.RESOLUTION").split(";");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.PHY_SIZE") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.PHY_SIZE").equals("")) {
                  phySizeArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.PHY_SIZE").split(";");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.ASPECT_RATIO") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.ASPECT_RATIO").equals("")) {
                  aspectRatioArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.ASPECT_RATIO").split(";");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.MODULES") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.MODULES").equals("")) {
                  modulesArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.MODULES").split(";");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.PITCH") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.PITCH").equals("")) {
                  pitchArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.PITCH").split(";");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.LUMINANCE") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.LUMINANCE").equals("")) {
                  luminanceArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.LUMINANCE").split(";");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.ABL") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.ABL").equals("")) {
                  ablArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.ABL").split(";");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.AUTO_SOURCE") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.AUTO_SOURCE").equals("")) {
                  autoSourceArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.AUTO_SOURCE").split(";");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.IC") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.IC").equals("")) {
                  icArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.IC").split(";");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.FW_VERSION") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.FW_VERSION").equals("")) {
                  fwVerArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.FW_VERSION").split(";");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.FPGA_VERSION") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.FPGA_VERSION").equals("")) {
                  fpgaVerArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.FPGA_VERSION").split(";");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.FAULT") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.FAULT").equals("")) {
                  faultArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.FAULT").split(";");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.POSITION") != null && !rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.POSITION").equals("")) {
                  xyPositionArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.POSITION").split(";");
               }

               sboxDevice = deviceDao.getDevice(deviceId);
               Long cabinetGroupId = Long.parseLong(groupInfo[0]);
               if (groupResolution != null && groupPositionArr != null) {
                  ledCabinetConfMgr.setLedCabinetGroupInfo(deviceId, cabinetGroupId, groupResolution, Long.valueOf(groupPositionArr[0]), Long.valueOf(groupPositionArr[1]));
               }

               List cabinetList = new ArrayList();
               int failedCount = 0;

               boolean isTemperatureError;
               for(int i = 0; i < childCount; ++i) {
                  if (childIdArr[i].equals("FAIL")) {
                     ++failedCount;
                     break;
                  }

                  boolean isLODError = false;
                  isTemperatureError = false;
                  boolean isVoltageError = false;
                  boolean isICError = false;
                  LedCabinet cabinet = new LedCabinet(deviceId);
                  cabinet.setCabinet_group_id(cabinetGroupId);
                  cabinet.setCabinet_id(Long.parseLong(childIdArr[i]));
                  if (childTypeArr != null && !StringUtils.isEmpty(childTypeArr[i])) {
                     cabinet.setCabinet_type(Long.parseLong(childTypeArr[i]));
                  }

                  if (powerArr != null && powerArr.length >= i + 1) {
                     cabinet.setPower(powerArr[i]);
                  } else {
                     cabinet.setPower("");
                  }

                  if (hdbtArr != null && hdbtArr.length >= i + 1) {
                     cabinet.setHdbt(hdbtArr[i]);
                  } else {
                     cabinet.setHdbt("");
                  }

                  String[] icErrDetail;
                  if (lodArr != null && lodArr.length >= i + 1) {
                     cabinet.setOpen_detection(lodArr[i]);
                     icErrDetail = lodArr[i].split(":");
                     int lodErrNum = 0;

                     for(int j = 0; j < icErrDetail.length; ++j) {
                        lodErrNum += Integer.valueOf(icErrDetail[j]);
                     }

                     if (element_LOD_Th != -1 && lodErrNum > element_LOD_Th) {
                        isLODError = true;
                     }
                  } else {
                     cabinet.setOpen_detection("");
                  }

                  if (productNumberArr != null && productNumberArr.length >= i + 1) {
                     cabinet.setModule_product_number(productNumberArr[i]);
                  } else {
                     cabinet.setModule_product_number("");
                  }

                  if (moduleDateArr != null && moduleDateArr.length >= i + 1) {
                     cabinet.setModule_date(moduleDateArr[i]);
                  } else {
                     cabinet.setModule_date("");
                  }

                  if (temperatureArr != null && temperatureArr.length >= i + 1) {
                     cabinet.setCurrent_temperature(Long.parseLong(temperatureArr[i]));
                     if (element_Temperture_Th != -1 && Integer.valueOf(temperatureArr[i]) > element_Temperture_Th) {
                        isTemperatureError = true;
                     }
                  } else {
                     cabinet.setCurrent_temperature(0L);
                  }

                  if (voltageStatusArr != null && voltageStatusArr.length >= i + 1) {
                     cabinet.setVoltage_status(voltageStatusArr[i]);
                     icErrDetail = voltageStatusArr[i].split(":0");
                     if (element_POWER_Th != -1 && icErrDetail.length > element_POWER_Th) {
                        isVoltageError = true;
                     }
                  } else {
                     cabinet.setVoltage_status("");
                  }

                  if (resolutionArr != null && resolutionArr.length >= i + 1) {
                     cabinet.setResolution(resolutionArr[i]);
                  } else {
                     cabinet.setResolution("");
                  }

                  if (phySizeArr != null && phySizeArr.length >= i + 1) {
                     cabinet.setPhy_size(phySizeArr[i]);
                  } else {
                     cabinet.setPhy_size("");
                  }

                  if (aspectRatioArr != null && aspectRatioArr.length >= i + 1) {
                     cabinet.setAspect_ratio(aspectRatioArr[i]);
                  } else {
                     cabinet.setAspect_ratio("");
                  }

                  if (modulesArr != null && modulesArr.length >= i + 1) {
                     cabinet.setModules(modulesArr[i]);
                  } else {
                     cabinet.setModules("");
                  }

                  if (pitchArr != null && pitchArr.length >= i + 1) {
                     cabinet.setPitch(pitchArr[i]);
                  } else {
                     cabinet.setPitch("");
                  }

                  if (luminanceArr != null && luminanceArr.length >= i + 1) {
                     cabinet.setLuminance(Long.valueOf(luminanceArr[i]));
                  } else {
                     cabinet.setLuminance(0L);
                  }

                  if (ablArr != null && ablArr.length >= i + 1) {
                     cabinet.setAbl(Long.valueOf(ablArr[i]));
                  } else {
                     cabinet.setAbl(0L);
                  }

                  if (autoSourceArr != null && autoSourceArr.length >= i + 1) {
                     cabinet.setAuto_source(Long.valueOf(autoSourceArr[i]));
                  } else {
                     cabinet.setAuto_source(0L);
                  }

                  if (icArr != null && icArr.length >= i + 1) {
                     cabinet.setIc(icArr[i]);
                     icErrDetail = icArr[i].split(":0");
                     if (element_IC_Th != -1 && icErrDetail.length > element_IC_Th) {
                        isICError = true;
                     }
                  } else {
                     cabinet.setIc("");
                  }

                  if (faultArr != null && faultArr.length >= i + 1) {
                     cabinet.setFault(faultArr[i] == null ? "" : faultArr[i]);
                  }

                  if (fwVerArr != null && fwVerArr.length >= i + 1) {
                     cabinet.setFw_version(fwVerArr[i]);
                  } else {
                     cabinet.setFw_version("");
                  }

                  if (fpgaVerArr != null && fpgaVerArr.length >= i + 1) {
                     cabinet.setFpga_version(fpgaVerArr[i]);
                  } else {
                     cabinet.setFpga_version("");
                  }

                  if (xyPositionArr != null && xyPositionArr.length >= i + 1) {
                     cabinet.setPosition_X(Long.valueOf(xyPositionArr[i].split(",")[0]));
                     cabinet.setPosition_Y(Long.valueOf(xyPositionArr[i].split(",")[1]));
                  } else {
                     cabinet.setPosition_X(0L);
                     cabinet.setPosition_Y(0L);
                  }

                  if (lastScannedTime != null) {
                     cabinet.setLast_scanned_time(lastScannedTime);
                  }

                  if (isLODError) {
                     cabinet.setIsLODError("ERROR");
                  }

                  if (isTemperatureError) {
                     cabinet.setIsTemperatureError("ERROR");
                  }

                  if (isVoltageError) {
                     cabinet.setIsVoltageError("ERROR");
                  }

                  if (isICError) {
                     cabinet.setIsICError("ERROR");
                  }

                  ledCabinetConfMgr.setLedCabinetConf(cabinet);
                  cabinetList.add(cabinet);
               }

               String externalPowerInfo;
               try {
                  if (DeviceUtils.isSupportNOC()) {
                     DeviceNocInfo nocDao = DeviceNocInfoImpl.getInstance();
                     externalPowerInfo = deviceDao.getDeviceGroupIdByDeviceId(deviceId);
                     isTemperatureError = nocDao.isNocSupportGroup(Long.valueOf(externalPowerInfo));
                     if (isTemperatureError) {
                        DeviceNocManager nocService = DeviceNocManagerImpl.getInstance();
                        nocService.thingworxUpdateLedCabinets(cabinetList, failedCount);
                     }
                  }
               } catch (Exception var56) {
                  this.logger.error("[SBOXMonitoringServiceActivity][thingworx] failed to call api for update cabinet info. :" + deviceId);
               }

               String externalPowerNumber = null;
               externalPowerInfo = null;
               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.EXTERNAL_POWER_NUMBER") != null) {
                  externalPowerNumber = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.EXTERNAL_POWER_NUMBER");
               }

               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.EXTERNAL_POWER_INFO") != null) {
                  externalPowerInfo = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.EXTERNAL_POWER_INFO");
               }

               if (null != externalPowerNumber && null != externalPowerInfo) {
                  DeviceSboxConfManager sboxManager = DeviceSboxConfManagerImpl.getInstance();
                  sboxManager.setExternalPower(this.ctxt.getDevice().getDevice_id(), externalPowerNumber, externalPowerInfo);
               }
            }

            if (!childIdArr[0].equals("FAIL")) {
               List cabinets = ledCabinetConfMgr.getLedCabinetList(deviceId);
               sboxDevice.setChild_cnt((long)cabinets.size());
               Long conn_count = 0L;
               if (cabinets != null) {
                  Iterator var66 = cabinets.iterator();

                  while(var66.hasNext()) {
                     LedCabinet cabinet = (LedCabinet)var66.next();
                     if (!"0".equals(cabinet.getPower())) {
                        conn_count = conn_count + 1L;
                     }
                  }
               }

               sboxDevice.setConn_child_cnt(conn_count);
            }

            deviceDao.setDevice(sboxDevice);
            RMQL rmql = RMQLInstanceCreator.getInstance(sboxDevice, "NOTIFY", (Long)null);
            rmql.addParam("RESULT", "SUCCESS");
            RMQLDriver driver = RMQLDriverUtil.getWSRMQLDriver();
            return driver.createAppBOForResponse(rmql);
         }
      } catch (Exception var57) {
         this.logger.info("[SBOX] SBOX - Cabinet Monitoring Exception " + var57.getMessage());
         this.logger.error("", var57);

         try {
            eventPath = rs.getAttribute("DEVICE_ID");
            DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
            Device device = deviceInfo.getDevice(eventPath);
            RMQL rmql = RMQLInstanceCreator.getInstance(device, "NOTIFY", (Long)null);
            rmql.addParam("RESULT", "FAIL");
            RMQLDriver driver = RMQLDriverUtil.getWSRMQLDriver();
            return driver.createAppBOForResponse(rmql);
         } catch (Exception var55) {
            throw new ServiceException(var55);
         }
      }
   }
}
