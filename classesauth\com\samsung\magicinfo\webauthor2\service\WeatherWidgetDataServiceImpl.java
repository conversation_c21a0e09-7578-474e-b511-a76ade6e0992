package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.weather.CityData;
import com.samsung.magicinfo.webauthor2.model.weather.Country;
import com.samsung.magicinfo.webauthor2.model.weather.Language;
import com.samsung.magicinfo.webauthor2.repository.WeatherWidgetCityInfoXMLFileRepository;
import com.samsung.magicinfo.webauthor2.repository.WeatherWidgetLanguageXMLFileRepository;
import java.util.List;
import javax.inject.Inject;
import org.springframework.stereotype.Service;

@Service
public class WeatherWidgetDataServiceImpl implements WeatherWidgetDataService {
  @Inject
  private WeatherWidgetCityInfoXMLFileRepository weatherWidgetDataXMLFileRepository;
  
  @Inject
  private WeatherWidgetLanguageXMLFileRepository languageXMLFileRepository;
  
  public List<Language> getLanguageList() {
    return this.languageXMLFileRepository.getLanguagesList();
  }
  
  public List<Country> getCountryList(String languageCode) {
    return this.weatherWidgetDataXMLFileRepository.getCountryList();
  }
  
  public List<CityData> getCityList(int id, String languageCode) {
    return this.weatherWidgetDataXMLFileRepository.getCitiesForCountryIndex(id);
  }
}
