package com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf;

import com.samsung.common.db.DBListExecuter;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.LedCabinet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

public interface LedCabinetConfManager extends DBListExecuter {
   LedCabinet getLedCabinet(String var1, Long var2, Long var3) throws SQLException;

   List getLedCabinetList(String var1, List var2) throws SQLException;

   List getLedCabinetList(String var1) throws SQLException;

   List getLedCabinetList(String var1, Long var2) throws SQLException;

   List getLedCabinetGroupIds(String var1) throws SQLException;

   List getLedCabinetGroupLayoutInfo(String var1) throws SQLException;

   int getLedCabinetCount(String var1) throws SQLException;

   int getLedCabinetCount(String var1, Long var2) throws SQLException;

   boolean setLedCabinetPowerOff(String var1, Long var2) throws SQLException;

   boolean setLedCabinetPowerOff(String var1, Long var2, Long var3) throws SQLException;

   boolean setLedCabinetConf(LedCabinet var1) throws SQLException;

   boolean addLedCabinet(List var1) throws SQLException;

   boolean addLedCabinet(LedCabinet var1) throws SQLException;

   boolean deleteLedCabinets(String var1) throws SQLException;

   List getLedCabinetPagedList(int var1, int var2, Map var3) throws SQLException;

   boolean setLedCabinetGroupInfo(String var1, Long var2, String var3, Long var4, Long var5) throws SQLException;

   int getErrorLedCabinetCntByGroupId(String var1, Long var2) throws SQLException;

   int getLedCabinetCountByGroup(String var1, Long var2) throws SQLException;
}
