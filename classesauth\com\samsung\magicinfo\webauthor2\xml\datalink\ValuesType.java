package com.samsung.magicinfo.webauthor2.xml.datalink;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ValuesType", propOrder = {"value"})
public class ValuesType {
  @XmlElement(name = "Value")
  private ValueType value;
  
  public ValuesType() {}
  
  public ValuesType(ValueType value) {
    this.value = value;
  }
  
  public ValueType getValue() {
    return this.value;
  }
  
  public void setValue(ValueType value) {
    this.value = value;
  }
}
