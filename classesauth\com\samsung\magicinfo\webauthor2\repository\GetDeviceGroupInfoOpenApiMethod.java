package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceGroupData;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceGroupListResponseData;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceGroupListResultListData;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.web.client.RestTemplate;

public class GetDeviceGroupInfoOpenApiMethod extends OpenApiMethod<List<DeviceGroupData>, DeviceGroupListResponseData> {
  private final String token;
  
  private final String parentGroupId;
  
  public GetDeviceGroupInfoOpenApiMethod(RestTemplate restTemplate, String token, String parentGroupId) {
    super(restTemplate);
    this.token = token;
    this.parentGroupId = parentGroupId;
  }
  
  protected String getOpenApiClassName() {
    return "PremiumDeviceService";
  }
  
  protected String getOpenApiMethodName() {
    return "getDeviceGroupInfo";
  }
  
  protected Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("token", this.token);
    vars.put("groupId", this.parentGroupId);
    return vars;
  }
  
  Class<DeviceGroupListResponseData> getResponseClass() {
    return DeviceGroupListResponseData.class;
  }
  
  List<DeviceGroupData> convertResponseData(DeviceGroupListResponseData responseData) {
    List<DeviceGroupData> resultList;
    DeviceGroupListResultListData resultListData = responseData.getResponseClass();
    if (resultListData != null && resultListData.getResultList() != null) {
      resultList = resultListData.getResultList();
    } else {
      resultList = new ArrayList<>();
    } 
    return resultList;
  }
}
