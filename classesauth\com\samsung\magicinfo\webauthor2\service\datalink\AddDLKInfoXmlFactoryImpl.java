package com.samsung.magicinfo.webauthor2.service.datalink;

import com.samsung.magicinfo.webauthor2.xml.dlkinfo.ConvertTableListType;
import javax.inject.Inject;
import javax.xml.transform.Result;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Service;
import org.springframework.xml.transform.StringResult;

@Service
public class AddDLKInfoXmlFactoryImpl implements AddDLKInfoXmlFactory {
  private final Jaxb2Marshaller jaxb2MarshallerForDlkInfo;
  
  @Inject
  public AddDLKInfoXmlFactoryImpl(Jaxb2Marshaller jaxb2MarshallerForDlkInfo) {
    this.jaxb2MarshallerForDlkInfo = jaxb2MarshallerForDlkInfo;
  }
  
  public String marshall(ConvertTableListType xmlObj) {
    StringResult stringResult = new StringResult();
    this.jaxb2MarshallerForDlkInfo.marshal(xmlObj, (Result)stringResult);
    return stringResult.toString();
  }
}
