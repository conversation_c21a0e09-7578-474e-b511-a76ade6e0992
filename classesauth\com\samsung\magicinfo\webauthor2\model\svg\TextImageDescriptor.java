package com.samsung.magicinfo.webauthor2.model.svg;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

public class TextImageDescriptor {
  private int angle;
  
  private Color backgroundColor;
  
  private int backgroundOpacity;
  
  private Color color;
  
  private boolean fixedAngle;
  
  private boolean fixedPosition;
  
  private boolean fixedSize;
  
  private String fontFamily;
  
  private int fontSize;
  
  private String fontStyle;
  
  private String fontWeight;
  
  private String name;
  
  private boolean outline;
  
  private Color outlineColor;
  
  private int outlineOpacity;
  
  private int outlineWidth;
  
  private boolean shadow;
  
  private int shadowAngle;
  
  private Color shadowColor;
  
  private int shadowOffset;
  
  private int shadowOpacity;
  
  private MediaSource source;
  
  private String text;
  
  private String textAlign;
  
  private String textDecoration;
  
  private int textSlide;
  
  private int textSpeed;
  
  private String textVerticalAlign;
  
  private int height;
  
  private int width;
  
  private double x;
  
  private double y;
  
  @JsonCreator
  public TextImageDescriptor(@JsonProperty(value = "angle", required = true) int angle, @JsonProperty(value = "backgroundColor", required = false) Color backgroundColor, @JsonProperty(value = "backgroundOpacity", required = false) int backgroundOpacity, @JsonProperty(value = "color", required = false) Color color, @JsonProperty(value = "fixedAngle", required = false) boolean fixedAngle, @JsonProperty(value = "fixedPosition", required = false) boolean fixedPosition, @JsonProperty(value = "fixedSize", required = false) boolean fixedSize, @JsonProperty(value = "fontFamily", required = true) String fontFamily, @JsonProperty(value = "fontSize", required = true) int fontSize, @JsonProperty(value = "fontStyle", required = true) String fontStyle, @JsonProperty(value = "fontWeight", required = true) String fontWeight, @JsonProperty(value = "name", required = true) String name, @JsonProperty(value = "outline", required = true) boolean outline, @JsonProperty(value = "outlineColor", required = true) Color outlineColor, @JsonProperty(value = "outlineOpacity", required = true) int outlineOpacity, @JsonProperty(value = "outlineWidth", required = false) int outlineWidth, @JsonProperty(value = "shadow", required = true) boolean shadow, @JsonProperty(value = "shadowAngle", required = false) int shadowAngle, @JsonProperty(value = "shadowColor", required = true) Color shadowColor, @JsonProperty(value = "shadowOffset", required = true) int shadowOffset, @JsonProperty(value = "shadowOpacity", required = false) int shadowOpacity, @JsonProperty(value = "source", required = true) MediaSource source, @JsonProperty(value = "text", required = true) String text, @JsonProperty(value = "textAlign", required = false) String textAlign, @JsonProperty(value = "textDecoration", required = false) String textDecoration, @JsonProperty(value = "textSlide", required = false) int textSlide, @JsonProperty(value = "textSpeed", required = false) int textSpeed, @JsonProperty(value = "textVerticalAlign", required = true) String textVerticalAlign, @JsonProperty(value = "height", required = true) int height, @JsonProperty(value = "width", required = true) int width, @JsonProperty(value = "x", required = false) double x, @JsonProperty(value = "y", required = false) double y) {
    this.angle = angle;
    this.backgroundColor = backgroundColor;
    this.backgroundOpacity = backgroundOpacity;
    this.color = color;
    this.fixedAngle = fixedAngle;
    this.fixedPosition = fixedPosition;
    this.fixedSize = fixedSize;
    this.fontFamily = fontFamily;
    this.fontSize = fontSize;
    this.fontStyle = fontStyle;
    this.fontWeight = fontWeight;
    this.name = name;
    this.outline = outline;
    this.outlineColor = outlineColor;
    this.outlineOpacity = outlineOpacity;
    this.outlineWidth = outlineWidth;
    this.shadow = shadow;
    this.shadowAngle = shadowAngle;
    this.shadowColor = shadowColor;
    this.shadowOffset = shadowOffset;
    this.shadowOpacity = shadowOpacity;
    this.source = source;
    this.text = text;
    this.textAlign = textAlign;
    this.textDecoration = textDecoration;
    this.textSlide = textSlide;
    this.textSpeed = textSpeed;
    this.textVerticalAlign = textVerticalAlign;
    this.height = height;
    this.width = width;
    this.x = x;
    this.y = y;
  }
  
  public int getAngle() {
    return this.angle;
  }
  
  public void setAngle(int angle) {
    this.angle = angle;
  }
  
  public Color getBackgroundColor() {
    return this.backgroundColor;
  }
  
  public void setBackgroundColor(Color backgroundColor) {
    this.backgroundColor = backgroundColor;
  }
  
  public int getBackgroundOpacity() {
    return this.backgroundOpacity;
  }
  
  public void setBackgroundOpacity(int backgroundOpacity) {
    this.backgroundOpacity = backgroundOpacity;
  }
  
  public Color getColor() {
    return this.color;
  }
  
  public void setColor(Color color) {
    this.color = color;
  }
  
  public boolean isFixedAngle() {
    return this.fixedAngle;
  }
  
  public void setFixedAngle(boolean fixedAngle) {
    this.fixedAngle = fixedAngle;
  }
  
  public boolean isFixedPosition() {
    return this.fixedPosition;
  }
  
  public void setFixedPosition(boolean fixedPosition) {
    this.fixedPosition = fixedPosition;
  }
  
  public boolean isFixedSize() {
    return this.fixedSize;
  }
  
  public void setFixedSize(boolean fixedSize) {
    this.fixedSize = fixedSize;
  }
  
  public String getFontFamily() {
    return this.fontFamily;
  }
  
  public void setFontFamily(String fontFamily) {
    this.fontFamily = fontFamily;
  }
  
  public int getFontSize() {
    return this.fontSize;
  }
  
  public void setFontSize(int fontSize) {
    this.fontSize = fontSize;
  }
  
  public double getFontSize1_24() {
    return this.fontSize * 1.24D;
  }
  
  public String getFontStyle() {
    return this.fontStyle;
  }
  
  public void setFontStyle(String fontStyle) {
    this.fontStyle = fontStyle;
  }
  
  public String getFontWeight() {
    return this.fontWeight;
  }
  
  public void setFontWeight(String fontWeight) {
    this.fontWeight = fontWeight;
  }
  
  public String getName() {
    return this.name;
  }
  
  public void setName(String name) {
    this.name = name;
  }
  
  public boolean isOutline() {
    return this.outline;
  }
  
  public void setOutline(boolean outline) {
    this.outline = outline;
  }
  
  public Color getOutlineColor() {
    return this.outlineColor;
  }
  
  public void setOutlineColor(Color outlineColor) {
    this.outlineColor = outlineColor;
  }
  
  public int getOutlineOpacity() {
    return this.outlineOpacity;
  }
  
  public void setOutlineOpacity(int outlineOpacity) {
    this.outlineOpacity = outlineOpacity;
  }
  
  public double getOutlineOpacityDivided() {
    return this.outlineOpacity / 100.0D;
  }
  
  public int getOutlineWidth() {
    return this.outlineWidth;
  }
  
  public void setOutlineWidth(int outlineWidth) {
    this.outlineWidth = outlineWidth;
  }
  
  public boolean isShadow() {
    return this.shadow;
  }
  
  public void setShadow(boolean shadow) {
    this.shadow = shadow;
  }
  
  public int getShadowAngle() {
    return this.shadowAngle;
  }
  
  public void setShadowAngle(int shadowAngle) {
    this.shadowAngle = shadowAngle;
  }
  
  public Color getShadowColor() {
    return this.shadowColor;
  }
  
  public void setShadowColor(Color shadowColor) {
    this.shadowColor = shadowColor;
  }
  
  public int getShadowOffset() {
    return this.shadowOffset;
  }
  
  public void setShadowOffset(int shadowOffset) {
    this.shadowOffset = shadowOffset;
  }
  
  public int getShadowOpacity() {
    return this.shadowOpacity;
  }
  
  public double getShadowOpacityDivided() {
    return this.shadowOpacity / 100.0D;
  }
  
  public void setShadowOppacity(int shadowOppacity) {
    this.shadowOpacity = shadowOppacity;
  }
  
  public MediaSource getSource() {
    return this.source;
  }
  
  public void setSource(MediaSource source) {
    this.source = source;
  }
  
  public String getText() {
    return this.text;
  }
  
  public void setText(String text) {
    this.text = text;
  }
  
  public String getTextAlign() {
    return this.textAlign;
  }
  
  public void setTextAlign(String textAlign) {
    this.textAlign = textAlign;
  }
  
  public String getTextDecoration() {
    return this.textDecoration;
  }
  
  public void setTextDecoration(String textDecoration) {
    this.textDecoration = textDecoration;
  }
  
  public int getTextSlide() {
    return this.textSlide;
  }
  
  public void setTextSlide(int textSlide) {
    this.textSlide = textSlide;
  }
  
  public int getTextSpeed() {
    return this.textSpeed;
  }
  
  public void setTextSpeed(int textSpeed) {
    this.textSpeed = textSpeed;
  }
  
  public String getTextVerticalAlign() {
    return this.textVerticalAlign;
  }
  
  public void setTextVerticalAlign(String textVerticalAllign) {
    this.textVerticalAlign = textVerticalAllign;
  }
  
  public int getHeight() {
    return this.height;
  }
  
  public void setHeight(int height) {
    this.height = height;
  }
  
  public int getWidth() {
    return this.width;
  }
  
  public void setWidth(int width) {
    this.width = width;
  }
  
  public double getX() {
    return this.x;
  }
  
  public void setX(double x) {
    this.x = x;
  }
  
  public double getY() {
    return this.y;
  }
  
  public void setY(double y) {
    this.y = y;
  }
}
