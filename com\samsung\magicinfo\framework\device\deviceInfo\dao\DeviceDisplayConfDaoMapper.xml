<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceDisplayConfDaoMapper">

	<select id="getDeviceDisplayConf"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf">
		SELECT A.*, B.DEVICE_MODEL_CODE, B.DEVICE_TYPE, B.DEVICE_TYPE_VERSION, B.DEVICE_NAME, B.DEVICE_MODEL_NAME, B.CONN_CHILD_CNT, B.VWT_ID, B.WEBCAM, B.CHILD_CNT, B.IS_CHILD FROM MI_DMS_INFO_DISPLAY A, MI_DMS_INFO_DEVICE B WHERE A.DEVICE_ID = #{deviceId} AND A.DEVICE_ID = B.DEVICE_ID
	</select>

	<select id="getListDeviceDisplayConf" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf">
        SELECT MI_DMS_INFO_DISPLAY.*, MI_DMS_INFO_DEVICE.DEVICE_TYPE, MI_DMS_INFO_DEVICE.DEVICE_TYPE_VERSION FROM MI_DMS_INFO_DISPLAY
        LEFT JOIN MI_DMS_INFO_DEVICE ON MI_DMS_INFO_DISPLAY.DEVICE_ID = MI_DMS_INFO_DEVICE.DEVICE_ID
        WHERE
		<foreach collection="deviceIds" item="deviceId" index="index" separator="or">
			MI_DMS_INFO_DISPLAY.DEVICE_ID = #{deviceId}
		</foreach>
	</select>
	
	<insert id="addDeviceDisplayConf">
		INSERT INTO MI_DMS_INFO_DISPLAY (
		DEVICE_ID, BASIC_POWER, BASIC_VOLUME, BASIC_MUTE,
		BASIC_SOURCE, BASIC_PANEL_STATUS, PPC_BRIGHTNESS, TIME_ON_TIME,
		TIME_OFF_TIME, MNT_SAFETY_LOCK, MISC_REMOCON, MISC_PANEL_LOCK,
		MISC_OSD, MISC_ALL_LOCK, DIAGNOSIS_MONITOR_TEMPERATURE,
		DIAGNOSIS_ALARM_TEMPERATURE,
		DIAGNOSIS_PANEL_ON_TIME, BASIC_DIRECT_CHANNEL, TIME_CURRENT_TIME, MDC_UPDATE_TIME, VWL_MODE, VWL_POSITION,VWL_FORMAT, VWL_LAYOUT,
		SENSOR_INTERNAL_TEMPERATURE, SENSOR_INTERNAL_HUMIDITY, SENSOR_ENVIRONMENT_TEMPERATURE, SENSOR_FRONTGLASS_TEMPERATURE, SENSOR_FRONTGLASS_HUMIDITY,
		CLEANUP_USER_DATA_INTERVAL, AUTO_SAVE, AUTO_POWER_OFF, SMTP, PRINT_SERVER

		VALUES (#{displayConf.device_id}, #{displayConf.basic_power},
				#{displayConf.basic_volume}, #{displayConf.basic_mute},
				#{displayConf.basic_source}, #{displayConf.basic_panel_status},
				#{displayConf.ppc_brightness}, #{displayConf.time_on_time}, 
				#{displayConf.time_off_time},	#{displayConf.mnt_safety_lock}, 
				#{displayConf.misc_remocon}, #{displayConf.misc_panel_lock},
				#{displayConf.misc_osd}, #{displayConf.misc_all_lock}, 
				#{displayConf.diagnosis_monitor_temperature},
				#{displayConf.diagnosis_alarm_temperature},
				#{displayConf.diagnosis_panel_on_time}, 
				#{displayConf.basic_direct_channel},
				#{displayConf.time_current_time}, #{displayConf.mdc_update_time},
				#{displayConf.vwl_mode}, #{displayConf.vwl_position}, #{displayConf.vwl_format},#{displayConf.vwl_layout},
				#{displayConf.sensor_internal_temperature}, #{displayConf.sensor_internal_humidity}, #{displayConf.sensor_environment_temperature}, 
				#{displayConf.sensor_frontglass_temperature}, #{displayConf.sensor_frontglass_humidity},
				#{displayConf.cleanup_user_data_interval}, #{displayConf.auto_save}, #{displayConf.auto_power_off},
				#{displayConf.smtp}, #{displayConf.print_server})
	</insert>

    <insert id="addDeviceDisplayConfList">
        INSERT INTO MI_DMS_INFO_DISPLAY (
        DEVICE_ID, BASIC_POWER, BASIC_VOLUME, BASIC_MUTE,
        BASIC_SOURCE, BASIC_PANEL_STATUS, PPC_BRIGHTNESS, TIME_ON_TIME,
        TIME_OFF_TIME, MNT_SAFETY_LOCK, MISC_REMOCON, MISC_PANEL_LOCK,
        MISC_OSD, MISC_ALL_LOCK, DIAGNOSIS_MONITOR_TEMPERATURE,
        DIAGNOSIS_ALARM_TEMPERATURE,
        DIAGNOSIS_PANEL_ON_TIME, BASIC_DIRECT_CHANNEL, TIME_CURRENT_TIME, MDC_UPDATE_TIME, VWL_MODE, VWL_POSITION,VWL_FORMAT, VWL_LAYOUT)

        VALUES 
        <foreach collection="displayConfList" item="displayConf"  separator=",">
            (
                #{displayConf.device_id}, #{displayConf.basic_power},
                #{displayConf.basic_volume}, #{displayConf.basic_mute},
                #{displayConf.basic_source}, #{displayConf.basic_panel_status},
                #{displayConf.ppc_brightness}, #{displayConf.time_on_time}, 
                #{displayConf.time_off_time},   #{displayConf.mnt_safety_lock}, 
                #{displayConf.misc_remocon}, #{displayConf.misc_panel_lock},
                #{displayConf.misc_osd}, #{displayConf.misc_all_lock}, 
                #{displayConf.diagnosis_monitor_temperature},
                #{displayConf.diagnosis_alarm_temperature},
                #{displayConf.diagnosis_panel_on_time}, 
                #{displayConf.basic_direct_channel},
                #{displayConf.time_current_time}, #{displayConf.mdc_update_time},
                #{displayConf.vwl_mode}, #{displayConf.vwl_position}, #{displayConf.vwl_format},#{displayConf.vwl_layout}
            )
        </foreach>
    </insert>
	<!--[V9 RC #5-2] [DAO] 4. Addition of new MO for Specialized Picture Mode support.-->
	<insert id="addDeviceDisplayExtConf">
		INSERT INTO MI_DMS_INFO_DISPLAY ( DEVICE_ID, SPECIALIZED_PICTURE_MODE,
		PV_MODE, PV_CONTRAST, PV_BRIGHTNESS, PV_SHARPNESS,
		PV_COLOR, PV_TINT, PV_COLORTONE, PV_COLOR_TEMPERATURE,
		PV_SIZE, PV_DIGITALNR, PV_FILMMODE, PV_VIDEO_PICTURE_POSITION_SIZE,
		PV_HDMI_BLACK_LEVEL,
		AUTO_MOTION_PLUS,
		AUTO_MOTION_PLUS_JUDDER_REDUCTION,

		PPC_MAGIC_BRIGHT, PPC_CONTRAST, PPC_BRIGHTNESS, PPC_COLORTONE,
		PPC_RED, PPC_GREEN, PPC_BLUE, PPC_SIZE,
		PPC_COLOR_TEMPERATURE, PPC_GAMMA, PPC_HDMI_BLACK_LEVEL,

		SOUND_MODE, SOUND_BASS, SOUND_TREBLE, SOUND_BALANCE,
		SOUND_SRS, SOUND_EFFECT,

		SB_STATUS, SB_RGAIN, SB_GGAIN, SB_BGAIN,
		SB_R_OFFSET, SB_G_OFFSET, SB_B_OFFSET, SB_GAIN,
		SB_SHARP,

		MNT_AUTO, MNT_MANUAL,
		MNT_SAFETY_SCREEN_TIMER, MNT_SAFETY_SCREEN_RUN, MNT_PIXEL_SHIFT, MNT_SAFETY_LOCK,

		ADVANCED_OSD_DISPLAY_TYPE, ADVANCED_FAN_CONTROL, ADVANCED_FAN_SPEED,
		ADVANCED_AUTO_POWER,
		ADVANCED_STAND_BY)
		
		VALUES (
				
				#{displayConf.device_id},
				#(displayConf.specialized_picture_mode),
				#{displayConf.pv_mode}, #{displayConf.pv_contrast}, #{displayConf.pv_brightness}, #{displayConf.pv_sharpness},
				#{displayConf.pv_color}, #{displayConf.pv_tint}, #{displayConf.pv_colortone}, #{displayConf.pv_color_temperature},
				#{displayConf.pv_size}, #{displayConf.pv_digitalnr}, #{displayConf.pv_filmmode}, #{displayConf.pv_video_picture_position_size},
				#{displayConf.pv_hdmi_black_level},
				#{displayConf.auto_motion_plus},
				#{displayConf.auto_motion_plus_judder_reduction},
				
				#{displayConf.ppc_magic_bright}, #{displayConf.ppc_contrast}, #{displayConf.ppc_brightness}, #{displayConf.ppc_colortone}, 
				#{displayConf.ppc_red}, #{displayConf.ppc_green}, #{displayConf.ppc_blue}, #{displayConf.ppc_size}, 
				#{displayConf.ppc_color_temperature}, #{displayConf.ppc_gamma}, #{displayConf.ppc_hdmi_black_level},
				
				#{displayConf.sound_mode}, #{displayConf.sound_bass}, #{displayConf.sound_treble}, #{displayConf.sound_balance}, 
				#{displayConf.sound_srs}, #{displayConf.sound_effect}, 
				
				#{displayConf.sb_status}, #{displayConf.sb_rgain}, #{displayConf.sb_ggain}, #{displayConf.sb_bgain},
				#{displayConf.sb_r_offset}, #{displayConf.sb_g_offset}, #{displayConf.sb_b_offset}, #{displayConf.sb_gain},
				#{displayConf.sb_sharp},
				
				
				#{displayConf.mnt_auto}, #{displayConf.mnt_manual}, #{displayConf.mnt_safety_screen_timer}, #{displayConf.mnt_safety_screen_run},
				#{displayConf.mnt_pixel_shift}, #{displayConf.mnt_safety_lock},
				
				#{displayConf.advanced_osd_display_type}, #{displayConf.advanced_fan_control}, #{displayConf.advanced_fan_speed}, #{displayConf.advanced_auto_power},
				#{displayConf.advanced_stand_by}	
				) 
	</insert>
	<!--[V9 RC $5-2] [DAO] 4. Addition of new MO for Specialized Picture Mode support.-->
	<insert id="addDeviceDisplayConfAll">
		INSERT INTO MI_DMS_INFO_DISPLAY (
		DEVICE_ID, BASIC_POWER, BASIC_VOLUME, BASIC_MUTE,
		BASIC_SOURCE, BASIC_PANEL_STATUS, TIME_ON_TIME,
		TIME_OFF_TIME, MISC_OSD,
		DIAGNOSIS_MONITOR_TEMPERATURE, DIAGNOSIS_ALARM_TEMPERATURE,
		DIAGNOSIS_PANEL_ON_TIME, BASIC_DIRECT_CHANNEL, TIME_CURRENT_TIME, MDC_UPDATE_TIME, VWL_MODE, VWL_POSITION,VWL_FORMAT, VWL_LAYOUT, 
		CHILD_ALARM_TEMPERATURE, AUTO_BRIGHTNESS, SPECIALIZED_PICTURE_MODE,
		PV_MODE, PV_CONTRAST, PV_BRIGHTNESS, PV_SHARPNESS,
		PV_COLOR, PV_TINT, PV_COLORTONE, PV_COLOR_TEMPERATURE,
		PV_SIZE, PV_DIGITALNR, PV_FILMMODE, PV_VIDEO_PICTURE_POSITION_SIZE,
		PV_HDMI_BLACK_LEVEL,
		AUTO_MOTION_PLUS,
		AUTO_MOTION_PLUS_JUDDER_REDUCTION,

		PPC_MAGIC_BRIGHT, PPC_CONTRAST, PPC_BRIGHTNESS, PPC_COLORTONE,
		PPC_RED, PPC_GREEN, PPC_BLUE, PPC_SIZE,
		PPC_COLOR_TEMPERATURE, PPC_GAMMA, PPC_HDMI_BLACK_LEVEL,

		SOUND_MODE, SOUND_BASS, SOUND_TREBLE, SOUND_BALANCE,
		SOUND_SRS, SOUND_EFFECT,

		SB_STATUS, SB_RGAIN, SB_GGAIN, SB_BGAIN,
		SB_R_OFFSET, SB_G_OFFSET, SB_B_OFFSET, SB_GAIN,
		SB_SHARP,

		MNT_AUTO, MNT_MANUAL,
		MNT_SAFETY_SCREEN_TIMER, MNT_SAFETY_SCREEN_RUN, MNT_PIXEL_SHIFT,

		ADVANCED_OSD_DISPLAY_TYPE, ADVANCED_FAN_CONTROL, ADVANCED_FAN_SPEED,
		ADVANCED_AUTO_POWER, ADVANCED_STAND_BY, NETWORK_STANDBY_MODE, AUTO_SOURCE_SWITCHING,
		MAX_POWER_SAVING, BRIGHTNESS_LIMIT, WEB_BROWSER_URL, SCREEN_MUTE,
		SCREEN_FREEZE, PV_MPEG_NOISE_FILTER, CUSTOM_LOGO, BLACK_TONE, FLESH_TONE, RGB_ONLY_MODE,
		OSD_MENU_SIZE, LED_HDR, LED_PICTURE_SIZE, ECO_SENSOR, MIN_BRIGHTNESS, LIVE_MODE, DISPLAY_OUTPUT_MODE, ADVANCED_USER_AUTO_COLOR)

		VALUES (#{displayConf.device_id}, #{displayConf.basic_power},
				#{displayConf.basic_volume}, #{displayConf.basic_mute},
				#{displayConf.basic_source}, #{displayConf.basic_panel_status},
				#{displayConf.time_on_time}, 
				#{displayConf.time_off_time},

				#{displayConf.misc_osd},
				#{displayConf.diagnosis_monitor_temperature},
				#{displayConf.diagnosis_alarm_temperature},
				#{displayConf.diagnosis_panel_on_time}, 
				#{displayConf.basic_direct_channel},
				#{displayConf.time_current_time}, #{displayConf.mdc_update_time},
				#{displayConf.vwl_mode}, #{displayConf.vwl_position}, #{displayConf.vwl_format},#{displayConf.vwl_layout}, 
				#{displayConf.child_alarm_temperature}, #{displayConf.auto_brightness},
				#{displayConf.specialized_picture_mode}, #{displayConf.pv_mode}, #{displayConf.pv_contrast}, #{displayConf.pv_brightness}, #{displayConf.pv_sharpness},
				#{displayConf.pv_color}, #{displayConf.pv_tint}, #{displayConf.pv_colortone}, #{displayConf.pv_color_temperature},
				#{displayConf.pv_size}, #{displayConf.pv_digitalnr}, #{displayConf.pv_filmmode}, #{displayConf.pv_video_picture_position_size},
				#{displayConf.pv_hdmi_black_level},
				#{displayConf.auto_motion_plus},
				#{displayConf.auto_motion_plus_judder_reduction},
				
				#{displayConf.ppc_magic_bright}, #{displayConf.ppc_contrast}, #{displayConf.ppc_brightness}, #{displayConf.ppc_colortone}, 
				#{displayConf.ppc_red}, #{displayConf.ppc_green}, #{displayConf.ppc_blue}, #{displayConf.ppc_size}, 
				#{displayConf.ppc_color_temperature}, #{displayConf.ppc_gamma}, #{displayConf.ppc_hdmi_black_level},
				
				#{displayConf.sound_mode}, #{displayConf.sound_bass}, #{displayConf.sound_treble}, #{displayConf.sound_balance}, 
				#{displayConf.sound_srs}, #{displayConf.sound_effect}, 
				
				#{displayConf.sb_status}, #{displayConf.sb_rgain}, #{displayConf.sb_ggain}, #{displayConf.sb_bgain},
				#{displayConf.sb_r_offset}, #{displayConf.sb_g_offset}, #{displayConf.sb_b_offset}, #{displayConf.sb_gain},
				#{displayConf.sb_sharp},
				
				
				#{displayConf.mnt_auto}, #{displayConf.mnt_manual}, #{displayConf.mnt_safety_screen_timer}, #{displayConf.mnt_safety_screen_run},
				#{displayConf.mnt_pixel_shift},
				
				#{displayConf.advanced_osd_display_type}, #{displayConf.advanced_fan_control}, #{displayConf.advanced_fan_speed}, 
				#{displayConf.advanced_auto_power},
				#{displayConf.advanced_stand_by}, #{displayConf.network_standby_mode}, #{displayConf.auto_source_switching},
				#{displayConf.max_power_saving}, #{displayConf.brightness_limit}, #{displayConf.web_browser_url},
				#{displayConf.screen_mute}, #{displayConf.screen_freeze}, #{displayConf.pv_mpeg_noise_filter}, #{displayConf.custom_logo},  #{displayConf.black_tone},
				#{displayConf.flesh_tone}, #{displayConf.rgb_only_mode}, #{displayConf.osd_menu_size}, #{displayConf.led_hdr}, #{displayConf.led_picture_size},
				#{displayConf.eco_sensor}, #{displayConf.min_brightness}, #{displayConf.live_mode},  #{displayConf.display_output_mode}, #{displayConf.advanced_user_auto_color})
	</insert>
	
	<select id="cntDeviceDisplayConfByDeviceId" resultType="int">
		SELECT COUNT(DEVICE_ID) FROM MI_DMS_INFO_DISPLAY WHERE DEVICE_ID = #{deviceId}
	</select>
	
	<update id="setDeviceDisplayConf">
		UPDATE MI_DMS_INFO_DISPLAY SET
		
		<if test="info.basic_power != null">
			BASIC_POWER = #{info.basic_power},
		</if>

		<if test="info.basic_volume != null">
			BASIC_VOLUME = #{info.basic_volume},
		</if>

		<if test="info.basic_mute != null">
			BASIC_MUTE = #{info.basic_mute},
		</if>

		<if test="info.basic_source != null">
			BASIC_SOURCE = #{info.basic_source},
		</if>

		<if test="info.basic_panel_status != null">
			BASIC_PANEL_STATUS = #{info.basic_panel_status},
		</if>

		<if test="info.ppc_brightness != null">
			PPC_BRIGHTNESS = #{info.ppc_brightness},
		</if>

		<if test="info.time_on_time != null">
			TIME_ON_TIME = #{info.time_on_time},
		</if>

		<if test="info.time_off_time != null">
			TIME_OFF_TIME = #{info.time_off_time},
		</if>

		<if test="info.mnt_safety_lock != null">
			MNT_SAFETY_LOCK = #{info.mnt_safety_lock},
		</if>

		<if test="info.misc_panel_lock != null">
			MISC_PANEL_LOCK = #{info.misc_panel_lock},
		</if>
		
		<if test="info.misc_all_lock != null">
			MISC_ALL_LOCK = #{info.misc_all_lock},
		</if>
		
		<if test="info.misc_remocon != null">
			MISC_REMOCON = #{info.misc_remocon},
		</if>

		<if test="info.misc_osd != null">
			MISC_OSD = #{info.misc_osd},
		</if>

		<if test="info.diagnosis_monitor_temperature != null">
			DIAGNOSIS_MONITOR_TEMPERATURE =
			#{info.diagnosis_monitor_temperature},
		</if>

		<if test="info.diagnosis_alarm_temperature != null">
			DIAGNOSIS_ALARM_TEMPERATURE =
			#{info.diagnosis_alarm_temperature},
		</if>

		<if test="info.diagnosis_panel_on_time != null">
			DIAGNOSIS_PANEL_ON_TIME = #{info.diagnosis_panel_on_time},
		</if>
		
		<if test="info.time_current_time != null">
			TIME_CURRENT_TIME = #{info.time_current_time},
		</if>
		
		<if test="info.basic_direct_channel != null">
			BASIC_DIRECT_CHANNEL = #{info.basic_direct_channel},
		</if>
		
		<if test="info.vwl_mode != null">
			VWL_MODE = #{info.vwl_mode},
		</if>
		
		<if test="info.vwl_position != null">
			VWL_POSITION = #{info.vwl_position},
		</if>
		
		<if test="info.vwl_format != null">
			VWL_FORMAT = #{info.vwl_format},
		</if>
		
		<if test="info.vwl_layout != null">
			VWL_LAYOUT = #{info.vwl_layout},
		</if>
		
		<if test="info.child_alarm_temperature != null">
            CHILD_ALARM_TEMPERATURE = #{info.child_alarm_temperature},
        </if>

        <if test="info.auto_brightness != null">
            AUTO_BRIGHTNESS = #{info.auto_brightness},
        </if>

        <if test="info.sensor_internal_temperature != null">
        	SENSOR_INTERNAL_TEMPERATURE = #{info.sensor_internal_temperature},
        </if>

        <if test="info.sensor_internal_humidity != null">
        	SENSOR_INTERNAL_HUMIDITY = #{info.sensor_internal_humidity},
        </if>
        
        <if test="info.sensor_environment_temperature != null">
        	SENSOR_ENVIRONMENT_TEMPERATURE = #{info.sensor_environment_temperature},
        </if>

        <if test="info.sensor_frontglass_temperature != null">
        	SENSOR_FRONTGLASS_TEMPERATURE = #{info.sensor_frontglass_temperature},
        </if>

        <if test="info.sensor_frontglass_humidity != null">
        	SENSOR_FRONTGLASS_HUMIDITY = #{info.sensor_frontglass_humidity},
        </if>
        
        <if test="info.cleanup_user_data_interval != null">
        	CLEANUP_USER_DATA_INTERVAL = #{info.cleanup_user_data_interval},
        </if>
        
        <if test="info.auto_save != null">
        	AUTO_SAVE = #{info.auto_save},
        </if>
        
        <if test="info.auto_power_off != null">
        	AUTO_POWER_OFF = #{info.auto_power_off},
        </if>

		<if test="info.smtp != null">
			SMTP = #{info.smtp},
		</if>

		<if test="info.print_server != null">
			PRINT_SERVER = #{info.print_server},
		</if>

		MDC_UPDATE_TIME = <include refid="utils.currentTimestamp" /> WHERE DEVICE_ID = #{info.device_id}
	</update>
	
	<update id="setDeviceDisplayExtConf">
		UPDATE MI_DMS_INFO_DISPLAY SET

		<if test="info.basic_direct_channel != null">
			BASIC_DIRECT_CHANNEL = #{info.basic_direct_channel},
		</if>
		
		<if test="info.pv_contrast != null">
			PV_CONTRAST = #{info.pv_contrast},
		</if>
		<!--[V9 RC #5-2] [DAO] 4. Addition of new MO for Specialized Picture Mode support.-->
		<if test="info.specialized_picture_mode != null">
			SPECIALIZED_PICTURE_MODE = #{info.specialized_picture_mode},
		</if>

		<if test="info.pv_mode != null">
			PV_MODE = #{info.pv_mode},
		</if>
		
		<if test="info.pv_brightness != null">
			PV_BRIGHTNESS = #{info.pv_brightness},
		</if>
		
		<if test="info.pv_sharpness != null">
			PV_SHARPNESS = #{info.pv_sharpness},
		</if>
		
		<if test="info.pv_color != null">
			PV_COLOR = #{info.pv_color},
		</if>
		
		<if test="info.pv_tint != null">
			PV_TINT = #{info.pv_tint},
		</if>
		
		<if test="info.pv_colortone != null">
			PV_COLORTONE = #{info.pv_colortone},
		</if>
		
		<if test="info.pv_color_temperature != null">
			PV_COLOR_TEMPERATURE = #{info.pv_color_temperature},
		</if>
		
		<if test="info.pv_size != null">
			PV_SIZE = #{info.pv_size},
		</if>
		
		<if test="info.pv_digitalnr != null">
			PV_DIGITALNR = #{info.pv_digitalnr},
		</if>
		
		<if test="info.pv_filmmode != null">
			PV_FILMMODE = #{info.pv_filmmode},
		</if>
		
		<if test="info.pv_video_picture_position_size != null">
			PV_VIDEO_PICTURE_POSITION_SIZE = #{info.pv_video_picture_position_size},
		</if>
		
		<if test="info.pv_hdmi_black_level != null">
			PV_HDMI_BLACK_LEVEL = #{info.pv_hdmi_black_level},
		</if>

        <if test="info.auto_motion_plus != null">
            AUTO_MOTION_PLUS = #{info.auto_motion_plus},
        </if>

        <if test="info.auto_motion_plus_judder_reduction != null">
            AUTO_MOTION_PLUS_JUDDER_REDUCTION = #{info.auto_motion_plus_judder_reduction},
        </if>

		<if test="info.ppc_magic_bright != null">
			PPC_MAGIC_BRIGHT = #{info.ppc_magic_bright},
		</if>
		
		<if test="info.ppc_contrast != null">
			PPC_CONTRAST = #{info.ppc_contrast},
		</if>
		
		<if test="info.ppc_brightness != null">
			PPC_BRIGHTNESS = #{info.ppc_brightness},
		</if>
		
		<if test="info.ppc_colortone != null">
			PPC_COLORTONE = #{info.ppc_colortone},
		</if>
		
		<if test="info.ppc_red != null">
			PPC_RED = #{info.ppc_red},
		</if>
		
		<if test="info.ppc_green != null">
			PPC_GREEN = #{info.ppc_green},
		</if>
		
		<if test="info.ppc_blue != null">
			PPC_BLUE = #{info.ppc_blue},
		</if>
		
		<if test="info.ppc_size != null">
			PPC_SIZE = #{info.ppc_size},
		</if>
		
		<if test="info.ppc_color_temperature != null">
			PPC_COLOR_TEMPERATURE = #{info.ppc_color_temperature},
		</if>
		
		<if test="info.ppc_gamma != null">
			PPC_GAMMA = #{info.ppc_gamma},
		</if>
		
		<if test="info.ppc_hdmi_black_level != null">
			PPC_HDMI_BLACK_LEVEL = #{info.ppc_hdmi_black_level},
		</if>
		
		<if test="info.sound_mode != null">
			SOUND_MODE = #{info.sound_mode},
		</if>
		
		<if test="info.sound_bass != null">
			SOUND_BASS = #{info.sound_bass},
		</if>
		
		<if test="info.sound_treble != null">
			SOUND_TREBLE = #{info.sound_treble},
		</if>
		
		<if test="info.sound_balance != null">
			SOUND_BALANCE = #{info.sound_balance},
		</if>
		
		<if test="info.sound_srs != null">
			SOUND_SRS = #{info.sound_srs},
		</if>
		
		<if test="info.sound_effect != null">
			SOUND_EFFECT = #{info.sound_effect},
		</if>
		
		<if test="info.sb_status != null">
			SB_STATUS = #{info.sb_status},
		</if>
		
		<if test="info.sb_rgain != null">
			SB_RGAIN = #{info.sb_rgain},
		</if>
		
		<if test="info.sb_ggain != null">
			SB_GGAIN = #{info.sb_ggain},
		</if>
		
		<if test="info.sb_bgain != null">
			SB_BGAIN = #{info.sb_bgain},
		</if>
		
		<if test="info.sb_r_offset != null">
			SB_R_OFFSET = #{info.sb_r_offset},
		</if>
		
		<if test="info.sb_g_offset != null">
			SB_G_OFFSET = #{info.sb_g_offset},
		</if>
		
		<if test="info.sb_b_offset != null">
			SB_B_OFFSET = #{info.sb_b_offset},
		</if>
		
		<if test="info.sb_gain != null">
			SB_GAIN = #{info.sb_gain},
		</if>
		
		<if test="info.sb_sharp != null">
			SB_SHARP = #{info.sb_sharp},
		</if>
		
		<if test="info.mnt_auto != null">
			MNT_AUTO = #{info.mnt_auto},
		</if>
		
		<if test="info.mnt_manual != null">
			MNT_MANUAL = #{info.mnt_manual},
		</if>
		
		<if test="info.mnt_safety_screen_timer != null">
			MNT_SAFETY_SCREEN_TIMER = #{info.mnt_safety_screen_timer},
		</if>
		
		<if test="info.mnt_safety_screen_run != null">
			MNT_SAFETY_SCREEN_RUN = #{info.mnt_safety_screen_run},
		</if>
		
		<if test="info.mnt_pixel_shift != null">
			MNT_PIXEL_SHIFT = #{info.mnt_pixel_shift},
		</if>
		
		<if test="info.mnt_safety_lock != null">
			MNT_SAFETY_LOCK = #{info.mnt_safety_lock},
		</if>
		
		<if test="info.misc_panel_lock != null">
			MISC_PANEL_LOCK = #{info.misc_panel_lock},
		</if>
		
		<if test="info.advanced_osd_display_type != null">
			ADVANCED_OSD_DISPLAY_TYPE = #{info.advanced_osd_display_type},
		</if>
		
		<if test="info.advanced_fan_control != null">
			ADVANCED_FAN_CONTROL = #{info.advanced_fan_control},
		</if>
		
		<if test="info.advanced_fan_speed != null">
			ADVANCED_FAN_SPEED = #{info.advanced_fan_speed},
		</if>
		
		<if test="info.advanced_auto_power != null">
			ADVANCED_AUTO_POWER = #{info.advanced_auto_power},
		</if>
		
		<if test="info.advanced_stand_by != null">
			ADVANCED_STAND_BY = #{info.advanced_stand_by},
		</if>
		
		<if test="info.network_standby_mode != null">
			NETWORK_STANDBY_MODE = #{info.network_standby_mode},
		</if>
		
        <if test="info.auto_source_switching != null">
            AUTO_SOURCE_SWITCHING = #{info.auto_source_switching},
        </if>
        
        <if test="info.max_power_saving != null">
            MAX_POWER_SAVING = #{info.max_power_saving},
        </if>        
        	
        <if test="info.brightness_limit != null">
            BRIGHTNESS_LIMIT = #{info.brightness_limit},
        </if>

        <if test="info.touch_control_lock != null">
            TOUCH_CONTROL_LOCK = #{info.touch_control_lock},
        </if>
             
        <if test="info.web_browser_url != null">
            WEB_BROWSER_URL = #{info.web_browser_url},
        </if>
        
        <if test="info.screen_mute != null">
            SCREEN_MUTE = #{info.screen_mute},
        </if> 
        
        <if test="info.freeze != null">
            SCREEN_FREEZE = #{info.screen_freeze},
        </if> 
        
        <if test="info.pv_mpeg_noise_filter != null">
            PV_MPEG_NOISE_FILTER = #{info.pv_mpeg_noise_filter},
        </if> 
        
        <if test="info.custom_logo != null">
            CUSTOM_LOGO = #{info.custom_logo},
        </if>
        
        <if test="info.black_tone != null">
            BLACK_TONE = #{info.black_tone},
        </if>          
        
        <if test="info.flesh_tone != null">
            FLESH_TONE = #{info.flesh_tone},
        </if>             
             
        <if test="info.rgb_only_mode != null">
            RGB_ONLY_MODE = #{info.rgb_only_mode},
        </if>
        
        <if test="info.osd_menu_size != null">
            OSD_MENU_SIZE = #{info.osd_menu_size},
        </if>
        
        <if test="info.led_hdr != null">
            LED_HDR = #{info.led_hdr},
        </if>

        <if test="info.led_picture_size != null">
            LED_PICTURE_SIZE = #{info.led_picture_size},
        </if>
        
        <if test="info.eco_sensor != null">
            ECO_SENSOR = #{info.eco_sensor},
        </if>
        
        <if test="info.min_brightness != null">
            MIN_BRIGHTNESS = #{info.min_brightness},
        </if>

        <if test="info.live_mode != null">
            LIVE_MODE = #{info.live_mode},
        </if>
        
        <if test="info.display_output_mode != null">
            DISPLAY_OUTPUT_MODE = #{info.display_output_mode},
        </if>

		MDC_UPDATE_TIME = <include refid="utils.currentTimestamp" /> WHERE DEVICE_ID = #{info.device_id}
	
	</update>

	<sql id="deviceExpirationDate">
		<if test="deviceExpirationDate != null">
			<![CDATA[ AND (EXTRACT('epoch' from EXPIRATION_DATE - CURRENT_DATE)::int)/60/60/24<=#{deviceExpirationDate} ]]>
		</if>
	</sql>

	<sql id="deviceExpirationDate" databaseId="mssql">
		<if test="deviceExpirationDate != null">
			<![CDATA[ AND DATEDIFF(D, GETDATE(), EXPIRATION_DATE) <= #{deviceExpirationDate} ]]> 
		</if>
	</sql>
    
    <sql id="deviceExpirationDate" databaseId="mysql">
        <if test="deviceExpirationDate != null">
            <![CDATA[ AND TIMESTAMPDIFF(DAY, NOW(), EXPIRATION_DATE) <= #{deviceExpirationDate} ]]> 
        </if>
    </sql>

	<sql id="deviceGroupAuthFrom">
		<if test="isDeviceGroupAuth">
			, MI_DMS_MAP_GROUP_USER D
		</if>
		
	</sql>
	
	<sql id="deviceGroupAuthWhere">
		<if test="isDeviceGroupAuth">
			AND C.GROUP_ID = D.GROUP_ID AND D.USER_ID = #{userId} 
		</if>
		
	</sql>
	
	<sql id="hasAlarmFilterQueries">
		<if test="condition.hasAlarmFilter != null">
			AND 
			  <foreach item="hasAlarm" collection="condition.hasAlarmFilter" separator=" OR " open="(" close=")">
                <choose>
                	<when test="hasAlarm == 'has_alarm_no_timezone'">
                		(TIME_ZONE_INDEX IS NULL OR TIME_ZONE_INDEX = '')
                	</when>
                	<when test="hasAlarm == 'has_alarm_not_enough_storage'">
                		( DISK_SPACE_REPOSITORY / (1024*1024)  &lt; <include refid="getInsufficientCapacity" /> )
                	</when>
                	<when test="hasAlarm == 'has_alarm_no_schedule_deployed'">
                		(B.DEVICE_ID IN (SELECT DEVICE_ID FROM MI_DMS_MAP_GROUP_DEVICE
							WHERE GROUP_ID NOT IN (
								SELECT DEVICE_GROUP_ID FROM MI_CDS_MAP_PROGRAM_DEVICE WHERE PROGRAM_ID IN (SELECT PROGRAM_ID FROM MI_CDS_INFO_PROGRAM WHERE IS_DEFAULT='N')
			             	)
						))
						<if test="condition != null and condition.rm_device_types != null">
							AND B.DEVICE_TYPE NOT IN 
							<foreach item="item" index="index" collection="condition.rm_device_types" open="(" separator="," close=")">
								#{item}
							</foreach>
						</if>						
                	</when>
                	<when test="hasAlarm == 'has_alarm_failed_schedule_deploy'">
                		(B.DEVICE_ID IN (SELECT DEVICE_ID FROM MI_CDS_MAP_PROGRAM_DEVICE_STATUS WHERE STATUS != 'SUCCESS')) 
                	</when>
                	<when test="hasAlarm == 'has_alarm_content_download'">
                		(B.DEVICE_ID IN (SELECT DISTINCT(DEVICE_ID) FROM MI_CDS_DOWNLOAD_STATUS WHERE PROGRESS IS NULL OR PROGRESS != '100 %')) 
                	</when>
                	 <when test="hasAlarm == 'filter_default_content'">
                		(B.CURR_CONTENT_ID IN ('DEFAULT_CONTENT_ID', 'C0C336FC-0EAA-416C-AC92-697C4A103EDF'))
                	</when>
                </choose>
            </foreach>
		</if>
	</sql>
	
	<sql id="hasFunctionFilterQueries">
		<if test="condition.hasFunctionFilter != null">
			AND 
			  <foreach item="hasFunction" collection="condition.hasFunctionFilter" separator=" OR " open="(" close=")">
                <choose>
                	<when test="hasFunction == 'is_videowall'">
                		(B.VWT_ID IS NOT NULL )
                	</when>
                	<when test="hasFunction == 'is_ams'">
                		(B.WEBCAM = <include refid="utils.true"/>) 
                	</when>
                </choose>
            </foreach>
		</if>
	</sql>
	
	<sql id="commonSearchKeywowrdQuery">
		<if test="condition.commonSearchKeyword != null and !condition.commonSearchKeyword.equals('')">
			AND (
				( LOWER(B.DEVICE_ID) LIKE #{condition.commonSearchKeyword} )
				OR
				( LOWER(B.DEVICE_NAME) LIKE #{condition.commonSearchKeyword} )
				OR
				( B.IP_ADDRESS LIKE #{condition.commonSearchKeyword} )
				OR
				( LOWER(B.DEVICE_MODEL_NAME) LIKE #{condition.commonSearchKeyword} )
				OR
				( LOWER(B.FIRMWARE_VERSION) LIKE #{condition.commonSearchKeyword} )
				OR
				( LOWER(B.APPLICATION_VERSION) LIKE #{condition.commonSearchKeyword} )
				OR
				( LOWER(B.LOCATION) LIKE #{condition.commonSearchKeyword} )
				OR
				( LOWER(B.SERIAL_DECIMAL) LIKE #{condition.commonSearchKeyword} )
				OR
				( LOWER(E.TAG_VALUE) LIKE #{condition.commonSearchKeyword} )
			)
		</if>
	</sql>

	<sql id="deviceFilterQuery">
		<if test="deviceTypeFilter != null">
			AND
			<foreach collection="deviceTypeFilter" open="(" separator="OR" close=")" item="deviceType">
				<choose>
					<when test="deviceType == constants.TYPE_SOC">
						(B.DEVICE_TYPE = #{constants.TYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_1_0})
					</when>
					<when test="deviceType == constants.TYPE_SOC2">
						(B.DEVICE_TYPE = #{constants.TYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_2_0})
					</when>
					<when test="deviceType == constants.TYPE_SOC3">
						(B.DEVICE_TYPE = #{constants.TYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_3_0})
					</when>
					<when test="deviceType == constants.TYPE_SOC4">
						(B.DEVICE_TYPE = #{constants.TYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_4_0})
					</when>
					<when test="deviceType == constants.TYPE_SOC5">
						(B.DEVICE_TYPE = #{constants.TYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_5_0})
					</when>
					<when test="deviceType == constants.TYPE_SOC6">
						(B.DEVICE_TYPE = #{constants.TYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_6_0})
					</when>
					<when test="deviceType == constants.TYPE_SOC7">
						(B.DEVICE_TYPE = #{constants.TYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_7_0})
					</when>
					<when test="deviceType == constants.TYPE_SOC9">
						(B.DEVICE_TYPE = #{constants.TYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_9_0})
					</when>
					<when test="deviceType == constants.TYPE_SOC10">
						(B.DEVICE_TYPE = #{constants.TYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_10_0})
					</when>
					<when test="deviceType == constants.TYPE_SIGNAGE3">
						(B.DEVICE_TYPE = #{constants.TYPE_SIGNAGE} AND (B.DEVICE_TYPE_VERSION =	#{constants.TYPE_VERSION_1_0} OR
						B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_3_0}))
					</when>
					<when test="deviceType == constants.TYPE_SIGNAGE4">
						(B.DEVICE_TYPE = #{constants.TYPE_SIGNAGE} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_4_0})
					</when>
					<when test="deviceType == constants.TYPE_SIGNAGE6">
						(B.DEVICE_TYPE = #{constants.TYPE_SIGNAGE} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_6_0})
					</when>
					<when test="deviceType == constants.TYPE_LEDBOX4">
						(B.DEVICE_TYPE = #{constants.TYPE_LEDBOX} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_4_0})
					</when>
					<when test="deviceType == constants.TYPE_LEDBOX6">
						(B.DEVICE_TYPE = #{constants.TYPE_LEDBOX} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_6_0})
					</when>
					<when test="deviceType == constants.TYPE_LEDBOX9">
						(B.DEVICE_TYPE = #{constants.TYPE_LEDBOX} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_9_0})
					</when>
					<when test="deviceType == constants.TYPE_LEDBOX10">
						(B.DEVICE_TYPE = #{constants.TYPE_LEDBOX} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_10_0})
					</when>
					<when test="deviceType == constants.TYPE_FLIP">
						(B.DEVICE_TYPE = #{constants.TYPE_FLIP} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_1_0})
					</when>
					<when test="deviceType == constants.TYPE_FLIP2">
						(B.DEVICE_TYPE = #{constants.TYPE_FLIP} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_2_0})
					</when>
					<when test="deviceType == constants.TYPE_FLIP3">
						(B.DEVICE_TYPE = #{constants.TYPE_FLIP} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_3_0})
					</when>
					<when test="deviceType == constants.TYPE_FLIP4">    <!-- Flip4 is Flip pro -->
						(B.DEVICE_TYPE = #{constants.TYPE_FLIP} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_4_0})
					</when>
					<when test="deviceType == constants.TYPE_RMS_S4">
						(B.DEVICE_TYPE = #{constants.TYPE_RMS_S} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_4_0})
					</when>
					<when test="deviceType == constants.TYPE_RMS_S5">
						(B.DEVICE_TYPE = #{constants.TYPE_RMS_S} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_5_0})
					</when>
					<when test="deviceType == constants.TYPE_RMS_S6">
						(B.DEVICE_TYPE = #{constants.TYPE_RMS_S} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_6_0})
					</when>
					<when test="deviceType == constants.TYPE_RMS_S7">
						(B.DEVICE_TYPE = #{constants.TYPE_RMS_S} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_7_0})
					</when>
					<when test="deviceType == constants.TYPE_RMS_S9">
						(B.DEVICE_TYPE = #{constants.TYPE_RMS_S} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_9_0})
					</when>
					<when test="deviceType == constants.TYPE_RMS_S10">
						(B.DEVICE_TYPE = #{constants.TYPE_RMS_S} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_10_0})
					</when>
					<when test="deviceType == constants.TYPE_RMS_LEDBOX4">
						(B.DEVICE_TYPE = #{constants.TYPE_RMS_LEDBOX} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_4_0})
					</when>
					<when test="deviceType == constants.TYPE_RMS_LEDBOX6">
						(B.DEVICE_TYPE = #{constants.TYPE_RMS_LEDBOX} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_6_0})
					</when>
					<when test="deviceType == constants.TYPE_RMS_LEDBOX9">
						(B.DEVICE_TYPE = #{constants.TYPE_RMS_LEDBOX} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_9_0})
					</when>
					<when test="deviceType == constants.TYPE_RMS_LEDBOX10">
						(B.DEVICE_TYPE = #{constants.TYPE_RMS_LEDBOX} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_10_0})
					</when>
					<when test="deviceType == constants.TYPE_RMS_SIGNAGE4">
						(B.DEVICE_TYPE = #{constants.TYPE_RMS_SIGNAGE} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_4_0})
					</when>
					<when test="deviceType == constants.TYPE_RMS_SIGNAGE6">
						(B.DEVICE_TYPE = #{constants.TYPE_RMS_SIGNAGE} AND B.DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_6_0})
					</when>
					<otherwise>
						(B.DEVICE_TYPE = #{deviceType} )
					</otherwise>
				</choose>
			</foreach>
		</if>
	</sql>
	
	<sql id="groupListQuery1">
		<if test="groupList1 != null and groupList1.size() > 0">
			AND C.GROUP_ID IN
			<foreach item="group" collection="groupList1" open="(" separator="," close=")">
				<foreach item="filterGroup" collection="filterGroupId" >
					<if test="group.group_id == filterGroup">
						#{group.group_id}
					</if>
				</foreach>
			</foreach>
		</if>
	</sql>

	<sql id="groupListQuery2">
		<choose>
			<when test="groupList2 != null and groupList2.size() > 0">
				<if test="groupList2 != null and groupList2.size() > 0">
					AND C.GROUP_ID IN
					<foreach item="group" collection="groupList2"
						open="(" separator="," close=")">
						#{group.group_id}
					</foreach>
				</if>
			</when>
			<otherwise>
			AND C.GROUP_ID = #{groupId}
			</otherwise>
		</choose>
	</sql>
	
	<sql id="limitResult">LIMIT #{pageSize} OFFSET #{startPos}</sql>
	
	
	
	<sql id="dateDiffQuery">
		<if test="string_from_dao == constants.DEVICE_STATUS_VIEW_CONNECTION">
			<![CDATA[AND EXTRACT('epoch' from CURRENT_TIMESTAMP - B.LAST_CONNECTION_TIME)::int < (B.MONITORING_INTERVAL + 1) * 60 ]]>
		</if>
 		<if test="string_from_dao == constants.DEVICE_STATUS_VIEW_DISCONNECTION"> 
 		    <if test= "condition.disconn_period == null">
 				<![CDATA[AND EXTRACT('epoch' from CURRENT_TIMESTAMP - B.LAST_CONNECTION_TIME)::int > (B.MONITORING_INTERVAL + 1) * 60 ]]>
			</if>
            <if test= "condition.disconn_period != null and condition.disconn_period != ''">
			<choose>
				<when test=" condition.disconn_period == 30">
		  			AND (CURRENT_TIMESTAMP - B.LAST_CONNECTION_TIME) &gt; INTERVAL '30 days' 
				</when>
			    <when test="condition.disconn_period == 60">
					AND (CURRENT_TIMESTAMP - B.LAST_CONNECTION_TIME) &gt; INTERVAL '60 days' 
				</when>
				<when test="condition.disconn_period == 90">
					AND (CURRENT_TIMESTAMP - B.LAST_CONNECTION_TIME) &gt; INTERVAL '90 days' 
				</when>
			</choose>
			</if> 
 		</if> 
	</sql>
    
    <sql id="dateDiffQuery" databaseId="mssql">
        <if test="string_from_dao == constants.DEVICE_STATUS_VIEW_CONNECTION">
            AND DATEDIFF(S, B.LAST_CONNECTION_TIME, GETDATE()) &lt; (B.MONITORING_INTERVAL + 1) * 60
        </if>
        
        <if test="string_from_dao == constants.DEVICE_STATUS_VIEW_DISCONNECTION"> 
        	<if test= "condition.disconn_period == null">
		        AND DATEDIFF(S, B.LAST_CONNECTION_TIME, GETDATE()) &gt; (B.MONITORING_INTERVAL + 1) * 60
        	</if>
           	<if test= "condition.disconn_period != null and condition.disconn_period != ''">
				<choose>
				    <when test="condition.disconn_period == 90">
						AND DATEDIFF(minute, B.LAST_CONNECTION_TIME, GETDATE())  &gt; 90*1440  
					</when>
				    <when test="condition.disconn_period == 60">
						AND DATEDIFF(minute, B.LAST_CONNECTION_TIME, GETDATE())  &gt; 60*1440  
					</when>
					<when test=" condition.disconn_period == 30">
			  			AND DATEDIFF(minute, B.LAST_CONNECTION_TIME, GETDATE())  &gt; 30*1440
					</when>
				</choose> 
			</if>	
        </if> 
    </sql>
    
    <sql id="dateDiffQuery" databaseId="mysql">
        <if test="string_from_dao == constants.DEVICE_STATUS_VIEW_CONNECTION">
            AND TIMESTAMPDIFF(SECOND, B.LAST_CONNECTION_TIME, NOW()) &lt; (B.MONITORING_INTERVAL + 1) * 60
        </if>
        
        <if test="string_from_dao == constants.DEVICE_STATUS_VIEW_DISCONNECTION"> 
            AND TIMESTAMPDIFF(SECOND, B.LAST_CONNECTION_TIME, NOW()) &gt; (B.MONITORING_INTERVAL + 1) * 60
        </if> 
    </sql>
	
	<sql id="commonQueryDeviceDisplayConfList">
		FROM
		MI_DMS_INFO_DISPLAY A, MI_DMS_INFO_DEVICE B
		<include refid="tagFrom"/>
        <include refid="com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceDaoMapper.iconsFrom">
        	<property name="alias" value="B"/>
        </include>
		, MI_DMS_MAP_GROUP_DEVICE C <include refid="deviceGroupAuthFrom"/>
		WHERE B.IS_APPROVED = <include refid="utils.true"/> AND A.DEVICE_ID = B.DEVICE_ID AND
		B.DEVICE_ID = C.DEVICE_ID <include refid="deviceGroupAuthWhere"/>

		<include refid="deviceFilterQuery" />

		<choose>
			<when test="groupList1 != null and groupList1.size() > 0">
				<include refid="groupListQuery1" />
			</when>
			<otherwise>
				<choose>
					<when test="(groupId != null and groupId != 0) or (groupList2 != null and groupList2.size() > 0)">
						<include refid="groupListQuery2" />
					</when>
					<otherwise>
						AND C.GROUP_ID != #{non_approval_group}
					</otherwise>
				</choose>
			</otherwise>
		</choose>

		<if test="deviceId != null and deviceId != ''">
			<bind name="deviceIdPattern" value="'%' + deviceId + '%'" />
			AND A.DEVICE_ID LIKE #{deviceIdPattern}
		</if>
		
		<if test="condition.src_name != null and condition.src_name != ''">
			<bind name="srcUpper" value="_parameter.condition.src_name.toUpperCase()"/>
			<bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
			AND ((UPPER(B.DEVICE_NAME)
			LIKE #{srcNamePattern}
			ESCAPE '^') OR (UPPER(B.MAC_ADDRESS)
			LIKE #{srcNamePattern}
			ESCAPE '^') OR (UPPER(B.DEVICE_MODEL_NAME)
			LIKE #{srcNamePattern}
			ESCAPE '^') OR (UPPER(B.IP_ADDRESS)
			LIKE #{srcNamePattern}
			ESCAPE '^') OR (UPPER(E.TAG_VALUE)
			LIKE #{srcNamePattern}
			ESCAPE '^') OR (UPPER(LOCATION)
			LIKE #{srcNamePattern}
			ESCAPE '^') OR (UPPER(FIRMWARE_VERSION)
			LIKE #{srcNamePattern}
			ESCAPE '^') OR (UPPER(SERIAL_DECIMAL)
			LIKE #{srcNamePattern}
			ESCAPE '^') OR (UPPER(APPLICATION_VERSION)
			LIKE #{srcNamePattern}
			ESCAPE '^') OR (UPPER(E.TAG_VALUE)
			LIKE #{srcNamePattern}
			ESCAPE '^'))
		</if>
		
		<if test="tagFilter != null">
            <foreach collection="tagFilter" open="AND E.TAG_ID IN (" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        
		<if test="condition.sourceFilterList != null" >
       		AND A.BASIC_SOURCE IN
       		   <foreach collection="condition.sourceFilterList" item="source" open="(" separator="," close=")" >
                   #{source}
               </foreach>
        </if>       
        
        <include refid="hasAlarmFilterQueries" />
        
        <include refid="hasFunctionFilterQueries" />
        
        <include refid="commonSearchKeywowrdQuery" />
        
		<include refid="dateDiffQuery"/>

		<include refid="deviceExpirationDate"/>
		
		AND B.IS_CHILD = <include refid="utils.false"/>
	</sql>
	
	<select id="getDeviceDisplayConfList"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf">
		
		<bind name="dirUpper" value="_parameter.dir.toUpperCase()"/>
		<bind name="srcUpper" value="_parameter.src.toUpperCase()"/>
		
		SELECT A.DEVICE_ID, B.DEVICE_MODEL_CODE, B.DEVICE_NAME, B.DEVICE_TYPE, B.DEVICE_TYPE_VERSION, B.ERROR_FLAG,
		B.MDC_UPDATE_TIME, BASIC_VOLUME, BASIC_MUTE, BASIC_SOURCE,
		BASIC_PANEL_STATUS,
		MISC_REMOCON, MNT_SAFETY_LOCK, MISC_PANEL_LOCK,
		MISC_OSD, MISC_ALL_LOCK,
		DIAGNOSIS_MONITOR_TEMPERATURE,
		DIAGNOSIS_ALARM_TEMPERATURE,
		DIAGNOSIS_PANEL_ON_TIME,
		B.VWT_ID, B.WEBCAM,
		B.CHILD_CNT,
		B.CONN_CHILD_CNT,
		B.IS_CHILD
		<include refid="com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceDaoMapper.iconsSelect"/>
		<include refid="commonQueryDeviceDisplayConfList"/>
		
								GROUP BY A.DEVICE_ID, B.DEVICE_MODEL_CODE, B.DEVICE_NAME, B.DEVICE_TYPE, B.DEVICE_TYPE_VERSION, B.ERROR_FLAG,
		B.MDC_UPDATE_TIME, BASIC_VOLUME, BASIC_MUTE, BASIC_SOURCE,
		BASIC_PANEL_STATUS,
		MISC_REMOCON, MNT_SAFETY_LOCK, MISC_PANEL_LOCK,
		MISC_OSD, MISC_ALL_LOCK,
		DIAGNOSIS_MONITOR_TEMPERATURE,
		DIAGNOSIS_ALARM_TEMPERATURE,
		DIAGNOSIS_PANEL_ON_TIME,
		B.VWT_ID, B.WEBCAM,
		B.CHILD_CNT,
		B.CONN_CHILD_CNT,
		B.IS_CHILD
		<if test="sort != null and sort != '' and dir != null and dir != ''">
                        <bind name="safe_sort" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(sort)" />
			<bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(dirUpper)" />
			ORDER BY ${safe_sort} ${safe_sortOrder}
		</if>
		
		<include refid="limitResult"/>
		
	</select>
	
	<select id="getDeviceDisplayConfList"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf" databaseId="mssql">
		
		<bind name="dirUpper" value="_parameter.dir.toUpperCase()"/>
		<bind name="srcUpper" value="_parameter.src.toUpperCase()"/>
		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
		SELECT * FROM (
		SELECT A.DEVICE_ID, B.DEVICE_MODEL_CODE, B.DEVICE_NAME, B.DEVICE_TYPE, B.DEVICE_TYPE_VERSION, B.ERROR_FLAG,
		B.MDC_UPDATE_TIME, BASIC_VOLUME, BASIC_MUTE, BASIC_SOURCE,
		BASIC_PANEL_STATUS,
		MISC_REMOCON, MNT_SAFETY_LOCK, MISC_PANEL_LOCK,
		MISC_OSD, MISC_ALL_LOCK,
		DIAGNOSIS_MONITOR_TEMPERATURE,
		DIAGNOSIS_ALARM_TEMPERATURE,
		DIAGNOSIS_PANEL_ON_TIME,
		B.VWT_ID, B.WEBCAM,
		B.CHILD_CNT,
		B.CONN_CHILD_CNT,
		B.IS_CHILD,
		ROW_NUMBER() OVER(ORDER BY
		<choose>
			<when test="sort != null and sort.length() > 0">
                <bind name="safe_sortColumn" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(sort)" />
				<choose>
			        <when test="safe_sortColumn != 'DEVICE_ID' and safe_sortColumn != 'DEVICE_NAME'">
			        	${safe_sortColumn}
			        </when>
		        	<otherwise>
		            	B.${safe_sortColumn}
		        	</otherwise> 
		        </choose>	
				<if test="dir != null and dir.length() > 0">
					<bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(dir)" />
					${safe_sortOrder}
				</if>
			</when>
			<otherwise> A.DEVICE_ID ASC </otherwise>
		</choose>
		) as rownum
		<include refid="com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceDaoMapper.iconsSelect"/>
		<include refid="commonQueryDeviceDisplayConfList"/>
				GROUP BY A.DEVICE_ID, B.DEVICE_MODEL_CODE, B.DEVICE_NAME, B.DEVICE_TYPE, B.DEVICE_TYPE_VERSION, B.ERROR_FLAG,
		B.MDC_UPDATE_TIME, BASIC_VOLUME, BASIC_MUTE, BASIC_SOURCE,
		BASIC_PANEL_STATUS,
		MISC_REMOCON, MNT_SAFETY_LOCK, MISC_PANEL_LOCK,
		MISC_OSD, MISC_ALL_LOCK,
		DIAGNOSIS_MONITOR_TEMPERATURE,
		DIAGNOSIS_ALARM_TEMPERATURE,
		DIAGNOSIS_PANEL_ON_TIME,
		B.VWT_ID, B.WEBCAM,
		B.CHILD_CNT,
		B.CONN_CHILD_CNT,
		B.IS_CHILD
		) as SubQuery
		WHERE rownum > ${safe_startPos} and rownum &lt;= ${safe_rownumLimit}
		ORDER BY rownum
		
	</select>
	
	<select id="getDeviceDisplayConfListCount" resultType="int">
		
		<bind name="srcUpper" value="_parameter.src.toUpperCase()"/>
		
		SELECT COUNT(DISTINCT A.DEVICE_ID)
		<include refid="commonQueryDeviceDisplayConfList"/>
		
	</select>
	
	<sql id="groupQuery">
		<choose>
			<when test="isRoot">
				<if test="groupList != null and groupList.size() > 0">
					AND C.GROUP_ID IN
					<foreach item="group" index="index" collection="groupList" open="(" separator="," close=")">
						#{group.group_id}
					</foreach>
				</if>
			</when>
			<otherwise>
			AND C.GROUP_ID = #{groupId}
			</otherwise>
		</choose>
	</sql>
	
	<select id="getDeviceDisplayConfFilterList" 
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf">
		
		<bind name="dirUpper" value="_parameter.dir.toUpperCase()"/>
		<bind name="srcUpper" value="_parameter.src.toUpperCase()"/>
		
		SELECT A.DEVICE_ID, B.DEVICE_NAME,
		BASIC_VOLUME, BASIC_MUTE, BASIC_SOURCE, BASIC_PANEL_STATUS,
		DIAGNOSIS_MONITOR_TEMPERATURE, DIAGNOSIS_ALARM_TEMPERATURE,
		BASIC_DIRECT_CHANNEL
		<include refid="com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceDaoMapper.iconsSelect"/>
		
		FROM MI_DMS_INFO_DISPLAY A, MI_DMS_INFO_DEVICE B
		<include refid="tagFrom"/>
        <include refid="com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceDaoMapper.iconsFrom">
        	<property name="alias" value="B"/>
        </include>
		, MI_DMS_MAP_GROUP_DEVICE C

		WHERE B.IS_APPROVED = <include refid="utils.true"/> AND A.DEVICE_ID = B.DEVICE_ID AND B.DEVICE_ID = C.DEVICE_ID
	
		<choose>
			<when test="groupId != null and groupId != 0">
				<include refid="groupQuery"/>
			</when>
			<otherwise>
				AND C.GROUP_ID != #{non_approval_group}
			</otherwise>
		</choose>
		
		<if test="deviceId != null and deviceId != ''">
			<bind name="deviceIdPattern" value="'%' + deviceId + '%'"/>
			AND A.DEVICE_ID LIKE #{deviceIdPattern}
		</if>
		
		<if test="src != null and src != ''">
            AND ((UPPER(B.DEVICE_NAME)
                LIKE '%' <include refid="utils.concatenate"/> #{src} <include refid="utils.concatenate"/> '%'
                ESCAPE '^') OR (UPPER(B.MAC_ADDRESS)
                LIKE '%' <include refid="utils.concatenate"/> #{src} <include refid="utils.concatenate"/> '%'
                ESCAPE '^') OR (UPPER(B.DEVICE_MODEL_NAME)
                LIKE '%' <include refid="utils.concatenate"/> #{src} <include refid="utils.concatenate"/> '%'
                ESCAPE '^') OR (UPPER(B.IP_ADDRESS)
                LIKE '%' <include refid="utils.concatenate"/> #{src} <include refid="utils.concatenate"/> '%'
                ESCAPE '^') OR (UPPER(E.TAG_VALUE)
                LIKE '%' <include refid="utils.concatenate"/> #{src} <include refid="utils.concatenate"/> '%'
                ESCAPE '^'))
        </if>
		<if test="tagFilter != null">
            <foreach collection="tagFilter" open="AND E.TAG_ID IN (" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        
        <include refid="hasAlarmFilterQueries" />
        
        <include refid="hasFunctionFilterQueries" />
        
        <include refid="commonSearchKeywowrdQuery" />
        
		<if test="sort != null and sort != '' and dir != null and dir != ''">
                        <bind name="safe_sort" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(sort)" />
			<bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(dirUpper)" />
			ORDER BY ${safe_sort} ${safe_sortOrder}
		</if>
				GROUP BY A.DEVICE_ID, B.DEVICE_NAME,
		BASIC_VOLUME, BASIC_MUTE, BASIC_SOURCE, BASIC_PANEL_STATUS,
		DIAGNOSIS_MONITOR_TEMPERATURE, DIAGNOSIS_ALARM_TEMPERATURE,
		BASIC_DIRECT_CHANNEL
	</select>
	
	<select id="checkSameVWLLayout" resultType="int">
		<bind name="deviceIdPattern" value="deviceId+ '\\_%'"/> 
		SELECT COUNT(DISTINCT VWL_LAYOUT) FROM MI_DMS_INFO_DISPLAY WHERE DEVICE_ID LIKE #{deviceIdPattern}
	</select>
	
	<select id="checkSameVWLLayout" resultType="int" databaseId="mssql">
		<bind name="deviceIdPattern" value="deviceId+ '[_]%'"/> 
		SELECT COUNT(DISTINCT VWL_LAYOUT) FROM MI_DMS_INFO_DISPLAY WHERE DEVICE_ID LIKE #{deviceIdPattern}
	</select>
	
	<select id="checkDifferentVWLPosition" resultType="int">
		<bind name="deviceIdPattern" value="deviceId+ '\\_%'"/> 
		SELECT COUNT(DISTINCT VWL_POSITION) FROM MI_DMS_INFO_DISPLAY WHERE DEVICE_ID LIKE #{deviceIdPattern}		
	</select>
	
	<select id="checkDifferentVWLPosition" resultType="int" databaseId="mssql">
		<bind name="deviceIdPattern" value="deviceId+ '[_]%'"/> 
		SELECT COUNT(DISTINCT VWL_POSITION) FROM MI_DMS_INFO_DISPLAY WHERE DEVICE_ID LIKE #{deviceIdPattern}		
	</select>
	
	<sql id="tagFrom">
    	left join MI_TAG_MAP_DEVICE_TAG F on B.DEVICE_ID = F.DEVICE_ID
		left join MI_TAG_INFO_TAG E on E.TAG_ID = F.TAG_ID 
    </sql>
    
    <sql id="getInsufficientCapacity" >
		( SELECT coalesce(INSUFFICIENT_CAPACITY, 1000) FROM MI_SYSTEM_INFO_SETUP WHERE ORGANIZATION_ID = 0 )
	</sql>
    
    
</mapper>

