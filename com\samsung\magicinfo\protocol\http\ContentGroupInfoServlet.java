package com.samsung.magicinfo.protocol.http;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Iterator;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.Logger;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

public class ContentGroupInfoServlet extends HttpServlet {
   private static final long serialVersionUID = -1143056137010460527L;
   private Logger logger = LoggingManagerV2.getLogger(ContentInfoServlet.class);

   public ContentGroupInfoServlet() {
      super();
   }

   public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");
      this.doPost(request, response);
   }

   public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");
      PrintWriter out = response.getWriter();

      try {
         String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
         if (!SecurityUtils.getSafeFile(CONTENTS_HOME).exists() && !SecurityUtils.getSafeFile(CONTENTS_HOME).mkdirs()) {
            this.logger.error("mkdir Fail");
         }

         CommonsMultipartResolver multiPartResolver = new CommonsMultipartResolver();
         MultipartHttpServletRequest multiRequest = multiPartResolver.resolveMultipart(request);
         Iterator files = multiRequest.getFileNames();

         String fileName;
         while(files.hasNext()) {
            fileName = (String)files.next();
            MultipartFile multipartFile = multiRequest.getFile(fileName);
            if (multipartFile != null) {
               multipartFile.transferTo(SecurityUtils.getSafeFile(CONTENTS_HOME + File.separator + fileName));
            }
         }

         multiPartResolver.cleanupMultipart(multiRequest);
         fileName = multiRequest.getParameter("GroupID");
         ContentInfo info = ContentInfoImpl.getInstance();
         Group group = info.getGroupInfo(Long.parseLong(fileName));
         out.println("GroupName↓" + group.getGroup_name());
         out.close();
      } catch (Exception var11) {
         this.logger.error(var11.getMessage());
         response.sendError(600, var11.toString());
      }

   }
}
