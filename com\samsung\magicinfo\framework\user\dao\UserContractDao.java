package com.samsung.magicinfo.framework.user.dao;

import com.samsung.common.db.PagedListInfo;
import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.entity.UserContract;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class UserContractDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(UserContractDao.class);

   public UserContractDao() {
      super();
   }

   public UserContractDao(SqlSession session) {
      super(session);
   }

   public boolean addUserContract(UserContract userContract) throws SQLException {
      boolean rtn = false;
      rtn = ((UserContractDaoMapper)this.getMapper()).addUserContract(userContract);
      return rtn;
   }

   public int addUserContractMap(String user_id, String contract_id) throws SQLException {
      int rtn = false;
      int rtn = ((UserContractDaoMapper)this.getMapper()).addUserContractMap(user_id, contract_id);
      return rtn;
   }

   public boolean deleteUserContract(String contract_id) {
      boolean rtn = false;

      try {
         if (!((UserContractDaoMapper)this.getMapper()).deleteUserContract(contract_id)) {
            return false;
         }

         if (this.deleteUserMapContract(contract_id) != 1) {
            return false;
         }

         rtn = true;
      } catch (Exception var4) {
         this.logger.error("", var4);
      }

      return rtn;
   }

   public int deleteUserMapContract(String contract_id) throws SQLException {
      return ((UserContractDaoMapper)this.getMapper()).deleteUserMapContract(contract_id);
   }

   public int checkUserId(String contract_id) throws SQLException {
      return ((UserContractDaoMapper)this.getMapper()).checkUserId(contract_id);
   }

   public List getAllNonApprovedContract(Map map, int startPos, int pageSize) throws SQLException {
      try {
         Map paramsMap = new HashMap();
         paramsMap.putAll(map);
         int offset = 0;
         if (startPos > 0) {
            offset = startPos - 1;
         }

         paramsMap.put("offset", offset);
         paramsMap.put("pageSize", pageSize);
         return ((UserContractDaoMapper)this.getMapper()).getAllNonApprovedContract(paramsMap);
      } catch (SQLException var6) {
         this.logger.error("", var6);
         return new ArrayList();
      }
   }

   public String getUseridfromMapContract(String contract_id) throws SQLException {
      String rtn = null;
      rtn = ((UserContractDaoMapper)this.getMapper()).getUseridfromMapContract(contract_id);
      return rtn;
   }

   public User getUseridfromUser(String contract_id) throws SQLException {
      User rtn = null;
      rtn = ((UserContractDaoMapper)this.getMapper()).getUseridfromUser(contract_id);
      return rtn;
   }

   public boolean setContractApproved(String contract_id) throws SQLException {
      boolean rtn = false;
      rtn = ((UserContractDaoMapper)this.getMapper()).setContractApproved(contract_id);
      return rtn;
   }

   public Long getUseridFromContract(String contract_id) throws SQLException {
      return ((UserContractDaoMapper)this.getMapper()).getUseridFromContract(contract_id);
   }

   public PagedListInfo getContractList(Map map, int startPos, int pageSize) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.put("limit", pageSize);
      paramsMap.put("offset", startPos > 0 ? startPos - 1 : 0);
      if (MapUtils.isNotEmpty(map) && StringUtils.isNotBlank((String)map.get("user_id"))) {
         paramsMap.put("user_id", (String)map.get("user_id"));
      }

      return new PagedListInfo(((UserContractDaoMapper)this.getMapper()).getContractList(paramsMap), ((UserContractDaoMapper)this.getMapper()).getContractListCount(paramsMap));
   }

   public int compareContractformDB(String contract_id) throws SQLException {
      return ((UserContractDaoMapper)this.getMapper()).compareContractformDB(contract_id);
   }

   public boolean initUserCount(String userId, String mfaType) throws SQLException {
      return ((UserContractDaoMapper)this.getMapper()).initUserCount(userId, mfaType);
   }

   public Map checkUserFail(String userId, String mfaType) throws SQLException {
      return ((UserContractDaoMapper)this.getMapper()).checkUserFail(userId, mfaType);
   }

   public boolean updateUserFailCount(String userId, String mfaType) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var4;
      try {
         if (((UserContractDaoMapper)this.getMapper(session)).updateUserFailCount(userId, mfaType) || ((UserContractDaoMapper)this.getMapper(session)).insertFailCount(userId, mfaType)) {
            session.commit();
            var4 = true;
            return var4;
         }

         session.rollback();
         var4 = false;
      } catch (SQLException var9) {
         session.rollback();
         boolean var5 = false;
         return var5;
      } finally {
         session.close();
      }

      return var4;
   }

   public boolean deleteUserFailCount(String userId, String mfaType) throws SQLException {
      return ((UserContractDaoMapper)this.getMapper()).deleteUserFailCount(userId, mfaType);
   }

   public boolean checkValidTimeForSignup() throws SQLException {
      Timestamp maxCreateDate = ((UserContractDaoMapper)this.getMapper()).getMaxCreateDate();
      Timestamp currentTime = new Timestamp(System.currentTimeMillis());
      return currentTime.getTime() - maxCreateDate.getTime() > 60000L;
   }

   public boolean updateLoginCount(String userId) throws SQLException {
      return ((UserContractDaoMapper)this.getMapper()).updateLoginCount(userId);
   }
}
