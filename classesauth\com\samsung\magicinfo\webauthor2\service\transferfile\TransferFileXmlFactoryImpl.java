package com.samsung.magicinfo.webauthor2.service.transferfile;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.model.FileInfo;
import com.samsung.magicinfo.webauthor2.xml.transferfile.TransferFileType;
import java.util.UUID;
import javax.inject.Inject;
import javax.xml.transform.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Service;
import org.springframework.xml.transform.StringResult;

@Service
public class TransferFileXmlFactoryImpl implements TransferFileXmlFactory {
  private static Logger logger = LoggerFactory.getLogger(TransferFileXmlFactoryImpl.class);
  
  private final Jaxb2Marshaller jaxb2MarshallerForTransferFile;
  
  @Inject
  public TransferFileXmlFactoryImpl(Jaxb2Marshaller jaxb2MarshallerForTransferFile) {
    this.jaxb2MarshallerForTransferFile = jaxb2MarshallerForTransferFile;
  }
  
  public String marshal(FileInfo dlkFileInfo, int reqIndex) {
    StringResult result = new StringResult();
    this.jaxb2MarshallerForTransferFile.marshal(createTransferFile(dlkFileInfo, reqIndex), (Result)result);
    return result.toString();
  }
  
  private TransferFileType createTransferFile(FileInfo dlkFileInfo, int reqIndex) {
    String fileId;
    TransferFileType transferFileType = new TransferFileType();
    if (Strings.isNullOrEmpty(dlkFileInfo.getFileId())) {
      UUID newFileId = UUID.randomUUID();
      fileId = newFileId.toString();
    } else {
      fileId = dlkFileInfo.getFileId();
    } 
    transferFileType.setFileId(fileId);
    transferFileType.setFileName(dlkFileInfo.getFileName());
    transferFileType.setFileHashValue(dlkFileInfo.getFileHash());
    transferFileType.setFileSize(dlkFileInfo.getSize());
    transferFileType.setReqIndex(reqIndex);
    transferFileType.setStorePath(".\\");
    transferFileType.setSupportFileItems(false);
    transferFileType.setType("dlk");
    return transferFileType;
  }
}
