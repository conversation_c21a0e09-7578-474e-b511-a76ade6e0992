package com.samsung.magicinfo.webauthor2.model.svg;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import java.io.Serializable;

public class MediaSource implements Serializable {
  private static final long serialVersionUID = 6928305098806111739L;
  
  private String contentId;
  
  private String contentName;
  
  private String fileId;
  
  private String data;
  
  private String thumb;
  
  private String thumbLg;
  
  private boolean supportFileItem = false;
  
  private MediaType mediaType;
  
  private String fileType;
  
  private String fileName;
  
  private String fileHash;
  
  private int mediaWidth;
  
  private int mediaHeight;
  
  private long mediaSize;
  
  private double mediaDuration;
  
  private String path;
  
  @JsonIgnore
  private boolean isNew = false;
  
  @JsonIgnore
  private String startupPage;
  
  @JsonIgnore
  private String title;
  
  public MediaSource() {}
  
  @JsonCreator
  public MediaSource(@JsonProperty(value = "contentId", required = false) String contentId, @JsonProperty(value = "contentName", required = false) String contentName, @JsonProperty(value = "fileId", required = false) String fileId, @JsonProperty(value = "data", required = true) String data, @JsonProperty(value = "thumb", required = false) String thumb, @JsonProperty(value = "thumbLg", required = false) String thumbLg, @JsonProperty(value = "mediaType", required = false) MediaType mediaType, @JsonProperty(value = "fileType", required = false) String fileType, @JsonProperty(value = "fileName", required = false) String fileName, @JsonProperty(value = "fileHash", required = false) String fileHash, @JsonProperty(value = "mediaWidth", required = false) int mediaWidth, @JsonProperty(value = "mediaHeight", required = false) int mediaHeight, @JsonProperty(value = "mediaSize", required = false) long mediaSize, @JsonProperty(value = "mediaDuration", required = false) double mediaDuration) {
    this.contentId = contentId;
    this.contentName = contentName;
    this.fileId = fileId;
    this.data = data;
    this.thumb = thumb;
    this.thumbLg = thumbLg;
    this.mediaType = mediaType;
    this.fileType = fileType;
    this.fileName = fileName;
    this.fileHash = fileHash;
    this.mediaWidth = mediaWidth;
    this.mediaHeight = mediaHeight;
    this.mediaSize = mediaSize;
    this.mediaDuration = mediaDuration;
  }
  
  public String getContentId() {
    return this.contentId;
  }
  
  public void setContentId(String contentId) {
    this.contentId = contentId;
  }
  
  public String getContentName() {
    return this.contentName;
  }
  
  public void setContentName(String contentName) {
    this.contentName = contentName;
  }
  
  public String getFileId() {
    return this.fileId;
  }
  
  public void setFileId(String fileId) {
    this.fileId = fileId;
  }
  
  public String getData() {
    return this.data;
  }
  
  public void setData(String data) {
    this.data = data;
  }
  
  public String getThumb() {
    return this.thumb;
  }
  
  public void setThumb(String thumb) {
    this.thumb = thumb;
  }
  
  public String getThumbLg() {
    return this.thumbLg;
  }
  
  public void setThumbLg(String thumbLg) {
    this.thumbLg = thumbLg;
  }
  
  public MediaType getMediaType() {
    return this.mediaType;
  }
  
  public void setMediaType(MediaType mediaType) {
    this.mediaType = mediaType;
  }
  
  public String getFileType() {
    return this.fileType;
  }
  
  public void setFileType(String fileType) {
    this.fileType = fileType;
  }
  
  public String getFileName() {
    return this.fileName;
  }
  
  public void setFileName(String fileName) {
    this.fileName = fileName;
  }
  
  public String getFileHash() {
    return this.fileHash;
  }
  
  public void setFileHash(String fileHash) {
    this.fileHash = fileHash;
  }
  
  public int getMediaWidth() {
    return this.mediaWidth;
  }
  
  public void setMediaWidth(int mediaWidth) {
    this.mediaWidth = mediaWidth;
  }
  
  public int getMediaHeight() {
    return this.mediaHeight;
  }
  
  public void setMediaHeight(int mediaHeight) {
    this.mediaHeight = mediaHeight;
  }
  
  public long getMediaSize() {
    return this.mediaSize;
  }
  
  public void setMediaSize(long mediaSize) {
    this.mediaSize = mediaSize;
  }
  
  public double getMediaDuration() {
    return this.mediaDuration;
  }
  
  public void setMediaDuration(double mediaDuration) {
    this.mediaDuration = mediaDuration;
  }
  
  public String getPath() {
    return this.path;
  }
  
  public void setPath(String path) {
    this.path = path;
  }
  
  public boolean isIsNew() {
    return this.isNew;
  }
  
  public void setIsNew(boolean isNew) {
    this.isNew = isNew;
  }
  
  public String getStartupPage() {
    return this.startupPage;
  }
  
  public void setStartupPage(String startupPage) {
    this.startupPage = startupPage;
  }
  
  public String getTitle() {
    return this.title;
  }
  
  public void setTitle(String title) {
    this.title = title;
  }
  
  public boolean isSupportFileItem() {
    return this.supportFileItem;
  }
  
  public void setSupportFileItem(boolean suportFileItem) {
    this.supportFileItem = suportFileItem;
  }
  
  public String toString() {
    return "MediaSource [contentId=" + this.contentId + ", contentName=" + this.contentName + ", fileId=" + this.fileId + ", data=" + this.data + ", fileType=" + this.fileType + ", fileName=" + this.fileName + ", fileHash=" + this.fileHash + ", mediaSize=" + this.mediaSize + ", path=" + this.path + "]";
  }
}
