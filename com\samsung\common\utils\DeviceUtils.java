package com.samsung.common.utils;

import com.samsung.common.cache.CacheFactory;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.device.constants.DeviceConstants;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceGroupDao;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceControl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSecurityConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemSetupConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTimeTimerConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.LedCabinet;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSecurityConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSecurityConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.LedCabinetConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.LedCabinetConfManagerImpl;
import com.samsung.magicinfo.framework.device.job.manager.JobManager;
import com.samsung.magicinfo.framework.device.job.manager.JobManagerImpl;
import com.samsung.magicinfo.framework.device.log.entity.ServerLogEntity;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManager;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManagerImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocInfo;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocInfoImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManager;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManagerImpl;
import com.samsung.magicinfo.framework.device.preconfig.manager.DevicePreconfigInfo;
import com.samsung.magicinfo.framework.device.preconfig.manager.DevicePreconfigInfoImpl;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManager;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManagerImpl;
import com.samsung.magicinfo.framework.monitoring.dao.ScreenCaptureDAO;
import com.samsung.magicinfo.framework.monitoring.entity.ConnectionInfoEntity;
import com.samsung.magicinfo.framework.monitoring.entity.ContentList;
import com.samsung.magicinfo.framework.monitoring.entity.CurrentPlayingEntity;
import com.samsung.magicinfo.framework.monitoring.entity.ScheduleInfoEntity;
import com.samsung.magicinfo.framework.monitoring.entity.ScreenCaptureEntity;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.role.dao.AbilityDao;
import com.samsung.magicinfo.framework.role.manager.AbilityInfo;
import com.samsung.magicinfo.framework.role.manager.AbilityInfoImpl;
import com.samsung.magicinfo.framework.scheduler.dao.ScheduleAdminDao;
import com.samsung.magicinfo.framework.scheduler.dao.ScheduleInfoDAO;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfo;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleAdminInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleAdminInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.setup.dao.SlmLicenseDao;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseEntity;
import com.samsung.magicinfo.framework.setup.manager.LicenseManager;
import com.samsung.magicinfo.framework.setup.manager.LicenseManagerImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManager;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManagerImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.entity.UserGroup;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.entity.Mail;
import com.samsung.magicinfo.protocol.interfaces.WSCall;
import com.samsung.magicinfo.protocol.scheduler.ScheduleManager;
import com.samsung.magicinfo.protocol.servicemanager.ServiceDispatcher;
import com.samsung.magicinfo.protocol.servicemanager.WSRMServiceDispatcher;
import com.samsung.magicinfo.protocol.util.ExpirationDeviceJob;
import com.samsung.magicinfo.protocol.util.mail.MailConfigurationManager;
import com.samsung.magicinfo.protocol.util.mail.MailManager;
import com.samsung.magicinfo.protocol.util.mail.MailManagerInterface;
import com.samsung.magicinfo.restapi.device.model.V2DeviceCabinetGroupConf;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URLEncoder;
import java.nio.file.CopyOption;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.StringTokenizer;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.CaseInsensitiveMap;
import org.apache.commons.lang.StringUtils;
import org.apache.ftpserver.db.DownloadInfo;
import org.apache.ftpserver.db.DownloadInfoImpl;
import org.apache.ftpserver.ftplet.UserManager;
import org.apache.ftpserver.usermanager.DbUserManagerFactory;
import org.apache.ftpserver.usermanager.impl.BaseUser;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;
import org.quartz.JobDetail;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.SimpleTrigger;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

public class DeviceUtils {
   static Logger logger = LoggingManagerV2.getLogger(DeviceUtils.class);
   static DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
   static DeviceDisplayConfManager deviceDisplayDao = DeviceDisplayConfManagerImpl.getInstance();
   static DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
   static LedCabinetConfManager ledCabinetMgr = LedCabinetConfManagerImpl.getInstance();
   static DeviceSystemSetupConfManager systemSetupDao = DeviceSystemSetupConfManagerImpl.getInstance();
   static LicenseManager licenseMgr = LicenseManagerImpl.getInstance();
   static SlmLicenseManager slmlicenseMgr = SlmLicenseManagerImpl.getInstance();
   static MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
   private static float suppDatalinkVersion = 0.0F;
   private static float suppSetupExtVersion = 0.0F;
   private static final String CONNECTIONINFO_REPOSITORY_LOOKUP_ID = "CONNECTIONINFO_MEMORY_MAP";
   private static Map deviceTypeAndVersion = null;
   public static final String A_PLAYER = "APLAYER";
   public static final String FLIP = "FLIP";
   public static final String FLIP2 = "FLIP2";
   public static final String FLIP3 = "FLIP3";
   public static final String FLIP4 = "FLIP4";
   public static final String L_PLAYER = "LPLAYER";
   public static final String I_PLAYER = "IPLAYER";
   public static final String S_PLAYER = "SPLAYER";
   public static final String S2_PLAYER = "S2PLAYER";
   public static final String S3_PLAYER = "S3PLAYER";
   public static final String S4_PLAYER = "S4PLAYER";
   public static final String S5_PLAYER = "S5PLAYER";
   public static final String S6_PLAYER = "S6PLAYER";
   public static final String S7_PLAYER = "S7PLAYER";
   public static final String S9_PLAYER = "S9PLAYER";
   public static final String S10_PLAYER = "S10PLAYER";
   public static final String SIGNAGE = "SIGNAGE";
   public static final String DEFAULT = "default";
   public static final String ORGANIZATION = "ORGANIZATION";
   public static final String R_KIOSK = "RKIOSK";
   public static final String RS4_PLAYER = "RS4PLAYER";
   public static final String RS5_PLAYER = "RS5PLAYER";
   public static final String RS6_PLAYER = "RS6PLAYER";
   public static final String RS7_PLAYER = "RS7PLAYER";
   public static final String RS9_PLAYER = "RS9PLAYER";
   public static final String RS10_PLAYER = "RS10PLAYER";
   public static final String RI_PLAYER = "RIPLAYER";
   public static final String W_PLAYER = "WPLAYER";
   public static final String LEDBOX = "LEDBOX";
   static Boolean alwaysFullSize = false;

   public DeviceUtils() {
      super();
   }

   public static float setSuppClientVersion(String configToken) {
      float version = 0.0F;

      try {
         version = Float.parseFloat(CommonConfig.get(configToken));
      } catch (Exception var3) {
      }

      return version;
   }

   public static float getSuppDatalinkClientVersion() {
      if (suppDatalinkVersion == 0.0F) {
         suppDatalinkVersion = setSuppClientVersion("supp.client_version.datalink");
      }

      logger.error("[MagicInfo_DeviceUtils] supp datalink version = " + suppDatalinkVersion);
      return suppDatalinkVersion;
   }

   public static float getSuppSetupExtClientVersion() {
      if (suppSetupExtVersion == 0.0F) {
         suppSetupExtVersion = setSuppClientVersion("supp.client_version.setup_ext");
      }

      logger.error("[MagicInfo_DeviceUtils] supp setupExt version = " + suppSetupExtVersion);
      return suppSetupExtVersion;
   }

   public static List getTagIdStringList(String deviceId) {
      ArrayList result = new ArrayList();

      try {
         List tagIdList = systemSetupDao.getDeviceTag(deviceId);
         if (tagIdList != null) {
            for(int j = 0; j < tagIdList.size(); ++j) {
               result.add(((Map)tagIdList.get(j)).get("tag_id").toString());
            }

            if (tagIdList.size() <= 0 && result.size() <= 0) {
               result.add("0");
            }
         }

         return result;
      } catch (Exception var4) {
         logger.error("", var4);
         return null;
      }
   }

   public static int getFileNumberfromZip(String zipFile) {
      int count = 0;
      ZipInputStream in = null;

      byte var4;
      try {
         if (zipFile != null) {
            in = new ZipInputStream(new FileInputStream(zipFile));
            ZipEntry entry = null;
            if (in != null) {
               for(; (entry = in.getNextEntry()) != null; in.closeEntry()) {
                  if (entry != null && !entry.isDirectory()) {
                     ++count;
                  }
               }
            }

            in.close();
            return count;
         }

         int var3 = count;
         return var3;
      } catch (Exception var15) {
         logger.error("zip file extract fail!!!", var15);
         var4 = 0;
      } finally {
         try {
            if (in != null) {
               in.close();
            }
         } catch (IOException var14) {
            logger.error("", var14);
         }

      }

      return var4;
   }

   public static String getSoftwareVersion(String zipFile) {
      String VER_FILE_NAME = "info.txt";
      String version = "";
      ZipFile zip_file = null;
      ZipEntry zip_entry = null;

      try {
         zipFile = zipFile.replace("\\", "/");
         zip_file = new ZipFile(zipFile);
         Enumeration e = zip_file.entries();

         while(e.hasMoreElements()) {
            ZipEntry entry = (ZipEntry)e.nextElement();
            String fileName = SecurityUtils.getSafeFile(entry.getName()).getName();
            if (fileName != null && fileName.equalsIgnoreCase("info.txt")) {
               String entryName = entry.getName();
               zip_entry = zip_file.getEntry(entryName);
               InputStream input = null;
               BufferedReader br = null;

               try {
                  input = zip_file.getInputStream(zip_entry);
                  br = new BufferedReader(new InputStreamReader(input, "UTF-8"));
                  version = br.readLine();
               } finally {
                  if (input != null) {
                     try {
                        input.close();
                     } catch (Exception var39) {
                        logger.error("", var39);
                     }
                  }

                  if (br != null) {
                     try {
                        br.close();
                     } catch (Exception var38) {
                        logger.error("", var38);
                     }
                  }

               }
            }
         }
      } catch (IOException var41) {
         logger.error("Error zip file", var41);
      } catch (Exception var42) {
         logger.error("InputStream error!", var42);
      } finally {
         try {
            if (zip_file != null) {
               zip_file.close();
            }
         } catch (IOException var37) {
            logger.error("Error close zip file", var37);
         }

      }

      return version;
   }

   public static String getSearchZipFile(String zipFilePath, String searchFileName) {
      if (null != zipFilePath && null != searchFileName) {
         String filePath = null;
         ZipFile zipFile = null;
         ZipEntry zipEntry = null;
         InputStream is = null;

         try {
            zipFilePath = zipFilePath.replace("\\", "/");
            zipFile = new ZipFile(zipFilePath);
            Enumeration e = zipFile.entries();

            while(e.hasMoreElements()) {
               ZipEntry entry = (ZipEntry)e.nextElement();
               String fileName = SecurityUtils.getSafeFile(entry.getName()).getName();
               if (fileName != null && fileName.equalsIgnoreCase(searchFileName)) {
                  zipEntry = zipFile.getEntry(fileName);
                  is = zipFile.getInputStream(zipEntry);
                  String directoryPath = zipFilePath.replace(".zip", "");
                  (new File(directoryPath)).mkdir();
                  Path targetPath = Paths.get(directoryPath, zipEntry.getName());
                  filePath = targetPath.toString();
                  File targetFile = new File(filePath);
                  if (targetFile.exists()) {
                     targetFile.delete();
                  }

                  Files.copy(is, targetPath, new CopyOption[0]);
                  break;
               }
            }
         } catch (IOException var22) {
            logger.error("Error zip file", var22);
         } catch (Exception var23) {
            logger.error("InputStream error!", var23);
         } finally {
            try {
               if (is != null) {
                  is.close();
               }

               if (zipFile != null) {
                  zipFile.close();
               }
            } catch (IOException var21) {
               logger.error("Error close zip file", var21);
            }

         }

         return filePath;
      } else {
         return null;
      }
   }

   public static Document parseXML(InputStream stream) throws Exception {
      DocumentBuilderFactory objDocumentBuilderFactory = null;
      DocumentBuilder objDocumentBuilder = null;
      Document doc = null;

      try {
         objDocumentBuilderFactory = DocumentBuilderFactory.newInstance();
         objDocumentBuilder = objDocumentBuilderFactory.newDocumentBuilder();
         doc = objDocumentBuilder.parse(stream);
         return doc;
      } catch (Exception var5) {
         throw var5;
      }
   }

   public static String getSoftwareVersionCustomFormat(String zipFile) {
      String version = null;
      String INNER_INFO_FILE_NAME = "pkginfo.xml";
      String VERSION_NODE_NAME = "packagever";
      String infoFilePath = getSearchZipFile(zipFile, "pkginfo.xml");
      FileInputStream is = null;

      try {
         is = new FileInputStream(infoFilePath);
         Document doc = parseXML(is);
         NodeList nl = doc.getElementsByTagName("packagever");
         Node node = nl.item(0).getFirstChild();
         version = node.getNodeValue();
      } catch (Exception var17) {
         logger.error(var17);
      } finally {
         try {
            if (is != null) {
               is.close();
            }
         } catch (Exception var16) {
            logger.error(var16.getMessage());
         }

      }

      return version;
   }

   public static Object[] moveTofirmwareImage(String uploadHome, String zipFile) {
      String VER_FILE_NAME = "info.txt";
      String home_path = uploadHome + zipFile;
      String filename = null;
      String entryName = null;
      if (zipFile.indexOf(".zip") <= -1 && zipFile.indexOf(".ZIP") <= -1) {
         return null;
      } else {
         filename = "zip";
         if (filename.equalsIgnoreCase("zip")) {
            zipFile = zipFile.replace(".zip", "");
            boolean isMove = false;
            String dirName = "C:\tmp";
            if (home_path.lastIndexOf(File.separator) <= 0) {
               File f = SecurityUtils.getSafeFile(dirName);
               if (f != null) {
                  boolean ret = f.mkdirs();
                  if (!ret) {
                     logger.error("returned fail");
                  }
               }
            } else {
               if (0 >= home_path.lastIndexOf(File.separator)) {
                  return null;
               }

               dirName = home_path.substring(0, home_path.lastIndexOf(File.separator));
            }

            BufferedInputStream bis = null;
            ZipInputStream in = null;
            FileOutputStream fos = null;
            BufferedOutputStream bos = null;
            ZipFile zipfile = null;

            try {
               in = new ZipInputStream(new FileInputStream(home_path));
               ZipEntry entry = null;

               File deleteFile;
               while((entry = in.getNextEntry()) != null) {
                  entryName = entry.getName();
                  if (!entryName.equalsIgnoreCase("info.txt") && !entry.isDirectory() && entryName.indexOf("info.txt") < 0) {
                     deleteFile = SecurityUtils.getSafeFile(uploadHome + zipFile);
                     if (!deleteFile.mkdir()) {
                        logger.error("mkdir() fail!!!!!!");
                     }

                     Path p = Paths.get(entryName);
                     entryName = p.getFileName().toString();

                     try {
                        zipfile = new ZipFile(home_path);
                        bis = new BufferedInputStream(zipfile.getInputStream(entry));
                        byte[] buffer = new byte[1024];
                        fos = new FileOutputStream(uploadHome + zipFile + File.separator + entryName);
                        bos = new BufferedOutputStream(fos, 1024);

                        int b;
                        while((b = bis.read(buffer, 0, 1024)) != -1) {
                           bos.write(buffer, 0, b);
                        }
                     } finally {
                        if (bos != null) {
                           try {
                              bos.flush();
                              bos.close();
                           } catch (Exception var69) {
                              logger.error("", var69);
                           }
                        }

                        if (bis != null) {
                           try {
                              bis.close();
                           } catch (Exception var68) {
                              logger.error("", var68);
                           }
                        }

                        if (fos != null) {
                           try {
                              fos.close();
                           } catch (Exception var67) {
                              logger.error("", var67);
                           }
                        }

                        if (zipfile != null) {
                           try {
                              zipfile.close();
                           } catch (Exception var66) {
                              logger.error("", var66);
                           }
                        }

                     }

                     isMove = true;
                     break;
                  }

                  in.closeEntry();
               }

               if (!isMove) {
                  logger.error("move file fail!!!!!!");
                  in.close();
                  throw new IOException();
               }

               in.close();
               deleteFile = SecurityUtils.getSafeFile(home_path);

               try {
                  if (!deleteFile.delete()) {
                     logger.error("file delete fail!!!!!!");
                  }
               } catch (Exception var70) {
                  logger.error("", var70);
               }
            } catch (Exception var72) {
               logger.error("zip file extract fail!!!", var72);
            } finally {
               try {
                  if (bis != null) {
                     bis.close();
                  }
               } catch (IOException var65) {
                  logger.error("", var65);
               }

               try {
                  if (in != null) {
                     in.close();
                  }
               } catch (IOException var64) {
                  logger.error("", var64);
               }

               try {
                  if (bos != null) {
                     bos.close();
                  }
               } catch (IOException var63) {
                  logger.error("", var63);
               }

               try {
                  if (fos != null) {
                     fos.close();
                  }
               } catch (IOException var62) {
                  logger.error("", var62);
               }

            }

            Object[] return_obj = new Object[]{entryName, zipFile + "/" + entryName, zipFile};
            return return_obj;
         } else {
            return null;
         }
      }
   }

   public static Long getFileSize(String filePath) {
      File file = SecurityUtils.getSafeFile(filePath);
      return !file.isFile() ? null : file.length();
   }

   public static boolean fileDelete(String deleteFileName) {
      File file = SecurityUtils.getSafeFile(deleteFileName);
      return file != null ? file.delete() : false;
   }

   public static List getDeviceTypeListByDeviceGroup(boolean isRoot, String groupId, String filterGroupIds) {
      ArrayList deviceTypeList = new ArrayList();

      try {
         if (StrUtils.nvl(CommonConfig.get("saas.ac.enable")).equals("true")) {
            deviceTypeList.add("S2PLAYER");
            return deviceTypeList;
         }
      } catch (ConfigException var12) {
      }

      List groupIdList = new ArrayList();
      String[] rtn;
      if (isRoot && filterGroupIds != null && !filterGroupIds.equals("")) {
         rtn = filterGroupIds.split(",");

         for(int i = 0; i < rtn.length; ++i) {
            ((List)groupIdList).add(Long.parseLong(rtn[i]));
         }
      } else if (isRoot) {
         try {
            groupIdList = deviceGroupDao.getChildGroupIdList(0, true);
         } catch (Exception var11) {
            logger.error("", var11);
         }
      } else if (groupId != null && !groupId.equals("")) {
         ((List)groupIdList).add(Long.parseLong(groupId));
      } else {
         try {
            groupIdList = deviceGroupDao.getChildGroupIdList(0, true);
         } catch (Exception var10) {
            logger.error("", var10);
         }
      }

      rtn = null;
      List deviceGroup = null;

      for(int i = 0; i < ((List)groupIdList).size(); ++i) {
         deviceGroup = null;

         try {
            deviceGroup = deviceGroupDao.getGroupType(((Long)((List)groupIdList).get(i)).intValue());
         } catch (Exception var9) {
            logger.error("", var9);
         }

         if (deviceGroup != null && !deviceGroup.isEmpty()) {
            for(int j = 0; j < deviceGroup.size(); ++j) {
               String rtn = (String)deviceGroup.get(j);
               if (!deviceTypeList.contains(rtn) && !rtn.equalsIgnoreCase("") && !rtn.equalsIgnoreCase("null")) {
                  deviceTypeList.add(rtn);
               }
            }
         }
      }

      return deviceTypeList;
   }

   public static String getDeviceType(String deviceId) {
      String deviceType = "";
      DeviceGeneralConf deviceGeneral = null;

      try {
         deviceGeneral = deviceDao.getDeviceGeneralConf(deviceId);
      } catch (Exception var4) {
         logger.error("", var4);
      }

      if (deviceGeneral != null && deviceGeneral.getDevice_type() != null) {
         deviceType = deviceGeneral.getDevice_type();
      }

      return deviceType;
   }

   public static String getDeviceIp(String deviceId) {
      String ip = "";
      DeviceGeneralConf deviceGeneral = null;

      try {
         deviceGeneral = deviceDao.getDeviceGeneralConf(deviceId);
      } catch (Exception var4) {
         logger.error("", var4);
         return ip;
      }

      if (deviceGeneral != null && deviceGeneral.getIp_address() != null) {
         ip = deviceGeneral.getIp_address();
      }

      return ip;
   }

   public static boolean isExistChargedLicense(String productCode) {
      boolean flag = false;
      int charged = 0;
      int freeOfCharged = 0;

      try {
         charged = slmlicenseMgr.getCntSlmLicenseMaxClients(productCode, "11");
         freeOfCharged = slmlicenseMgr.getCntSlmLicenseMaxClients(productCode, "12");
      } catch (SQLException var5) {
         logger.error("", var5);
      }

      if (charged + freeOfCharged > 0) {
         flag = true;
      }

      return flag;
   }

   public static String getSectionNameForNonApprovedDevice(String swChange, String swGrade) {
      String sectionName = "getNonApprovedDeviceListLimit";
      if (isExistChargedLicense("010120")) {
         sectionName = "getNonApprovedPremiumOnlyDeviceListLimit";
      }

      return sectionName;
   }

   public static int getRemainedLicenseCount(String deviceType) {
      SlmLicenseManager licenseDao = SlmLicenseManagerImpl.getInstance();
      boolean var2 = false;

      int LicCnt;
      try {
         LicCnt = licenseDao.getRemainLicenseCountByDeviceType(deviceType);
      } catch (Exception var4) {
         logger.error("", var4);
         return 0;
      }

      return LicCnt < 0 ? 0 : LicCnt;
   }

   public static boolean isConnected(String deviceId) {
      return motMgr.isConnected(deviceId);
   }

   public static boolean isAbnormalShutDown(String deviceId) throws Exception {
      ConnectionInfoEntity connInfo = (ConnectionInfoEntity)CacheFactory.getCache().get("CONNECTIONINFO_MEMORY_MAP" + deviceId);
      boolean result = false;
      if (connInfo == null) {
         return false;
      } else {
         long curTimeMillis = System.currentTimeMillis();
         if (curTimeMillis - connInfo.getLastConnectionTime() - 60000L >= connInfo.getMonitoringInterval() * 60000L && connInfo.getLastConnectionTime() != 0L) {
            result = true;
         }

         return result;
      }
   }

   public static boolean setDisconnected(String deviceId) {
      try {
         return motMgr.setDisconnected(deviceId);
      } catch (Exception var2) {
         logger.error("", var2);
         return false;
      }
   }

   public static CurrentPlayingEntity getPlayingContent(String deviceId) {
      try {
         return motMgr.getPlayingContent(deviceId);
      } catch (Exception var2) {
         logger.error("", var2);
         return null;
      }
   }

   public static String getDiffMin(Timestamp sTime, boolean diffFlag) {
      String res = "";
      Long diff = (System.currentTimeMillis() - Long.valueOf(sTime.getTime())) / 60000L;
      SimpleDateFormat sdfCurrent = new SimpleDateFormat("yyyy-MM-dd HH:mm");
      new SimpleDateFormat("HH:mm:ss");
      String displayTime = sdfCurrent.format(sTime);
      if (diff >= 0L) {
         if (diff > 518400L) {
            res = displayTime;
         } else if (diff > 43200L) {
            if (diff / 43200L == 1L) {
               res = "about" + diff / 43200L + " month ago";
            } else {
               res = "about " + diff / 43200L + " months ago";
            }
         } else if (diff > 1440L) {
            if (diff / 1440L == 1L) {
               res = "about" + diff / 1440L + " day ago";
            } else {
               res = "about " + diff / 1440L + " days ago";
            }
         } else if (diff > 60L) {
            if (diff / 60L == 1L) {
               res = "about " + diff / 60L + " hour ago ";
            } else {
               res = "about " + diff / 60L + " hours ago ";
            }
         } else if (diff > 0L) {
            if (diff == 1L) {
               res = diff + " minute ago ";
            } else {
               res = diff + " minutes ago ";
            }
         } else {
            res = "a moment ago";
         }
      }

      return diffFlag ? res : "[" + displayTime + "]";
   }

   public static String getDiffMin(Timestamp sTime, boolean diffFlag, Locale locale) {
      return getDiffMin(sTime, diffFlag, locale, "yyyy-MM-dd HH:mm");
   }

   public static String getDiffMin(Timestamp sTime, boolean diffFlag, Locale locale, String dateFormat) {
      String res = "";
      Long diff = (System.currentTimeMillis() - Long.valueOf(sTime.getTime())) / 60000L;
      SimpleDateFormat sdfCurrent = new SimpleDateFormat(dateFormat);
      String displayTime = sdfCurrent.format(sTime);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      if (locale == null) {
         locale = new Locale("en");
      }

      String text_about = "MIS_TEXT_ABOUT_P";
      String text_month_ago = "MIS_TEXT_MONTH_AGO_P";
      String text_months_ago = "MIS_TEXT_MONTHS_AGO_P";
      String text_day_ago = "MIS_TEXT_DAY_AGO_P";
      String text_days_ago = "MIS_TEXT_DAYS_AGO_P";
      String text_hour_ago = "MIS_TEXT_HOUR_AGO_P";
      String text_hours_ago = "MIS_TEXT_HOURS_AGO_P";
      String text_min_ago = "MIS_TEXT_MINUTE_AGO_P";
      String text_mins_ago = "MIS_TEXT_MINUTES_AGO_P";
      String text_a_moment_ago = "MIS_TEXT_A_MOMENT_AGO_P";
      String about = rms.getMessage(text_about, (Object[])null, locale);
      String monthAgo = rms.getMessage(text_month_ago, (Object[])null, locale);
      String monthsAgo = rms.getMessage(text_months_ago, (Object[])null, locale);
      String dayAgo = rms.getMessage(text_day_ago, (Object[])null, locale);
      String daysAgo = rms.getMessage(text_days_ago, (Object[])null, locale);
      String hourAgo = rms.getMessage(text_hour_ago, (Object[])null, locale);
      String hoursAgo = rms.getMessage(text_hours_ago, (Object[])null, locale);
      String minAgo = rms.getMessage(text_min_ago, (Object[])null, locale);
      String minsAgo = rms.getMessage(text_mins_ago, (Object[])null, locale);
      String aMomentAgo = rms.getMessage(text_a_moment_ago, (Object[])null, locale);
      res = aMomentAgo;
      if (diff >= 0L) {
         if (diff > 518400L) {
            res = displayTime;
         } else if (diff > 43200L) {
            if (diff / 43200L == 1L) {
               res = about + " " + diff / 43200L + " " + monthAgo;
            } else {
               res = about + " " + diff / 43200L + " " + monthsAgo;
            }
         } else if (diff > 1440L) {
            if (diff / 1440L == 1L) {
               res = about + " " + diff / 1440L + " " + dayAgo;
            } else {
               res = about + " " + diff / 1440L + " " + daysAgo;
            }
         } else if (diff > 60L) {
            if (diff / 60L == 1L) {
               res = about + " " + diff / 60L + " " + hourAgo;
            } else {
               res = about + " " + diff / 60L + " " + hoursAgo;
            }
         } else if (diff > 0L) {
            if (diff == 1L) {
               res = about + " " + diff + " " + minAgo;
            } else {
               res = about + " " + diff + " " + minsAgo;
            }
         } else {
            res = aMomentAgo;
         }
      }

      return diffFlag ? res : "(" + displayTime + ")";
   }

   public static boolean isOrgGroupByDeviceId(String deviceId, int orgGroupId) {
      if (orgGroupId == 0) {
         return true;
      } else {
         boolean rtn = false;

         try {
            DeviceGroup deviceGroup = deviceGroupDao.getGroupByDeviceId(deviceId);
            if (deviceGroup != null && deviceGroup.getGroup_id() != null) {
               int gId = deviceGroup.getGroup_id().intValue();
               Long orgId = deviceGroupDao.getOrgIdByGroupId((long)gId);
               if (orgId != null && orgId.intValue() == orgGroupId) {
                  rtn = true;
               }
            }
         } catch (Exception var6) {
            logger.error("", var6);
         }

         return rtn;
      }
   }

   public static void setDeviceCountInGroupList(String productType, List treeList) {
      int dCnt = 0;
      Long gId = 0L;
      Long pGId = 0L;

      for(int i = 0; i < treeList.size(); ++i) {
         gId = ((DeviceGroup)treeList.get(i)).getGroup_id();
         pGId = ((DeviceGroup)treeList.get(i)).getP_group_id();
         if (pGId > 0L && gId != null) {
            try {
               if (productType.equals("PREMIUM")) {
                  dCnt = deviceGroupDao.getCntDeviceInDeviceGroup(gId.intValue());
               }

               ((DeviceGroup)treeList.get(i)).setDevice_count((long)dCnt);
            } catch (Exception var7) {
               logger.error("", var7);
            }
         }
      }

   }

   public static String getGroupNamePathString(int groupId) {
      StringBuffer sb = new StringBuffer();
      ArrayList pList = new ArrayList();

      int i;
      try {
         i = groupId;
         DeviceGroup deviceGroup = null;

         do {
            deviceGroup = deviceGroupDao.getGroup(i);
            if (deviceGroup != null) {
               pList.add(deviceGroup.getGroup_name());
               i = deviceGroup.getP_group_id().intValue();
            } else {
               i = -1;
            }
         } while(i > 0);
      } catch (Exception var5) {
         logger.error("", var5);
      }

      for(i = pList.size() - 1; i >= 0; --i) {
         if (sb.length() > 0) {
            sb.append(">");
         }

         sb.append(((String)pList.get(i)).toString());
      }

      return sb.toString();
   }

   public static List getGroupNamePath(int groupId) {
      List pList = null;

      try {
         pList = getParentGroupNamePathByGroupId(groupId);
      } catch (SQLException var3) {
         logger.error("[MagicInfo_DeviceUtils] fail to get getGroupNamePath e : " + var3.getMessage());
      }

      return pList;
   }

   public static List getParentGroupNamePathByGroupId(int groupId) throws SQLException {
      return deviceGroupDao.getParentGroupNamePathByGroupId((long)groupId);
   }

   public static String approveDevice(String deviceIds, Long groupId, String deviceName, String location, String selDeviceId, Locale locale, String sessionId, String userId, String calDate, String organization, String ipAddress) throws Exception {
      String deviceId = "";
      String message = null;
      DeviceLogManager logMgr = DeviceLogManagerImpl.getInstance();
      SlmLicenseManager licenseMgr = SlmLicenseManagerImpl.getInstance();
      String deviceModelName = null;
      deviceIds = deviceIds.replace(",", ";");
      String[] chkInfo = null;
      if (deviceIds != null) {
         chkInfo = deviceIds.split(";");
      }

      if (locale == null || locale.toString().equals("")) {
         locale = Locale.US;
      }

      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      Long orgGroupId = groupDao.getOrgIdByGroupId(groupId);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      String eventType = "TEXT_TITLE_NON_APPROVAL_DEVICE_APPROVAL_P";
      rms.getMessage("COM_SID_MENU", (Object[])null, locale);
      rms.getMessage("COM_TV_SID_UNAPPROVED", (Object[])null, locale);
      int cntRemainLicenseByDeviceType = false;
      int nowCntApproval = 0;
      String deviceType = null;
      Float deviceTypeVersion = 1.0F;
      List approvedList = new ArrayList();
      Device tmpDevice;
      if (chkInfo != null) {
         nowCntApproval = chkInfo.length;
         String tmpDeviceId = "";
         if (selDeviceId != null && !selDeviceId.equals("")) {
            tmpDeviceId = selDeviceId;
         } else {
            tmpDeviceId = chkInfo[0];
         }

         tmpDevice = deviceInfo.getDevice(tmpDeviceId);
         deviceTypeVersion = tmpDevice.getDevice_type_version();
         deviceType = tmpDevice.getDevice_type();
      } else if (selDeviceId != null && !selDeviceId.equals("")) {
         ++nowCntApproval;
         tmpDevice = deviceInfo.getDevice(selDeviceId);
         deviceType = tmpDevice.getDevice_type();
         deviceTypeVersion = tmpDevice.getDevice_type_version();
      }

      int cntRemainLicenseByDeviceType = licenseMgr.getRemainLicenseCountByDeviceType(deviceType);
      logger.error("[MagicInfo_DeviceUtils][APPROVAL] devices approval modelName:" + deviceModelName + ", deviceType:" + deviceType + ", cntRemainLicenseByDeviceType:" + cntRemainLicenseByDeviceType + ", nowCount : " + nowCntApproval);
      if (cntRemainLicenseByDeviceType < nowCntApproval) {
         message = "device_approval_max_connection_over";
      } else {
         ScheduleInfoEntity schEntity;
         MessageInfo msgInfo;
         JobManager jobMgr;
         Float deviceTypeVerseion;
         if (selDeviceId != null && !selDeviceId.equals("")) {
            if (selDeviceId != null && !selDeviceId.equals("")) {
               deviceId = selDeviceId;
            } else if (chkInfo != null) {
               deviceId = chkInfo[0];
            }

            Device device = deviceInfo.getDevice(deviceId);
            if (calDate.equals("∞")) {
               calDate = "";
            }

            Map param = new HashMap();
            param.put("device_id", deviceId);
            param.put("device_name", deviceName);
            param.put("device_type", device.getDevice_type());
            param.put("group_id", groupId);
            param.put("current_group_id", 999999);
            param.put("device_model_name", deviceModelName);
            param.put("location", location);
            param.put("is_approved", true);
            param.put("calDate", calDate);
            param.put("organization", organization);
            boolean result = deviceInfo.setDeviceForApproval(param);
            if (result) {
               approvedList.add(deviceId);
               DeviceGeneralConf deviceTemp = deviceInfo.getDeviceGeneralConf(deviceId, true);
               DBCacheUtils.setDeviceGeneralConf(deviceTemp, deviceId);
               groupDao.addGroupTotalCount(groupId, 1L);
               DownloadInfo downloadDao = DownloadInfoImpl.getInstance();
               logger.error("[MagicInfo_DeviceUtils][APPROVAL][START FTP REGISTERING]DeviceType is : " + deviceType);
               if (downloadDao.getUserByName(deviceId) == null) {
                  BaseUser user = new BaseUser();
                  user.setName(deviceId);
                  user.setPassword("MagicInfo");
                  UserManager userMgr = (new DbUserManagerFactory()).createUserManager();
                  userMgr.save(user);
                  logger.error("[MagicInfo_DeviceUtils][APPROVAL][FTP REGISTERING]DeviceID : " + deviceId);
                  logger.error("[MagicInfo_DeviceUtils][APPROVAL][FTP REGISTERING]realPassword : MagicInfo");
               }

               logger.error("[MagicInfo_DeviceUtils][APPROVAL][END FTP REGISTERING]=====================================");
               motMgr.connectionReload(deviceId, 1);
               motMgr.scheduleReload(deviceId, 1);
               schEntity = motMgr.getScheduleStatus(deviceId);
               if (!StrUtils.nvl(CommonConfig.get("saas.eu.enable")).equalsIgnoreCase("TRUE") && schEntity != null) {
                  try {
                     ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
                     schInfo.deploySchedule(deviceId, schEntity.getScheduleId(), schEntity.getScheduleVersion());
                  } catch (Exception var43) {
                     logger.fatal("[MagicInfo_DeviceUtils][APPROVAL] Error of mapping content schedule while approving device " + deviceId);
                  }

                  try {
                     msgInfo = MessageInfoImpl.getInstance();
                     msgInfo.deployMessage(deviceId, schEntity.getMessageId(), schEntity.getMessageVersion());
                  } catch (Exception var42) {
                     logger.fatal("[MagicInfo_DeviceUtils][APPROVAL] Error of mapping message mschedule while approving device " + deviceId);
                  }
               }

               jobMgr = JobManagerImpl.getInstance();
               jobMgr.deployJobSchedule("", device);
               WSCall.setPlayerRequest(deviceId, "device approval");
               if (motMgr.isConnected(deviceId)) {
                  DeviceGeneralConf info = new DeviceGeneralConf();
                  info.setDevice_id(deviceId);
                  info.setDevice_name(deviceName);
                  DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
                  confManager.reqSetGeneralToDevice(info, sessionId);
               }

               makeExpirationJob(deviceId, calDate);
               message = "device_approval_success";

               try {
                  deviceTypeVerseion = device.getDevice_type_version();
                  boolean isSupportStatistics = deviceType.equalsIgnoreCase("iPLAYER") || deviceType.equalsIgnoreCase("SPLAYER") && deviceTypeVerseion >= CommonDataConstants.ALL_DEVICE_TYPE_VERSION_ARRAY[2];
                  if (isSupportStatistics && !deviceInfo.isRequestTimeExist(deviceId) && deviceInfo.addStatRequestTimeInsertCurrent(deviceId)) {
                     logger.info("[MagicInfo_DeviceUtils][APPROVAL] Statistics Requesttiem is Inserted deivce: " + deviceId);
                  }
               } catch (Exception var44) {
                  logger.error("", var44);
               }
            } else {
               message = "device_approval_fail";
            }

            try {
               DevicePreconfigInfo preconfigInfo = DevicePreconfigInfoImpl.getInstance();
               preconfigInfo.deployToDevice(device);
            } catch (Exception var41) {
               logger.error("Failed to send preconfig. " + device.getDevice_id(), var41);
            }
         } else if (chkInfo != null && chkInfo.length > 0) {
            boolean flag = false;
            rms.getMessage("TEXT_TITLE_DEVICE_APPROVAL_P", (Object[])null, locale);
            Device device = null;

            int i;
            for(i = 0; i < chkInfo.length; ++i) {
               deviceId = chkInfo[i];
               device = deviceInfo.getDevice(deviceId);
               if (calDate.equals("∞")) {
                  calDate = "";
               }

               Map param = new HashMap();
               param.put("device_id", deviceId);
               param.put("device_name", deviceName);
               param.put("group_id", groupId);
               param.put("current_group_id", 999999);
               param.put("device_model_name", deviceModelName);
               param.put("device_type", device.getDevice_type());
               param.put("location", location);
               param.put("is_approved", true);
               param.put("orgGroupId", orgGroupId);
               param.put("calDate", calDate);
               param.put("organization", organization);
               int seq = deviceInfo.setApprovalWithSeq(param);
               String newDeviceName = deviceName + "_" + seq;
               if (seq > -1) {
                  approvedList.add(deviceId);
               }

               DownloadInfo downloadDao = DownloadInfoImpl.getInstance();
               logger.error("[MagicInfo_DeviceUtils][MULTI][APPROVAL][START FTP REGISTERING] DeviceType is : " + deviceType);
               if (downloadDao.getUserByName(deviceId) == null) {
                  BaseUser user = new BaseUser();
                  user.setName(deviceId);
                  user.setPassword("MagicInfo");
                  UserManager userMgr = (new DbUserManagerFactory()).createUserManager();
                  userMgr.save(user);
                  logger.error("[MagicInfo_DeviceUtils][APPROVAL][FTP REGISTERING] DeviceID : " + deviceId);
                  logger.error("[MagicInfo_DeviceUtils][APPROVAL][FTP REGISTERING] realPassword : MagicInfo");
               }

               logger.error("[MagicInfo_DeviceUtils][MULTI][APPROVAL][END FTP REGISTERING]=====================================");
               WSCall.setPlayerRequest(deviceId, "device approval");
               device = deviceInfo.getDeviceOperationInfo(deviceId);
               DeviceGeneralConf info = new DeviceGeneralConf();
               info.setDevice_id(deviceId);
               info.setDevice_name(newDeviceName);
               DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
               confManager.reqSetGeneralToDevice(info, sessionId);
               DeviceGeneralConf deviceTemp = deviceInfo.getDeviceGeneralConf(deviceId, true);
               DBCacheUtils.setDeviceGeneralConf(deviceTemp, deviceId);
               motMgr.connectionReload(deviceId, 1);
               motMgr.scheduleReload(deviceId, 1);
            }

            i = 0;

            while(true) {
               if (i >= chkInfo.length) {
                  DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
                  deviceGroupInfo.addGroupTotalCount(groupId, (long)chkInfo.length);
                  makeExpirationJob(deviceId, calDate);
                  if (flag) {
                     message = "not_approved_some_devices";
                  } else {
                     message = "device_approval_success";

                     try {
                        DevicePreconfigInfo preconfigInfo = DevicePreconfigInfoImpl.getInstance();
                        preconfigInfo.deployToDevice(device);
                     } catch (Exception var40) {
                        logger.error("Failed to send preconfig. " + device.getDevice_id(), var40);
                     }
                  }
                  break;
               }

               deviceId = chkInfo[i];
               device = deviceInfo.getDevice(deviceId);
               ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
               schEntity = motMgr.getScheduleStatus(deviceId);
               if (schEntity != null) {
                  schInfo.deploySchedule(deviceId, schEntity.getScheduleId(), schEntity.getScheduleVersion());
                  msgInfo = MessageInfoImpl.getInstance();
                  msgInfo.deployMessage(deviceId, schEntity.getMessageId(), schEntity.getMessageVersion());
               }

               jobMgr = JobManagerImpl.getInstance();
               jobMgr.deployJobSchedule("", device);
               WSCall.setPlayerRequest(deviceId, "device approval");

               try {
                  deviceTypeVerseion = device.getDevice_type_version();
                  String tempDeviceType = device.getDevice_type();
                  boolean isSupportStatistics = tempDeviceType.equalsIgnoreCase("iPLAYER") || tempDeviceType.equalsIgnoreCase("SPLAYER") && deviceTypeVerseion >= CommonDataConstants.ALL_DEVICE_TYPE_VERSION_ARRAY[2];
                  if (isSupportStatistics && !deviceInfo.isRequestTimeExist(deviceId) && deviceInfo.addStatRequestTimeInsertCurrent(deviceId)) {
                     logger.info("[MagicInfo_DeviceUtils][MULTI][APPROVAL] Statistics Requesttiem is Inserted deivce: " + deviceId);
                  }
               } catch (Exception var45) {
                  logger.error("", var45);
               }

               ++i;
            }
         }

         if (message != null && message.equalsIgnoreCase("device_approval_success") && approvedList != null && approvedList.size() > 0 && isSupportNOC()) {
            DeviceNocInfo nocDao = DeviceNocInfoImpl.getInstance();
            boolean nocGroup = nocDao.isNocSupportGroup(groupId);
            if (nocGroup) {
               DeviceNocManager nocService = DeviceNocManagerImpl.getInstance();
               nocService.thingworxCreateDevice(approvedList);
            }
         }
      }

      return message;
   }

   public static HashMap approveDeviceForE2E(List deviceIds, Long groupId, String deviceName, String location, String selDeviceId, Locale locale, String sessionId, String userId, String calDate, String organization, String ipAddress, String accountCode, String brandCode, String modelCd) throws Exception {
      String deviceId = "";
      String message = null;
      DeviceLogManager logMgr = DeviceLogManagerImpl.getInstance();
      SlmLicenseManager licenseMgr = SlmLicenseManagerImpl.getInstance();
      new ServerLogEntity();
      String deviceModelName = null;
      String[] chkInfo = null;
      if (deviceIds != null) {
         chkInfo = (String[])deviceIds.toArray(new String[0]);
      }

      if (locale == null || locale.toString().equals("")) {
         locale = Locale.US;
      }

      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      Long orgGroupId = groupDao.getOrgIdByGroupId(groupId);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      String eventType = "TEXT_TITLE_NON_APPROVAL_DEVICE_APPROVAL_P";
      rms.getMessage("COM_SID_MENU", (Object[])null, locale);
      rms.getMessage("COM_TV_SID_UNAPPROVED", (Object[])null, locale);
      int cntRemainLicenseByDeviceType = false;
      int nowCntApproval = 0;
      String deviceType = null;
      Float deviceTypeVersion = 1.0F;
      List approvedList = new ArrayList();
      HashMap resultMap = new HashMap();
      Device tmpDevice;
      int nowCntApproval;
      if (chkInfo != null) {
         nowCntApproval = chkInfo.length;
         String tmpDeviceId = "";
         if (selDeviceId != null && !selDeviceId.equals("")) {
            tmpDeviceId = selDeviceId;
         } else {
            tmpDeviceId = chkInfo[0];
         }

         tmpDevice = deviceInfo.getDevice(tmpDeviceId);
         deviceTypeVersion = tmpDevice.getDevice_type_version();
         deviceType = tmpDevice.getDevice_type();
      } else if (selDeviceId != null && !selDeviceId.equals("")) {
         nowCntApproval = nowCntApproval + 1;
         tmpDevice = deviceInfo.getDevice(selDeviceId);
         deviceType = tmpDevice.getDevice_type();
         deviceTypeVersion = tmpDevice.getDevice_type_version();
      }

      ScheduleInfoEntity schEntity;
      MessageInfo msgInfo;
      JobManager jobMgr;
      boolean isSupportStatistics;
      if (selDeviceId != null && !selDeviceId.equals("")) {
         if (selDeviceId != null && !selDeviceId.equals("")) {
            deviceId = selDeviceId;
         } else if (chkInfo != null) {
            deviceId = chkInfo[0];
         }

         Device device = deviceInfo.getDevice(deviceId);
         if (calDate.equals("∞")) {
            calDate = "";
         }

         Map param = new HashMap();
         param.put("device_id", deviceId);
         param.put("device_name", deviceName);
         param.put("device_type", device.getDevice_type());
         param.put("group_id", groupId);
         param.put("current_group_id", 999999);
         param.put("device_model_name", deviceModelName);
         param.put("location", location);
         param.put("is_approved", true);
         param.put("calDate", calDate);
         param.put("organization", organization);

         try {
            licenseMgr.activationProcessForE2E(selDeviceId, accountCode, brandCode, modelCd, location, deviceName);
         } catch (Exception var48) {
            resultMap.put("message", "activationProcess Fail");
            resultMap.put(deviceId, selDeviceId);
            throw var48;
         }

         boolean result = deviceInfo.setDeviceForApproval(param);
         if (result) {
            approvedList.add(deviceId);
            DeviceGeneralConf deviceTemp = deviceInfo.getDeviceGeneralConf(deviceId, true);
            DBCacheUtils.setDeviceGeneralConf(deviceTemp, deviceId);
            groupDao.addGroupTotalCount(groupId, 1L);
            DownloadInfo downloadDao = DownloadInfoImpl.getInstance();
            logger.error("[MagicInfo_DeviceUtils][APPROVAL][START FTP REGISTERING]DeviceType is : " + deviceType);
            if (downloadDao.getUserByName(deviceId) == null) {
               BaseUser user = new BaseUser();
               user.setName(deviceId);
               user.setPassword("MagicInfo");
               UserManager userMgr = (new DbUserManagerFactory()).createUserManager();
               userMgr.save(user);
               logger.error("[MagicInfo_DeviceUtils][APPROVAL][FTP REGISTERING]DeviceID : " + deviceId);
               logger.error("[MagicInfo_DeviceUtils][APPROVAL][FTP REGISTERING]realPassword : MagicInfo");
            }

            logger.error("[MagicInfo_DeviceUtils][APPROVAL][END FTP REGISTERING]=====================================");
            motMgr.connectionReload(deviceId, 1);
            motMgr.scheduleReload(deviceId, 1);
            schEntity = motMgr.getScheduleStatus(deviceId);
            if (!StrUtils.nvl(CommonConfig.get("saas.eu.enable")).equalsIgnoreCase("TRUE") && schEntity != null) {
               try {
                  ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
                  schInfo.deploySchedule(deviceId, schEntity.getScheduleId(), schEntity.getScheduleVersion());
               } catch (Exception var47) {
                  logger.fatal("[MagicInfo_DeviceUtils][APPROVAL] Error of mapping content schedule while approving device " + deviceId);
               }

               try {
                  msgInfo = MessageInfoImpl.getInstance();
                  msgInfo.deployMessage(deviceId, schEntity.getMessageId(), schEntity.getMessageVersion());
               } catch (Exception var46) {
                  logger.fatal("[MagicInfo_DeviceUtils][APPROVAL] Error of mapping message mschedule while approving device " + deviceId);
               }
            }

            jobMgr = JobManagerImpl.getInstance();
            jobMgr.deployJobSchedule("", device);
            WSCall.setPlayerRequest(deviceId, "device approval");
            if (motMgr.isConnected(deviceId)) {
               DeviceGeneralConf info = new DeviceGeneralConf();
               info.setDevice_id(deviceId);
               info.setDevice_name(deviceName);
               DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
               confManager.reqSetGeneralToDevice(info, sessionId);
            }

            makeExpirationJob(deviceId, calDate);
            rms.getMessage("TEXT_TITLE_DEVICE_APPROVAL_P", (Object[])null, locale);
            resultMap.put("message", "device_approval_success");

            try {
               Float deviceTypeVerseion = device.getDevice_type_version();
               isSupportStatistics = deviceType.equalsIgnoreCase("iPLAYER") || deviceType.equalsIgnoreCase("SPLAYER") && deviceTypeVerseion >= CommonDataConstants.ALL_DEVICE_TYPE_VERSION_ARRAY[2];
               if (isSupportStatistics && !deviceInfo.isRequestTimeExist(deviceId) && deviceInfo.addStatRequestTimeInsertCurrent(deviceId)) {
                  logger.info("[MagicInfo_DeviceUtils][APPROVAL] Statistics Requesttiem is Inserted deivce: " + deviceId);
               }
            } catch (Exception var51) {
               logger.error("", var51);
            }
         } else {
            resultMap.put("message", "device_approval_fail");
         }
      } else if (chkInfo != null && chkInfo.length > 0) {
         boolean flag = false;
         rms.getMessage("TEXT_TITLE_DEVICE_APPROVAL_P", (Object[])null, locale);
         Device device = null;

         int i;
         for(i = 0; i < chkInfo.length; ++i) {
            deviceId = chkInfo[i];
            device = deviceInfo.getDevice(deviceId);
            if (calDate.equals("占쏙옙")) {
               calDate = "";
            }

            Map param = new HashMap();
            param.put("device_id", deviceId);
            param.put("device_name", deviceName);
            param.put("group_id", groupId);
            param.put("current_group_id", 999999);
            param.put("device_model_name", deviceModelName);
            param.put("device_type", device.getDevice_type());
            param.put("location", location);
            param.put("is_approved", true);
            param.put("orgGroupId", orgGroupId);
            param.put("calDate", calDate);
            int seq = deviceInfo.setApprovalWithSeq(param);
            String newDeviceName = deviceName + "_" + seq;

            try {
               licenseMgr.activationProcessForE2E(selDeviceId, accountCode, brandCode, modelCd, location, deviceName);
            } catch (Exception var50) {
               flag = true;
               logger.error("", var50);
               resultMap.put(deviceId, "activationProcess Fail");
               continue;
            }

            if (seq > -1) {
               approvedList.add(deviceId);
            }

            DownloadInfo downloadDao = DownloadInfoImpl.getInstance();
            logger.error("[MagicInfo_DeviceUtils][MULTI][APPROVAL][START FTP REGISTERING] DeviceType is : " + deviceType);
            if (downloadDao.getUserByName(deviceId) == null) {
               BaseUser user = new BaseUser();
               user.setName(deviceId);
               user.setPassword("MagicInfo");
               UserManager userMgr = (new DbUserManagerFactory()).createUserManager();
               userMgr.save(user);
               logger.error("[MagicInfo_DeviceUtils][APPROVAL][FTP REGISTERING] DeviceID : " + deviceId);
               logger.error("[MagicInfo_DeviceUtils][APPROVAL][FTP REGISTERING] realPassword : MagicInfo");
            }

            logger.error("[MagicInfo_DeviceUtils][MULTI][APPROVAL][END FTP REGISTERING]=====================================");
            WSCall.setPlayerRequest(deviceId, "device approval");
            deviceInfo.getDeviceOperationInfo(deviceId);
            DeviceGeneralConf info = new DeviceGeneralConf();
            info.setDevice_id(deviceId);
            info.setDevice_name(newDeviceName);
            DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
            confManager.reqSetGeneralToDevice(info, sessionId);
            DeviceGeneralConf deviceTemp = deviceInfo.getDeviceGeneralConf(deviceId, true);
            DBCacheUtils.setDeviceGeneralConf(deviceTemp, deviceId);
            motMgr.connectionReload(deviceId, 1);
            motMgr.scheduleReload(deviceId, 1);
         }

         for(i = 0; i < chkInfo.length; ++i) {
            deviceId = chkInfo[i];
            device = deviceInfo.getDevice(deviceId);
            ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
            schEntity = motMgr.getScheduleStatus(deviceId);
            if (schEntity != null) {
               schInfo.deploySchedule(deviceId, schEntity.getScheduleId(), schEntity.getScheduleVersion());
               msgInfo = MessageInfoImpl.getInstance();
               msgInfo.deployMessage(deviceId, schEntity.getMessageId(), schEntity.getMessageVersion());
            }

            jobMgr = JobManagerImpl.getInstance();
            jobMgr.deployJobSchedule("", device);
            WSCall.setPlayerRequest(deviceId, "device approval");

            try {
               Float deviceTypeVerseion = device.getDevice_type_version();
               String tempDeviceType = device.getDevice_type();
               isSupportStatistics = tempDeviceType.equalsIgnoreCase("iPLAYER") || tempDeviceType.equalsIgnoreCase("SPLAYER") && deviceTypeVerseion >= CommonDataConstants.ALL_DEVICE_TYPE_VERSION_ARRAY[2];
               if (isSupportStatistics && !deviceInfo.isRequestTimeExist(deviceId) && deviceInfo.addStatRequestTimeInsertCurrent(deviceId)) {
                  logger.info("[MagicInfo_DeviceUtils][MULTI][APPROVAL] Statistics Requesttiem is Inserted deivce: " + deviceId);
               }
            } catch (Exception var49) {
               logger.error("", var49);
            }
         }

         groupDao.addGroupTotalCount(groupId, (long)chkInfo.length);
         makeExpirationJob(deviceId, calDate);
         if (flag) {
            resultMap.put("message", "not_approved_some_devices");
         } else {
            resultMap.put("message", "device_approval_success");
         }
      }

      if (message != null && ((String)message).equalsIgnoreCase("device_approval_success") && approvedList != null && approvedList.size() > 0) {
         try {
            if (isSupportNOC()) {
               DeviceNocInfo nocDao = DeviceNocInfoImpl.getInstance();
               boolean nocGroup = nocDao.isNocSupportGroup(groupId);
               if (nocGroup) {
                  DeviceNocManager nocService = DeviceNocManagerImpl.getInstance();
                  nocService.thingworxCreateDevice(approvedList);
               }
            }
         } catch (Exception var45) {
         }
      }

      return resultMap;
   }

   public static HashMap approveDeviceForE2E_SLMDirect(List deviceIds, Long groupId, String deviceName, String location, String selDeviceId, Locale locale, String sessionId, String userId, String calDate, String organization, String ipAddress, String soldToCode, String modelCd, String secorgId) throws Exception {
      String deviceId = "";
      String message = null;
      SlmLicenseManager licenseMgr = SlmLicenseManagerImpl.getInstance();
      String deviceModelName = null;
      String[] chkInfo = null;
      if (deviceIds != null) {
         chkInfo = (String[])deviceIds.toArray(new String[0]);
      }

      if (locale == null || locale.toString().equals("")) {
         locale = Locale.US;
      }

      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      groupDao.getOrgIdByGroupId(groupId);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      int nowCntApproval = 0;
      String deviceType = null;
      Float deviceTypeVersion = 1.0F;
      List approvedList = new ArrayList();
      HashMap resultMap = new HashMap();
      Device tmpDevice;
      int nowCntApproval;
      if (chkInfo != null) {
         nowCntApproval = chkInfo.length;
         String tmpDeviceId = "";
         if (selDeviceId != null && !selDeviceId.equals("")) {
            tmpDeviceId = selDeviceId;
         } else {
            tmpDeviceId = chkInfo[0];
         }

         tmpDevice = deviceInfo.getDevice(tmpDeviceId);
         deviceTypeVersion = tmpDevice.getDevice_type_version();
         deviceType = tmpDevice.getDevice_type();
      } else if (selDeviceId != null && !selDeviceId.equals("")) {
         nowCntApproval = nowCntApproval + 1;
         tmpDevice = deviceInfo.getDevice(selDeviceId);
         deviceType = tmpDevice.getDevice_type();
         deviceTypeVersion = tmpDevice.getDevice_type_version();
      }

      if (selDeviceId != null && !selDeviceId.equals("")) {
         if (selDeviceId != null && !selDeviceId.equals("")) {
            deviceId = selDeviceId;
         } else if (chkInfo != null) {
            deviceId = chkInfo[0];
         }

         Device device = deviceInfo.getDevice(deviceId);
         if (calDate.equals("∞")) {
            calDate = "";
         }

         Map param = new HashMap();
         param.put("device_id", deviceId);
         param.put("device_name", deviceName);
         param.put("device_type", device.getDevice_type());
         param.put("group_id", groupId);
         param.put("current_group_id", 999999);
         param.put("device_model_name", deviceModelName);
         param.put("location", location);
         param.put("is_approved", true);
         param.put("calDate", calDate);
         param.put("organization", organization);

         try {
            licenseMgr.activationProcessForE2E_SLMDirect(selDeviceId, soldToCode, modelCd, secorgId, location, deviceName);
         } catch (Exception var40) {
            resultMap.put("message", "activationProcess Fail");
            resultMap.put(deviceId, selDeviceId);
            throw var40;
         }

         boolean result = deviceInfo.setDeviceForApproval(param);
         if (result) {
            approvedList.add(deviceId);
            DeviceGeneralConf deviceTemp = deviceInfo.getDeviceGeneralConf(deviceId, true);
            DBCacheUtils.setDeviceGeneralConf(deviceTemp, deviceId);
            groupDao.addGroupTotalCount(groupId, 1L);
            DownloadInfo downloadDao = DownloadInfoImpl.getInstance();
            logger.error("[MagicInfo_DeviceUtils][APPROVAL][START FTP REGISTERING]DeviceType is : " + deviceType);
            if (downloadDao.getUserByName(deviceId) == null) {
               BaseUser user = new BaseUser();
               user.setName(deviceId);
               user.setPassword("MagicInfo");
               UserManager userMgr = (new DbUserManagerFactory()).createUserManager();
               userMgr.save(user);
               logger.error("[MagicInfo_DeviceUtils][APPROVAL][FTP REGISTERING]DeviceID : " + deviceId);
               logger.error("[MagicInfo_DeviceUtils][APPROVAL][FTP REGISTERING]realPassword : MagicInfo");
            }

            logger.error("[MagicInfo_DeviceUtils][APPROVAL][END FTP REGISTERING]=====================================");
            motMgr.connectionReload(deviceId, 1);
            motMgr.scheduleReload(deviceId, 1);
            ScheduleInfoEntity schEntity = motMgr.getScheduleStatus(deviceId);
            if (!StrUtils.nvl(CommonConfig.get("saas.eu.enable")).equalsIgnoreCase("TRUE") && schEntity != null) {
               try {
                  ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
                  schInfo.deploySchedule(deviceId, schEntity.getScheduleId(), schEntity.getScheduleVersion());
               } catch (Exception var39) {
                  logger.fatal("[MagicInfo_DeviceUtils][APPROVAL] Error of mapping content schedule while approving device " + deviceId);
               }

               try {
                  MessageInfo msgInfo = MessageInfoImpl.getInstance();
                  msgInfo.deployMessage(deviceId, schEntity.getMessageId(), schEntity.getMessageVersion());
               } catch (Exception var38) {
                  logger.fatal("[MagicInfo_DeviceUtils][APPROVAL] Error of mapping message mschedule while approving device " + deviceId);
               }
            }

            JobManager jobMgr = JobManagerImpl.getInstance();
            jobMgr.deployJobSchedule("", device);
            WSCall.setPlayerRequest(deviceId, "device approval");
            if (motMgr.isConnected(deviceId)) {
               DeviceGeneralConf info = new DeviceGeneralConf();
               info.setDevice_id(deviceId);
               info.setDevice_name(deviceName);
               DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
               confManager.reqSetGeneralToDevice(info, sessionId);
            }

            makeExpirationJob(deviceId, calDate);
            rms.getMessage("TEXT_TITLE_DEVICE_APPROVAL_P", (Object[])null, locale);
            resultMap.put("message", "device_approval_success");

            try {
               Float deviceTypeVerseion = device.getDevice_type_version();
               boolean isSupportStatistics = deviceType.equalsIgnoreCase("iPLAYER") || deviceType.equalsIgnoreCase("SPLAYER") && deviceTypeVerseion >= CommonDataConstants.ALL_DEVICE_TYPE_VERSION_ARRAY[2];
               if (isSupportStatistics && !deviceInfo.isRequestTimeExist(deviceId) && deviceInfo.addStatRequestTimeInsertCurrent(deviceId)) {
                  logger.info("[MagicInfo_DeviceUtils][APPROVAL] Statistics Requesttiem is Inserted deivce: " + deviceId);
               }
            } catch (Exception var41) {
               logger.error("", var41);
            }
         } else {
            resultMap.put("message", "device_approval_fail");
         }
      }

      return resultMap;
   }

   public static String swapDeviceForE2E(String oldDeviceId, String newDeviceId, String userId) throws Exception {
      Device oldDevice = deviceDao.getDevice(oldDeviceId);
      Device newDevice = deviceDao.getDevice(newDeviceId);
      if (oldDevice.getDevice_type().equals(newDevice.getDevice_type()) && oldDevice.getDevice_type_version().equals(newDevice.getDevice_type_version())) {
         DeviceGroup oldDeviceGroup = deviceGroupDao.getGroupByDeviceId(oldDeviceId);
         String organization = deviceGroupDao.getOrgNameByGroupId(oldDeviceGroup.getGroup_id());
         long oldDeviceGroupId = oldDeviceGroup.getGroup_id();
         SlmLicenseDao licenseDao = new SlmLicenseDao();
         SlmLicenseEntity slmLicenseEntity = licenseDao.getSlmLicenseForE2EByDeviceId(oldDeviceId);
         MonitoringManager monMgr = MonitoringManagerImpl.getInstance();
         DeviceLogManager logMgr = DeviceLogManagerImpl.getInstance();
         ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
         rms.setBasename("resource/messages");
         String eventType = rms.getMessage("TEXT_TITLE_DELETE_DEVICE_P", (Object[])null, Locale.ENGLISH);
         String strMenu = rms.getMessage("COM_SID_MENU", (Object[])null, Locale.ENGLISH);
         String strMenuName = rms.getMessage("COM_TEXT_MONITORING_P", (Object[])null, Locale.ENGLISH);
         String strCommand = rms.getMessage("TEXT_TITLE_DELETE_DEVICE_P", (Object[])null, Locale.ENGLISH);
         boolean isRedundancy = false;
         boolean isVwtDevice = false;
         boolean tempIsRedundancy = deviceDao.isRedundancyDevice(oldDeviceId);
         isRedundancy = isRedundancy || tempIsRedundancy;
         boolean isRedundancyGroupTarget = false;

         try {
            isRedundancyGroupTarget = deviceGroupDao.isRedundancyGroup(Integer.parseInt(deviceDao.getDeviceGroupIdByDeviceId(oldDeviceId)));
         } catch (Exception var36) {
            logger.error("", var36);
         }

         isRedundancy = isRedundancy || isRedundancyGroupTarget;
         if (oldDevice != null && oldDevice.getVwt_id() != null && !oldDevice.getVwt_id().equals("")) {
            isVwtDevice = true;
         }

         if (isVwtDevice) {
            throw new Exception("Check VWL Group");
         } else if (isRedundancy) {
            throw new Exception("Check Redundancy Group");
         } else {
            boolean result;
            try {
               result = false;
               if (CommonConfig.get("e2e.license.system") != null && !CommonConfig.get("e2e.license.system").toUpperCase().equals(ExternalSystemUtils.SYSTEM_PBP)) {
                  result = slmlicenseMgr.swapProcessForE2E_SLMDirect(slmLicenseEntity, newDeviceId, oldDevice.getLocation(), oldDevice.getDevice_name());
               } else {
                  result = slmlicenseMgr.swapProcessForE2E(slmLicenseEntity, newDeviceId, oldDevice.getLocation(), oldDevice.getDevice_name());
               }

               if (!result) {
                  throw new Exception("swap fail - " + oldDeviceId);
               }

               boolean res = licenseDao.deleteLicenseInfoForE2EByDeviceId(oldDeviceId);
               if (!res) {
                  logger.error("Success deactivation but fail to delete lincse info on DB" + oldDeviceId);
                  throw new Exception("deactivaion fail - " + oldDeviceId);
               }
            } catch (Exception var38) {
               logger.error("", var38);
               throw new Exception("deactivaion exception - " + oldDeviceId);
            }

            result = deviceDao.deleteDevice(oldDeviceId);
            if (!result) {
               throw new Exception("deactivaion success, delete from DB fail - " + oldDeviceId);
            } else {
               new ServerLogEntity();
               monMgr.connectionReload(oldDeviceId, 0);
               monMgr.scheduleReload(oldDeviceId, 0);
               monMgr.deleteConnectionInfo(oldDeviceId);
               WSCall.setPlayerRequest(oldDeviceId, "agent restart");
               DBCacheUtils.deletePreAssignedGroup(oldDeviceId);
               List approvedList = new ArrayList();
               String deviceType = newDevice.getDevice_type();
               Map param = new HashMap();
               param.put("device_id", newDeviceId);
               param.put("device_name", oldDevice.getDevice_name());
               param.put("device_type", newDevice.getDevice_type());
               param.put("group_id", oldDeviceGroupId);
               param.put("current_group_id", 999999);
               param.put("device_model_name", (Object)null);
               param.put("location", oldDevice.getLocation());
               param.put("is_approved", true);
               param.put("calDate", "");
               param.put("organization", organization);
               boolean result = deviceDao.setDeviceForApproval(param);
               if (!result) {
                  throw new Exception("activaion success, approval to DB fail - " + newDeviceId);
               } else {
                  approvedList.add(newDeviceId);
                  DeviceGeneralConf deviceTemp = deviceDao.getDeviceGeneralConf(newDeviceId, true);
                  DBCacheUtils.setDeviceGeneralConf(deviceTemp, newDeviceId);
                  DeviceGroupInfo groupInfo = DeviceGroupInfoImpl.getInstance();
                  groupInfo.addGroupTotalCount(oldDeviceGroupId, 1L);
                  DownloadInfo downloadDao = DownloadInfoImpl.getInstance();
                  logger.error("[MagicInfo_DeviceUtils][APPROVAL][START FTP REGISTERING]DeviceType is : " + deviceType);
                  if (downloadDao.getUserByName(newDeviceId) == null) {
                     BaseUser user = new BaseUser();
                     user.setName(newDeviceId);
                     user.setPassword("MagicInfo");
                     UserManager userMgr = (new DbUserManagerFactory()).createUserManager();
                     userMgr.save(user);
                     logger.error("[MagicInfo_DeviceUtils][APPROVAL][FTP REGISTERING]DeviceID : " + newDeviceId);
                     logger.error("[MagicInfo_DeviceUtils][APPROVAL][FTP REGISTERING]realPassword : MagicInfo");
                  }

                  logger.error("[MagicInfo_DeviceUtils][APPROVAL][END FTP REGISTERING]=====================================");
                  motMgr.connectionReload(newDeviceId, 1);
                  motMgr.scheduleReload(newDeviceId, 1);
                  ScheduleInfoEntity schEntity = motMgr.getScheduleStatus(newDeviceId);
                  if (!StrUtils.nvl(CommonConfig.get("saas.eu.enable")).equalsIgnoreCase("TRUE") && schEntity != null) {
                     try {
                        ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
                        schInfo.deploySchedule(newDeviceId, schEntity.getScheduleId(), schEntity.getScheduleVersion());
                     } catch (Exception var35) {
                        logger.fatal("[MagicInfo_DeviceUtils][APPROVAL] Error of mapping content schedule while approving device " + newDeviceId);
                     }

                     try {
                        MessageInfo msgInfo = MessageInfoImpl.getInstance();
                        msgInfo.deployMessage(newDeviceId, schEntity.getMessageId(), schEntity.getMessageVersion());
                     } catch (Exception var34) {
                        logger.fatal("[MagicInfo_DeviceUtils][APPROVAL] Error of mapping message mschedule while approving device " + newDeviceId);
                     }
                  }

                  JobManager jobMgr = JobManagerImpl.getInstance();
                  jobMgr.deployJobSchedule("", newDevice);
                  WSCall.setPlayerRequest(newDeviceId, "device approval");
                  if (motMgr.isConnected(newDeviceId)) {
                     DeviceGeneralConf info = new DeviceGeneralConf();
                     info.setDevice_id(newDeviceId);
                     info.setDevice_name(oldDevice.getDevice_name());
                     DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
                     confManager.reqSetGeneralToDevice(info, "");
                  }

                  makeExpirationJob(newDeviceId, "");
                  strCommand = rms.getMessage("TEXT_TITLE_DEVICE_APPROVAL_P", (Object[])null, Locale.ENGLISH);
                  String message = "device_approval_success";

                  try {
                     Float deviceTypeVerseion = newDevice.getDevice_type_version();
                     boolean isSupportStatistics = deviceType.equalsIgnoreCase("iPLAYER") || deviceType.equalsIgnoreCase("SPLAYER") && deviceTypeVerseion >= CommonDataConstants.ALL_DEVICE_TYPE_VERSION_ARRAY[2];
                     if (isSupportStatistics && !deviceDao.isRequestTimeExist(newDeviceId) && deviceDao.addStatRequestTimeInsertCurrent(newDeviceId)) {
                        logger.info("[MagicInfo_DeviceUtils][APPROVAL] Statistics Requesttiem is Inserted deivce: " + newDeviceId);
                     }
                  } catch (Exception var37) {
                     logger.error("", var37);
                  }

                  if (message != null && message.equalsIgnoreCase("device_approval_success") && approvedList != null && approvedList.size() > 0 && isSupportNOC()) {
                     DeviceNocInfo nocDao = DeviceNocInfoImpl.getInstance();
                     boolean nocGroup = nocDao.isNocSupportGroup(oldDeviceGroupId);
                     if (nocGroup) {
                        DeviceNocManager nocService = DeviceNocManagerImpl.getInstance();
                        nocService.thingworxCreateDevice(approvedList);
                     }
                  }

                  return message;
               }
            }
         }
      } else {
         throw new Exception("Deivce Type must be same");
      }
   }

   public static String approveAndreplace(String deviceIds, Long groupId, String deviceName, String location, String selDeviceId, Locale locale, String sessionId, String userId, String calDate, String organization, String ipAddress) throws Exception {
      String deviceId = "";
      String message = null;
      DeviceLogManager logMgr = DeviceLogManagerImpl.getInstance();
      SlmLicenseManager licenseMgr = SlmLicenseManagerImpl.getInstance();
      new ServerLogEntity();
      String deviceModelName = null;
      String[] chkInfo = null;
      if (deviceIds != null && !deviceIds.equals("")) {
         chkInfo = deviceIds.split(";");
      }

      if (locale == null || locale.toString().equals("")) {
         locale = Locale.US;
      }

      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      groupDao.getOrgIdByGroupId(groupId);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      String eventType = "TEXT_TITLE_NON_APPROVAL_DEVICE_APPROVAL_P";
      rms.getMessage("COM_SID_MENU", (Object[])null, locale);
      rms.getMessage("COM_TV_SID_UNAPPROVED", (Object[])null, locale);
      int cntRemainLicenseByDeviceType = false;
      int nowCntApproval = 0;
      String deviceType = null;
      Float deviceTypeVersion = 1.0F;
      List approvedList = new ArrayList();
      Device tmpDevice;
      if (chkInfo != null) {
         nowCntApproval = chkInfo.length;
         String tmpDeviceId = "";
         if (selDeviceId != null && !selDeviceId.equals("")) {
            tmpDeviceId = selDeviceId;
         } else {
            tmpDeviceId = chkInfo[0];
         }

         tmpDevice = deviceInfo.getDevice(tmpDeviceId);
         deviceTypeVersion = tmpDevice.getDevice_type_version();
         deviceType = tmpDevice.getDevice_type();
      } else if (selDeviceId != null && !selDeviceId.equals("")) {
         ++nowCntApproval;
         tmpDevice = deviceInfo.getDevice(selDeviceId);
         deviceType = tmpDevice.getDevice_type();
         deviceTypeVersion = tmpDevice.getDevice_type_version();
      }

      int cntRemainLicenseByDeviceType = licenseMgr.getRemainLicenseCountByDeviceType(deviceType);
      logger.error("[MagicInfo_DeviceUtils][APPROVAL] devices approval modelName:" + deviceModelName + ", deviceType:" + deviceType + ", cntRemainLicenseByDeviceType:" + cntRemainLicenseByDeviceType + ", nowCount : " + nowCntApproval);
      if (cntRemainLicenseByDeviceType < nowCntApproval) {
         rms.getMessage("TEXT_MAX_OVER_DEVICE_APPROVAL_P", (Object[])null, locale);
         message = "device_approval_max_connection_over";
      } else {
         if (selDeviceId != null && !selDeviceId.equals("")) {
            if (selDeviceId != null && !selDeviceId.equals("")) {
               deviceId = selDeviceId;
            } else if (chkInfo != null) {
               deviceId = chkInfo[0];
            }

            Device device = deviceInfo.getDevice(deviceId);
            if (calDate.equals("∞")) {
               calDate = "";
            }

            Map param = new HashMap();
            param.put("device_id", deviceId);
            param.put("device_name", deviceName);
            param.put("device_type", device.getDevice_type());
            param.put("group_id", groupId);
            param.put("current_group_id", 999999);
            param.put("device_model_name", deviceModelName);
            param.put("location", location);
            param.put("is_approved", true);
            param.put("calDate", calDate);
            param.put("organization", organization);
            boolean result = deviceInfo.setDeviceForApproval(param);
            if (result) {
               approvedList.add(deviceId);
               DeviceGeneralConf deviceTemp = deviceInfo.getDeviceGeneralConf(deviceId, true);
               DBCacheUtils.setDeviceGeneralConf(deviceTemp, deviceId);
               groupDao.addGroupTotalCount(groupId, 1L);
               DownloadInfo downloadDao = DownloadInfoImpl.getInstance();
               logger.error("[MagicInfo_DeviceUtils][APPROVAL][START FTP REGISTERING]DeviceType is : " + deviceType);
               if (downloadDao.getUserByName(deviceId) == null) {
                  BaseUser user = new BaseUser();
                  user.setName(deviceId);
                  user.setPassword("MagicInfo");
                  UserManager userMgr = (new DbUserManagerFactory()).createUserManager();
                  userMgr.save(user);
                  logger.error("[MagicInfo_DeviceUtils][APPROVAL][FTP REGISTERING]DeviceID : " + deviceId);
                  logger.error("[MagicInfo_DeviceUtils][APPROVAL][FTP REGISTERING]realPassword : MagicInfo");
               }

               logger.error("[MagicInfo_DeviceUtils][APPROVAL][END FTP REGISTERING]=====================================");
               motMgr.connectionReload(deviceId, 1);
               motMgr.scheduleReload(deviceId, 1);
               WSCall.setPlayerRequest(deviceId, "device approval");
               if (motMgr.isConnected(deviceId)) {
                  DeviceGeneralConf info = new DeviceGeneralConf();
                  info.setDevice_id(deviceId);
                  info.setDevice_name(deviceName);
                  DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
                  confManager.reqSetGeneralToDevice(info, sessionId);
               }

               makeExpirationJob(deviceId, calDate);
               message = "device_approval_success";

               try {
                  Float deviceTypeVerseion = device.getDevice_type_version();
                  boolean isSupportStatistics = deviceType.equalsIgnoreCase("iPLAYER") || deviceType.equalsIgnoreCase("SPLAYER") && deviceTypeVerseion >= CommonDataConstants.ALL_DEVICE_TYPE_VERSION_ARRAY[2];
                  if (isSupportStatistics && !deviceInfo.isRequestTimeExist(deviceId) && deviceInfo.addStatRequestTimeInsertCurrent(deviceId)) {
                     logger.info("[MagicInfo_DeviceUtils][APPROVAL] Statistics Requesttiem is Inserted deivce: " + deviceId);
                  }
               } catch (Exception var37) {
                  logger.error("", var37);
               }
            } else {
               message = "device_approval_fail";
            }
         }

         if (message != null && message.equalsIgnoreCase("device_approval_success") && approvedList != null && approvedList.size() > 0 && isSupportNOC()) {
            DeviceNocInfo nocDao = DeviceNocInfoImpl.getInstance();
            boolean nocGroup = nocDao.isNocSupportGroup(groupId);
            if (nocGroup) {
               DeviceNocManager nocService = DeviceNocManagerImpl.getInstance();
               nocService.thingworxCreateDevice(approvedList);
            }
         }
      }

      return message;
   }

   public static String changeExpiration(String[] deviceIdArr, String selDeviceId, String calDate) throws Exception {
      String message = null;
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      Device device;
      if (selDeviceId != null && !selDeviceId.equals("")) {
         device = deviceInfo.getDevice(selDeviceId);
         Map param = new HashMap();
         param.put("device_id", selDeviceId);
         param.put("calDate", calDate);
         param.put("is_approved", true);
         param.put("device_type", device.getDevice_type());
         deviceInfo.setNameDeviceAndModel(param);
         makeExpirationJob(selDeviceId, calDate);
      } else if (deviceIdArr != null && deviceIdArr.length > 0) {
         device = null;
         String deviceId = null;

         for(int i = 0; i < deviceIdArr.length; ++i) {
            deviceId = deviceIdArr[i];
            device = deviceInfo.getDevice(deviceId);
            Map param = new HashMap();
            param.put("device_id", deviceId);
            param.put("calDate", calDate);
            param.put("is_approved", true);
            param.put("device_type", device.getDevice_type());
            deviceInfo.setNameDeviceAndModel(param);
            makeExpirationJob(deviceId, calDate);
         }
      }

      message = "device_approval_success";
      return message;
   }

   public static boolean useSocTimeZone(String deviceType, List timeZoneList, HashMap timeZoneListMulti) {
      boolean result = false;
      if (deviceType.equalsIgnoreCase("SPLAYER") || deviceType.equalsIgnoreCase("S2PLAYER") || deviceType.equalsIgnoreCase("LPLAYER")) {
         if (timeZoneList != null && timeZoneList.size() > 0) {
            result = true;
         }

         if (timeZoneListMulti != null && timeZoneListMulti.size() > 0) {
            result = true;
         }
      }

      return result;
   }

   public static void makeExpirationJob(String deviceId, String calDate) {
      Scheduler scheduler = ScheduleManager.getSchedulerInstance();
      String schedulerJobName = "ExpirationDevice_" + deviceId;
      String schedulerJobGroup = "ExpirationDevice";

      try {
         CommonUtils.deleteJob(schedulerJobName, schedulerJobGroup);
         if (calDate != null && !calDate.equals("")) {
            JobDetail jobdetail = CommonUtils.getJobDetail(schedulerJobName, schedulerJobGroup, ExpirationDeviceJob.class);
            SimpleTrigger trigger = CommonUtils.getSimpleTrigger(schedulerJobName, schedulerJobGroup, DateUtils.string2Timestamp(calDate, "yyyy-MM-dd"), (Date)null, 0, 1L);
            scheduler.scheduleJob(jobdetail, trigger);
         }
      } catch (SchedulerException var7) {
         logger.error(var7);
      }

   }

   public static void makeMail(String message, String mailContent, String toUserIdList, long orgId, String attachedFileName) {
      Boolean mailEnable = false;
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Map infoMap = null;

      try {
         infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
         mailEnable = (Boolean)infoMap.get("SMTP_ENABLE");
      } catch (Exception var14) {
         logger.error("DeviceUtils: makeMail " + var14.getMessage(), var14);
      }

      if (attachedFileName != null && !attachedFileName.equals("")) {
         infoMap.put("attachedFileName", attachedFileName);
      }

      if (mailEnable && infoMap != null) {
         MailConfigurationManager mailComposer = null;
         Mail mail = null;
         MailManagerInterface mailManager = MailManager.getInstance();

         try {
            mailComposer = new MailConfigurationManager();
            mail = mailComposer.makeMail(toUserIdList, (String)null, message, mailContent, (String)null);
            mailManager.sendEmail(mail, infoMap);
         } catch (Exception var13) {
            logger.error("DeviceUtils-makeMail " + var13.getMessage(), var13);
         }

      }
   }

   public static List refreshPriorityByDeviceType(Long groupId, String deviceId) throws Exception {
      return refreshPriorityByDeviceType(groupId, deviceId, (SqlSession)null);
   }

   public static List refreshPriorityByDeviceType(Long groupId, String deviceId, SqlSession session) throws Exception {
      ScheduleInfo schInfo = ScheduleInfoImpl.getInstance(session);
      MessageInfo mInfo = MessageInfoImpl.getInstance();
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance(session);
      DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance(session);
      ScheduleInfoDAO dao = new ScheduleInfoDAO(session);
      ScheduleAdminDao scheduleAdminDao = new ScheduleAdminDao(session);
      List deployDefaultPrograms = null;
      Long updatePriority = deviceDao.getMinimalPriorityByGroupId(groupId);
      String defaultProgramId = null;
      String deployedProgramId = null;
      ProgramEntity program = null;
      logger.error("[Device Update]-Refresh Group Priority  groupId: " + groupId + " deviceId: " + deviceId);
      if (groupId != 999999L) {
         int scheduleCount = schInfo.getCountProgramIdByGroupId(groupId);
         ProgramEntity scheduleInfo = null;
         ProgramEntity deploidScheduleInfo = null;
         defaultProgramId = deviceGroupDao.getDefaultProgramId(groupId);
         if (scheduleCount > 0) {
            ScheduleAdminInfo scheduleAdmin = ScheduleAdminInfoImpl.getInstance();
            deployedProgramId = scheduleAdmin.getProgramIdByDeviceGroupId(groupId);
            deploidScheduleInfo = schInfo.getProgram(deployedProgramId);
         } else {
            scheduleInfo = schInfo.getProgram(defaultProgramId);
         }

         Long schePriority = null;
         if (scheduleCount > 0 && deploidScheduleInfo != null) {
            schePriority = deviceGroupDao.getPriority(deploidScheduleInfo.getDevice_type(), deploidScheduleInfo.getDevice_type_version());
         } else if (scheduleInfo != null) {
            schePriority = deviceGroupDao.getPriority(scheduleInfo.getDevice_type(), scheduleInfo.getDevice_type_version());
         }

         if (scheduleCount > 0 && schePriority != null && updatePriority != 999999L && updatePriority > schePriority) {
            logger.error("[MagicInfo_GroupPriorityUpdate] current group priority : " + updatePriority + " schedule priority : " + schePriority);
         } else if (schePriority != null && updatePriority != 999999L && updatePriority < schePriority) {
            logger.error("[MagicInfo_GroupPriorityUpdate] Unmapping Schedule(deploy default progmra, message) groupId: " + groupId + " deviceId: " + deviceId);
            schInfo.setDefaultProgramId(groupId, defaultProgramId);
            mInfo.unmappMessageId(groupId);
            if (deviceId != null && !deviceId.equals("")) {
               mInfo.deployMessage(deviceId, "00000000-0000-0000-0000-000000000000", 1L);
            }

            program = dao.getProgram(defaultProgramId);
            program.setDevice_group_ids(groupId.toString());
            deployDefaultPrograms = new ArrayList();
            deployDefaultPrograms.add(program);
            if (session == null) {
               scheduleAdminDao.deployDefaultPrograms(deployDefaultPrograms);
            }
         } else {
            logger.error("[MagicInfo_GroupPriorityUpdate] current group priority : " + updatePriority + " schedule priority : " + schePriority);
         }
      }

      if (updatePriority != null) {
         logger.error("[Device Update]-Update Group Priority updatePriority: " + updatePriority + " groupId: " + groupId);
         deviceGroupDao.updateDeviceGroupPriority(updatePriority, groupId, session);
      }

      checkGroupType(groupId, deviceGroupDao, session);
      return session != null ? deployDefaultPrograms : null;
   }

   public static void checkGroupType(Long groupId, DeviceGroupInfo deviceGroupDao) throws SQLException {
      checkGroupType(groupId, deviceGroupDao, (SqlSession)null);
   }

   public static void checkGroupType(Long groupId, DeviceGroupInfo deviceGroupDao, SqlSession session) throws SQLException {
      List deviceTypeList = deviceGroupDao.getDeviceTypesMapGroup(groupId);
      String deviceGroupType = null;
      if (!deviceTypeList.isEmpty()) {
         if (deviceTypeList.size() > 1) {
            boolean isSamePlatform = true;
            String[] deviceTypeSplayerCheckList = new String[]{"SPLAYER", "SIGNAGE"};

            for(int i = 0; i < deviceTypeSplayerCheckList.length; ++i) {
               if (!deviceTypeList.contains(deviceTypeSplayerCheckList[i])) {
                  isSamePlatform = false;
                  break;
               }
            }

            if (isSamePlatform) {
               deviceGroupType = "SPLAYER";
            }
         } else {
            deviceGroupType = (String)deviceTypeList.get(0);
         }
      }

      if (deviceGroupType == null) {
         deviceGroupDao.setGroupTypeDefault(groupId);
         logger.error("[Device Update]- Update Group Type groupId: " + groupId + " groupType: null");
      } else if ("LPLAYER".equalsIgnoreCase(deviceGroupType)) {
         deviceGroupDao.setDeviceGroupType(groupId.intValue(), "LPLAYER", session);
         logger.error("[Device Update]- Update Group Type groupId: " + groupId + " groupType: LPLAYER");
      } else if ("SPLAYER".equalsIgnoreCase(deviceGroupType)) {
         deviceGroupDao.setDeviceGroupType(groupId.intValue(), "SPLAYER", session);
         logger.error("[Device Update]- Update Group Type groupId: " + groupId + " groupType: SPLAYER");
      } else if ("S4PLAYER".equalsIgnoreCase(deviceGroupType)) {
         deviceGroupDao.setDeviceGroupType(groupId.intValue(), "S4PLAYER", session);
         logger.error("[Device Update]- Update Group Type groupId: " + groupId + " groupType: S4PLAYER");
      } else if ("S5PLAYER".equalsIgnoreCase(deviceGroupType)) {
         deviceGroupDao.setDeviceGroupType(groupId.intValue(), "S5PLAYER", session);
         logger.error("[Device Update]- Update Group Type groupId: " + groupId + " groupType: S5PLAYER");
      } else if ("SIGNAGE".equalsIgnoreCase(deviceGroupType)) {
         deviceGroupDao.setDeviceGroupType(groupId.intValue(), "SIGNAGE", session);
         logger.error("[Device Update]- Update Group Type groupId: " + groupId + " groupType: SIGNAGE");
      } else if ("iPLAYER".equalsIgnoreCase(deviceGroupType)) {
         deviceGroupDao.setDeviceGroupType(groupId.intValue(), "iPLAYER", session);
         logger.error("[Device Update]- Update Group Type groupId: " + groupId + " groupType: iPLAYER");
      } else if ("3rdPartyPLAYER".equalsIgnoreCase(deviceGroupType)) {
         deviceGroupDao.setDeviceGroupType(groupId.intValue(), "3rdPartyPLAYER", session);
         logger.error("[Device Update]- Update Group Type groupId: " + groupId + " groupType: 3rd PLAYER");
      } else if ("MPLAYER".equalsIgnoreCase(deviceGroupType)) {
         deviceGroupDao.setDeviceGroupType(groupId.intValue(), "MPLAYER", session);
         logger.error("[Device Update]- Update Group Type groupId: " + groupId + " groupType: MPLAYER");
      } else if ("APLAYER".equalsIgnoreCase(deviceGroupType)) {
         deviceGroupDao.setDeviceGroupType(groupId.intValue(), "APLAYER", session);
         logger.error("[Device Update]- Update Group Type groupId: " + groupId + " groupType: APLAYER");
      } else if ("RSPLAYER".equalsIgnoreCase(deviceGroupType)) {
         deviceGroupDao.setDeviceGroupType(groupId.intValue(), "RSPLAYER", session);
         logger.error("[Device Update]- Update Group Type groupId: " + groupId + " groupType: RSPLAYER");
      } else if ("RIPLAYER".equalsIgnoreCase(deviceGroupType)) {
         deviceGroupDao.setDeviceGroupType(groupId.intValue(), "RIPLAYER", session);
         logger.error("[Device Update]- Update Group Type groupId: " + groupId + " groupType: RIPLAYER");
      } else if ("LEDBOX".equalsIgnoreCase(deviceGroupType)) {
         deviceGroupDao.setDeviceGroupType(groupId.intValue(), "LEDBOX", session);
         logger.error("[Device Update]- Update Group Type groupId: " + groupId + " groupType: LEDBOX");
      } else if ("FLIP".equalsIgnoreCase(deviceGroupType)) {
         deviceGroupDao.setDeviceGroupType(groupId.intValue(), "FLIP", session);
         logger.error("[Device Update]- Update Group Type groupId: " + groupId + " groupType: FLIP");
      } else if ("RLEDBOX".equalsIgnoreCase(deviceGroupType)) {
         deviceGroupDao.setDeviceGroupType(groupId.intValue(), "RLEDBOX", session);
         logger.error("[Device Update]- Update Group Type groupId: " + groupId + " groupType: RLEDBOX");
      } else if ("RSIGNAGE".equalsIgnoreCase(deviceGroupType)) {
         deviceGroupDao.setDeviceGroupType(groupId.intValue(), "RSIGNAGE", session);
         logger.error("[Device Update]- Update Group Type groupId: " + groupId + " groupType: RSIGNAGE");
      } else if ("WPLAYER".equalsIgnoreCase(deviceGroupType)) {
         deviceGroupDao.setDeviceGroupType(groupId.intValue(), "WPLAYER", session);
         logger.error("[Device Update]- Update Group Type groupId: " + groupId + " groupType: WPLAYER");
      } else if ("RKIOSK".equalsIgnoreCase(deviceGroupType)) {
         deviceGroupDao.setDeviceGroupType(groupId.intValue(), "RKIOSK", session);
         logger.error("[Device Update]- Update Group Type groupId: " + groupId + " groupType: RKIOSK");
      } else {
         logger.fatal("[Device Update]- CAN NOT UPDATE GROUP groupId: " + groupId + " groupType: NOT MATCHED - " + deviceGroupType);
      }

   }

   public static boolean comparePriority(Long groupId, String deviceId) throws SQLException {
      logger.error("[Device Update]- comparePriority  groupId: " + groupId + " deviceId: " + deviceId);
      DeviceGeneralConf deviceGeneral = deviceDao.getDeviceGeneralConf(deviceId);
      Long groupPriority = deviceDao.getDeviceGroupPriority(groupId);
      long programPriority = 0L;

      try {
         programPriority = (long)deviceDao.getProgramDeviceTypeByGroupId(groupId);
      } catch (Exception var11) {
      }

      String deviceType = deviceGeneral.getDevice_type();
      Float deviceTypeVersion = 1.0F;
      if (deviceGeneral.getDevice_type_version() != null) {
         deviceTypeVersion = deviceGeneral.getDevice_type_version();
      }

      Long devicePriority = deviceGroupDao.getPriority(deviceType, deviceTypeVersion);
      if (groupPriority.equals(DeviceConstants.DEV_GROUP_BASIC_PRIORITY)) {
         logger.error("[Device Update]- groupPriority is 999999");

         try {
            logger.error("[Device Update]- goint to checking mapping schedule on device group");
            if (programPriority == 0L) {
               return false;
            } else {
               logger.error("[Device Update]- schePriority: " + programPriority);
               if (devicePriority < programPriority && devicePriority >= 0L) {
                  logger.error("[Device Update]- devicePriority: " + devicePriority + " < schePriority: " + programPriority);
                  return true;
               } else {
                  logger.error("[Device Update]- devicePriority: " + devicePriority + " => schePriority: " + programPriority);
                  return false;
               }
            }
         } catch (Exception var10) {
            return false;
         }
      } else if (groupPriority != DeviceConstants.DEV_GROUP_BASIC_PRIORITY && groupPriority > devicePriority) {
         return programPriority != 0L && programPriority > devicePriority && devicePriority >= 0L;
      } else {
         return false;
      }
   }

   public static boolean checkMinusPriority(String groupId, String deviceId) throws SQLException {
      DeviceGeneralConf deviceGeneral = deviceDao.getDeviceGeneralConf(deviceId);
      if (deviceGeneral == null) {
         return false;
      } else {
         String deviceType = deviceGeneral.getDevice_type();
         Float deviceTypeVersion = 1.0F;
         if (deviceGeneral.getDevice_type_version() != null) {
            deviceTypeVersion = deviceGeneral.getDevice_type_version();
         }

         Long devicePriority = deviceGroupDao.getPriority(deviceType, deviceTypeVersion);
         Long groupPriority = deviceGroupDao.getMinimumPriority(groupId);
         if (devicePriority != null && groupPriority != null) {
            if (groupPriority == 999999L) {
               return true;
            } else if (devicePriority > 0L && groupPriority > 0L) {
               return true;
            } else if ((devicePriority <= 0L || groupPriority >= 0L) && (devicePriority >= 0L || groupPriority <= 0L)) {
               if (devicePriority < 0L && groupPriority < 0L) {
                  String groupDeviceType = deviceGroupDao.getDeviceTypeByGroupId(Long.valueOf(groupId));
                  return deviceType != null && deviceType.equalsIgnoreCase(groupDeviceType);
               } else {
                  return false;
               }
            } else {
               return false;
            }
         } else {
            return false;
         }
      }
   }

   public static void deleteMapSchedule(Long groupId) throws SQLException {
      ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
      String defaultProgramId = deviceGroupDao.getDefaultProgramId(groupId);
      schInfo.setDefaultProgramId(groupId, defaultProgramId);
   }

   public static boolean isDeviceGroupAuth(String roleName, String userId) throws SQLException {
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      UserInfo userDao = UserInfoImpl.getInstance();
      UserGroupInfo userGroupDao = UserGroupInfoImpl.getInstance();
      String organNameByUserId = "";
      boolean isMu = false;
      User user = userDao.getUserByUserId(userId);
      if (user != null) {
         organNameByUserId = user.getOrganization();
         isMu = user.isMu();
      }

      if (isMu) {
         return false;
      } else {
         long orgId = -1L;
         if (SecurityUtils.getLoginUser() != null && userId.equals(SecurityUtils.getLoginUser().getUser_id())) {
            orgId = SecurityUtils.getLoginUser().getRoot_group_id();
         } else {
            orgId = userGroupDao.getOrgGroupIdByName(organNameByUserId);
         }

         Boolean permissions_func = serverSetupDao.checkPermissionsDeviceByOrgId(orgId);
         if (permissions_func != null && permissions_func) {
            if (roleName == null) {
               return false;
            } else {
               return !roleName.equals("Administrator") && !roleName.equals("Server Administrator");
            }
         } else {
            return false;
         }
      }
   }

   public static boolean isDeviceGroupAuth(User user) throws SQLException {
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      long orgId = userGroupInfo.getOrgGroupIdByName(user.getOrganization());
      Boolean permissions_func = serverSetupDao.checkPermissionsDeviceByOrgId(orgId);
      if (permissions_func != null && permissions_func) {
         if (user.isMu()) {
            return false;
         } else if (user.getRole_name() == null) {
            return false;
         } else if (!user.getRole_name().equals("Administrator") && !user.getRole_name().equals("Server Administrator")) {
            AbilityDao abilityDao = new AbilityDao();
            int abilityCount = abilityDao.hasAbilityByUserId(user.getUser_id(), "Device Read Authority");
            return abilityCount >= 1;
         } else {
            return false;
         }
      } else {
         return false;
      }
   }

   public static void arrangeGroupMinPriority() throws Exception {
      List groupList = deviceGroupDao.getGroupList();

      for(int i = 0; i < groupList.size(); ++i) {
         refreshPriorityByDeviceType(((DeviceGroup)groupList.get(i)).getGroup_id(), (String)null);
      }

   }

   public static void scanChildDevice(String deviceId, long[] childCntArr, String[] cabinetIPArr, String[] autoSetIDArr) throws Exception {
      boolean isSupportNoc = false;
      DeviceNocManagerImpl nocService = DeviceNocManagerImpl.getInstance();

      try {
         if (isSupportNOC()) {
            DeviceNocInfo nocDao = DeviceNocInfoImpl.getInstance();
            String groupId = deviceDao.getDeviceGroupIdByDeviceId(deviceId);
            boolean nocGroup = nocDao.isNocSupportGroup(Long.valueOf(groupId));
            if (nocGroup) {
               isSupportNoc = true;
            }
         }

         if (isSupportNoc) {
            nocService.thingworxDeleteCabinets(deviceId);
         }
      } catch (Exception var20) {
         logger.error("error while get exist cabinet list " + deviceId);
      }

      try {
         ledCabinetMgr.deleteLedCabinets(deviceId);
      } catch (Exception var19) {
         logger.error("error while deleteChildDevice " + deviceId, var19);
      }

      long childCnt = 0L;
      int var10;
      if (childCntArr.length > 0) {
         long[] var23 = childCntArr;
         int var9 = childCntArr.length;

         for(var10 = 0; var10 < var9; ++var10) {
            long cnt = var23[var10];
            childCnt += cnt;
         }
      }

      String sessionId = UUID.randomUUID().toString();
      DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();

      try {
         addDummyChildDevice(deviceId, childCntArr, "LED_CABINET");
      } catch (Exception var18) {
         logger.error("error while adding dummy child " + deviceId, var18);
      }

      int cabinetIpLength = false;
      int autoSetIdLength = false;
      if (cabinetIPArr != null && cabinetIPArr.length > 0) {
         var10 = cabinetIPArr.length;
      }

      if (autoSetIDArr != null && autoSetIDArr.length > 0) {
         int var28 = autoSetIDArr.length;
      }

      StringBuffer scanChildValue = new StringBuffer();
      if (childCntArr != null && childCntArr.length > 0) {
         for(int i = 0; i < childCntArr.length; ++i) {
            scanChildValue.append(i + 1 + ":" + childCntArr[i]);
            if (i < childCntArr.length - 1) {
               scanChildValue.append(";");
            }
         }
      }

      try {
         Device parentDevice = deviceDao.getDevice(deviceId);
         if (scanChildValue != null) {
            confManager.reqSetSboxCmd(deviceId, (List)null, "SCAN_CHILD_INFO", scanChildValue.toString(), sessionId);
         }

         parentDevice.setChild_cnt(childCnt);
         deviceDao.setDevice(parentDevice);
      } catch (Exception var17) {
         logger.error("error while setChild_cnt " + deviceId, var17);
      }

      try {
         if (isSupportNoc) {
            List list = new ArrayList();
            Map device = new HashMap();
            device.put("deviceId", deviceId);
            String cabinetCnt = "";

            for(int i = 0; i < childCntArr.length; ++i) {
               if (cabinetCnt.length() > 0) {
                  cabinetCnt = cabinetCnt + ",";
               }

               cabinetCnt = cabinetCnt + childCntArr[i];
            }

            device.put("cabinetCnt", cabinetCnt);
            list.add(device);
            nocService.thingworxCreateCabinets(list);
         }
      } catch (Exception var21) {
         logger.error("[DeviceUtils][thingworx] failed to call api for creating cabinets. :" + deviceId);
      }

   }

   public static void scanChildDevice(String deviceId, long childCnt) throws Exception {
      List childDeviceIds = deviceDao.getChildDeviceIdList(deviceId);
      MonitoringManager mgr = MonitoringManagerImpl.getInstance();
      String sessionId;
      if (childDeviceIds != null) {
         Iterator var5 = childDeviceIds.iterator();

         while(var5.hasNext()) {
            sessionId = (String)var5.next();
            mgr.setDisconnected(sessionId);
            DBCacheUtils.deleteDeviceEntities(sessionId);
         }
      }

      List childDeviceIdList = getChildDeviceIdList(deviceId, childCnt);

      try {
         deviceDao.deleteChildDevice(deviceId);
      } catch (Exception var11) {
         logger.error("error while deleteChildDevice " + deviceId, var11);
      }

      try {
         Device parentDevice = deviceDao.getDevice(deviceId);
         parentDevice.setChild_cnt(childCnt);
         deviceDao.setDevice(parentDevice);
      } catch (Exception var10) {
         logger.error("error while setChild_cnt " + deviceId, var10);
      }

      sessionId = UUID.randomUUID().toString();
      DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();

      try {
         addDummyChildDevice(deviceId, childDeviceIdList, "SIG_CHILD");
      } catch (Exception var9) {
         logger.error("error while adding dummy child" + deviceId, var9);
      }

      confManager.reqSetSignageCmd(deviceId, "SCAN_CHILD_INFO", String.valueOf(childCnt), sessionId);
   }

   public static void addDummyChildDevice(String deviceId, List deviceIdList, String deviceType) throws Exception {
      if (deviceIdList != null && deviceIdList.size() > 0) {
         String parentGroupId_str = deviceDao.getDeviceGroupIdByDeviceId(deviceId);
         int parentGroupId = Integer.valueOf(parentGroupId_str);
         Timestamp lastConnTime = new Timestamp(0L);
         List childDeviceList = new ArrayList();
         List childDeviceDisplayConfList = new ArrayList();

         for(int i = 1; i <= deviceIdList.size(); ++i) {
            Device childDevice = new Device();
            childDevice.setDefaultDBValues();
            String childDeviceId = (String)deviceIdList.get(i - 1);
            childDevice.setDevice_id(childDeviceId);
            childDevice.setDevice_type(deviceType);
            childDevice.setDevice_type_version(CommonDataConstants.TYPE_VERSION_1_0);
            childDevice.setDevice_model_name(deviceType);
            childDevice.setDevice_model_code("99");
            childDevice.setScreen_rotation(0L);
            childDevice.setLast_connection_time(lastConnTime);
            childDevice.setApplication_version("DEFAULT;DEFAULT");
            childDevice.setScreen_size("0");
            childDevice.setCreate_date(lastConnTime);
            childDevice.setIs_approved(true);
            childDevice.setDevice_name(String.valueOf(i));
            childDevice.setIs_child(true);
            childDevice.setMonitoring_interval(60L);
            DeviceDisplayConf childDeviceDisplayConf = new DeviceDisplayConf();
            childDeviceDisplayConf.setDefaultDBValues();
            childDeviceDisplayConf.setDevice_id(childDeviceId);
            childDeviceDisplayConf.setBasic_power("0");
            childDeviceDisplayConf.setBasic_volume(0L);
            childDeviceDisplayConf.setBasic_mute(0L);
            childDeviceDisplayConf.setBasic_panel_status(1L);
            childDeviceDisplayConf.setBasic_source(32L);
            childDeviceDisplayConf.setMnt_safety_lock(0L);
            childDeviceDisplayConf.setMisc_remocon(0L);
            childDeviceDisplayConf.setMisc_panel_lock(0L);
            childDeviceDisplayConf.setMisc_osd(0L);
            childDeviceDisplayConf.setMisc_all_lock(0L);
            childDeviceDisplayConf.setDiagnosis_monitor_temperature(0L);
            childDeviceDisplayConf.setDiagnosis_alarm_temperature(0L);
            childDeviceDisplayConf.setDiagnosis_panel_on_time("");
            childDeviceDisplayConf.setVwl_mode("DEFAULT");
            childDeviceDisplayConf.setVwl_position("DEFAULT");
            childDeviceDisplayConf.setVwl_format("DEFAULT");
            childDeviceDisplayConf.setVwl_layout("DEFAULT");
            childDeviceDisplayConf.setIs_child(true);
            childDeviceList.add(childDevice);
            childDeviceDisplayConfList.add(childDeviceDisplayConf);
         }

         deviceDao.addChildDevice((List)childDeviceList);
         deviceDisplayDao.addDeviceDisplayConf((List)childDeviceDisplayConfList);
         deviceDao.addDeviceGroupMapping(parentGroupId, deviceIdList);
      }
   }

   public static void addDummyChildDevice(String deviceId, long[] childCount, String deviceType) throws Exception {
      if (childCount != null && childCount.length > 0) {
         List ledCabinets = new ArrayList();

         for(int j = 0; j < childCount.length; ++j) {
            long cnt = childCount[j];

            for(int i = 0; (long)i < cnt; ++i) {
               LedCabinet cabinet = new LedCabinet(deviceId);
               cabinet.setCabinet_group_id((long)j + 1L);
               cabinet.setCabinet_id((long)i + 2L);
               ledCabinets.add(cabinet);
            }
         }

         ledCabinetMgr.addLedCabinet((List)ledCabinets);
      }
   }

   public static List getChildDeviceIdList(String parentDeviceId, long childCnt) {
      List ret = new ArrayList();

      for(int i = 1; (long)i <= childCnt; ++i) {
         ret.add(parentDeviceId + "_" + i);
      }

      return ret;
   }

   public static void sendChildCmd(String deviceId, String cmd, String targets) throws Exception {
      String sessionId = UUID.randomUUID().toString();
      DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
      if (cmd.equalsIgnoreCase("SIGNAGE_ID_AUTOSET")) {
         confManager.reqSetSignageCmd(deviceId, "SIGNAGE_ID_AUTOSET", (String)null, sessionId);
      } else if (cmd.equalsIgnoreCase("SIGNAGE_ID_SHOW")) {
         confManager.reqSetSignageCmd(deviceId, "SIGNAGE_ID_SHOW", (String)null, sessionId);
      } else if (cmd.equalsIgnoreCase("SIGNAGE_ID_HIDE")) {
         confManager.reqSetSignageCmd(deviceId, "SIGNAGE_ID_HIDE", (String)null, sessionId);
      } else if (cmd.equalsIgnoreCase("SBOX_ID_AUTOSET")) {
         confManager.reqSetSboxCmd(deviceId, (List)null, "SBOX_ID_AUTOSET", (String)null, sessionId);
      } else if (!cmd.equalsIgnoreCase("SBOX_ID_SHOW") && !cmd.equalsIgnoreCase("SBOX_ID_HIDE")) {
         if (cmd.equalsIgnoreCase("CLEAR_EXTERNAL_POWER_FAULT")) {
            confManager.reqSetSboxCmd(deviceId, (List)null, "CLEAR_EXTERNAL_POWER_FAULT", (String)null, sessionId);
         }
      } else {
         List targetList = Arrays.asList(targets.split(";"));
         List childIds = new ArrayList();
         if (targets != null && !targets.equals("")) {
            for(int i = 1; i <= 4; ++i) {
               childIds.clear();
               Iterator var8 = targetList.iterator();

               while(var8.hasNext()) {
                  String temp = (String)var8.next();
                  String childId = temp.split("_")[1];
                  if (childId.split("-")[0].equals(i + "")) {
                     childIds.add(childId);
                  }
               }

               if (childIds.size() > 0) {
                  confManager.reqSetSboxCmd(deviceId, childIds, cmd, (String)null, sessionId);
               }
            }
         } else {
            confManager.reqSetSboxCmd(deviceId, (List)null, cmd, (String)null, sessionId);
         }
      }

   }

   public static String getDeviceNameForList(String device_name, Long conn_child_cnt, Long child_cnt) {
      String device_name_for_list = null;
      if (child_cnt != null && child_cnt > 0L) {
         device_name_for_list = CommonUtils.cutLen(device_name.toCharArray(), 7) + "(" + conn_child_cnt + "/" + child_cnt + ")";
      } else {
         device_name_for_list = CommonUtils.cutLen(device_name.toCharArray(), 10);
      }

      return device_name_for_list;
   }

   public static boolean getDeviceTagStatus(long groupId) throws SQLException {
      List deviceList = deviceDao.getDeviceIdListByGroup((int)groupId);
      DeviceSystemSetupConfManager SystemSetupDao = DeviceSystemSetupConfManagerImpl.getInstance("PREMIUM");
      List tagValueList = null;
      String TempDeviceId = null;
      int tagCount = 0;
      List newdeviceList = new ArrayList();

      int i;
      for(i = 0; i < deviceList.size(); ++i) {
         TempDeviceId = (String)((Map)deviceList.get(i)).get("device_id");
         if (!TempDeviceId.contains("_")) {
            newdeviceList.add(deviceList.get(i));
         }
      }

      for(i = 0; i < newdeviceList.size(); ++i) {
         TempDeviceId = (String)((Map)newdeviceList.get(i)).get("device_id");
         tagValueList = SystemSetupDao.getDeviceTag(TempDeviceId);
         if (!tagValueList.isEmpty() && tagValueList.size() == 1) {
            ++tagCount;
         }
      }

      if (tagCount < 1) {
         return false;
      } else if (tagCount == newdeviceList.size()) {
         return true;
      } else {
         return false;
      }
   }

   public static String[] splitter(String str, int length) {
      String[] retArr = new String[length];

      for(int i = 0; i < length; ++i) {
         retArr[i] = "";
      }

      String[] targetArr = str.split(";");

      for(int i = 0; i < targetArr.length; ++i) {
         retArr[i] = targetArr[i];
      }

      return retArr;
   }

   public static String[] emptyArray(int length) {
      String[] retArr = new String[length];

      for(int i = 0; i < length; ++i) {
         retArr[i] = "";
      }

      return retArr;
   }

   public static HashMap getDeviceStatusInfos(String contextPath, DeviceMonitoring monitoring, boolean getResizedImg, CurrentPlayingEntity playingEntity, boolean monitoringStatus) {
      if (alwaysFullSize != null && alwaysFullSize) {
         getResizedImg = false;
      }

      String captureUrl = null;
      String thumbUrl = null;
      String scheduleName = null;
      String contentName = null;
      int basicSource = 32;
      if (playingEntity == null) {
         monitoringStatus = false;
      } else {
         basicSource = playingEntity.getInputSource();
      }

      switch(basicSource) {
      case 4:
         scheduleName = "S-Video";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_s_video" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 8:
         scheduleName = "Component";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_component" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 12:
         scheduleName = "AV";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_av" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 20:
         scheduleName = "PC";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_pc" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 24:
      case 31:
         scheduleName = "DVI";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_dvi" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 30:
         scheduleName = "BNC";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_bnc" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 33:
      case 34:
         scheduleName = "HDMI1";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_hdmi1" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 35:
      case 36:
         scheduleName = "HDMI2";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_hdmi2" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 37:
         scheduleName = "Display Port";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_display_port" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 48:
      case 64:
         if (playingEntity.getDirectChannel() != null) {
            String[] arrCh = playingEntity.getDirectChannel().split(";");
            if (arrCh.length == 6) {
               contentName = arrCh[3] + "-" + arrCh[5];
            } else if (arrCh.length == 5) {
               contentName = arrCh[3];
            }
         }

         scheduleName = "TV";
         captureUrl = thumbUrl = contextPath + "/images/device/device_tv" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 102:
         scheduleName = "Samsung Workspace";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_samsungworkspace.png";
         break;
      case 1000:
         scheduleName = "Network Standby";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_network_standby.png";
         break;
      default:
         if (!monitoringStatus) {
            thumbUrl = captureUrl = contextPath + "/image/img/thumb_img_power.png";
         } else {
            playingEntity = getPlayingContent(monitoring.getDevice_id());
            if (playingEntity != null) {
               List playingContentList = playingEntity.getContentLists();

               for(int j = 0; playingContentList != null && j < playingContentList.size(); ++j) {
                  ContentList playingContent = (ContentList)playingContentList.get(j);
                  if (playingContent.getFrameIndex() != null && playingContent.getFrameIndex().equals("0") && playingContent.getContentName() != null && !playingContent.getContentName().equals("") || playingContent.getFrameIndex() != null && playingContent.isMainFrame()) {
                     monitoring.setThumb_file_id(StrUtils.nvl(playingContent.getThumbnailFileId()));
                     monitoring.setThumb_file_name(StrUtils.nvl(playingContent.getThumbnailFileName()));
                     monitoring.setContent_id(StrUtils.nvl(playingContent.getContentId()));
                     monitoring.setContent_name(StrUtils.nvl(playingContent.getContentName()));
                     monitoring.setProgram_name(playingEntity.getProgramName());
                     break;
                  }
               }
            }

            if (playingEntity != null && playingEntity.getPanelStatus() == 1L) {
               captureUrl = thumbUrl = contextPath + "/images/device/panel_off.png";
            } else {
               captureUrl = thumbUrl = contextPath + "/images/device/device_on.png";
               if (!StrUtils.nvl(monitoring.getThumb_file_id()).equals("") && !StrUtils.nvl(monitoring.getThumb_file_name()).equals("")) {
                  Date now = new Date();

                  try {
                     String thumbnailFailName = monitoring.getThumb_file_name() + (getResizedImg ? "_MEDIUM.PNG" : "");
                     thumbUrl = contextPath + "/servlet/ContentThumbnail?thumb_id=" + monitoring.getThumb_file_id() + "&thumb_filename=" + URLEncoder.encode(thumbnailFailName, "UTF-8") + "&" + now.getTime();
                  } catch (Exception var15) {
                     thumbUrl = contextPath + "/servlet/ContentThumbnail?thumb_id=" + monitoring.getThumb_file_id() + "&thumb_filename=" + monitoring.getThumb_file_name() + "&" + now.getTime();
                  }
               }
            }
         }

         scheduleName = StrUtils.nvl(monitoring.getProgram_name());
         contentName = StrUtils.nvl(monitoring.getContent_name());
      }

      if (monitoring.getDevice_type().equals("FLIP") && monitoring.getDevice_type_version() == 1.0F) {
         captureUrl = thumbUrl = contextPath + (monitoringStatus ? "/images/device/device_flip_on.png" : "/image/img/thumb_img_power.png");
      } else if (monitoringStatus) {
         ScreenCaptureDAO scDao = new ScreenCaptureDAO();

         try {
            ScreenCaptureEntity scInfo = scDao.selInfoById(monitoring.getDevice_id());
            if (scInfo != null) {
               String[] fileName = scInfo.getFile_name().split("\\.");
               Date now = new Date();
               captureUrl = contextPath + "/servlet/ContentThumbnail?content_id=undefined&page_no=CAPTURE&thumb_filename=" + fileName[0] + "." + fileName[1] + (getResizedImg ? "&width=200&height=112" : "") + "&" + now.getTime();
            }
         } catch (SQLException var14) {
            logger.error("", var14);
         }
      }

      HashMap resultMap = new HashMap();
      resultMap.put("captureUrl", captureUrl);
      resultMap.put("thumbUrl", thumbUrl);
      resultMap.put("scheduleName", scheduleName);
      resultMap.put("contentName", contentName);
      return resultMap;
   }

   public static HashMap getV2DeviceStatusInfos(String contextPath, DeviceGeneralConf device, boolean getResizedImg, CurrentPlayingEntity playingEntity, boolean monitoringStatus) {
      if (alwaysFullSize != null && alwaysFullSize) {
         getResizedImg = false;
      }

      String captureUrl = null;
      String thumbUrl = null;
      String scheduleName = null;
      String contentName = null;
      int basicSource = 32;
      if (playingEntity == null) {
         monitoringStatus = false;
      } else {
         basicSource = playingEntity.getInputSource();
      }

      String[] arrCh;
      switch(basicSource) {
      case 4:
         scheduleName = "S-Video";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_s_video" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 8:
         scheduleName = "Component";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_component" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 12:
         scheduleName = "AV";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_av" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 20:
         scheduleName = "PC";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_pc" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 24:
      case 31:
         scheduleName = "DVI";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_dvi" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 30:
         scheduleName = "BNC";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_bnc" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 33:
      case 34:
         scheduleName = "HDMI1";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_hdmi1" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 35:
      case 36:
         scheduleName = "HDMI2";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_hdmi2" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 37:
         scheduleName = "Display Port";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_display_port" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 48:
         if (playingEntity.getDirectChannel() != null) {
            arrCh = playingEntity.getDirectChannel().split(";");
            if (arrCh.length == 6) {
               contentName = arrCh[3] + "-" + arrCh[5];
            } else if (arrCh.length == 5) {
               contentName = arrCh[3];
            }
         }

         scheduleName = "ATV";
         captureUrl = thumbUrl = contextPath + "/images/device/device_tv" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 64:
         if (playingEntity.getDirectChannel() != null) {
            arrCh = playingEntity.getDirectChannel().split(";");
            if (arrCh.length == 6) {
               contentName = arrCh[3] + "-" + arrCh[5];
            } else if (arrCh.length == 5) {
               contentName = arrCh[3];
            }
         }

         scheduleName = "DTV";
         captureUrl = thumbUrl = contextPath + "/images/device/device_dtv" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 102:
         scheduleName = "Samsung Workspace";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_samsungworkspace.png";
         break;
      case 1000:
         scheduleName = "Network Standby";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_network_standby.png";
         break;
      default:
         if (!monitoringStatus) {
            thumbUrl = captureUrl = contextPath + "/image/img/thumb_img_power.png";
         } else {
            playingEntity = getPlayingContent(device.getDevice_id());
            if (playingEntity != null) {
               List playingContentList = playingEntity.getContentLists();

               for(int j = 0; playingContentList != null && j < playingContentList.size(); ++j) {
                  ContentList playingContent = (ContentList)playingContentList.get(j);
                  if (playingContent.getFrameIndex() != null && playingContent.getFrameIndex().equals("0") && playingContent.getContentName() != null && !playingContent.getContentName().equals("") || playingContent.getFrameIndex() != null && playingContent.isMainFrame()) {
                     device.setThumb_file_id(StrUtils.nvl(playingContent.getThumbnailFileId()));
                     device.setThumb_file_name(StrUtils.nvl(playingContent.getThumbnailFileName()));
                     device.setContent_id(StrUtils.nvl(playingContent.getContentId()));
                     device.setContent_name(StrUtils.nvl(playingContent.getContentName()));
                     device.setProgram_name(playingEntity.getProgramName());
                     break;
                  }
               }
            }

            if (playingEntity != null && playingEntity.getPanelStatus() == 1L) {
               captureUrl = thumbUrl = contextPath + "/images/device/panel_off.png";
            } else {
               captureUrl = thumbUrl = contextPath + "/images/device/device_on.png";
               if (!StrUtils.nvl(device.getThumb_file_id()).equals("") && !StrUtils.nvl(device.getThumb_file_name()).equals("")) {
                  new Date();

                  try {
                     (new StringBuilder()).append(device.getThumb_file_name()).append(getResizedImg ? "_MEDIUM.PNG" : "").toString();
                     thumbUrl = contextPath + "/restapi/v2.0/cms/contents/thumbnails/" + device.getThumb_file_id();
                  } catch (Exception var15) {
                     thumbUrl = contextPath + "/restapi/v2.0/cms/contents/thumbnails/" + device.getThumb_file_id();
                  }
               }
            }
         }

         scheduleName = StrUtils.nvl(device.getProgram_name());
         contentName = StrUtils.nvl(device.getContent_name());
      }

      if (device.getDevice_type().equals("FLIP") && device.getDevice_type_version() == 1.0F) {
         captureUrl = thumbUrl = contextPath + (monitoringStatus ? "/images/device/device_flip_on.png" : "/image/img/thumb_img_power.png");
      } else if (monitoringStatus) {
         ScreenCaptureDAO scDao = new ScreenCaptureDAO();

         try {
            ScreenCaptureEntity scInfo = scDao.selInfoById(device.getDevice_id());
            if (scInfo != null) {
               String[] fileName = scInfo.getFile_name().split("\\.");
               Date now = new Date();
               captureUrl = contextPath + "/restapi/v2.0/cms/contents/thumbnails/CAPTURE?capturedFileName=" + fileName[0] + "." + fileName[1] + (getResizedImg ? "&width=200&height=112" : "") + "&" + now.getTime();
            }
         } catch (SQLException var14) {
            logger.error("", var14);
         }
      }

      HashMap resultMap = new HashMap();
      resultMap.put("captureUrl", captureUrl);
      resultMap.put("thumbUrl", thumbUrl);
      resultMap.put("scheduleName", scheduleName);
      resultMap.put("contentName", contentName);
      return resultMap;
   }

   public static HashMap getDeviceStatusInfos(String contextPath, DeviceMonitoring monitoring, boolean getResizedImg) {
      if (alwaysFullSize != null && alwaysFullSize) {
         getResizedImg = false;
      }

      String captureUrl = null;
      String thumbUrl = null;
      String scheduleName = null;
      String contentName = null;
      boolean monitoringStatus = isConnected(monitoring.getDevice_id());
      CurrentPlayingEntity playingEntity = motMgr.getPlayingContent(monitoring.getDevice_id());
      int basicSource = 32;
      if (playingEntity == null) {
         monitoringStatus = false;
      } else {
         basicSource = playingEntity.getInputSource();
      }

      switch(basicSource) {
      case 4:
         scheduleName = "S-Video";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_s_video" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 8:
         scheduleName = "Component";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_component" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 12:
         scheduleName = "AV";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_av" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 20:
         scheduleName = "PC";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_pc" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 24:
      case 31:
         scheduleName = "DVI";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_dvi" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 30:
         scheduleName = "BNC";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_bnc" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 33:
      case 34:
         scheduleName = "HDMI1";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_hdmi1" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 35:
      case 36:
         scheduleName = "HDMI2";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_hdmi2" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 37:
         scheduleName = "Display Port";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_display_port" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 48:
      case 64:
         if (playingEntity.getDirectChannel() != null) {
            String[] arrCh = playingEntity.getDirectChannel().split(";");
            if (arrCh.length == 6) {
               contentName = arrCh[3] + "-" + arrCh[5];
            } else if (arrCh.length == 5) {
               contentName = arrCh[3];
            }
         }

         scheduleName = "TV";
         captureUrl = thumbUrl = contextPath + "/images/device/device_tv" + (monitoringStatus ? "_on" : "") + ".png";
         break;
      case 1000:
         scheduleName = "Network Standby";
         contentName = "";
         captureUrl = thumbUrl = contextPath + "/images/device/device_network_standby.png";
         break;
      default:
         if (!monitoringStatus) {
            thumbUrl = captureUrl = contextPath + "/image/img/thumb_img_power.png";
         } else {
            playingEntity = getPlayingContent(monitoring.getDevice_id());
            if (playingEntity != null) {
               List playingContentList = playingEntity.getContentLists();

               for(int j = 0; playingContentList != null && j < playingContentList.size(); ++j) {
                  ContentList playingContent = (ContentList)playingContentList.get(j);
                  if (playingContent.getFrameIndex() != null && playingContent.getFrameIndex().equals("0") && playingContent.getContentName() != null && !playingContent.getContentName().equals("") || playingContent.getFrameIndex() != null && playingContent.isMainFrame()) {
                     monitoring.setThumb_file_id(StrUtils.nvl(playingContent.getThumbnailFileId()));
                     monitoring.setThumb_file_name(StrUtils.nvl(playingContent.getThumbnailFileName()));
                     monitoring.setContent_id(StrUtils.nvl(playingContent.getContentId()));
                     monitoring.setContent_name(StrUtils.nvl(playingContent.getContentName()));
                     monitoring.setProgram_name(playingEntity.getProgramName());
                     break;
                  }
               }
            }

            if (playingEntity != null && playingEntity.getPanelStatus() == 1L) {
               captureUrl = thumbUrl = contextPath + "/images/device/panel_off.png";
            } else {
               captureUrl = thumbUrl = contextPath + "/images/device/device_on.png";
               if (!StrUtils.nvl(monitoring.getThumb_file_id()).equals("") && !StrUtils.nvl(monitoring.getThumb_file_name()).equals("")) {
                  Date now = new Date();

                  try {
                     String thumbnailFailName = monitoring.getThumb_file_name() + (getResizedImg ? "_MEDIUM.PNG" : "");
                     thumbUrl = contextPath + "/servlet/ContentThumbnail?thumb_id=" + monitoring.getThumb_file_id() + "&thumb_filename=" + URLEncoder.encode(thumbnailFailName, "UTF-8") + "&" + now.getTime();
                  } catch (Exception var15) {
                     thumbUrl = contextPath + "/servlet/ContentThumbnail?thumb_id=" + monitoring.getThumb_file_id() + "&thumb_filename=" + monitoring.getThumb_file_name() + "&" + now.getTime();
                  }
               }
            }
         }

         scheduleName = StrUtils.nvl(monitoring.getProgram_name());
         contentName = StrUtils.nvl(monitoring.getContent_name());
      }

      if (monitoring.getDevice_type().equals("FLIP")) {
         captureUrl = thumbUrl = contextPath + (monitoringStatus ? "/images/device/device_flip_on.png" : "/image/img/thumb_img_power.png");
      } else if (monitoringStatus) {
         ScreenCaptureDAO scDao = new ScreenCaptureDAO();

         try {
            ScreenCaptureEntity scInfo = scDao.selInfoById(monitoring.getDevice_id());
            if (scInfo != null) {
               String[] fileName = scInfo.getFile_name().split("\\.");
               Date now = new Date();
               captureUrl = contextPath + "/servlet/ContentThumbnail?content_id=undefined&page_no=CAPTURE&thumb_filename=" + fileName[0] + "." + fileName[1] + (getResizedImg ? "&width=200&height=112" : "") + "&" + now.getTime();
            }
         } catch (SQLException var14) {
            logger.error("", var14);
         }
      }

      HashMap resultMap = new HashMap();
      resultMap.put("captureUrl", captureUrl);
      resultMap.put("thumbUrl", thumbUrl);
      resultMap.put("scheduleName", scheduleName);
      resultMap.put("contentName", contentName);
      return resultMap;
   }

   public static HashMap getRmsStatusInfos(String contextPath, DeviceMonitoring monitoring, boolean getResizedImg) {
      if (alwaysFullSize != null && alwaysFullSize) {
         getResizedImg = false;
      }

      String captureUrl = null;
      String thumbUrl = null;
      String scheduleName = null;
      String contentName = null;
      String thumbFileName = null;
      boolean monitoringStatus = isConnected(monitoring.getDevice_id());
      CurrentPlayingEntity playingEntity = motMgr.getPlayingContent(monitoring.getDevice_id());
      int basicSource = 32;
      if (playingEntity == null) {
         monitoringStatus = false;
      } else {
         basicSource = playingEntity.getInputSource();
         if (monitoringStatus) {
            if (playingEntity.getPanelStatus() == 1L) {
               thumbFileName = "pannel-off";
            } else {
               thumbFileName = "device_on";
            }
         }
      }

      switch(basicSource) {
      case 4:
         scheduleName = "S-Video";
         contentName = "";
         thumbFileName = "device_s_video" + (monitoringStatus ? "_on" : "") + "";
         break;
      case 8:
         scheduleName = "Component";
         contentName = "";
         thumbFileName = "device_component" + (monitoringStatus ? "_on" : "") + "";
         break;
      case 12:
         scheduleName = "AV";
         contentName = "";
         thumbFileName = "device_av" + (monitoringStatus ? "_on" : "") + "";
         break;
      case 20:
         scheduleName = "PC";
         contentName = "";
         thumbFileName = "device_pc" + (monitoringStatus ? "_on" : "") + "";
         break;
      case 24:
      case 31:
         scheduleName = "DVI";
         contentName = "";
         thumbFileName = "device_dvi" + (monitoringStatus ? "_on" : "") + "";
         break;
      case 30:
         scheduleName = "BNC";
         contentName = "";
         thumbFileName = "device_bnc" + (monitoringStatus ? "_on" : "") + "";
         break;
      case 33:
      case 34:
         scheduleName = "HDMI1";
         contentName = "";
         thumbFileName = "device_hdmi1" + (monitoringStatus ? "_on" : "") + "";
         break;
      case 35:
      case 36:
         scheduleName = "HDMI2";
         contentName = "";
         thumbFileName = "device_hdmi2" + (monitoringStatus ? "_on" : "") + "";
         break;
      case 37:
         scheduleName = "Display Port";
         contentName = "";
         thumbFileName = "device_display_port" + (monitoringStatus ? "_on" : "") + "";
         break;
      case 48:
      case 64:
         if (playingEntity.getDirectChannel() != null) {
            String[] arrCh = playingEntity.getDirectChannel().split(";");
            if (arrCh.length == 6) {
               contentName = arrCh[3] + "-" + arrCh[5];
            } else if (arrCh.length == 5) {
               contentName = arrCh[3];
            }
         }

         scheduleName = "TV";
         thumbFileName = "device_tv" + (monitoringStatus ? "_on" : "") + "";
         break;
      case 1000:
         scheduleName = "Network Standby";
         contentName = "";
         thumbFileName = "device_network_standby";
         break;
      default:
         if (!monitoringStatus) {
            thumbFileName = "thumb_img_power";
         } else {
            playingEntity = getPlayingContent(monitoring.getDevice_id());
            if (playingEntity != null) {
               List playingContentList = playingEntity.getContentLists();

               for(int j = 0; playingContentList != null && j < playingContentList.size(); ++j) {
                  ContentList playingContent = (ContentList)playingContentList.get(j);
                  if (playingContent.getFrameIndex() != null && playingContent.getFrameIndex().equals("0") && playingContent.getContentName() != null && !playingContent.getContentName().equals("") || playingContent.getFrameIndex() != null && playingContent.isMainFrame()) {
                     monitoring.setThumb_file_id(StrUtils.nvl(playingContent.getThumbnailFileId()));
                     monitoring.setThumb_file_name(StrUtils.nvl(playingContent.getThumbnailFileName()));
                     monitoring.setContent_id(StrUtils.nvl(playingContent.getContentId()));
                     monitoring.setContent_name(StrUtils.nvl(playingContent.getContentName()));
                     monitoring.setProgram_name(playingEntity.getProgramName());
                     break;
                  }
               }
            }

            if (!StrUtils.nvl(monitoring.getThumb_file_id()).equals("") && !StrUtils.nvl(monitoring.getThumb_file_name()).equals("")) {
               Date now = new Date();
               thumbUrl = contextPath + "/servlet/ContentThumbnail?thumb_id=" + monitoring.getThumb_file_id() + "&thumb_filename=" + monitoring.getThumb_file_name() + (getResizedImg ? "_MEDIUM.png" : "") + "&" + now.getTime();
               thumbFileName = "";
            }
         }

         scheduleName = StrUtils.nvl(monitoring.getProgram_name());
         contentName = StrUtils.nvl(monitoring.getContent_name());
      }

      if (monitoring.getDevice_type().equals("FLIP")) {
         thumbFileName = monitoringStatus ? "device_flip_on" : "thumb_img_power";
      } else if (monitoringStatus) {
         ScreenCaptureDAO scDao = new ScreenCaptureDAO();

         try {
            ScreenCaptureEntity scInfo = scDao.selInfoById(monitoring.getDevice_id());
            if (scInfo != null) {
               String[] fileName = scInfo.getFile_name().split("\\.");
               Date now = new Date();
               captureUrl = contextPath + "/servlet/ContentThumbnail?content_id=undefined&page_no=CAPTURE&thumb_filename=" + fileName[0] + "." + fileName[1] + (getResizedImg ? "&width=200&height=112" : "") + "&" + now.getTime();
            }
         } catch (SQLException var15) {
            logger.error("", var15);
         }
      }

      HashMap resultMap = new HashMap();
      resultMap.put("captureUrl", captureUrl);
      resultMap.put("thumbFileName", thumbFileName);
      resultMap.put("thumbUrl", thumbUrl);
      resultMap.put("scheduleName", scheduleName);
      resultMap.put("contentName", contentName);
      return resultMap;
   }

   public static List getDeviceGroupList(List list, String userId, Long groupId, long pGroupId) throws SQLException {
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      List childList = groupDao.getGroupById("PREMIUM", "DEVICE", groupId, false);
      Iterator var7 = childList.iterator();

      while(var7.hasNext()) {
         DeviceGroup deviceGroup = (DeviceGroup)var7.next();
         if (groupDao.checkChildPermissions2(userId, deviceGroup.getGroup_id()) == 0) {
            getDeviceGroupList(list, userId, deviceGroup.getGroup_id(), pGroupId);
         } else {
            deviceGroup.setP_group_id(pGroupId);
            list.add(deviceGroup);
         }
      }

      return list;
   }

   public static boolean checkLicenseCountToMove(String deviceId, long groupId, long index) throws Exception {
      DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
      int orgId = deviceGroupDao.getDeviceOrgGroupId((int)groupId);
      long groupIdOfDevice = deviceGroupDao.getGroupByDeviceId(deviceId).getGroup_id();
      long orgIdOfDevice = (long)deviceGroupDao.getDeviceOrgGroupId((int)groupIdOfDevice);
      String deviceType = getDeviceType(deviceId);
      if ((long)orgId == orgIdOfDevice) {
         return true;
      } else {
         SlmLicenseManager licenseMgr = SlmLicenseManagerImpl.getInstance();
         if (orgIdOfDevice != 999999L && !licenseMgr.isOrgSetLicenseNum(deviceType, (long)orgId) && !licenseMgr.isOrgSetLicenseNum(deviceType, orgIdOfDevice)) {
            return true;
         } else {
            return licenseMgr.getMaximumNumMovable(getDeviceType(deviceId), (long)orgId) >= index;
         }
      }
   }

   public static String checkGroupStatusToMove(String deviceIds, String selDeviceId, Long groupId) throws SQLException {
      try {
         DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
         DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
         boolean isLowerPriorityDevice = false;
         boolean isLowerPriorityDeviceTmp = false;
         boolean isRedundancyGroup = deviceGroupDao.isRedundancyGroup(groupId.intValue());
         boolean isSyncPlayGroup = deviceGroupDao.isSyncPlayGroup(groupId.intValue());
         boolean isVwtDevice = false;
         boolean confirmMinusPriority = true;
         boolean confirmMinusPriorityTmp = true;
         if (selDeviceId != null && !selDeviceId.equals("")) {
            isLowerPriorityDevice = comparePriority(groupId, selDeviceId);
         } else {
            String[] chkInfo = null;
            if (deviceIds != null) {
               chkInfo = deviceIds.split(";");
            }

            for(int i = 0; i < chkInfo.length; ++i) {
               isLowerPriorityDeviceTmp = comparePriority(groupId, chkInfo[i]);
               confirmMinusPriorityTmp = checkMinusPriority(groupId.toString(), chkInfo[i]);
               if (isLowerPriorityDeviceTmp) {
                  isLowerPriorityDevice = isLowerPriorityDeviceTmp;
               }

               if (!confirmMinusPriorityTmp) {
                  confirmMinusPriority = confirmMinusPriorityTmp;
                  break;
               }

               Device device = deviceInfo.getDevice(chkInfo[i]);

               try {
                  if (!"".equals(deviceInfo.getIsRedundancy(Integer.parseInt(deviceInfo.getDeviceGroupIdByDeviceId(device.getDevice_id()))))) {
                     isRedundancyGroup = true;
                     break;
                  }
               } catch (Exception var16) {
               }

               if (deviceInfo.isRedundancyDevice(chkInfo[i])) {
                  isRedundancyGroup = true;
                  break;
               }

               if (device != null && device.getVwt_id() != null && !device.getVwt_id().equals("")) {
                  isVwtDevice = true;
                  break;
               }
            }
         }

         if (!confirmMinusPriority) {
            return "FAIL_UNAVAILABLE_GROUP";
         } else if (isRedundancyGroup) {
            return "FAIL_IS_REDUNDANCY_GROUP";
         } else if (isVwtDevice) {
            return "FAIL_VWT_DEVICE";
         } else if (isSyncPlayGroup) {
            return "FAIL_SYNCPLAY_GROUP";
         } else {
            return isLowerPriorityDevice ? "FAIL_LOWER_PRIRORITY_DEVICE" : "SUCCESS";
         }
      } catch (Exception var17) {
         logger.error(var17);
         return "FAIL_UNKNOWN_ERROR";
      }
   }

   public static HashMap checkGroupStatus(String type, String deviceId, Long groupId) throws Exception {
      String selDeviceId = null;
      if (deviceId != null) {
         deviceId.replaceAll(",", ";");
         if (deviceId.indexOf(";") < 0) {
            selDeviceId = deviceId;
         }
      }

      if (!checkLicenseCountToMove(deviceId, groupId, 1L)) {
         HashMap resultMap = new HashMap();
         resultMap.put("status", "failure");
         resultMap.put("reason", "FAIL_INSUFFICIENT_LICENSE");
         return resultMap;
      } else {
         String checkResult = checkGroupStatusToMove(deviceId, selDeviceId, groupId);
         HashMap resultMap;
         if (checkResult != null && (checkResult == null || !checkResult.equalsIgnoreCase("FAIL_UNAVAILABLE_GROUP"))) {
            if (checkResult.equalsIgnoreCase("FAIL_VWT_DEVICE")) {
               resultMap = new HashMap();
               resultMap.put("status", "failure");
               resultMap.put("reason", "vwllayout_device_move_fail");
               return resultMap;
            } else if (checkResult.equalsIgnoreCase("FAIL_IS_REDUNDANCY_GROUP")) {
               resultMap = new HashMap();
               resultMap.put("status", "failure");
               if (type.equals("move")) {
                  resultMap.put("reason", "redundancy_group_fail_move");
               } else if (type.equals("approve")) {
                  resultMap.put("reason", "redundancy_group_APPROVAL_FAIL");
               }

               return resultMap;
            } else if (checkResult.equalsIgnoreCase("FAIL_SYNCPLAY_GROUP")) {
               resultMap = new HashMap();
               resultMap.put("status", "failure");
               resultMap.put("reason", "syncplay_group_approval_fail");
               return resultMap;
            } else if (checkResult.equalsIgnoreCase("FAIL_LOWER_PRIRORITY_DEVICE")) {
               resultMap = new HashMap();
               resultMap.put("status", "check");
               resultMap.put("reason", "init_schedule");
               return resultMap;
            } else {
               DeviceGroupDao devGroupDao = new DeviceGroupDao();
               boolean isVwlGroup = devGroupDao.getBooleanVwlGroupId(groupId);
               HashMap resultMap;
               if (isVwlGroup) {
                  resultMap = new HashMap();
                  resultMap.put("status", "failure");
                  if (type.equals("move")) {
                     resultMap.put("reason", "vwllayout_group_move_fail");
                  } else if (type.equals("approve")) {
                     resultMap.put("reason", "vwllayout_group_APPROVAL_FAIL");
                  }

                  return resultMap;
               } else {
                  resultMap = new HashMap();
                  resultMap.put("status", "success");
                  return resultMap;
               }
            }
         } else {
            resultMap = new HashMap();
            resultMap.put("status", "failure");
            resultMap.put("reason", "FAIL_UNAVAILABLE_GROUP");
            return resultMap;
         }
      }
   }

   public static boolean sendPostboot(String deviceId) {
      return sendPostboot(deviceId, (String)null);
   }

   public static boolean sendPostboot(String deviceId, String step) {
      try {
         Device device = deviceDao.getDeviceMinInfo(deviceId);
         HashMap params = new HashMap();
         if (device != null) {
            params.put("device", device);
            if (null != step) {
               params.put("step", step);
            }

            params.put("application_version", device.getApplication_version());
            ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
            dispatcher.startService("POST_BOOTSTRAP_SERVICE", params);
            return true;
         } else {
            logger.error("[DeviceUtils][sendPostboot] The device is not existed in device table. : " + deviceId);
            return false;
         }
      } catch (Exception var5) {
         logger.error("[DeviceUtils][sendPostboot][1] Failed to enqueue for postboot : " + deviceId + " " + var5);
         return false;
      }
   }

   public static boolean checkPcMode(long inputSource) {
      return inputSource == 20L || inputSource == 30L || inputSource == 24L || inputSource == 32L || inputSource == 34L || inputSource == 36L || inputSource == 37L || inputSource == 80L;
   }

   public static String getInputSource(String inputsource) {
      byte var2 = -1;
      switch(inputsource.hashCode()) {
      case 52:
         if (inputsource.equals("4")) {
            var2 = 4;
         }
         break;
      case 56:
         if (inputsource.equals("8")) {
            var2 = 5;
         }
         break;
      case 1569:
         if (inputsource.equals("12")) {
            var2 = 3;
         }
         break;
      case 1570:
         if (inputsource.equals("13")) {
            var2 = 21;
         }
         break;
      case 1571:
         if (inputsource.equals("14")) {
            var2 = 22;
         }
         break;
      case 1598:
         if (inputsource.equals("20")) {
            var2 = 0;
         }
         break;
      case 1602:
         if (inputsource.equals("24")) {
            var2 = 2;
         }
         break;
      case 1629:
         if (inputsource.equals("30")) {
            var2 = 1;
         }
         break;
      case 1630:
         if (inputsource.equals("31")) {
            var2 = 20;
         }
         break;
      case 1631:
         if (inputsource.equals("32")) {
            var2 = 6;
         }
         break;
      case 1632:
         if (inputsource.equals("33")) {
            var2 = 9;
         }
         break;
      case 1633:
         if (inputsource.equals("34")) {
            var2 = 13;
         }
         break;
      case 1634:
         if (inputsource.equals("35")) {
            var2 = 10;
         }
         break;
      case 1635:
         if (inputsource.equals("36")) {
            var2 = 14;
         }
         break;
      case 1636:
         if (inputsource.equals("37")) {
            var2 = 17;
         }
         break;
      case 1637:
         if (inputsource.equals("38")) {
            var2 = 18;
         }
         break;
      case 1668:
         if (inputsource.equals("48")) {
            var2 = 25;
         }
         break;
      case 1669:
         if (inputsource.equals("49")) {
            var2 = 11;
         }
         break;
      case 1691:
         if (inputsource.equals("50")) {
            var2 = 15;
         }
         break;
      case 1692:
         if (inputsource.equals("51")) {
            var2 = 12;
         }
         break;
      case 1693:
         if (inputsource.equals("52")) {
            var2 = 16;
         }
         break;
      case 1726:
         if (inputsource.equals("64")) {
            var2 = 19;
         }
         break;
      case 1784:
         if (inputsource.equals("80")) {
            var2 = 8;
         }
         break;
      case 1789:
         if (inputsource.equals("85")) {
            var2 = 23;
         }
         break;
      case 1821:
         if (inputsource.equals("96")) {
            var2 = 7;
         }
         break;
      case 1822:
         if (inputsource.equals("97")) {
            var2 = 24;
         }
         break;
      case 1824:
         if (inputsource.equals("99")) {
            var2 = 28;
         }
         break;
      case 48626:
         if (inputsource.equals("101")) {
            var2 = 26;
         }
         break;
      case 48627:
         if (inputsource.equals("102")) {
            var2 = 27;
         }
         break;
      case 1507423:
         if (inputsource.equals("1000")) {
            var2 = 29;
         }
      }

      switch(var2) {
      case 0:
         return "PC";
      case 1:
         return "BNC";
      case 2:
         return "DVI";
      case 3:
         return "AV";
      case 4:
         return "S-Video";
      case 5:
         return "Component";
      case 6:
         return "MagicInfo";
      case 7:
         return "MagicInfo-Lite/S";
      case 8:
         return "PlugInModule";
      case 9:
         return "HDMI1";
      case 10:
         return "HDMI2";
      case 11:
         return "HDMI3";
      case 12:
         return "HDMI4";
      case 13:
         return "HDMI1_PC";
      case 14:
         return "HDMI2_PC";
      case 15:
         return "HDMI3_PC";
      case 16:
         return "HDMI4_PC";
      case 17:
         return "DisplayPort";
      case 18:
         return "DisplayPort2";
      case 19:
         return "DTV";
      case 20:
         return "DVI_VIDEO";
      case 21:
         return "AV2";
      case 22:
         return "Ext";
      case 23:
         return "HDBaseT";
      case 24:
         return "WiFi";
      case 25:
         return "ATV";
      case 26:
         return "WebBrowser";
      case 27:
         return "SamsungWorkspace";
      case 28:
         return "URLLauncher";
      case 29:
         return "PanelOff";
      default:
         return "N/A(" + inputsource + ")";
      }
   }

   public static String convertInputSource(String codeOrName) {
      if (codeOrName == null) {
         return "";
      } else {
         byte var2 = -1;
         switch(codeOrName.hashCode()) {
         case -1732246028:
            if (codeOrName.equals("DISPLAY_PORT2")) {
               var2 = 48;
            }
            break;
         case -1523257200:
            if (codeOrName.equals("URL_LAUNCHER")) {
               var2 = 58;
            }
            break;
         case -872551335:
            if (codeOrName.equals("HDMI1_PC")) {
               var2 = 43;
            }
            break;
         case -872521544:
            if (codeOrName.equals("HDMI2_PC")) {
               var2 = 44;
            }
            break;
         case -872491753:
            if (codeOrName.equals("HDMI3_PC")) {
               var2 = 45;
            }
            break;
         case -872461962:
            if (codeOrName.equals("HDMI4_PC")) {
               var2 = 46;
            }
            break;
         case -332973570:
            if (codeOrName.equals("DISPLAY_PORT")) {
               var2 = 47;
            }
            break;
         case -198252672:
            if (codeOrName.equals("MAGICINFO-LITE")) {
               var2 = 37;
            }
            break;
         case 52:
            if (codeOrName.equals("4")) {
               var2 = 4;
            }
            break;
         case 56:
            if (codeOrName.equals("8")) {
               var2 = 5;
            }
            break;
         case 1569:
            if (codeOrName.equals("12")) {
               var2 = 3;
            }
            break;
         case 1570:
            if (codeOrName.equals("13")) {
               var2 = 21;
            }
            break;
         case 1571:
            if (codeOrName.equals("14")) {
               var2 = 22;
            }
            break;
         case 1598:
            if (codeOrName.equals("20")) {
               var2 = 0;
            }
            break;
         case 1602:
            if (codeOrName.equals("24")) {
               var2 = 2;
            }
            break;
         case 1629:
            if (codeOrName.equals("30")) {
               var2 = 1;
            }
            break;
         case 1630:
            if (codeOrName.equals("31")) {
               var2 = 20;
            }
            break;
         case 1631:
            if (codeOrName.equals("32")) {
               var2 = 6;
            }
            break;
         case 1632:
            if (codeOrName.equals("33")) {
               var2 = 9;
            }
            break;
         case 1633:
            if (codeOrName.equals("34")) {
               var2 = 13;
            }
            break;
         case 1634:
            if (codeOrName.equals("35")) {
               var2 = 10;
            }
            break;
         case 1635:
            if (codeOrName.equals("36")) {
               var2 = 14;
            }
            break;
         case 1636:
            if (codeOrName.equals("37")) {
               var2 = 17;
            }
            break;
         case 1637:
            if (codeOrName.equals("38")) {
               var2 = 18;
            }
            break;
         case 1668:
            if (codeOrName.equals("48")) {
               var2 = 25;
            }
            break;
         case 1669:
            if (codeOrName.equals("49")) {
               var2 = 11;
            }
            break;
         case 1691:
            if (codeOrName.equals("50")) {
               var2 = 15;
            }
            break;
         case 1692:
            if (codeOrName.equals("51")) {
               var2 = 12;
            }
            break;
         case 1693:
            if (codeOrName.equals("52")) {
               var2 = 16;
            }
            break;
         case 1726:
            if (codeOrName.equals("64")) {
               var2 = 19;
            }
            break;
         case 1784:
            if (codeOrName.equals("80")) {
               var2 = 8;
            }
            break;
         case 1789:
            if (codeOrName.equals("85")) {
               var2 = 23;
            }
            break;
         case 1821:
            if (codeOrName.equals("96")) {
               var2 = 7;
            }
            break;
         case 1822:
            if (codeOrName.equals("97")) {
               var2 = 24;
            }
            break;
         case 1824:
            if (codeOrName.equals("99")) {
               var2 = 28;
            }
            break;
         case 2101:
            if (codeOrName.equals("AV")) {
               var2 = 33;
            }
            break;
         case 2547:
            if (codeOrName.equals("PC")) {
               var2 = 30;
            }
            break;
         case 48626:
            if (codeOrName.equals("101")) {
               var2 = 26;
            }
            break;
         case 48627:
            if (codeOrName.equals("102")) {
               var2 = 27;
            }
            break;
         case 65155:
            if (codeOrName.equals("ATV")) {
               var2 = 55;
            }
            break;
         case 65181:
            if (codeOrName.equals("AV2")) {
               var2 = 51;
            }
            break;
         case 65911:
            if (codeOrName.equals("BNC")) {
               var2 = 31;
            }
            break;
         case 68038:
            if (codeOrName.equals("DTV")) {
               var2 = 49;
            }
            break;
         case 68087:
            if (codeOrName.equals("DVI")) {
               var2 = 32;
            }
            break;
         case 69121:
            if (codeOrName.equals("EXT")) {
               var2 = 52;
            }
            break;
         case 1507423:
            if (codeOrName.equals("1000")) {
               var2 = 29;
            }
            break;
         case 2664213:
            if (codeOrName.equals("WIFI")) {
               var2 = 54;
            }
            break;
         case 68595609:
            if (codeOrName.equals("HDMI1")) {
               var2 = 39;
            }
            break;
         case 68595610:
            if (codeOrName.equals("HDMI2")) {
               var2 = 40;
            }
            break;
         case 68595611:
            if (codeOrName.equals("HDMI3")) {
               var2 = 41;
            }
            break;
         case 68595612:
            if (codeOrName.equals("HDMI4")) {
               var2 = 42;
            }
            break;
         case 74707987:
            if (codeOrName.equals("DVI_VIDEO")) {
               var2 = 50;
            }
            break;
         case 327400016:
            if (codeOrName.equals("SAMSUNG_WORKSPACE")) {
               var2 = 57;
            }
            break;
         case 426427640:
            if (codeOrName.equals("PLUGIN_MODULE")) {
               var2 = 38;
            }
            break;
         case 620789691:
            if (codeOrName.equals("MAGICINFO")) {
               var2 = 36;
            }
            break;
         case 1305403508:
            if (codeOrName.equals("PANEL_OFF")) {
               var2 = 59;
            }
            break;
         case 1386687709:
            if (codeOrName.equals("COMPONENT")) {
               var2 = 35;
            }
            break;
         case 1624881929:
            if (codeOrName.equals("HD_BASE_T")) {
               var2 = 53;
            }
            break;
         case 1803761917:
            if (codeOrName.equals("WEB_BROWSER")) {
               var2 = 56;
            }
            break;
         case 2018338401:
            if (codeOrName.equals("S-VIDEO")) {
               var2 = 34;
            }
         }

         switch(var2) {
         case 0:
            return "PC";
         case 1:
            return "BNC";
         case 2:
            return "DVI";
         case 3:
            return "AV";
         case 4:
            return "S-VIDEO";
         case 5:
            return "COMPONENT";
         case 6:
            return "MAGICINFO";
         case 7:
            return "MAGICINFO-LITE";
         case 8:
            return "PLUGIN_MODULE";
         case 9:
            return "HDMI1";
         case 10:
            return "HDMI2";
         case 11:
            return "HDMI3";
         case 12:
            return "HDMI4";
         case 13:
            return "HDMI1_PC";
         case 14:
            return "HDMI2_PC";
         case 15:
            return "HDMI3_PC";
         case 16:
            return "HDMI4_PC";
         case 17:
            return "DISPLAY_PORT";
         case 18:
            return "DISPLAY_PORT2";
         case 19:
            return "DTV";
         case 20:
            return "DVI_VIDEO";
         case 21:
            return "AV2";
         case 22:
            return "EXT";
         case 23:
            return "HD_BASE_T";
         case 24:
            return "WIFI";
         case 25:
            return "ATV";
         case 26:
            return "WEB_BROWSER";
         case 27:
            return "SAMSUNG_WORKSPACE";
         case 28:
            return "URL_LAUNCHER";
         case 29:
            return "PANEL_OFF";
         case 30:
            return "20";
         case 31:
            return "30";
         case 32:
            return "24";
         case 33:
            return "12";
         case 34:
            return "4";
         case 35:
            return "8";
         case 36:
            return "32";
         case 37:
            return "96";
         case 38:
            return "80";
         case 39:
            return "33";
         case 40:
            return "35";
         case 41:
            return "49";
         case 42:
            return "51";
         case 43:
            return "34";
         case 44:
            return "36";
         case 45:
            return "50";
         case 46:
            return "52";
         case 47:
            return "37";
         case 48:
            return "38";
         case 49:
            return "64";
         case 50:
            return "31";
         case 51:
            return "13";
         case 52:
            return "14";
         case 53:
            return "85";
         case 54:
            return "97";
         case 55:
            return "48";
         case 56:
            return "101";
         case 57:
            return "102";
         case 58:
            return "99";
         case 59:
            return "1000";
         default:
            return "";
         }
      }
   }

   public static Boolean isSupportNOC() {
      try {
         if (CommonConfig.get("thingworx.update.ui") != null && CommonConfig.get("thingworx.update.enable") != null && Boolean.parseBoolean(CommonConfig.get("thingworx.update.ui")) && Boolean.parseBoolean(CommonConfig.get("thingworx.update.enable"))) {
            return true;
         }
      } catch (ConfigException var1) {
         return false;
      }

      return false;
   }

   public static String countLedCabinet(String parentDeviceId) {
      String cabinetCnt = "";

      try {
         List groupList = ledCabinetMgr.getLedCabinetGroupIds(parentDeviceId);
         if (groupList != null && groupList.size() > 0) {
            for(int i = 0; i < groupList.size(); ++i) {
               Long groupId = (Long)groupList.get(i);
               int count = ledCabinetMgr.getLedCabinetCountByGroup(parentDeviceId, groupId);
               if (cabinetCnt != null && cabinetCnt.length() > 0) {
                  cabinetCnt = cabinetCnt + ",";
               }

               cabinetCnt = cabinetCnt + count;
            }
         }
      } catch (SQLException var6) {
         logger.error("", var6);
      }

      return cabinetCnt;
   }

   public static List getOrgGroupIdByUserId(String userId) throws SQLException {
      UserGroupInfo userInfo = UserGroupInfoImpl.getInstance();
      DeviceGroupInfo deviceInfo = DeviceGroupInfoImpl.getInstance();
      List list = userInfo.getUserManageGroupListByUserId(userId);
      List deviceorgIds = null;
      if (list != null && list.size() > 0) {
         deviceorgIds = new ArrayList();
         Iterator var5 = list.iterator();

         while(var5.hasNext()) {
            UserGroup user = (UserGroup)var5.next();
            deviceorgIds.add(deviceInfo.getOrganGroupIdByName(user.getGroup_name()));
         }
      }

      return deviceorgIds;
   }

   public static List getDeviceGroupByUserId(String userId) throws SQLException {
      UserGroupInfo userInfo = UserGroupInfoImpl.getInstance();
      DeviceGroupInfo deviceInfo = DeviceGroupInfoImpl.getInstance();
      List list = userInfo.getUserManageGroupListByUserId(userId);
      List userGroup = null;
      if (list != null && list.size() > 0) {
         userGroup = new ArrayList();
         Iterator var5 = list.iterator();

         while(var5.hasNext()) {
            UserGroup user = (UserGroup)var5.next();
            DeviceGroup group = new DeviceGroup();
            group.setGroup_id(deviceInfo.getOrganGroupIdByName(user.getGroup_name()));
         }
      }

      return userGroup;
   }

   public static List getGroupIdsByOrgManagerUserId(Long groupId, String userId) throws SQLException {
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      UserInfo userDao = UserInfoImpl.getInstance();
      String orgName;
      if (groupId == 0L) {
         List deviceOrgIds = getOrgGroupIdByUserId(userId);
         if (deviceOrgIds != null && deviceOrgIds.size() > 0) {
            List groupList = groupDao.getAllDeviceGroups(deviceOrgIds);
            if (CollectionUtils.isNotEmpty(groupList)) {
               List groupIds = new ArrayList();
               Iterator var14 = groupList.iterator();

               while(var14.hasNext()) {
                  DeviceGroup deviceGroup = (DeviceGroup)var14.next();
                  groupIds.add(deviceGroup.getGroup_id());
               }

               return groupIds;
            }
         } else {
            orgName = SecurityUtils.getLoginUser().getOrganization();
            String deviceOrgId = String.valueOf(deviceGroupDao.getOrganGroupIdByName(orgName));
            List groupList = groupDao.getChildGroupList(Integer.valueOf(deviceOrgId), true);
            if (groupList != null && groupList.size() > 0) {
               List groupIdList = new ArrayList();

               for(int i = 0; i < groupList.size(); ++i) {
                  groupIdList.add(((DeviceGroup)groupList.get(i)).getGroup_id());
               }

               return groupIdList;
            }
         }
      } else {
         String orgName = SecurityUtils.getLoginUser().getOrganization();
         orgName = String.valueOf(deviceGroupDao.getOrganGroupIdByName(orgName));
         List groupList = groupDao.getChildGroupList(Integer.valueOf(orgName), true);
         if (groupList != null && groupList.size() > 0) {
            List groupIdList = new ArrayList();

            for(int i = 0; i < groupList.size(); ++i) {
               groupIdList.add(((DeviceGroup)groupList.get(i)).getGroup_id());
            }

            return groupIdList;
         }
      }

      return null;
   }

   public static List getOrgByOrgManagerUserId(Long groupId, String userId) throws SQLException {
      List organizationList = null;
      if (groupId == 0L) {
         UserGroupInfo userInfo = UserGroupInfoImpl.getInstance();
         List list = userInfo.getMngGroupListByUserId(userId);
         if (list != null && list.size() > 0) {
            organizationList = new ArrayList();
            Iterator var5 = list.iterator();

            while(var5.hasNext()) {
               UserGroup group = (UserGroup)var5.next();
               if (!StringUtils.isEmpty(group.getGroup_name())) {
                  organizationList.add(group.getGroup_name());
               }
            }
         }
      }

      return organizationList;
   }

   public static Boolean isSupportPlayingSchedule(String deviceType, Float deviceTypeVersion) {
      byte var3 = -1;
      switch(deviceType.hashCode()) {
      case -392637480:
         if (deviceType.equals("RIPLAYER")) {
            var3 = 3;
         }
         break;
      case -312192656:
         if (deviceType.equals("RSIGNAGE")) {
            var3 = 5;
         }
         break;
      case -107535262:
         if (deviceType.equals("RSPLAYER")) {
            var3 = 2;
         }
         break;
      case 81272:
         if (deviceType.equals("RMS")) {
            var3 = 0;
         }
         break;
      case 2160749:
         if (deviceType.equals("FLIP")) {
            var3 = 1;
         }
         break;
      case 2002487986:
         if (deviceType.equals("RLEDBOX")) {
            var3 = 4;
         }
      }

      switch(var3) {
      case 0:
      case 1:
      case 2:
      case 3:
      case 4:
      case 5:
         return false;
      default:
         return true;
      }
   }

   public static int setPreconfigGroupMap(String preconfigId, String groupId) {
      int result = -1;

      try {
         if (preconfigId != null) {
            DevicePreconfigInfo preconfigDao = DevicePreconfigInfoImpl.getInstance();
            result = preconfigDao.addPreconfigGroupMappingList(preconfigId, groupId);
         }
      } catch (Exception var4) {
         logger.error("[DeviceUtils:setPreconfigGroupMap] fail, " + var4.getMessage());
      }

      return result;
   }

   public static int deployPreconfig(String preconfigId, String groupIds) {
      byte result = -1;

      try {
         DevicePreconfigInfo preconfigInfo = DevicePreconfigInfoImpl.getInstance();
         DeviceGroupInfo groupInfo = DeviceGroupInfoImpl.getInstance();
         if (groupIds != null && groupIds.length() > 0) {
            String[] groupArray = groupIds.split(",");
            String[] var6 = groupArray;
            int var7 = groupArray.length;

            for(int var8 = 0; var8 < var7; ++var8) {
               String groupId = var6[var8];
               List deviceList = groupInfo.getChildDeviceList((int)Long.parseLong(groupId.trim()));
               if (deviceList != null && deviceList.size() > 0) {
                  Iterator var11 = deviceList.iterator();

                  while(var11.hasNext()) {
                     Device device = (Device)var11.next();

                     try {
                        int ret = false;
                        if (isConnected(device.getDevice_id())) {
                           int ret = preconfigInfo.deployToDevice(device);
                           logger.info("Deploy Preconfig ret : " + ret + ", device : " + device.getDevice_id());
                        } else {
                           logger.info("Deploy Preconfig Failed : Device is not connected. " + device.getDevice_id());
                        }
                     } catch (Exception var14) {
                        logger.error("Failed to send preconfig. " + device.getDevice_id(), var14);
                     }
                  }
               }
            }
         }
      } catch (Exception var15) {
         logger.error("[DeviceUtils:setPreconfigGroupMap] fail, " + var15.getMessage());
      }

      return result;
   }

   public static boolean checkDeviceAuthorization(String authorization, String deviceId) {
      if (authorization == null) {
         return false;
      } else {
         boolean ret = false;

         try {
            if (authorization.startsWith("Basic")) {
               authorization = authorization.split(" ")[1];
            }

            deviceId = deviceId.replace(":", "-");
            byte[] decoded = Base64.decodeBase64(authorization);
            String[] authstr = (new String(decoded)).split(":");
            if (authstr == null || authstr.length < 2 || !deviceId.equals(authstr[0])) {
               return false;
            }

            ret = checkDeviceAuthorizationUtil(authstr[0], authstr[1]);
            if (!ret) {
               logger.info("Authorization Failed : " + authstr[0] + ", " + authstr[1]);
            }
         } catch (ArrayIndexOutOfBoundsException var5) {
            logger.error("", var5);
            ret = false;
         }

         return ret;
      }
   }

   public static boolean checkDeviceAuthorizationUtil(String id, String pw) {
      try {
         DownloadInfo downloadInfo = DownloadInfoImpl.getInstance();
         String userpw = downloadInfo.getPassword(id);
         return SecurityUtils.matchPassword(pw, userpw);
      } catch (Exception var4) {
         logger.error("Device Authorization Failed. " + var4.getMessage());
         return false;
      }
   }

   public static Float getMaxDeviceVersion(String deviceType) {
      try {
         if (deviceTypeAndVersion == null) {
            List list = deviceDao.getMaxDeviceTypeVersion();
            if (list != null && list.size() > 0) {
               deviceTypeAndVersion = new HashMap();

               for(int i = 0; i < list.size(); ++i) {
                  Map tmp = (Map)list.get(i);
                  deviceTypeAndVersion.put(tmp.get("device_type").toString(), Float.valueOf(tmp.get("max_device_version").toString()));
               }
            }
         }

         return (Float)deviceTypeAndVersion.get(deviceType);
      } catch (SQLException var4) {
         return 1.0F;
      }
   }

   public static DeviceControl getDeviceControlInfo(String deviceId, Boolean cache) {
      try {
         DeviceControl control = new DeviceControl();
         String productType = "PREMIUM";

         try {
            DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
            DeviceGeneralConf general = deviceDao.getDeviceGeneralConf(deviceId, cache);
            control.setGeneral(general);
         } catch (Exception var9) {
            control.setGeneral((DeviceGeneralConf)null);
         }

         try {
            DeviceSystemSetupConfManager deviceConf = DeviceSystemSetupConfManagerImpl.getInstance(productType);
            DeviceSystemSetupConf systemSetup = deviceConf.getDeviceSystemSetupConf(deviceId, cache);
            control.setSetup(systemSetup);
         } catch (Exception var8) {
            control.setSetup((DeviceSystemSetupConf)null);
         }

         try {
            DeviceDisplayConfManager displayConfDao = DeviceDisplayConfManagerImpl.getInstance();
            DeviceDisplayConf display = displayConfDao.getDeviceDisplayConf(deviceId, cache);
            control.setDisplay(display);
         } catch (Exception var7) {
            control.setDisplay((DeviceDisplayConf)null);
         }

         try {
            DeviceSecurityConfManager securityDao = DeviceSecurityConfManagerImpl.getInstance(productType);
            DeviceSecurityConf security = securityDao.getDeviceSecurityConf(deviceId, cache);
            control.setSecurity(security);
         } catch (Exception var6) {
            control.setSecurity((DeviceSecurityConf)null);
         }

         control.setTargetId(deviceId);
         return control;
      } catch (Exception var10) {
         logger.error(var10);
         return null;
      }
   }

   public static String getDeviceTypeByPriority(Long minPriority, String groupType) {
      if (minPriority == -1L) {
         return "APLAYER";
      } else if (minPriority == 1L) {
         return "LPLAYER";
      } else if (minPriority == 5L) {
         return "SPLAYER";
      } else if (minPriority == 10L) {
         return "S2PLAYER";
      } else if (minPriority == 20L) {
         return "SIGNAGE".equals(groupType) ? "SIGNAGE" : "S3PLAYER";
      } else if (minPriority == 50L) {
         if ("LEDBOX".equals(groupType)) {
            return "LEDBOX";
         } else {
            return "SIGNAGE".equals(groupType) ? "SIGNAGE" : "S4PLAYER";
         }
      } else if (minPriority == 60L) {
         return "S5PLAYER";
      } else if (minPriority == 70L) {
         return "S6PLAYER";
      } else if (minPriority == 110L) {
         return "iPLAYER";
      } else if (minPriority == 1000L) {
         return "SPLAYER";
      } else if (minPriority == 2000L) {
         return "SPLAYER";
      } else if (minPriority == -100L) {
         return "RS4PLAYER";
      } else if (minPriority == -98L) {
         return "RS5PLAYER";
      } else if (minPriority == -96L) {
         return "RS6PLAYER";
      } else if (minPriority == -3L) {
         return "RIPLAYER";
      } else if (minPriority == -50L) {
         return "FLIP";
      } else if (minPriority == -48L) {
         return "FLIP2";
      } else if (minPriority == -46L) {
         return "FLIP3";
      } else {
         return minPriority == -44L ? "FLIP4" : "default";
      }
   }

   public static Object convertBeautifiedString(String key, Object value) {
      if (value == null) {
         return null;
      } else {
         String ret = null;
         byte var4 = -1;
         switch(key.hashCode()) {
         case -2124761348:
            if (key.equals("misc_all_lock")) {
               var4 = 23;
            }
            break;
         case -2117263028:
            if (key.equals("basic_source")) {
               var4 = 8;
            }
            break;
         case -2031640501:
            if (key.equals("basic_volume")) {
               var4 = 36;
            }
            break;
         case -1947598036:
            if (key.equals("mnt_safety_lock")) {
               var4 = 21;
            }
            break;
         case -1825555527:
            if (key.equals("misc_panel_lock")) {
               var4 = 22;
            }
            break;
         case -1777765959:
            if (key.equals("custom_logo")) {
               var4 = 10;
            }
            break;
         case -1745725632:
            if (key.equals("pv_filmmode")) {
               var4 = 16;
            }
            break;
         case -1401174344:
            if (key.equals("diagnosis_alarm_temperature")) {
               var4 = 19;
            }
            break;
         case -1327851916:
            if (key.equals("misc_block_network_connection")) {
               var4 = 33;
            }
            break;
         case -1124162678:
            if (key.equals("basic_mute")) {
               var4 = 31;
            }
            break;
         case -1025994506:
            if (key.equals("misc_server_network_setting")) {
               var4 = 26;
            }
            break;
         case -952142899:
            if (key.equals("touch_control_lock")) {
               var4 = 24;
            }
            break;
         case -873668212:
            if (key.equals("timer1")) {
               var4 = 0;
            }
            break;
         case -873668211:
            if (key.equals("timer2")) {
               var4 = 1;
            }
            break;
         case -873668210:
            if (key.equals("timer3")) {
               var4 = 2;
            }
            break;
         case -873668209:
            if (key.equals("timer4")) {
               var4 = 3;
            }
            break;
         case -873668208:
            if (key.equals("timer5")) {
               var4 = 4;
            }
            break;
         case -873668207:
            if (key.equals("timer6")) {
               var4 = 5;
            }
            break;
         case -873668206:
            if (key.equals("timer7")) {
               var4 = 6;
            }
            break;
         case -832764689:
            if (key.equals("screen_monitoring_lock")) {
               var4 = 29;
            }
            break;
         case -715622925:
            if (key.equals("advanced_osd_display_type")) {
               var4 = 11;
            }
            break;
         case -597289107:
            if (key.equals("web_browser_url")) {
               var4 = 9;
            }
            break;
         case -342656301:
            if (key.equals("sound_mode")) {
               var4 = 18;
            }
            break;
         case -209297766:
            if (key.equals("pv_size")) {
               var4 = 14;
            }
            break;
         case -103861186:
            if (key.equals("advanced_fan_speed")) {
               var4 = 35;
            }
            break;
         case 205712769:
            if (key.equals("misc_block_usb_port")) {
               var4 = 32;
            }
            break;
         case 224868380:
            if (key.equals("bluetooth_lock")) {
               var4 = 27;
            }
            break;
         case 295114911:
            if (key.equals("pv_color_temperature")) {
               var4 = 13;
            }
            break;
         case 336412964:
            if (key.equals("diagnosis_panel_on_time")) {
               var4 = 7;
            }
            break;
         case 478472284:
            if (key.equals("pv_colortone")) {
               var4 = 12;
            }
            break;
         case 766857860:
            if (key.equals("capture_lock")) {
               var4 = 25;
            }
            break;
         case 786009827:
            if (key.equals("pv_digitalnr")) {
               var4 = 15;
            }
            break;
         case 924034892:
            if (key.equals("remote_control_server_lock")) {
               var4 = 30;
            }
            break;
         case 965276154:
            if (key.equals("misc_remocon")) {
               var4 = 34;
            }
            break;
         case 1223047734:
            if (key.equals("pv_hdmi_black_level")) {
               var4 = 17;
            }
            break;
         case 1400960021:
            if (key.equals("wifi_lock")) {
               var4 = 28;
            }
            break;
         case 1427374017:
            if (key.equals("diagnosis_monitor_temperature")) {
               var4 = 20;
            }
         }

         String[] temp;
         int j;
         switch(var4) {
         case 0:
         case 1:
         case 2:
         case 3:
         case 4:
         case 5:
         case 6:
            try {
               int idx = Integer.parseInt(key.substring(5));
               List timeList = (List)value;
               if (timeList.size() < idx) {
                  return null;
               }

               DeviceTimeTimerConf time = (DeviceTimeTimerConf)timeList.get(idx - 1);
               if (ret == null) {
                  ret = "";
               }

               if ("1".equals(time.getTimer_on_status())) {
                  ret = ret + "On Time : ";
                  ret = ret + time.getTimer_on_h() + ":" + String.format("%02d", Integer.valueOf(time.getTimer_on_m())) + ("1".equals(time.getTimer_on_ampm()) ? "AM" : "PM");
                  ret = ret + ", " + getDayString(time.getTimer_manual_weekday());
                  if ("1".equals(time.getTimer_holiday_enable()) || "2".equals(time.getTimer_holiday_enable())) {
                     ret = ret + ", Holiday";
                  }
               }

               if ("1".equals(time.getTimer_off_status())) {
                  if (!StringUtils.isEmpty(ret)) {
                     ret = ret + "\n";
                  }

                  ret = ret + "Off Time : ";
                  ret = ret + time.getTimer_off_h() + ":" + String.format("%02d", Integer.valueOf(time.getTimer_off_m())) + ("1".equals(time.getTimer_off_ampm()) ? "AM" : "PM");
                  ret = ret + ", " + getDayString(time.getTimer_off_manual_weekday());
                  if ("1".equals(time.getTimer_holiday_enable()) || "3".equals(time.getTimer_holiday_enable())) {
                     ret = ret + ", Holiday";
                  }
               }

               if ("1".equals(time.getTimer_on_status())) {
                  if (!StringUtils.isEmpty(ret)) {
                     ret = ret + "\n";
                  }

                  ret = ret + "Volume : " + time.getTimer_volume();
                  ret = ret + "\n";

                  int i;
                  for(i = 0; !time.getTimer_source().equals(DeviceConstants.INPUTSOURCE_CODE[i]) && i < DeviceConstants.INPUTSOURCE_CODE.length - 1; ++i) {
                  }

                  ret = ret + "Source : " + DeviceConstants.INPUTSOURCE_NAME[i];
               }
            } catch (Exception var13) {
               ret = null;
            }

            return ret;
         case 7:
            String[] panelTime = ((String)value).split(";");
            int panelOnTime = 0;
            if (panelTime.length == 2) {
               if (!panelTime[1].equals("0")) {
                  panelOnTime = Integer.valueOf(panelTime[0]) * 256 + Integer.valueOf(panelTime[1]);
               }

               if (panelOnTime != 0) {
                  panelOnTime /= 6;
               }
            }

            return panelOnTime;
         case 8:
            int i;
            for(i = 0; !String.valueOf(value).equals(DeviceConstants.INPUTSOURCE_CODE[i]) && i < DeviceConstants.INPUTSOURCE_CODE.length - 1; ++i) {
            }

            return DeviceConstants.INPUTSOURCE_NAME[i];
         case 9:
            try {
               temp = ((String)value).split(";");
               if (temp.length > 0) {
                  ret = "";

                  for(j = 0; j < temp.length; ++j) {
                     if (j == 0) {
                        ret = ret + ("0".equals(temp[j]) ? "OFF" : temp[j]);
                     } else if (j == 1) {
                        ret = ret + ", " + temp[j] + "%";
                     } else if (j == 2) {
                        ret = ret + ", " + ("0".equals(temp[j]) ? "Samsung Display" : temp[j + 1]);
                     }
                  }
               }
            } catch (Exception var12) {
               ret = null;
            }

            return ret;
         case 10:
            try {
               temp = ((String)value).split(";");
               if (temp.length > 0) {
                  ret = "";

                  for(j = 0; j < temp.length; ++j) {
                     if (j == 0) {
                        ret = ret + ("0".equals(temp[j]) ? "OFF" : ("1".equals(temp[j]) ? "Image" : "Video"));
                     } else if (j == 1) {
                        ret = ret + ", " + temp[j] + " Sec";
                     }
                  }
               }
            } catch (Exception var11) {
               ret = null;
            }

            return ret;
         case 11:
            try {
               temp = ((String)value).split(";");
               if (temp.length > 0) {
                  ret = "";

                  for(j = 0; j < temp.length; ++j) {
                     if (j == 0) {
                        ret = ret + "Source OSD : " + ("0".equals(temp[j]) ? "On" : "Off");
                     } else if (j == 1) {
                        ret = ret + "\nNot Optimimum Mode OSD : " + ("0".equals(temp[j]) ? "On" : "Off");
                     } else if (j == 2) {
                        ret = ret + "\nNo Signal OSD : " + ("0".equals(temp[j]) ? "On" : "Off");
                     } else if (j == 3) {
                        ret = ret + "\nMDC OSD : " + ("0".equals(temp[j]) ? "On" : "Off");
                     } else if (j == 4) {
                        ret = ret + "\nDownload Status Message : " + ("0".equals(temp[j]) ? "On" : "Off");
                     }
                  }
               }
            } catch (Exception var10) {
               ret = null;
            }

            return ret;
         case 12:
            if ((Long)value == 80L) {
               ret = "Off";
            } else if ((Long)value == 1L) {
               ret = "Cool";
            } else if ((Long)value == 2L) {
               ret = "Standard";
            } else if ((Long)value == 3L) {
               ret = "Warm1";
            } else if ((Long)value == 4L) {
               ret = "Warm2";
            } else if ((Long)value == 5L) {
               ret = "Natural";
            }

            return ret;
         case 13:
            switch(((Long)value).intValue()) {
            case 0:
               return "5,000K";
            case 1:
               return "6,000K";
            case 2:
               return "7,000K";
            case 3:
               return "8,000K";
            case 4:
               return "9,000K";
            case 5:
               return "10,000K";
            case 6:
               return "11,000K";
            case 7:
               return "12,000K";
            case 8:
               return "13,000K";
            case 9:
               return "14,000K";
            case 16:
               return "15,000K";
            case 35:
               return "3,500K";
            case 45:
               return "4,500K";
            case 55:
               return "5,500K";
            case 65:
               return "6,500K";
            case 75:
               return "7,500K";
            case 85:
               return "8,500K";
            case 95:
               return "9,500K";
            case 105:
               return "10,500K";
            case 115:
               return "11,500K";
            case 125:
               return "12,500K";
            case 135:
               return "13,500K";
            case 145:
               return "14,500K";
            case 155:
               return "15,500K";
            case 160:
               return "16,000K";
            case 253:
               return "2,800K";
            case 254:
               return "3,000K";
            case 255:
               return "4,000K";
            }
         case 14:
            switch(((Long)value).intValue()) {
            case 1:
               return "16:9 Standard";
            case 2:
            case 3:
            case 7:
            case 8:
            case 10:
            case 16:
            case 17:
            case 18:
            case 19:
            case 20:
            case 21:
            case 22:
            case 23:
            case 24:
            case 25:
            case 26:
            case 27:
            case 28:
            case 29:
            case 30:
            case 31:
            case 33:
            case 34:
            case 35:
            case 36:
            case 37:
            case 38:
            case 39:
            case 40:
            case 41:
            case 42:
            case 43:
            case 44:
            case 45:
            case 46:
            case 47:
            case 48:
            default:
               break;
            case 4:
               return "Zoom";
            case 5:
               return "Zoom 1";
            case 6:
               return "Zoom 2";
            case 9:
               return "Fit to screen";
            case 11:
               return "4:3";
            case 12:
               return "Wide Fit";
            case 13:
               return "Custom";
            case 14:
               return "Smart View 1";
            case 15:
               return "Smart View 2";
            case 32:
               return "Original Ratio";
            case 49:
               return "Wide Zoom";
            }
         case 15:
            switch(((Long)value).intValue()) {
            case 0:
               return "Off";
            case 1:
               return "On";
            case 2:
               return "Medium";
            case 3:
               return "High";
            case 4:
               return "Auto";
            case 5:
               return "Auto Visualization";
            }
         case 16:
            switch(((Long)value).intValue()) {
            case 0:
               return "Off";
            case 1:
               return "Auto1";
            case 2:
               return "Auto2";
            case 3:
               return "Cinema Smooth";
            }
         case 17:
            switch(((Long)value).intValue()) {
            case 0:
               return "Normal";
            case 1:
               return "Low";
            case 2:
               return "Auto";
            }
         case 18:
            switch(((Long)value).intValue()) {
            case 0:
               return "Standard";
            case 1:
               return "Music";
            case 2:
               return "Movie";
            case 3:
               return "Clear Voice";
            case 4:
               return "Custom";
            case 5:
               return "Amplify";
            }
         case 19:
         case 20:
            if ((Long)value <= 0L) {
               return null;
            }

            return value;
         case 21:
         case 22:
         case 23:
         case 24:
         case 25:
         case 26:
         case 27:
         case 28:
         case 29:
         case 30:
         case 31:
            if ((Long)value == 0L) {
               return "UnLocked";
            } else {
               if ((Long)value == 1L) {
                  return "Lock";
               }

               return null;
            }
         case 32:
         case 33:
         case 34:
            if ((Long)value == 0L) {
               return "Lock";
            } else {
               if ((Long)value == 1L) {
                  return "Unlocked";
               }

               return null;
            }
         case 35:
         case 36:
            if ((Long)value < 0L) {
               return null;
            }

            return value;
         default:
            if (value instanceof Boolean) {
               if ((Boolean)value) {
                  return "ON";
               } else {
                  return "OFF";
               }
            } else {
               return value;
            }
         }
      }
   }

   public static boolean isSupported(String key, String deviceType) {
      if (!"FLIP".equals(deviceType)) {
         byte var3 = -1;
         key.hashCode();
         switch(var3) {
         default:
            return true;
         }
      } else {
         return true;
      }
   }

   public static List convertCabinetGroupSetting(String setting) {
      if (setting != null && !setting.equals("")) {
         ArrayList cabinetGroupSetting = new ArrayList();

         try {
            if (setting != null) {
               String[] groups = setting.split(";");
               String[] var3 = groups;
               int var4 = groups.length;

               for(int var5 = 0; var5 < var4; ++var5) {
                  String group = var3[var5];
                  String[] values = group.split(":");
                  if (values.length >= 3) {
                     V2DeviceCabinetGroupConf groupInfo = new V2DeviceCabinetGroupConf();
                     groupInfo.setIndex(Integer.valueOf(values[0].trim()));
                     groupInfo.setChildNumber(Integer.valueOf(values[1].trim()));
                     groupInfo.setCabinetIPAddress(values[2]);
                     cabinetGroupSetting.add(groupInfo);
                  }
               }
            } else {
               cabinetGroupSetting = null;
            }
         } catch (Exception var9) {
            cabinetGroupSetting = null;
         }

         return cabinetGroupSetting;
      } else {
         return null;
      }
   }

   private static String getDayString(String day) {
      String ret = "";
      int manual = Integer.parseInt(day);
      if (manual == 127) {
         ret = "Everyday";
      } else if (manual == 126) {
         ret = "Mon - Sat";
      } else if (manual == 0) {
         ret = "Once";
      } else if (manual == 62) {
         ret = "Mon - Fri";
      } else {
         while(manual > 0) {
            if (manual >= 64) {
               manual -= 64;
               ret = "Sat" + (!ret.equals("") ? "/" : "") + ret;
            } else if (manual >= 32) {
               manual -= 32;
               ret = "Fri" + (!ret.equals("") ? "/" : "") + ret;
            } else if (manual >= 16) {
               manual -= 16;
               ret = "Thu" + (!ret.equals("") ? "/" : "") + ret;
            } else if (manual >= 8) {
               manual -= 8;
               ret = "Wed" + (!ret.equals("") ? "/" : "") + ret;
            } else if (manual >= 4) {
               manual -= 4;
               ret = "Tue" + (!ret.equals("") ? "/" : "") + ret;
            } else if (manual >= 2) {
               manual -= 2;
               ret = "Mon" + (!ret.equals("") ? "/" : "") + ret;
            } else if (manual >= 1) {
               --manual;
               ret = "Sun" + (!ret.equals("") ? "/" : "") + ret;
            }
         }
      }

      return ret;
   }

   public static List stringToSourceLockList(String source) {
      ArrayList sourceLockList = new ArrayList();

      try {
         if (null == source || source.isEmpty()) {
            return sourceLockList;
         }

         StringTokenizer tokenizer = new StringTokenizer(source, ";");

         while(tokenizer.hasMoreElements()) {
            StringTokenizer tokenizer1 = new StringTokenizer(tokenizer.nextToken(), ":");
            HashMap inputSource = new HashMap();
            String code = tokenizer1.nextToken();
            inputSource.put("code", Integer.parseInt(code));
            inputSource.put("value", Integer.parseInt(tokenizer1.nextToken()));
            inputSource.put("text", getInputSource(code));
            sourceLockList.add(inputSource);
         }
      } catch (NumberFormatException var6) {
         logger.error(var6.getMessage());
      }

      return sourceLockList;
   }

   public static String sourceLockListToString(List sourceLockList) {
      if (null != sourceLockList && !sourceLockList.isEmpty()) {
         AtomicReference retValue = new AtomicReference("");
         sourceLockList.forEach((stringObjectHashMap) -> {
            if (!((String)retValue.get()).isEmpty()) {
               retValue.set(retValue + ";");
            }

            retValue.set((String)retValue.get() + stringObjectHashMap.get("code"));
            retValue.set((String)retValue.get() + ":" + stringObjectHashMap.get("value"));
         });
         return (String)retValue.get();
      } else {
         return null;
      }
   }

   public static String getFirmwareIndicators(String applicationVersion) {
      String firmware_indicators = null;

      try {
         String temp = applicationVersion.split(";")[0];
         String[] temp2 = temp.split(" ")[0].split("-");
         if (temp2.length >= 2) {
            firmware_indicators = temp2[0] + "-" + temp2[1];
         }
      } catch (Exception var4) {
         logger.error("Error occured during creating firmware indicators", var4);
      }

      return firmware_indicators;
   }

   public static boolean useRandomIV(String supportFlag) {
      return false;
   }

   public static boolean isSupportExternalPower(String supportFlag) {
      int index = 6;
      if (null == supportFlag) {
         return false;
      } else {
         return supportFlag.length() > index && supportFlag.charAt(index) == '1';
      }
   }

   public static String decode(String deviceId, String data, boolean useRandomIV) {
      String key = SecurityUtils.getPincodeEncryptionKey(deviceId);

      try {
         if (useRandomIV) {
            AesCbcCipher aesCbc = new AesCbcCipher();
            return AesCbcCipher.decrypt(key, data);
         } else {
            AES256Cipher aes256 = new AES256Cipher(key);
            return aes256.aesDecode(data);
         }
      } catch (Exception var5) {
         logger.error(var5);
         return null;
      }
   }

   public static String encode(String deviceId, String data, boolean useRandomIV) {
      String key = SecurityUtils.getPincodeEncryptionKey(deviceId);

      try {
         if (useRandomIV) {
            AesCbcCipher aesCbc = new AesCbcCipher();
            return AesCbcCipher.encrypt(data, deviceId);
         } else {
            AES256Cipher a256 = new AES256Cipher(key);
            return a256.aesEncode(data);
         }
      } catch (Exception var5) {
         logger.error(var5);
         return null;
      }
   }

   public static boolean deleteLogFile(File filePath, String type, String fileName) {
      String TYPE_PLATFORM = "platform";
      if (!filePath.exists()) {
         return false;
      } else {
         File[] files = filePath.listFiles();
         if (files == null) {
            return false;
         } else {
            File[] var5 = files;
            int var6 = files.length;

            for(int var7 = 0; var7 < var6; ++var7) {
               File file = var5[var7];
               if (file != null && (!"platform".equalsIgnoreCase(type) || !file.getName().contains("zip")) && ("platform".equalsIgnoreCase(type) || file.getName().contains("zip")) && ("platform".equalsIgnoreCase(type) || !StringUtils.isEmpty(fileName) && file.getName().contains(fileName))) {
                  file.delete();
               }
            }

            return true;
         }
      }
   }

   public static boolean isDeviceReadAuthorityPresent() throws SQLException {
      User curUser = SecurityUtils.getUserContainer().getUser();
      AbilityInfo abilityInfo = AbilityInfoImpl.getInstance();
      if (curUser != null) {
         List abilityList = abilityInfo.getAllAbilityListByUserId(curUser.getUser_id());
         Map deviceReadMap = new CaseInsensitiveMap();
         deviceReadMap.put("ABILITY_NAME", "Device Read Authority");
         return abilityList.contains(deviceReadMap);
      } else {
         return false;
      }
   }

   public static String processCabinetGroupLayoutString(String deviceId, String display_and_cabinet_info) throws SQLException {
      if (!StringUtils.isEmpty(deviceId) && !StringUtils.isEmpty(display_and_cabinet_info)) {
         List extractedInfo = getDisplayOutputModeAndCabinetInfo(display_and_cabinet_info);
         if (extractedInfo == null) {
            logger.error("display_and_cabinet_info string is invalid!");
            return display_and_cabinet_info;
         } else {
            Long display_output_mode = Long.parseLong((String)extractedInfo.get(0));
            String cabinet_group_layout = (String)extractedInfo.get(1);
            if (display_output_mode >= 0L && display_output_mode <= 3L) {
               DeviceDisplayConf info = new DeviceDisplayConf();
               info.setDevice_id(deviceId);
               info.setDisplay_output_mode(display_output_mode);
               deviceDisplayDao.setDeviceDisplayConf(info);
               deviceDisplayDao.setDeviceDisplayExtConf(info);
               logger.info("Display Output Mode value updated successfully.");
               return cabinet_group_layout;
            } else {
               logger.error("Invalid value found for display output mode!");
               return cabinet_group_layout;
            }
         }
      } else {
         logger.error("Display Output Mode not updated. deviceId or cabinet_group_layout is empty.");
         return display_and_cabinet_info;
      }
   }

   public static List getDisplayOutputModeAndCabinetInfo(String display_and_cabinet_info) {
      List result = new ArrayList();
      if (display_and_cabinet_info == null) {
         return null;
      } else {
         int splitIdx = display_and_cabinet_info.indexOf(",");
         if (splitIdx != 1) {
            logger.error("display_and_cabinet_info string is invalid!");
            return null;
         } else {
            String display_output_mode = display_and_cabinet_info.substring(0, splitIdx);
            String cabinet_group_layout = display_and_cabinet_info.substring(splitIdx + 1);
            result.add(display_output_mode);
            result.add(cabinet_group_layout);
            return result;
         }
      }
   }

   public static String getDeviceType(long priority, String groupType) {
      switch((int)priority) {
      case -150:
         return "RKIOSK";
      case -100:
         return "RS4PLAYER";
      case -98:
         return "RS5PLAYER";
      case -96:
         return "RS6PLAYER";
      case -94:
         return "RS7PLAYER";
      case -92:
         return "RS9PLAYER";
      case -90:
         return "RS10PLAYER";
      case -50:
         return "FLIP";
      case -48:
         return "FLIP2";
      case -46:
         return "FLIP3";
      case -44:
         return "FLIP4";
      case -3:
         return "RIPLAYER";
      case -2:
         return "WPLAYER";
      case -1:
         return "APLAYER";
      case 1:
         return "LPLAYER";
      case 5:
      case 1000:
      case 2000:
         return "SPLAYER";
      case 10:
         return "S2PLAYER";
      case 20:
         if ("SIGNAGE".equals(groupType)) {
            return "SIGNAGE";
         } else if ("S3PLAYER".equals(groupType)) {
            return "S3PLAYER";
         }
      case 50:
         if ("LEDBOX".equals(groupType)) {
            return "LEDBOX";
         } else if ("SIGNAGE".equals(groupType)) {
            return "SIGNAGE";
         } else if ("S4PLAYER".equals(groupType)) {
            return "S4PLAYER";
         }
      case 60:
         return "S5PLAYER";
      case 70:
         return "S6PLAYER";
      case 80:
         return "S7PLAYER";
      case 90:
         return "S9PLAYER";
      case 100:
         return "S10PLAYER";
      case 110:
         return "IPLAYER";
      default:
         return "default";
      }
   }

   static {
      try {
         if (CommonConfig.get("device.thumbnail.alwaysfullsize") != null) {
            alwaysFullSize = Boolean.parseBoolean(CommonConfig.get("device.thumbnail.alwaysfullsize"));
         }
      } catch (ConfigException var1) {
         logger.error("", var1);
      }

   }
}
