package com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.exception.WebAuthorAbstractException;
import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.UploadResponse;
import com.samsung.magicinfo.webauthor2.model.svg.FontDescription;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import com.samsung.magicinfo.webauthor2.service.ContentService;
import com.samsung.magicinfo.webauthor2.service.RemoteContentService;
import com.samsung.magicinfo.webauthor2.service.UploadFileNameValidationService;
import com.samsung.magicinfo.webauthor2.service.upload.FontUploadService;
import com.samsung.magicinfo.webauthor2.util.MultipartFilenameValidator;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping({"/font"})
public class FontController {
  private static final Logger logger = LoggerFactory.getLogger(FontController.class);
  
  private final FontUploadService fontUploadService;
  
  private final RemoteContentService remoteContentService;
  
  private final ContentService contentService;
  
  private final UploadFileNameValidationService uploadFileNameValidationService;
  
  private final MultipartFilenameValidator multipartFilenameValidator;
  
  @Autowired
  public FontController(FontUploadService fontUploadService, RemoteContentService remoteContentService, ContentService contentService, UploadFileNameValidationService uploadFileNameValidationService, MultipartFilenameValidator multipartFilenameValidator) {
    this.fontUploadService = fontUploadService;
    this.remoteContentService = remoteContentService;
    this.contentService = contentService;
    this.uploadFileNameValidationService = uploadFileNameValidationService;
    this.multipartFilenameValidator = multipartFilenameValidator;
  }
  
  @PostMapping({"/analysis"})
  public HttpEntity<FontDescription> requestFontDescription(@RequestParam String fileName) throws UploaderException {
    FontDescription fontDesc = this.fontUploadService.getFontDescription(fileName);
    return (HttpEntity<FontDescription>)ResponseEntity.ok(fontDesc);
  }
  
  @GetMapping({"/validateFileName"})
  public HttpEntity<String> validateFileName(@RequestParam String fileName) {
    String errMessage = this.multipartFilenameValidator.validateName(fileName);
    if (errMessage == null)
      return (HttpEntity<String>)ResponseEntity.ok(""); 
    return (HttpEntity<String>)ResponseEntity.ok(errMessage);
  }
  
  @PostMapping(value = {"/prerequisite"}, consumes = {"multipart/form-data"})
  public HttpEntity<List<MediaSource>> requestMediaSource(@RequestParam String fileName, @RequestParam String playerType, @RequestParam("upload") MultipartFile font) throws IOException {
    if (false == this.uploadFileNameValidationService.validateFileNameNotToMoveIntoUpperFolder(font).booleanValue() || false == this.uploadFileNameValidationService
      .validateFileNameNotToMoveIntoUpperFolder(fileName).booleanValue()) {
      List<MediaSource> empty = new ArrayList<>();
      return (HttpEntity<List<MediaSource>>)ResponseEntity.badRequest().body(empty);
    } 
    List<MediaSource> updatedMediaSources = this.fontUploadService.getUpdatedMediaSources(font, fileName, DeviceType.valueOf(playerType));
    return (HttpEntity<List<MediaSource>>)ResponseEntity.ok(updatedMediaSources);
  }
  
  @PostMapping({"/submitLfd"})
  public HttpEntity<UploadResponse> xmlUpload(@RequestParam String lfdXml) throws UploaderException {
    String contentId = this.fontUploadService.uploadFont(lfdXml);
    UploadResponse response = new UploadResponse(HttpStatus.OK.value(), contentId);
    return (HttpEntity<UploadResponse>)ResponseEntity.ok(response);
  }
  
  @GetMapping({"/download"})
  public HttpEntity<String> getFontFile(@RequestParam String fileId, @RequestParam String fileName) {
    return (HttpEntity<String>)ResponseEntity.ok(this.remoteContentService.getFontFileFromMagicInfoServer(fileId, fileName).toString());
  }
  
  @GetMapping({"/delete"})
  public HttpEntity<String> deleteFontFile(@RequestParam String contentId) {
    return (HttpEntity<String>)ResponseEntity.ok(this.contentService.deleteContent(contentId).toString());
  }
  
  @ExceptionHandler({WebAuthorAbstractException.class})
  public ResponseEntity<UploadResponse> webAuthorExceptionHandler(WebAuthorAbstractException ex) {
    logger.error(ex.getMessage(), (Throwable)ex);
    UploadResponse uploadResponse = new UploadResponse(ex.getErrorCode(), ex.getMessage());
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(uploadResponse);
  }
  
  @ExceptionHandler({Exception.class})
  public ResponseEntity<UploadResponse> generalExceptionHandler(Exception ex) {
    logger.error(ex.getMessage(), ex);
    UploadResponse uploadResponse = new UploadResponse(HttpStatus.INTERNAL_SERVER_ERROR.value(), ex.getMessage());
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(uploadResponse);
  }
}
