package com.samsung.magicinfo.protocol.file;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLDecoder;
import java.nio.ByteBuffer;
import java.nio.channels.Channels;
import java.nio.channels.ReadableByteChannel;
import java.nio.channels.WritableByteChannel;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.Logger;

public class FFmpegDownloadServlet extends HttpServlet {
   private static final long serialVersionUID = -3161866407287043158L;
   private Logger logger = LoggingManagerV2.getLogger(FFmpegDownloadServlet.class);

   public FFmpegDownloadServlet() {
      super();
   }

   public void doGet(HttpServletRequest req, HttpServletResponse res) throws ServletException, IOException {
      req.setCharacterEncoding("UTF-8");
      res.setContentType("text/html; charset=UTF-8");
      this.doPost(req, res);
   }

   public void doPost(HttpServletRequest req, HttpServletResponse res) throws ServletException, IOException {
      res.setContentType("text/html; charset=UTF-8");
      String cmd = StrUtils.nvl(req.getParameter("cmd")).equals("") ? "" : req.getParameter("cmd");
      FileInputStream in;
      WritableByteChannel outChannel;
      String fileName;
      ByteBuffer buf;
      int index;
      if (!cmd.equals("ffmpeg") && !cmd.equals("")) {
         String fileName;
         File previewFile;
         if (cmd.equals("ffmpeglength")) {
            try {
               fileName = CommonConfig.get("uploader_path").replace('/', File.separatorChar);
               previewFile = SecurityUtils.getSafeFile(fileName + File.separatorChar + "ffmpeg.exe");
               if (!previewFile.exists() || !previewFile.isFile()) {
                  res.sendError(600, "File doesn't exist");
               }

               res.setHeader("FileSize", String.valueOf(previewFile.length()));
            } catch (Exception var129) {
               res.sendError(600, "File doesn't exist");
            }
         } else {
            ReadableByteChannel inChannel;
            String ContentView;
            if (cmd.equals("preview")) {
               in = null;
               inChannel = null;

               try {
                  ContentView = CommonConfig.get("uploader_path").replace('/', File.separatorChar) + File.separatorChar + "MpPreview.zip";
                  in = new FileInputStream(SecurityUtils.directoryTraversalChecker(ContentView, (String)null));
                  inChannel = Channels.newChannel(in);
                  OutputStream out = res.getOutputStream();
                  WritableByteChannel outChannel = Channels.newChannel(out);
                  buf = ByteBuffer.allocateDirect(4096);

                  while(true) {
                     index = inChannel.read(buf);
                     if (index == -1) {
                        break;
                     }

                     buf.flip();
                     outChannel.write(buf);
                     buf.clear();
                  }
               } catch (Exception var127) {
                  res.sendError(600, var127.toString());
               } finally {
                  in.close();
                  inChannel.close();
               }
            } else if (cmd.equals("previewlength")) {
               try {
                  fileName = CommonConfig.get("uploader_path").replace('/', File.separatorChar);
                  previewFile = SecurityUtils.getSafeFile(fileName + File.separatorChar + "MpPreview.zip");
                  if (!previewFile.exists() || !previewFile.isFile()) {
                     res.sendError(600, "File doesn't exist");
                  }

                  res.setHeader("FileSize", String.valueOf(previewFile.length()));
               } catch (Exception var126) {
                  res.sendError(600, "File doesn't exist");
               }
            } else {
               String fileId;
               if (cmd.equalsIgnoreCase("FileDownLoad")) {
                  in = null;

                  try {
                     fileId = StrUtils.nvl(req.getParameter("fileName")).equals("") ? "" : req.getParameter("fileName");
                     fileId = SecurityUtils.directoryTraversalChecker(fileId, req.getRemoteAddr());
                     ContentView = CommonConfig.get("uploader_path").replace('/', File.separatorChar) + File.separatorChar + fileId;
                     in = new FileInputStream(SecurityUtils.directoryTraversalChecker(ContentView, (String)null));
                     ReadableByteChannel inChannel = Channels.newChannel(in);
                     OutputStream out = res.getOutputStream();
                     WritableByteChannel outChannel = Channels.newChannel(out);
                     ByteBuffer buf = ByteBuffer.allocateDirect(4096);

                     while(true) {
                        int ret = inChannel.read(buf);
                        if (ret == -1) {
                           break;
                        }

                        buf.flip();
                        outChannel.write(buf);
                        buf.clear();
                     }
                  } catch (Exception var124) {
                     res.sendError(600, var124.toString());
                  } finally {
                     try {
                        if (in != null) {
                           in.close();
                        }
                     } catch (IOException var119) {
                        this.logger.error("", var119);
                     }

                  }
               } else if (cmd.equalsIgnoreCase("FileLength")) {
                  try {
                     fileName = StrUtils.nvl(req.getParameter("fileName")).equals("") ? "" : req.getParameter("fileName");
                     fileId = CommonConfig.get("uploader_path").replace('/', File.separatorChar);
                     File previewFile = SecurityUtils.getSafeFile(fileId + File.separatorChar + fileName);
                     if (!previewFile.exists() || !previewFile.isFile()) {
                        res.sendError(600, "File doesn't exist");
                     }

                     res.setHeader("FileSize", String.valueOf(previewFile.length()));
                  } catch (Exception var123) {
                     res.sendError(600, "File doesn't exist");
                  }
               } else {
                  String fileId;
                  if (cmd.equalsIgnoreCase("LayoutAuthorFileDownload")) {
                     in = null;
                     inChannel = null;
                     OutputStream out = null;
                     outChannel = null;

                     try {
                        fileName = StrUtils.nvl(req.getParameter("fileName")).equals("") ? "" : req.getParameter("fileName");
                        fileName = URLDecoder.decode(fileName, "UTF-8");
                        fileId = StrUtils.nvl(req.getParameter("fileId")).equals("") ? "" : req.getParameter("fileId");
                        fileName = SecurityUtils.directoryTraversalChecker(fileName, req.getRemoteAddr());
                        index = fileName.lastIndexOf(".");
                        String extension = fileName.substring(index + 1);
                        String filePath = "";
                        String ContentView = StrUtils.nvl(req.getParameter("ContentView")).equals("") ? "" : req.getParameter("ContentView");
                        if (extension.equalsIgnoreCase("MDD")) {
                           filePath = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separator + extension + File.separator + fileName;
                        } else if (extension.equalsIgnoreCase("VWL")) {
                           if (!ContentView.equals("")) {
                              filePath = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separator + CommonConfig.get("CONTENTS_DIR") + File.separator + fileId + File.separator + fileName;
                           } else {
                              filePath = CommonConfig.get("VWT_HOME").replace('/', File.separatorChar) + File.separator + fileId + File.separator + fileName;
                           }
                        }

                        in = new FileInputStream(SecurityUtils.directoryTraversalChecker(filePath, (String)null));
                        inChannel = Channels.newChannel(in);
                        out = res.getOutputStream();
                        outChannel = Channels.newChannel(out);
                        ByteBuffer buf = ByteBuffer.allocateDirect(4096);

                        while(true) {
                           int ret = inChannel.read(buf);
                           if (ret == -1) {
                              break;
                           }

                           buf.flip();
                           outChannel.write(buf);
                           buf.clear();
                        }
                     } catch (Exception var121) {
                        res.sendError(600, var121.toString());
                     } finally {
                        try {
                           in.close();
                        } catch (Exception var118) {
                        }

                        try {
                           inChannel.close();
                        } catch (Exception var117) {
                        }

                        try {
                           out.close();
                        } catch (Exception var116) {
                        }

                        try {
                           outChannel.close();
                        } catch (Exception var115) {
                        }

                     }
                  } else if (cmd.equalsIgnoreCase("LayoutAuthorFileLength")) {
                     try {
                        fileName = StrUtils.nvl(req.getParameter("fileName")).equals("") ? "" : req.getParameter("fileName");
                        fileName = URLDecoder.decode(fileName, "UTF-8");
                        fileId = StrUtils.nvl(req.getParameter("fileId")).equals("") ? "" : req.getParameter("fileId");
                        ContentView = StrUtils.nvl(req.getParameter("ContentView")).equals("") ? "" : req.getParameter("ContentView");
                        int index = fileName.lastIndexOf(".");
                        fileName = fileName.substring(index + 1);
                        fileId = "";
                        if (fileName.equalsIgnoreCase("MDD")) {
                           fileId = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separator + fileName + File.separator + fileName;
                        } else if (fileName.equalsIgnoreCase("VWL")) {
                           if (!ContentView.equals("")) {
                              fileId = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separator + CommonConfig.get("CONTENTS_DIR") + File.separator + fileId + File.separator + fileName;
                           } else {
                              fileId = CommonConfig.get("VWT_HOME").replace('/', File.separatorChar) + File.separator + fileId + File.separator + fileName;
                           }
                        }

                        File file = SecurityUtils.getSafeFile(fileId);
                        if (!file.exists() || !file.isFile()) {
                           res.sendError(600, "File doesn't exist");
                        }

                        res.setHeader("FileSize", String.valueOf(file.length()));
                     } catch (Exception var120) {
                        res.sendError(600, "File doesn't exist");
                     }
                  }
               }
            }
         }
      } else {
         in = null;
         OutputStream out = null;
         ReadableByteChannel inChannel = null;
         outChannel = null;

         try {
            fileName = CommonConfig.get("uploader_path").replace('/', File.separatorChar) + File.separatorChar + "ffmpeg.exe";
            in = new FileInputStream(SecurityUtils.directoryTraversalChecker(fileName, (String)null));
            inChannel = Channels.newChannel(in);
            out = res.getOutputStream();
            outChannel = Channels.newChannel(out);
            buf = ByteBuffer.allocateDirect(4096);

            while(true) {
               index = inChannel.read(buf);
               if (index == -1) {
                  break;
               }

               buf.flip();
               outChannel.write(buf);
               buf.clear();
            }
         } catch (Exception var130) {
            res.sendError(600, var130.toString());
         } finally {
            try {
               if (in != null) {
                  in.close();
               }

               if (out != null) {
                  out.close();
               }

               if (inChannel != null) {
                  inChannel.close();
               }

               if (outChannel != null) {
                  outChannel.close();
               }
            } catch (IOException var114) {
               this.logger.error("", var114);
            }

         }
      }

   }
}
