package com.samsung.magicinfo.framework.device.preconfig.manager;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.device.constants.DeviceMOConstants;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceControl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSecurityConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceServiceConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSoftwareConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemSetupConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTimeConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTimeHolidayConf;
import com.samsung.magicinfo.framework.device.preconfig.entity.DeviceConfig;
import com.samsung.magicinfo.framework.device.preconfig.entity.DeviceConfigItem;
import com.samsung.magicinfo.framework.device.preconfig.entity.DeviceConfigItemValue;
import com.samsung.magicinfo.framework.device.preconfig.entity.DownloadConfig;
import com.samsung.magicinfo.framework.device.preconfig.entity.DownloadConfigItem;
import com.samsung.magicinfo.framework.device.preconfig.entity.ServiceConfig;
import com.samsung.magicinfo.framework.device.preconfig.entity.ServiceConfigItem;
import com.samsung.magicinfo.framework.device.preconfig.entity.ServiceConfigItemServer;
import com.samsung.magicinfo.framework.device.preconfig.entity.ServiceConfigItemServerInfo;
import com.samsung.magicinfo.framework.device.software.entity.Software;
import com.samsung.magicinfo.framework.device.software.manager.SoftwareManager;
import com.samsung.magicinfo.framework.device.software.manager.SoftwareManagerImpl;
import edu.emory.mathcs.backport.java.util.Collections;
import java.sql.SQLException;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import jodd.util.StringUtil;
import org.apache.logging.log4j.Logger;

public class PreConfigUtils {
   protected static Logger logger = LoggingManagerV2.getLogger(PreConfigUtils.class);
   private static boolean initialized = false;
   private static Map displayMoPropertyMap = null;
   private static Map displayPropertyMoMap = null;
   private static Map setupMoPropertyMap = null;
   private static Map setupPropertyMoMap = null;
   private static Map timeMoPropertyMap = null;
   private static Map timePropertyMoMap = null;
   private static Map securityMoPropertyMap = null;
   private static Map securityPropertyMoMap = null;

   public PreConfigUtils() {
      super();
   }

   private static void init() {
      logger.info("PreconfigUtils Init....");
      if (!initialized) {
         displayMoPropertyMap = DeviceMOConstants.getDisplayMOEntity();
         displayPropertyMoMap = DeviceMOConstants.getDisplayEntityMO();
         setupMoPropertyMap = DeviceMOConstants.getSetupMOEntity();
         setupPropertyMoMap = DeviceMOConstants.getSetupEntityMO();
         timeMoPropertyMap = DeviceMOConstants.getTimeMOEntity();
         timePropertyMoMap = DeviceMOConstants.getTimeEntityMO();
         securityMoPropertyMap = DeviceMOConstants.getSecurityMOEntity();
         securityPropertyMoMap = DeviceMOConstants.getSecurityEntityMO();
         initialized = true;
      }

   }

   public static Map convertPreconfigResultMap(DeviceConfig config) {
      if (!initialized) {
         init();
      }

      Map result = new HashMap();
      List items = config.getAllItems();
      if (items == null) {
         return result;
      } else {
         Map displayResult = new HashMap();
         Iterator var4 = items.iterator();

         while(true) {
            String property;
            List values;
            String property;
            do {
               do {
                  if (!var4.hasNext()) {
                     result.put("display", displayResult);
                     Map setupResult = new HashMap();
                     Iterator var15 = items.iterator();

                     while(true) {
                        String property;
                        List values;
                        String property;
                        do {
                           do {
                              if (!var15.hasNext()) {
                                 result.put("setup", setupResult);
                                 Map timeResult = new HashMap();
                                 Iterator var18 = items.iterator();

                                 while(true) {
                                    List values;
                                    do {
                                       do {
                                          if (!var18.hasNext()) {
                                             result.put("time", timeResult);
                                             Map securityResult = new HashMap();
                                             Iterator var22 = items.iterator();

                                             while(true) {
                                                List values;
                                                do {
                                                   do {
                                                      if (!var22.hasNext()) {
                                                         result.put("security", securityResult);
                                                         return result;
                                                      }

                                                      DeviceConfigItem item = (DeviceConfigItem)var22.next();
                                                      property = (String)securityMoPropertyMap.get(item.getMoType());
                                                      values = item.getMoValue();
                                                   } while(property == null);
                                                } while(values == null);

                                                String value = null;
                                                Iterator var32 = values.iterator();

                                                while(var32.hasNext()) {
                                                   DeviceConfigItemValue val = (DeviceConfigItemValue)var32.next();
                                                   if (value == null) {
                                                      value = val.getItemValue();
                                                   } else if (!value.equals(val.getItemValue())) {
                                                      value = "PARTIAL";
                                                      break;
                                                   }
                                                }

                                                securityResult.put(property, value);
                                             }
                                          }

                                          DeviceConfigItem item = (DeviceConfigItem)var18.next();
                                          property = (String)timeMoPropertyMap.get(item.getMoType());
                                          values = item.getMoValue();
                                       } while(property == null);
                                    } while(values == null);

                                    String value = null;
                                    Iterator var29 = values.iterator();

                                    while(var29.hasNext()) {
                                       DeviceConfigItemValue val = (DeviceConfigItemValue)var29.next();
                                       if (value == null) {
                                          value = val.getItemValue();
                                       } else if (!value.equals(val.getItemValue())) {
                                          value = "PARTIAL";
                                          break;
                                       }
                                    }

                                    timeResult.put(property, value);
                                 }
                              }

                              DeviceConfigItem item = (DeviceConfigItem)var15.next();
                              property = (String)setupMoPropertyMap.get(item.getMoType());
                              values = item.getMoValue();
                           } while(property == null);
                        } while(values == null);

                        property = null;
                        Iterator var26 = values.iterator();

                        while(var26.hasNext()) {
                           DeviceConfigItemValue val = (DeviceConfigItemValue)var26.next();
                           if (property == null) {
                              property = val.getItemValue();
                           } else if (!property.equals(val.getItemValue())) {
                              property = "PARTIAL";
                              break;
                           }
                        }

                        setupResult.put(property, property);
                     }
                  }

                  DeviceConfigItem item = (DeviceConfigItem)var4.next();
                  property = (String)displayMoPropertyMap.get(item.getMoType());
                  values = item.getMoValue();
               } while(property == null);
            } while(values == null);

            property = null;
            Iterator var9 = values.iterator();

            while(var9.hasNext()) {
               DeviceConfigItemValue val = (DeviceConfigItemValue)var9.next();
               if (property == null) {
                  property = val.getItemValue();
               } else if (!property.equals(val.getItemValue())) {
                  property = "PARTIAL";
                  break;
               }
            }

            displayResult.put(property, property);
         }
      }
   }

   public static DeviceConfig convertDeviceConfig(DeviceControl deviceControl) {
      DeviceConfig dc = null;
      if (deviceControl == null) {
         return null;
      } else {
         dc = new DeviceConfig();
         DeviceDisplayConf display = deviceControl.getDisplay();
         DeviceSecurityConf security = deviceControl.getSecurity();
         DeviceSystemSetupConf setup = deviceControl.getSetup();
         DeviceTimeConf time = deviceControl.getTime();
         new DeviceTimeHolidayConf();
         if (time != null) {
            if (time.getTimer_timer1() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.TIMER.TIMER1", time.getTimer_timer1()));
            }

            if (time.getTimer_timer2() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.TIMER.TIMER2", time.getTimer_timer2()));
            }

            if (time.getTimer_timer3() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.TIMER.TIMER3", time.getTimer_timer3()));
            }

            if (time.getTimer_timer4() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.TIMER.TIMER4", time.getTimer_timer4()));
            }

            if (time.getTimer_timer5() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.TIMER.TIMER5", time.getTimer_timer5()));
            }

            if (time.getTimer_timer6() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.TIMER.TIMER6", time.getTimer_timer6()));
            }

            if (time.getTimer_timer7() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.TIMER.TIMER7", time.getTimer_timer7()));
            }

            if (time.getTimer_holiday() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.TIMER.HOLIDAY", time.getTimer_holiday()));
            }
         }

         if (display != null) {
            if (display.getBasic_volume() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.BASIC.VOLUME", display.getBasic_volume().toString()));
            }

            if (display.getBasic_mute() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.BASIC.MUTE", display.getBasic_mute().toString()));
            }

            if (display.getBasic_source() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.BASIC.SOURCE", display.getBasic_source().toString()));
            }

            if (display.getBasic_panel_status() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS", display.getBasic_panel_status().toString()));
            }

            if (display.getSpecialized_picture_mode() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.PICTURE_VIDEO.SPECIALIZED_PICTURE_MODE", display.getSpecialized_picture_mode().toString()));
            }

            if (display.getPv_mode() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.PICTURE_VIDEO.MODE", display.getPv_mode().toString()));
            }

            if (display.getPv_contrast() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.PICTURE_VIDEO.CONTRAST", display.getPv_contrast().toString()));
            }

            if (display.getPv_brightness() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.PICTURE_VIDEO.BRIGHTNESS", display.getPv_brightness().toString()));
            }

            if (display.getPv_sharpness() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.PICTURE_VIDEO.SHARPNESS", display.getPv_sharpness().toString()));
            }

            if (display.getPv_color() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR", display.getPv_color().toString()));
            }

            if (display.getPv_colortone() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TONE", display.getPv_colortone().toString()));
            }

            if (display.getPv_color_temperature() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TEMPERATURE", display.getPv_color_temperature().toString()));
            }

            if (display.getPv_tint() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.PICTURE_VIDEO.TINT", display.getPv_tint().toString()));
            }

            if (display.getPv_size() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.PICTURE_VIDEO.SIZE", display.getPv_size().toString()));
            }

            if (display.getMnt_manual() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.MAINTENANCE.MANUAL", display.getMnt_manual().toString()));
            }

            if (display.getPpc_gamma() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.PICTURE_PC.GAMMA", display.getPpc_gamma().toString()));
            }

            if (display.getPv_hdmi_black_level() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.PICTURE_VIDEO.HDMI_BLACK_LEVEL", display.getPv_hdmi_black_level().toString()));
            }

            if (display.getPv_digitalnr() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.PICTURE_VIDEO.DIGITAL_NR", display.getPv_digitalnr().toString()));
            }

            if (display.getPv_filmmode() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.PICTURE_VIDEO.FILM_MODE", display.getPv_filmmode().toString()));
            }

            if (display.getMnt_auto() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.MAINTENANCE.AUTO", display.getMnt_auto()));
            }

            if (display.getPpc_magic_bright() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.PICTURE_PC.MAGIC_BRIGHT", display.getPpc_magic_bright().toString()));
            }

            if (display.getWeb_browser_url() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.BASIC.WEB_BROWSER_URL", display.getWeb_browser_url()));
            }

            if (display.getCustom_logo() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.BASIC.CUSTOM_LOGO", display.getCustom_logo()));
            }

            if (display.getScreen_mute() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.BASIC.SCREEN_MUTE", display.getScreen_mute().toString()));
            }

            if (display.getScreen_freeze() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.BASIC.FREEZE", display.getScreen_freeze().toString()));
            }

            if (display.getTime_on_time() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.TIME.ON_TIME", display.getTime_on_time()));
            }

            if (display.getTime_off_time() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.TIME.OFF_TIME", display.getTime_off_time()));
            }

            if (display.getMisc_osd() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.MISC.OSD", display.getMisc_osd().toString()));
            }

            if (display.getOsd_menu_size() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.MISC.OSD_MENU_SIZE", display.getOsd_menu_size().toString()));
            }

            if (display.getDiagnosis_alarm_temperature() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.DIAGNOSIS.ALARM_TEMPERATURE", display.getDiagnosis_alarm_temperature().toString()));
            }

            if (display.getBasic_direct_channel() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.BASIC.DIRECT_CHANNEL", display.getBasic_direct_channel()));
            }

            if (display.getPv_mpeg_noise_filter() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.PICTURE_VIDEO.MPEG_NOISE_FILTER", display.getPv_mpeg_noise_filter().toString()));
            }

            if (display.getSound_mode() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.SOUND.MODE", display.getSound_mode().toString()));
            }

            if (display.getSound_bass() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.SOUND.BASS", display.getSound_bass().toString()));
            }

            if (display.getSound_treble() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.SOUND.TREBLE", display.getSound_treble().toString()));
            }

            if (display.getSound_balance() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.SOUND.BALANCE", display.getSound_balance().toString()));
            }

            if (display.getSound_srs() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.SOUND.SRS", display.getSound_srs().toString()));
            }

            if (display.getSb_status() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.SIGNAL_BALANCE.STATE", display.getSb_status().toString()));
            }

            if (display.getSb_gain() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_GAIN", display.getSb_gain().toString()));
            }

            if (display.getSb_sharp() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_SHARPNESS", display.getSb_sharp().toString()));
            }

            if (display.getMnt_safety_screen_timer() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_TIMER", display.getMnt_safety_screen_timer()));
            }

            if (display.getAuto_source_switching() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.AUTO_SOURCE_SWITCHING", display.getAuto_source_switching()));
            }

            if (display.getMax_power_saving() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.MAX_POWER_SAVING", display.getMax_power_saving().toString()));
            }

            if (display.getBrightness_limit() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.BRIGHTNESS_LIMIT", display.getBrightness_limit().toString()));
            }

            if (display.getTouch_control_lock() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.TOUCH_CONTROL_LOCK", display.getTouch_control_lock().toString()));
            }

            if (display.getBlack_tone() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.BLACK_TONE", display.getBlack_tone().toString()));
            }

            if (display.getFlesh_tone() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.FLESH_TONE", display.getFlesh_tone().toString()));
            }

            if (display.getRgb_only_mode() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.RGB_ONLY_MODE", display.getRgb_only_mode().toString()));
            }

            if (display.getLed_hdr() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.PICTURE_PC.LED_HDR", display.getLed_hdr()));
            }

            if (display.getLed_picture_size() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.PICTURE_PC.LED_PICTURE_SIZE", display.getLed_picture_size()));
            }

            if (display.getEco_sensor() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.ECO_SENSOR", display.getEco_sensor().toString()));
            }

            if (display.getMin_brightness() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.MIN_BRIGHTNESS", display.getMin_brightness().toString()));
            }

            if (display.getLive_mode() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.LIVE_MODE", display.getLive_mode().toString()));
            }

            if (display.getDisplay_output_mode() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.DISPLAY_OUTPUT_MODE", display.getDisplay_output_mode().toString()));
            }

            if (display.getSb_rgain() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_GAIN", display.getSb_rgain().toString()));
            }

            if (display.getSb_ggain() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_GAIN", display.getSb_ggain().toString()));
            }

            if (display.getSb_bgain() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_GAIN", display.getSb_bgain().toString()));
            }

            if (display.getSb_r_offset() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_OFFSET", display.getSb_r_offset().toString()));
            }

            if (display.getSb_g_offset() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_OFFSET", display.getSb_g_offset().toString()));
            }

            if (display.getSb_b_offset() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_OFFSET", display.getSb_b_offset().toString()));
            }

            if (display.getMnt_safety_screen_run() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_RUN", display.getMnt_safety_screen_run().toString()));
            }

            if (display.getMnt_pixel_shift() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.MAINTENANCE.PIXEL_SHIFT", display.getMnt_pixel_shift()));
            }

            if (display.getPv_video_picture_position_size() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.PICTURE_VIDEO.VIDEO_PICTURE_POSITION_SIZE", display.getPv_video_picture_position_size()));
            }

            if (display.getAdvanced_rj45_setting_refresh() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.RJ45_SETTING_REFRESH", display.getAdvanced_rj45_setting_refresh().toString()));
            }

            if (!StringUtil.isEmpty(display.getAdvanced_osd_display_type())) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.OSD_DISPLAY_TYPE", display.getAdvanced_osd_display_type()));
            }

            if (display.getAdvanced_fan_control() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.FAN_CONTROL", display.getAdvanced_fan_control().toString()));
            }

            if (display.getAdvanced_fan_speed() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.FAN_SPEED", display.getAdvanced_fan_speed().toString()));
            }

            if (display.getAdvanced_reset() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.RESET", display.getAdvanced_reset().toString()));
            }

            if (display.getAdvanced_auto_power() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.AUTO_POWER", display.getAdvanced_auto_power().toString()));
            }

            if (display.getAdvanced_user_auto_color() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.USER_AUTO_COLOR", display.getAdvanced_user_auto_color().toString()));
            }

            if (display.getAdvanced_stand_by() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.STAND_BY", display.getAdvanced_stand_by().toString()));
            }

            if (display.getNetwork_standby_mode() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.NETWORK_STAND_BY", display.getNetwork_standby_mode()));
            }
         }

         if (security != null) {
            if (security.getMnt_safety_lock() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_LOCK", security.getMnt_safety_lock().toString()));
            }

            if (security.getMisc_remocon() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.MISC.REMOCON", security.getMisc_remocon().toString()));
            }

            if (security.getMisc_panel_lock() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.MISC.PANEL_LOCK", security.getMisc_panel_lock().toString()));
            }

            if (security.getMisc_all_lock() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.MISC.ALL_LOCK", security.getMisc_all_lock().toString()));
            }

            if (security.getMisc_block_usb_port() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.MISC.BLOCK_USB_PORT", security.getMisc_block_usb_port().toString()));
            }

            if (security.getMisc_block_network_connection() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.MISC.BLOCK_NETWORK_CONNECTION", security.getMisc_block_network_connection().toString()));
            }

            if (security.getMisc_white_list() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.MISC.WHITE_LIST", security.getMisc_white_list().toString()));
            }

            if (security.getMisc_server_network_setting() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.MISC.SERVER_NETWORK_SETTING_LOCK", security.getMisc_server_network_setting().toString()));
            }

            if (security.getTouch_control_lock() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.ADVANCED.TOUCH_CONTROL_LOCK", security.getTouch_control_lock().toString()));
            }

            if (security.getBluetooth_lock() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.SECURITY.BLUETOOTH_LOCK", security.getBluetooth_lock().toString()));
            }

            if (security.getWifi_lock() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.SECURITY.WIFI_LOCK", security.getWifi_lock().toString()));
            }

            if (security.getSource_lock() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.SECURITY.SOURCE_LOCK", security.getSource_lock()));
            }

            if (security.getScreen_monitoring_lock() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DISPLAY_CONF.SECURITY.SCREEN_MONITORING_LOCK", security.getScreen_monitoring_lock().toString()));
            }
         }

         if (setup != null) {
            if (setup.getTime_zone_index() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DEVICE_CONF.SYSTEM_SETUP.TIME_ZONE_INDEX", setup.getTime_zone_index()));
            }

            if (setup.getDay_light_saving() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DEVICE_CONF.SYSTEM_SETUP.DAY_LIGHT_SAVING", setup.getDay_light_saving().toString()));
            }

            if (setup.getDay_light_saving_manual() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DEVICE_CONF.SYSTEM_SETUP.DAY_LIGHT_SAVING_MANUAL", setup.getDay_light_saving_manual()));
            }

            if (setup.getTrigger_interval() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DEVICE_CONF.SYSTEM_SETUP.TRIGGER_INTERVAL", setup.getTrigger_interval().toString()));
            }

            if (setup.getBandwidth() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DEVICE_CONF.SYSTEM_SETUP.BANDWIDTH_LIMIT", setup.getBandwidth().toString()));
            }

            if (setup.getMonitoring_interval() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DEVICE_CONF.SYSTEM_SETUP.MONITORING_INTERVAL", setup.getMonitoring_interval().toString()));
            }

            if (setup.getFtp_connect_mode() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DEVICE_CONF.SYSTEM_SETUP.FTP_CONNECT_MODE", setup.getFtp_connect_mode()));
            }

            if (setup.getScreen_capture_interval() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DEVICE_CONF.SYSTEM_SETUP.SCREEN_CAPTURE_INTERVAL", setup.getScreen_capture_interval().toString()));
            }

            if (setup.getSystem_restart_interval() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DEVICE_CONF.SYSTEM_SETUP.SYSTEM_RESTART_INTERVAL", setup.getSystem_restart_interval()));
            }

            if (setup.getProtocol_priority() != null && setup.getProtocol_priority() >= 0L) {
               dc.addItem(new DeviceConfigItem(".MO.DEVICE_CONF.SYSTEM_SETUP.PROTOCOL_PRIORITY", setup.getProtocol_priority().toString()));
            }

            if (setup.getSmart_download() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DEVICE_CONF.SYSTEM_SETUP.SMART_DOWNLOAD", setup.getSmart_download().toString()));
            }

            if (setup.getThird_application_update_domain() != null) {
               dc.addItem(new DeviceConfigItem(".MO.DEVICE_CONF.SYSTEM_SETUP.THIRD_APPLICATION_UPDATE_DOMAIN", setup.getThird_application_update_domain()));
            }
         }

         return dc;
      }
   }

   public static ServiceConfig convertServiceConfig(List deviceServiceConfs) {
      ServiceConfig sc = new ServiceConfig();
      if (deviceServiceConfs != null && deviceServiceConfs.size() > 0) {
         Collections.sort(deviceServiceConfs, new PreConfigUtils.ServiceTypeSort());
         String serviceTypeName = null;
         ServiceConfigItem sci = null;

         for(int i = 0; i < deviceServiceConfs.size(); ++i) {
            DeviceServiceConf serviceConf = (DeviceServiceConf)deviceServiceConfs.get(i);
            if (serviceTypeName == null || !serviceTypeName.equals(serviceConf.getService_type())) {
               sci = new ServiceConfigItem(serviceConf.getService_type());
               serviceTypeName = serviceConf.getService_type();
            }

            ServiceConfigItemServer sciServer = new ServiceConfigItemServer();
            String var7 = serviceConf.getProtocol().toLowerCase();
            byte var8 = -1;
            switch(var7.hashCode()) {
            case 101730:
               if (var7.equals("ftp")) {
                  var8 = 2;
               }
               break;
            case 3153745:
               if (var7.equals("ftps")) {
                  var8 = 3;
               }
               break;
            case 3213448:
               if (var7.equals("http")) {
                  var8 = 0;
               }
               break;
            case 99617003:
               if (var7.equals("https")) {
                  var8 = 1;
               }
            }

            switch(var8) {
            case 0:
               sciServer.setHttp(new ServiceConfigItemServerInfo(serviceConf.getPath(), serviceConf.getPort().toString()));
               break;
            case 1:
               sciServer.setHttps(new ServiceConfigItemServerInfo(serviceConf.getPath(), serviceConf.getPort().toString()));
               break;
            case 2:
               sciServer.setFtp(new ServiceConfigItemServerInfo(serviceConf.getPath(), serviceConf.getPort().toString()));
               break;
            case 3:
               sciServer.setFtps(new ServiceConfigItemServerInfo(serviceConf.getPath(), serviceConf.getPort().toString()));
               break;
            default:
               continue;
            }

            sciServer.setHost(serviceConf.getHost());
            sci.addServer(sciServer);
            if (i == deviceServiceConfs.size() - 1) {
               sc.addItem(sci);
            } else if (!serviceTypeName.equals(((DeviceServiceConf)deviceServiceConfs.get(i + 1)).getService_type())) {
               sc.addItem(sci);
            }
         }

         if (sc.getItemSize() > 0) {
            return sc;
         } else {
            return null;
         }
      } else {
         return null;
      }
   }

   public static DownloadConfig convertSoftwareConfig(List deviceSoftwareConfs) {
      SoftwareManager softwareManager = SoftwareManagerImpl.getInstance();
      DownloadConfig dc = new DownloadConfig();
      if (deviceSoftwareConfs != null && deviceSoftwareConfs.size() > 0) {
         try {
            for(int i = 0; i < deviceSoftwareConfs.size(); ++i) {
               DeviceSoftwareConf softwareConf = (DeviceSoftwareConf)deviceSoftwareConfs.get(i);
               Software software = softwareManager.getSoftware(softwareConf.getSoftware_id());
               DownloadConfigItem dci = new DownloadConfigItem();
               String var7 = softwareConf.getSoftware_type();
               byte var8 = -1;
               switch(var7.hashCode()) {
               case 1539:
                  if (var7.equals("03")) {
                     var8 = 0;
                  }
                  break;
               case 1540:
                  if (var7.equals("04")) {
                     var8 = 1;
                  }
               }

               switch(var8) {
               case 0:
                  dci.setType("CUSTOM_LOGO");
                  break;
               case 1:
                  dci.setType("DEFAULT_CONTENT");
                  break;
               default:
                  continue;
               }

               dci.setId(softwareConf.getSoftware_id());
               dci.setSize(software.getFile_size());
               dci.setName(software.getFile_name());
               dci.setVersion(software.getSoftware_version());
               dc.addItem(dci);
            }
         } catch (SQLException var9) {
            logger.error("", var9);
         }

         return dc.getItemSize() > 0 ? dc : null;
      } else {
         return null;
      }
   }

   private static class ServiceTypeSort implements Comparator {
      private ServiceTypeSort() {
         super();
      }

      public int compare(DeviceServiceConf o1, DeviceServiceConf o2) {
         return o1.getService_type().compareTo(o2.getService_type());
      }

      // $FF: synthetic method
      ServiceTypeSort(Object x0) {
         this();
      }
   }
}
