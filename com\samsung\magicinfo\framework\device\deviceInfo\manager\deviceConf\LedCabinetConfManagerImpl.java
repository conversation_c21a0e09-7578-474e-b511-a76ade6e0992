package com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf;

import com.samsung.common.db.PagedListInfo;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.LedCabinetConfDao;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.LedCabinet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import org.apache.logging.log4j.Logger;

public class LedCabinetConfManagerImpl implements LedCabinetConfManager {
   Logger logger = LoggingManagerV2.getLogger(LedCabinetConfManagerImpl.class);
   static LedCabinetConfManager instance;
   private LedCabinetConfDao dao = null;

   private LedCabinetConfManagerImpl() {
      super();
      this.dao = new LedCabinetConfDao();
   }

   public static synchronized LedCabinetConfManager getInstance() {
      if (instance == null) {
         instance = new LedCabinetConfManagerImpl();
      }

      return instance;
   }

   public PagedListInfo getPagedList(int startPos, int pageSize, Map condition, String section) throws Exception {
      PagedListInfo pagedListInfo = null;
      if (section.equals("getLedCabinetList")) {
         pagedListInfo = this.dao.getLedCabinetList(startPos, pageSize, condition);
      }

      return pagedListInfo;
   }

   public List getLedCabinetList(String parentDeviceId, List childIds) throws SQLException {
      return this.dao.getLedCabinetList(parentDeviceId, childIds);
   }

   public List getLedCabinetList(String parentDeviceId) throws SQLException {
      return this.dao.getLedCabinetList(parentDeviceId, 0L);
   }

   public List getLedCabinetList(String parentDeviceId, Long groupId) throws SQLException {
      return this.dao.getLedCabinetList(parentDeviceId, groupId);
   }

   public List getLedCabinetGroupIds(String parentDeviceId) throws SQLException {
      return this.dao.getLedCabinetGroupIds(parentDeviceId);
   }

   public List getLedCabinetGroupLayoutInfo(String parentDeviceId) throws SQLException {
      return this.dao.getLedCabinetGroupLayoutInfo(parentDeviceId);
   }

   public boolean setLedCabinetPowerOff(String parentDeviceId, Long cabinetGroupId) throws SQLException {
      return this.dao.setLedCabinetPowerOff(parentDeviceId, cabinetGroupId, (Long)null);
   }

   public boolean setLedCabinetPowerOff(String parentDeviceId, Long cabinetGroupId, Long cabinetId) throws SQLException {
      return this.dao.setLedCabinetPowerOff(parentDeviceId, cabinetGroupId, cabinetId);
   }

   public boolean setLedCabinetConf(LedCabinet info) throws SQLException {
      boolean result = false;
      LedCabinet current = null;

      try {
         current = this.dao.getLedCabinet(info.getParent_device_id(), info.getCabinet_group_id(), info.getCabinet_id());
      } catch (Exception var5) {
         current = null;
         this.dao.deleteLedCabinet(info.getParent_device_id(), info.getCabinet_group_id(), info.getCabinet_id());
      }

      if (current == null && info.getCabinet_id() != null && info.getCabinet_id() > 0L && info.getCabinet_group_id() != null && info.getCabinet_group_id() > 0L) {
         result = this.dao.addLedCabinet(info);
      } else {
         result = this.dao.setLedCabinet(info);
      }

      return result;
   }

   public boolean addLedCabinet(List ledCabinets) throws SQLException {
      return this.dao.addLedCabinetList(ledCabinets);
   }

   public boolean addLedCabinet(LedCabinet ledCabinet) throws SQLException {
      return this.dao.addLedCabinet(ledCabinet);
   }

   public boolean deleteLedCabinets(String parentDeviceId) throws SQLException {
      return this.dao.deleteLedCabinets(parentDeviceId);
   }

   public int getLedCabinetCount(String parentDeviceId) throws SQLException {
      return this.dao.getLedCabinetListCount(parentDeviceId);
   }

   public int getLedCabinetCount(String parentDeviceId, Long groupId) throws SQLException {
      return this.dao.getLedCabinetListCount(parentDeviceId, groupId);
   }

   public List getLedCabinetPagedList(int startPos, int pageSize, Map map) throws SQLException {
      return this.dao.getLedCabinetPagedList(startPos, pageSize, map);
   }

   public LedCabinet getLedCabinet(String parentDeviceId, Long cabinetGroupId, Long cabinetId) throws SQLException {
      return this.dao.getLedCabinet(parentDeviceId, cabinetGroupId, cabinetId);
   }

   public boolean setLedCabinetGroupInfo(String parentDeviceId, Long cabinetGroupId, String groupResolution, Long positionX, Long positionY) throws SQLException {
      return this.dao.setLedCabinetGroupInfo(parentDeviceId, cabinetGroupId, groupResolution, positionX, positionY);
   }

   public int getErrorLedCabinetCntByGroupId(String parentDeviceId, Long groupId) throws SQLException {
      return this.dao.getErrorLedCabinetCntByGroupId(parentDeviceId, groupId);
   }

   public int getLedCabinetCountByGroup(String parentDeviceId, Long groupId) throws SQLException {
      return this.dao.getLedCabinetCountByGroup(parentDeviceId, groupId);
   }
}
