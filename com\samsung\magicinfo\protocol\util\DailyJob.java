package com.samsung.magicinfo.protocol.util;

import com.samsung.common.cache.CacheFactory;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.StatisticsConstants;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.FileUtils;
import com.samsung.magicinfo.auth.security.TokenUtils;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.kpi.manager.KpiSummaryManager;
import com.samsung.magicinfo.framework.kpi.manager.KpiSummaryManagerImpl;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramFile;
import com.samsung.magicinfo.framework.scheduler.manager.ProgramFileManager;
import com.samsung.magicinfo.framework.scheduler.manager.ProgramFileManagerImpl;
import com.samsung.magicinfo.framework.setup.manager.NotificationHistoryInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManager;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManagerImpl;
import com.samsung.magicinfo.framework.statistics.dao.NewContentFrequencyStatisticsForChartDao;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserLdapSyncInfo;
import com.samsung.magicinfo.framework.user.manager.UserLdapSyncInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserPasswordManager;
import com.samsung.magicinfo.framework.user.manager.UserPasswordManagerImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.util.dao.DatabaseCleaningDao;
import com.samsung.magicinfo.service.statistics.DeviceStatisticsDownloadService;
import com.samsung.magicinfo.service.user.LDAPService;
import java.io.File;
import java.io.FilenameFilter;
import java.io.IOException;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import jodd.util.StringUtil;
import org.apache.logging.log4j.Logger;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

public class DailyJob implements Job {
   Logger logger = LoggingManagerV2.getLogger(DailyJob.class);

   public DailyJob() {
      super();
   }

   public void execute(JobExecutionContext jobexecutioncontext) throws JobExecutionException {
      this.logger.error("[MagicInfo_DailyJob] Start DailyJob");

      try {
         this.checkDailyLicense();
      } catch (Exception var18) {
         this.logger.error("", var18);
      }

      try {
         this.cleanDatabase();
      } catch (Exception var17) {
         this.logger.error("", var17);
      }

      try {
         this.deletePopFiles();
      } catch (Exception var16) {
         this.logger.error("", var16);
      }

      try {
         this.sendExpirationDeviceAlarm();
      } catch (Exception var15) {
         this.logger.error("", var15);
      }

      ServerSetupInfo serverSetup;
      try {
         serverSetup = ServerSetupInfoImpl.getInstance();
         Map serverSetUpMap = serverSetup.getServerCommonInfo();
         Boolean ldapSyncEnable = (Boolean)serverSetUpMap.get("ldap_sync_enable");
         Boolean separateSettings = (Boolean)serverSetUpMap.get("ldap_separate_settings");
         if (ldapSyncEnable || separateSettings) {
            this.ldapSync();
         }
      } catch (Exception var19) {
         this.logger.error("", var19);
      }

      try {
         this.removeNotificationhistory();
      } catch (Exception var14) {
         this.logger.error(var14);
      }

      try {
         this.checkPOPFileHistoryTable();
      } catch (Exception var13) {
         this.logger.error("", var13);
      }

      try {
         this.cleanRmDataFile();
      } catch (Exception var12) {
         this.logger.error(var12);
      }

      try {
         this.setIsResetPasswordBatch();
      } catch (Exception var11) {
         this.logger.error(var11);
      }

      try {
         this.deleteUserPasswordBatch();
      } catch (Exception var10) {
         this.logger.error(var10);
      }

      try {
         this.cleanProgramFilesOnCaches();
      } catch (Exception var9) {
         this.logger.error(var9);
      }

      this.logger.info("Start refresh server management info.");
      serverSetup = ServerSetupInfoImpl.getInstance();

      try {
         serverSetup.refreshServerManagementInfo();
      } catch (Exception var8) {
         this.logger.error(var8);
      }

      try {
         this.checkBlockTokenList();
      } catch (Exception var7) {
         this.logger.error("", var7);
      }

      try {
         this.checkDbValidation();
      } catch (Exception var6) {
         this.logger.error(var6);
      }

      KpiSummaryManager kpiSummaryManager = KpiSummaryManagerImpl.getInstance();
      kpiSummaryManager.getSummaryKpiDaily();
      this.deleteExpiredStoredDevice();
      this.deleteProductCodeHistory();
      this.logger.error("[MagicInfo_DailyJob] End DailyJob");
   }

   private void deleteExpiredStoredDevice() {
      try {
         UserInfo userInfo = UserInfoImpl.getInstance();
         userInfo.deleteExpiredUserDevice();
      } catch (Exception var2) {
         this.logger.error(var2.getMessage());
      }

   }

   private void checkDailyLicense() throws Exception {
      SlmLicenseManager slmlicenseMgr = SlmLicenseManagerImpl.getInstance();
      if (CommonConfig.get("e2e.enable") != null && !CommonConfig.get("e2e.enable").equalsIgnoreCase("false")) {
         slmlicenseMgr.checkDBSlmLicenseForE2E();
      } else {
         slmlicenseMgr.checkDBSlmLicense();
         slmlicenseMgr.checkSlmLicenseExpired();
      }

   }

   public Date getBeforeMonth(int month) {
      Calendar beforeMonth = Calendar.getInstance();
      beforeMonth.setTime(new Date());
      beforeMonth.add(2, month * -1);
      return new Date(beforeMonth.getTimeInMillis());
   }

   public boolean isDeletePopFile(File file, int month) {
      Date dBeforeMonth = this.getBeforeMonth(month);
      SimpleDateFormat format = null;
      String fileName = file.getName();
      String[] var6 = StatisticsConstants.arrPopFilePrefixList;
      int var7 = var6.length;

      for(int var8 = 0; var8 < var7; ++var8) {
         String prefix = var6[var8];
         fileName = fileName.replace(prefix, "");
      }

      Date fileDate = null;
      String[] dateFormatList = new String[]{"yyyyMMdd", "yyyy_MM", "yyyy_'M'MM"};
      int[] dateFormatLengthList = new int[]{8, 7, 8};

      for(int i = 0; i < dateFormatList.length; ++i) {
         try {
            format = new SimpleDateFormat(dateFormatList[i]);
            if (fileName.length() >= dateFormatLengthList[i]) {
               fileDate = format.parse(fileName.substring(0, dateFormatLengthList[i]));
               break;
            }
         } catch (Exception var11) {
         }
      }

      if (fileDate == null) {
         return false;
      } else {
         return dBeforeMonth.compareTo(fileDate) > 0;
      }
   }

   public File[] getFileListByRegExp(String dirPath, final String regExp) {
      File directory = new File(dirPath);
      FilenameFilter filter = new FilenameFilter() {
         public Pattern pattern = Pattern.compile(regExp, 2);

         public boolean accept(File dir, String name) {
            return this.pattern.matcher(name).matches();
         }
      };
      File[] files = directory.listFiles(filter);
      if (null == files) {
         return null;
      } else {
         Arrays.sort(files);
         return files;
      }
   }

   public boolean deleteOldPopFiles(String[] arrBackupDirList, int month, String type) {
      String[] arrRegExpList = null;
      if ("backup".equals(type)) {
         arrRegExpList = StatisticsConstants.arrBackupRegExpList;
      } else if ("report".equals(type)) {
         arrRegExpList = StatisticsConstants.arrReportRegExpList;
      }

      boolean result = false;
      String[] var6 = arrBackupDirList;
      int var7 = arrBackupDirList.length;

      for(int var8 = 0; var8 < var7; ++var8) {
         String dirPath = var6[var8];
         String[] var10 = arrRegExpList;
         int var11 = arrRegExpList.length;

         for(int var12 = 0; var12 < var11; ++var12) {
            String regExp = var10[var12];
            File[] files = this.getFileListByRegExp(dirPath, regExp);
            if (null != files) {
               File[] var15 = files;
               int var16 = files.length;

               for(int var17 = 0; var17 < var16; ++var17) {
                  File file = var15[var17];

                  try {
                     result = false;
                     result = this.isDeletePopFile(file, month);
                     if (!result) {
                        break;
                     }

                     FileUtils.deleteFilesRecursively(file);
                     this.logger.info("deletePopBackupFiles : " + file.getName());
                  } catch (Exception var20) {
                     this.logger.error(var20);
                     return false;
                  }
               }
            }
         }
      }

      return true;
   }

   public void deletePopFiles() {
      String CONTENTS_DIR = null;
      int DEFAULT_POP_FILE_DELETION_PERIOD_MONTH = 6;
      int popFileDeletionPeriodMonth = 6;

      try {
         CONTENTS_DIR = CommonConfig.get("CONTENTS_HOME");
         if (CONTENTS_DIR == null) {
            return;
         }

         if ("0".equalsIgnoreCase(CommonConfig.get("daily.file.clean.period.mon.pop"))) {
            return;
         }

         popFileDeletionPeriodMonth = CommonUtils.getConfigNumber("daily.file.clean.period.mon.pop", DEFAULT_POP_FILE_DELETION_PERIOD_MONTH);
         if (popFileDeletionPeriodMonth < 1 || popFileDeletionPeriodMonth > 24) {
            popFileDeletionPeriodMonth = DEFAULT_POP_FILE_DELETION_PERIOD_MONTH;
         }
      } catch (ConfigException var10) {
         this.logger.error("", var10);
      }

      String popBackupDir = CONTENTS_DIR + File.separator + "pop" + File.separator + "backup";
      String evtBackupDir = CONTENTS_DIR + File.separator + "evt" + File.separator + "backup";
      String popReportDir = CONTENTS_DIR + File.separator + "pop" + File.separator + "report";
      String evtReportDir = CONTENTS_DIR + File.separator + "evt" + File.separator + "report";
      String[] arrBackupDirList = new String[]{popBackupDir, evtBackupDir};
      String[] arrReportDirList = new String[]{popReportDir, evtReportDir};
      this.deleteOldPopFiles(arrBackupDirList, popFileDeletionPeriodMonth, "backup");
      this.deleteOldPopFiles(arrReportDirList, popFileDeletionPeriodMonth, "report");
   }

   private void cleanDatabase() {
      DatabaseCleaningDao dbCleaningDao = new DatabaseCleaningDao();
      this.logger.error("[MagicInfo_DailyJob] cleanDatabase");
      int statisticDay = 30;

      try {
         statisticDay = Integer.valueOf(CommonConfig.get("daily.db.clean.period.day.pop"));
      } catch (NumberFormatException var37) {
         this.logger.error("[MagicInfo_DailyJob] Cannot get daily.db.clean.period.day.pop from config.properties - set to 30 days (default)");
      } catch (ConfigException var38) {
         this.logger.error("[MagicInfo_DailyJob] Cannot get daily.db.clean.period.day.pop from config.properties - set to 30 days (default)");
      }

      int month = 1;

      try {
         month = Integer.valueOf(CommonConfig.get("daily.db.clean.period.mon.pop"));
      } catch (NumberFormatException var35) {
         this.logger.error("[MagicInfo_DailyJob] Cannot get daily.db.clean.period.mon.pop from config.properties - set to 1 month (default)");
      } catch (ConfigException var36) {
         this.logger.error("[MagicInfo_DailyJob] Cannot get daily.db.clean.period.mon.pop from config.properties - set to 1 month (default)");
      }

      this.logger.error("[MagicInfo_DailyJob] 1. cleanStatistics Tables & Backup csv clean statistics day=" + statisticDay + " clean statistics month=" + month);

      try {
         dbCleaningDao.cleanPopDayHourSecondByDay(statisticDay);
         this.logger.error("[MagicInfo_DailyJob] Success -  DB cleaning- POP DAY, HOUR, SECOND");
      } catch (SQLException var34) {
         this.logger.error("[MagicInfo_DailyJob] Cannot clean DB - POP DAY, HOUR, SECOND");
      }

      try {
         dbCleaningDao.cleanPopMonthByMonth(month);
         this.logger.error("[MagicInfo_DailyJob] Success -  DB cleaning- POP MONTH");
      } catch (SQLException var33) {
         this.logger.error("[MagicInfo_DailyJob] Cannot clean DB - POP MONTH");
      }

      try {
         dbCleaningDao.cleanAmsDayHistory(statisticDay);
         this.logger.error("[MagicInfo_DailyJob] Success -  DB cleaning- AMS DAY, HOUR ");
      } catch (SQLException var32) {
         this.logger.error("[MagicInfo_DailyJob] Cannot clean DB - AMS DAY, HOUR ");
      }

      try {
         dbCleaningDao.cleanAmsMonthHistory(month);
         this.logger.error("[MagicInfo_DailyJob] Success -  DB cleaning- AMS MONTH");
      } catch (SQLException var31) {
         this.logger.error("[MagicInfo_DailyJob] Cannot clean DB - AMS MONTH");
      }

      try {
         int cleanPeriodYear = 1;
         dbCleaningDao.cleanAmsYearHistory(cleanPeriodYear);
         this.logger.error("[MagicInfo_DailyJob] Success -  DB cleaning- AMS YEAR");
      } catch (SQLException var30) {
         this.logger.error("[MagicInfo_DailyJob] Cannot clean DB - AMS YEAR");
      }

      String CONTENTS_DIR = null;

      try {
         CONTENTS_DIR = CommonConfig.get("CONTENTS_HOME");
      } catch (ConfigException var29) {
         this.logger.error("", var29);
      }

      String popBackupDir = CONTENTS_DIR + File.separator + "pop" + File.separator + "backup";
      String amsBackupDir = CONTENTS_DIR + File.separator + "face" + File.separator + "backup";
      DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
      Calendar cal = Calendar.getInstance();
      cal.setTime(new Date());
      cal.set(5, cal.get(5) - statisticDay);
      Date date = cal.getTime();
      String deleteDate = dateFormat.format(date);
      int deleteCount = 0;

      try {
         deleteCount = FileUtils.deleteStatisticsDir(popBackupDir, date, "POP");
         this.logger.error("[MagicInfo_DailyJob] Success -  Delete POP Backup Files Date Before: " + deleteDate + " deleteCount: " + deleteCount);
      } catch (Exception var28) {
         this.logger.error("[MagicInfo_DailyJob] Cannot Delete POP Backup Files Date Before: " + deleteDate + " deleteCount: " + deleteCount);
      }

      try {
         deleteCount = FileUtils.deleteStatisticsDir(amsBackupDir, date, "AMS");
         this.logger.error("[MagicInfo_DailyJob] Success -  Delete AMS Backup Files Date Before: " + deleteDate + " deleteCount: " + deleteCount);
      } catch (Exception var27) {
         this.logger.error("[MagicInfo_DailyJob] Cannot Delete AMS Backup Files Date Before: " + deleteDate + " deleteCount: " + deleteCount);
      }

      try {
         deleteCount = FileUtils.deleteStatisticsDir(popBackupDir, date, "VWL");
         this.logger.error("[MagicInfo_DailyJob] Success -  Delete VWL Backup Files Date Before: " + deleteDate + " deleteCount: " + deleteCount);
      } catch (Exception var26) {
         this.logger.error("[MagicInfo_DailyJob] Cannot Delete VWL Backup Files Date Before: " + deleteDate + " deleteCount: " + deleteCount);
      }

      int day = 30;

      try {
         day = Integer.valueOf(CommonConfig.get("daily.db.clean.period.day.log"));
      } catch (NumberFormatException var24) {
         this.logger.error("[MagicInfo_DailyJob] Cannot get daily.db.clean.period.day.log from config.properties - set to 30 days (default)");
      } catch (ConfigException var25) {
         this.logger.error("[MagicInfo_DailyJob] Cannot get daily.db.clean.period.day.log from config.properties - set to 30 days (default)");
      }

      this.logger.error("[MagicInfo_DailyJob] 2. cleanLog day=" + day);

      try {
         dbCleaningDao.cleanContentPlayHistory(day);
         dbCleaningDao.cleanRmInfoReservationHistory(day);
         dbCleaningDao.cleanRmLogActStatus(day);
         dbCleaningDao.cleanRmLogServiceStatus(day);
         dbCleaningDao.cleanNocHistory(day);
      } catch (SQLException var23) {
         this.logger.error("[MagicInfo_DailyJob] Cannot clean DB - history or log");
      }

      month = 1;

      try {
         month = Integer.valueOf(CommonConfig.get("daily.db.clean.period.mon.dev_conn_history"));
      } catch (NumberFormatException var21) {
         this.logger.error("[MagicInfo_DailyJob] Cannot get daily.db.clean.period.mon.dev_conn_history from config.properties - set to 1 month (default)");
      } catch (ConfigException var22) {
         this.logger.error("[MagicInfo_DailyJob] Cannot get daily.db.clean.period.mon.dev_conn_history from config.properties - set to 1 month (default)");
      }

      this.logger.error("[MagicInfo_DailyJob] 3. clean_Device_Connected_Disconnected_History. month=" + month);

      try {
         dbCleaningDao.cleanDevConnHistory(month);
         this.logger.error("[MagicInfo_DailyJob] Success -  DB cleaning- clean_Connected_Disconnected_History");
      } catch (SQLException var20) {
         this.logger.error("[MagicInfo_DailyJob] Cannot clean DB - clean_Connected_Disconnected_History");
      }

      day = 30;

      try {
         day = Integer.valueOf(CommonConfig.get("daily.db.clean.period.mon.dev_fault_alarm_history"));
      } catch (NumberFormatException var18) {
         this.logger.error("[MagicInfo_DailyJob] Cannot get daily.db.clean.period.mon.dev_conn_history from config.properties - set to 30 days (default)");
      } catch (ConfigException var19) {
         this.logger.error("[MagicInfo_DailyJob] Cannot get daily.db.clean.period.mon.dev_conn_history from config.properties - set to 30 days (default)");
      }

      try {
         dbCleaningDao.cleanDevFaultAlarmHistory(day);
         this.logger.error("[MagicInfo_DailyJob] Success -  DB cleaning- cleanDevFaultAlarmHistory");
      } catch (SQLException var17) {
         this.logger.error("[MagicInfo_DailyJob] Cannot clean DB - cleanDevFaultAlarmHistory");
      }

      day = 30;

      try {
         day = Integer.valueOf(CommonConfig.get("daily.db.clean.period.mon.log_service_status"));
      } catch (NumberFormatException var15) {
         this.logger.error("[MagicInfo_DailyJob] Cannot get daily.db.clean.period.mon.dev_conn_history from config.properties - set to 30 days (default)");
      } catch (ConfigException var16) {
         this.logger.error("[MagicInfo_DailyJob] Cannot get daily.db.clean.period.mon.dev_conn_history from config.properties - set to 30 days (default)");
      }

      try {
         dbCleaningDao.cleanLogServiceStatus(day);
         this.logger.error("[MagicInfo_DailyJob] Success -  DB cleaning- cleanLogServiceStatus");
      } catch (SQLException var14) {
         this.logger.error("[MagicInfo_DailyJob] Cannot clean DB - cleanLogServiceStatus");
      }

   }

   public void sendExpirationDeviceAlarm() {
      String message = "device_expiration_alarm";
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      List deviceList = null;

      try {
         deviceList = deviceInfo.getExpiredDeviceList(7);
      } catch (SQLException var7) {
         this.logger.info(var7.getMessage());
      }

      if (deviceList != null && deviceList.size() != 0) {
         StringBuffer mailContent = new StringBuffer("");
         Iterator var5 = deviceList.iterator();

         while(var5.hasNext()) {
            DeviceMonitoring device = (DeviceMonitoring)var5.next();
            mailContent.append("------------------------------------------------------------------------------------------------------------------------<br>Name        : " + device.getDevice_name() + "<br>Model Name  : " + device.getDevice_model_name() + "<br>Device ID   : " + device.getDevice_id() + "<br>IP  Address : " + device.getIp_address() + "<br>Group Name  : " + device.getGroup_name() + "<br>Expiration Date : " + device.getExpiration_date() + "<br>");
         }

         MailUtil.makeMail(message, mailContent.toString(), "admin", 0L, "");
      }
   }

   private void ldapSync() {
      try {
         UserLdapSyncInfo userLdapSyncInfo = UserLdapSyncInfoImpl.getInstance();
         ServerSetupInfo serverSetupInfo = ServerSetupInfoImpl.getInstance();
         Boolean separateSettings = serverSetupInfo.isLdapSeparateSettings();
         Long[] ids = userLdapSyncInfo.getUserLdapSyncEnableIds(separateSettings);
         LDAPService.startUserLdapSync(ids);
      } catch (Exception var5) {
         this.logger.error("[MagicInfo_DailyJob] issue for ldapSync, error: " + var5.getMessage());
      }

   }

   private void removeNotificationhistory() throws Exception {
      ServerSetupInfo serverSetupInfo = ServerSetupInfoImpl.getInstance();
      NotificationHistoryInfoImpl notificationHistoryInfo = NotificationHistoryInfoImpl.getInstance();

      try {
         notificationHistoryInfo.deleteGarbageFiles();
      } catch (Exception var7) {
         this.logger.error(var7);
      }

      Map serverInfoMap = serverSetupInfo.getServerInfoByOrgId(0L);
      long period = 0L;
      if (serverInfoMap.get("NOTIFICATION_HISTORY_RETENTION_PERIOD") != null) {
         period = (Long)serverInfoMap.get("NOTIFICATION_HISTORY_RETENTION_PERIOD");
      }

      if (period > 0L) {
         Timestamp retentionTime = new Timestamp(System.currentTimeMillis() - period * 24L * 60L * 60L * 1000L);
         notificationHistoryInfo.deleteHistory(retentionTime);
      }

   }

   private void checkPOPFileHistoryTable() throws Exception {
      NewContentFrequencyStatisticsForChartDao content_freq_dao = new NewContentFrequencyStatisticsForChartDao();
      Date date = new Date();
      Calendar cal = Calendar.getInstance();
      cal.setTime(date);
      String year = String.valueOf(cal.get(1));
      String month = "";
      if (cal.get(2) <= 8) {
         month = "0" + String.valueOf(cal.get(2) + 1);
      } else {
         month = String.valueOf(cal.get(2) + 1);
      }

      boolean isExist = content_freq_dao.isExistPOPFileHistoryMappingInfo(year + "-" + month);
      if (!isExist) {
         try {
            int result = content_freq_dao.cntPOPFileHistoryMappingInfo();
            String tableNo = "";
            if (result >= 6) {
               Map resultMap = content_freq_dao.getOldestPOPFileHistoryMappingInfo();
               tableNo = (String)resultMap.get("TABLE_NO");
               String tableDate = (String)resultMap.get("TABLE_DATE");
               this.createPOPBackupFile(tableNo, tableDate);
               content_freq_dao.deleteOldestPOPFileHistoryTable(tableNo);
               content_freq_dao.deleteOldestPOPFileMappingInfo(tableNo);
            } else {
               tableNo = content_freq_dao.getNewPOPFileMappingInfo();
            }

            content_freq_dao.insertNewPOPFileMappingInfo(tableNo, year + "-" + month);
            this.logger.error("[MagicInfo_DailyJob] Success to create POP File History Table No: " + tableNo + " Year: " + year + " Month: " + month);
            content_freq_dao.initPOPFileHistoryTable(tableNo);
         } catch (SQLException var11) {
            this.logger.error("[MagicInfo_DailyJob] Fail to create POP File History Table");
         }
      }

   }

   private void createPOPBackupFile(String tableNo, String tableDate) throws Exception {
      NewContentFrequencyStatisticsForChartDao content_freq_dao = new NewContentFrequencyStatisticsForChartDao();
      List resultList = content_freq_dao.getMonthlyHistoryByTableNo(tableNo);
      int dataListSize = resultList.size();
      Object[] dataList = new Object[dataListSize];

      for(int index = 0; index < dataListSize; ++index) {
         dataList[index] = resultList.get(index);
      }

      String upload_path = CommonConfig.get("UPLOAD_HOME").replace('/', File.separatorChar);
      String fileName = upload_path + File.separator + CommonConfig.get("POP_LOG_DIR") + File.separator + "report" + File.separator + "POP_RECEIVE_" + tableDate + ".xls";
      String[] columnNames = new String[]{"device_id", "DAY1", "DAY2", "DAY3", "DAY4", "DAY5", "DAY6", "DAY7", "DAY8", "DAY9", "DAY10", "DAY11", "DAY12", "DAY13", "DAY14", "DAY15", "DAY16", "DAY17", "DAY18", "DAY19", "DAY20", "DAY21", "DAY22", "DAY23", "DAY24", "DAY25", "DAY26", "DAY27", "DAY28", "DAY29", "DAY30", "DAY31"};
      String[] fieldNames = new String[]{"device_id", "DAY1", "DAY2", "DAY3", "DAY4", "DAY5", "DAY6", "DAY7", "DAY8", "DAY9", "DAY10", "DAY11", "DAY12", "DAY13", "DAY14", "DAY15", "DAY16", "DAY17", "DAY18", "DAY19", "DAY20", "DAY21", "DAY22", "DAY23", "DAY24", "DAY25", "DAY26", "DAY27", "DAY28", "DAY29", "DAY30", "DAY31"};
      final Map dataMap = new HashMap();
      dataMap.put("fileName", fileName);
      dataMap.put("sheetName", tableDate);
      dataMap.put("columnNames", columnNames);
      dataMap.put("fieldNames", fieldNames);
      dataMap.put("dataList", dataList);
      final DeviceStatisticsDownloadService downloadService = new DeviceStatisticsDownloadService();
      Thread thread = new Thread(new Runnable() {
         public void run() {
            try {
               downloadService.saveExcelFile(dataMap);
            } catch (IOException var2) {
               DailyJob.this.logger.error("", var2);
            }

         }
      });
      thread.start();
   }

   private void cleanRmDataFile() {
      try {
         String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar);
         String RM_MONITORING_DIR = CommonConfig.get("RM_MONITORING_DIR");
         if (StringUtil.isEmpty(RM_MONITORING_DIR)) {
            RM_MONITORING_DIR = "rm_monitoring";
         }

         int cleanDay = 1;

         try {
            cleanDay = Integer.valueOf(CommonConfig.get("rm.data.clean.period"));
         } catch (NumberFormatException var9) {
            this.logger.error("[MagicInfo_DailyJob] Cannot get rm.data.clean.period from config.properties - set to 1 day (default)");
         }

         Calendar cal = Calendar.getInstance();
         cal.setTime(new Date());
         cal.set(5, cal.get(5) - cleanDay);
         Date date = cal.getTime();
         String dirPath = CONTENTS_HOME + File.separator + RM_MONITORING_DIR;

         try {
            FileUtils.cleanDir(dirPath, date);
         } catch (Exception var8) {
            this.logger.error("[MagicInfo_DailyJob] Clean Dir " + var8);
         }
      } catch (ConfigException var10) {
         this.logger.error("[MagicInfo_DailyJob] cleanRmDataFile " + var10);
      }

   }

   private void setIsResetPasswordBatch() {
      try {
         UserInfo userInfo = UserInfoImpl.getInstance();
         userInfo.setIsResetPasswordBatch();
      } catch (Exception var2) {
         this.logger.error("[MagicInfo_DailyJob] setIsResetPasswordBatch " + var2);
      }

   }

   private void deleteUserPasswordBatch() {
      try {
         UserPasswordManager userPassword = UserPasswordManagerImpl.getInstance();
         userPassword.deleteUserPasswordHistoryBatch();
      } catch (Exception var2) {
         this.logger.error("[MagicInfo_DailyJob] deleteUserPasswordBatch " + var2);
      }

   }

   private void deleteProductCodeHistory() {
      try {
         ContentInfo contentInfo = ContentInfoImpl.getInstance();
         String date = LocalDate.now().minusWeeks(2L).toString() + " 00:00:00";
         contentInfo.deleteProductCodeHistoryByDate(date);
      } catch (Exception var3) {
         this.logger.error(var3);
      }

   }

   private void cleanProgramFilesOnCaches() {
      ProgramFileManager programFileManager = ProgramFileManagerImpl.getInstance();
      this.logger.warn("[DailyJob] clean program files on cache");

      try {
         boolean isUpdate = false;
         Calendar calendar = Calendar.getInstance();
         calendar.add(5, -5);
         Map programFiles = programFileManager.getProgramFiles();
         if (programFiles != null) {
            Iterator keys = programFiles.keySet().iterator();

            while(keys.hasNext()) {
               String programId = (String)keys.next();
               ProgramFile programFile = (ProgramFile)programFiles.get(programId);
               if (programFile != null && !calendar.getTime().before(programFile.getCreateTime())) {
                  this.logger.warn("[DailyJob] remove program File on Cache " + programId);
                  programFiles.remove(programId);
                  isUpdate = true;
               }
            }

            if (isUpdate) {
               this.logger.warn("[DailyJob] update program Files on Cache ");
               programFileManager.setProgramFiles(programFiles);
            }
         }
      } catch (Exception var8) {
         this.logger.error("[DailJob] fail to clean program files on cache");
      }

   }

   public void checkBlockTokenList() {
      Object obj = null;

      try {
         TokenUtils tokenUtils = new TokenUtils();
         obj = CacheFactory.getCache().get("BLOCK_TOKEN_LIST");
         if (obj == null) {
            return;
         }

         Set tokenList = (Set)obj;
         Set newTokenList = new HashSet();
         Iterator var5 = tokenList.iterator();

         while(var5.hasNext()) {
            String token = (String)var5.next();
            if (!tokenUtils.isExpiredToken(token)) {
               newTokenList.add(token);
            }
         }

         CacheFactory.getCache().set("BLOCK_TOKEN_LIST", newTokenList);
      } catch (Exception var7) {
         this.logger.error(var7);
      }

   }

   private void checkDbValidation() throws Exception {
      ServerSetupInfo serverSetupInfo = ServerSetupInfoImpl.getInstance();
      serverSetupInfo.checkCheckingItemsFromJson();
      this.logger.info("Checking database integrity");
      ServerSetupInfo serverSetup = ServerSetupInfoImpl.getInstance();
      String fileName = "base_schema_" + CommonConfig.get("wsrm.dbVendor") + "_" + CommonConfig.get("openAPI.Version").split("\\.")[0] + ".json";
      String filePath = CommonConfig.get("UPLOAD_HOME") + File.separator + "validation";
      List baseTables = serverSetup.readTableSchema(filePath, fileName);
      List targetTables = serverSetup.createTableSchema();
      if (baseTables != null && targetTables != null) {
         Map compareResult = serverSetup.compareTableSchema(baseTables, targetTables);
         Map serverHealthMap = (HashMap)CacheFactory.getCache().get("SERVER_HEALTH_MAP");
         if (serverHealthMap == null) {
            serverHealthMap = new HashMap();
         }

         if (!compareResult.isEmpty()) {
            this.logger.info("Database schema has some issues.");
            serverHealthMap.put("db_integrity", compareResult);
         } else {
            this.logger.info("Database schema has no issues.");
            serverHealthMap.put("db_integrity", (Object)null);
         }

         CacheFactory.getCache().set("SERVER_HEALTH_MAP", serverHealthMap);
      } else {
         this.logger.info("Reference schema is missing : " + fileName);
      }

   }
}
