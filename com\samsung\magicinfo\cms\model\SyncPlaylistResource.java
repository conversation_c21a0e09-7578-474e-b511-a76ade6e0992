package com.samsung.magicinfo.cms.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@JsonInclude(Include.NON_NULL)
public class SyncPlaylistResource {
   @ApiModelProperty(
      example = "32FA85B4-2389-476C-845A-0FC6F1D10D81"
   )
   @Pattern(
      regexp = "^[0-9A-Fa-f]{8}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{12}$|",
      message = "[SyncPlaylistResource][playlistId] Not UUID pattern."
   )
   private String playlistId = "";
   @ApiModelProperty(
      example = "1",
      required = true
   )
   @Pattern(
      regexp = "^[0-9]*$",
      message = "[SyncPlaylistResource][groupId] Only number is available."
   )
   private String groupId = "";
   @ApiModelProperty(
      example = "New Playlist",
      required = true
   )
   @Size(
      max = 60,
      message = "[SyncPlaylistResource][playlistName] max size is 60."
   )
   private String playlistName = "";
   @ApiModelProperty(
      example = "1",
      required = true
   )
   @Min(
      value = 0L,
      message = "[SyncPlaylistResource][shareFlag] allowable value for shareFlag are 0 or 1."
   )
   @Max(
      value = 1L,
      message = "[SyncPlaylistResource][shareFlag] allowable value for shareFlag are 0 or 1."
   )
   private int shareFlag = 1;
   @ApiModelProperty(
      example = "SPLAYER",
      required = true
   )
   @NotEmpty(
      message = "[SyncPlaylistResource][deviceType] deviceType can not be empty."
   )
   private String deviceType = "";
   @ApiModelProperty(
      example = "3.0",
      required = true
   )
   @NotEmpty(
      message = "[SyncPlaylistResource][deviceTypeVersion] deviceTypeVersion can not be empty."
   )
   private String deviceTypeVersion = "";
   @ApiModelProperty(
      example = "-",
      required = true
   )
   @Size(
      max = 200,
      message = "[SyncPlaylistResource][metaData] max size is 200."
   )
   private String metaData = "";
   @ApiModelProperty(
      example = "PREMIUM",
      required = true
   )
   private String productType = "PREMIUM";
   @ApiModelProperty(
      example = "1"
   )
   private Long defaultContentDuration = 0L;
   private String tagList = "";
   @ApiModelProperty(
      example = "2",
      required = true
   )
   @Min(
      value = 1L,
      message = "[SyncPlaylistResource][contentCountInEachGroup] minimum value is 1."
   )
   private int contentCountInEachGroup = 0;
   @Valid
   private List contentGroups;

   public SyncPlaylistResource() {
      super();
   }

   public String getPlaylistId() {
      return this.playlistId;
   }

   public void setPlaylistId(String playlistId) {
      this.playlistId = playlistId;
   }

   public String getGroupId() {
      return this.groupId;
   }

   public void setGroupId(String groupId) {
      this.groupId = groupId;
   }

   public String getPlaylistName() {
      return this.playlistName;
   }

   public void setPlaylistName(String playlistName) {
      this.playlistName = playlistName;
   }

   public int getShareFlag() {
      return this.shareFlag;
   }

   public void setShareFlag(int shareFlag) {
      this.shareFlag = shareFlag;
   }

   public String getDeviceType() {
      return this.deviceType;
   }

   public void setDeviceType(String deviceType) {
      this.deviceType = deviceType;
   }

   public String getDeviceTypeVersion() {
      return this.deviceTypeVersion;
   }

   public void setDeviceTypeVersion(String deviceTypeVersion) {
      this.deviceTypeVersion = deviceTypeVersion;
   }

   public String getMetaData() {
      return this.metaData;
   }

   public void setMetaData(String metaData) {
      this.metaData = metaData;
   }

   public String getProductType() {
      return this.productType;
   }

   public void setProductType(String productType) {
      this.productType = productType;
   }

   public Long getDefaultContentDuration() {
      return this.defaultContentDuration;
   }

   public void setDefaultContentDuration(Long defaultContentDuration) {
      this.defaultContentDuration = defaultContentDuration;
   }

   public String getTagList() {
      return this.tagList;
   }

   public void setTagList(String tagList) {
      this.tagList = tagList;
   }

   public int getContentCountInEachGroup() {
      return this.contentCountInEachGroup;
   }

   public void setContentCountInEachGroup(int contentCountInEachGroup) {
      this.contentCountInEachGroup = contentCountInEachGroup;
   }

   public List getContentGroups() {
      return this.contentGroups;
   }

   public void setContentGroups(List contentGroups) {
      this.contentGroups = contentGroups;
   }
}
