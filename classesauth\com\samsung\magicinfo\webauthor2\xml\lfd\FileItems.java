package com.samsung.magicinfo.webauthor2.xml.lfd;

import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.NONE)
@XmlRootElement(name = "FileItems")
public class FileItems {
  @XmlElement(name = "FileItem")
  private List<FileItem> fileItems;
  
  public List<FileItem> getFileItems() {
    return this.fileItems;
  }
  
  public void setFileItems(List<FileItem> fileItem) {
    this.fileItems = fileItem;
  }
}
