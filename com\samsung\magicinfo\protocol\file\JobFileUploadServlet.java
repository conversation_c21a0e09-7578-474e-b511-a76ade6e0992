package com.samsung.magicinfo.protocol.file;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.exception.ExceptionCode;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.device.job.manager.JobManager;
import com.samsung.magicinfo.framework.device.job.manager.JobManagerImpl;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.Logger;

public class JobFileUploadServlet extends HttpServlet {
   private static final long serialVersionUID = 8746018144105858231L;
   private Logger logger = LoggingManagerV2.getLogger(JobFileUploadServlet.class);

   public JobFileUploadServlet() {
      super();
   }

   public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");
      this.doPost(request, response);
   }

   public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");

      try {
         String JOBS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "jobs_home";
         String jobId = StrUtils.nvl(request.getHeader("JobId"));
         String fileID = StrUtils.nvl(request.getHeader("FileID"));
         File fileJobHome = SecurityUtils.getSafeFile(JOBS_HOME);
         if (!fileJobHome.exists()) {
            boolean fSuccess = fileJobHome.mkdir();
            if (!fSuccess) {
               this.logger.error("mkdir Fail");
            }
         }

         String filePath = JOBS_HOME + File.separator + fileID;
         File file = SecurityUtils.getSafeFile(filePath);
         if (!file.exists()) {
            boolean fSuccess = file.mkdir();
            if (!fSuccess) {
               this.logger.error("mkdir Fail");
            }
         }

         byte[] buf = new byte[1048576];
         boolean var10 = false;

         try {
            InputStream is = request.getInputStream();
            Throwable var12 = null;

            try {
               FileOutputStream fos = new FileOutputStream(SecurityUtils.directoryTraversalChecker(filePath + File.separator + jobId + ".zip", (String)null), true);
               Throwable var14 = null;

               try {
                  int binaryRead;
                  try {
                     while((binaryRead = is.read(buf)) != -1) {
                        fos.write(buf, 0, binaryRead);
                     }
                  } catch (Throwable var41) {
                     var14 = var41;
                     throw var41;
                  }
               } finally {
                  if (fos != null) {
                     if (var14 != null) {
                        try {
                           fos.close();
                        } catch (Throwable var40) {
                           var14.addSuppressed(var40);
                        }
                     } else {
                        fos.close();
                     }
                  }

               }
            } catch (Throwable var43) {
               var12 = var43;
               throw var43;
            } finally {
               if (is != null) {
                  if (var12 != null) {
                     try {
                        is.close();
                     } catch (Throwable var39) {
                        var12.addSuppressed(var39);
                     }
                  } else {
                     is.close();
                  }
               }

            }
         } catch (Exception var45) {
            this.logger.error(var45);
         }

         JobManager jobMgr = JobManagerImpl.getInstance();
         boolean retVal = jobMgr.setFileUploadStatusAsTrue(fileID);
         if (!retVal) {
            response.sendError(500, ExceptionCode.HTTP500[2]);
         }
      } catch (Exception var46) {
         response.sendError(600, var46.toString());
         this.logger.error(var46);
      }

   }
}
