package com.samsung.magicinfo.protocol.http;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DisasterAlertStatusEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManager;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManagerImpl;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.awt.image.BufferedImage;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.InetAddress;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.util.Iterator;
import java.util.List;
import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.Logger;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

public class DisasterAlertServlet extends HttpServlet {
   private static final long serialVersionUID = -3161866407287043159L;
   private Logger logger = LoggingManagerV2.getLogger(DisasterAlertServlet.class);
   static final int BUFFER_SIZE = 4096;

   public DisasterAlertServlet() {
      super();
   }

   public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");
      String cmd = request.getParameter("cmd");
      String deviceId = request.getParameter("deviceId");
      String type = "HTML";
      if (request.getParameter("type") != null) {
         type = request.getParameter("type");
      }

      BufferedOutputStream os = null;
      File m_file = null;
      FileInputStream fileIS = null;
      FileChannel fileChannel = null;
      if (cmd != null && cmd.equalsIgnoreCase("PLAY")) {
         String fileoffset = null;
         String fileName = null;
         String fullPath = null;
         String save_dir = null;

         try {
            save_dir = CommonConfig.get("UPLOAD_HOME") + File.separator + "disaster_content_home" + File.separator;
         } catch (ConfigException var84) {
            this.logger.error("", var84);
         }

         fileName = StrUtils.nvl(request.getParameter("file_name"));
         if (save_dir != null && fileName != null) {
            fullPath = save_dir + fileName;
            fullPath = fullPath.replace('/', File.separatorChar);
            response.setHeader("Content-Disposition", "filename=" + fullPath + ";");
            response.setHeader("Content-Type", "application/octet-stream; charset=UTF-8");
            if (type.equalsIgnoreCase("IMAGE")) {
               System.out.println("File Type : IMAGE");
               response.setContentType("image/png");
               ServletOutputStream out = response.getOutputStream();

               try {
                  fileIS = new FileInputStream(fullPath);
                  String extensionName = fileName.substring(fileName.lastIndexOf(46) + 1);
                  Iterator readers = ImageIO.getImageReadersBySuffix(extensionName);
                  if (readers.hasNext()) {
                     ImageReader imageReader = (ImageReader)readers.next();
                     ImageInputStream imageInputStream = ImageIO.createImageInputStream(fileIS);
                     imageReader.setInput(imageInputStream, false);
                     int num = imageReader.getNumImages(true);
                     BufferedImage[] images = new BufferedImage[num];

                     for(int i = 0; i < num; ++i) {
                        images[i] = imageReader.read(i);
                     }

                     ImageIO.write(images[0], "png", out);
                  }
               } catch (IOException var92) {
                  this.logger.error("", var92);
               } finally {
                  try {
                     if (fileIS != null) {
                        fileIS.close();
                     }
                  } catch (IOException var82) {
                     this.logger.error("", var82);
                  }

               }
            } else {
               int binaryRead;
               if (type.equalsIgnoreCase("VIDEO")) {
                  System.out.println("File Extention : mp4");
                  response.setContentType("video/mp4");
                  System.out.println("File Extention : mp4");

                  try {
                     os = new BufferedOutputStream(response.getOutputStream());
                     m_file = SecurityUtils.getSafeFile(fullPath);
                     fileIS = new FileInputStream(m_file);
                     fileChannel = fileIS.getChannel();
                     if (fileoffset == null) {
                        fileoffset = "0";
                     }

                     long fileOffsetLong = Long.parseLong(fileoffset);
                     if (m_file.length() > 0L) {
                        for(ByteBuffer buf = ByteBuffer.allocate(1024); (binaryRead = fileChannel.read(buf, fileOffsetLong)) != -1; fileOffsetLong += (long)binaryRead) {
                           buf.flip();
                           os.write(buf.array(), 0, binaryRead);
                           buf.clear();
                        }
                     }
                  } catch (IOException var90) {
                     this.logger.error("", var90);
                  } finally {
                     try {
                        if (os != null) {
                           os.close();
                        }

                        if (fileChannel != null) {
                           fileChannel.close();
                        }

                        if (fileIS != null) {
                           fileIS.close();
                        }
                     } catch (IOException var81) {
                        this.logger.error("", var81);
                     }

                  }
               } else {
                  response.setContentType("text/html");

                  try {
                     os = new BufferedOutputStream(response.getOutputStream());
                     fullPath = SecurityUtils.directoryTraversalChecker(fullPath, request.getRemoteAddr());
                     String hostName = CommonConfig.get("download.server.node.name");
                     if (hostName == null) {
                        hostName = InetAddress.getLocalHost().getHostName();
                     }

                     m_file = SecurityUtils.getSafeFile(fullPath);
                     fileIS = new FileInputStream(m_file);
                     fileChannel = fileIS.getChannel();
                     if (fileoffset == null) {
                        fileoffset = "0";
                     }

                     long fileOffsetLong = Long.parseLong(fileoffset);
                     if (m_file.length() > 0L) {
                        for(ByteBuffer buf = ByteBuffer.allocate(1024); (binaryRead = fileChannel.read(buf, fileOffsetLong)) != -1; fileOffsetLong += (long)binaryRead) {
                           buf.flip();
                           os.write(buf.array(), 0, binaryRead);
                           buf.clear();
                        }
                     }

                     String[] tempPath = fileName.split("\\\\");
                     String tempAlertId = tempPath[0];
                     if (deviceId != null) {
                        DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
                        List disasterAlertStatusByDeviceId = deviceMgr.selectDisasterAlertStatusByDeviceId(deviceId);
                        if (disasterAlertStatusByDeviceId.size() > 0) {
                           try {
                              for(int i = 0; i < disasterAlertStatusByDeviceId.size(); ++i) {
                                 String tmpAlertId = ((DisasterAlertStatusEntity)disasterAlertStatusByDeviceId.get(i)).getAlert_id();
                                 String tmpStatus = ((DisasterAlertStatusEntity)disasterAlertStatusByDeviceId.get(i)).getStatus();
                                 if (!tmpAlertId.equalsIgnoreCase(tempAlertId) && tmpStatus.equals("ON-AIR")) {
                                    ((DisasterAlertStatusEntity)disasterAlertStatusByDeviceId.get(i)).setStatus("STOPPED");
                                    deviceMgr.updateDisasterAlertStatus((DisasterAlertStatusEntity)disasterAlertStatusByDeviceId.get(i));
                                 }
                              }
                           } catch (Exception var85) {
                              this.logger.error("[Disaster Alert PLAY ERROR] : try to update ON-AIR status to STOPPED. ");
                           }
                        }

                        List disasterAlertStatus = deviceMgr.selectDisasterAlertStatus(tempAlertId);
                        ((DisasterAlertStatusEntity)disasterAlertStatus.get(0)).setStatus("ON-AIR");
                        deviceMgr.updateDisasterAlertStatus((DisasterAlertStatusEntity)disasterAlertStatus.get(0));
                     }
                  } catch (IOException var86) {
                     this.logger.error("", var86);
                  } catch (ConfigException var87) {
                     this.logger.error("", var87);
                  } catch (Exception var88) {
                     this.logger.error("", var88);
                  } finally {
                     try {
                        if (os != null) {
                           os.close();
                        }

                        if (fileChannel != null) {
                           fileChannel.close();
                        }

                        if (fileIS != null) {
                           fileIS.close();
                        }
                     } catch (IOException var83) {
                        this.logger.error("", var83);
                     }

                  }
               }
            }
         } else {
            response.getWriter().print("file directory error");
         }
      }

   }

   public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      try {
         request.setCharacterEncoding("UTF-8");
         String cmd = request.getHeader("cmd");
         String save_dir = CommonConfig.get("UPLOAD_HOME") + File.separator + "disaster_content_home" + File.separator;
         String deviceMac = request.getHeader("deviceMacList");
         String[] deviceMacList = deviceMac.split(",");
         String alertId = request.getHeader("alertId");
         String DisasterAlertValue = null;
         DisasterAlertStatusEntity disasterAlertStatus = new DisasterAlertStatusEntity();
         disasterAlertStatus.setAlert_id(alertId);
         DeviceConfManager DeviceConfManager = DeviceConfManagerImpl.getInstance();
         MonitoringManager checkConnection = MonitoringManagerImpl.getInstance();
         DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
         File tempF;
         String savePath;
         if (cmd != null && save_dir != null && cmd.equalsIgnoreCase("ALERT_REQUEST")) {
            savePath = request.getHeader("fileName");
            String playEndTime = request.getHeader("playEndTime");
            String savePath = save_dir + File.separator + alertId + File.separator;
            String htmlPath = alertId + File.separator + savePath;
            tempF = SecurityUtils.getSafeFile(savePath);
            if (!tempF.exists()) {
               SecurityUtils.getSafeFile(savePath).mkdirs();
            }

            CommonsMultipartResolver multiPartResolver = new CommonsMultipartResolver();
            MultipartHttpServletRequest multiRequest = multiPartResolver.resolveMultipart(request);
            Iterator files = multiRequest.getFileNames();
            String FilePath = null;

            String url;
            try {
               while(files.hasNext()) {
                  url = (String)files.next();
                  if (url != null) {
                     MultipartFile multipartFile = multiRequest.getFile(url);
                     FilePath = savePath + multipartFile.getOriginalFilename();
                     multipartFile.transferTo(SecurityUtils.getSafeFile(FilePath));
                  }
               }
            } catch (Exception var28) {
               this.logger.info("[Disaster Alert ALERT_REQUEST]: Donwload Error alertId: " + alertId);
               String[] delFiles = tempF.list();

               for(int i = 0; i < delFiles.length; ++i) {
                  File tempF = SecurityUtils.getSafeFile(savePath + File.separatorChar + delFiles[i]);
                  this.logger.info("[Disaster Alert Delete File]: " + savePath + File.separatorChar + delFiles[i]);
                  tempF.delete();
               }
            }

            url = null;
            this.logger.info("[Disaster Alert ALERT_REQUEST] DisasterAlertValue: " + DisasterAlertValue);
            this.logger.info("[Disaster Alert ALERT_REQUEST] File written to: " + savePath);
            disasterAlertStatus.setFile_name(savePath);
            disasterAlertStatus.setPlay_end_time(playEndTime);
            disasterAlertStatus.setStatus("DEPLOY COMPLETED");
            if (deviceMacList.length > 0) {
               for(int i = 0; i < deviceMacList.length; ++i) {
                  disasterAlertStatus.setDevice_id(deviceMacList[i]);
                  url = "http://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath() + "/servlet/DisasterAlertServlet?cmd=PLAY&deviceId=" + deviceMacList[i] + "&file_name=" + htmlPath;
                  DisasterAlertValue = "START;" + url + ";" + playEndTime;

                  try {
                     if (checkConnection.isConnected(deviceMacList[i])) {
                        DeviceConfManager.reqSetDisasterAler(deviceMacList[i], ".MO.MONITORING_INFO.DISASTER_ALERT", DisasterAlertValue);
                        this.logger.info("[Disaster Alert MO] deviceId: " + deviceMacList[i] + " MO Value: " + DisasterAlertValue);
                        deviceMgr.insertDisasterAlertStatus(disasterAlertStatus);
                     } else {
                        disasterAlertStatus.setStatus("DISCONNECTED");
                        disasterAlertStatus.setMo_value(DisasterAlertValue);
                        this.logger.info("[Disaster DB Update - disconneced] deviceId: " + deviceMacList[i] + " MO Value: " + DisasterAlertValue);
                        deviceMgr.insertDisasterAlertStatus(disasterAlertStatus);
                     }
                  } catch (Exception var27) {
                     this.logger.error("", var27);
                  }
               }
            }
         } else if (cmd != null && cmd.equalsIgnoreCase("CANCEL")) {
            DisasterAlertValue = "STOP;;";
            this.logger.info("[Disaster Alert Cancel] DisasterAlertValue: " + DisasterAlertValue);
            disasterAlertStatus.setStatus("CANCELLED");
            if (deviceMacList.length > 0) {
               for(int i = 0; i < deviceMacList.length; ++i) {
                  disasterAlertStatus.setDevice_id(deviceMacList[i]);

                  try {
                     if (checkConnection.isConnected(deviceMacList[i])) {
                        DeviceConfManager.reqSetDisasterAler(deviceMacList[i], ".MO.MONITORING_INFO.DISASTER_ALERT", DisasterAlertValue);
                        this.logger.info("[Disaster Alert MO] deviceId: " + deviceMacList[i] + " MO Value: " + DisasterAlertValue);
                        deviceMgr.updateDisasterAlertStatus(disasterAlertStatus);
                     } else {
                        List diasterInfo = deviceMgr.getDisconnectedDisasterAlertByDeviceIdAndAlertId(deviceMacList[i], alertId);
                        if (!diasterInfo.isEmpty()) {
                           deviceMgr.deleteDisconnectedDisasterAlertStatus(deviceMacList[i], alertId);
                        } else {
                           disasterAlertStatus.setStatus("DISCONNECTED");
                           disasterAlertStatus.setMo_value(DisasterAlertValue);
                           this.logger.info("[Disaster DB Update - disconneced] deviceId: " + deviceMacList[i] + " MO Value: " + DisasterAlertValue);
                           deviceMgr.updateDisasterAlertStatus(disasterAlertStatus);
                        }
                     }
                  } catch (Exception var26) {
                     this.logger.error("", var26);
                  }
               }
            }

            savePath = save_dir + File.separator + alertId + File.separator;
            File f = SecurityUtils.getSafeFile(savePath);
            if (f.exists()) {
               String[] delFiles = f.list();

               for(int i = 0; i < delFiles.length; ++i) {
                  tempF = SecurityUtils.getSafeFile(savePath + File.separatorChar + delFiles[i]);
                  this.logger.info("[Disaster Alert Delete File]: " + savePath + File.separatorChar + delFiles[i]);
                  tempF.delete();
               }
            }
         } else if (save_dir == null || cmd == null) {
            if (save_dir == null) {
               response.getWriter().print("file directory error");
            } else if (cmd == null) {
               response.getWriter().print("cmd value error");
            }
         }
      } catch (ConfigException var29) {
         this.logger.error("", var29);
      }

   }
}
