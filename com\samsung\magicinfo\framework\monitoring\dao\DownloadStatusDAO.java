package com.samsung.magicinfo.framework.monitoring.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class DownloadStatusDAO extends SqlSessionBaseDao {
   private Logger logger = LoggingManagerV2.getLogger(DownloadStatusDAO.class);

   public DownloadStatusDAO() {
      super();
   }

   public DownloadStatusDAO(SqlSession session) {
      super(session);
   }

   public boolean add(String deviceId, String contentId, String progress) throws SQLException {
      return this.add(deviceId, contentId, progress, "content");
   }

   public boolean add(String deviceId, String contentId, String progress, String activeType) throws SQLException {
      int cnt = ((DownloadStatusDAOMapper)this.getMapper()).countStatus(deviceId, contentId, activeType);
      int cnt2 = false;
      int cnt2;
      if (cnt < 1) {
         cnt2 = ((DownloadStatusDAOMapper)this.getMapper()).addStatus(deviceId, contentId, progress, activeType);
      } else {
         cnt2 = ((DownloadStatusDAOMapper)this.getMapper()).updateStatus(deviceId, contentId, progress, activeType);
      }

      return cnt2 > 0;
   }

   public int countStatus(String deviceId, String contentId, String activeType) throws SQLException {
      return ((DownloadStatusDAOMapper)this.getMapper()).countStatus(deviceId, contentId, activeType);
   }

   public void delete(String deviceId) throws SQLException {
      this.delete(deviceId, "content");
      ((DownloadStatusDAOMapper)this.getMapper()).deleteVWLDownloadStatus(deviceId);
      ((DownloadStatusDAOMapper)this.getMapper()).deleteProgramStatus(deviceId);
   }

   public void delete(String deviceId, String activeType) throws SQLException {
      ((DownloadStatusDAOMapper)this.getMapper()).deleteDownloadStatus(deviceId, activeType);
   }

   public void deleteScheduleDeployStatusByDeviceId(SqlSession sqlSession, String deviceId) throws SQLException {
      ((DownloadStatusDAOMapper)this.getMapper(sqlSession)).deleteScheduleDeployStatusByDeviceId(deviceId);
   }

   public void deleteScheduleDeployStatusByDeviceId(String deviceId) throws SQLException {
      ((DownloadStatusDAOMapper)this.getMapper()).deleteScheduleDeployStatusByDeviceId(deviceId);
   }

   public void deleteDownloadStatusByProgramId(String programId) throws SQLException {
      ((DownloadStatusDAOMapper)this.getMapper()).deleteScheduleDeployStatusByProgramId(programId);
   }

   public int getCntDownloadStatusByDeviceId(String device_id, String content_id) throws SQLException {
      return ((DownloadStatusDAOMapper)this.getMapper()).getCntDownloadStatusByDeviceId(device_id, content_id);
   }

   public String getDownloadProgress(String device_id, String content_id, String active_type) throws SQLException {
      return ((DownloadStatusDAOMapper)this.getMapper()).getDownloadProgress(device_id, content_id, active_type);
   }

   public void addScheduleDeployStatus(String programId, String deviceId, String status) throws SQLException {
      ((DownloadStatusDAOMapper)this.getMapper()).addScheduleDeployStatus(programId, deviceId, status);
   }

   public int updateScheduleDeployStatus(String programId, String deviceId, String status) throws SQLException {
      return ((DownloadStatusDAOMapper)this.getMapper()).updateScheduleDeployStatus(programId, deviceId, status);
   }

   public int updateScheduleDeployStatusByDeviceId(String deviceId, String status) throws SQLException {
      return ((DownloadStatusDAOMapper)this.getMapper()).updateScheduleDeployStatusByDeviceId(deviceId, status);
   }

   public void deleteScheduleDeployStatus(String programId, String deviceId) throws SQLException {
      ((DownloadStatusDAOMapper)this.getMapper()).deleteScheduleDeployStatus(programId, deviceId);
   }

   public List getTotalDownloadProgress(String device_id) throws SQLException {
      return ((DownloadStatusDAOMapper)this.getMapper()).getTotalDownloadProgress(device_id);
   }

   public void addDownloadStatus(String programId, List deviceList, List contentList) throws SQLException {
      Iterator var4 = deviceList.iterator();

      while(var4.hasNext()) {
         Device device = (Device)var4.next();
         Iterator var6 = contentList.iterator();

         while(var6.hasNext()) {
            String contentId = (String)var6.next();
            ((DownloadStatusDAOMapper)this.getMapper()).addDownloadStatus(programId, device.getDevice_id(), contentId, "content");
         }
      }

   }

   public void initDownloadStatus(Map downloadStatus) throws SQLException {
      SqlSession session = this.openNewSession(false);
      DownloadStatusDAOMapper mapper = (DownloadStatusDAOMapper)this.getMapper(session);
      String activeType = "content";
      if ("event".equals(downloadStatus.get("activeType"))) {
         activeType = "event";
      }

      String downloadType = downloadStatus.get("downloadType") != null ? (String)downloadStatus.get("downloadType") : "INIT_DOWNLOAD_FROM_RE_PUBLISH";
      String programId = (String)downloadStatus.get("programId");
      if (downloadType.equals("INIT_DOWNLOAD_FROM_NEW_OR_EDIT_SCHEDULE")) {
         mapper.deleteScheduleDeployStatusByProgramId(programId);
      }

      try {
         if (downloadStatus != null) {
            if (downloadStatus.get("device") != null) {
               List deviceList = (List)downloadStatus.get("device");
               List contentList = downloadStatus.get("contentList") != null ? (List)downloadStatus.get("contentList") : new ArrayList();
               Map tempDownloadStatus = new HashMap();
               tempDownloadStatus.putAll(downloadStatus);
               List tempDeviceList = new ArrayList();
               Iterator var11 = deviceList.iterator();

               label156:
               while(true) {
                  while(true) {
                     if (!var11.hasNext()) {
                        break label156;
                     }

                     Device device = (Device)var11.next();
                     mapper.deleteDownloadStatus(device.getDevice_id(), activeType);
                     if (downloadType.equals("INIT_DOWNLOAD_FROM_RE_PUBLISH")) {
                        mapper.updateScheduleDeployStatusByDeviceId(device.getDevice_id(), "WAITING");
                        Iterator var20 = ((List)contentList).iterator();

                        while(var20.hasNext()) {
                           String contentId = (String)var20.next();
                           mapper.updateStatus(device.getDevice_id(), contentId, "0", activeType);
                        }
                     } else {
                        List tempContentList = new ArrayList();
                        tempDeviceList.clear();
                        tempDeviceList.add(device);
                        tempDownloadStatus.put("device", tempDeviceList);

                        try {
                           for(int i = 0; i < ((List)contentList).size(); ++i) {
                              if (i != 0 && i % 100 == 0) {
                                 tempDownloadStatus.put("contentList", tempContentList);
                                 mapper.initDownloadStatus(tempDownloadStatus);
                                 tempContentList = new ArrayList();
                              }

                              tempContentList.add(((List)contentList).get(i));
                              if (i == ((List)contentList).size() - 1) {
                                 tempDownloadStatus.put("contentList", tempContentList);
                                 mapper.initDownloadStatus(tempDownloadStatus);
                              }
                           }
                        } catch (Exception var18) {
                           this.logger.error("[MagicInfo_DownloadStatus] fail init for download status. deviceId : " + device.getDevice_id() + " e :" + var18.getMessage());
                        }
                     }
                  }
               }
            }

            session.commit();
         }
      } finally {
         session.close();
      }

   }

   public void initDownloadStatus(Map downloadStatus, SqlSession session) throws SQLException {
      DownloadStatusDAOMapper mapper = (DownloadStatusDAOMapper)this.getMapper(session);

      try {
         if (downloadStatus != null) {
            if (downloadStatus.get("device") != null) {
               List deviceList = (List)downloadStatus.get("device");
               Iterator var5 = deviceList.iterator();

               while(var5.hasNext()) {
                  Device device = (Device)var5.next();
                  mapper.deleteDownloadStatus(device.getDevice_id(), "content");
                  mapper.deleteScheduleDeployStatusByDeviceId(device.getDevice_id());
               }

               Map tempDownloadStatus = new HashMap();
               tempDownloadStatus.putAll(downloadStatus);
               List tempDeviceList = new ArrayList();
               Iterator var7 = deviceList.iterator();

               while(var7.hasNext()) {
                  Device device = (Device)var7.next();
                  tempDeviceList.clear();
                  tempDeviceList.add(device);
                  tempDownloadStatus.put("device", tempDeviceList);
                  mapper.initDownloadStatus(tempDownloadStatus);
               }
            }

            session.commit();
         }
      } catch (SQLException var12) {
         session.rollback();
         throw var12;
      } finally {
         session.close();
      }

   }

   public void deleteDownloadStatus(String programId) throws SQLException {
      SqlSession session = this.openNewSession(false);
      DownloadStatusDAOMapper mapper = (DownloadStatusDAOMapper)this.getMapper(session);

      try {
         mapper.deleteScheduleDeployStatusByProgramId(programId);
         mapper.deleteDownloadStatusByProgramId(programId);
         mapper.deleteDownloadStatusDetailByProgramId(programId);
         session.commit();
      } catch (Exception var8) {
         session.rollback();
         throw var8;
      } finally {
         session.close();
      }

   }

   public void deleteDownloadStatusByDeviceId(String deviceId) throws SQLException {
      SqlSession session = this.openNewSession(false);
      DownloadStatusDAOMapper mapper = (DownloadStatusDAOMapper)this.getMapper(session);

      try {
         mapper.deleteScheduleDeployStatusByDeviceId(deviceId);
         mapper.deleteAllDownloadStatusByDeviceId(deviceId);
         session.commit();
      } catch (Exception var8) {
         session.rollback();
         throw var8;
      } finally {
         session.close();
      }

   }

   public void deleteDownloadStatus(String programId, SqlSession session) throws SQLException {
      DownloadStatusDAOMapper mapper;
      if (session == null) {
         session = this.openNewSession(false);
         mapper = (DownloadStatusDAOMapper)this.getMapper(session);

         try {
            mapper.deleteScheduleDeployStatusByProgramId(programId);
            mapper.deleteDownloadStatusByProgramId(programId);
            session.commit();
         } catch (Exception var8) {
            session.rollback();
            throw var8;
         } finally {
            session.close();
         }
      } else {
         mapper = (DownloadStatusDAOMapper)this.getMapper(session);
         mapper.deleteScheduleDeployStatusByProgramId(programId);
         mapper.deleteDownloadStatusByProgramId(programId);
      }

   }

   public void deleteDownloadStatus(String deviceId, String activeType) throws SQLException {
      ((DownloadStatusDAOMapper)this.getMapper()).deleteDownloadStatus(deviceId, activeType);
   }

   public List getDownloadStatusListByProgramId(String programId, String activeType) throws SQLException {
      return ((DownloadStatusDAOMapper)this.getMapper()).getDownloadStatusListByProgramId(programId, activeType);
   }

   public List getProgressInfoByDeviceId(String programId, List deviceList, String activeType) throws SQLException {
      return ((DownloadStatusDAOMapper)this.getMapper()).getProgressInfoByDeviceId(programId, deviceList, activeType);
   }

   public void deleteAllDownloadStatusByDeviceId(SqlSession sqlSession, String deviceId) throws SQLException {
      ((DownloadStatusDAOMapper)this.getMapper(sqlSession)).deleteAllDownloadStatusByDeviceId(deviceId);
   }

   public void deleteAllDownloadStatusByDeviceId(String deviceId) throws SQLException {
      ((DownloadStatusDAOMapper)this.getMapper()).deleteAllDownloadStatusByDeviceId(deviceId);
   }

   public void deleteDownloadStatusWithoutContentIdsByDeviceId(String deviceId, List contentIds) throws SQLException {
      ((DownloadStatusDAOMapper)this.getMapper()).deleteDownloadStatusWithoutContentIdsByDeviceId(deviceId, contentIds);
   }

   public List getProgramStatusByProgramId(String programId) {
      return ((DownloadStatusDAOMapper)this.getMapper()).getProgramStatusByProgramId(programId);
   }

   public void addScheduleDeployStatusWithDevices(String programId, List deviceList, String status) throws SQLException {
      ((DownloadStatusDAOMapper)this.getMapper()).addScheduleDeployStatusWithDeviceList(programId, deviceList, status);
   }

   public void deleteDownloadStatusDetailByDeviceId(String deviceId) throws SQLException {
      ((DownloadStatusDAOMapper)this.getMapper()).deleteDownloadStatusDetailByDeviceId(deviceId);
   }

   public void addScheduleDeployStatus(String programId, List deviceList) throws SQLException {
      ((DownloadStatusDAOMapper)this.getMapper()).addScheduleDeployStatusWithDeviceList(programId, deviceList, "WAITING");
   }

   public int updateStatus(String deviceId, String contentId, String progress, String activeType) throws SQLException {
      return ((DownloadStatusDAOMapper)this.getMapper()).updateStatus(deviceId, contentId, progress, activeType);
   }

   public int updateStatus(String deviceId, String contentId, String progress) throws SQLException {
      return ((DownloadStatusDAOMapper)this.getMapper()).updateStatusByAllTypes(deviceId, contentId, progress);
   }

   public void addDownloadStatusWithProgramIdNGroupId(String programId, String deviceId, String contentId, String activeType, Long groupId, String progress) {
      ((DownloadStatusDAOMapper)this.getMapper()).addDownloadStatusWithProgramIdNGroupId(programId, deviceId, contentId, activeType, groupId, progress);
   }

   public List getDownloadStatusesByProgramId(String programId) throws SQLException {
      return ((DownloadStatusDAOMapper)this.getMapper()).getDownloadStatusesByProgramId(programId);
   }

   public String getStatusByProgramIdAndDeviceId(String deviceId, String programId) throws SQLException {
      return ((DownloadStatusDAOMapper)this.getMapper()).getStatusByProgramIdAndDeviceId(deviceId, programId);
   }
}
