package com.samsung.magicinfo.framework.monitoring.dao;

import com.samsung.magicinfo.framework.monitoring.entity.ProgramStatusDetail;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;

public interface DownloadStatusDAOMapper {
   int addStatus(@Param("deviceId") String var1, @Param("contentId") String var2, @Param("progress") String var3, @Param("activeType") String var4) throws SQLException;

   int updateStatus(@Param("deviceId") String var1, @Param("contentId") String var2, @Param("progress") String var3, @Param("activeType") String var4) throws SQLException;

   int countStatus(@Param("deviceId") String var1, @Param("contentId") String var2, @Param("activeType") String var3) throws SQLException;

   void deleteDownloadStatus(@Param("deviceId") String var1, @Param("activeType") String var2) throws SQLException;

   void deleteDownloadStatusDetailByDeviceId(@Param("deviceId") String var1) throws SQLException;

   void deleteVWLDownloadStatus(@Param("consoleId") String var1) throws SQLException;

   void deleteProgramStatus(@Param("consoleId") String var1) throws SQLException;

   int getCntDownloadStatusByDeviceId(@Param("deviceId") String var1, @Param("contentId") String var2) throws SQLException;

   String getDownloadProgress(@Param("deviceId") String var1, @Param("contentId") String var2, @Param("activeType") String var3) throws SQLException;

   int updateStatusData(@Param("deviceId") String var1, @Param("contentId") String var2, @Param("progress") String var3) throws SQLException;

   int addScheduleDeployStatus(@Param("programId") String var1, @Param("deviceId") String var2, @Param("status") String var3) throws SQLException;

   int updateScheduleDeployStatus(@Param("programId") String var1, @Param("deviceId") String var2, @Param("status") String var3) throws SQLException;

   int updateScheduleDeployStatusByDeviceId(@Param("deviceId") String var1, @Param("status") String var2) throws SQLException;

   void deleteScheduleDeployStatus(@Param("programId") String var1, @Param("deviceId") String var2) throws SQLException;

   void deleteAllDownloadStatusByDeviceId(@Param("deviceId") String var1) throws SQLException;

   void deleteScheduleDeployStatusByDeviceId(@Param("deviceId") String var1) throws SQLException;

   void deleteDownloadStatusDetailByProgramId(@Param("programId") String var1) throws SQLException;

   void deleteScheduleDeployStatusByProgramId(@Param("programId") String var1) throws SQLException;

   List getTotalDownloadProgress(@Param("deviceId") String var1) throws SQLException;

   void initDownloadStatus(@Param("downloadStatus") Map var1) throws SQLException;

   void deleteDownloadStatusByProgramId(@Param("programId") String var1) throws SQLException;

   boolean deleteDownloadStatus(@Param("programId") String var1) throws SQLException;

   List getDownloadStatusListByProgramId(@Param("programId") String var1, @Param("activeType") String var2) throws SQLException;

   List getProgressInfoByDeviceId(@Param("programId") String var1, @Param("deviceList") List var2, @Param("activeType") String var3) throws SQLException;

   void addScheduleDeployStatusWithDeviceList(@Param("programId") String var1, @Param("deviceList") List var2, @Param("status") String var3) throws SQLException;

   void addDownloadStatus(@Param("programId") String var1, @Param("deviceId") String var2, @Param("contentId") String var3, @Param("activeType") String var4);

   void deleteDownloadStatusWithoutContentIdsByDeviceId(@Param("deviceId") String var1, @Param("contentIds") List var2) throws SQLException;

   void deleteDownloadStatusWithContentIdsByDeviceIdAndActiveType(@Param("deviceId") String var1, @Param("contentIds") List var2, @Param("activeType") String var3) throws SQLException;

   List getProgramStatusByProgramId(@Param("programId") String var1);

   List getProgramStatusByDeviceId(@Param("deviceId") String var1);

   ProgramStatusDetail getProgramStatusDetailByDeviceId(@Param("deviceId") String var1);

   void deleteDownloadStatusWithoutDeviceIdNProgramId(@Param("programId") String var1, @Param("deviceId") String var2);

   void addDownloadStatusWithProgramIdNGroupId(@Param("programId") String var1, @Param("deviceId") String var2, @Param("contentId") String var3, @Param("activeType") String var4, @Param("groupId") Long var5, @Param("progress") String var6);

   int updateStatusByAllTypes(@Param("deviceId") String var1, @Param("contentId") String var2, @Param("progress") String var3);

   List getDownloadStatusesByProgramId(@Param("programId") String var1);

   String getStatusByProgramIdAndDeviceId(@Param("deviceId") String var1, @Param("programId") String var2);
}
