package com.samsung.magicinfo.webauthor2.model.datalink;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SplitGroup {
  private final int id;
  
  private final String name;
  
  private final List<Element> elements;
  
  private final List<DLKData> dataList;
  
  @JsonCreator
  public SplitGroup(@JsonProperty("id") int id, @JsonProperty("name") String name, @JsonProperty("elements") List<Element> elements, @JsonProperty("dataList") List<DLKData> dataList) {
    this.name = name;
    this.id = id;
    this.elements = elements;
    this.dataList = dataList;
  }
  
  public int getId() {
    return this.id;
  }
  
  public String getName() {
    return this.name;
  }
  
  public List<Element> getElements() {
    return this.elements;
  }
  
  public List<DLKData> getDataList() {
    return this.dataList;
  }
}
