package com.samsung.magicinfo.framework.device.constants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class DeviceMOConstants {
   private static Map ENTITY_MO_GENERAL = null;
   private static Map ENTITY_MO_SETUP = null;
   private static Map ENTITY_MO_DISPLAY = null;
   private static Map ENTITY_MO_SECURITY = null;
   private static Map ENTITY_MO_TIME = null;
   private static Map MO_ENTITY_GENERAL = null;
   private static Map MO_ENTITY_SETUP = null;
   private static Map MO_ENTITY_DISPLAY = null;
   private static Map MO_ENTITY_SECURITY = null;
   private static Map MO_ENTITY_TIME = null;
   private static ArrayList MO_GET_ALL_GENERAL = null;
   private static ArrayList MO_GET_ALL_DISPLAY = null;
   private static ArrayList MO_GET_ALL_SECURITY = null;
   private static ArrayList MO_GET_ALL_TIME = null;

   public DeviceMOConstants() {
      super();
   }

   public static ArrayList getAllStatusMO(String type) {
      byte var2 = -1;
      switch(type.hashCode()) {
      case -80148248:
         if (type.equals("general")) {
            var2 = 0;
         }
         break;
      case 96673:
         if (type.equals("all")) {
            var2 = 4;
         }
         break;
      case 3560141:
         if (type.equals("time")) {
            var2 = 2;
         }
         break;
      case 949122880:
         if (type.equals("security")) {
            var2 = 3;
         }
         break;
      case 1671764162:
         if (type.equals("display")) {
            var2 = 1;
         }
      }

      switch(var2) {
      case 0:
         if (MO_GET_ALL_GENERAL == null) {
            MO_GET_ALL_GENERAL = new ArrayList();
            MO_GET_ALL_GENERAL.add(".MO.DEVICE_CONF.GENERAL");
            MO_GET_ALL_GENERAL.add(".MO.DEVICE_CONF.SYSTEM_SETUP");
         }

         return MO_GET_ALL_GENERAL;
      case 1:
         if (MO_GET_ALL_DISPLAY == null) {
            MO_GET_ALL_DISPLAY = new ArrayList();
            MO_GET_ALL_DISPLAY.add(".MO.DISPLAY_CONF.BASIC");
            MO_GET_ALL_DISPLAY.add(".MO.DISPLAY_CONF.MISC");
            MO_GET_ALL_DISPLAY.add(".MO.DISPLAY_CONF.ADVANCED");
         }

         return MO_GET_ALL_DISPLAY;
      case 2:
         if (MO_GET_ALL_TIME == null) {
            MO_GET_ALL_TIME = new ArrayList();
            MO_GET_ALL_TIME.add(".MO.DISPLAY_CONF.TIMER");
         }

         return MO_GET_ALL_TIME;
      case 3:
         if (MO_GET_ALL_SECURITY == null) {
            MO_GET_ALL_SECURITY = new ArrayList();
            MO_GET_ALL_SECURITY.add(".MO.DISPLAY_CONF.MISC");
            MO_GET_ALL_SECURITY.add(".MO.DISPLAY_CONF.SECURITY");
         }

         return MO_GET_ALL_SECURITY;
      case 4:
         if (MO_GET_ALL_TIME == null) {
            MO_GET_ALL_TIME = new ArrayList();
            MO_GET_ALL_TIME.add(".MO.DEVICE_CONF");
            MO_GET_ALL_TIME.add(".MO.DISPLAY_CONF");
         }

         return MO_GET_ALL_TIME;
      default:
         return null;
      }
   }

   public static Map getGeneralEntityMO() {
      if (ENTITY_MO_GENERAL == null) {
         ENTITY_MO_GENERAL = new HashMap();
         ENTITY_MO_GENERAL.put("device_name", ".MO.DEVICE_CONF.GENERAL.DEVICE_NAME");
         MO_ENTITY_GENERAL = swapKeyValue(ENTITY_MO_GENERAL);
      }

      return ENTITY_MO_GENERAL;
   }

   public static Map getSetupEntityMO() {
      if (ENTITY_MO_SETUP == null) {
         ENTITY_MO_SETUP = new HashMap();
         ENTITY_MO_SETUP.put("time_zone_index", ".MO.DEVICE_CONF.SYSTEM_SETUP.TIME_ZONE_INDEX");
         ENTITY_MO_SETUP.put("day_light_saving", ".MO.DEVICE_CONF.SYSTEM_SETUP.DAY_LIGHT_SAVING");
         ENTITY_MO_SETUP.put("day_light_saving_manual", ".MO.DEVICE_CONF.SYSTEM_SETUP.DAY_LIGHT_SAVING_MANUAL");
         ENTITY_MO_SETUP.put("auto_time_setting", ".MO.DEVICE_CONF.SYSTEM_SETUP.AUTO_TIME_SETTING");
         ENTITY_MO_SETUP.put("on_timer_setting", ".MO.DEVICE_CONF.SYSTEM_SETUP.ON_TIMER_SETTING");
         ENTITY_MO_SETUP.put("off_timer_setting", ".MO.DEVICE_CONF.SYSTEM_SETUP.OFF_TIMER_SETTING");
         ENTITY_MO_SETUP.put("magicinfo_server_url", ".MO.DEVICE_CONF.SYSTEM_SETUP.MAGICINFO_SERVER_URL");
         ENTITY_MO_SETUP.put("cabinet_group_layout", ".MO.DEVICE_CONF.SYSTEM_SETUP.CABINET_GROUP_LAYOUT");
         ENTITY_MO_SETUP.put("is_reverse", ".MO.DEVICE_CONF.SYSTEM_SETUP.IS_REVERSE");
         ENTITY_MO_SETUP.put("tunneling_server", ".MO.DEVICE_CONF.SYSTEM_SETUP.TUNNELING_SERVER");
         ENTITY_MO_SETUP.put("trigger_interval", ".MO.DEVICE_CONF.SYSTEM_SETUP.TRIGGER_INTERVAL");
         ENTITY_MO_SETUP.put("bandwidth", ".MO.DEVICE_CONF.SYSTEM_SETUP.BANDWIDTH_LIMIT");
         ENTITY_MO_SETUP.put("switch_time", ".MO.DEVICE_CONF.SYSTEM_SETUP.PAGE_SWITCH_TIME");
         ENTITY_MO_SETUP.put("monitoring_interval", ".MO.DEVICE_CONF.SYSTEM_SETUP.MONITORING_INTERVAL");
         ENTITY_MO_SETUP.put("child_monitoring_interval", ".MO.DEVICE_CONF.SYSTEM_SETUP.CHILD_MONITORING_INTERVAL");
         ENTITY_MO_SETUP.put("ftp_connect_mode", ".MO.DEVICE_CONF.SYSTEM_SETUP.FTP_CONNECT_MODE");
         ENTITY_MO_SETUP.put("repository_path", ".MO.DEVICE_CONF.SYSTEM_SETUP.REPOSITORY_PATH");
         ENTITY_MO_SETUP.put("screen_capture_interval", ".MO.DEVICE_CONF.SYSTEM_SETUP.SCREEN_CAPTURE_INTERVAL");
         ENTITY_MO_SETUP.put("bg_color", ".MO.DEVICE_CONF.SYSTEM_SETUP.BG_COLOR");
         ENTITY_MO_SETUP.put("proxy_setting", ".MO.DEVICE_CONF.SYSTEM_SETUP.PROXY_SETTING");
         ENTITY_MO_SETUP.put("proxy_exclude_list", ".MO.DEVICE_CONF.SYSTEM_SETUP.PROXY_EXCLUDE_LIST");
         ENTITY_MO_SETUP.put("connection_limit_time", ".MO.DEVICE_CONF.SYSTEM_SETUP.CONNECTION_LIMIT_TIME");
         ENTITY_MO_SETUP.put("mnt_folder_path", ".MO.DEVICE_CONF.SYSTEM_SETUP.MNT_FOLDER_PATH");
         ENTITY_MO_SETUP.put("system_restart_interval", ".MO.DEVICE_CONF.SYSTEM_SETUP.SYSTEM_RESTART_INTERVAL");
         ENTITY_MO_SETUP.put("log_mnt", ".MO.DEVICE_CONF.SYSTEM_SETUP.LOG_MNT");
         ENTITY_MO_SETUP.put("proof_of_play_mnt", ".MO.DEVICE_CONF.SYSTEM_SETUP.PROOF_OF_PLAY_MNT");
         ENTITY_MO_SETUP.put("content_mnt", ".MO.DEVICE_CONF.SYSTEM_SETUP.CONTENT_MNT");
         ENTITY_MO_SETUP.put("screen_rotation", ".MO.DEVICE_CONF.SYSTEM_SETUP.SCREEN_ROTATION");
         ENTITY_MO_SETUP.put("play_mode", ".MO.DEVICE_CONF.SYSTEM_SETUP.PLAY_MODE");
         ENTITY_MO_SETUP.put("reset_password", ".MO.DEVICE_CONF.SYSTEM_SETUP.RESET_PASSWORD");
         ENTITY_MO_SETUP.put("computer_name", ".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.COMPUTERNAME");
         ENTITY_MO_SETUP.put("vnc_password", ".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.VNC_PASSWORD");
         ENTITY_MO_SETUP.put("auto_ip_set", ".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.AUTO_IP_SET");
         ENTITY_MO_SETUP.put("auto_computer_name_set", ".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.AUTO_COMPUTERNAME_SET");
         ENTITY_MO_SETUP.put("use_mpplayer", ".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.USE_MPPLAYER");
         ENTITY_MO_SETUP.put("download_server", ".MO.DEVICE_CONF.SYSTEM_SETUP.DOWNLOAD_SERVER");
         ENTITY_MO_SETUP.put("player_resolution", ".MO.DEVICE_CONF.SYSTEM_SETUP.PLAYER_RESOLUTION");
         ENTITY_MO_SETUP.put("has_child", ".MO.DEVICE_CONF.SYSTEM_SETUP.HAS_SIGNAGE_CHILD");
         ENTITY_MO_SETUP.put("filedata_del_size", ".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.FILEDATA_DEL_SIZE");
         ENTITY_MO_SETUP.put("content_ready_interval", ".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.CONTENT_READY_INTERVAL");
         ENTITY_MO_SETUP.put("player_start_timeout", ".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.PLAYER_START_TIMEOUT");
         ENTITY_MO_SETUP.put("last_sent_event", ".MO.DEVICE_CONF.PROCESS.EVENT");
         ENTITY_MO_SETUP.put("contents_progress_enable", ".MO.DEVICE_CONF.SYSTEM_SETUP.DOWN_PROGRESS.ENABLE");
         ENTITY_MO_SETUP.put("contents_progress_unit", ".MO.DEVICE_CONF.SYSTEM_SETUP.DOWN_PROGRESS.MEASURE_UNIT");
         ENTITY_MO_SETUP.put("contents_progress_interval", ".MO.DEVICE_CONF.SYSTEM_SETUP.DOWN_PROGRESS.MEASURE_INTERVAL");
         ENTITY_MO_SETUP.put("contents_download_mode", ".MO.DEVICE_CONF.SYSTEM_SETUP.DOWNLOAD_MODE");
         ENTITY_MO_SETUP.put("url_launcher", ".MO.DEVICE_CONF.SYSTEM_SETUP.URL_LAUNCHER");
         ENTITY_MO_SETUP.put("pin_code", ".MO.DEVICE_CONF.SYSTEM_SETUP.PIN_CODE");
         ENTITY_MO_SETUP.put("download_protocol", ".MO.DEVICE_CONF.SYSTEM_SETUP.PROTOCOL_PRIORITY");
         MO_ENTITY_SETUP = swapKeyValue(ENTITY_MO_SETUP);
      }

      return ENTITY_MO_SETUP;
   }

   public static Map getDisplayEntityMO() {
      if (ENTITY_MO_DISPLAY == null) {
         ENTITY_MO_DISPLAY = new HashMap();
         ENTITY_MO_DISPLAY.put("basic_power", ".MO.DISPLAY_CONF.BASIC.POWER");
         ENTITY_MO_DISPLAY.put("basic_volume", ".MO.DISPLAY_CONF.BASIC.VOLUME");
         ENTITY_MO_DISPLAY.put("basic_mute", ".MO.DISPLAY_CONF.BASIC.MUTE");
         ENTITY_MO_DISPLAY.put("basic_source", ".MO.DISPLAY_CONF.BASIC.SOURCE");
         ENTITY_MO_DISPLAY.put("web_browser_url", ".MO.DISPLAY_CONF.BASIC.WEB_BROWSER_URL");
         ENTITY_MO_DISPLAY.put("custom_logo", ".MO.DISPLAY_CONF.BASIC.CUSTOM_LOGO");
         ENTITY_MO_DISPLAY.put("basic_panel_status", ".MO.DISPLAY_CONF.BASIC.PANEL_STATUS");
         ENTITY_MO_DISPLAY.put("ppc_brightness", ".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS");
         ENTITY_MO_DISPLAY.put("screen_mute", ".MO.DISPLAY_CONF.BASIC.SCREEN_MUTE");
         ENTITY_MO_DISPLAY.put("screen_freeze", ".MO.DISPLAY_CONF.BASIC.FREEZE");
         ENTITY_MO_DISPLAY.put("time_on_time", ".MO.DISPLAY_CONF.TIME.ON_TIME");
         ENTITY_MO_DISPLAY.put("time_off_time", ".MO.DISPLAY_CONF.TIME.OFF_TIME");
         ENTITY_MO_DISPLAY.put("misc_osd", ".MO.DISPLAY_CONF.MISC.OSD");
         ENTITY_MO_DISPLAY.put("osd_menu_size", ".MO.DISPLAY_CONF.MISC.OSD_MENU_SIZE");
         ENTITY_MO_DISPLAY.put("diagnosis_alarm_temperature", ".MO.DISPLAY_CONF.DIAGNOSIS.ALARM_TEMPERATURE");
         ENTITY_MO_DISPLAY.put("basic_direct_channel", ".MO.DISPLAY_CONF.BASIC.DIRECT_CHANNEL");
         ENTITY_MO_DISPLAY.put("pv_mode", ".MO.DISPLAY_CONF.PICTURE_VIDEO.MODE");
         ENTITY_MO_DISPLAY.put("pv_contrast", ".MO.DISPLAY_CONF.PICTURE_VIDEO.CONTRAST");
         ENTITY_MO_DISPLAY.put("pv_brightness", ".MO.DISPLAY_CONF.PICTURE_VIDEO.BRIGHTNESS");
         ENTITY_MO_DISPLAY.put("specialized_picture_mode", ".MO.DISPLAY_CONF.PICTURE_VIDEO.SPECIALIZED_PICTURE_MODE");
         ENTITY_MO_DISPLAY.put("pv_sharpness", ".MO.DISPLAY_CONF.PICTURE_VIDEO.SHARPNESS");
         ENTITY_MO_DISPLAY.put("pv_color", ".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR");
         ENTITY_MO_DISPLAY.put("pv_tint", ".MO.DISPLAY_CONF.PICTURE_VIDEO.TINT");
         ENTITY_MO_DISPLAY.put("pv_colortone", ".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TONE");
         ENTITY_MO_DISPLAY.put("pv_color_temperature", ".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TEMPERATURE");
         ENTITY_MO_DISPLAY.put("pv_size", ".MO.DISPLAY_CONF.PICTURE_VIDEO.SIZE");
         ENTITY_MO_DISPLAY.put("pv_digitalnr", ".MO.DISPLAY_CONF.PICTURE_VIDEO.DIGITAL_NR");
         ENTITY_MO_DISPLAY.put("pv_filmmode", ".MO.DISPLAY_CONF.PICTURE_VIDEO.FILM_MODE");
         ENTITY_MO_DISPLAY.put("pv_mpeg_noise_filter", ".MO.DISPLAY_CONF.PICTURE_VIDEO.MPEG_NOISE_FILTER");
         ENTITY_MO_DISPLAY.put("ppc_magic_bright", ".MO.DISPLAY_CONF.PICTURE_PC.MAGIC_BRIGHT");
         ENTITY_MO_DISPLAY.put("ppc_contrast", ".MO.DISPLAY_CONF.PICTURE_PC.RGB_CONTRAST");
         ENTITY_MO_DISPLAY.put("ppc_brightness", ".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS");
         ENTITY_MO_DISPLAY.put("ppc_colortone", ".MO.DISPLAY_CONF.PICTURE_PC.PC_COLOR_TONE");
         ENTITY_MO_DISPLAY.put("ppc_color_temperature", ".MO.DISPLAY_CONF.PICTURE_PC.COLOR_TEMPERATURE");
         ENTITY_MO_DISPLAY.put("ppc_red", ".MO.DISPLAY_CONF.DIAGNOSIS.ALARM_TEMPERATURE");
         ENTITY_MO_DISPLAY.put("ppc_green", ".MO.DISPLAY_CONF.PICTURE_PC.RGB_GREEN");
         ENTITY_MO_DISPLAY.put("ppc_blue", ".MO.DISPLAY_CONF.PICTURE_PC.RGB_BLUE");
         ENTITY_MO_DISPLAY.put("ppc_size", ".MO.DISPLAY_CONF.PICTURE_PC.PICTURE_SIZE");
         ENTITY_MO_DISPLAY.put("sound_mode", ".MO.DISPLAY_CONF.SOUND.MODE");
         ENTITY_MO_DISPLAY.put("sound_bass", ".MO.DISPLAY_CONF.SOUND.BASS");
         ENTITY_MO_DISPLAY.put("sound_treble", ".MO.DISPLAY_CONF.SOUND.TREBLE");
         ENTITY_MO_DISPLAY.put("sound_balance", ".MO.DISPLAY_CONF.SOUND.BALANCE");
         ENTITY_MO_DISPLAY.put("sound_srs", ".MO.DISPLAY_CONF.SOUND.SRS");
         ENTITY_MO_DISPLAY.put("sb_status", ".MO.DISPLAY_CONF.SIGNAL_BALANCE.STATE");
         ENTITY_MO_DISPLAY.put("sb_gain", ".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_GAIN");
         ENTITY_MO_DISPLAY.put("sb_sharp", ".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_SHARPNESS");
         ENTITY_MO_DISPLAY.put("mnt_auto", ".MO.DISPLAY_CONF.MAINTENANCE.AUTO");
         ENTITY_MO_DISPLAY.put("mnt_manual", ".MO.DISPLAY_CONF.MAINTENANCE.MANUAL");
         ENTITY_MO_DISPLAY.put("mnt_safety_screen_timer", ".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_TIMER");
         ENTITY_MO_DISPLAY.put("auto_source_switching", ".MO.DISPLAY_CONF.ADVANCED.AUTO_SOURCE_SWITCHING");
         ENTITY_MO_DISPLAY.put("max_power_saving", ".MO.DISPLAY_CONF.ADVANCED.MAX_POWER_SAVING");
         ENTITY_MO_DISPLAY.put("brightness_limit", ".MO.DISPLAY_CONF.ADVANCED.BRIGHTNESS_LIMIT");
         ENTITY_MO_DISPLAY.put("touch_control_lock", ".MO.DISPLAY_CONF.ADVANCED.TOUCH_CONTROL_LOCK");
         ENTITY_MO_DISPLAY.put("black_tone", ".MO.DISPLAY_CONF.ADVANCED.BLACK_TONE");
         ENTITY_MO_DISPLAY.put("flesh_tone", ".MO.DISPLAY_CONF.ADVANCED.FLESH_TONE");
         ENTITY_MO_DISPLAY.put("rgb_only_mode", ".MO.DISPLAY_CONF.ADVANCED.RGB_ONLY_MODE");
         ENTITY_MO_DISPLAY.put("picture_enhancer", ".MO.DISPLAY_CONF.ADVANCED.PICTURE_ENHANCER");
         ENTITY_MO_DISPLAY.put("color_space", ".MO.DISPLAY_CONF.ADVANCED.COLOR_SPACE");
         ENTITY_MO_DISPLAY.put("led_hdr", ".MO.DISPLAY_CONF.PICTURE_PC.LED_HDR");
         ENTITY_MO_DISPLAY.put("led_hdr_dre", ".MO.DISPLAY_CONF.PICTURE_PC.LED_HDR_DRE");
         ENTITY_MO_DISPLAY.put("led_picture_size", ".MO.DISPLAY_CONF.PICTURE_PC.LED_PICTURE_SIZE");
         ENTITY_MO_DISPLAY.put("auto_motion_plus", ".MO.DISPLAY_CONF.PICTURE_PC.AUTO_MOTION_PLUS");
         ENTITY_MO_DISPLAY.put("auto_motion_plus_judder_reduction", ".MO.DISPLAY_CONF.PICTURE_PC.AUTO_MOTION_PLUS_JUDDER_REDUCTION");
         ENTITY_MO_DISPLAY.put("eco_sensor", ".MO.DISPLAY_CONF.ADVANCED.ECO_SENSOR");
         ENTITY_MO_DISPLAY.put("min_brightness", ".MO.DISPLAY_CONF.ADVANCED.MIN_BRIGHTNESS");
         ENTITY_MO_DISPLAY.put("live_mode", ".MO.DISPLAY_CONF.ADVANCED.LIVE_MODE");
         ENTITY_MO_DISPLAY.put("display_output_mode", ".MO.DISPLAY_CONF.ADVANCED.DISPLAY_OUTPUT_MODE");
         ENTITY_MO_DISPLAY.put("sb_rgain", ".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_GAIN");
         ENTITY_MO_DISPLAY.put("sb_ggain", ".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_GAIN");
         ENTITY_MO_DISPLAY.put("sb_bgain", ".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_GAIN");
         ENTITY_MO_DISPLAY.put("sb_r_offset", ".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_OFFSET");
         ENTITY_MO_DISPLAY.put("sb_g_offset", ".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_OFFSET");
         ENTITY_MO_DISPLAY.put("sb_b_offset", ".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_OFFSET");
         ENTITY_MO_DISPLAY.put("mnt_safety_screen_run", ".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_RUN");
         ENTITY_MO_DISPLAY.put("mnt_pixel_shift", ".MO.DISPLAY_CONF.MAINTENANCE.PIXEL_SHIFT");
         ENTITY_MO_DISPLAY.put("pv_video_picture_position_size", ".MO.DISPLAY_CONF.PICTURE_VIDEO.VIDEO_PICTURE_POSITION_SIZE");
         ENTITY_MO_DISPLAY.put("pv_hdmi_black_level", ".MO.DISPLAY_CONF.PICTURE_VIDEO.HDMI_BLACK_LEVEL");
         ENTITY_MO_DISPLAY.put("ppc_gamma", ".MO.DISPLAY_CONF.PICTURE_PC.GAMMA");
         ENTITY_MO_DISPLAY.put("ppc_hdmi_black_level", ".MO.DISPLAY_CONF.PICTURE_PC.HDMI_BLACK_LEVEL");
         ENTITY_MO_DISPLAY.put("advanced_rj45_setting_refresh", ".MO.DISPLAY_CONF.ADVANCED.RJ45_SETTING_REFRESH");
         ENTITY_MO_DISPLAY.put("advanced_osd_display_type", ".MO.DISPLAY_CONF.ADVANCED.OSD_DISPLAY_TYPE");
         ENTITY_MO_DISPLAY.put("advanced_fan_control", ".MO.DISPLAY_CONF.ADVANCED.FAN_CONTROL");
         ENTITY_MO_DISPLAY.put("advanced_fan_speed", ".MO.DISPLAY_CONF.ADVANCED.FAN_SPEED");
         ENTITY_MO_DISPLAY.put("advanced_reset", ".MO.DISPLAY_CONF.ADVANCED.RESET");
         ENTITY_MO_DISPLAY.put("advanced_auto_power", ".MO.DISPLAY_CONF.ADVANCED.AUTO_POWER");
         ENTITY_MO_DISPLAY.put("advanced_user_auto_color", ".MO.DISPLAY_CONF.ADVANCED.USER_AUTO_COLOR");
         ENTITY_MO_DISPLAY.put("advanced_stand_by", ".MO.DISPLAY_CONF.ADVANCED.STAND_BY");
         ENTITY_MO_DISPLAY.put("network_standby_mode", ".MO.DISPLAY_CONF.ADVANCED.NETWORK_STAND_BY");
         ENTITY_MO_DISPLAY.put("cleanup_user_data_interval", ".MO.DISPLAY_CONF.ADVANCED.CLEANUP_USER_DATA_INTERVAL");
         ENTITY_MO_DISPLAY.put("auto_save", ".MO.DISPLAY_CONF.ADVANCED.AUTO_SAVE");
         ENTITY_MO_DISPLAY.put("auto_power_off", ".MO.DISPLAY_CONF.ADVANCED.AUTO_POWER_OFF");
         ENTITY_MO_DISPLAY.put("smtp", ".MO.DISPLAY_CONF.ADVANCED.SMTP");
         ENTITY_MO_DISPLAY.put("print_server", ".MO.DISPLAY_CONF.ADVANCED.PRINT_SERVER");
         ENTITY_MO_DISPLAY.put("web_command", ".MO.DISPLAY_CONF.WEB.COMMAND");
         ENTITY_MO_DISPLAY.put("web_option", ".MO.DISPLAY_CONF.WEB.OPTION");
         ENTITY_MO_DISPLAY.put("web_url", ".MO.DISPLAY_CONF.WEB.URL");
         ENTITY_MO_DISPLAY.put("web_end_time", ".MO.DISPLAY_CONF.WEB.END_TIME");
         ENTITY_MO_DISPLAY.put("web_duration", ".MO.DISPLAY_CONF.WEB.DURATION");
         MO_ENTITY_DISPLAY = swapKeyValue(ENTITY_MO_DISPLAY);
      }

      return ENTITY_MO_DISPLAY;
   }

   public static Map getSecurityEntityMO() {
      if (ENTITY_MO_SECURITY == null) {
         ENTITY_MO_SECURITY = new HashMap();
         ENTITY_MO_SECURITY.put("mnt_safety_lock", ".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_LOCK");
         ENTITY_MO_SECURITY.put("misc_remocon", ".MO.DISPLAY_CONF.MISC.REMOCON");
         ENTITY_MO_SECURITY.put("misc_panel_lock", ".MO.DISPLAY_CONF.MISC.PANEL_LOCK");
         ENTITY_MO_SECURITY.put("misc_all_lock", ".MO.DISPLAY_CONF.MISC.ALL_LOCK");
         ENTITY_MO_SECURITY.put("misc_block_usb_port", ".MO.DISPLAY_CONF.MISC.BLOCK_USB_PORT");
         ENTITY_MO_SECURITY.put("misc_block_network_connection", ".MO.DISPLAY_CONF.MISC.BLOCK_NETWORK_CONNECTION");
         ENTITY_MO_SECURITY.put("misc_white_list", ".MO.DISPLAY_CONF.MISC.WHITE_LIST");
         ENTITY_MO_SECURITY.put("misc_server_network_setting", ".MO.DISPLAY_CONF.MISC.SERVER_NETWORK_SETTING_LOCK");
         ENTITY_MO_SECURITY.put("touch_control_lock", ".MO.DISPLAY_CONF.ADVANCED.TOUCH_CONTROL_LOCK");
         ENTITY_MO_SECURITY.put("bluetooth_lock", ".MO.DISPLAY_CONF.SECURITY.BLUETOOTH_LOCK");
         ENTITY_MO_SECURITY.put("wifi_lock", ".MO.DISPLAY_CONF.SECURITY.WIFI_LOCK");
         ENTITY_MO_SECURITY.put("source_lock", ".MO.DISPLAY_CONF.SECURITY.SOURCE_LOCK");
         ENTITY_MO_SECURITY.put("screen_monitoring_lock", ".MO.DISPLAY_CONF.SECURITY.SCREEN_MONITORING_LOCK");
         ENTITY_MO_SECURITY.put("capture_lock", ".MO.DISPLAY_CONF.ADVANCED.CAPTURE");
         MO_ENTITY_SECURITY = swapKeyValue(ENTITY_MO_SECURITY);
      }

      return ENTITY_MO_SECURITY;
   }

   public static Map getTimeEntityMO() {
      if (ENTITY_MO_TIME == null) {
         ENTITY_MO_TIME = new HashMap();
         ENTITY_MO_TIME.put("time_on_time", ".MO.DISPLAY_CONF.TIME.ON_TIME");
         ENTITY_MO_TIME.put("time_off_time", ".MO.DISPLAY_CONF.TIME.OFF_TIME");
         ENTITY_MO_TIME.put("time_current_time", ".MO.DISPLAY_CONF.TIME.CURRENT_TIME");
         ENTITY_MO_TIME.put("timer_clock", ".MO.DISPLAY_CONF.TIMER.CLOCK");
         ENTITY_MO_TIME.put("timer_timer1", ".MO.DISPLAY_CONF.TIMER.TIMER1");
         ENTITY_MO_TIME.put("timer_timer2", ".MO.DISPLAY_CONF.TIMER.TIMER2");
         ENTITY_MO_TIME.put("timer_timer3", ".MO.DISPLAY_CONF.TIMER.TIMER3");
         ENTITY_MO_TIME.put("timer_timer4", ".MO.DISPLAY_CONF.TIMER.TIMER4");
         ENTITY_MO_TIME.put("timer_timer5", ".MO.DISPLAY_CONF.TIMER.TIMER5");
         ENTITY_MO_TIME.put("timer_timer6", ".MO.DISPLAY_CONF.TIMER.TIMER6");
         ENTITY_MO_TIME.put("timer_timer7", ".MO.DISPLAY_CONF.TIMER.TIMER7");
         ENTITY_MO_TIME.put("timer_holiday", ".MO.DISPLAY_CONF.TIMER.HOLIDAY");
         MO_ENTITY_TIME = swapKeyValue(ENTITY_MO_TIME);
      }

      return ENTITY_MO_TIME;
   }

   public static Map getGeneralMOEntity() {
      if (MO_ENTITY_GENERAL == null) {
         getGeneralEntityMO();
      }

      return MO_ENTITY_GENERAL;
   }

   public static Map getSetupMOEntity() {
      if (MO_ENTITY_SETUP == null) {
         getSetupEntityMO();
      }

      return MO_ENTITY_SETUP;
   }

   public static Map getDisplayMOEntity() {
      if (MO_ENTITY_DISPLAY == null) {
         getDisplayEntityMO();
      }

      return MO_ENTITY_DISPLAY;
   }

   public static Map getSecurityMOEntity() {
      if (MO_ENTITY_SECURITY == null) {
         getSecurityEntityMO();
      }

      return MO_ENTITY_SECURITY;
   }

   public static Map getTimeMOEntity() {
      if (MO_ENTITY_TIME == null) {
         getTimeEntityMO();
      }

      return MO_ENTITY_TIME;
   }

   public static Map swapKeyValue(Map origin) {
      Map swapMap = null;
      if (origin == null) {
         return null;
      } else {
         swapMap = new HashMap();
         Iterator var2 = origin.keySet().iterator();

         while(var2.hasNext()) {
            String originkey = (String)var2.next();
            String originvalue = (String)origin.get(originkey);
            swapMap.put(originvalue, originkey);
         }

         return swapMap;
      }
   }
}
