package com.samsung.magicinfo.webauthor2.model.datalink;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes({@Type(value = TextValue.class, name = "TEXT"), @Type(value = FileValue.class, name = "FILE"), @Type(value = FolderValue.class, name = "FOLDER")})
public abstract class Value {
  @JsonIgnore
  private final ValueType type;
  
  public Value(ValueType type) {
    this.type = type;
  }
  
  public ValueType getType() {
    return this.type;
  }
}
