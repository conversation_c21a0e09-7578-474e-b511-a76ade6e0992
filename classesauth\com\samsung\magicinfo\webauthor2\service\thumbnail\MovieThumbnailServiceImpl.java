package com.samsung.magicinfo.webauthor2.service.thumbnail;

import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.model.FileMetaInfo;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import com.samsung.magicinfo.webauthor2.service.FFmpegService;
import com.samsung.magicinfo.webauthor2.service.FileMetaService;
import com.samsung.magicinfo.webauthor2.util.OperatingSystem;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MovieThumbnailServiceImpl implements MovieThumbnailService {
  private static Logger logger = LoggerFactory.getLogger(MovieThumbnailServiceImpl.class);
  
  private FileMetaService fileMetaService;
  
  private FFmpegService ffmpegService;
  
  @Autowired
  public MovieThumbnailServiceImpl(FileMetaService fileMetaService, FFmpegService ffmpegService) {
    this.fileMetaService = fileMetaService;
    this.ffmpegService = ffmpegService;
  }
  
  public void createMovieThumbnail(MediaSource mediaSource, Path thumbnailPath) throws UploaderException {
    try {
      Path thumbnailPathParent = thumbnailPath.getParent();
      if (thumbnailPathParent != null && Files.notExists(thumbnailPathParent, new java.nio.file.LinkOption[0]))
        Files.createDirectories(thumbnailPathParent, (FileAttribute<?>[])new FileAttribute[0]); 
      FileMetaInfo meta = this.fileMetaService.getFileMeta(Paths.get(mediaSource.getPath(), new String[0]));
      int widthBig = 438, heightBig = 258;
      if (meta != null) {
        long width = Long.parseLong((String)meta.getWidth().or("0"));
        long height = Long.parseLong((String)meta.getHeight().or("0"));
        if (width / 438L > height / 258L) {
          heightBig = (int)(height * 438L / width);
          if (heightBig % 2 != 0)
            heightBig++; 
        } else {
          widthBig = (int)(width * 258L / height);
          if (widthBig % 2 != 0)
            widthBig++; 
        } 
      } 
      Process process = callFFmpegProcess(Paths.get(mediaSource.getPath(), new String[0]), thumbnailPath, widthBig, heightBig);
      int processResult = process.waitFor();
      logger.info("FFMpeg exit code:" + processResult + "\n");
    } catch (NoSuchElementException ex) {
      logger.error(ex.getMessage());
      throw new UploaderException(699, "UploadFileCorruptError");
    } catch (OutOfMemoryError ex) {
      logger.error(ex.getMessage());
      throw new UploaderException(699, "UploadLargeFileError");
    } catch (IOException e) {
      logger.error(e.getMessage());
      throw new UploaderException(1057, "ServerInternalErrorInterruptedError1057");
    } catch (InterruptedException e) {
      logger.error(e.getMessage());
      throw new UploaderException(1058, "ServerInternalErrorInterruptedError1058");
    } catch (Exception e) {
      logger.error(e.getMessage());
      throw new UploaderException(699, "ServerInternalUploadError");
    } 
    logger.info(":Thumbnail Creation is Done !!!:");
  }
  
  private Process callFFmpegProcess(Path contentFile, Path thumbnailPath, int widthBig, int heightBig) throws IOException {
    List<String> params = new ArrayList<>();
    if (OperatingSystem.isWindows()) {
      String ffmpegPath = this.ffmpegService.getFFmpeg().toAbsolutePath().toString();
      params.add(ffmpegPath);
    } else if (OperatingSystem.isLinux()) {
      params.add("ffmpeg");
    } 
    params.add("-i");
    params.add(contentFile.toAbsolutePath().toString());
    params.add("-f");
    params.add("image2");
    params.add("-vframes");
    params.add("1");
    params.add("-s");
    params.add(widthBig + "x" + heightBig);
    params.add("-ss");
    params.add("2");
    params.add("-an");
    params.add("-y");
    params.add(thumbnailPath.toAbsolutePath().toString());
    Process process = (new ProcessBuilder(params)).start();
    try (BufferedReader bri = new BufferedReader(new InputStreamReader(process
            .getErrorStream(), StandardCharsets.UTF_8.toString()))) {
      for (String line = bri.readLine(); line != null; line = bri.readLine())
        logger.debug(line); 
    } 
    return process;
  }
}
