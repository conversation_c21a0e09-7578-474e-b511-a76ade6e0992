package com.samsung.magicinfo.framework.device.deviceInfo.entity;

import com.samsung.common.utils.StrUtils;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class LedCabinet {
   protected String parent_device_id;
   protected String power;
   protected Long cabinet_id;
   protected Long cabinet_group_id;
   protected Long cabinet_type;
   protected String hdbt;
   protected String open_detection;
   protected String module_product_number;
   protected String module_date;
   protected Date last_scanned_time;
   protected String voltage_status;
   protected String resolution;
   protected String phy_size;
   protected String modules;
   protected String pitch;
   protected Long luminance;
   protected Long abl;
   protected Long current_temperature;
   protected Long auto_source;
   protected String ic;
   protected String aspect_ratio;
   protected Long input_source;
   protected Long gamut;
   protected Long backlight;
   protected Long max_backlight;
   protected Long pixel_rgb_cc;
   protected Long module_rgb_cc;
   protected Long edge_correction;
   protected String fault;
   protected String fw_version;
   protected String fpga_version;
   protected String on_screen_display;
   private String isLODError;
   private String isTemperatureError;
   private String isVoltageError;
   private String isICError;
   private Long position_X;
   private Long position_Y;

   public LedCabinet() {
      super();
   }

   public LedCabinet(String parentDeviceId) {
      super();
      this.parent_device_id = parentDeviceId;
   }

   public String getParent_device_id() {
      return this.parent_device_id;
   }

   public void setParent_device_id(String parent_device_id) {
      this.parent_device_id = parent_device_id;
   }

   public Long getCabinet_id() {
      return this.cabinet_id;
   }

   public void setCabinet_id(Long cabinet_id) {
      this.cabinet_id = cabinet_id;
   }

   public Long getCabinet_group_id() {
      return this.cabinet_group_id;
   }

   public void setCabinet_group_id(Long cabinet_group_id) {
      this.cabinet_group_id = cabinet_group_id;
   }

   public Long getCabinet_type() {
      return this.cabinet_type;
   }

   public void setCabinet_type(Long cabinet_type) {
      this.cabinet_type = cabinet_type;
   }

   public String getHdbt() {
      return this.hdbt;
   }

   public void setHdbt(String hdbt) {
      this.hdbt = hdbt;
   }

   public String getOpen_detection() {
      return this.open_detection;
   }

   public void setOpen_detection(String open_detection) {
      this.open_detection = open_detection;
   }

   public Date getLast_scanned_time() {
      return this.last_scanned_time;
   }

   public Timestamp getLast_scanned_time_timestamp() {
      return this.last_scanned_time != null ? new Timestamp(this.last_scanned_time.getTime()) : null;
   }

   public void setLast_scanned_time(Date last_scanned_time) {
      this.last_scanned_time = last_scanned_time;
   }

   public String getVoltage_status() {
      return this.voltage_status;
   }

   public void setVoltage_status(String voltage_status) {
      this.voltage_status = voltage_status;
   }

   public String getResolution() {
      return this.resolution;
   }

   public void setResolution(String resolution) {
      this.resolution = resolution;
   }

   public String getPhy_size() {
      return this.phy_size;
   }

   public void setPhy_size(String phy_size) {
      this.phy_size = phy_size;
   }

   public String getModules() {
      return this.modules;
   }

   public void setModules(String modules) {
      this.modules = modules;
   }

   public String getPitch() {
      return this.pitch;
   }

   public void setPitch(String pitch) {
      this.pitch = pitch;
   }

   public Long getLuminance() {
      return this.luminance;
   }

   public void setLuminance(Long luminance) {
      this.luminance = luminance;
   }

   public Long getAbl() {
      return this.abl;
   }

   public void setAbl(Long abl) {
      this.abl = abl;
   }

   public Long getAuto_source() {
      return this.auto_source;
   }

   public void setAuto_source(Long auto_source) {
      this.auto_source = auto_source;
   }

   public String getIc() {
      return this.ic;
   }

   public void setIc(String ic) {
      this.ic = ic;
   }

   public String getAspect_ratio() {
      return this.aspect_ratio;
   }

   public void setAspect_ratio(String aspect_ratio) {
      this.aspect_ratio = aspect_ratio;
   }

   public Long getInput_source() {
      return this.input_source;
   }

   public void setInput_source(Long input_source) {
      this.input_source = input_source;
   }

   public Long getGamut() {
      return this.gamut;
   }

   public void setGamut(Long gamut) {
      this.gamut = gamut;
   }

   public Long getBacklight() {
      return this.backlight;
   }

   public void setBacklight(Long backlight) {
      this.backlight = backlight;
   }

   public Long getMax_backlight() {
      return this.max_backlight;
   }

   public void setMax_backlight(Long max_backlight) {
      this.max_backlight = max_backlight;
   }

   public Long getPixel_rgb_cc() {
      return this.pixel_rgb_cc;
   }

   public void setPixel_rgb_cc(Long pixel_rgb_cc) {
      this.pixel_rgb_cc = pixel_rgb_cc;
   }

   public Long getModule_rgb_cc() {
      return this.module_rgb_cc;
   }

   public void setModule_rgb_cc(Long module_rgb_cc) {
      this.module_rgb_cc = module_rgb_cc;
   }

   public Long getEdge_correction() {
      return this.edge_correction;
   }

   public void setEdge_correction(Long edge_correction) {
      this.edge_correction = edge_correction;
   }

   public String getFault() {
      return this.fault;
   }

   public void setFault(String fault) {
      this.fault = fault;
   }

   public String getFw_version() {
      return this.fw_version;
   }

   public void setFw_version(String fw_version) {
      this.fw_version = fw_version;
   }

   public String getFpga_version() {
      return this.fpga_version;
   }

   public void setFpga_version(String fpga_version) {
      this.fpga_version = fpga_version;
   }

   public String getPower() {
      return this.power;
   }

   public void setPower(String power) {
      this.power = power;
   }

   public Long getCurrent_temperature() {
      return this.current_temperature;
   }

   public void setCurrent_temperature(Long current_temperature) {
      this.current_temperature = current_temperature;
   }

   public String getIsLODError() {
      return this.isLODError;
   }

   public void setIsLODError(String isLODError) {
      this.isLODError = isLODError;
   }

   public String getIsTemperatureError() {
      return this.isTemperatureError;
   }

   public void setIsTemperatureError(String isTemperatureError) {
      this.isTemperatureError = isTemperatureError;
   }

   public String getIsVoltageError() {
      return this.isVoltageError;
   }

   public void setIsVoltageError(String isVoltageError) {
      this.isVoltageError = isVoltageError;
   }

   public String getIsICError() {
      return this.isICError;
   }

   public void setIsICError(String isICError) {
      this.isICError = isICError;
   }

   public Long getPosition_X() {
      return this.position_X;
   }

   public void setPosition_X(Long position_X) {
      this.position_X = position_X;
   }

   public Long getPosition_Y() {
      return this.position_Y;
   }

   public void setPosition_Y(Long position_Y) {
      this.position_Y = position_Y;
   }

   public void convertUserFriendlyFormat() {
      if (this.resolution != null) {
         this.resolution = this.resolution.replace(':', 'x');
      }

      if (this.pitch != null) {
         this.pitch = this.pitch.replace(':', 'x');
      }

      if (this.modules != null) {
         this.modules = this.modules.replace(':', 'x');
      }

      if (this.phy_size != null) {
         this.phy_size = this.phy_size.replace(':', 'x');
      }

   }

   public String getIc_fpga() {
      return "1".equals(this.getIc("fpga")) ? "Available" : "Not Available";
   }

   public String getIc_powerDetectIc() {
      return "1".equals(this.getIc("power_detect_ic")) ? "Available" : "Not Available";
   }

   public String getIc_13v() {
      return "1".equals(this.getIc("13v")) ? "Available" : "Not Available";
   }

   public String getVoltage_status_130() {
      return "1".equals(this.getVoltage_status("13")) ? "Available" : "Not Available";
   }

   public String getVoltage_status_50() {
      return "1".equals(this.getVoltage_status("5.0")) ? "Available" : "Not Available";
   }

   public String getVoltage_status_33() {
      return "1".equals(this.getVoltage_status("3.3")) ? "Available" : "Not Available";
   }

   public String getVoltage_status_18() {
      return "1".equals(this.getVoltage_status("1.8")) ? "Available" : "Not Available";
   }

   public String getVoltage_status_12() {
      return "1".equals(this.getVoltage_status("1.2")) ? "Available" : "Not Available";
   }

   public String getVoltage_status_42() {
      return "1".equals(this.getVoltage_status("4.2")) ? "Available" : "Not Available";
   }

   public String getLed_open_detection_m1() {
      return this.getOpenDetection(0);
   }

   public String getLed_open_detection_m2() {
      return this.getOpenDetection(1);
   }

   public String getLed_open_detection_m3() {
      return this.getOpenDetection(2);
   }

   public String getLed_open_detection_m4() {
      return this.getOpenDetection(3);
   }

   public String getLed_open_detection_m5() {
      return this.getOpenDetection(4);
   }

   public String getLed_open_detection_m6() {
      return this.getOpenDetection(5);
   }

   private String getIc(String type) {
      if (this.ic == null) {
         return null;
      } else {
         String ret = null;
         String[] icInfo = this.ic.split(",");
         byte var5 = -1;
         switch(type.hashCode()) {
         case -1389927044:
            if (type.equals("power_detect_ic")) {
               var5 = 1;
            }
            break;
         case 48788:
            if (type.equals("13v")) {
               var5 = 2;
            }
            break;
         case 3149604:
            if (type.equals("fpga")) {
               var5 = 0;
            }
         }

         switch(var5) {
         case 0:
            if (icInfo.length > 0) {
               ret = icInfo[0].split(":")[1];
            }
            break;
         case 1:
            if (icInfo.length > 2) {
               ret = icInfo[2].split(":")[1];
            }
            break;
         case 2:
            if (icInfo.length > 3) {
               ret = icInfo[3].split(":")[1];
            }
         }

         return ret;
      }
   }

   private String getOpenDetection(int m) {
      if (this.open_detection == null) {
         return null;
      } else {
         String[] openDetections = this.open_detection.split(":");
         String[] products = this.module_product_number.split(":");
         if (openDetections.length != products.length) {
            return null;
         } else {
            return openDetections.length > m && StrUtils.isNotEmpty(openDetections[m]) ? openDetections[m] : null;
         }
      }
   }

   private String getVoltage_status(String type) {
      if (this.voltage_status == null) {
         return null;
      } else {
         String[] voltageInfo = this.voltage_status.split(",");
         if (voltageInfo.length < 1) {
            return null;
         } else {
            Map voltageMap = new HashMap();

            for(int i = 0; i < voltageInfo.length; ++i) {
               String temp = voltageInfo[i];
               if (temp.indexOf(":") >= 0) {
                  voltageMap.put(temp.split(":")[0], temp.split(":")[1]);
               }
            }

            return (String)voltageMap.get(type);
         }
      }
   }

   public String getAbl_str() {
      return this.abl <= 0L ? "OFF" : "ON";
   }

   public String getOn_screen_display() {
      return this.on_screen_display;
   }

   public void setOn_screen_display(String on_screen_display) {
      this.on_screen_display = on_screen_display;
   }

   public String getModule_product_number() {
      return this.module_product_number;
   }

   public void setModule_product_number(String product_number) {
      this.module_product_number = product_number;
   }

   public String getModule_date() {
      return this.module_date;
   }

   public void setModule_date(String module_date) {
      this.module_date = module_date;
   }

   public String getCurrent_temparature_with_f() {
      return this.current_temperature != null ? this.current_temperature + "℃ (" + (this.current_temperature * 9L / 5L + 32L) + " ℉)" : null;
   }
}
