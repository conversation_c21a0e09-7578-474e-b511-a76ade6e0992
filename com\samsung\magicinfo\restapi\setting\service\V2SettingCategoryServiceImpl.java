package com.samsung.magicinfo.restapi.setting.service;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.SequenceDB;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.setup.entity.CategoryEntity;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfo;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfoImpl;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.service.statistics.DeviceStatisticsDownloadService;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import javax.ws.rs.NotFoundException;
import org.apache.logging.log4j.Logger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2SettingCategoryService")
@Transactional
public class V2SettingCategoryServiceImpl implements V2SettingCategoryService {
   protected Logger logger = LoggingManagerV2.getLogger(V2SettingCategoryServiceImpl.class);
   private DeviceStatisticsDownloadService downloadService = null;

   public V2SettingCategoryServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority', 'Server Setup Manage Authority', 'Content Schedule Add Authority', 'Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public List getCategory(String id) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      String organization = userContainer.getUser().getOrganization();
      CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
      String mode = "view";
      List categoryList;
      if (id != null && !id.equals("")) {
         categoryList = categoryInfo.getCategoryWithPgroupId(Long.valueOf(id));
         return categoryList;
      } else {
         categoryList = null;
         if (userContainer.getUser().getRoot_group_id() == 0L) {
            categoryList = categoryInfo.getCategoryWithPgroupId(0L, "ALL");
         } else {
            categoryList = categoryInfo.getCategoryWithPgroupId(0L, organization);
         }

         List tempList;
         if (categoryList == null || categoryList != null && categoryList.size() < 1) {
            List originCategoryList = categoryInfo.getCategoryWithPgroupId(0L);
            this.logger.error("[MagicInfo_Category] init organization! id : " + userId);
            DeviceGroupInfo deviceInfo = DeviceGroupInfoImpl.getInstance();
            tempList = deviceInfo.getOrganizationGroup();
            Iterator var11 = tempList.iterator();

            while(var11.hasNext()) {
               DeviceGroup group = (DeviceGroup)var11.next();
               boolean createCategoryGroup = true;
               if (originCategoryList != null && originCategoryList.size() > 0) {
                  Iterator var14 = originCategoryList.iterator();

                  while(var14.hasNext()) {
                     CategoryEntity categoryEntity = (CategoryEntity)var14.next();
                     if (categoryEntity.getGroup_name().equals(group.getGroup_name())) {
                        createCategoryGroup = false;
                     }
                  }
               }

               if (createCategoryGroup) {
                  String org = group.getGroup_name();
                  long group_id = (long)SequenceDB.getNextValue("MI_CATEGORY_INFO_GROUP");
                  CategoryEntity category = new CategoryEntity();
                  category.setGroup_name(org);
                  category.setP_group_id(0L);
                  category.setGroup_id(group_id);
                  category.setGroup_depth(0L);
                  category.setDescription("Organization");
                  category.setCreator_id("admin");
                  category.setCreate_date(new Timestamp(System.currentTimeMillis()));
                  categoryInfo.addCategory(category);
               }
            }

            if (userContainer.getUser().getRoot_group_id() == 0L) {
               categoryList = categoryInfo.getCategoryWithPgroupId(0L, "ALL");
            } else {
               categoryList = categoryInfo.getCategoryWithPgroupId(0L, organization);
            }

            return categoryList;
         } else {
            int size = categoryList.size();

            for(int i = 0; i < size; ++i) {
               tempList = this.getSubCategory(((CategoryEntity)categoryList.get(i)).getGroup_id());
               if (tempList != null && tempList.size() != 0) {
                  categoryList.addAll(tempList);
               }
            }

            return categoryList;
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public CategoryEntity addCategory(String categoryName, String pGroupId) throws Exception {
      CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      CategoryEntity category = new CategoryEntity();
      CategoryEntity parentNode = categoryInfo.getCategory(Long.valueOf(pGroupId));
      category.setGroup_depth(parentNode.getGroup_depth() + 1L);
      category.setP_group_id(parentNode.getGroup_id());
      category.setGroup_name(categoryName);
      category.setCreator_id(userId);
      categoryInfo.addCategory(category);
      return category;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public CategoryEntity updateCategoryName(String categoryId, String categoryName) throws Exception {
      CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      CategoryEntity category = categoryInfo.getCategory(Long.valueOf(categoryId));
      if (category.getP_group_id() == 0L) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_EDIT_ORGANIZATION);
      } else {
         category.setGroup_name(categoryName);
         category.setCreator_id(userId);
         if (!categoryInfo.updateCategory(category)) {
            throw new Exception("Failed to update category.");
         } else {
            return category;
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public void deleteCategory(String categoryId) throws Exception {
      CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
      CategoryEntity entity = categoryInfo.getCategory(Long.valueOf(categoryId));
      if (entity == null) {
         throw new NotFoundException("Category is not found");
      } else if (entity.getP_group_id() == 0L) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_DELETE_ORGANIZATION);
      } else if (!categoryInfo.deleteCategory(Long.valueOf(categoryId))) {
         throw new Exception("Failed to delete category.");
      }
   }

   private List getSubCategory(Long categoryId) throws SQLException {
      CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
      List tempList = categoryInfo.getCategoryWithPgroupId(Long.valueOf(categoryId));
      List categoryList = new ArrayList();
      Iterator var5 = tempList.iterator();

      while(var5.hasNext()) {
         CategoryEntity category = (CategoryEntity)var5.next();
         categoryList.add(category);
         List rtList = this.getSubCategory(category.getGroup_id());
         if (rtList != null && rtList.size() != 0) {
            categoryList.addAll(rtList);
         }
      }

      return categoryList;
   }
}
