package com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.model.UploadResponse;
import com.samsung.magicinfo.webauthor2.model.VerificationResponse;
import com.samsung.magicinfo.webauthor2.service.LFDImportService;
import com.samsung.magicinfo.webauthor2.service.LFDMapperService;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping({"/LFDImport"})
public class LFDImportController {
  private static final Logger logger = LoggerFactory.getLogger(LFDImportController.class);
  
  private LFDMapperService lFDMapperService;
  
  private LFDImportService lFDImportService;
  
  @Autowired
  public LFDImportController(LFDMapperService lFDMapperService, LFDImportService lFDImportService) {
    this.lFDMapperService = lFDMapperService;
    this.lFDImportService = lFDImportService;
  }
  
  @PostMapping(value = {"/zipExtract"}, consumes = {"multipart/form-data"})
  public HttpEntity<List<String>> zipExtract(@RequestParam("upload") MultipartFile fileItem) throws IOException, UploaderException {
    List<String> extractedFileRelativePath = new ArrayList<>();
    extractedFileRelativePath = this.lFDImportService.getZipFileListForImport(fileItem);
    return (HttpEntity<List<String>>)ResponseEntity.ok(extractedFileRelativePath);
  }
  
  @PostMapping({"/Validate"})
  public HttpEntity<VerificationResponse> validateExtractedLfd(@RequestParam String xml) {
    VerificationResponse response = this.lFDMapperService.validateWithResponse(xml);
    return (HttpEntity<VerificationResponse>)ResponseEntity.ok(response);
  }
  
  @ExceptionHandler({Exception.class})
  public ResponseEntity<UploadResponse> generalExceptionHandler(Exception ex) {
    logger.error(ex.getMessage(), ex);
    UploadResponse uploadResponse = new UploadResponse(HttpStatus.INTERNAL_SERVER_ERROR.value(), ex.getMessage());
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(uploadResponse);
  }
  
  @ExceptionHandler({UploaderException.class})
  public HttpEntity<UploadResponse> uploaderExceptionHandler(UploaderException ex) {
    logger.error(ex.getMessage(), (Throwable)ex);
    UploadResponse response = new UploadResponse(ex.getErrorCode(), ex.getMessage());
    return (HttpEntity<UploadResponse>)ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
  }
  
  @ExceptionHandler({IOException.class})
  public HttpEntity<String> ioExceptionHandler(IOException ex) {
    logger.error(ex.getMessage(), ex);
    return (HttpEntity<String>)ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex.getMessage());
  }
}
