package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.Device;
import com.samsung.magicinfo.webauthor2.model.DeviceGroup;
import com.samsung.magicinfo.webauthor2.repository.OpenAPIDeviceRepository;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceData;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceGroupData;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceGroupLayout;
import java.util.List;
import javax.inject.Inject;
import org.springframework.stereotype.Service;

@Service
public class DeviceServiceImpl implements DeviceService {
  private OpenAPIDeviceRepository repository;
  
  @Inject
  public DeviceServiceImpl(OpenAPIDeviceRepository repository) {
    this.repository = repository;
  }
  
  public List<DeviceGroup> getDeviceGroupList() {
    List<DeviceGroupData> resultData = this.repository.getDeviceGroupList();
    return DeviceGroup.fromData(resultData);
  }
  
  public List<DeviceGroup> getDeviceGroupInfo(String parentGroupId) {
    List<DeviceGroupData> resultData = this.repository.getDeviceGroupInfo(parentGroupId);
    return DeviceGroup.fromData(resultData);
  }
  
  public List<Device> getDeviceList(String groupId) {
    List<DeviceData> resultData = this.repository.getDeviceList(groupId);
    return Device.fromData(resultData);
  }
  
  public List<Device> getDeviceListWithDeviceType(String groupId) {
    List<DeviceData> resultData = this.repository.getDeviceListWithDeviceType(groupId);
    return Device.fromData(resultData);
  }
  
  public DeviceGroupLayout getVideowallLayoutPath(String groupId) {
    DeviceGroupLayout groupLayout = this.repository.getVideowallLayoutPath(groupId);
    return groupLayout;
  }
  
  public Boolean hasWPlayerDevice() {
    List<DeviceData> resultData = this.repository.getWPlayerDeviceList();
    Boolean hasWPlayerDevice = Boolean.valueOf(!resultData.isEmpty());
    return hasWPlayerDevice;
  }
}
