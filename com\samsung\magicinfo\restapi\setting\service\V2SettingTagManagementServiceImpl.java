package com.samsung.magicinfo.restapi.setting.service;

import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.TagUtils;
import com.samsung.common.utils.page.ListManager;
import com.samsung.common.utils.page.PageManager;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.setup.entity.TagConditionEntity;
import com.samsung.magicinfo.framework.setup.entity.TagEntity;
import com.samsung.magicinfo.framework.setup.manager.TagInfo;
import com.samsung.magicinfo.framework.setup.manager.TagInfoImpl;
import com.samsung.magicinfo.framework.user.entity.UserGroup;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.protocol.constants.CommonConstants;
import com.samsung.magicinfo.restapi.common.model.V2CommonDeleteFail;
import com.samsung.magicinfo.restapi.common.model.V2CommonDeleteResult;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.exception.BaseRestException;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.setting.model.V2SettingTagListFilter;
import com.samsung.magicinfo.restapi.setting.model.V2SettingTagThumbAndMapping;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.validation.Valid;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2SettingTagManagementService")
@Transactional
public class V2SettingTagManagementServiceImpl implements V2SettingTagManagementService {
   public V2SettingTagManagementServiceImpl() {
      super();
   }

   public V2PageResource getTagListWithFilter(V2SettingTagListFilter filter) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      TagInfo tagDao = TagInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      String type = StrUtils.nvl(filter.getType()).equals("") ? "media" : filter.getType();
      String searchText = StrUtils.nvl(filter.getSearchText()).equals("") ? "" : filter.getSearchText();
      String organId = filter.getOrganId();
      if (userContainer.getUser().getRoot_group_id() != 0L && (organId == null || organId != null && organId.equalsIgnoreCase("ALL"))) {
         organId = CommonConstants.COMMON_ORGAN_ID + "," + userContainer.getUser().getRoot_group_id();
      }

      int pageSize = filter.getPageSize();
      ListManager listMgr = new ListManager(tagDao, "commonlist");
      if (type.equals("variable")) {
         listMgr.addSearchInfo("type", "0");
      }

      listMgr.addSearchInfo("sortColumn", filter.getSortColumn());
      listMgr.addSearchInfo("sortOrder", filter.getSortOrder());
      listMgr.addSearchInfo("searchText", searchText);
      listMgr.addSearchInfo("organId", organId);
      listMgr.addSearchInfo("tagContentCount", "true");
      if (type.equals("variable")) {
         listMgr.setSection("getTagList");
      } else {
         listMgr.setSection("getContentTag");
      }

      List searchList = listMgr.V2dbexecute(filter.getStartIndex(), filter.getPageSize());
      PageManager pageMgr = listMgr.getPageManager();
      if (null == searchList) {
         return V2PageResource.createPageResource(searchList, pageMgr);
      } else {
         for(int i = 0; i < searchList.size(); ++i) {
            TagEntity tag = (TagEntity)searchList.get(i);
            Long tagOrg = tag.getTag_organ();
            String tagOrgName = "";
            if (tagOrg == -2L) {
               tagOrgName = "Common";
            } else {
               UserGroup ug = userGroupInfo.getGroupById(tag.getTag_organ());
               if (ug != null) {
                  tagOrgName = ug.getGroup_name();
               }
            }

            ((TagEntity)searchList.get(i)).setTagOrgName(tagOrgName);
            int deviceMapCnt = tagDao.getDeviceTagMappingCnt(tag.getTag_id());
            ((TagEntity)searchList.get(i)).setDeviceCount(deviceMapCnt);
            List tagMap = tagDao.getTagConditionFromTagId((long)tag.getTag_id());
            if (tagMap != null && tag.getTag_type() == 0L) {
               String[] conditionStrArr = new String[tagMap.size()];

               for(int j = 0; j < tagMap.size(); ++j) {
                  conditionStrArr[j] = (String)((String)((Map)tagMap.get(j)).get("tag_condition"));
               }

               ((TagEntity)searchList.get(i)).setTag_condition(conditionStrArr);
            } else {
               ((TagEntity)searchList.get(i)).setTag_condition(new String[0]);
            }
         }

         V2PageResource resource = V2PageResource.createPageResource(searchList, pageMgr);
         return resource;
      }
   }

   public TagEntity addTagInfo(TagEntity tag) throws Exception {
      new TagEntity();
      long tagType = tag.getTag_type();
      long tagOrganId = tag.getTag_organ();
      String tagName = tag.getTag_name().trim();
      String tagDesc = tag.getTag_desc().trim();
      String tagValues = tag.getTag_name().trim();
      TagInfo tagDao = TagInfoImpl.getInstance();
      if (tagDao.chkTagName(tagName) > 0) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_TAG_NAME_DUPLICATED);
      } else {
         tagDao.addTagInfo(tagName, tagOrganId, tagDesc, tagType);
         tag = tagDao.getTagByName(tagName);
         return tag;
      }
   }

   public TagEntity getTagInfo(String tagId) throws Exception {
      TagInfo tagDao = TagInfoImpl.getInstance();
      TagEntity tag = tagDao.getTag(Integer.valueOf(tagId));
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      Long tagOrg = tag.getTag_organ();
      String tagOrgName = "";
      if (tagOrg == -2L) {
         tagOrgName = "Common";
      } else {
         tagOrgName = userGroupInfo.getGroupById(tag.getTag_organ()).getGroup_name();
      }

      tag.setTagOrgName(tagOrgName);
      if (tag.getTag_type() == 0L) {
         List tagMap = tagDao.getTagConditionEntityFromTagId((long)tag.getTag_id());
         tag.setTagConditionList(tagMap);
      }

      return tag;
   }

   public TagEntity editTagInfo(String tagId, @Valid TagEntity tag) throws Exception {
      TagInfo tagDao = TagInfoImpl.getInstance();
      TagEntity tagForCheck = tagDao.getTag(Integer.parseInt(tagId));
      if (!tagForCheck.getTag_name().equals(tag.getTag_name()) && tagDao.chkTagName(tag.getTag_name()) > 0) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_TAG_NAME_DUPLICATED);
      } else if (tagForCheck.getTag_name().isEmpty()) {
         throw new BaseRestException("INVALID_PARAM", "The tag does not exist.");
      } else {
         tag.setTag_value(tag.getTag_name());
         boolean isTagNameUpdated = !tagForCheck.getTag_name().equals(tag.getTag_name());
         tagDao.updateTagInfo(tag, isTagNameUpdated);
         TagEntity updatedTag = tagDao.getTag(Integer.parseInt(tagId));
         if (updatedTag.getTag_type() == 0L) {
            List tagMap = tagDao.getTagConditionEntityFromTagId((long)tag.getTag_id());
            updatedTag.setTagConditionList(tagMap);
         }

         return updatedTag;
      }
   }

   public V2CommonDeleteResult deleteTagInfo(List tagIds) throws Exception {
      V2CommonDeleteResult result = new V2CommonDeleteResult();
      List successList = new ArrayList();
      List failedList = new ArrayList();
      TagInfo tagDao = TagInfoImpl.getInstance();
      Iterator var6 = tagIds.iterator();

      while(true) {
         while(var6.hasNext()) {
            String tagId = (String)var6.next();
            int deviceMapCnt = tagDao.getTotalTagMappingCnt(Integer.parseInt(tagId));
            if (deviceMapCnt > 0) {
               V2CommonDeleteFail deleteFail = new V2CommonDeleteFail();
               deleteFail.setId(tagId);
               deleteFail.setReason("You cannot delete a tag that is currently in use");
               failedList.add(deleteFail);
            } else {
               try {
                  tagDao.deleteTagInfo(Integer.parseInt(tagId));
               } catch (Exception var11) {
                  V2CommonDeleteFail deleteFail = new V2CommonDeleteFail();
                  deleteFail.setId(tagId);
                  deleteFail.setReason("SQL EXCEPTION");
                  failedList.add(deleteFail);
                  continue;
               }

               successList.add(tagId);
            }
         }

         result.setDeletedSuccessList(successList);
         result.setDeletedFailList(failedList);
         return result;
      }
   }

   public List getCondition(String tagId) throws Exception {
      TagInfo tagDao = TagInfoImpl.getInstance();
      TagEntity tagForCheck = tagDao.getTag(Integer.valueOf(tagId));
      if (tagForCheck == null) {
         throw new BaseRestException("INVALID_PARAM", "The tag does not exist.");
      } else {
         List tagConditionList = tagDao.getTagConditionEntityFromTagId(Long.valueOf(tagId));
         if (tagConditionList != null && tagConditionList.size() > 0) {
            TagConditionEntity tagCondition = new TagConditionEntity();
            tagCondition.setTag_condition_id(-1L);
            tagCondition.setTag_id(Integer.valueOf(tagId));
            tagCondition.setTag_condition("Not Assigned");
            tagConditionList.add(tagCondition);
         }

         return tagConditionList;
      }
   }

   public TagConditionEntity addCondition(String tagId, String tagCondition) throws Exception {
      TagInfo tagDao = TagInfoImpl.getInstance();
      TagConditionEntity tagConditionEntity = new TagConditionEntity();
      TagEntity tagForCheck = tagDao.getTag(Integer.valueOf(tagId));
      if (tagCondition.trim().isEmpty()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_NAME_WITH_SPACE);
      } else if (tagForCheck == null) {
         throw new BaseRestException("INVALID_PARAM", "The tag does not exist.");
      } else {
         long tagConditionId = tagDao.addCondition(Long.valueOf(tagId), tagCondition);
         tagConditionEntity.setTag_condition_id(tagConditionId);
         tagConditionEntity.setTag_condition(tagCondition);
         tagConditionEntity.setTag_id(Integer.valueOf(tagId));
         return tagConditionEntity;
      }
   }

   public List updateConditionList(String tagId, List tagConditionList) throws Exception {
      TagInfo tagDao = TagInfoImpl.getInstance();
      List result = new ArrayList();
      List oldList = tagDao.getTagConditionEntityFromTagId((long)Integer.valueOf(tagId));
      Iterator var6 = tagConditionList.iterator();

      while(true) {
         while(true) {
            TagConditionEntity tagConditionEntity;
            label38:
            do {
               while(var6.hasNext()) {
                  tagConditionEntity = (TagConditionEntity)var6.next();
                  if (tagConditionEntity.getTag_condition_id() > 0L) {
                     continue label38;
                  }

                  long tagConditionId = tagDao.addCondition(Long.valueOf(tagId), tagConditionEntity.getTag_condition());
                  tagConditionEntity.setTag_condition_id(tagConditionId);
                  tagConditionEntity.setTag_id(Integer.valueOf(tagId));
                  result.add(tagConditionEntity);
               }

               if (null != oldList) {
                  var6 = oldList.iterator();

                  while(var6.hasNext()) {
                     tagConditionEntity = (TagConditionEntity)var6.next();
                     tagDao.deleteCondition(Long.valueOf(tagId), tagConditionEntity.getTag_condition_id());
                  }
               }

               return result;
            } while(null == oldList);

            Iterator var10 = oldList.iterator();

            while(var10.hasNext()) {
               TagConditionEntity oldTagConditionEntity = (TagConditionEntity)var10.next();
               if (oldTagConditionEntity.getTag_condition_id() == tagConditionEntity.getTag_condition_id() && oldTagConditionEntity.getTag_condition().equalsIgnoreCase(tagConditionEntity.getTag_condition())) {
                  result.add(tagConditionEntity);
                  oldList.remove(oldTagConditionEntity);
                  break;
               }
            }
         }
      }
   }

   public void deleteCondition(String tagId, String tagConditionId) throws Exception {
      TagInfo tagDao = TagInfoImpl.getInstance();
      TagEntity tagForCheck = tagDao.getTag(Integer.valueOf(tagId));
      if (tagForCheck == null) {
         throw new BaseRestException("INVALID_PARAM", "The tag does not exist.");
      } else if (tagDao.getContentTagCondition(Long.valueOf(tagId), Long.valueOf(tagConditionId)) < 1) {
         tagDao.deleteCondition(Long.valueOf(tagId), Long.valueOf(tagConditionId));
      } else {
         throw new BaseRestException("INVALID_PARAM", "The tag condition id is already used");
      }
   }

   public V2SettingTagThumbAndMapping getThumbAndMappingInfoWithTagAndConditions(String tagId, V2CommonIds v2CommonIds) throws Exception {
      TagInfo tagDao = TagInfoImpl.getInstance();
      TagEntity tag = tagDao.getTag(Integer.valueOf(tagId));
      String conditionId = null;
      if (tag == null) {
         throw new BaseRestException("INVALID_PARAM", "The tag does not exist.");
      } else {
         long tagType = tag.getTag_type();
         Map results = new LinkedHashMap();
         PlaylistInfo pInfo = PlaylistInfoImpl.getInstance();
         List tempConditionList = v2CommonIds.getIds();
         if (tagType == 1L) {
            List equals = new ArrayList();
            List ups = new ArrayList();
            List downs = new ArrayList();
            Iterator var14 = tempConditionList.iterator();

            while(var14.hasNext()) {
               String temp = (String)var14.next();
               if (temp != null && !temp.equals("")) {
                  String tagCondition = temp.trim();
                  String regexInequality = "^([0-9]*)(-)([0-9]*)$";
                  String regexNumber = "^([0-9]*)$";
                  if (tagCondition.matches(regexInequality)) {
                     String[] list = null;
                     if (tagCondition.indexOf("-") > 0) {
                        list = tagCondition.split("-");
                        ups.add(list[1]);
                        downs.add(list[0]);
                     }
                  } else if (tagCondition.matches(regexNumber) && tagCondition != null && !tagCondition.equals("")) {
                     equals.add(tagCondition);
                  }
               }
            }

            String[] tagConditionEqual = (String[])equals.toArray(new String[equals.size()]);
            String[] tagConditionUp = (String[])ups.toArray(new String[ups.size()]);
            String[] tagConditionDown = (String[])downs.toArray(new String[downs.size()]);
            conditionId = pInfo.getConditionIdWithTagNumber(Long.valueOf(tagId), tagConditionEqual, tagConditionUp, tagConditionDown);
         } else {
            conditionId = String.join(",", tempConditionList);
         }

         V2SettingTagThumbAndMapping v2SettingTagThumbAndMapping = new V2SettingTagThumbAndMapping();
         if (conditionId != null) {
            List contentInfo = pInfo.getCntContentAtTagPlaylist(Long.valueOf(tagId), conditionId);
            if (contentInfo != null) {
               results.put("contentInfo", contentInfo);
            }

            List thumb = pInfo.getThumbContentAtTagPlaylist(Long.valueOf(tagId), conditionId);
            if (thumb != null) {
               results.put("thumb", thumb);
            }

            v2SettingTagThumbAndMapping.setContentInfo(contentInfo);
            v2SettingTagThumbAndMapping.setThumb(thumb);
         }

         return v2SettingTagThumbAndMapping;
      }
   }

   public List getMappedContentListWithTagAndConditions(String tagId, V2CommonIds conditionIds, String deviceType, String deviceTypeVersion) throws Exception {
      String convertedDevice = "";
      String[] deviceTypeArr = null;
      if (!deviceType.equals("") && !deviceTypeVersion.equals("")) {
         convertedDevice = CommonUtils.convertDeviceTypeAndVerisonToDeviceType(deviceType, deviceTypeVersion);
         deviceTypeArr = CommonUtils.getAllSupportDeviceListByDeviceFilterList(convertedDevice.split(","));
      }

      List contentList = null;

      try {
         TagInfo tagInfo = TagInfoImpl.getInstance();
         PlaylistInfo pInfo = PlaylistInfoImpl.getInstance();
         TagEntity tag = tagInfo.getTag(Integer.valueOf(tagId));
         String conditionId = null;
         if (tag.getTag_type() == 1L) {
            if (conditionIds != null && !conditionIds.equals("")) {
               boolean chk = true;
               List equals = new ArrayList();
               List ups = new ArrayList();
               List downs = new ArrayList();
               List tempConditionList = conditionIds.getIds();
               Iterator var17 = tempConditionList.iterator();

               while(var17.hasNext()) {
                  String temp = (String)var17.next();
                  String tagCondition = temp.trim();
                  String regexInequality = "^([0-9]*)(-)([0-9]*)$";
                  String regexNumber = "^([0-9]*)$";
                  if (tagCondition.matches(regexInequality)) {
                     String[] list = null;
                     if (tagCondition.indexOf("-") > 0) {
                        list = tagCondition.split("-");
                        ups.add(list[1]);
                        downs.add(list[0]);
                     }
                  } else if (tagCondition.matches(regexNumber)) {
                     equals.add(tagCondition);
                  } else {
                     chk = true;
                  }
               }

               if (chk) {
                  String[] tagConditionEqual = (String[])equals.toArray(new String[equals.size()]);
                  String[] tagConditionUp = (String[])ups.toArray(new String[ups.size()]);
                  String[] tagConditionDown = (String[])downs.toArray(new String[downs.size()]);
                  conditionId = pInfo.getConditionIdWithTagNumber(Long.valueOf(tagId), tagConditionEqual, tagConditionUp, tagConditionDown);
               }

               if (conditionId != null) {
                  contentList = tagInfo.getMappedContentListByTagIdAndConditions(Long.valueOf(tagId), conditionId, deviceTypeArr);
               } else if (conditionId != null) {
                  contentList = tagInfo.getMappedContentListByTagIdAndConditions(Long.valueOf(tagId), conditionId, deviceTypeArr);
               }
            }
         } else {
            conditionId = String.join(",", conditionIds.getIds());
            contentList = tagInfo.getMappedContentListByTagIdAndConditions(Long.valueOf(tagId), conditionId, deviceTypeArr);
         }
      } catch (Exception var23) {
         var23.printStackTrace();
      }

      return contentList;
   }

   public ArrayList getTagMapingInfo(String tagId) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      TagInfo tagDao = TagInfoImpl.getInstance();
      TagEntity tag = tagDao.getTag(Integer.parseInt(tagId));
      if (tag == null) {
         throw new BaseRestException("INVALID_PARAM", "The tag does not exist.");
      } else {
         ArrayList mappingResult = TagUtils.getTagMappingInfoByTagId(Integer.valueOf(tagId), userContainer.getUser().getUser_id(), "");
         return mappingResult;
      }
   }

   public List getTagOrganInfo() throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      List result = new ArrayList();
      Group tagOrgan;
      if (userContainer.getUser().getRoot_group_id() == 0L) {
         tagOrgan = new Group();
         tagOrgan.setGroup_name("Common");
         tagOrgan.setGroup_id(CommonConstants.COMMON_ORGAN_ID);
         tagOrgan.setResponseDataType("TAG_BY_GROUP");
         result.add(tagOrgan);
         List manageGroupList = userGroupInfo.getUserManageGroupListByUserId(SecurityUtils.getLoginUserId());
         if (manageGroupList != null && manageGroupList.size() > 0) {
            Iterator var10 = manageGroupList.iterator();

            while(var10.hasNext()) {
               UserGroup userGroup = (UserGroup)var10.next();
               tagOrgan = new Group();
               tagOrgan.setResponseDataType("TAG_BY_GROUP");
               tagOrgan.setGroup_name(userGroup.getGroup_name());
               tagOrgan.setGroup_id(userGroup.getGroup_id());
               result.add(tagOrgan);
            }
         } else {
            List tempList = userGroupInfo.getAllOrganizationGroup();

            for(int i = 0; i < tempList.size(); ++i) {
               tagOrgan = new Group();
               tagOrgan.setResponseDataType("TAG_BY_GROUP");
               String groupName = ((Map)tempList.get(i)).get("group_name").toString();
               String groupId = ((Map)tempList.get(i)).get("group_id").toString();
               tagOrgan.setGroup_name(groupName);
               tagOrgan.setGroup_id(Long.parseLong(groupId));
               result.add(tagOrgan);
            }
         }
      } else {
         tagOrgan = new Group();
         tagOrgan.setResponseDataType("TAG_BY_GROUP");
         tagOrgan.setGroup_name(userContainer.getUser().getOrganization());
         tagOrgan.setGroup_id(userContainer.getUser().getRoot_group_id());
         result.add(tagOrgan);
         tagOrgan = new Group();
         tagOrgan.setResponseDataType("TAG_BY_GROUP");
         tagOrgan.setGroup_name("Common");
         tagOrgan.setGroup_id(CommonConstants.COMMON_ORGAN_ID);
         result.add(tagOrgan);
      }

      return result;
   }
}
