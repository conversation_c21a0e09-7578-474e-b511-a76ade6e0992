package com.samsung.magicinfo.openapi.custom.service;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.MDCTimeStrUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.SequenceDB;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.WakeOnLan;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.common.FileManagerImpl;
import com.samsung.magicinfo.framework.common.LeftMenuGroupTreeDao;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemInfoConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemSetupConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTimeConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTimeTimerConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.LedCabinet;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceGeneralConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceGeneralConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemInfoConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemInfoConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceTimeConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceTimeConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.LedCabinetConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.LedCabinetConfManagerImpl;
import com.samsung.magicinfo.framework.device.job.dao.JobDAO;
import com.samsung.magicinfo.framework.device.job.entity.JobEntity;
import com.samsung.magicinfo.framework.device.job.manager.JobManager;
import com.samsung.magicinfo.framework.device.job.manager.JobManagerImpl;
import com.samsung.magicinfo.framework.device.log.entity.LogCondition;
import com.samsung.magicinfo.framework.device.log.entity.ServerLogEntity;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManager;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManagerImpl;
import com.samsung.magicinfo.framework.device.ruleProcessing.entity.Alarm;
import com.samsung.magicinfo.framework.device.ruleProcessing.entity.Fault;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManager;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManagerImpl;
import com.samsung.magicinfo.framework.device.software.entity.Software;
import com.samsung.magicinfo.framework.device.software.entity.SoftwareId;
import com.samsung.magicinfo.framework.device.software.manager.SoftwareManager;
import com.samsung.magicinfo.framework.device.software.manager.SoftwareManagerImpl;
import com.samsung.magicinfo.framework.device.vwlLayout.entity.VwlLayout;
import com.samsung.magicinfo.framework.device.vwlLayout.manager.VwlLayoutManager;
import com.samsung.magicinfo.framework.device.vwlLayout.manager.VwlLayoutManagerImpl;
import com.samsung.magicinfo.framework.monitoring.dao.ScreenCaptureDAO;
import com.samsung.magicinfo.framework.monitoring.entity.ClientFaultEntity;
import com.samsung.magicinfo.framework.monitoring.entity.ContentList;
import com.samsung.magicinfo.framework.monitoring.entity.CurrentPlayingEntity;
import com.samsung.magicinfo.framework.monitoring.entity.DashboardEntity;
import com.samsung.magicinfo.framework.monitoring.entity.ScheduleInfoEntity;
import com.samsung.magicinfo.framework.monitoring.entity.ScreenCaptureEntity;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.role.entity.Ability;
import com.samsung.magicinfo.framework.role.entity.Role;
import com.samsung.magicinfo.framework.role.manager.AbilityInfo;
import com.samsung.magicinfo.framework.role.manager.AbilityInfoImpl;
import com.samsung.magicinfo.framework.role.manager.RoleInfo;
import com.samsung.magicinfo.framework.role.manager.RoleInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.EventManager;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfo;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.setup.dao.RmServerDao;
import com.samsung.magicinfo.framework.setup.entity.RmServerEntity;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManager;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManagerImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.openapi.auth.TokenRegistry;
import com.samsung.magicinfo.openapi.custom.domain.device.DeviceIF;
import com.samsung.magicinfo.openapi.custom.domain.device.DeviceImpl;
import com.samsung.magicinfo.openapi.custom.domain.log.LogIF;
import com.samsung.magicinfo.openapi.custom.domain.log.LogImpl;
import com.samsung.magicinfo.openapi.custom.openEntity.ResultList;
import com.samsung.magicinfo.openapi.custom.openEntity.device.Cabinet;
import com.samsung.magicinfo.openapi.custom.openEntity.device.CabinetGroup;
import com.samsung.magicinfo.openapi.custom.openEntity.device.DeviceCondition;
import com.samsung.magicinfo.openapi.custom.openEntity.device.DeviceConnectionEntity;
import com.samsung.magicinfo.openapi.custom.openEntity.device.DeviceGroupLayout;
import com.samsung.magicinfo.openapi.custom.service.builder.DeviceVncViewerJnlpStringBuilder;
import com.samsung.magicinfo.openapi.custom.service.builder.SoftwareUpdateFileUploaderJnlpStringBuilder;
import com.samsung.magicinfo.openapi.impl.DeviceUtil;
import com.samsung.magicinfo.openapi.impl.OpenApiExceptionCode;
import com.samsung.magicinfo.openapi.impl.OpenApiParameterValidator;
import com.samsung.magicinfo.openapi.impl.OpenApiServiceException;
import com.samsung.magicinfo.openapi.interfaces.OpenApiException;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.file.ChecksumCRC32;
import com.samsung.magicinfo.protocol.file.FileUploadCommonHelper;
import com.samsung.magicinfo.protocol.interfaces.WSCall;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.io.StringReader;
import java.net.ConnectException;
import java.net.SocketException;
import java.net.URL;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.KeyManager;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;
import javax.servlet.http.HttpServletRequest;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.logging.log4j.Logger;
import org.apache.xml.security.utils.Base64;
import org.springframework.context.annotation.Scope;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

@Service("openApiPremiumDeviceService")
@Scope("prototype")
public class PremiumDeviceService {
   static Logger logger = LoggingManagerV2.getLogger(DeviceService.class);
   String token = null;
   private UserContainer user = null;
   private TokenRegistry tokenRegistry = TokenRegistry.getTokenRegistry();

   public PremiumDeviceService() {
      super();
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public boolean deleteDeviceInfo(String deviceId) throws OpenApiServiceException {
      DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
      boolean result = false;

      try {
         result = deviceMgr.deleteDevice(deviceId);
         if (result) {
            MonitoringManager monMgr = MonitoringManagerImpl.getInstance();
            monMgr.connectionReload(deviceId, 0);
            monMgr.scheduleReload(deviceId, 0);
            WSCall.setPlayerRequest(deviceId, "agent restart");
            return result;
         } else {
            throw new OpenApiServiceException(OpenApiExceptionCode.D013[0], OpenApiExceptionCode.D013[1] + "(Delete fail)");
         }
      } catch (Exception var5) {
         logger.error(var5);
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      }
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public boolean deleteDeviceInfoForMega(String deviceId) throws OpenApiServiceException, SQLException {
      DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
      DeviceGroupInfo dGroupInfo = DeviceGroupInfoImpl.getInstance();
      Device targetDevice = deviceMgr.getDevice(deviceId);
      boolean result = false;

      try {
         result = deviceMgr.deleteDevice(deviceId);
      } catch (Exception var10) {
         logger.error(var10);
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      }

      if (!result) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D013[0], OpenApiExceptionCode.D013[1] + "(Delete fail)");
      } else {
         Long groupId = targetDevice.getGroup_id();
         logger.info("delete device group : " + groupId);
         if (groupId != null && groupId.intValue() >= 1) {
            dGroupInfo.delGroup(groupId.intValue());
         } else {
            logger.fatal("error to delete device group : " + groupId);
         }

         MonitoringManager monMgr = MonitoringManagerImpl.getInstance();
         monMgr.connectionReload(deviceId, 0);
         monMgr.scheduleReload(deviceId, 0);

         try {
            WSCall.setPlayerRequest(deviceId, "agent restart");
            return result;
         } catch (Exception var9) {
            logger.error(var9);
            throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
         }
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public Alarm getAlarmInfo(String alarmId) throws OpenApiServiceException {
      Alarm alarm = null;

      try {
         DeviceIF mgr = DeviceImpl.getInstance();
         alarm = mgr.getDeviceAlarmInfo(alarmId);
         if (alarm != null) {
            alarm.setAlarm_id(alarmId);
         }

         return alarm;
      } catch (SQLException var4) {
         logger.error(var4);
         throw new OpenApiServiceException(OpenApiExceptionCode.D006[0], OpenApiExceptionCode.D006[1] + "(Alarm Info)");
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public ResultList getAlarmList(int startPos, int pageSize, String sortName, String sortDir, String searchText) throws OpenApiServiceException {
      return this.getAlarmListByEventDate(startPos, pageSize, sortName, sortDir, searchText, (String)null);
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public ResultList getAlarmListByEventDate(int startPos, int pageSize, String sortName, String sortDir, String searchText, String eventDate) throws OpenApiServiceException {
      boolean canReadAuth = this.user.checkAuthority("Device Read");
      if (!canReadAuth) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D001[0], OpenApiExceptionCode.D001[1]);
      } else {
         DeviceIF mgr = DeviceImpl.getInstance();
         ResultList resultList = new ResultList();
         PagedListInfo pagedList = null;
         ArrayList openDeviceList = new ArrayList();

         try {
            Timestamp eventDateTimestamp = null;
            if (StringUtils.isNotBlank(eventDate)) {
               eventDateTimestamp = DateUtils.string2Timestamp(eventDate, "yyyy-MM-dd HH:mm:ss");
            }

            pagedList = mgr.getPagedAlarmDeviceList(startPos, pageSize, sortName, sortDir, searchText, eventDateTimestamp);
            List devList = pagedList.getPagedResultList();

            for(int i = 0; i < devList.size(); ++i) {
               Alarm alarm = (Alarm)devList.get(i);
               openDeviceList.add(alarm);
            }

            resultList.setResultList(openDeviceList);
            resultList.setTotalCount(pagedList.getTotalRowCount());
            return resultList;
         } catch (Exception var16) {
            logger.error(var16);
            throw new OpenApiServiceException(OpenApiExceptionCode.D006[0], OpenApiExceptionCode.D006[1] + "(Alarm List)");
         }
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public ResultList getAlarmListByDeviceId(int startPos, int pageSize, String sortName, String sortDir, String deviceId) throws OpenApiServiceException {
      DeviceIF mgr = DeviceImpl.getInstance();
      ResultList resultList = new ResultList();
      PagedListInfo pagedList = null;
      ArrayList openDeviceList = new ArrayList();

      try {
         pagedList = mgr.getPagedAlarmDeviceListByDeviceId(startPos, pageSize, sortName, sortDir, deviceId);
         List devList = pagedList.getPagedResultList();

         for(int i = 0; i < devList.size(); ++i) {
            Alarm alarm = (Alarm)devList.get(i);
            openDeviceList.add(alarm);
         }

         resultList.setResultList(openDeviceList);
         resultList.setTotalCount(pagedList.getTotalRowCount());
         return resultList;
      } catch (Exception var13) {
         logger.error(var13);
         throw new OpenApiServiceException(OpenApiExceptionCode.D006[0], OpenApiExceptionCode.D006[1] + "(Alarm List)");
      }
   }

   public DashboardEntity getDashBoardInfo(String userId) throws OpenApiServiceException, SQLException {
      DashboardEntity entity = null;
      MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
      UserInfo userInfo = UserInfoImpl.getInstance();
      RoleInfo roleInfo = RoleInfoImpl.getInstance();
      User user = userInfo.getUserInfo(userId);
      if (user == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.U001[0], OpenApiExceptionCode.U001[1]);
      } else {
         try {
            Role role = roleInfo.getRoleByUserId(userId);
            AbilityInfo abilityInfo = AbilityInfoImpl.getInstance();
            Long roleId = role.getRole_id();
            List abilityList = abilityInfo.getAbilityListByRoleId(roleId);
            new Ability();
            boolean canReadAuth = false;

            for(int i = 0; i < abilityList.size(); ++i) {
               Ability ability = (Ability)abilityList.get(i);
               if (ability.getAbility_name().contains("Device Read")) {
                  canReadAuth = true;
                  break;
               }
            }

            if (!canReadAuth) {
               throw new OpenApiServiceException(OpenApiExceptionCode.D001[0], OpenApiExceptionCode.D001[1]);
            }

            user.getRoot_group_id();
            DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
            String orgName = user.getOrganization();
            if (orgName.equals("ROOT")) {
               entity = motMgr.getDashboardStatus();
            } else {
               deviceGroupInfo.getOrganGroupIdByName(orgName);
               entity = motMgr.getDashboardStatus(orgName, (String)null, (Float)null);
            }
         } catch (Exception var15) {
            logger.error(var15);
            throw new OpenApiServiceException(OpenApiExceptionCode.G114[0], OpenApiExceptionCode.G114[1]);
         }

         if (entity == null) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D006[0], OpenApiExceptionCode.D006[1] + "(DashBoard Info)");
         } else {
            return entity;
         }
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public DeviceGeneralConf getDeviceBasicInfo(String deviceId) throws OpenApiServiceException {
      DeviceGeneralConf info = null;
      DeviceInfo deviceInfo = null;

      try {
         deviceInfo = DeviceInfoImpl.getInstance();
         info = deviceInfo.getDeviceGeneralConf(deviceId);
      } catch (Exception var5) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D013[0], OpenApiExceptionCode.D013[1]);
      }

      if (info == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D013[0], OpenApiExceptionCode.D013[1]);
      } else {
         return info;
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public boolean getDeviceConnection(String deviceId) throws OpenApiException {
      MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
      return motMgr.isConnected(deviceId);
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public ResultList getDeviceConnectionList(String deviceIds) throws OpenApiException {
      ResultList resultList = new ResultList();
      MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
      List deviceConnectionList = new ArrayList();
      String[] arrDeviceId = deviceIds.split(",");
      int loopSize = arrDeviceId.length;

      for(int i = 0; i < loopSize; ++i) {
         DeviceConnectionEntity deviceConnectionEntity = new DeviceConnectionEntity(arrDeviceId[i], motMgr.isConnected(arrDeviceId[i]));
         deviceConnectionList.add(deviceConnectionEntity);
      }

      resultList.setResultList(deviceConnectionList);
      resultList.setTotalCount(deviceConnectionList.size());
      return resultList;
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public DeviceDisplayConf getDeviceDisplayInfo(String deviceId) throws OpenApiServiceException {
      DeviceDisplayConf info = null;

      try {
         DeviceDisplayConfManager displayConf = DeviceDisplayConfManagerImpl.getInstance();
         info = displayConf.getDeviceDisplayConf(deviceId);
      } catch (Exception var4) {
         logger.error(var4);
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      }

      if (info == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D006[0], OpenApiExceptionCode.D006[1] + "(Display Info)");
      } else {
         return info;
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public DeviceDisplayConf getDeviceDisplayInfoByRealTime(String deviceId, int reqTimeOut) throws OpenApiServiceException {
      DeviceDisplayConf info = null;
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      DeviceInfo deviceDao = null;
      Device device = null;

      try {
         deviceDao = DeviceInfoImpl.getInstance();
         device = deviceDao.getDeviceMinInfo(deviceId);
      } catch (Exception var13) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      }

      if (device == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D010[0], OpenApiExceptionCode.D010[1] + "(Check Device)");
      } else {
         boolean isConn = true;

         try {
            isConn = this.getDeviceConnection(deviceId);
         } catch (Exception var12) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
         }

         if (!isConn) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D015[0], OpenApiExceptionCode.D015[1]);
         } else {
            try {
               confMgr.reqGetDisplayFromDevice(deviceId, this.getToken(), "ALL_MDC");
            } catch (Exception var11) {
               logger.error(var11);
               throw new OpenApiServiceException(OpenApiExceptionCode.D010[0], OpenApiExceptionCode.D010[1]);
            }

            int i = false;

            int i;
            for(i = 0; i < reqTimeOut; ++i) {
               info = confMgr.getDisplayResultSet(deviceId, this.getToken(), "GET_DEVICE_DISPLAY_CONF", "ALL_MDC");
               if (info != null) {
                  break;
               }

               try {
                  Thread.sleep(1000L);
               } catch (InterruptedException var10) {
                  logger.error(var10);
                  throw new OpenApiServiceException(OpenApiExceptionCode.D011[0], OpenApiExceptionCode.D011[1]);
               }
            }

            if (info == null && i >= reqTimeOut) {
               throw new OpenApiServiceException(OpenApiExceptionCode.D016[0], OpenApiExceptionCode.D016[1]);
            } else {
               return info;
            }
         }
      }
   }

   @PreAuthorize("hasAnyRole('Device Read Authority', 'Content Schedule Add Authority', 'Content Schedule Write Authority', 'Message Schedule Write Authority', 'Message Schedule Manage Authority')")
   public ResultList getDeviceListWithDeviceType(DeviceCondition condition, String deviceType) throws OpenApiException {
      new ArrayList();
      ResultList resultList = new ResultList();
      DeviceIF devIF = DeviceImpl.getInstance();
      MonitoringManagerImpl.getInstance();
      if (deviceType.equalsIgnoreCase("ALL")) {
         deviceType = "";
      }

      try {
         List deviceList = devIF.getPagedDeviceListWithDeviceType(condition, deviceType);
         resultList.setTotalCount(deviceList.size());
         resultList.setResultList(deviceList);
         return resultList;
      } catch (Exception var10) {
         logger.error(var10);
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      } finally {
         ;
      }
   }

   @PreAuthorize("hasAnyRole('Device Read Authority', 'Content Schedule Add Authority', 'Content Schedule Write Authority', 'Message Schedule Write Authority', 'Message Schedule Manage Authority')")
   public ResultList getDeviceList(DeviceCondition condition, String deviceType) throws OpenApiException {
      new ArrayList();
      ResultList resultList = new ResultList();
      DeviceIF devIF = DeviceImpl.getInstance();
      MonitoringManagerImpl.getInstance();
      if (deviceType.equalsIgnoreCase("ALL")) {
         deviceType = "";
      }

      try {
         List deviceList = devIF.getPagedDeviceList(condition, deviceType);
         int totalCount = devIF.getPagedDeviceListCnt(condition, deviceType);
         resultList.setTotalCount(totalCount);
         resultList.setResultList(deviceList);
         return resultList;
      } catch (Exception var10) {
         logger.error(var10);
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      } finally {
         ;
      }
   }

   @PreAuthorize("hasAnyRole('Device Read Authority', 'Content Schedule Add Authority', 'Content Schedule Write Authority', 'Message Schedule Write Authority', 'Message Schedule Manage Authority')")
   public ResultList getDeviceListWithFullGroupName(DeviceCondition condition, String deviceType) throws OpenApiException {
      new ArrayList();
      ResultList resultList = new ResultList();
      DeviceIF devIF = DeviceImpl.getInstance();
      MonitoringManagerImpl.getInstance();
      if (deviceType.equalsIgnoreCase("ALL")) {
         deviceType = "";
      }

      try {
         List deviceList = devIF.getDeviceListWithFullGroupName(condition, deviceType);
         if (deviceList != null) {
            Iterator var6 = deviceList.iterator();

            label86:
            while(true) {
               Device device;
               List groupList;
               do {
                  if (!var6.hasNext()) {
                     break label86;
                  }

                  device = (Device)var6.next();
                  groupList = DeviceUtils.getParentGroupNamePathByGroupId((int)device.getGroup_id());
               } while(groupList == null);

               StringBuffer str = new StringBuffer();

               for(int i = 0; i < groupList.size(); ++i) {
                  if (i > 0) {
                     str.append(" > ");
                  }

                  str.append(((DeviceGroup)groupList.get(i)).getGroup_name());
               }

               device.setGroup_id(device.getGroup_id());
               device.setGroup_name(str.toString());
            }
         }

         int totalCount = devIF.getPagedDeviceListCnt(condition, deviceType);
         resultList.setTotalCount(totalCount);
         resultList.setResultList(deviceList);
         return resultList;
      } catch (Exception var14) {
         logger.error(var14);
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      } finally {
         ;
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public CurrentPlayingEntity getDevicePlayingContent(String deviceId) throws OpenApiServiceException {
      MonitoringManager motMgr = null;
      CurrentPlayingEntity playingEntity = null;

      try {
         motMgr = MonitoringManagerImpl.getInstance();
         playingEntity = motMgr.getPlayingContent(deviceId);
      } catch (Exception var5) {
         logger.error(var5);
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      }

      if (playingEntity == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D006[0], OpenApiExceptionCode.D006[1] + "(Currnet Playing Info)");
      } else {
         return playingEntity;
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public ResultList getDevicePlayingContents(String deviceIds) throws OpenApiServiceException {
      ResultList resultList = new ResultList();
      String[] arrDeviceId = deviceIds.split(",");
      MonitoringManager motMgr = null;
      ArrayList list = new ArrayList();

      try {
         motMgr = MonitoringManagerImpl.getInstance();
         if (arrDeviceId != null && arrDeviceId.length > 0) {
            String[] var6 = arrDeviceId;
            int var7 = arrDeviceId.length;

            for(int var8 = 0; var8 < var7; ++var8) {
               String deviceId = var6[var8];
               CurrentPlayingEntity playingEntity = motMgr.getPlayingContent(deviceId);
               if (playingEntity != null) {
                  Map map = new HashMap();
                  map.put(deviceId, playingEntity);
                  list.add(map);
               }
            }
         }
      } catch (Exception var12) {
         logger.error(var12);
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      }

      if (list == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D006[0], OpenApiExceptionCode.D006[1] + "(Currnet Playing Info)");
      } else {
         resultList.setTotalCount(list.size());
         resultList.setResultList(list);
         return resultList;
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public DeviceSystemInfoConf getDeviceSystemInfo(String deviceId) throws OpenApiServiceException {
      DeviceSystemInfoConf info = null;

      try {
         DeviceSystemInfoConfManager systemInfoDao = DeviceSystemInfoConfManagerImpl.getInstance();
         info = systemInfoDao.getDeviceSystemInfoConf(deviceId);
      } catch (Exception var4) {
         logger.error(var4);
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      }

      if (info == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D013[0], OpenApiExceptionCode.D013[1]);
      } else {
         return info;
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public DeviceSystemInfoConf getDeviceSystemInfoByRealTime(String deviceId, int reqTimeOut) throws OpenApiServiceException {
      DeviceSystemInfoConf info = null;
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      DeviceInfo deviceDao = null;
      Device device = null;

      try {
         deviceDao = DeviceInfoImpl.getInstance();
         device = deviceDao.getDeviceMinInfo(deviceId);
      } catch (Exception var13) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      }

      if (device == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D010[0], OpenApiExceptionCode.D010[1] + "(Check Device)");
      } else {
         boolean isConn = true;

         try {
            isConn = this.getDeviceConnection(deviceId);
         } catch (Exception var12) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
         }

         if (!isConn) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D015[0], OpenApiExceptionCode.D015[1]);
         } else {
            try {
               confMgr.reqGetSystemInfoFromDevice(deviceId, this.getToken());
            } catch (Exception var11) {
               logger.error(var11);
               throw new OpenApiServiceException(OpenApiExceptionCode.D010[0], OpenApiExceptionCode.D010[1]);
            }

            int i = false;

            int i;
            for(i = 0; i < reqTimeOut; ++i) {
               info = confMgr.getSystemInfoResultSet(deviceId, this.getToken(), "GET_DEVICE_SYSTEM_INFO_CONF");
               if (info != null) {
                  break;
               }

               try {
                  Thread.sleep(1000L);
               } catch (InterruptedException var10) {
                  logger.error(var10);
                  throw new OpenApiServiceException(OpenApiExceptionCode.D011[0], OpenApiExceptionCode.D011[1]);
               }
            }

            if (info == null && i >= reqTimeOut) {
               throw new OpenApiServiceException(OpenApiExceptionCode.D016[0], OpenApiExceptionCode.D016[1]);
            } else {
               return info;
            }
         }
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public DeviceSystemSetupConf getDeviceSystemSetupInfo(String deviceId) throws OpenApiServiceException {
      DeviceSystemSetupConf info = null;

      try {
         DeviceSystemSetupConfManager systemSetupDao = DeviceSystemSetupConfManagerImpl.getInstance();
         info = systemSetupDao.getDeviceSystemSetupConf(deviceId);
      } catch (Exception var4) {
         logger.error(var4);
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      }

      if (info == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D013[0], OpenApiExceptionCode.D013[1]);
      } else {
         return info;
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public DeviceTimeConf getDeviceTimeInfo(String deviceId) throws OpenApiServiceException {
      DeviceTimeConf info = null;
      DeviceInfo mgr = DeviceInfoImpl.getInstance();
      String deviceModelCode = "";

      try {
         deviceModelCode = mgr.getDeviceModelCodeByDeviceId(deviceId);
      } catch (Exception var9) {
         logger.error(var9);
         throw new OpenApiServiceException(OpenApiExceptionCode.D013[0], OpenApiExceptionCode.D013[1]);
      }

      try {
         String modelKind = MDCTimeStrUtils.getModelKind(deviceModelCode);
         int timerCnt = MDCTimeStrUtils.getTimerCnt(deviceModelCode);
         DeviceTimeConfManager dao = DeviceTimeConfManagerImpl.getInstance();
         if (modelKind.equalsIgnoreCase("NEW")) {
            info = dao.getDeviceNewTimeConf(deviceId, timerCnt);
         } else {
            info = dao.getDeviceOldTimeConf(deviceId);
         }
      } catch (Exception var8) {
         logger.error(var8);
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      }

      if (info == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D006[0], OpenApiExceptionCode.D006[1] + "(Time Info)");
      } else {
         return info;
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public DeviceTimeConf getDeviceTimeInfoByRealTime(String deviceId, int reqTimeOut) throws OpenApiServiceException {
      DeviceTimeConf info = null;
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      DeviceInfo deviceDao = null;
      Device device = null;

      try {
         deviceDao = DeviceInfoImpl.getInstance();
         device = deviceDao.getDeviceMinInfo(deviceId);
      } catch (Exception var13) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      }

      if (device == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D010[0], OpenApiExceptionCode.D010[1] + "(Check Device)");
      } else {
         boolean isConn = true;

         try {
            isConn = this.getDeviceConnection(deviceId);
         } catch (Exception var12) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
         }

         if (!isConn) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D015[0], OpenApiExceptionCode.D015[1]);
         } else {
            try {
               confMgr.reqGetTimeFromDevice(deviceId, this.getToken());
            } catch (Exception var11) {
               logger.error(var11);
               throw new OpenApiServiceException(OpenApiExceptionCode.D010[0], OpenApiExceptionCode.D010[1]);
            }

            int i = false;

            int i;
            for(i = 0; i < reqTimeOut; ++i) {
               info = confMgr.getTimeResultSet(deviceId, this.getToken(), "GET_DEVICE_TIME_CONF");
               if (info != null) {
                  break;
               }

               try {
                  Thread.sleep(1000L);
               } catch (InterruptedException var10) {
                  logger.error(var10);
                  throw new OpenApiServiceException(OpenApiExceptionCode.D011[0], OpenApiExceptionCode.D011[1]);
               }
            }

            if (info == null && i >= reqTimeOut) {
               throw new OpenApiServiceException(OpenApiExceptionCode.D016[0], OpenApiExceptionCode.D016[1]);
            } else {
               return info;
            }
         }
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public String getDeviceVncInfo(String deviceId, String tunnelingIP, String serverUrl) throws OpenApiServiceException {
      DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
      SlmLicenseManager slmLicenseMgr = SlmLicenseManagerImpl.getInstance();
      String jnlpPasswordParam = "";
      String jnlpFileName = "";
      String realPath = "";
      String urlToken = Base64.encode(this.tokenRegistry.issueToken(SecurityUtils.getUserContainer()).getBytes());
      String serverUrlPath = serverUrl + "/MagicInfo/uploader/" + urlToken + "/jnlp/";

      try {
         String miHome = slmLicenseMgr.getRegistryValue("path");
         if (miHome != null && !miHome.equals("")) {
            realPath = miHome + File.separator + "server" + File.separator + "uploader" + File.separator + "jnlp" + File.separator;
         }
      } catch (Exception var20) {
      }

      if (realPath.equals("")) {
         realPath = "C:/MagicInfo-i Premium/server/uploader/jnlp/";
         logger.error("RegisteryValue(path) is invalid. new path=" + realPath);
      }

      try {
         Device device = deviceMgr.getDevice(deviceId);
         if (device.getVnc_password() != null && !device.getVnc_password().equals("")) {
            jnlpPasswordParam = "       <param name='PASSWORD' value='" + device.getVnc_password() + "'>\n";
         }

         SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
         Calendar cal = Calendar.getInstance();
         jnlpFileName = "openApiVncViewer_" + formatter.format(cal.getTime()) + ".jnlp";
         SecurityUtils.getSafeFile(realPath).mkdirs();
         File jnlpFile = SecurityUtils.getSafeFile(realPath + jnlpFileName);
         jnlpFile.createNewFile();
         String browserHeight = CommonConfig.get("VNC_VIEWER_HEIGHT");
         String browserWidth = CommonConfig.get("VNC_VIEWER_WIDTH");
         if (browserHeight == null) {
            browserHeight = "800";
         }

         if (browserWidth == null) {
            browserWidth = "1500";
         }

         DeviceVncViewerJnlpStringBuilder vncViewerJnlpBuilder = (new DeviceVncViewerJnlpStringBuilder()).addServerUrlPath(serverUrlPath).addJnlpFilename(jnlpFileName).addBrowserWidth(browserWidth).addBrowserHeight(browserHeight).addDeviceId(deviceId).addTunnelingIp(tunnelingIP).addJnlpPasswordParam(jnlpPasswordParam);
         FileWriter writer = new FileWriter(jnlpFile);
         PrintWriter fout = new PrintWriter(writer);
         fout.write(vncViewerJnlpBuilder.build());
         fout.close();
         writer.close();
      } catch (Exception var21) {
         logger.error(var21);
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      }

      return serverUrlPath + jnlpFileName;
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public Fault getFaultInfo(Long faultId) throws OpenApiServiceException {
      Fault fault = null;

      try {
         DeviceIF mgr = DeviceImpl.getInstance();
         fault = mgr.getDeviceFaultInfo(faultId);
         if (fault != null) {
            fault.setFault_id(faultId);
         }

         return fault;
      } catch (SQLException var4) {
         logger.error(var4);
         throw new OpenApiServiceException(OpenApiExceptionCode.D006[0], OpenApiExceptionCode.D006[1] + "(Fault Info)");
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public ResultList getFaultList(int startPos, int pageSize, String sortName, String sortDir, String searchText) throws OpenApiServiceException {
      return this.getFaultListByEventDate(startPos, pageSize, sortName, sortDir, searchText, (String)null);
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public ResultList getFaultListByEventDate(int startPos, int pageSize, String sortName, String sortDir, String searchText, String eventDate) throws OpenApiServiceException {
      DeviceIF mgr = DeviceImpl.getInstance();
      ResultList resultList = new ResultList();
      PagedListInfo pagedList = null;
      ArrayList openDeviceList = new ArrayList();

      try {
         Timestamp eventDateTimestamp = null;
         if (StringUtils.isNotBlank(eventDate)) {
            eventDateTimestamp = DateUtils.string2Timestamp(eventDate, "yyyy-MM-dd HH:mm:ss");
         }

         pagedList = mgr.getPagedFaultDeviceList(startPos, pageSize, sortName, sortDir, searchText, eventDateTimestamp);
         List devList = pagedList.getPagedResultList();

         for(int i = 0; i < devList.size(); ++i) {
            Fault fault = (Fault)devList.get(i);
            if (fault.getProcessor_id() == null || fault.getProcessor_id().length() == 0) {
               openDeviceList.add(fault);
            }
         }

         resultList.setResultList(openDeviceList);
         resultList.setTotalCount(openDeviceList.size());
         return resultList;
      } catch (SQLException var15) {
         logger.error(var15);
         throw new OpenApiServiceException(OpenApiExceptionCode.D006[0], OpenApiExceptionCode.D006[1] + "(Fault List)");
      } catch (Exception var16) {
         logger.error(var16);
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public ResultList getFaultListByDeviceId(int startPos, int pageSize, String sortName, String sortDir, String deviceId) throws OpenApiServiceException {
      DeviceIF mgr = DeviceImpl.getInstance();
      ResultList resultList = new ResultList();
      PagedListInfo pagedList = null;
      ArrayList openDeviceList = new ArrayList();

      try {
         pagedList = mgr.getPagedFaultDeviceListByDeviceId(startPos, pageSize, sortName, sortDir, deviceId);
         List devList = pagedList.getPagedResultList();

         for(int i = 0; i < devList.size(); ++i) {
            Fault fault = (Fault)devList.get(i);
            if (fault.getProcessor_id() == null || fault.getProcessor_id().length() == 0) {
               openDeviceList.add(fault);
            }
         }

         resultList.setResultList(openDeviceList);
         resultList.setTotalCount(openDeviceList.size());
         return resultList;
      } catch (SQLException var13) {
         logger.error(var13);
         throw new OpenApiServiceException(OpenApiExceptionCode.D006[0], OpenApiExceptionCode.D006[1] + "(Fault List)");
      } catch (Exception var14) {
         logger.error(var14);
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      }
   }

   @PreAuthorize("hasAnyRole('Device Read Authority')")
   public ResultList getDeviceGroupInfo(String groupId) throws OpenApiServiceException, SQLException {
      ResultList resultList = new ResultList();
      List deviceGroup = null;
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Long orgId = 0L;
      String userId = this.user.getUser().getUser_id();
      Boolean useDevicePermission = false;
      String orgName = this.user.getUser().getOrganization();
      Long userOrgId = userGroupInfo.getOrgGroupIdByName(this.user.getUser().getOrganization());
      if (groupId != null && groupId.equals("ROOT")) {
         if (orgName.equals("ROOT")) {
            orgId = 0L;
            userId = null;
         } else {
            orgId = groupDao.getOrganGroupIdByName(orgName);
         }
      } else {
         try {
            orgId = Long.valueOf(groupId);
            if (orgName.equals("ROOT")) {
               userId = null;
            }
         } catch (Exception var14) {
            logger.error(var14);
            throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
         }
      }

      if (useDevicePermission) {
         deviceGroup = groupDao.getGroupByIdWithPermission("PREMIUM", "DEVICE", orgId, userId, false);
      } else {
         deviceGroup = groupDao.getGroupById("PREMIUM", "DEVICE", orgId, false);
      }

      Iterator var12 = deviceGroup.iterator();

      while(var12.hasNext()) {
         DeviceGroup group = (DeviceGroup)var12.next();
         System.out.println("group vwt : " + group.getVwt_id());
         System.out.println("group device count : " + group.getDevice_count());
      }

      resultList.setResultList(deviceGroup);
      resultList.setTotalCount(deviceGroup.size());
      return resultList;
   }

   @PreAuthorize("hasAnyRole('Device Read Authority', 'Content Schedule Add Authority', 'Content Schedule Write Authority', 'Message Schedule Write Authority', 'Message Schedule Manage Authority')")
   public ResultList getDeviceGroupList(Boolean recursive, String deviceType) throws OpenApiServiceException, SQLException {
      UserInfoImpl.getInstance();
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      ResultList resultList = new ResultList();
      List deviceGroup = null;
      Long orgId = 0L;
      String orgName = this.user.getUser().getOrganization();
      Boolean useDevicePermission = false;
      String userId = null;
      Long userOrgId = userGroupInfo.getOrgGroupIdByName(this.user.getUser().getOrganization());
      if (orgName.equals("ROOT")) {
         orgId = 0L;
      } else {
         orgId = groupDao.getOrganGroupIdByName(orgName);
         userId = this.user.getUser().getUser_id();
      }

      useDevicePermission = serverSetupDao.checkPermissionsDeviceByOrgId(userOrgId);

      try {
         if (orgId != null) {
            if (useDevicePermission) {
               if (recursive != null) {
                  deviceGroup = groupDao.getChildGroupListWithPermission(orgId.intValue(), userId, recursive);
               } else {
                  deviceGroup = groupDao.getChildGroupListWithPermission(orgId.intValue(), userId, false);
               }
            } else if (recursive != null) {
               deviceGroup = groupDao.getChildGroupList(orgId.intValue(), recursive);
            } else {
               deviceGroup = groupDao.getChildGroupList(orgId.intValue(), false);
            }
         }
      } catch (Exception var17) {
         logger.error(var17);
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      }

      if (deviceGroup == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D006[0], OpenApiExceptionCode.D006[1] + " (Group List)");
      } else {
         String hideType = "SPLAYER";
         if (deviceType.equals("iPLAYER")) {
            hideType = "SPLAYER";
         } else if (deviceType.equals("SPLAYER")) {
            hideType = "iPLAYER";
         } else {
            hideType = "ALL";
         }

         new DeviceGroup();

         for(int i = 0; i < deviceGroup.size(); ++i) {
            DeviceGroup myDeviceGroup = (DeviceGroup)deviceGroup.get(i);
            if (myDeviceGroup.getGroup_type() != null) {
               if (!deviceType.equalsIgnoreCase("3rdPartyPLAYER")) {
                  if (myDeviceGroup.getGroup_type().equals(hideType) || myDeviceGroup.getGroup_type().equals("3rdPartyPLAYER") || myDeviceGroup.getGroup_type() == null) {
                     deviceGroup.remove(i);
                     --i;
                  }
               } else if (!myDeviceGroup.getGroup_type().equals("3rdPartyPLAYER")) {
                  deviceGroup.remove(i);
                  --i;
               }
            }
         }

         resultList.setResultList(deviceGroup);
         resultList.setTotalCount(deviceGroup.size());
         return resultList;
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public String getMagicInfoServer(String deviceId) throws OpenApiServiceException {
      DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
      Device device = null;
      String magicInfoServerUrl = "";

      try {
         device = deviceMgr.getDevice(deviceId);
         if (device != null) {
            magicInfoServerUrl = device.getMagicinfo_server_url();
            return magicInfoServerUrl;
         } else {
            throw new OpenApiServiceException(OpenApiExceptionCode.D013[0], OpenApiExceptionCode.D013[1]);
         }
      } catch (SQLException var6) {
         logger.error(var6);
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public ResultList getTimeZoneInfo(String timeZoneVersion) throws OpenApiServiceException {
      ResultList resultList = new ResultList();
      DeviceSystemSetupConfManager deviceConf = DeviceSystemSetupConfManagerImpl.getInstance();
      List timeZoneList = null;

      try {
         timeZoneList = deviceConf.getTimeZoneMapList2(timeZoneVersion);
      } catch (SQLException var6) {
         logger.error(var6);
         throw new OpenApiServiceException(OpenApiExceptionCode.D006[0], OpenApiExceptionCode.D006[1]);
      }

      resultList.setResultList(timeZoneList);
      resultList.setTotalCount(timeZoneList.size());
      return resultList;
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public Boolean modifyDeviceName(String deviceId, String newDeviceName) throws Exception {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      DeviceGeneralConfManager deviceGeneralConf = DeviceGeneralConfManagerImpl.getInstance();
      DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
      DeviceGeneralConf info = new DeviceGeneralConf();
      info.setDevice_id(deviceId);
      info.setDevice_name(newDeviceName);
      boolean dbResult = deviceGeneralConf.setDeviceGeneralConf(info);
      if (dbResult) {
         confManager.reqSetGeneralToDevice(info, "Session");
         return true;
      } else {
         return false;
      }
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public DeviceDisplayConf modifyDeviceDisplayInfo(DeviceDisplayConf info, int reqTimeOut) throws OpenApiServiceException {
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      if (reqTimeOut < 1) {
         reqTimeOut = 10;
      }

      if (info != null && info.getDevice_id() != null && !info.getDevice_id().equals("")) {
         DeviceInfo deviceDao = null;
         Device device = null;
         if (info != null && info.getDevice_id() != null && !info.getDevice_id().equals("")) {
            try {
               deviceDao = DeviceInfoImpl.getInstance();
               device = deviceDao.getDeviceMinInfo(info.getDevice_id());
            } catch (Exception var14) {
               throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
            }

            if (device == null) {
               throw new OpenApiServiceException(OpenApiExceptionCode.D007[0], OpenApiExceptionCode.D007[1] + "(Check Device)");
            } else {
               boolean isConn = true;

               try {
                  isConn = this.getDeviceConnection(info.getDevice_id());
               } catch (Exception var13) {
                  throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
               }

               if (!isConn) {
                  throw new OpenApiServiceException(OpenApiExceptionCode.D015[0], OpenApiExceptionCode.D015[1]);
               } else {
                  try {
                     confMgr.reqSetDisplayToDevice(info, this.getToken(), "ALL_MDC");
                  } catch (Exception var12) {
                     logger.error(var12);
                     throw new OpenApiServiceException(OpenApiExceptionCode.D007[0], OpenApiExceptionCode.D007[1]);
                  }

                  int i = false;
                  DeviceDisplayConf resultEntity = null;

                  int i;
                  for(i = 0; i < reqTimeOut; ++i) {
                     try {
                        resultEntity = confMgr.getSettingResultByDisplay(info.getDevice_id(), this.getToken(), "SET_DEVICE_DISPLAY_CONF");
                     } catch (SQLException var11) {
                        logger.error(var11);
                        throw new OpenApiServiceException(OpenApiExceptionCode.D008[0], OpenApiExceptionCode.D008[1]);
                     }

                     if (resultEntity != null) {
                        break;
                     }

                     try {
                        Thread.sleep(1000L);
                     } catch (InterruptedException var10) {
                        logger.error(var10);
                        throw new OpenApiServiceException(OpenApiExceptionCode.D008[0], OpenApiExceptionCode.D008[1]);
                     }
                  }

                  if (resultEntity == null && i >= reqTimeOut) {
                     throw new OpenApiServiceException(OpenApiExceptionCode.D016[0], OpenApiExceptionCode.D016[1]);
                  } else {
                     return resultEntity;
                  }
               }
            }
         } else {
            throw new OpenApiServiceException(OpenApiExceptionCode.D005[0], OpenApiExceptionCode.D005[1]);
         }
      } else {
         throw new OpenApiServiceException(OpenApiExceptionCode.D005[0], OpenApiExceptionCode.D005[1]);
      }
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public boolean setDevicePowerOn(String deviceIds) throws OpenApiServiceException {
      String[] arrDeviceIds = null;
      if (deviceIds == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D005[0], OpenApiExceptionCode.D005[1]);
      } else {
         arrDeviceIds = deviceIds.split(";");
         if (arrDeviceIds.length <= 0) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D005[0], OpenApiExceptionCode.D005[1]);
         } else {
            int sendCount = 0;
            DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
            WakeOnLan wol = new WakeOnLan();

            for(int i = 0; i < arrDeviceIds.length; ++i) {
               if (!arrDeviceIds[i].equals("")) {
                  try {
                     Device device = deviceMgr.getDevice(arrDeviceIds[i]);
                     if (device != null) {
                        wol.wol(device.getIp_address(), device.getSubnet_mask(), device.getDevice_id());
                        ++sendCount;
                     }
                  } catch (SQLException var8) {
                     logger.error(var8);
                     throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
                  }
               }
            }

            return sendCount > 0;
         }
      }
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public boolean setDevicePowerOff(String deviceId) throws OpenApiServiceException {
      DeviceDisplayConf info = new DeviceDisplayConf();
      info.setDevice_id(deviceId);
      info.setBasic_power("0");
      DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();

      try {
         confManager.reqSetDisplayToDevice(info, "", "ALL_MDC");
         DeviceUtils.setDisconnected(deviceId);
         return true;
      } catch (Exception var5) {
         return false;
      }
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public DeviceSystemSetupConf modifyDeviceSystemSetupInfo(DeviceSystemSetupConf info, int reqTimeOut) throws OpenApiServiceException, ConfigException {
      String[] rtnCode = null;
      if (CommonConfig.get("saas.eu.enable") != null && CommonConfig.get("saas.eu.enable").equalsIgnoreCase("TRUE")) {
         rtnCode = null;
      } else {
         rtnCode = DeviceUtil.checkDeviceSystemSetupConfEntity(info, 1);
      }

      if (rtnCode != null) {
         throw new OpenApiServiceException(rtnCode[0], rtnCode[1]);
      } else {
         DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
         if (reqTimeOut < 1) {
            reqTimeOut = 10;
         }

         DeviceInfo deviceDao = null;
         Device device = null;
         if (info.getDevice_id() != null && !info.getDevice_id().equals("")) {
            try {
               deviceDao = DeviceInfoImpl.getInstance();
               device = deviceDao.getDeviceMinInfo(info.getDevice_id());
            } catch (Exception var15) {
               throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
            }

            if (device == null) {
               throw new OpenApiServiceException(OpenApiExceptionCode.D007[0], OpenApiExceptionCode.D007[1] + "(Check Device)");
            } else {
               boolean isConn = true;

               try {
                  isConn = this.getDeviceConnection(info.getDevice_id());
               } catch (Exception var14) {
                  throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
               }

               if (!isConn) {
                  throw new OpenApiServiceException(OpenApiExceptionCode.D015[0], OpenApiExceptionCode.D015[1]);
               } else {
                  try {
                     confMgr.reqSetSystemSetupToDevice(info, this.getToken());
                  } catch (Exception var13) {
                     logger.error(var13);
                     throw new OpenApiServiceException(OpenApiExceptionCode.D007[0], OpenApiExceptionCode.D007[1]);
                  }

                  int i = false;
                  DeviceSystemSetupConf resultEntity = null;

                  int i;
                  for(i = 0; i < reqTimeOut; ++i) {
                     try {
                        resultEntity = confMgr.getSettingResultBySystemSetup(info.getDevice_id(), this.getToken(), "SET_DEVICE_SYSTEM_SETUP_CONF");
                     } catch (Exception var12) {
                        logger.error(var12);
                        throw new OpenApiServiceException(OpenApiExceptionCode.D008[0], OpenApiExceptionCode.D008[1]);
                     }

                     if (resultEntity != null) {
                        break;
                     }

                     try {
                        Thread.sleep(1000L);
                     } catch (InterruptedException var11) {
                        logger.error(var11);
                        throw new OpenApiServiceException(OpenApiExceptionCode.D008[0], OpenApiExceptionCode.D008[1]);
                     }
                  }

                  if (resultEntity == null && i >= reqTimeOut) {
                     throw new OpenApiServiceException(OpenApiExceptionCode.D016[0], OpenApiExceptionCode.D016[1]);
                  } else {
                     return resultEntity;
                  }
               }
            }
         } else {
            throw new OpenApiServiceException(OpenApiExceptionCode.D005[0], OpenApiExceptionCode.D005[1]);
         }
      }
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public DeviceTimeConf modifyDeviceTimeInfo(DeviceTimeConf info, int reqTimeOut) throws OpenApiServiceException {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = null;
      String deviceId = info.getDevice_id();
      if (info != null && deviceId != null && !deviceId.equals("")) {
         try {
            device = deviceDao.getDeviceMinInfo(deviceId);
         } catch (Exception var20) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
         }

         if (device == null) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D007[0], OpenApiExceptionCode.D007[1] + "(Check Device)");
         } else {
            boolean isConn = true;

            try {
               isConn = this.getDeviceConnection(deviceId);
            } catch (Exception var19) {
               throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
            }

            if (!isConn) {
               throw new OpenApiServiceException(OpenApiExceptionCode.D015[0], OpenApiExceptionCode.D015[1]);
            } else {
               DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
               DeviceTimeConf conf = new DeviceTimeConf();
               conf.setDevice_id(deviceId);
               String deviceModelCode = "";

               try {
                  deviceModelCode = deviceDao.getDeviceModelCodeByDeviceId(deviceId);
               } catch (Exception var18) {
                  logger.error(var18);
                  throw new OpenApiServiceException(OpenApiExceptionCode.D013[0], OpenApiExceptionCode.D013[1]);
               }

               String modelKind = MDCTimeStrUtils.getModelKind(deviceModelCode);
               int timerCnt = MDCTimeStrUtils.getTimerCnt(deviceModelCode);
               if (modelKind.equalsIgnoreCase("NEW")) {
                  if (timerCnt == 3) {
                     if ((info.getTimer_clock() == null || info.getTimer_clock().equals("")) && (info.getTimer_timer1() == null || info.getTimer_timer1().equals("")) && (info.getTimer_timer2() == null || info.getTimer_timer2().equals("")) && (info.getTimer_timer3() == null || info.getTimer_timer3().equals("")) && (info.getTimer_holiday() == null || info.getTimer_holiday().equals(""))) {
                        throw new OpenApiServiceException(OpenApiExceptionCode.D009[0], OpenApiExceptionCode.D009[1] + "(New Timer Model)");
                     }

                     if (info.getTimer_clock() != null) {
                        conf.setTimer_clock(info.getTimer_clock());
                     }

                     if (info.getTimer_timer1() != null) {
                        conf.setTimer_timer1(info.getTimer_timer1());
                     }

                     if (info.getTimer_timer2() != null) {
                        conf.setTimer_timer2(info.getTimer_timer2());
                     }

                     if (info.getTimer_timer3() != null) {
                        conf.setTimer_timer3(info.getTimer_timer3());
                     }
                  } else if (timerCnt == 7) {
                     if ((info.getTimer_clock() == null || info.getTimer_clock().equals("")) && (info.getTimer_timer1() == null || info.getTimer_timer1().equals("")) && (info.getTimer_timer2() == null || info.getTimer_timer2().equals("")) && (info.getTimer_timer3() == null || info.getTimer_timer3().equals("")) && (info.getTimer_timer4() == null || info.getTimer_timer4().equals("")) && (info.getTimer_timer5() == null || info.getTimer_timer5().equals("")) && (info.getTimer_timer6() == null || info.getTimer_timer6().equals("")) && (info.getTimer_timer7() == null || info.getTimer_timer7().equals("")) && (info.getTimer_holiday() == null || info.getTimer_holiday().equals(""))) {
                        throw new OpenApiServiceException(OpenApiExceptionCode.D009[0], OpenApiExceptionCode.D009[1] + "(New Timer Model)");
                     }

                     if (info.getTimer_clock() != null) {
                        conf.setTimer_clock(info.getTimer_clock());
                     }

                     if (info.getTimer_timer1() != null) {
                        conf.setTimer_timer1(info.getTimer_timer1());
                     }

                     if (info.getTimer_timer2() != null) {
                        conf.setTimer_timer2(info.getTimer_timer2());
                     }

                     if (info.getTimer_timer3() != null) {
                        conf.setTimer_timer3(info.getTimer_timer3());
                     }

                     if (info.getTimer_timer4() != null) {
                        conf.setTimer_timer4(info.getTimer_timer4());
                     }

                     if (info.getTimer_timer5() != null) {
                        conf.setTimer_timer5(info.getTimer_timer5());
                     }

                     if (info.getTimer_timer6() != null) {
                        conf.setTimer_timer6(info.getTimer_timer6());
                     }

                     if (info.getTimer_timer7() != null) {
                        conf.setTimer_timer7(info.getTimer_timer7());
                     }
                  }

                  if (info.getTimer_holiday() != null) {
                     conf.setTimer_holiday(info.getTimer_holiday());
                  }
               } else {
                  if ((info.getTime_current_time() == null || info.getTime_current_time().equals("")) && (info.getTime_on_time() == null || info.getTime_on_time().equals("")) && (info.getTime_off_time() == null || info.getTime_off_time().equals(""))) {
                     throw new OpenApiServiceException(OpenApiExceptionCode.D009[0], OpenApiExceptionCode.D009[1] + "(Old Timer Model)");
                  }

                  if (info.getTime_current_time() != null) {
                     conf.setTime_current_time(info.getTime_current_time());
                  }

                  if (info.getTime_on_time() != null) {
                     conf.setTime_on_time(info.getTime_on_time());
                  }

                  if (info.getTime_off_time() != null) {
                     conf.setTime_off_time(info.getTime_off_time());
                  }
               }

               try {
                  conf.setDevice_model_code(deviceModelCode);
                  confMgr.reqSetTimeToDevice(conf, this.getToken());
               } catch (Exception var17) {
                  logger.error(var17);
                  throw new OpenApiServiceException(OpenApiExceptionCode.D007[0], OpenApiExceptionCode.D007[1]);
               }

               DeviceTimeConf resultEntity = null;

               for(int i = 0; i < reqTimeOut; ++i) {
                  try {
                     resultEntity = confMgr.getTimeResultSet(deviceId, this.getToken(), "SET_DEVICE_TIME_CONF");
                  } catch (Exception var16) {
                     logger.error(var16);
                     throw new OpenApiServiceException(OpenApiExceptionCode.D008[0], OpenApiExceptionCode.D008[1]);
                  }

                  if (resultEntity != null) {
                     break;
                  }

                  try {
                     Thread.sleep(1000L);
                  } catch (InterruptedException var15) {
                     logger.error(var15);
                     throw new OpenApiServiceException(OpenApiExceptionCode.D008[0], OpenApiExceptionCode.D008[1]);
                  }
               }

               return this.getDeviceTimeInfoByRealTime(deviceId, reqTimeOut);
            }
         }
      } else {
         throw new OpenApiServiceException(OpenApiExceptionCode.D005[0], OpenApiExceptionCode.D005[1]);
      }
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public Boolean modifyMagicInfoServer(String deviceId, String serverUrl) throws OpenApiServiceException {
      DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
      DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
      DeviceSystemSetupConf info = new DeviceSystemSetupConf();
      info.setDevice_id(deviceId);
      info.setMagicinfo_server_url(serverUrl);
      boolean result = false;
      Device device = null;

      try {
         device = deviceMgr.getDeviceMinInfo(deviceId);
      } catch (Exception var13) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      }

      if (device == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D007[0], OpenApiExceptionCode.D007[1] + "(Check Device)");
      } else {
         boolean isConn = true;

         try {
            isConn = this.getDeviceConnection(deviceId);
         } catch (Exception var12) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
         }

         if (!isConn) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D015[0], OpenApiExceptionCode.D015[1]);
         } else {
            try {
               confManager.reqSetSystemSetupToDevice(info, this.getToken());
               result = deviceMgr.deleteDevice(deviceId);
            } catch (Exception var11) {
               logger.error(var11);
               throw new OpenApiServiceException(OpenApiExceptionCode.D007[0], OpenApiExceptionCode.D007[1]);
            }

            if (result) {
               try {
                  WSCall.setPlayerRequest(deviceId, "agent restart");
               } catch (Exception var10) {
                  logger.error(var10);
                  throw new OpenApiServiceException(OpenApiExceptionCode.D012[0], OpenApiExceptionCode.D012[1]);
               }
            }

            return result;
         }
      }
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public boolean modifyMultiDeviceDisplayInfo(DeviceDisplayConf info, String deviceIds, String groupId) throws OpenApiServiceException {
      String[] arrDeviceIds = null;
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      boolean result = true;
      if (info != null) {
         if (groupId != null && !groupId.equals("") && (deviceIds == null || deviceIds.equals(""))) {
            DeviceGroupInfo groupMgr = DeviceGroupInfoImpl.getInstance();

            try {
               List devIds = groupMgr.getChildDeviceIdList(Integer.parseInt(groupId));
               String[] arrTemp = null;
               if (devIds != null && devIds.size() > 0) {
                  arrTemp = new String[devIds.size()];

                  for(int i = 0; i < devIds.size(); ++i) {
                     arrTemp[i] = (String)devIds.get(i);
                  }

                  arrDeviceIds = arrTemp;
               }
            } catch (SQLException var12) {
               result = false;
               logger.error(var12);
               throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
            }
         } else if (deviceIds != null && !deviceIds.equals("")) {
            arrDeviceIds = deviceIds.split(";");
         }

         if (arrDeviceIds == null) {
            result = false;
            throw new OpenApiServiceException(OpenApiExceptionCode.D005[0], OpenApiExceptionCode.D005[1]);
         }

         for(int j = 0; j < arrDeviceIds.length; ++j) {
            try {
               new DeviceDisplayConf();
               info.setDevice_id(arrDeviceIds[j]);
               confMgr.reqSetDisplayToDevice(info, this.getToken(), "ALL_MDC");
            } catch (Exception var11) {
               result = false;
               logger.error(var11);
               throw new OpenApiServiceException(OpenApiExceptionCode.D007[0], OpenApiExceptionCode.D007[1]);
            }
         }
      }

      return result;
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public boolean modifyMultiDeviceTimeInfo(DeviceTimeConf info, String deviceIds, String groupId) throws OpenApiServiceException {
      String[] rtnCode = DeviceUtil.checkDeviceTimeConfEntity(info, 1);
      if (rtnCode != null) {
         throw new OpenApiServiceException(rtnCode[0], rtnCode[1]);
      } else {
         String[] arrDeviceIds = null;
         DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
         boolean result = true;
         if (groupId != null && !groupId.equals("") && (deviceIds == null || deviceIds.equals(""))) {
            DeviceGroupInfo groupMgr = DeviceGroupInfoImpl.getInstance();

            try {
               List devIds = groupMgr.getChildDeviceIdList(Integer.parseInt(groupId));
               String[] arrTemp = null;
               if (devIds != null && devIds.size() > 0) {
                  arrTemp = new String[devIds.size()];

                  for(int i = 0; i < devIds.size(); ++i) {
                     arrTemp[i] = (String)devIds.get(i);
                  }

                  arrDeviceIds = arrTemp;
               }
            } catch (SQLException var13) {
               result = false;
               logger.error(var13);
               throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
            }
         } else if (deviceIds != null && !deviceIds.equals("")) {
            arrDeviceIds = deviceIds.split(";");
         }

         if (arrDeviceIds == null) {
            result = false;
            throw new OpenApiServiceException(OpenApiExceptionCode.D005[0], OpenApiExceptionCode.D005[1]);
         } else {
            for(int j = 0; j < arrDeviceIds.length; ++j) {
               new DeviceTimeConf();
               DeviceTimeConf conf = info;
               info.setDevice_id(arrDeviceIds[j]);

               try {
                  confMgr.reqSetTimeToDevice(conf, this.getToken());
               } catch (Exception var12) {
                  result = false;
                  logger.error(var12);
                  throw new OpenApiServiceException(OpenApiExceptionCode.D007[0], OpenApiExceptionCode.D007[1]);
               }
            }

            return result;
         }
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public boolean modifyMultiDeviceSystemSetupInfo(DeviceSystemSetupConf info, String deviceIds, String groupId) throws OpenApiServiceException {
      String[] rtnCode = DeviceUtil.checkDeviceSystemSetupConfEntity(info, 1);
      if (rtnCode != null) {
         throw new OpenApiServiceException(rtnCode[0], rtnCode[1]);
      } else {
         String[] arrDeviceIds = null;
         DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
         boolean result = true;
         if (groupId != null && !groupId.equals("") && (deviceIds == null || deviceIds.equals(""))) {
            DeviceGroupInfo groupMgr = DeviceGroupInfoImpl.getInstance();

            try {
               List devIds = groupMgr.getChildDeviceIdList(Integer.parseInt(groupId));
               String[] arrTemp = null;
               if (devIds != null && devIds.size() > 0) {
                  arrTemp = new String[devIds.size()];

                  for(int i = 0; i < devIds.size(); ++i) {
                     arrTemp[i] = (String)devIds.get(i);
                  }

                  arrDeviceIds = arrTemp;
               }
            } catch (SQLException var13) {
               result = false;
               logger.error(var13);
               throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
            }
         } else if (deviceIds != null && !deviceIds.equals("")) {
            arrDeviceIds = deviceIds.split(";");
         }

         if (arrDeviceIds == null) {
            result = false;
            throw new OpenApiServiceException(OpenApiExceptionCode.D013[0], OpenApiExceptionCode.D013[1]);
         } else {
            for(int j = 0; j < arrDeviceIds.length; ++j) {
               try {
                  new DeviceSystemSetupConf();
                  info.setDevice_id(arrDeviceIds[j]);
                  confMgr.reqSetSystemSetupToDevice(info, this.getToken());
               } catch (Exception var12) {
                  result = false;
                  logger.error(var12);
               }
            }

            return result;
         }
      }
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public ResultList getDeviceTimeHoliday(String deviceId) throws OpenApiServiceException, SQLException, ConfigException {
      DeviceTimeConfManager deviceConf = DeviceTimeConfManagerImpl.getInstance();
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      String device_model_code = device.getDevice_model_code();
      int timerCnt = MDCTimeStrUtils.getTimerCnt(device_model_code);
      List holidayList = null;
      ResultList resultList = new ResultList();
      if (!device_model_code.equalsIgnoreCase("DEFAULT") && Integer.parseInt(device_model_code, 10) > 55 && Integer.parseInt(device_model_code, 10) < 7000 && !device_model_code.equalsIgnoreCase("DEFAULT")) {
         deviceConf.getDeviceNewTimeConf(deviceId, timerCnt);
         holidayList = deviceConf.getDeviceTimeHolidayConf(deviceId);
         resultList.setResultList(holidayList);
      } else {
         deviceConf.getDeviceOldTimeConf(deviceId);
      }

      return resultList;
   }

   @PreAuthorize("hasRole('Remote Job Read Authority')")
   public ResultList getRemoteJobList(String deviceId) throws SQLException {
      ResultList resultList = new ResultList();
      JobDAO dao = new JobDAO();
      List jEntityList = dao.getJobListByDeviceId("", deviceId, true);
      resultList.setResultList(jEntityList);
      return resultList;
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public String getDeviceModelKind(String deviceId) throws OpenApiServiceException {
      DeviceInfo mgr = DeviceInfoImpl.getInstance();
      String deviceModelCode = "";

      try {
         deviceModelCode = mgr.getDeviceModelCodeByDeviceId(deviceId);
      } catch (Exception var5) {
         logger.error(var5);
         throw new OpenApiServiceException(OpenApiExceptionCode.D013[0], OpenApiExceptionCode.D013[1]);
      }

      String modelKind = MDCTimeStrUtils.getModelKind(deviceModelCode);
      MDCTimeStrUtils.getTimerCnt(deviceModelCode);
      return modelKind;
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public int getDeviceTimerCnt(String deviceId) throws OpenApiServiceException {
      DeviceInfo mgr = DeviceInfoImpl.getInstance();
      String deviceModelCode = "";

      try {
         deviceModelCode = mgr.getDeviceModelCodeByDeviceId(deviceId);
      } catch (Exception var5) {
         logger.error(var5);
         throw new OpenApiServiceException(OpenApiExceptionCode.D013[0], OpenApiExceptionCode.D013[1]);
      }

      int timerCnt = MDCTimeStrUtils.getTimerCnt(deviceModelCode);
      return timerCnt;
   }

   public boolean isSeparatedTimer(String deviceId) throws OpenApiServiceException, SQLException {
      DeviceTimeConfManager deviceConf = DeviceTimeConfManagerImpl.getInstance();
      DeviceTimeTimerConf timer1 = deviceConf.getDeviceTimeTimerConf(deviceId, 1);
      String[] arrTimer1 = MDCTimeStrUtils.timerObjToStr(timer1).split(";");
      return arrTimer1.length == 15;
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public int addGroup(DeviceGroup deviceGroup) throws Exception {
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      Long p_group_id = deviceGroup.getP_group_id();
      DeviceGroup pGroup = deviceGroupInfo.getGroup(p_group_id.intValue());
      if (p_group_id == 0L) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D602[0], OpenApiExceptionCode.D602[1]);
      } else if (pGroup == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D600[0], OpenApiExceptionCode.D600[1]);
      } else {
         Long p_group_depth = pGroup.getGroup_depth();
         if (p_group_depth == null) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D601[0], OpenApiExceptionCode.D601[1]);
         } else {
            deviceGroup.setGroup_depth(p_group_depth + 1L);
            deviceGroup.setIs_root(false);
            deviceGroup.setCreator_id(this.user.getUser().getUser_id());
            int rtnGroupId = deviceGroupInfo.addGroup(deviceGroup);
            ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
            String programId = schInfo.createDefaultProgram(deviceGroup.getGroup_name(), Integer.toString(rtnGroupId), deviceGroup.getCreator_id());
            if (programId != null) {
               deviceGroupInfo.setDefaultProgramId((long)rtnGroupId, programId);
            }

            DeviceGroup tmpGroup = deviceGroupInfo.getGroup(rtnGroupId);
            if (tmpGroup.getGroup_type() != null && !tmpGroup.getGroup_type().equals("")) {
               schInfo.updateDefaultProgramDeviceType((long)rtnGroupId, tmpGroup.getGroup_type());
            }

            return rtnGroupId;
         }
      }
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public String deleteGroup(String groupId) throws OpenApiServiceException, SQLException, ConfigException {
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      int p_group_id = deviceGroupInfo.getParentGroupId(Integer.parseInt(groupId));
      if (p_group_id == 0) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D603[0], OpenApiExceptionCode.D603[1]);
      } else {
         boolean result = deviceGroupInfo.deleteChildGroupAndDevice(Long.parseLong(groupId), this.user.getUser().getUser_id(), (HttpServletRequest)null);
         return result ? "Operation Success" : "Operation Fail";
      }
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public String reNameGroup(String groupId, String newGroupName) throws NumberFormatException, SQLException, OpenApiServiceException {
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      int p_group_id = deviceGroupInfo.getParentGroupId(Integer.parseInt(groupId));
      if (p_group_id == 0) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D604[0], OpenApiExceptionCode.D604[1]);
      } else {
         LeftMenuGroupTreeDao tree_dao = new LeftMenuGroupTreeDao();

         try {
            tree_dao.setGroupTreeRename("PremiumDevice", "MI_DMS_INFO_GROUP", groupId, newGroupName, this.user.getUser().getUser_id());
            return "Operation Success";
         } catch (Exception var7) {
            return "Operation Fail";
         }
      }
   }

   @PreAuthorize("hasRole('Device Move Authority')")
   public String moveDevice(String deviceId, String targetGroupId) throws NumberFormatException, SQLException, OpenApiServiceException {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      DeviceLogManager logMgr = DeviceLogManagerImpl.getInstance();
      MonitoringManager monMgr = MonitoringManagerImpl.getInstance();
      String fromGroupId = deviceDao.getDeviceGroupIdByDeviceId(deviceId);
      boolean isVWLGroup = false;
      boolean isBackupPlayGroup = false;
      isVWLGroup = deviceGroupInfo.isVwlGroup(fromGroupId);
      if (isVWLGroup) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D605[0], OpenApiExceptionCode.D605[1]);
      } else {
         isVWLGroup = deviceGroupInfo.isVwlGroup(targetGroupId);
         if (isVWLGroup) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D606[0], OpenApiExceptionCode.D606[1]);
         } else {
            isBackupPlayGroup = deviceGroupInfo.isRedundancyGroup(Integer.parseInt(fromGroupId));
            if (isBackupPlayGroup) {
               throw new OpenApiServiceException(OpenApiExceptionCode.D607[0], OpenApiExceptionCode.D607[1]);
            } else {
               isBackupPlayGroup = deviceGroupInfo.isRedundancyGroup(Integer.parseInt(targetGroupId));
               if (isBackupPlayGroup) {
                  throw new OpenApiServiceException(OpenApiExceptionCode.D608[0], OpenApiExceptionCode.D608[1]);
               } else {
                  int p_group_id = deviceGroupInfo.getParentGroupId(Integer.parseInt(targetGroupId));
                  if (p_group_id == 0) {
                     throw new OpenApiServiceException(OpenApiExceptionCode.D609[0], OpenApiExceptionCode.D609[1]);
                  } else {
                     ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
                     rms.setBasename("resource/messages");
                     String eventType = "TEXT_TITLE_MOVE_DEVICE_GROUP_P";
                     String strMenu = rms.getMessage("COM_SID_MENU", (Object[])null, Locale.ENGLISH);
                     String strMenuName = rms.getMessage("COM_TEXT_GENERAL_P", (Object[])null, Locale.ENGLISH);
                     String strCommand = rms.getMessage("TEXT_MOVE_GROUP_P", (Object[])null, Locale.ENGLISH);
                     String moveGroupName = deviceGroupInfo.getGroup(Integer.parseInt(targetGroupId)).getGroup_name();
                     Map map = new HashMap();
                     map.put("device_id", deviceId);
                     map.put("group_id", Long.parseLong(targetGroupId));
                     String prvGroupName = (String)deviceGroupInfo.getGroupNameByDeviceId(deviceId).get("group_name");
                     Device device = deviceDao.getDevice(deviceId);
                     map.put("device_type", device.getDevice_type());
                     int cnt = deviceDao.setDeviceGroupId(map);
                     if (cnt <= 0) {
                        throw new OpenApiServiceException(OpenApiExceptionCode.D610[0], OpenApiExceptionCode.D610[1]);
                     } else {
                        monMgr.scheduleReload(deviceId, 1);
                        ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
                        ScheduleInfoEntity schEntity = monMgr.getScheduleStatus(deviceId);
                        if (schEntity != null) {
                           try {
                              schInfo.deploySchedule(deviceId, schEntity.getScheduleId(), schEntity.getScheduleVersion());
                              MessageInfo msgInfo = MessageInfoImpl.getInstance();
                              msgInfo.deployMessage(deviceId, schEntity.getMessageId(), schEntity.getMessageVersion());
                              JobManager jobMgr = JobManagerImpl.getInstance();
                              Device tmpDev = deviceDao.getDevice(deviceId);
                              jobMgr.deployJobSchedule("", tmpDev);
                           } catch (Exception var26) {
                              throw new OpenApiServiceException(OpenApiExceptionCode.D611[0], OpenApiExceptionCode.D611[1]);
                           }
                        }

                        return "Operation Success";
                     }
                  }
               }
            }
         }
      }
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public String moveGroup(String groupId, String targetParentGroupId) throws NumberFormatException, SQLException, OpenApiServiceException {
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      DeviceGroup pGroup = deviceGroupInfo.getGroup(Integer.parseInt(targetParentGroupId));
      Long p_group_depth = pGroup.getGroup_depth();
      Long curOrgId = deviceGroupInfo.getOrgIdByGroupId(Long.parseLong(groupId));
      Long targetOrgId = deviceGroupInfo.getOrgIdByGroupId(Long.parseLong(targetParentGroupId));
      if (curOrgId != null && !curOrgId.equals(targetOrgId)) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D605[0], OpenApiExceptionCode.D605[1]);
      } else {
         LeftMenuGroupTreeDao tree_dao = new LeftMenuGroupTreeDao();

         try {
            tree_dao.setGroupTreeMove("PremiumDevice", "MI_DMS_INFO_GROUP", groupId, p_group_depth.toString(), targetParentGroupId);
            return "Operation Success";
         } catch (Exception var10) {
            return "Operation Fail";
         }
      }
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public String restartDevice(String deviceId) throws Exception {
      JobManager jobMgr = JobManagerImpl.getInstance();
      int jobId = false;
      int jobId = SequenceDB.getNextValue("MI_DMS_INFO_JOB");
      JobEntity job = new JobEntity();
      job.setJob_id((long)jobId);
      job.setJob_name("restart_" + jobId);
      job.setRepeat_type("immediately");
      job.setUser_id(this.user.getUser().getUser_id());
      job.setJob_type("reboot");
      job.setTarget("System");
      job.setIs_canceled(false);
      String[] devicearr = new String[]{deviceId};
      boolean result = false;
      result = jobMgr.addPremiumJob(job, devicearr);
      if (result) {
         DeviceInfo devMgr = DeviceInfoImpl.getInstance();
         MonitoringManager MotMgr = MonitoringManagerImpl.getInstance();
         Device device = null;
         device = devMgr.getDevice(devicearr[0]);
         jobMgr.deployRestartJobSchedule(String.valueOf(jobId), device);
         MotMgr.setDisconnected(devicearr[0]);
      }

      return String.valueOf(result);
   }

   public boolean sendEventToDevice(String eventId, String eventValue, String deviceIds) {
      EventManager eManager = new EventManager();
      String[] arrDeviceId = deviceIds.split(",");
      boolean retVal = true;

      try {
         retVal = eManager.sendEventToDevice(eventId, eventValue, this.getToken(), arrDeviceId);
      } catch (ConfigException var8) {
         logger.error("", var8);
      } catch (SQLException var9) {
         logger.error("", var9);
      } catch (IOException var10) {
         logger.error("", var10);
      } catch (ParseException var11) {
         logger.error("", var11);
      }

      return retVal;
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public ResultList getNewAndModifiedDeviceList(String startDate, String endDate) throws SQLException {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      new ArrayList();
      List deviceList = deviceDao.getNewAndModifiedDeviceList(startDate, endDate);
      ResultList resultList = new ResultList();
      resultList.setResultList(deviceList);
      resultList.setTotalCount(deviceList.size());
      return resultList;
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public String getDeviceScreenshotURL(String device_id) throws SQLException, OpenApiServiceException {
      String image_url = "";
      new ScreenCaptureEntity();
      ScreenCaptureDAO scDao = new ScreenCaptureDAO();
      DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
      Device device = null;
      String magicInfoServerUrl = "";
      device = deviceMgr.getDevice(device_id);
      if (device != null) {
         magicInfoServerUrl = device.getMagicinfo_server_url();
         if (device.getDevice_type().equals("SPLAYER")) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D018[0], OpenApiExceptionCode.D018[1]);
         } else {
            ScreenCaptureEntity scInfo = scDao.selInfoById(device_id);
            if (scInfo != null) {
               image_url = magicInfoServerUrl + "/servlet/FileLoader?paramPathConfName=CAPTURE_DIR&filepath=";
               image_url = image_url + scInfo.getFile_name();
            }

            return image_url;
         }
      } else {
         throw new OpenApiServiceException(OpenApiExceptionCode.D013[0], OpenApiExceptionCode.D013[1]);
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public String getDeviceThumbnailURL(String device_id) throws SQLException, OpenApiServiceException {
      String image_url = "";
      String imageId = "";
      String imageFilename = "";
      DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
      Device device = null;
      String magicInfoServerUrl = "";
      boolean panelStatus = false;
      device = deviceMgr.getDevice(device_id);
      if (device == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D013[0], OpenApiExceptionCode.D013[1]);
      } else {
         magicInfoServerUrl = device.getMagicinfo_server_url();
         List var9 = deviceMgr.getMonitoringInfoListByDeviceId(device_id);
         DeviceMonitoring monitoring = (DeviceMonitoring)var9.get(0);
         int basicSource = 32;
         if (DeviceUtils.getPlayingContent(monitoring.getDevice_id()) != null) {
            CurrentPlayingEntity playingEntity = DeviceUtils.getPlayingContent(monitoring.getDevice_id());
            if (playingEntity.getPanelStatus() == 0L) {
               panelStatus = true;
            }

            List playingContentList = playingEntity.getContentLists();
            basicSource = playingEntity.getInputSource();

            for(int j = 0; playingContentList != null && j < playingContentList.size(); ++j) {
               ContentList playingContent = (ContentList)playingContentList.get(j);
               if (playingContent.getFrameIndex() != null && playingContent.getFrameIndex().equals("0") && playingContent.getContentName() != null && !playingContent.getContentName().equals("") || playingContent.getFrameIndex() != null && playingContent.isMainFrame()) {
                  monitoring.setThumb_file_id(StrUtils.nvl(playingContent.getThumbnailFileId()));
                  monitoring.setThumb_file_name(StrUtils.nvl(playingContent.getThumbnailFileName()));
                  break;
               }
            }
         }

         boolean monitoringStatus = DeviceUtils.isConnected(monitoring.getDevice_id());
         if (!monitoringStatus) {
            image_url = magicInfoServerUrl + "/images/device/power_off.png";
         } else if (image_url == "" && monitoringStatus && panelStatus) {
            image_url = magicInfoServerUrl + "/images/device/power_on.png";
         } else if (monitoringStatus && !panelStatus) {
            image_url = magicInfoServerUrl + "/images/device/panel_off.png";
         }

         switch(basicSource) {
         case 4:
            image_url = magicInfoServerUrl + "/images/device/device_s_video_on.png";
            break;
         case 8:
            image_url = magicInfoServerUrl + "/images/device/device_component_on.png";
            break;
         case 12:
            image_url = magicInfoServerUrl + "/images/device/device_av_on.png";
            break;
         case 20:
            image_url = magicInfoServerUrl + "/images/device/device_pc_on.png";
            break;
         case 24:
         case 31:
            image_url = magicInfoServerUrl + "/images/device/device_dvi_on.png";
            break;
         case 30:
            image_url = magicInfoServerUrl + "/images/device/device_bnc_on.png";
            break;
         case 33:
         case 34:
            image_url = magicInfoServerUrl + "/images/device/device_hdmi1_on.png";
            break;
         case 35:
         case 36:
            image_url = magicInfoServerUrl + "/images/device/device_hdmi2_on.png";
            break;
         case 37:
            image_url = magicInfoServerUrl + "/images/device/device_display_port_on.png";
            break;
         case 48:
         case 64:
            image_url = magicInfoServerUrl + "/images/device/device_tv_on.png";
            break;
         case 102:
            image_url = magicInfoServerUrl + "/images/device/device_samsungworkspace.png";
            break;
         default:
            if (!StrUtils.nvl(monitoring.getThumb_file_id()).equals("") && !StrUtils.nvl(monitoring.getThumb_file_name()).equals("")) {
               image_url = magicInfoServerUrl + "/servlet/ContentThumbnail?";
               imageId = "thumb_id=" + monitoring.getThumb_file_id();
               imageFilename = "&thumb_filename=" + monitoring.getThumb_file_name() + "_MEDIUM.PNG";
               image_url = image_url + imageId + imageFilename;
            }
         }

         return image_url;
      }
   }

   @PreAuthorize("hasRole('Device Log Authority')")
   public ResultList getDeviceLogList(LogCondition condition) throws Exception {
      if (condition.getToDate() == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.V105[0], OpenApiExceptionCode.V105[1]);
      } else if (condition.getFromDate() == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.V104[0], OpenApiExceptionCode.V104[1]);
      } else {
         if (condition.getOrder_dir() == null) {
            condition.setOrder_dir("asc");
         }

         OpenApiParameterValidator openApiParameterValidator = new OpenApiParameterValidator();
         if (!openApiParameterValidator.checkPeriod(condition.getFromDate(), condition.getToDate())) {
            return null;
         } else {
            LogIF mgr = LogImpl.getInstance();
            DeviceLogManagerImpl.getInstance();
            ResultList resultList = new ResultList();
            this.user.getUser().getOrganization();
            String userId = this.user.getUser().getUser_id();
            int startPos = condition.getStart_index();
            int pageSize = condition.getPage_size();
            String fromMinNsec = ":00:00";
            String toMinNsec = ":59:59";
            String strFromHour = !StrUtils.nvl(condition.getFromHour()).equals("") && !StrUtils.nvl(condition.getFromHour()).equals("-1") ? condition.getFromHour() : "0";
            String strToHour = !StrUtils.nvl(condition.getToHour()).equals("") && !StrUtils.nvl(condition.getToHour()).equals("-1") ? condition.getToHour() : "23";
            strFromHour = strFromHour + fromMinNsec;
            strToHour = strToHour + toMinNsec;
            condition.setFromHour(strFromHour);
            condition.setToHour(strToHour);
            condition.setUser_id(userId);
            Timestamp startTime = new Timestamp(DateUtils.string2Timestamp(condition.getFromDate() + " " + strFromHour, "yyyy-MM-dd HH:mm:ss").getTime());
            Timestamp finishTime = new Timestamp(DateUtils.string2Timestamp(condition.getToDate() + " " + strToHour, "yyyy-MM-dd HH:mm:ss").getTime());
            condition.setStart_time(startTime);
            condition.setFinish_time(finishTime);
            PagedListInfo pagedList;
            if (condition.getDevice_id() == null) {
               pagedList = mgr.getDeviceLogList(condition, startPos, pageSize, "getAllDeviceLogList");
            } else {
               pagedList = mgr.getDeviceLogList(condition, startPos, pageSize, "getServerLogList");
            }

            if (pagedList != null) {
               List tmpList = pagedList.getPagedResultList();
               new ArrayList();
               System.currentTimeMillis();
               resultList.setResultList(tmpList);
               resultList.setTotalCount(pagedList.getTotalRowCount());
            }

            return resultList;
         }
      }
   }

   @PreAuthorize("hasRole('Software Write Authority')")
   public String registerUpdateSoftware(String locale, String deviceType, String deviceModelName) throws OpenApiServiceException {
      OpenApiParameterValidator openApiParameterValidator = new OpenApiParameterValidator();
      if (!openApiParameterValidator.isLocale(locale)) {
         throw new OpenApiServiceException(OpenApiExceptionCode.C104[0], OpenApiExceptionCode.C104[1]);
      } else if (deviceType.equals("SPLAYER") && deviceModelName == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D020[0], OpenApiExceptionCode.D020[1]);
      } else {
         String ret = "";

         try {
            String user_id = this.user.getUser().getUser_id();
            String jnlpFileName = "swUpdateFileUploader_" + user_id + "_" + DateUtils.date2String(this.user.getUser().getCreate_date(), "yyyyMMddHHmmss") + ".jnlp";
            String sessionId = this.getToken();
            if (deviceType.equals("iPLAYER")) {
               deviceModelName = "iPLAYER";
            }

            URL urlValue = Thread.currentThread().getContextClassLoader().getResource("/");
            if (urlValue == null) {
               return null;
            }

            String topPath = urlValue.getPath();
            topPath = topPath.replaceAll("%20", " ");
            topPath = topPath + "../../uploader/jnlp/";
            logger.debug("addContent = " + topPath + jnlpFileName);
            SecurityUtils.getSafeFile(topPath).mkdirs();
            File jnlpFile = SecurityUtils.getSafeFile(topPath + jnlpFileName);
            jnlpFile.createNewFile();
            String ftpPort = CommonConfig.get("download.server.ftp.port");
            String url = CommonConfig.get("web_url");
            String newToken = this.issueToken();
            String newTokenEncode = Base64.encode(newToken.getBytes());
            SoftwareUpdateFileUploaderJnlpStringBuilder swUpdaterJnlpBuilder = (new SoftwareUpdateFileUploaderJnlpStringBuilder()).addServerUrl(url).addUrlToken(newTokenEncode).addJnlpFilename(jnlpFileName).addLocale(locale).addSessionId(sessionId).addUserId(user_id).addToken(newToken).addFtpPort(Integer.valueOf(ftpPort)).addDeviceType(deviceType).addDeviceModelName(deviceModelName);
            FileWriter writer = new FileWriter(jnlpFile);
            PrintWriter fout = new PrintWriter(writer);
            fout.write(swUpdaterJnlpBuilder.build());
            fout.close();
            writer.close();
            ret = url + "/uploader/" + newTokenEncode + "/jnlp/" + jnlpFileName;
         } catch (IOException var19) {
            logger.error(var19);
         } catch (ConfigException var20) {
            logger.error(var20);
         }

         return ret;
      }
   }

   @PreAuthorize("hasRole('Software Write Authority')")
   public String registerUpdateSoftwareForMega(Software software) throws OpenApiServiceException, SQLException {
      Long software_id = new Long((long)SequenceDB.getNextValue("MI_DMS_INFO_SOFTWARE"));
      SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
      software.setSoftware_id(software_id);
      software.setDevice_type("SPLAYER");
      software.setSoftware_type("02");
      software.setIs_auto_update(false);
      software.setUpgrade_version("single");
      software.setStatus("done");
      software.setCreator_id("admin");
      software.setSubscriber_id("admin");
      boolean result = softwareDao.addDeviceSoftware(software, "P");
      return result ? String.valueOf(software_id) : "Fail";
   }

   public ResultList getUpdateSoftwareList(int startPos, int pageSize, String sortName, String sortDir, String searchText) throws Exception {
      SelectCondition condition = new SelectCondition();
      condition.setSort_name(sortName);
      condition.setOrder_dir(sortDir);
      condition.setSrc_name(searchText);
      Hashtable searchParam = new Hashtable();
      searchParam.put("condition", condition);
      ResultList resultList = new ResultList();
      SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
      PagedListInfo pagedList = softwareDao.getPagedList(startPos, pageSize, searchParam, "getSoftwareFileList");
      if (pagedList != null) {
         List tmpList = pagedList.getPagedResultList();
         resultList.setResultList(tmpList);
         resultList.setTotalCount(pagedList.getTotalRowCount());
      }

      return resultList;
   }

   public String deployUpdateSoftware(String softwareId, String deployTime) throws Exception {
      String organization = this.user.getUser().getOrganization();
      int orgId = Integer.valueOf(String.valueOf(this.user.getUser().getGroup_id()));
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      List longGroupIdList = deviceGroupInfo.getChildGroupIdList(orgId, true);
      String groupIdStr = null;
      if (longGroupIdList != null && longGroupIdList.size() > 0) {
         for(int i = 0; i < longGroupIdList.size(); ++i) {
            groupIdStr = groupIdStr + String.valueOf(longGroupIdList.get(i));
            if (i > 0) {
               groupIdStr = groupIdStr + ",";
            }
         }

         SoftwareManager swMgr = SoftwareManagerImpl.getInstance();
         Software software = swMgr.getSoftware(Long.parseLong(softwareId));
         software.setSubscriber_id(this.user.getUser().getUser_id());
         if (deployTime.equalsIgnoreCase("NOW")) {
            software.setRsv_date(new Timestamp(System.currentTimeMillis() + 60000L));
         } else {
            software.setRsv_date(DateUtils.string2Timestamp(deployTime, "yyyy-MM-dd HH:mm:ss"));
         }

         software.setDeploy_applied_ver("VERSION_ALL");
         software.setApplied_type("GROUP");
         software.setDevice_group(groupIdStr);
         Long result = 0L;
         Object[] obj = swMgr.deploySoftwareToDevices(software, organization);
         if (obj != null && obj.length > 0) {
            result = (Long)obj[0];
         }

         boolean flag = result > 0L;
         if (flag) {
            return "software_file_deploy_success";
         } else {
            return "software_file_deploy_fail";
         }
      } else {
         return "software_file_deploy_fail";
      }
   }

   public ResultList getUpdateSoftwareStatusList(int startPos, int pageSize, String sortName, String sortDir, String searchText) throws Exception {
      SelectCondition condition = new SelectCondition();
      condition.setSort_name(sortName);
      condition.setOrder_dir(sortDir);
      condition.setSrc_name(searchText);
      Hashtable searchParam = new Hashtable();
      searchParam.put("condition", condition);
      ResultList resultList = new ResultList();
      SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
      PagedListInfo pagedList = softwareDao.getPagedList(startPos, pageSize, searchParam, "getSoftwareReservationList");
      if (pagedList != null) {
         List tmpList = pagedList.getPagedResultList();
         resultList.setResultList(tmpList);
         resultList.setTotalCount(pagedList.getTotalRowCount());
      }

      return resultList;
   }

   public ResultList getSoftwareDeployDetailStatusList(int startPos, int pageSize, String sortName, String sortDir, String swReservationId) throws Exception {
      SelectCondition condition = new SelectCondition();
      condition.setSort_name(sortName);
      condition.setOrder_dir(sortDir);
      Hashtable searchParam = new Hashtable();
      searchParam.put("condition", condition);
      searchParam.put("software_rsv_id", Long.valueOf(swReservationId));
      ResultList resultList = new ResultList();
      SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
      PagedListInfo pagedList = softwareDao.getPagedList(startPos, pageSize, searchParam, "getReservedDeviceList");
      if (pagedList != null) {
         List tmpList = pagedList.getPagedResultList();
         resultList.setResultList(tmpList);
         resultList.setTotalCount(pagedList.getTotalRowCount());
      }

      return resultList;
   }

   public Long getMinimumPriority(String deviceGroupIds) throws Exception {
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      return deviceGroupInfo.getMinimumPriority(deviceGroupIds);
   }

   public Long getPriority(String deviceType, Float deviceTypeVersion) throws Exception {
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      return deviceGroupInfo.getPriority(deviceType, deviceTypeVersion);
   }

   public String getDeviceTypeByMinimumPriority(Long minimumPriority) throws Exception {
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      return deviceGroupInfo.getDeviceTypeByMinimumPriority(minimumPriority);
   }

   public Float getDeviceTypeVersionByMinimumPriority(Long minimumPriority) throws Exception {
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      return deviceGroupInfo.getDeviceTypeVersionByMinimumPriority(minimumPriority);
   }

   public String getToken() {
      return this.token;
   }

   public void setToken(String token) {
      this.token = token;
      this.user = (UserContainer)this.tokenRegistry.getUserObject(token);
   }

   private String issueToken() throws ConfigException {
      return StrUtils.nvl(CommonConfig.get("saas.no_token.enable")).equalsIgnoreCase("TRUE") ? this.tokenRegistry.onloadUserContainer(this.user) : this.tokenRegistry.issueToken(this.user);
   }

   public String startRMServer(String deviceId) throws Exception {
      DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
      DocumentBuilder builder = factory.newDocumentBuilder();
      RmServerDao rmserverDao = new RmServerDao();
      List lists = rmserverDao.getRmserverInfoList();
      List rtn_lists = new ArrayList();
      String rmServer = null;
      String return_code = null;
      String rmServer_monitor_url = null;
      if (lists == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.RM001[0], OpenApiExceptionCode.RM001[1]);
      } else {
         BufferedReader rd;
         String authority;
         if (lists != null) {
            HttpClient httpclient = new DefaultHttpClient();

            BasicHttpParams httpParams;
            HttpResponse Rmserver_response;
            Document line;
            NodeList line;
            NodeList headNodeList;
            BufferedReader rd;
            for(int i = 0; i < lists.size(); ++i) {
               if (((RmServerEntity)lists.get(i)).getUse_ssl()) {
                  rmServer = "https://";
               } else {
                  rmServer = "http://";
               }

               rmServer = rmServer + ((RmServerEntity)lists.get(i)).getIp_address() + ":" + ((RmServerEntity)lists.get(i)).getPort() + "/RMServer/openapi/open?service=RMService.status&deviceId=" + deviceId;
               HttpEntity entity;
               String connection;
               if (((RmServerEntity)lists.get(i)).getUse_ssl()) {
                  URL url = new URL(rmServer);
                  HttpsURLConnection conn = (HttpsURLConnection)url.openConnection();
                  conn.setHostnameVerifier(new HostnameVerifier() {
                     public boolean verify(String hostname, SSLSession session) {
                        return true;
                     }
                  });
                  SSLContext context = SSLContext.getInstance("TLS");
                  context.init((KeyManager[])null, new TrustManager[]{new X509TrustManager() {
                     public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                     }

                     public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                        try {
                           KeyStore trustStore = KeyStore.getInstance("JKS");
                           FileInputStream fis = null;

                           try {
                              fis = new FileInputStream("MagicInfoIdentity.jks");
                              trustStore.load(fis, "changeit".toCharArray());
                           } finally {
                              if (fis != null) {
                                 try {
                                    fis.close();
                                 } catch (Exception var14) {
                                    PremiumDeviceService.logger.error(var14);
                                 }
                              }

                           }

                           TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
                           tmf.init(trustStore);
                           TrustManager[] tms = tmf.getTrustManagers();
                           ((X509TrustManager)tms[0]).checkServerTrusted(chain, authType);
                        } catch (KeyStoreException var16) {
                           PremiumDeviceService.logger.error("", var16);
                        } catch (NoSuchAlgorithmException var17) {
                           PremiumDeviceService.logger.error("", var17);
                        } catch (IOException var18) {
                        }

                     }

                     public X509Certificate[] getAcceptedIssuers() {
                        return null;
                     }
                  }}, (SecureRandom)null);
                  conn.setSSLSocketFactory(context.getSocketFactory());
                  conn.setConnectTimeout(10000);

                  try {
                     conn.connect();
                     conn.setInstanceFollowRedirects(true);
                     InputStream in = conn.getInputStream();
                     rd = new BufferedReader(new InputStreamReader(in));
                     entity = null;
                     StringBuffer resultXml = new StringBuffer();

                     String line;
                     while((line = rd.readLine()) != null) {
                        resultXml.append(line);
                     }

                     Document doc = builder.parse(new InputSource(new StringReader(resultXml.toString())));
                     doc.getDocumentElement().normalize();
                     NodeList headNodeList = doc.getElementsByTagName("response");
                     Element subItem = (Element)headNodeList.item(0);
                     return_code = subItem.getAttribute("code");
                     if (return_code.equals("0")) {
                        NodeList resutNode = doc.getElementsByTagName("resultValue");
                        String connection = resutNode.item(0).getFirstChild().getNodeValue();
                        connection = null;
                        String[] rmServerResult = connection.split("\\|", 2);
                        if (rmServerResult.length != 2) {
                           throw new OpenApiServiceException(OpenApiExceptionCode.RM005[0], OpenApiExceptionCode.RM005[1]);
                        }

                        int index = rmServerResult[0].indexOf(":");
                        if (index == -1) {
                           return null;
                        }

                        if (index + 1 >= rmServerResult[0].length()) {
                           throw new OpenApiServiceException(OpenApiExceptionCode.RM005[0], OpenApiExceptionCode.RM005[1]);
                        }

                        connection = (String)rmServerResult[0].subSequence(index + 1, rmServerResult[0].length());
                        index = rmServerResult[1].indexOf(":");
                        if (index == -1) {
                           throw new OpenApiServiceException(OpenApiExceptionCode.RM005[0], OpenApiExceptionCode.RM005[1]);
                        }

                        if (index + 1 >= rmServerResult[1].length()) {
                           throw new OpenApiServiceException(OpenApiExceptionCode.RM005[0], OpenApiExceptionCode.RM005[1]);
                        }

                        connection = (String)rmServerResult[1].subSequence(index + 1, rmServerResult[1].length());
                        new RmServerEntity();
                        RmServerEntity serverClient = (RmServerEntity)lists.get(i);
                        serverClient.setConnection(Long.valueOf(connection));
                        serverClient.setServerDeviceChk(Long.valueOf(connection));
                        rtn_lists.add(serverClient);
                     }
                  } catch (Exception var90) {
                     logger.info("Openapi SSL time out!");
                     throw new OpenApiServiceException(OpenApiExceptionCode.RM002[0], OpenApiExceptionCode.RM002[1]);
                  }
               } else {
                  rd = null;
                  InputStreamReader isr = null;

                  try {
                     HttpGet httpget = new HttpGet(rmServer);
                     httpParams = new BasicHttpParams();
                     HttpConnectionParams.setConnectionTimeout(httpParams, 3000);
                     httpclient = new DefaultHttpClient(httpParams);
                     Rmserver_response = httpclient.execute(httpget);
                     entity = Rmserver_response.getEntity();
                     if (entity != null) {
                        isr = new InputStreamReader(Rmserver_response.getEntity().getContent());
                        rd = new BufferedReader(isr);
                        String line = null;
                        StringBuffer resultXml = new StringBuffer();

                        while((line = rd.readLine()) != null) {
                           resultXml.append(line);
                        }

                        line = builder.parse(new InputSource(new StringReader(resultXml.toString())));
                        line.getDocumentElement().normalize();
                        line = line.getElementsByTagName("response");
                        Element subItem = (Element)line.item(0);
                        return_code = subItem.getAttribute("code");
                        if (return_code != null && return_code.equals("0")) {
                           headNodeList = line.getElementsByTagName("resultValue");
                           connection = headNodeList.item(0).getFirstChild().getNodeValue();
                           String deviceChk = null;
                           String[] rmServerResult = connection.split("\\|", 2);
                           if (rmServerResult.length != 2) {
                              throw new OpenApiServiceException(OpenApiExceptionCode.RM005[0], OpenApiExceptionCode.RM005[1]);
                           }

                           int index = rmServerResult[0].indexOf(":");
                           if (index == -1) {
                              throw new OpenApiServiceException(OpenApiExceptionCode.RM005[0], OpenApiExceptionCode.RM005[1]);
                           }

                           if (index + 1 >= rmServerResult[0].length()) {
                              throw new OpenApiServiceException(OpenApiExceptionCode.RM005[0], OpenApiExceptionCode.RM005[1]);
                           }

                           connection = (String)rmServerResult[0].subSequence(index + 1, rmServerResult[0].length());
                           index = rmServerResult[1].indexOf(":");
                           if (index == -1) {
                              throw new OpenApiServiceException(OpenApiExceptionCode.RM005[0], OpenApiExceptionCode.RM005[1]);
                           }

                           if (index + 1 >= rmServerResult[1].length()) {
                              throw new OpenApiServiceException(OpenApiExceptionCode.RM005[0], OpenApiExceptionCode.RM005[1]);
                           }

                           deviceChk = (String)rmServerResult[1].subSequence(index + 1, rmServerResult[1].length());
                           new RmServerEntity();
                           RmServerEntity serverClient = (RmServerEntity)lists.get(i);
                           serverClient.setConnection(Long.valueOf(connection));
                           serverClient.setServerDeviceChk(Long.valueOf(deviceChk));
                           rtn_lists.add(serverClient);
                        }
                     }

                     httpget.abort();
                  } catch (ClientProtocolException var91) {
                     throw new OpenApiServiceException(OpenApiExceptionCode.RM002[0], OpenApiExceptionCode.RM002[1]);
                  } catch (IllegalStateException var92) {
                     throw new OpenApiServiceException(OpenApiExceptionCode.RM002[0], OpenApiExceptionCode.RM002[1]);
                  } catch (ConnectException var93) {
                     throw new OpenApiServiceException(OpenApiExceptionCode.RM002[0], OpenApiExceptionCode.RM002[1]);
                  } catch (ConnectTimeoutException var94) {
                     throw new OpenApiServiceException(OpenApiExceptionCode.RM002[0], OpenApiExceptionCode.RM002[1]);
                  } catch (SocketException var95) {
                     throw new OpenApiServiceException(OpenApiExceptionCode.RM002[0], OpenApiExceptionCode.RM002[1]);
                  } finally {
                     httpclient.getConnectionManager().shutdown();

                     try {
                        if (rd != null) {
                           rd.close();
                        }
                     } catch (Exception var82) {
                        logger.error("", var82);
                     }

                     try {
                        if (isr != null) {
                           isr.close();
                        }
                     } catch (Exception var81) {
                        logger.error("", var81);
                     }

                  }
               }
            }

            if (rtn_lists.size() >= 1) {
               Collections.sort(rtn_lists, new Comparator() {
                  public int compare(RmServerEntity contact, RmServerEntity another) {
                     int result = contact.getConnection().compareTo(another.getConnection());
                     if (result == 0) {
                        result = contact.getServerDeviceChk().compareTo(another.getServerDeviceChk());
                     }

                     return result;
                  }
               });
               new RmServerEntity();
               rd = null;
               RmServerEntity rmServerClient = (RmServerEntity)rtn_lists.get(0);
               String use_ssl;
               if (rmServerClient.getUse_ssl()) {
                  use_ssl = "https://";
               } else {
                  use_ssl = "http://";
               }

               if (rmServerClient.getServerDeviceChk() > 0L) {
                  throw new OpenApiServiceException(OpenApiExceptionCode.RM002[0], OpenApiExceptionCode.RM002[1]);
               }

               return_code = null;
               UserContainer userContainer = null;

               try {
                  userContainer = SecurityUtils.getUserContainer();
               } catch (Exception var85) {
                  logger.error("[MagicInfo_deleteContentUtils_checkContentFromSchedule] fail get UserContainer. e : " + var85.getMessage());
               }

               authority = "";
               if (userContainer != null) {
                  if (userContainer.checkAuthority("Device Write")) {
                     authority = authority + "DeviceWrite";
                  }

                  if (userContainer.checkAuthority("Device Control")) {
                     authority = authority + "DeviceControl";
                  }

                  if (userContainer.checkAuthority("Device Read")) {
                     authority = authority + "DeviceRead";
                  }
               }

               if (rmServerClient.getPrivate_mode()) {
                  rmServer_monitor_url = use_ssl + rmServerClient.getIp_address() + ":" + rmServerClient.getPrivate_port() + "/RMServer/remocon.do";
                  rmServer = use_ssl + rmServerClient.getPrivate_ip_address() + ":" + rmServerClient.getPrivate_port() + "/RMServer/openapi/open?service=RMService.ready&deviceId=" + deviceId + "&token=" + this.token + "&authority=" + authority;
               } else {
                  rmServer_monitor_url = use_ssl + rmServerClient.getIp_address() + ":" + rmServerClient.getPort() + "/RMServer/remocon.do";
                  rmServer = use_ssl + rmServerClient.getIp_address() + ":" + rmServerClient.getPort() + "/RMServer/openapi/open?service=RMService.ready&deviceId=" + deviceId + "&token=" + this.token + "&authority=" + authority;
               }

               if (rmServerClient.getUse_ssl()) {
                  URL url = new URL(rmServer);
                  HttpsURLConnection conn = (HttpsURLConnection)url.openConnection();
                  conn.setHostnameVerifier(new HostnameVerifier() {
                     public boolean verify(String hostname, SSLSession session) {
                        return true;
                     }
                  });
                  SSLContext context = SSLContext.getInstance("TLS");
                  context.init((KeyManager[])null, new TrustManager[]{new X509TrustManager() {
                     public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                     }

                     public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                        FileInputStream fis = null;

                        try {
                           KeyStore trustStore = KeyStore.getInstance("JKS");
                           fis = new FileInputStream("MagicInfoIdentity.jks");
                           trustStore.load(fis, "changeit".toCharArray());
                           TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
                           tmf.init(trustStore);
                           TrustManager[] tms = tmf.getTrustManagers();
                           ((X509TrustManager)tms[0]).checkServerTrusted(chain, authType);
                        } catch (KeyStoreException var19) {
                           PremiumDeviceService.logger.error("", var19);
                        } catch (NoSuchAlgorithmException var20) {
                           PremiumDeviceService.logger.error("", var20);
                        } catch (IOException var21) {
                           PremiumDeviceService.logger.error("", var21);
                        } finally {
                           try {
                              fis.close();
                           } catch (Exception var18) {
                              PremiumDeviceService.logger.error("", var18);
                           }

                        }

                     }

                     public X509Certificate[] getAcceptedIssuers() {
                        return null;
                     }
                  }}, (SecureRandom)null);
                  conn.setSSLSocketFactory(context.getSocketFactory());
                  conn.setConnectTimeout(10000);

                  try {
                     conn.connect();
                     conn.setInstanceFollowRedirects(true);
                     InputStream in = conn.getInputStream();
                     BufferedReader reader = new BufferedReader(new InputStreamReader(in));
                     line = null;
                     StringBuffer resultXml = new StringBuffer();

                     String line;
                     while((line = reader.readLine()) != null) {
                        resultXml.append(line);
                     }

                     Document doc = builder.parse(new InputSource(new StringReader(resultXml.toString())));
                     doc.getDocumentElement().normalize();
                     headNodeList = doc.getElementsByTagName("response");
                     Element subItem = (Element)headNodeList.item(0);
                     return_code = subItem.getAttribute("code");
                     if (!return_code.equals("0")) {
                        throw new OpenApiServiceException(OpenApiExceptionCode.RM006[0], OpenApiExceptionCode.RM006[1] + " " + return_code);
                     }

                     StringBuilder ChangeInfoValue = new StringBuilder();
                     if (rmServerClient.getUse_ssl()) {
                        use_ssl = "true";
                     } else {
                        use_ssl = "false";
                     }

                     ChangeInfoValue = ChangeInfoValue.append(rmServerClient.getIp_address() + ":" + rmServerClient.getPort() + "@" + use_ssl + "@" + this.token);
                     DeviceConfManager DeviceConfManager = DeviceConfManagerImpl.getInstance();

                     try {
                        DeviceConfManager.reqSetDeviceConnStatus(deviceId, ".MO.DEVICE_CONF.RM.START", ChangeInfoValue.toString());
                     } catch (Exception var84) {
                     }
                  } catch (Exception var86) {
                     logger.info("SSL time out!");
                     throw new OpenApiServiceException(OpenApiExceptionCode.RM002[0], OpenApiExceptionCode.RM002[1]);
                  }
               } else {
                  InputStreamReader inputStreamReader = null;
                  rd = null;

                  try {
                     HttpClient rmHttpClient = new DefaultHttpClient();
                     HttpGet httpget = new HttpGet(rmServer);
                     HttpResponse Rmserver_response = rmHttpClient.execute(httpget);
                     HttpEntity entity = Rmserver_response.getEntity();
                     if (entity != null) {
                        inputStreamReader = new InputStreamReader(Rmserver_response.getEntity().getContent());
                        rd = new BufferedReader(inputStreamReader);
                        line = null;

                        String resultXml;
                        String line;
                        for(resultXml = new String(); (line = rd.readLine()) != null; resultXml = resultXml + line) {
                        }

                        Document doc = builder.parse(new InputSource(new StringReader(resultXml)));
                        doc.getDocumentElement().normalize();
                        NodeList headNodeList = doc.getElementsByTagName("response");
                        Element subItem = (Element)headNodeList.item(0);
                        return_code = subItem.getAttribute("code");
                        if (return_code != null) {
                           if (!return_code.equals("0")) {
                              throw new OpenApiServiceException(OpenApiExceptionCode.RM006[0], OpenApiExceptionCode.RM006[1] + " " + return_code);
                           }

                           StringBuilder ChangeInfoValue = new StringBuilder();
                           if (rmServerClient.getUse_ssl()) {
                              use_ssl = "true";
                           } else {
                              use_ssl = "false";
                           }

                           ChangeInfoValue = ChangeInfoValue.append(rmServerClient.getIp_address() + ":" + rmServerClient.getPort() + "@" + use_ssl + "@" + this.token);
                           DeviceConfManager DeviceConfManager = DeviceConfManagerImpl.getInstance();

                           try {
                              DeviceConfManager.reqSetDeviceConnStatus(deviceId, ".MO.DEVICE_CONF.RM.START", ChangeInfoValue.toString());
                           } catch (Exception var83) {
                           }
                        }
                     }

                     httpget.abort();
                  } catch (ClientProtocolException var87) {
                     logger.error("", var87);
                  } catch (IOException var88) {
                     logger.error("", var88);
                  } finally {
                     httpclient.getConnectionManager().shutdown();

                     try {
                        if (rd != null) {
                           rd.close();
                           Rmserver_response = null;
                        }
                     } catch (Exception var80) {
                        logger.error("", var80);
                     }

                     try {
                        if (inputStreamReader != null) {
                           inputStreamReader.close();
                           httpParams = null;
                        }
                     } catch (Exception var79) {
                        logger.error("", var79);
                     }

                  }
               }

               if (return_code != null && !return_code.equals("0")) {
                  throw new OpenApiServiceException(OpenApiExceptionCode.RM006[0], OpenApiExceptionCode.RM006[1] + " " + return_code);
               }
            }
         }

         DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
         MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
         rd = null;
         CurrentPlayingEntity playingEntity = motMgr.getPlayingContent(deviceId);
         Device device = deviceMgr.getDevice(deviceId);
         authority = "&information=" + device.getDevice_name() + "|";
         if (playingEntity != null) {
            if (playingEntity.getProgramName() == null) {
               authority = authority + "|";
            } else {
               authority = authority + playingEntity.getProgramName() + "|";
            }
         } else {
            authority = authority + "|";
         }

         authority = authority + device.getDevice_model_name() + "|" + device.getDevice_id() + "|" + device.getFirmware_version() + "|" + device.getIp_address() + "|" + device.getGateway() + "|" + device.getTrigger_interval();
         String rtn_url = rmServer_monitor_url + "?deviceId=" + deviceId + "&token=" + this.token + authority;
         return rtn_url;
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public ResultList getClientFaultStatus(String deviceId) throws OpenApiServiceException {
      ClientFaultEntity clientfault = null;
      ResultList resultList = new ResultList();
      List result = new ArrayList();
      MonitoringManager mgr = MonitoringManagerImpl.getInstance();

      try {
         clientfault = mgr.getClientFaultStatus(deviceId);
         if (clientfault != null && clientfault.getFaultMapList() != null && clientfault.getFaultMapList().size() > 0) {
            int faultListSize = false;
            ArrayList clientfaultList = clientfault.getFaultMapList();
            if (clientfaultList != null) {
               int faultListSize = clientfaultList.size();

               for(int i = 0; i < faultListSize; ++i) {
                  if (((Map)clientfaultList.get(i)).containsKey("SCH")) {
                     result.add((String)((Map)clientfaultList.get(i)).get("SCH"));
                  } else if (((Map)clientfaultList.get(i)).containsKey("EVT")) {
                     result.add((String)((Map)clientfaultList.get(i)).get("EVT"));
                  } else if (((Map)clientfaultList.get(i)).containsKey("NET")) {
                     result.add((String)((Map)clientfaultList.get(i)).get("NET"));
                  } else if (((Map)clientfaultList.get(i)).containsKey("DIS")) {
                     result.add((String)((Map)clientfaultList.get(i)).get("DIS"));
                  }
               }
            }
         }
      } catch (Exception var9) {
         logger.error("", var9);
      }

      resultList.setResultList(result);
      resultList.setTotalCount(result.size());
      return resultList;
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public String getDeviceNetworkTraffic(String deviceId, int reqTimeOut) throws Exception {
      String result = null;
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      DeviceInfo deviceDao = null;
      Device device = null;

      try {
         deviceDao = DeviceInfoImpl.getInstance();
         device = deviceDao.getDeviceMinInfo(deviceId);
      } catch (Exception var13) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      }

      if (device == null) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D010[0], OpenApiExceptionCode.D010[1] + "(Check Device)");
      } else {
         boolean isConn = true;

         try {
            isConn = this.getDeviceConnection(deviceId);
         } catch (Exception var12) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
         }

         if (!isConn) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D015[0], OpenApiExceptionCode.D015[1]);
         } else {
            try {
               confMgr.reqGetDevicePredefinedCmd(deviceId, "NETWORK_TRAFFIC", this.getToken());
            } catch (Exception var11) {
               logger.error(var11);
               throw new OpenApiServiceException(OpenApiExceptionCode.D010[0], OpenApiExceptionCode.D010[1]);
            }

            int i = false;

            int i;
            for(i = 0; i < reqTimeOut; ++i) {
               result = confMgr.getDevicePredefinedCmdResultSet(deviceId, this.getToken(), "NETWORK_TRAFFIC");
               if (result != null) {
                  if (result.equals("FAIL")) {
                     logger.error("\n [getDeviceNetworkTraffic]--result is fail!!!!--");
                     throw new OpenApiServiceException(OpenApiExceptionCode.D011[0], OpenApiExceptionCode.D011[1]);
                  }

                  if (result.equals("INVALID")) {
                     logger.error("\n [getDeviceNetworkTraffic]--result is invalid!!!!--");
                     throw new OpenApiServiceException(OpenApiExceptionCode.D021[0], OpenApiExceptionCode.D021[1]);
                  }
                  break;
               }

               try {
                  Thread.sleep(1000L);
               } catch (InterruptedException var10) {
                  logger.error(var10);
                  throw new OpenApiServiceException(OpenApiExceptionCode.D011[0], OpenApiExceptionCode.D011[1]);
               }
            }

            if (result == null && i >= reqTimeOut) {
               throw new OpenApiServiceException(OpenApiExceptionCode.D016[0], OpenApiExceptionCode.D016[1]);
            } else {
               return result;
            }
         }
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public ResultList getDisasterAlertStatus(String alertId) throws OpenApiServiceException {
      ResultList resultList = new ResultList();
      DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
      List simpleDisasterAlertStatus = null;

      try {
         simpleDisasterAlertStatus = deviceMgr.selectSimpleDisasterAlertStatus(alertId);
         resultList.setResultList(simpleDisasterAlertStatus);
         resultList.setTotalCount(simpleDisasterAlertStatus.size());
         return resultList;
      } catch (Exception var6) {
         logger.error(var6);
         throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
      }
   }

   public String getDeviceIdByName(String deviceName, String groupName, String statusViewMode, String deviceType) throws OpenApiException, SQLException {
      String deviceId = "";
      String deviceGroupId = this.getDeviceGroupIdByName(groupName, deviceType);
      if (!deviceGroupId.isEmpty()) {
         DeviceCondition condition = new DeviceCondition();
         condition.setStartPos(1);
         condition.setPageSize(Integer.MAX_VALUE);
         condition.setGroupId(Long.valueOf(deviceGroupId));
         condition.setStatusViewMode(statusViewMode);
         ResultList resList = this.getDeviceList(condition, deviceType);
         List deviceList = resList.getResultList();
         Iterator var10 = deviceList.iterator();

         while(var10.hasNext()) {
            Device device = (Device)var10.next();
            String name = device.getDevice_name();
            String namePattern = "(<!\\[CDATA\\[)?\\s*" + name + "\\s*(\\]\\]>)?";
            if (deviceName.matches(namePattern)) {
               deviceId = device.getDevice_id();
               break;
            }
         }
      }

      return deviceId;
   }

   public String getDeviceGroupIdByName(String deviceGroupName, String deviceType) throws OpenApiServiceException, SQLException {
      String deviceGroupId = "";
      ResultList resList = this.getDeviceGroupList(true, deviceType);
      List deviceGroupList = resList.getResultList();
      Iterator var6 = deviceGroupList.iterator();

      while(var6.hasNext()) {
         DeviceGroup dg = (DeviceGroup)var6.next();
         String groupName = dg.getGroup_name();
         String groupNamePattern = "(<!\\[CDATA\\[)?\\s*" + groupName + "\\s*(\\]\\]>)?";
         if (deviceGroupName.matches(groupNamePattern)) {
            deviceGroupId = String.valueOf(dg.getGroup_id());
         }
      }

      return deviceGroupId;
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public String getDeviceOrgId(String deviceId) throws OpenApiServiceException, NumberFormatException, SQLException {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
      String deviceGroupId = deviceDao.getDeviceGroupIdByDeviceId(deviceId);
      if (deviceGroupId == null) {
         return "no DeviceGroup";
      } else {
         Long orgId = deviceGroupDao.getOrgIdByGroupId(Long.valueOf(deviceGroupId));
         return String.valueOf(orgId);
      }
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public boolean setDeviceScheduleChannel(String deviceId, String schchannel) throws Exception {
      DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
      DeviceDisplayConf info = new DeviceDisplayConf();
      info.setChkSchChannel(Long.parseLong(schchannel));
      boolean result = false;
      info.setDevice_id(deviceId);

      try {
         confManager.reqSetDisplayToDevice(info, this.getToken(), "ALL_MDC");
         return true;
      } catch (Exception var7) {
         logger.error(var7);
         throw new OpenApiServiceException(OpenApiExceptionCode.D007[0], OpenApiExceptionCode.D007[1]);
      }
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public DeviceGroupLayout getVWTInfoByGroupID(String groupId) throws Exception {
      String result = "";
      DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
      VwlLayoutManager vwlLayoutDao = VwlLayoutManagerImpl.getInstance();
      String vwtId = deviceGroupDao.getVwlLayoutIdByGroupId(groupId);
      if (vwtId != null && !vwtId.equals("")) {
         VwlLayout vwl = vwlLayoutDao.getVwlLayoutInfo(vwtId);
         DeviceGroupLayout layout = new DeviceGroupLayout();
         layout.setVwt_id(vwl.getVwt_id());
         layout.setVwt_file_name(vwl.getVwt_file_name());
         return layout;
      } else {
         throw new OpenApiServiceException(OpenApiExceptionCode.D022[0], OpenApiExceptionCode.D022[1]);
      }
   }

   @PreAuthorize("hasAnyRole('Device Read Authority')")
   public ResultList getAllCabinetLayoutConfiguration(String deviceId) throws OpenApiException {
      ResultList resultList = new ResultList();
      if (deviceId != null && !deviceId.equals("")) {
         DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
         Device device = null;

         try {
            device = deviceDao.getDevice(deviceId);
         } catch (Exception var19) {
            logger.error(var19);
            throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
         }

         if (device == null) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D023[0], OpenApiExceptionCode.D023[1]);
         } else if (!"LEDBOX".equals(device.getDevice_type()) && !"RLEDBOX".equals(device.getDevice_type())) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D023[0], OpenApiExceptionCode.D023[1]);
         } else {
            String cabinet_group_layout = device.getCabinet_group_layout();
            if (StringUtils.isEmpty(cabinet_group_layout)) {
               throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
            } else {
               String[] cabinet_group_layout_arr = cabinet_group_layout.split(",", 16);
               ArrayList rtnList = new ArrayList();

               try {
                  String[] var8 = cabinet_group_layout_arr;
                  int var9 = cabinet_group_layout_arr.length;

                  for(int var10 = 0; var10 < var9; ++var10) {
                     String cabinet_group_layout_element = var8[var10];
                     String[] cabinet_properties = cabinet_group_layout_element.split(":", 3);
                     CabinetGroup cabGrp = new CabinetGroup();
                     if (cabinet_properties.length == 3) {
                        long id = Long.parseLong(cabinet_properties[0]);
                        String resolution = cabinet_properties[1];
                        String positions = cabinet_properties[2];
                        cabGrp.setId(id);
                        if (resolution.contains("x")) {
                           cabGrp.setWidth(resolution.split("x")[0]);
                           cabGrp.setHeight(resolution.split("x")[1]);
                        } else {
                           cabGrp.setWidth("-1");
                           cabGrp.setHeight("-1");
                        }

                        if (positions.contains("x")) {
                           cabGrp.setX(Long.parseLong(positions.split("x")[0]));
                           cabGrp.setY(Long.parseLong(positions.split("x")[1]));
                        } else {
                           cabGrp.setX(-1L);
                           cabGrp.setY(-1L);
                        }

                        rtnList.add(cabGrp);
                     }
                  }

                  resultList.setTotalCount(rtnList.size());
                  resultList.setResultList(rtnList);
                  return resultList;
               } catch (Exception var18) {
                  logger.error(var18);
                  throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
               }
            }
         }
      } else {
         throw new OpenApiServiceException(OpenApiExceptionCode.V002[0], OpenApiExceptionCode.V002[1]);
      }
   }

   @PreAuthorize("hasAnyRole('Device Read Authority')")
   public ResultList getCabinetGroupList(String deviceId) throws OpenApiException {
      ResultList resultList = new ResultList();
      if (deviceId != null && !deviceId.equals("")) {
         DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
         Device device = null;

         try {
            device = deviceDao.getDevice(deviceId);
         } catch (Exception var16) {
            logger.error(var16);
            throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
         }

         if (device == null) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D023[0], OpenApiExceptionCode.D023[1]);
         } else if (!"LEDBOX".equals(device.getDevice_type()) && !"RLEDBOX".equals(device.getDevice_type())) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D023[0], OpenApiExceptionCode.D023[1]);
         } else {
            LedCabinetConfManager ledCabinetMgr = LedCabinetConfManagerImpl.getInstance();
            new ArrayList();
            ArrayList rtnList = new ArrayList();

            try {
               List cabinetGroups = ledCabinetMgr.getLedCabinetGroupLayoutInfo(deviceId);
               Iterator var8 = cabinetGroups.iterator();

               while(true) {
                  LedCabinet ledCabGrp;
                  ArrayList cabinetList;
                  CabinetGroup cabGrp;
                  do {
                     do {
                        do {
                           do {
                              if (!var8.hasNext()) {
                                 resultList.setTotalCount(rtnList.size());
                                 resultList.setResultList(rtnList);
                                 return resultList;
                              }

                              ledCabGrp = (LedCabinet)var8.next();
                              cabinetList = new ArrayList();
                              cabGrp = new CabinetGroup();
                           } while(ledCabGrp.getCabinet_group_id() == null);
                        } while(ledCabGrp.getPosition_X() == null);
                     } while(ledCabGrp.getPosition_Y() == null);
                  } while(ledCabGrp.getResolution() == null);

                  cabGrp.setId(ledCabGrp.getCabinet_group_id());
                  cabGrp.setX(ledCabGrp.getPosition_X());
                  cabGrp.setY(ledCabGrp.getPosition_Y());
                  if (ledCabGrp.getResolution().contains(":")) {
                     cabGrp.setWidth(ledCabGrp.getResolution().split(":")[0]);
                     cabGrp.setHeight(ledCabGrp.getResolution().split(":")[1]);
                  } else {
                     cabGrp.setWidth("-1");
                     cabGrp.setHeight("-1");
                  }

                  List ledCabinets = ledCabinetMgr.getLedCabinetList(deviceId, ledCabGrp.getCabinet_group_id());

                  Cabinet cab;
                  for(Iterator var13 = ledCabinets.iterator(); var13.hasNext(); cabinetList.add(cab)) {
                     LedCabinet ledCab = (LedCabinet)var13.next();
                     cab = new Cabinet();
                     if (ledCab.getCabinet_id() == null) {
                        cab.setId(-1L);
                     } else {
                        cab.setId(ledCab.getCabinet_id());
                     }

                     if (ledCab.getPosition_X() != null) {
                        cab.setX(ledCab.getPosition_X());
                     } else {
                        cab.setX(0L);
                     }

                     if (ledCab.getPosition_Y() != null) {
                        cab.setY(ledCab.getPosition_Y());
                     } else {
                        cab.setY(0L);
                     }

                     if (ledCab.getResolution() != null && ledCab.getResolution().contains(":")) {
                        cab.setWidth(ledCab.getResolution().split(":")[0]);
                        cab.setHeight(ledCab.getResolution().split(":")[1]);
                     } else {
                        cab.setWidth("-1");
                        cab.setHeight("-1");
                     }

                     if (ledCab.getPhy_size() != null) {
                        cab.setSize(ledCab.getPhy_size().replace(':', 'x'));
                     } else {
                        cab.setSize("0x0");
                     }

                     if (ledCab.getAspect_ratio() != null) {
                        cab.setRatio(ledCab.getAspect_ratio());
                     } else {
                        cab.setRatio("0:0");
                     }

                     if (ledCab.getModules() != null) {
                        cab.setModule(ledCab.getModules().replace(':', 'x'));
                     } else {
                        cab.setModule("null");
                     }

                     if (ledCab.getPitch() != null) {
                        cab.setPitch(ledCab.getPitch());
                     } else {
                        cab.setPitch("0");
                     }

                     if (ledCab.getCabinet_type() != null) {
                        cab.setModeltype(ledCab.getCabinet_type());
                     } else {
                        cab.setModeltype(-1L);
                     }
                  }

                  cabGrp.setCabinetList(cabinetList);
                  rtnList.add(cabGrp);
               }
            } catch (Exception var17) {
               logger.error(var17);
               throw new OpenApiServiceException(OpenApiExceptionCode.D999[0], OpenApiExceptionCode.D999[1]);
            }
         }
      } else {
         throw new OpenApiServiceException(OpenApiExceptionCode.V002[0], OpenApiExceptionCode.V002[1]);
      }
   }

   @PreAuthorize("hasAnyRole('Device Read Authority')")
   public String getVWTFile(String vwtId) throws Exception {
      String url = CommonConfig.get("web_url");
      VwlLayoutManager vwlLayoutDao = VwlLayoutManagerImpl.getInstance();
      VwlLayout vwlLayout = vwlLayoutDao.getVwlLayoutInfo(vwtId);
      url = url + "/servlet/FileLoader?paramPathConfName=VWT_HOME&download=D&filepath=" + vwtId + "/" + vwlLayout.getVwt_file_name();
      return url;
   }

   @PreAuthorize("hasAnyRole('Device Read Authority')")
   public String getVWTFileByDeviceId(String deviceId, String type) throws Exception {
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      VwlLayoutManager vwlLayoutDao = VwlLayoutManagerImpl.getInstance();
      Device device = deviceInfo.getDevice(deviceId);
      String vwtId = "";
      String vwtFileName = "";
      if (type.equalsIgnoreCase("GROUP")) {
         vwtId = device.getVwt_id();
         VwlLayout vwlLayout = vwlLayoutDao.getVwlLayoutInfo(vwtId);
         vwtFileName = vwlLayout.getVwt_file_name();
      } else if (type.equalsIgnoreCase("SBOX")) {
         vwtId = device.getLed_vwt_id();
         vwtFileName = device.getLed_vwt_file_name();
      }

      return vwtId != null && !vwtId.equals("") && vwtFileName != null && !vwtFileName.equals("") ? CommonConfig.get("web_url") + "/servlet/FileLoader?paramPathConfName=VWT_HOME&download=D&filepath=" + vwtId + "/" + vwtFileName : "";
   }

   @PreAuthorize("hasAnyRole('Device Write Authority')")
   public SoftwareId uploadCustomizeFile(File file, String type, String name) throws Exception {
      String swType = null;
      if (type.equals("1")) {
         swType = "defaultContent";
      } else if (type.equals("2")) {
         swType = "customLogo";
      }

      String userID = this.user.getUser().getUser_id();
      DeviceLogManager logMgr = DeviceLogManagerImpl.getInstance();
      new ServerLogEntity();
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      String device_type = "SPLAYER";
      String device_model = "Default";
      Software software = new Software();
      if (name.equals("")) {
         name = file.getPath();
      }

      software.setSoftware_name(name);
      software.setFile_name(file.getName());
      software.setFile_size(file.length());
      software.setDevice_type(device_type);
      software.setDevice_model_name(device_model);
      software.setIs_auto_update(false);
      software.setCreator_id(userID);
      software.setUpgrade_version("multi");
      String fileSavePath;
      String savedFileName;
      String[] acceptFilesVideo;
      int var17;
      boolean isFileSuccess;
      String filePath;
      if (swType.equalsIgnoreCase("customLogo")) {
         savedFileName = null;
         String[] acceptFiles = new String[]{".jpg", ".jpeg", ".bmp", ".png", ".gif"};
         acceptFilesVideo = new String[]{".avi", ".mpg", ".mpeg", ".mp4", ".vob", ".vro", ".ts", ".trp", ".tp", ".svi", ".mkv", ".wmv", ".asf", ".3gp", ".divx", ".flv", ".m2ts", ".mov", ".rm", ".rmvb", ".mts", ".webm", ".s4ud", ".ps", ".avs", ".um4", ".km4"};
         String[] var16 = acceptFiles;
         var17 = acceptFiles.length;

         int var18;
         String str;
         for(var18 = 0; var18 < var17; ++var18) {
            str = var16[var18];
            if (file.getName().toLowerCase().indexOf(str) > 0) {
               savedFileName = "picture";
            }
         }

         if (savedFileName == null) {
            var16 = acceptFilesVideo;
            var17 = acceptFilesVideo.length;

            for(var18 = 0; var18 < var17; ++var18) {
               str = var16[var18];
               if (file.getName().toLowerCase().indexOf(str) > 0) {
                  savedFileName = "video";
               }
            }
         }

         if (savedFileName == null) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D701[0], OpenApiExceptionCode.D701[1]);
         }

         if (savedFileName.equals("picture") && file.length() > 52428800L) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D700[0], OpenApiExceptionCode.D700[1]);
         }

         if (savedFileName.equals("video") && file.length() > 157286400L) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D700[0], OpenApiExceptionCode.D700[1]);
         }

         fileSavePath = "sw.logo";
         software.setSoftware_type("03");
      } else {
         if (!swType.equalsIgnoreCase("defaultContent")) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D702[0], OpenApiExceptionCode.D702[1]);
         }

         String[] acceptFiles = new String[]{".wmv", ".vro", ".vob", ".ts", ".trp", ".tp", ".svi", ".mts", ".mpg", ".mpeg", ".mp4", ".mov", ".mkv", ".m2ts", ".flv", ".divx", ".avi", ".asf", ".3gp"};
         isFileSuccess = false;
         acceptFilesVideo = acceptFiles;
         int var23 = acceptFiles.length;

         for(var17 = 0; var17 < var23; ++var17) {
            filePath = acceptFilesVideo[var17];
            if (file.getName().toLowerCase().indexOf(filePath) > 0) {
               isFileSuccess = true;
            }
         }

         if (!isFileSuccess) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D701[0], OpenApiExceptionCode.D701[1]);
         }

         fileSavePath = "sw.default_content";
         software.setSoftware_type("04");
      }

      savedFileName = System.currentTimeMillis() + file.getName();
      isFileSuccess = FileUploadCommonHelper.saveFileToDisk(savedFileName, file, fileSavePath);
      if (!isFileSuccess) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D703[0], OpenApiExceptionCode.D703[1]);
      } else {
         software.setMime_type(ChecksumCRC32.getCRC32Value(savedFileName, fileSavePath));
         software.setFile_path(FileUploadCommonHelper.getWebPath(fileSavePath) + "/" + savedFileName);
         Long software_id = new Long((long)SequenceDB.getNextValue("MI_DMS_INFO_SOFTWARE"));
         software.setSoftware_id(software_id);
         String UPLOAD_HOME = "";
         UPLOAD_HOME = CommonConfig.get("UPLOAD_HOME");
         UPLOAD_HOME = UPLOAD_HOME.replace('/', File.separatorChar);
         FileManagerImpl fileManager;
         File swFile;
         if ("03".equalsIgnoreCase(software.getSoftware_type())) {
            fileManager = FileManagerImpl.getInstance();
            filePath = FileUploadCommonHelper.getAbsoluteDirectoryPath("sw.logo") + File.separator + savedFileName;
            swFile = new File(filePath);
            fileManager.createThumbnailFile(swFile, FileUploadCommonHelper.getAbsoluteDirectoryPath(fileSavePath), savedFileName);
         } else {
            if (!"04".equalsIgnoreCase(software.getSoftware_type())) {
               throw new OpenApiServiceException(OpenApiExceptionCode.D702[0], OpenApiExceptionCode.D702[1]);
            }

            fileManager = FileManagerImpl.getInstance();
            filePath = FileUploadCommonHelper.getAbsoluteDirectoryPath("sw.default_content") + File.separator + savedFileName;
            swFile = new File(filePath);
            fileManager.createThumbnailFile(swFile, FileUploadCommonHelper.getAbsoluteDirectoryPath(fileSavePath), savedFileName);
         }

         SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
         boolean result = softwareDao.addDeviceSoftware(software, "P");
         if (!result) {
            throw new OpenApiServiceException(OpenApiExceptionCode.D703[0], OpenApiExceptionCode.D703[1]);
         } else {
            SoftwareId sId = new SoftwareId();
            sId.setSoftware_id(software_id);
            return sId;
         }
      }
   }

   @PreAuthorize("hasAnyRole('Device Read Authority')")
   public boolean publishCustomizeFile(String softwareId, String groupID) throws Exception {
      Long result = 0L;
      String rsvDate = "NOW";
      String deployAppliedVer = "";
      String appliedType = "GROUP";
      String appliedGroupStr = groupID;
      String userID = this.user.getUser().getUser_id();
      String organization = this.user.getUser().getOrganization();

      try {
         SoftwareManager swMgr = SoftwareManagerImpl.getInstance();
         Software software = swMgr.getSoftware(Long.parseLong(softwareId));
         if (rsvDate.equals("NOW")) {
            String timeStamp = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(Calendar.getInstance().getTime());
            software.setRsv_date(Timestamp.valueOf(timeStamp));
         } else {
            software.setType(1L);
            software.setRsv_date(DateUtils.string2Timestamp(rsvDate, SecurityUtils.getUserDateFormat() + " " + SecurityUtils.getUserTimeFormat()));
         }

         software.setDeploy_applied_ver(deployAppliedVer);
         software.setApplied_type(appliedType);
         if (appliedType.equals("GROUP")) {
            software.setDevice_group(appliedGroupStr);
         }

         software.setSubscriber_id(userID);
         Object[] obj = swMgr.deploySoftwareToDevices(software, organization);
         if (obj != null && obj.length > 0) {
            result = (Long)obj[0];
         }
      } catch (Exception var13) {
         logger.error("[SoftwareController] DEPLOY_SAVE fail! softwareId : " + softwareId);
         result = 0L;
      }

      boolean flag = result > 0L;
      if (!flag) {
         throw new OpenApiServiceException(OpenApiExceptionCode.D704[0], OpenApiExceptionCode.D704[1]);
      } else {
         return true;
      }
   }
}
