package com.samsung.magicinfo.restapi.utils;

import com.samsung.common.db.PagedListInfo;
import com.samsung.common.utils.page.PageManager;
import java.io.Serializable;
import java.util.List;

public class V2PageResource implements Serializable {
   private int recordsReturned;
   private int recordsTotal;
   private int recordsFiltered;
   private int startIndex;
   private String sortColumn;
   private String sortOrder;
   private int pageSize;
   private List list;

   public V2PageResource() {
      super();
   }

   public static V2PageResource createPageResource(List list, int totalCount) {
      V2PageResource v2PageResource = new V2PageResource();
      v2PageResource.setRecordsTotal(totalCount);
      v2PageResource.setList(list);
      return v2PageResource;
   }

   public static V2PageResource createPageResource(List list, PageManager pageManager) {
      V2PageResource v2PageResource = new V2PageResource();
      v2PageResource.setRecordsTotal(pageManager.getTotalRowCount());
      v2PageResource.setList(list);
      if (pageManager.getTotalRowCount() > 0) {
         v2PageResource.setRecordsTotal(pageManager.getTotalRowCount());
      } else {
         v2PageResource.setRecordsTotal(0);
      }

      v2PageResource.setPageSize(pageManager.getPageSize());
      return v2PageResource;
   }

   public static V2PageResource createPageResource(List list, PagedListInfo pagedListInfo, int pageSize) {
      V2PageResource v2PageResource = new V2PageResource();
      v2PageResource.setRecordsTotal(pagedListInfo.getTotalRowCount());
      v2PageResource.setList(list);
      v2PageResource.setPageSize(pageSize);
      return v2PageResource;
   }

   public static V2PageResource createPageResource(List list, PagedListInfo pagedListInfo, int startIndex, int pageSize) {
      V2PageResource v2PageResource = createPageResource(list, pagedListInfo, pageSize);
      v2PageResource.setStartIndex(startIndex);
      return v2PageResource;
   }

   public int getRecordsReturned() {
      return this.recordsReturned;
   }

   public void setRecordsReturned(int recordsReturned) {
      this.recordsReturned = recordsReturned;
   }

   public int getRecordsTotal() {
      return this.recordsTotal;
   }

   public void setRecordsTotal(int recordsTotal) {
      this.recordsTotal = recordsTotal;
   }

   public int getRecordsFiltered() {
      return this.recordsFiltered;
   }

   public void setRecordsFiltered(int recordsFiltered) {
      this.recordsFiltered = recordsFiltered;
   }

   public int getStartIndex() {
      return this.startIndex;
   }

   public void setStartIndex(int startIndex) {
      this.startIndex = startIndex;
   }

   public String getSortColumn() {
      return this.sortColumn;
   }

   public void setSortColumn(String sortColumn) {
      this.sortColumn = sortColumn;
   }

   public String getSortOrder() {
      return this.sortOrder;
   }

   public void setSortOrder(String sortOrder) {
      this.sortOrder = sortOrder;
   }

   public int getPageSize() {
      return this.pageSize;
   }

   public void setPageSize(int pageSize) {
      this.pageSize = pageSize;
   }

   public List getList() {
      return this.list;
   }

   public void setList(List list) {
      this.list = list;
   }
}
