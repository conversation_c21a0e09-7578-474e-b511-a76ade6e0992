package com.samsung.magicinfo.cms.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Size;

@JsonInclude(Include.NON_NULL)
public class UrlContentSettingResource {
   @ApiModelProperty(
      example = "00000000-0000-0000-0000-000000000000",
      allowEmptyValue = true
   )
   private String contentId = "";
   @ApiModelProperty(
      example = "URL",
      allowableValues = "LFD,IMAGE,MOVIE,OFFICE,PDF,FLASH,SOUND,DLK,DLKT,FTP,CIFS,STRM,URL,HTML"
   )
   private String type = "";
   @ApiModelProperty(
      example = "0"
   )
   private String groupId = "";
   @ApiModelProperty(
      example = "admin"
   )
   @Size(
      max = 20,
      message = "[ContentFilter][userId] max size is 20."
   )
   private String userId = "";
   private String urlContentName = "";
   private String urlAddress = "";
   private String contentMetaData = "";
   private String refreshInterval = "";

   public UrlContentSettingResource() {
      super();
   }

   public String getContentId() {
      return this.contentId;
   }

   public void setContentId(String contentId) {
      this.contentId = contentId;
   }

   public String getType() {
      return this.type;
   }

   public void setType(String type) {
      this.type = type;
   }

   public String getGroupId() {
      return this.groupId;
   }

   public void setGroupId(String groupId) {
      this.groupId = groupId;
   }

   public String getUserId() {
      return this.userId;
   }

   public void setUserId(String userId) {
      this.userId = userId;
   }

   public String getUrlContentName() {
      return this.urlContentName;
   }

   public void setUrlContentName(String urlContentName) {
      this.urlContentName = urlContentName;
   }

   public String getUrlAddress() {
      return this.urlAddress;
   }

   public void setUrlAddress(String urlAddress) {
      this.urlAddress = urlAddress;
   }

   public String getContentMetaData() {
      return this.contentMetaData;
   }

   public void setContentMetaData(String contentMetaData) {
      this.contentMetaData = contentMetaData;
   }

   public String getRefreshInterval() {
      return this.refreshInterval;
   }

   public void setRefreshInterval(String refreshInterval) {
      this.refreshInterval = refreshInterval;
   }
}
