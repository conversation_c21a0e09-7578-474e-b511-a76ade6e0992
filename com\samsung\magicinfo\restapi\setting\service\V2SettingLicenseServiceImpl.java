package com.samsung.magicinfo.restapi.setting.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DBCacheUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.ExternalSystemUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.common.MessageSourceManager;
import com.samsung.magicinfo.framework.common.MessageSourceManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManager;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManagerImpl;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.setup.dao.SlmLicenseDao;
import com.samsung.magicinfo.framework.setup.entity.CompanyInfoEntity;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseEntity;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseOrgEntity;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManager;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManagerImpl;
import com.samsung.magicinfo.framework.setup.manager.SlmSdkManager;
import com.samsung.magicinfo.framework.setup.manager.SlmSdkManagerImpl;
import com.samsung.magicinfo.protocol.interfaces.WSCall;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.setting.model.V2BrandCodeResource;
import com.samsung.magicinfo.restapi.setting.model.V2OrgLicenseEntity;
import com.samsung.magicinfo.restapi.setting.model.V2OrgLicenseInfoResource;
import com.samsung.magicinfo.restapi.setting.model.V2SoldToCodeResource;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service("V2SettingLicenseService")
@Transactional
public class V2SettingLicenseServiceImpl implements V2SettingLicenseService {
   protected Logger logger = LoggingManagerV2.getLogger(V2SettingLicenseServiceImpl.class);

   public V2SettingLicenseServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public V2PageResource getLicenseList() throws Exception {
      SlmLicenseDao licenseDao = new SlmLicenseDao();
      SlmLicenseManager licenseMng = SlmLicenseManagerImpl.getInstance();
      int startIndex = 1;
      int results = 20;
      Map condition = new HashMap();
      condition.put("sort", "reg_date");
      condition.put("order", "desc");
      PagedListInfo listInfo = licenseDao.getLicenseList(startIndex, results, condition);
      new ArrayList();
      List list = listInfo.getPagedResultList();

      for(int i = 0; i < list.size(); ++i) {
         SlmLicenseEntity info = (SlmLicenseEntity)list.get(i);
         info.getLicense_key();
         String productName = licenseMng.getProductCode(info.getProduct_code());
         String license_type = "";
         ((SlmLicenseEntity)list.get(i)).setProduct_name(productName);
         if (info.getLicense_type().equals("11")) {
            license_type = "Charged";
         } else if (info.getLicense_type().equals("12")) {
            license_type = "Free of charged";
         } else if (info.getLicense_type().equals("13")) {
            license_type = "Free Trial";
         }

         ((SlmLicenseEntity)list.get(i)).setLicense_type(license_type);
         if (info.getEnd_date() != null && Math.abs(info.getStart_date().getYear() - info.getEnd_date().getYear()) >= 100) {
            ((SlmLicenseEntity)list.get(i)).setEnd_date((Timestamp)null);
         }
      }

      V2PageResource resource = V2PageResource.createPageResource(list, list.size());
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public boolean checkLicense(String licenseKey) throws Exception {
      SlmLicenseManager LicenseMag = SlmLicenseManagerImpl.getInstance();
      String[] key = licenseKey.split("-");
      if (key.length != 4) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PRODUCT_NOT_SUPPORT);
      } else if (!LicenseMag.checkProductCode(key[0])) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PRODUCT_NOT_SUPPORT);
      } else {
         int ErrorCode = LicenseMag.licenseKeyVerify(licenseKey);
         if (ErrorCode == 0) {
            return true;
         } else if (ErrorCode == 900) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_LICENSE_IN_USED);
         } else {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_NOT_MATCH_LICENSE_KEY_N_SELECTED_PRODUCT);
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public String getHWUniqueKey() throws Exception {
      SlmLicenseManager LicenseMag = SlmLicenseManagerImpl.getInstance();
      return LicenseMag.getHWUniqueKey();
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public boolean firstActivationOnline(String licenseKey, CompanyInfoEntity company) throws Exception {
      Locale locale = SecurityUtils.getLocale();
      SlmLicenseManager LicenseMag = SlmLicenseManagerImpl.getInstance();
      String[] key = licenseKey.split("-");
      if (!LicenseMag.checkProductCode(key[0])) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PRODUCT_NOT_SUPPORT);
      } else if (LicenseMag.hasChargedLicense(licenseKey)) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SLM_CANNOT_ACTIVATE_MORE_LICENSE);
      } else if (LicenseMag.hasMigrationLicense() >= 0L && LicenseMag.hasOldLicense()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SLM_COMMON_ERROR, new String[]{"You have to return OLD license."});
      } else {
         String hwUniqueKey = LicenseMag.getHWUniqueKey();
         int ErrorCode = LicenseMag.activationRequest(licenseKey, hwUniqueKey, company.getCompany_name(), company.getDivision(), company.getAddress(), company.getPhone(), company.getEmail());
         this.logger.error("licenseKey : " + licenseKey + " ,hwUniqueKey : " + hwUniqueKey + " ,companyName : " + company.getCompany_name() + " ,division : " + company.getDivision() + " ,address : " + company.getAddress() + " ,phone : " + company.getPhone() + ", email : " + company.getEmail());
         if (ErrorCode == 0) {
            LicenseMag.SlmlicenseLoadingConfig();
            ServerSetupInfo serverSetup = ServerSetupInfoImpl.getInstance();
            serverSetup.refreshServerManagementInfo();
            return true;
         } else {
            LicenseMag.setSlmLicenseHistory("01", licenseKey, "licenseKey verify fail! code : " + ErrorCode, true);
            RestExceptionCode restExceptionCode = errorCodeToRestExceptionCode(ErrorCode);
            if (restExceptionCode == RestExceptionCode.BAD_REQUEST_SLM_COMMON_ERROR) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SLM_COMMON_ERROR, new String[]{String.valueOf(ErrorCode)});
            } else {
               throw new RestServiceException(restExceptionCode);
            }
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public boolean firstActivationOffline(String licenseKey, MultipartFile file) throws Exception {
      SlmLicenseManager LicenseMag = SlmLicenseManagerImpl.getInstance();
      MessageSourceManager messageMgr = MessageSourceManagerImpl.getInstance();
      String[] key = licenseKey.split("-");
      if (!LicenseMag.checkProductCode(key[0])) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PRODUCT_NOT_SUPPORT);
      } else if (LicenseMag.hasChargedLicense(licenseKey)) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SLM_CANNOT_ACTIVATE_MORE_LICENSE);
      } else if (LicenseMag.hasMigrationLicense() >= 0L && LicenseMag.hasOldLicense()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SLM_COMMON_ERROR, new String[]{"You have to return OLD license."});
      } else {
         InputStream inputStream = file.getInputStream();
         BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));

         String line;
         String activationKey;
         for(activationKey = ""; (line = bufferedReader.readLine()) != null; activationKey = activationKey + line) {
         }

         int ErrorCode = LicenseMag.hwUniqueKeyVerify(activationKey);
         if (ErrorCode != 0) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_HW_KEY_INVALID);
         } else {
            String licKeyFromAct = LicenseMag.getLicenseKeyfromActivationKey(activationKey);
            if (!licenseKey.equals(licKeyFromAct)) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SLM_COMMON_ERROR, new String[]{"license invalid"});
            } else if (isNumber(licenseKey)) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SLM_COMMON_ERROR, new String[]{licenseKey});
            } else {
               ErrorCode = LicenseMag.licenseKeyVerify(licenseKey);
               if (ErrorCode == 0) {
                  ErrorCode = LicenseMag.activationActive(licKeyFromAct, activationKey);
                  if (ErrorCode == 0) {
                     ServerSetupInfo serverSetup = ServerSetupInfoImpl.getInstance();
                     serverSetup.refreshServerManagementInfo();
                     return true;
                  } else {
                     LicenseMag.setSlmLicenseHistory("01", licenseKey, "licenseKey verify fail! code : " + ErrorCode, true);
                     throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SLM_COMMON_ERROR, new String[]{String.valueOf(ErrorCode)});
                  }
               } else {
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SLM_COMMON_ERROR, new String[]{String.valueOf(ErrorCode)});
               }
            }
         }
      }
   }

   public boolean checkAdditionalActivation(String licenseKey) throws Exception {
      SlmLicenseManager licenseMgr = SlmLicenseManagerImpl.getInstance();
      if (licenseMgr.checkFreeSlmLicense(licenseKey)) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_FREE_LICENSE_NO_ADDITIONAL);
      } else {
         return true;
      }
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public boolean additionalActivationOnline(String licenseKey) throws Exception {
      SlmLicenseManager LicenseMag = SlmLicenseManagerImpl.getInstance();
      int ErrorCode = LicenseMag.changeActivationRequest(LicenseMag.getActivationKey(licenseKey));
      if (ErrorCode == 0) {
         LicenseMag.SlmlicenseLoadingConfig();
         ServerSetupInfo serverSetup = ServerSetupInfoImpl.getInstance();
         serverSetup.refreshServerManagementInfo();
         return true;
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SLM_COMMON_ERROR, new String[]{String.valueOf(ErrorCode)});
      }
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public boolean additionalActivationOffline(String licenseKey, MultipartFile file) throws Exception {
      SlmLicenseManager LicenseMag = SlmLicenseManagerImpl.getInstance();
      InputStream inputStream = file.getInputStream();
      BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));

      String line;
      String NewActivationKey;
      for(NewActivationKey = ""; (line = bufferedReader.readLine()) != null; NewActivationKey = NewActivationKey + line) {
      }

      String OldActivationKey = LicenseMag.getActivationKey(licenseKey);
      int ErrorCode = LicenseMag.changeActivationActive(OldActivationKey, NewActivationKey);
      if (ErrorCode == 0) {
         LicenseMag.SlmlicenseLoadingConfig();
         ServerSetupInfo serverSetup = ServerSetupInfoImpl.getInstance();
         serverSetup.refreshServerManagementInfo();
         return true;
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SLM_COMMON_ERROR, new String[]{String.valueOf(ErrorCode)});
      }
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public String deleteLicense(String licenseKey, String isConnected) throws Exception {
      MessageSourceManager messageMgr = MessageSourceManagerImpl.getInstance();
      Locale locale = SecurityUtils.getLocale();
      SlmLicenseManager LicenseMag = SlmLicenseManagerImpl.getInstance();
      if (LicenseMag.checkFreeSlmLicense(licenseKey)) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_FREE_LICENSE_NOT_DELETE);
      } else {
         ServerSetupInfo serverSetup;
         if (isConnected.equalsIgnoreCase("true")) {
            int ErrorCode = LicenseMag.deActivationRequest(licenseKey);
            if (ErrorCode == 0) {
               LicenseMag.SlmlicenseLoadingConfig();
               serverSetup = ServerSetupInfoImpl.getInstance();
               serverSetup.refreshServerManagementInfo();
               return licenseKey;
            } else {
               RestExceptionCode restExcpetionCode = errorCodeToRestExceptionCode(ErrorCode);
               if (restExcpetionCode == RestExceptionCode.BAD_REQUEST_SLM_COMMON_ERROR) {
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SLM_COMMON_ERROR, new String[]{String.valueOf(ErrorCode)});
               } else {
                  throw new RestServiceException(restExcpetionCode);
               }
            }
         } else {
            String deActivationKey = LicenseMag.deActivationActive(licenseKey);
            if (deActivationKey != null && deActivationKey.length() != 0) {
               if (deActivationKey.equals(500)) {
                  throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SLM_DB_ERROR);
               } else {
                  LicenseMag.SlmlicenseLoadingConfig();
                  serverSetup = ServerSetupInfoImpl.getInstance();
                  serverSetup.refreshServerManagementInfo();
                  return deActivationKey;
               }
            } else {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_DEACTIVATION_KEY_INVALID);
            }
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public List getAvailableFreeLicense() throws Exception {
      List result = new ArrayList();
      SlmLicenseManager LicenseMag = SlmLicenseManagerImpl.getInstance();
      String FreeLicensePremiums = "false";
      String FreeLicenseAndroid = "false";
      String FreeLicenseLite = "false";
      String freeLicenseRM = "false";
      if (!LicenseMag.checkFreeSlmLicense("01015A-L6VPSS-MOE2JC-GRZ5") && !LicenseMag.checkFreeSlmLicense("01014A-QXOL6C-FTYQXL-5ICE") && !LicenseMag.checkFreeSlmLicense("010121-XQGZAG-ERMEJG-QAGF") && !LicenseMag.checkFreeSlmLicense("010120-ISEBNS-WFRI5B-LECS") && !LicenseMag.checkFreeSlmLicense("010V31-Q225OK-WXYFXH-YAUE") && LicenseMag.getCntSlmLicenseMaxClients("01015A", "11") < 1 && LicenseMag.getCntSlmLicenseMaxClients("01014A", "11") < 1 && LicenseMag.getCntSlmLicenseMaxClients("010121", "11") < 1 && LicenseMag.getCntSlmLicenseMaxClients("010120", "11") < 1 && LicenseMag.getCntSlmLicenseMaxClients("010V31", "11") < 1 && LicenseMag.getCntSlmLicenseMaxClients("01015A", "12") < 1 && LicenseMag.getCntSlmLicenseMaxClients("01014A", "12") < 1 && LicenseMag.getCntSlmLicenseMaxClients("010121", "12") < 1 && LicenseMag.getCntSlmLicenseMaxClients("010120", "12") < 1 && LicenseMag.getCntSlmLicenseMaxClients("010V31", "12") < 1) {
         result.add("01015A-L6VPSS-MOE2JC-GRZ5");
      }

      if (!LicenseMag.checkFreeSlmLicense("010311-6YPNK6-KC3EAO-XHEL") && LicenseMag.getCntSlmLicenseMaxClients("010311", "11") < 1 && LicenseMag.getCntSlmLicenseMaxClients("010311", "12") < 1) {
         result.add("010311-6YPNK6-KC3EAO-XHEL");
      }

      if (!LicenseMag.checkFreeSlmLicense("01064A-RR6UU4-XE2GE5-JLCW") && LicenseMag.getCntSlmLicenseMaxClients("01064A", "11") < 1 && LicenseMag.getCntSlmLicenseMaxClients("01064A", "12") < 1) {
         result.add("01064A-RR6UU4-XE2GE5-JLCW");
      }

      return result;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public boolean freeActivationOnline(String licenseKey, CompanyInfoEntity company) throws Exception {
      MessageSourceManager messageMgr = MessageSourceManagerImpl.getInstance();
      Locale locale = SecurityUtils.getLocale();
      SlmLicenseManager LicenseMag = SlmLicenseManagerImpl.getInstance();
      String hwUniqueKey = LicenseMag.getHWUniqueKey();
      String productCode = licenseKey.split("-")[0];
      if (productCode.equals("01015A")) {
         licenseKey = "01015A-L6VPSS-MOE2JC-GRZ5";
      } else if (productCode.equals("01014A")) {
         licenseKey = "01014A-QXOL6C-FTYQXL-5ICE";
      } else if (productCode.equals("010311")) {
         licenseKey = "010311-6YPNK6-KC3EAO-XHEL";
      } else if (productCode.equals("01011N")) {
         licenseKey = "01011N-FRDGVS-NCRI4K-T6OS";
      } else if (productCode.equals("01064A")) {
         licenseKey = "01064A-RR6UU4-XE2GE5-JLCW";
      }

      if (LicenseMag.licenseKeyDuplicate(licenseKey) != 0) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_LICENSE_IN_USED);
      } else {
         int ErrorCode = LicenseMag.activationRequest(licenseKey, hwUniqueKey, company.getCompany_name(), company.getDivision(), company.getAddress(), company.getPhone(), company.getEmail());
         if (ErrorCode == 0) {
            LicenseMag.setSlmLicenseHistory("00", licenseKey, "Request " + LicenseMag.getProductCode(productCode) + " Free license", true);
            LicenseMag.SlmlicenseLoadingConfig();
            return true;
         } else {
            LicenseMag.setSlmLicenseHistory("01", licenseKey, LicenseMag.getProductCode(productCode) + " Free license fail code : " + ErrorCode, true);
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SLM_COMMON_ERROR, new String[]{String.valueOf(ErrorCode)});
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public boolean freeActivationOffline(String licenseKey, MultipartFile file) throws Exception {
      MessageSourceManager messageMgr = MessageSourceManagerImpl.getInstance();
      Locale locale = SecurityUtils.getLocale();
      SlmLicenseManager LicenseMag = SlmLicenseManagerImpl.getInstance();
      String productCode = licenseKey.split("-")[0];
      if (productCode.equals("01015A")) {
         licenseKey = "01015A-L6VPSS-MOE2JC-GRZ5";
      } else if (productCode.equals("01014A")) {
         licenseKey = "01014A-QXOL6C-FTYQXL-5ICE";
      } else if (productCode.equals("010311")) {
         licenseKey = "010311-6YPNK6-KC3EAO-XHEL";
      } else if (productCode.equals("01011N")) {
         licenseKey = "01011N-FRDGVS-NCRI4K-T6OS";
      } else if (productCode.equals("01064A")) {
         licenseKey = "01064A-RR6UU4-XE2GE5-JLCW";
      }

      if (LicenseMag.licenseKeyDuplicate(licenseKey) != 0) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_LICENSE_IN_USED);
      } else {
         InputStream inputStream = file.getInputStream();
         BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));

         String line;
         String NewActivationKey;
         for(NewActivationKey = ""; (line = bufferedReader.readLine()) != null; NewActivationKey = NewActivationKey + line) {
         }

         int ErrorCode = LicenseMag.hwUniqueKeyVerify(NewActivationKey);
         if (ErrorCode != 0) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_HW_KEY_INVALID);
         } else {
            ErrorCode = LicenseMag.activationActive(licenseKey, NewActivationKey);
            if (ErrorCode == 0) {
               LicenseMag.setSlmLicenseHistory("00", licenseKey, "Request " + LicenseMag.getProductCode(productCode) + " Free license", true);
               LicenseMag.SlmlicenseLoadingConfig();
               return true;
            } else {
               LicenseMag.setSlmLicenseHistory("01", licenseKey, LicenseMag.getProductCode(productCode) + " Free license fail code : " + ErrorCode, true);
               LicenseMag.setSlmLicenseHistory("01", licenseKey, "licenseKey verify fail! code : " + ErrorCode, true);
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SLM_COMMON_ERROR, new String[]{String.valueOf(ErrorCode)});
            }
         }
      }
   }

   public int getLicensesHistoriesCnt() throws Exception {
      SlmLicenseManager LicenseMag = SlmLicenseManagerImpl.getInstance();
      List licenseHistoryList = LicenseMag.getAllSlmLicenseHistory();
      return licenseHistoryList.size();
   }

   public List getLicensesHistories(int page) throws Exception {
      SlmLicenseManager LicenseMag = SlmLicenseManagerImpl.getInstance();
      List licenseHistoryList = LicenseMag.getAllSlmLicenseHistory(page);
      return licenseHistoryList;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public CompanyInfoEntity getCompanyInfo() throws Exception {
      SlmLicenseManager licenseDao = SlmLicenseManagerImpl.getInstance();
      CompanyInfoEntity company = null;
      company = licenseDao.getCompanyInfo();
      if (company == null) {
         company = new CompanyInfoEntity();
      }

      return company;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public boolean saveCompanyInfo(CompanyInfoEntity company) throws Exception {
      SlmLicenseManager LicenseMag = SlmLicenseManagerImpl.getInstance();
      SlmSdkManager slmSDKManager = SlmSdkManagerImpl.getInstance();
      JSONArray codeList = slmSDKManager.requestGetAccountList(company.getSold_to_code());

      for(int i = 0; i < codeList.length(); ++i) {
         JSONObject code = codeList.getJSONObject(i);
         if (code.getString("soldToCode").equals(company.getSold_to_code())) {
            company.setCompany_name(code.getString("accountNm"));
         }
      }

      LicenseMag.insertCompanyInfo(company);
      return true;
   }

   public V2PageResource getE2ELicenseList() throws Exception {
      SlmLicenseDao licenseDao = new SlmLicenseDao();
      int startIndex = 1;
      int results = 20;
      Map condition = new HashMap();
      PagedListInfo listInfo = licenseDao.getLicenseListForE2E(startIndex, results, condition);
      new ArrayList();
      List list = listInfo.getPagedResultList();
      V2PageResource resource = V2PageResource.createPageResource(list, list.size());
      return resource;
   }

   public int getE2ELicensesHistoriesCnt(String deviceId) throws Exception {
      SlmLicenseManager LicenseMag = SlmLicenseManagerImpl.getInstance();
      int result;
      if (deviceId != null && !deviceId.equals("")) {
         result = LicenseMag.getCntSlmLicenseHistoryForE2EByDeviceId(deviceId);
      } else {
         result = LicenseMag.getCntAllSlmLicenseHistoryForE2E();
      }

      return result;
   }

   public List getE2ELicensesHistories(String deviceId, int startIndex) throws Exception {
      SlmLicenseManager LicenseMag = SlmLicenseManagerImpl.getInstance();
      List licenseHistoryList = null;
      if (deviceId != null && !deviceId.equals("")) {
         licenseHistoryList = LicenseMag.getSlmLicenseHistoryForE2EByDeviceId(deviceId, startIndex);
      } else {
         licenseHistoryList = LicenseMag.getAllSlmLicenseHistoryForE2E(startIndex);
      }

      return licenseHistoryList;
   }

   public boolean deleteE2ELicense(String licenseKey) throws Exception {
      MessageSourceManager messageMgr = MessageSourceManagerImpl.getInstance();
      Locale locale = SecurityUtils.getLocale();
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
      MonitoringManager monMgr = MonitoringManagerImpl.getInstance();
      DeviceLogManager logMgr = DeviceLogManagerImpl.getInstance();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      SlmLicenseManager licenseMgr = SlmLicenseManagerImpl.getInstance();
      SlmLicenseDao licenseDao = new SlmLicenseDao();
      SlmLicenseEntity slmEntity = licenseDao.getSlmLicenseForE2E(licenseKey);
      String deviceId = slmEntity.getDevice_id();
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      String eventType = rms.getMessage("TEXT_TITLE_DELETE_DEVICE_P", (Object[])null, Locale.ENGLISH);
      String strMenu = rms.getMessage("COM_SID_MENU", (Object[])null, Locale.ENGLISH);
      String strMenuName = rms.getMessage("COM_TEXT_MONITORING_P", (Object[])null, Locale.ENGLISH);
      String strCommand = rms.getMessage("TEXT_TITLE_DELETE_DEVICE_P", (Object[])null, Locale.ENGLISH);
      boolean flag = true;
      boolean isRedundancy = false;
      boolean isVwtDevice = false;
      boolean tempIsRedundancy = deviceDao.isRedundancyDevice(deviceId);
      isRedundancy = isRedundancy || tempIsRedundancy;
      boolean isRedundancyGroupTarget = false;

      try {
         isRedundancyGroupTarget = deviceGroupDao.isRedundancyGroup(Integer.parseInt(deviceDao.getDeviceGroupIdByDeviceId(deviceId)));
      } catch (Exception var28) {
         this.logger.error("", var28);
      }

      isRedundancy = isRedundancy || isRedundancyGroupTarget;
      Device device = deviceDao.getDevice(deviceId);
      if (device != null && device.getVwt_id() != null && !device.getVwt_id().equals("")) {
         isVwtDevice = true;
      }

      if (!isVwtDevice && !isRedundancy) {
         boolean deActivationResult;
         if (CommonConfig.get("e2e.enable") != null && CommonConfig.get("e2e.enable").equalsIgnoreCase("true")) {
            boolean res;
            if (CommonConfig.get("e2e.license.system") != null && !CommonConfig.get("e2e.license.system").toUpperCase().equals(ExternalSystemUtils.SYSTEM_PBP)) {
               try {
                  deActivationResult = licenseMgr.deActivationProcessForE2E_SLMDirect(deviceId);
                  if (!deActivationResult) {
                     flag = false;
                  } else {
                     res = licenseDao.deleteLicenseInfoForE2EByDeviceId(deviceId);
                     if (!res) {
                        this.logger.error("Success deactivation but fail to delete on DB" + deviceId);
                     }
                  }
               } catch (Exception var26) {
                  this.logger.error("", var26);
                  flag = false;
               }
            } else {
               try {
                  deActivationResult = licenseMgr.deActivationProcessForE2E(deviceId);
                  if (!deActivationResult) {
                     flag = false;
                  } else {
                     res = licenseDao.deleteLicenseInfoForE2EByDeviceId(deviceId);
                     if (!res) {
                        this.logger.error("Success deactivation but fail to delete on DB" + deviceId);
                     }
                  }
               } catch (Exception var27) {
                  this.logger.error("", var27);
                  flag = false;
               }
            }
         }

         if (deviceId != null && !deviceId.equals("")) {
            deActivationResult = deviceDao.deleteDevice(deviceId);
            if (!deActivationResult) {
               flag = false;
            } else {
               monMgr.connectionReload(deviceId, 0);
               monMgr.scheduleReload(deviceId, 0);
               monMgr.deleteConnectionInfo(deviceId);
               WSCall.setPlayerRequest(deviceId, "agent restart");
               DBCacheUtils.deletePreAssignedGroup(deviceId);
            }
         } else {
            flag = false;
         }
      } else {
         if (isVwtDevice) {
            throw new Exception("Cannot delete a device in the Video Wall layout group.");
         }

         if (isRedundancy) {
            throw new Exception("Backup Player device cannot be deleted. if you want delete this device, please clear Backup Player on the group.");
         }
      }

      if (flag) {
         return true;
      } else {
         throw new Exception("Failed to delete E2E license.");
      }
   }

   public boolean swapE2ELicense(String oldDeviceId, String newDeviceId) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      String message = "";

      try {
         DeviceUtils.swapDeviceForE2E(oldDeviceId, newDeviceId, userId);
         return true;
      } catch (Exception var7) {
         throw new Exception(message);
      }
   }

   private static boolean isNumber(String str) {
      if (str == null) {
         return false;
      } else {
         Pattern p = Pattern.compile("([\\p{Digit}]+)");
         Matcher m = p.matcher(str);
         return m.matches();
      }
   }

   private static RestExceptionCode errorCodeToRestExceptionCode(int errorCode) {
      switch(errorCode) {
      case 8:
         return RestExceptionCode.BAD_REQUEST_SLM_SERVER_NOT_AVAILABLE;
      case 900:
         return RestExceptionCode.BAD_REQUEST_SAME_TYPE_LICENSE;
      case 4315:
         return RestExceptionCode.BAD_REQUEST_NOT_AVAILABLE_SSL;
      case 12000:
         return RestExceptionCode.BAD_REQUEST_WRONG_LICENSE_KEY;
      case 13000:
         return RestExceptionCode.BAD_REQUEST_LICENSE_KEY_USED_IN_ANOTHER_PC;
      case 19002:
         return RestExceptionCode.BAD_REQUEST_NO_ACTIVATION_TO_UPDATE;
      default:
         return RestExceptionCode.BAD_REQUEST_SLM_COMMON_ERROR;
      }
   }

   public List getSoldToCode(String soldToCode) throws Exception {
      SlmSdkManager slmSDKManager = SlmSdkManagerImpl.getInstance();
      JSONArray codeList = null;
      if (soldToCode != null && !soldToCode.equals("")) {
         codeList = slmSDKManager.requestGetAccountList(soldToCode);
         int removePos = 0;

         for(int i = 0; i < codeList.length(); ++i) {
            if (codeList.getJSONObject(i).getString("soldToCode").equals(soldToCode)) {
               removePos = i;
            }
         }

         codeList.remove(removePos);
      } else {
         SlmLicenseDao slmDao = new SlmLicenseDao();
         CompanyInfoEntity company = slmDao.getCompanyInfo();
         soldToCode = company.getSold_to_code();
         codeList = slmSDKManager.requestGetAccountList(soldToCode);

         for(int i = 0; i < codeList.length(); ++i) {
            if (!codeList.getJSONObject(i).getString("soldToCode").equals(soldToCode)) {
               codeList.remove(i);
            }
         }
      }

      ObjectMapper mapper = new ObjectMapper();
      List result = Arrays.asList((Object[])mapper.readValue(codeList.toString(), V2SoldToCodeResource[].class));
      return result;
   }

   public List getBrandCode() throws Exception {
      SlmSdkManager slmSDKManager = SlmSdkManagerImpl.getInstance();
      JSONArray codeList = null;
      new SlmLicenseDao();
      String[] brandCodeArr = null;
      if (CommonConfig.get("pbp.brandCode") != null) {
         brandCodeArr = CommonConfig.get("pbp.brandCode").split(",");
      }

      codeList = slmSDKManager.requestGetBrandList(brandCodeArr);

      for(int i = 0; i < codeList.length(); ++i) {
      }

      ObjectMapper mapper = new ObjectMapper();
      List result = Arrays.asList((Object[])mapper.readValue(codeList.toString(), V2BrandCodeResource[].class));
      return result;
   }

   public boolean addDeviceTypeListByProductCode(String productCode, String productName, String deviceTypeList) throws SQLException {
      SlmLicenseDao licenseDao = new SlmLicenseDao();
      return licenseDao.addDeviceTypeListByProductCode(productCode, productName, deviceTypeList);
   }

   public boolean updateDeviceTypeListByProductCode(String productCode, String deviceTypeList) throws SQLException {
      SlmLicenseDao licenseDao = new SlmLicenseDao();
      return licenseDao.updateDeviceTypeListByProductCode(productCode, deviceTypeList);
   }

   public List getLicenseInfoAssignedToOrganization() throws SQLException {
      SlmLicenseManager LicenseMag = SlmLicenseManagerImpl.getInstance();
      LicenseMag.initSlmLicenseOrgInfo();
      SlmLicenseDao licenseDao = new SlmLicenseDao();
      List slmLicenseOrgEntities = licenseDao.getLicenseInfoAssignedToOrganization();
      List v2OrgLicenseInfoResources = new ArrayList();
      long curOrgId = -1L;
      V2OrgLicenseInfoResource v2OrgLicenseInfoResource = new V2OrgLicenseInfoResource();
      Iterator var8 = slmLicenseOrgEntities.iterator();

      while(var8.hasNext()) {
         SlmLicenseOrgEntity slmLicenseOrgEntity = (SlmLicenseOrgEntity)var8.next();
         if (curOrgId != slmLicenseOrgEntity.getOrganization_id()) {
            v2OrgLicenseInfoResource = new V2OrgLicenseInfoResource();
            v2OrgLicenseInfoResource.setOrganizationId(slmLicenseOrgEntity.getOrganization_id());
            v2OrgLicenseInfoResource.setOrganizationName(slmLicenseOrgEntity.getOrganization_name());
            List v2OrgLicenseEntities = new ArrayList();
            v2OrgLicenseInfoResource.setOrgLicenseEntities(v2OrgLicenseEntities);
            v2OrgLicenseInfoResources.add(v2OrgLicenseInfoResource);
            curOrgId = slmLicenseOrgEntity.getOrganization_id();
         }

         V2OrgLicenseEntity v2OrgLicenseEntity = new V2OrgLicenseEntity();
         v2OrgLicenseEntity.setProductCode(slmLicenseOrgEntity.getProduct_code());
         v2OrgLicenseEntity.setProductName(slmLicenseOrgEntity.getProduct_name());
         if (slmLicenseOrgEntity.getMax_license_count() == -1L) {
            v2OrgLicenseEntity.setMaxLicenseCount(0L);
            v2OrgLicenseEntity.setAssigned(false);
         } else {
            v2OrgLicenseEntity.setMaxLicenseCount(slmLicenseOrgEntity.getMax_license_count());
            v2OrgLicenseEntity.setAssigned(true);
         }

         v2OrgLicenseEntity.setUsedLicenseCount(slmLicenseOrgEntity.getUsed_license_count());
         this.logger.info(slmLicenseOrgEntity.toString());
         v2OrgLicenseInfoResource.getOrgLicenseEntities().add(v2OrgLicenseEntity);
      }

      return v2OrgLicenseInfoResources;
   }

   public boolean editLicenseInfoAssignedToOrganization(List orgLicenseInfoResources) {
      this.validateParameterLicenseInfoAssignedToOrganization(orgLicenseInfoResources);
      List slmLicenseOrgEntities = new ArrayList();
      Iterator var3 = orgLicenseInfoResources.iterator();

      while(var3.hasNext()) {
         V2OrgLicenseInfoResource orgLicenseInfoResource = (V2OrgLicenseInfoResource)var3.next();
         List orgLicenseEntities = orgLicenseInfoResource.getOrgLicenseEntities();
         Iterator var6 = orgLicenseEntities.iterator();

         while(var6.hasNext()) {
            V2OrgLicenseEntity orgLicenseEntity = (V2OrgLicenseEntity)var6.next();
            SlmLicenseOrgEntity slmLicenseOrgEntity = new SlmLicenseOrgEntity();
            slmLicenseOrgEntity.setOrganization_id(orgLicenseInfoResource.getOrganizationId());
            slmLicenseOrgEntity.setProduct_code(orgLicenseEntity.getProductCode());
            slmLicenseOrgEntity.setMax_license_count(orgLicenseEntity.getMaxLicenseCount());
            slmLicenseOrgEntity.setAssigned(orgLicenseEntity.isAssigned());
            slmLicenseOrgEntities.add(slmLicenseOrgEntity);
         }
      }

      try {
         SlmLicenseDao licenseDao = new SlmLicenseDao();
         licenseDao.editLicenseInfoAssignedToOrganization(slmLicenseOrgEntities);
      } catch (SQLException var9) {
         if (var9.getMessage().contains("mi_system_map_slm_license_org_fkey_group_id")) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_NOT_EXIST_ORGANIZATION_ID);
         }

         if (var9.getMessage().contains("mi_system_info_slm_license_org_fkey_product_code")) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_NOT_EXIST_SLM_LICENSE_PRODUCT_CODE);
         }
      } catch (RuntimeException var10) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_ASSIGN_NUMBER_OF_LICENSE_TO_ORGANIZATION);
      }

      return true;
   }

   private void validateParameterLicenseInfoAssignedToOrganization(List orgLicenseInfoResources) {
      Iterator var2 = orgLicenseInfoResources.iterator();

      while(var2.hasNext()) {
         V2OrgLicenseInfoResource orgLicenseInfoResource = (V2OrgLicenseInfoResource)var2.next();
         List orgLicenseEntities = orgLicenseInfoResource.getOrgLicenseEntities();
         Iterator var5 = orgLicenseEntities.iterator();

         while(var5.hasNext()) {
            V2OrgLicenseEntity orgLicenseEntity = (V2OrgLicenseEntity)var5.next();
            if (orgLicenseEntity.getMaxLicenseCount() < 0L) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"Max License Count"});
            }

            if (orgLicenseInfoResource.getOrganizationId() < 0L) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"Organization ID"});
            }

            if (orgLicenseEntity.getProductCode().isEmpty()) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_EMPTY, new String[]{"Product Code"});
            }

            if (!orgLicenseEntity.isAssigned() && orgLicenseEntity.getMaxLicenseCount() != 0L) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_SET_MAX_LICENSE_COUNT);
            }

            if (orgLicenseEntity.isAssigned() && orgLicenseEntity.getMaxLicenseCount() < 0L) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_MUST_SET_MAX_LICENSE_COUNT);
            }
         }
      }

   }
}
