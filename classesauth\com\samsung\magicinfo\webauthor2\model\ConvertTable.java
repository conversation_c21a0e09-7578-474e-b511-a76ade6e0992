package com.samsung.magicinfo.webauthor2.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.samsung.magicinfo.webauthor2.repository.model.datalink.ConvertDataData;
import com.samsung.magicinfo.webauthor2.repository.model.datalink.ConvertTableData;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ConvertTable {
  private String name;
  
  private ConvertType convertType;
  
  private String createdDate;
  
  private List<ConvertData> convertDataList = new ArrayList<>();
  
  @JsonIgnore
  private final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
  
  public ConvertTable() {}
  
  public ConvertTable(String name, ConvertType convertType, String createdDate, List<ConvertData> convertDataList) {
    this.name = name;
    this.convertType = convertType;
    this.createdDate = createdDate;
    this.convertDataList = convertDataList;
  }
  
  public ConvertTableData toData() {
    List<ConvertDataData> convertList = new ArrayList<>();
    for (ConvertData data : this.convertDataList)
      convertList.add(data.toData()); 
    return new ConvertTableData(this.name, this.convertType, this.createdDate, convertList);
  }
  
  public static ConvertTable fromData(ConvertTableData data) {
    return new ConvertTable(data.getName(), data.getConvertType(), data.getCreatedDate(), ConvertData.fromData(data.getConvertDataList()));
  }
  
  public static List<ConvertTable> fromData(List<ConvertTableData> dataList) {
    List<ConvertTable> convertTables = new ArrayList<>();
    for (ConvertTableData data : dataList)
      convertTables.add(fromData(data)); 
    return convertTables;
  }
  
  public void updateDate() {
    Date now = new Date();
    String dateString = this.DATE_FORMAT.format(now);
    setCreatedDate(dateString);
  }
  
  public void updateConvertDataFieldsInList() {
    for (ConvertData data : this.convertDataList) {
      if (data.getCreatedDate() == null)
        data.setCreatedDate(getCreatedDate()); 
      data.setConvertType(getConvertType());
      data.setName(getName());
    } 
  }
  
  public String getName() {
    return this.name;
  }
  
  public void setName(String name) {
    this.name = name;
  }
  
  public ConvertType getConvertType() {
    return this.convertType;
  }
  
  public void setConvertType(ConvertType convertType) {
    this.convertType = convertType;
  }
  
  public String getCreatedDate() {
    return this.createdDate;
  }
  
  public void setCreatedDate(String createdDate) {
    this.createdDate = createdDate;
  }
  
  public List<ConvertData> getConvertDataList() {
    return this.convertDataList;
  }
  
  public void setConvertDataList(List<ConvertData> convertDataList) {
    this.convertDataList = convertDataList;
  }
}
