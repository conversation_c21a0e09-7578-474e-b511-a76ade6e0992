<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.samsung.magicinfo.framework.setup.dao.CategoryDaoMapper">


	<select id="getCategoryWithPgroupId" resultType="com.samsung.magicinfo.framework.setup.entity.CategoryEntity">
		SELECT * FROM MI_CATEGORY_INFO_GROUP
		WHERE P_GROUP_ID = #{groupId}
		ORDER BY GROUP_NAME ASC, DESCRIPTION
	</select>
	
	<select id="getCategoryWithPgroupIdOrganization" resultType="com.samsung.magicinfo.framework.setup.entity.CategoryEntity">
		SELECT * FROM MI_CATEGORY_INFO_GROUP
		WHERE P_GROUP_ID = #{groupId} AND (DESCRIPTION = 'Organization' OR DESCRIPTION = '0')AND GROUP_NAME = #{organization}
		ORDER BY GROUP_NAME ASC, DESCRIPTION
	</select>

	<insert id="addCategory">
		INSERT INTO MI_CATEGORY_INFO_GROUP (GROUP_ID, GROUP_NAME, P_GROUP_ID, GROUP_DEPTH, CREATOR_ID, CREATE_DATE, DESCRIPTION)
		VALUES (#{category.group_id}, #{category.group_name}, #{category.p_group_id}, #{category.group_depth}, #{category.creator_id}, CURRENT_TIMESTAMP, #{category.description})
	</insert>
	
	<delete id="deleteCategory">
		DELETE FROM MI_CATEGORY_INFO_GROUP
		WHERE GROUP_ID = #{groupId}
	</delete>

	<select id="getCategory" resultType="com.samsung.magicinfo.framework.setup.entity.CategoryEntity">
		SELECT * FROM MI_CATEGORY_INFO_GROUP WHERE GROUP_ID = #{groupId}
	</select>
	
	<select id="getCategoryList" resultType="map">
		SELECT CONTENT_NAME, CATEGORY.GROUP_NAME, CATEGORY.GROUP_ID
		FROM MI_CMS_INFO_CONTENT
		LEFT JOIN (
			SELECT CATEGORY_GROUP.GROUP_NAME, CATEGORY_GROUP.GROUP_ID, CATEGORY_MAP.CONTENT_ID
			FROM MI_CATEGORY_MAP_CONTENT CATEGORY_MAP
			LEFT JOIN MI_CATEGORY_INFO_GROUP CATEGORY_GROUP ON CATEGORY_MAP.GROUP_ID=CATEGORY_GROUP.GROUP_ID
			WHERE CONTENT_ID = #{contentId}
		) CATEGORY ON CATEGORY.CONTENT_ID = MI_CMS_INFO_CONTENT.CONTENT_ID
		WHERE MI_CMS_INFO_CONTENT.CONTENT_ID = #{contentId}
	</select>
	<select id="getTagList" resultType="map">
		SELECT CONTENT_NAME, TAG.TAG_ID, TAG.TAG_NAME
		FROM MI_CMS_INFO_CONTENT
		LEFT JOIN (
			SELECT TAG_MAP.CONTENT_ID, TAG_INFO.TAG_ID, TAG_INFO.TAG_NAME FROM MI_TAG_MAP_CONTENT TAG_MAP
			LEFT JOIN MI_TAG_INFO_TAG TAG_INFO ON TAG_MAP.TAG_ID=TAG_INFO.TAG_ID
			WHERE CONTENT_ID = #{contentId}
		) TAG ON TAG.CONTENT_ID = MI_CMS_INFO_CONTENT.CONTENT_ID
		WHERE MI_CMS_INFO_CONTENT.CONTENT_ID = #{contentId}
	</select>
	
	<select id="getCategoryWithContentId" resultType="com.samsung.magicinfo.framework.setup.entity.CategoryEntity">
		SELECT CATEGORY_GROUP.*
		FROM MI_CATEGORY_MAP_CONTENT CATEGORY_MAP
		LEFT JOIN MI_CATEGORY_INFO_GROUP CATEGORY_GROUP ON CATEGORY_MAP.GROUP_ID=CATEGORY_GROUP.GROUP_ID
		WHERE CONTENT_ID = #{contentId}
	</select>
	
	<select id="getCategoryWithPlaylistId" resultType="com.samsung.magicinfo.framework.setup.entity.CategoryEntity">
		SELECT CATEGORY_GROUP.*
		FROM MI_CATEGORY_MAP_PLAYLIST CATEGORY_MAP
		LEFT JOIN MI_CATEGORY_INFO_GROUP CATEGORY_GROUP ON CATEGORY_MAP.GROUP_ID=CATEGORY_GROUP.GROUP_ID
		WHERE PLAYLIST_ID = #{playlistId}
	</select>
	
	<insert id="setCategoryFromContentId">
		INSERT INTO MI_CATEGORY_MAP_CONTENT (GROUP_ID, CONTENT_ID) VALUES(#{groupId}, #{contentId})
	</insert>
	<insert id="setCategoryFromPlaylistId">
		INSERT INTO MI_CATEGORY_MAP_PLAYLIST (GROUP_ID, PLAYLIST_ID) VALUES(#{groupId}, #{playlistId})
	</insert>
	
	
	<delete id="deleteCategoryFromContentId">
		DELETE FROM MI_CATEGORY_MAP_CONTENT WHERE CONTENT_ID = #{contentId}
	</delete>
	
	<delete id="deleteCategoryFromPlaylistId">
		DELETE FROM MI_CATEGORY_MAP_PLAYLIST WHERE PLAYLIST_ID = #{playlistId}
	</delete>
	
	<update id="moveCategory">
		UPDATE MI_CATEGORY_INFO_GROUP SET P_GROUP_ID = #{parentId} WHERE GROUP_ID = #{groupId}
	</update>
	
	<update id="updateCategory">
		UPDATE MI_CATEGORY_INFO_GROUP SET
		GROUP_NAME = #{category.group_name},
		P_GROUP_ID = #{category.p_group_id},
		GROUP_DEPTH = #{category.group_depth},
		CREATOR_ID = #{category.creator_id},
		DESCRIPTION = #{category.description}
		WHERE GROUP_ID = #{category.group_id}
	</update>

	<select id="getCategoryByMultipleOrg" resultType="com.samsung.magicinfo.framework.setup.entity.CategoryEntity">
		SELECT * FROM MI_CATEGORY_INFO_GROUP
		<if test="userList != null and userList.size() > 0">
			WHERE
			<foreach collection="userList" item="group" open="(" close=")" separator=" OR ">
				GROUP_NAME = #{group.group_name}
			</foreach>
		</if>
		ORDER BY GROUP_NAME ASC, DESCRIPTION
	</select>
</mapper>