package com.samsung.magicinfo.webauthor2.model;

import com.samsung.magicinfo.webauthor2.repository.inmemory.model.PreviewUsage;
import java.util.ArrayList;
import java.util.List;

public class PreviewUsageResponse {
  private String contentId;
  
  private String lastused;
  
  private String userId;
  
  private String startPage;
  
  private int progress;
  
  public String getContentId() {
    return this.contentId;
  }
  
  public void setContentId(String contentId) {
    this.contentId = contentId;
  }
  
  public String getLastused() {
    return this.lastused;
  }
  
  public void setLastused(String lastused) {
    this.lastused = lastused;
  }
  
  public String getUserId() {
    return this.userId;
  }
  
  public void setUserId(String userId) {
    this.userId = userId;
  }
  
  public String getStartPage() {
    return this.startPage;
  }
  
  public void setStartPage(String startPage) {
    this.startPage = startPage;
  }
  
  public int getProgress() {
    return this.progress;
  }
  
  public void setProgress(int progress) {
    this.progress = progress;
  }
  
  public PreviewUsageResponse() {}
  
  public PreviewUsageResponse(String contentId, String lastused, String userId, String startPage, int progress) {
    this.contentId = contentId;
    this.lastused = lastused;
    this.userId = userId;
    this.startPage = startPage;
    this.progress = progress;
  }
  
  public static PreviewUsageResponse fromUsageData(PreviewUsage usage) {
    return new PreviewUsageResponse(usage.getContentId(), usage.getLastused().toString("yyyy-MM-dd"), usage.getUserId(), usage.getStartPage(), usage.getProgress());
  }
  
  public static List<PreviewUsageResponse> fromUsageData(List<PreviewUsage> usages) {
    List<PreviewUsageResponse> responses = new ArrayList<>();
    for (PreviewUsage usage : usages)
      responses.add(fromUsageData(usage)); 
    return responses;
  }
}
