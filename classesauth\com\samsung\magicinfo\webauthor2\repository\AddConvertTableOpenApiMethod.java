package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.exception.repository.CannotAddConvertTableException;
import com.samsung.magicinfo.webauthor2.repository.model.OpenAPIResponseData;
import com.samsung.magicinfo.webauthor2.repository.model.datalink.ConvertTableData;
import com.samsung.magicinfo.webauthor2.util.JaxbUtil;
import java.io.StringReader;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

public class AddConvertTableOpenApiMethod {
  private static final Logger logger = LoggerFactory.getLogger(AddConvertTableOpenApiMethod.class);
  
  private final String PREFIX_REST_PATH = "/openapi/open?service=";
  
  private final char DOT_CHAR = '.';
  
  private final RestTemplate restTemplate;
  
  private final JaxbUtil jaxbUtil;
  
  private final String userId;
  
  private final String token;
  
  private final ConvertTableData convertTableData;
  
  public AddConvertTableOpenApiMethod(RestTemplate restTemplate, String userId, String token, ConvertTableData convertTableData, JaxbUtil jaxbUtil) {
    this.restTemplate = restTemplate;
    this.userId = userId;
    this.token = token;
    this.convertTableData = convertTableData;
    this.jaxbUtil = jaxbUtil;
  }
  
  protected Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("token", this.token);
    return vars;
  }
  
  protected MultiValueMap<String, String> getRequestMap() {
    String tableValue = this.jaxbUtil.convertTable(this.convertTableData);
    LinkedMultiValueMap linkedMultiValueMap = new LinkedMultiValueMap();
    linkedMultiValueMap.add("convertTable", tableValue);
    return (MultiValueMap<String, String>)linkedMultiValueMap;
  }
  
  String convertResponseData(OpenAPIResponseData responseData) {
    if (responseData.getErrorMessage() != null)
      throw new CannotAddConvertTableException(responseData.getCode(), responseData.getErrorMessage()); 
    return responseData.getResponseClass();
  }
  
  private String path() {
    String path = getRestPath();
    StringBuilder sb = new StringBuilder(path);
    for (String parameterName : getRequestParams().keySet())
      sb.append("&").append(parameterName).append("={").append(parameterName).append("}"); 
    return sb.toString();
  }
  
  private String getRestPath() {
    StringBuilder sbRestPath = new StringBuilder();
    sbRestPath.append("/openapi/open?service=");
    sbRestPath.append("CommonContentService");
    sbRestPath.append('.');
    sbRestPath.append("addConvertTable");
    return sbRestPath.toString();
  }
  
  public String callPostMethod() throws JAXBException {
    HttpHeaders headers = new HttpHeaders();
    Map<String, String> vars = getRequestParams();
    headers.setAccept(Arrays.asList(new MediaType[] { MediaType.APPLICATION_XML }));
    HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity(getRequestMap(), (MultiValueMap)headers);
    logger.info("Call OpenAPI method from address: " + path());
    ResponseEntity<String> responseData = this.restTemplate.exchange(path(), HttpMethod.POST, entity, String.class, vars);
    Unmarshaller jaxbUnmarshaller = createMarshaller();
    OpenAPIResponseData responseObj = (OpenAPIResponseData)jaxbUnmarshaller.unmarshal(new StringReader((String)responseData.getBody()));
    return convertResponseData(responseObj);
  }
  
  private Unmarshaller createMarshaller() throws JAXBException {
    JAXBContext jaxbContext = JAXBContext.newInstance(new Class[] { OpenAPIResponseData.class });
    Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
    return jaxbUnmarshaller;
  }
}
