package com.samsung.magicinfo.protocol.file;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.Logger;

public class FileDownloadForGetServlet extends HttpServlet {
   private static final long serialVersionUID = -4188887666725307081L;
   private static Logger logger = LoggingManagerV2.getLogger(FileLoaderServlet.class);

   public FileDownloadForGetServlet() {
      super();
   }

   public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      StrUtils ul = new StrUtils();
      String paramPathConfName = StrUtils.nvl(request.getParameter("paramPathConfName"));
      String fileoffset = request.getParameter("fileoffset");
      String filepath = StrUtils.nvl(request.getParameter("filepath"));
      if (filepath != null && !filepath.isEmpty()) {
         FileInputStream fileIS = null;
         FileChannel fileChannel = null;
         BufferedOutputStream os = null;

         try {
            int dotIdx = filepath.lastIndexOf(".");
            String topPath;
            String fullPath;
            if (dotIdx > 0) {
               topPath = filepath.substring(dotIdx + 1, filepath.length());
               fullPath = FileLoaderServlet.getContentType(topPath.toLowerCase());
               response.setContentType(fullPath);
            } else {
               response.setContentType("application/unknown");
            }

            os = new BufferedOutputStream(response.getOutputStream());
            if (!paramPathConfName.equals("CONTENTS_HOME")) {
               return;
            }

            topPath = CommonConfig.get("CONTENTS_HOME") + File.separator + "contents_home";
            fullPath = topPath + File.separator + filepath;
            fullPath = SecurityUtils.directoryTraversalChecker(fullPath, request.getRemoteAddr());
            File file = SecurityUtils.getSafeFile(fullPath);
            if (file.exists()) {
               if (file.length() <= 2147483647L) {
                  response.setContentLength((int)file.length());
               } else {
                  response.addHeader("Content-Length", Long.toString(file.length()));
               }

               fileIS = new FileInputStream(file);
               fileChannel = fileIS.getChannel();
               if (fileoffset == null) {
                  fileoffset = "0";
               }

               long fileOffsetLong = Long.parseLong(fileoffset);
               int binaryRead;
               if (file.length() > 0L) {
                  for(ByteBuffer buf = ByteBuffer.allocate(1024); (binaryRead = fileChannel.read(buf, fileOffsetLong)) != -1; fileOffsetLong += (long)binaryRead) {
                     buf.flip();
                     os.write(buf.array(), 0, binaryRead);
                     buf.clear();
                  }
               }

               return;
            }

            response.sendError(500, "Cannot find the content");
            logger.error("[MagicInfo_HTTP] download fail for GET protocal, Cannot find the content");
         } catch (Exception var37) {
            logger.error("[MagicInfo_HTTP] download fail for GET protocal");
            response.sendError(500, var37.getMessage());
            return;
         } finally {
            if (fileChannel != null) {
               try {
                  fileChannel.close();
               } catch (Exception var36) {
               }
            }

            if (fileIS != null) {
               try {
                  fileIS.close();
               } catch (Exception var35) {
               }
            }

            if (os != null) {
               try {
                  os.close();
               } catch (Exception var34) {
               }
            }

         }

      } else {
         response.sendError(500, "filepath is null");
         logger.error("[MagicInfo_HTTP] download fail for GET protocal, filepath is null");
      }
   }
}
