package com.samsung.magicinfo.webauthor2.service.transferfile;

import com.samsung.magicinfo.webauthor2.model.ContentSaveElements;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import com.samsung.magicinfo.webauthor2.util.PlayTimeUtil;
import com.samsung.magicinfo.webauthor2.util.UserData;
import com.samsung.magicinfo.webauthor2.xml.csd.CSDFileType;
import com.samsung.magicinfo.webauthor2.xml.csd.CSDTransferFileType;
import java.util.ArrayList;
import java.util.List;
import javax.inject.Inject;
import javax.xml.transform.Result;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Service;
import org.springframework.xml.transform.StringResult;

@Service
public class CSDFileXmlFactoryImpl implements CSDFileXmlFactory {
  private static Logger logger = LoggerFactory.getLogger(CSDFileXmlFactoryImpl.class);
  
  private final Jaxb2Marshaller jaxb2MarshallerForCSD;
  
  private UserData userData;
  
  private static int i = 0;
  
  @Inject
  public CSDFileXmlFactoryImpl(Jaxb2Marshaller jaxb2MarshallerForCSD, UserData userData) {
    this.jaxb2MarshallerForCSD = jaxb2MarshallerForCSD;
    this.userData = userData;
  }
  
  public String marshal(ContentSaveElements contentSaveElements, String userName) {
    StringResult result = new StringResult();
    this.jaxb2MarshallerForCSD.marshal(createCSDFile(contentSaveElements, userName), (Result)result);
    return result.toString();
  }
  
  private CSDFileType createCSDFile(ContentSaveElements elements, String userName) {
    CSDFileType csdFileType = new CSDFileType();
    csdFileType.setCid(((MediaSource)elements.getMediaSources().get(0)).getContentId());
    csdFileType.setUser(userName);
    csdFileType.setTitle(elements.getContentName());
    csdFileType.setDisplayWidth("" + elements.getWidth());
    csdFileType.setDisplayHeight("" + elements.getHeight());
    csdFileType.setPlayerType(elements.getPlayerType().getPlayerType());
    csdFileType.setPlayerTypeVersion(elements.getPlayerType().getPlayerVersion());
    csdFileType.setCategory("0");
    csdFileType.setDirName("");
    csdFileType.setMeta("");
    csdFileType.setIsShareContents("");
    csdFileType.setPromThumbailType("");
    List<CSDTransferFileType> fileList = new ArrayList<>();
    for (int i = 0; i < elements.getMediaSources().size(); i++) {
      MediaSource source = elements.getMediaSources().get(i);
      CSDTransferFileType transferFileType = populateTransferFileType(i, source);
      fileList.add(transferFileType);
    } 
    fileList.add(populateTransferFileType(elements.getMediaSources().size(), elements.getProjectThumbnailMediaSource()));
    csdFileType.setFileList(fileList);
    return csdFileType;
  }
  
  private CSDTransferFileType populateTransferFileType(int i, MediaSource source) {
    CSDTransferFileType transferFileType = new CSDTransferFileType();
    transferFileType.setType(generateFileTypeString(source.getFileType()));
    transferFileType.setSupportFileItems(source.isSupportFileItem());
    transferFileType.setReqIndex(i);
    transferFileType.setFileName(source.getFileName());
    transferFileType.setFileSize(source.getMediaSize());
    transferFileType.setPlayTime((source.getMediaDuration() > 0.0D) ? PlayTimeUtil.covertPlayTimeFromSeconds(source.getMediaDuration()) : "");
    transferFileType.setResolution((source.getMediaWidth() > 0) ? (source.getMediaWidth() + " x " + source.getMediaHeight()) : "");
    transferFileType.setStorePath(".\\");
    transferFileType.setFileId(source.getFileId());
    transferFileType.setFileHashValue(source.getFileHash());
    return transferFileType;
  }
  
  private String generateFileTypeString(String mediatype) {
    switch (mediatype) {
      case "lfd":
        return mediatype;
      case "lft":
        return mediatype;
      case "dlk":
        return mediatype;
      case "VWL":
        return mediatype;
      case "thumbnail":
        return mediatype;
      case "plugin_effect":
        return mediatype;
    } 
    return "content";
  }
  
  public String marshal(List<MediaSource> mediaSources, String contentId, DeviceType deviceType) {
    StringResult result = new StringResult();
    this.jaxb2MarshallerForCSD.marshal(createCsdFile(mediaSources, contentId, deviceType), (Result)result);
    return result.toString();
  }
  
  private CSDFileType createCsdFile(List<MediaSource> mediaSources, String contentId, DeviceType deviceType) {
    CSDFileType csdFileType = new CSDFileType();
    csdFileType.setCid(contentId);
    csdFileType.setUser(this.userData.getUserId());
    csdFileType.setTitle((((MediaSource)mediaSources.get(0)).getTitle() == null) ? FilenameUtils.getBaseName(((MediaSource)mediaSources.get(0)).getFileName()) : ((MediaSource)mediaSources.get(0)).getTitle());
    csdFileType.setDisplayWidth(Integer.toString(((MediaSource)mediaSources.get(0)).getMediaWidth()));
    csdFileType.setDisplayHeight(Integer.toString(((MediaSource)mediaSources.get(0)).getMediaHeight()));
    csdFileType.setPlayerType(deviceType.getPlayerType());
    csdFileType.setPlayerTypeVersion(deviceType.getPlayerVersion());
    csdFileType.setCategory("0");
    csdFileType.setDirName("");
    csdFileType.setMeta("");
    csdFileType.setIsShareContents("");
    csdFileType.setPromThumbailType("");
    i = 0;
    List<CSDTransferFileType> fileList = new ArrayList<>();
    for (MediaSource ms : mediaSources) {
      CSDTransferFileType transferFileType = populateTransferFileType(ms);
      fileList.add(transferFileType);
    } 
    csdFileType.setFileList(fileList);
    return csdFileType;
  }
  
  private CSDTransferFileType populateTransferFileType(MediaSource mediaSource) {
    CSDTransferFileType transferFileType = new CSDTransferFileType();
    transferFileType.setType(generateFileTypeString(mediaSource.getFileType()));
    transferFileType.setSupportFileItems(mediaSource.isSupportFileItem());
    transferFileType.setReqIndex(i);
    transferFileType.setFileName(mediaSource.getFileName());
    transferFileType.setFileSize(mediaSource.getMediaSize());
    transferFileType.setPlayTime((mediaSource.getMediaDuration() > 0.0D) ? 
        PlayTimeUtil.covertPlayTimeFromSeconds(mediaSource.getMediaDuration()) : "");
    transferFileType.setResolution((mediaSource.getMediaWidth() > 0) ? (mediaSource
        .getMediaWidth() + " x " + mediaSource.getMediaHeight()) : "");
    transferFileType.setStorePath(mediaSource.isSupportFileItem() ? mediaSource.getData() : ".\\");
    transferFileType.setFileId(mediaSource.getFileId());
    transferFileType.setFileHashValue(mediaSource.getFileHash());
    if (mediaSource.getMediaType() == MediaType.HTML) {
      if (mediaSource.getFileType().equalsIgnoreCase(MediaType.LFD.toString()))
        transferFileType.setType(MediaType.HTML.toString().toLowerCase()); 
    } else if (mediaSource.getMediaType() == MediaType.FONT && 
      mediaSource.getFileType().equalsIgnoreCase(MediaType.LFD.toString())) {
      transferFileType.setType(MediaType.FONT.toString().toLowerCase());
    } 
    i++;
    return transferFileType;
  }
}
