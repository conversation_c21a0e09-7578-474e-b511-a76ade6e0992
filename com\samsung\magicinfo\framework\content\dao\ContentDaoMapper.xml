<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.samsung.magicinfo.framework.content.dao.ContentDaoMapper">

	<sql id="getBaseFrom">
		FROM
			MI_CMS_INFO_CONTENT A,
			MI_CMS_INFO_CONTENT_VERSION B,
			MI_CMS_INFO_CONTENT_GROUP C,
			MI_CMS_MAP_GROUP_CONTENT D,
			MI_CMS_INFO_FILE E
	</sql>

    <!-- KDH RQ190703-00340 만료일자 추가 -->
	<sql id="getContentListSelect">
		SELECT DISTINCT
			A.CONTENT_ID, A.CONTENT_NAME,
			A.CONTENT_META_DATA, A.LAST_MODIFIED_DATE,
			A.IS_DELETED, <PERSON><PERSON>_FLAG,
			<PERSON>.ORGA<PERSON>ION_ID,
			<PERSON><PERSON>_INTERVAL, A.APPROVAL_STATUS,
			A.APPROVAL_OPINION,
			B.VERSION_ID, B.CREATOR_ID, B.CREATE_DATE,
			B.MEDIA_TYPE, B.TOTAL_SIZE,
			B.PLAY_TIME,
			B.RESOLUTION, B.IS_ACTIVE,
			B.MAIN_FILE_ID, B.THUMB_FILE_ID,
			B.SFI_FILE_ID,
			B.IS_LINEAR_VWL,
			B.MODEL_COUNT_INFO,
			B.SCREEN_COUNT,
			B.X_COUNT, B.Y_COUNT, B.X_RANGE,
			B.Y_RANGE,
			B.IS_STREAMING, B.IS_TEMPLATE, B.IS_PARSED,
			B.IS_USED_TEMPLATE, B.DEVICE_TYPE, B.DEVICE_TYPE_VERSION,
			B.TEMPLATE_PAGE_COUNT,
			B.MAIN_FILE_EXTENSION,
			B.VWL_VERSION,
			B.MULTI_VWL,
			B.PLAY_TIME_MILLI,
			B.HTML_START_PAGE, B.URL_ADDRESS,
			B.REFRESH_INTERVAL,
			C.GROUP_NAME,
			D.GROUP_ID,
			E.FILE_NAME AS THUMB_FILE_NAME,
			E.FILE_NAME AS MAIN_FILE_NAME,
			A.IS_IN_SHARE_FOLDER,
			A.EXPIRATION_DATE,
			B.IS_AISR
	</sql>


	<select id="getContentName" resultType="java.lang.String">
		SELECT
			CONTENT_NAME
		FROM
			MI_CMS_INFO_CONTENT
		<if test="value != null">
			WHERE CONTENT_ID = #{value}
		</if>
	</select>

	<select id="getContentOrgCreatorId"
		resultType="java.lang.String">
		SELECT
			CREATOR_ID
		FROM
			MI_CMS_INFO_CONTENT
		<if test="value != null">
			WHERE CONTENT_ID = #{value}
		</if>
	</select>

	<select id="getContentAllVerInfo"
		resultType="com.samsung.magicinfo.framework.content.entity.Content">
		<include refid="getContentListSelect" />
		<include refid="getBaseFrom" />
		WHERE
			A.CONTENT_ID = B.CONTENT_ID AND
			C.GROUP_ID = D.GROUP_ID AND
			D.CONTENT_ID = A.CONTENT_ID AND
			E.FILE_ID = B.MAIN_FILE_ID AND
			VERSION_ID > 0 AND
			A.CONTENT_ID = #{value}
			ORDER BY VERSION_ID DESC
	</select>

	<select id="getContentActiveVerVwtId" resultType="String">
		SELECT
			B.MODEL_COUNT_INFO
		<include refid="getBaseFrom" />
		WHERE
			A.CONTENT_ID = B.CONTENT_ID AND
			IS_ACTIVE = 'Y' AND
			C.GROUP_ID = D.GROUP_ID AND
			D.CONTENT_ID = A.CONTENT_ID AND
			E.FILE_ID = B.MAIN_FILE_ID AND
			A.CONTENT_ID = #{contentId}
	</select>

	<select id="getAdsContentActiveVersionInfo" resultType="map">
		SELECT
			PUBLISHER_ID, PUBLISHER_NAME, API_KEY, API_KEY_SECRET,
			AD_UNIT_ID, IMAGE_TYPE_SET, IMAGE_DURATION, VIDEO_TYPE_SET,
			VIDEO_DURATION, DEFAULT_CONTENT, DEFAULT_CONTENT_FILE_ID
		FROM
			MI_CMS_INFO_ADS_SETTING
		WHERE
			CONTENT_ID = #{contentId} AND
			IS_ACTIVE = 'Y' AND IS_DELETED = 'N'
	</select>

	<select id="getContentActiveVerInfo"
		resultType="com.samsung.magicinfo.framework.content.entity.Content">
		<include refid="getContentListSelect" />
		, F.GROUP_NAME AS ORGANIZATION_NAME
		<include refid="getBaseFrom" />
        , MI_USER_INFO_GROUP F
		WHERE
			A.CONTENT_ID = B.CONTENT_ID AND
			B.IS_ACTIVE = 'Y' AND
			C.GROUP_ID = D.GROUP_ID AND
			D.CONTENT_ID = A.CONTENT_ID AND
			E.FILE_ID = B.MAIN_FILE_ID AND
			F.GROUP_ID = A.ORGANIZATION_ID AND
			A.CONTENT_ID = #{contentId}
	</select>

	<select id="getContentActiveVerInfoTemporary"
		resultType="com.samsung.magicinfo.framework.content.entity.Content">
		<include refid="getContentListSelect" />
		<include refid="getBaseFrom" />
		WHERE
			A.CONTENT_ID = B.CONTENT_ID AND
			C.GROUP_ID = D.GROUP_ID AND
			D.CONTENT_ID = A.CONTENT_ID AND
			E.FILE_ID = B.MAIN_FILE_ID AND
			A.CONTENT_ID = #{value}
	</select>

	<select id="getThumbInfoOfActiveVersion"
		resultType="com.samsung.magicinfo.framework.content.entity.Content">
		SELECT
			A.THUMB_FILE_ID, B.FILE_NAME AS THUMB_FILE_NAME,
			IS_LINEAR_VWL, SCREEN_COUNT, X_COUNT,
			Y_COUNT, X_RANGE, Y_RANGE,
			C.CONTENT_NAME, A.MEDIA_TYPE, A.VERSION_ID, A.CREATE_DATE,
			C.LAST_MODIFIED_DATE
		FROM
			MI_CMS_INFO_CONTENT_VERSION A,
			MI_CMS_INFO_FILE B,
			MI_CMS_INFO_CONTENT C
		WHERE
			A.IS_ACTIVE = 'Y' AND
			B.FILE_ID = A.THUMB_FILE_ID
			AND A.CONTENT_ID = C.CONTENT_ID AND
			A.CONTENT_ID = #{value}
	</select>

	<select id="getThumbFileInfoOfActiveVersion" resultType="map">
		SELECT
			A.THUMB_FILE_ID AS FILE_ID, B.FILE_NAME, B.FILE_SIZE, A.MEDIA_TYPE
		FROM
			MI_CMS_INFO_CONTENT_VERSION A,
			MI_CMS_INFO_FILE B,
			MI_CMS_INFO_CONTENT C
		WHERE
			A.IS_ACTIVE = 'Y' AND
			B.FILE_ID =	A.THUMB_FILE_ID AND
			A.CONTENT_ID = C.CONTENT_ID AND
			A.CONTENT_ID = #{value}
	</select>

	<select id="getContentUseInPlaylist" resultType="map" >
		SELECT DISTINCT A.PLAYLIST_ID, B.PLAYLIST_NAME , C.ORGANIZATION
		FROM MI_CMS_MAP_PLAYLIST_CONTENT A
		LEFT JOIN MI_CMS_INFO_PLAYLIST B ON A.PLAYLIST_ID = B.PLAYLIST_ID
		LEFT JOIN  MI_USER_INFO_USER C ON B.CREATOR_ID = C.USER_ID
		WHERE A.CONTENT_ID = #{contentId}
	</select>

	<select id="getContentUseInSchedule" resultType="map" >
		SELECT DISTINCT A.PROGRAM_ID , B.PROGRAM_NAME , C.ORGANIZATION
		FROM MI_CDS_INFO_SCHEDULE A
		LEFT JOIN MI_CDS_INFO_PROGRAM B ON A.PROGRAM_ID = B.PROGRAM_ID
		LEFT JOIN  MI_USER_INFO_USER C ON B.USER_ID = C.USER_ID
		WHERE A.CONTENT_ID = #{contentId}
	</select>

	<select id="getThumbFileInfoOfActiveVersionTemporary"
		resultType="map">
		SELECT
			A.THUMB_FILE_ID AS FILE_ID, B.FILE_NAME, B.FILE_SIZE
		FROM
			MI_CMS_INFO_CONTENT_VERSION A,
			MI_CMS_INFO_FILE B,
			MI_CMS_INFO_CONTENT C
		WHERE
			B.FILE_ID = A.THUMB_FILE_ID AND
			A.CONTENT_ID = C.CONTENT_ID AND
			A.CONTENT_ID = #{value}
	</select>

	<select id="getContentVerInfo"
		resultType="com.samsung.magicinfo.framework.content.entity.Content">
		<include refid="getContentListSelect" />
		<include refid="getBaseFrom" />
		WHERE
			A.CONTENT_ID = B.CONTENT_ID AND
			C.GROUP_ID = D.GROUP_ID AND
			D.CONTENT_ID = A.CONTENT_ID AND
			E.FILE_ID = B.MAIN_FILE_ID AND
			A.CONTENT_ID = #{contentId} AND
			B.VERSION_ID = #{versionId}
	</select>

	<select id="getSearchList"
		resultType="com.samsung.magicinfo.framework.content.entity.Content">
		<include refid="getBaseSelectContentSearch" />
		<include refid="getBaseWhereContentSearch" />
		<include refid="getBaseOrderByContentSearch" />
	</select>

	<select id="getSearchListPage"
		resultType="com.samsung.magicinfo.framework.content.entity.Content">
		<include refid="getBaseSelectContentSearch" />
		<include refid="getBaseWhereContentSearch" />
		<include refid="getBaseOrderByContentSearch" />
		LIMIT #{pageSize} OFFSET #{startPos}
	</select>

	<select id="getSearchListPage"
		resultType="com.samsung.magicinfo.framework.content.entity.Content"
		databaseId="mssql">
		SELECT * FROM
		(
		<include refid="getBaseSelectContentSearch" />
		, ROW_NUMBER() OVER(
		<include refid="getBaseOrderByContentSearch" />
		) as rownum
		<include refid="getBaseWhereContentSearch" />
		) as SubQuery
		WHERE rownum > ${startPos} and rownum &lt;= ${startPos +
		pageSize}
		ORDER BY rownum
	</select>

	<select id="getSearchListCnt" resultType="int">
		SELECT COUNT(A.CONTENT_ID)
		<include refid="getBaseWhereContentSearch" />
	</select>

	<sql id="getBaseSelectContentSearch">
		SELECT
			A.CONTENT_ID, 
			VERSION_ID, 
			A.CONTENT_NAME,
			A.LAST_MODIFIED_DATE,
			B.MEDIA_TYPE, 
			B.CREATOR_ID, 
			B.CREATE_DATE,
			TOTAL_SIZE, PLAY_TIME, RESOLUTION, A.IS_DELETED, IS_ACTIVE,
			SHARE_FLAG,
			IS_LINEAR_VWL, SCREEN_COUNT,
			X_COUNT, Y_COUNT, X_RANGE,
			Y_RANGE, A.CONTENT_META_DATA , MAIN_FILE_ID,
			THUMB_FILE_ID, SFI_FILE_ID,
			FILE_NAME AS THUMB_FILE_NAME, GROUP_NAME, B.IS_STREAMING,
			MAIN_FILE_EXTENSION,
			B.IS_USED_TEMPLATE, B.TEMPLATE_PAGE_COUNT
	</sql>

	<sql id="getBaseWhereContentSearch">
		FROM
		    MI_CMS_INFO_CONTENT A,
		    MI_CMS_INFO_CONTENT_VERSION B LEFT OUTER JOIN MI_CMS_INFO_FILE E ON E.FILE_ID = B.THUMB_FILE_ID,
		    MI_CMS_INFO_CONTENT_GROUP C,
		    MI_CMS_MAP_GROUP_CONTENT D,
			MI_USER_INFO_USER F
		WHERE
			A.CONTENT_ID = B.CONTENT_ID AND
			D.GROUP_ID = C.GROUP_ID AND
			D.CONTENT_ID = A.CONTENT_ID AND
			( F.ROOT_GROUP_ID = A.ORGANIZATION_ID or exists (
						SELECT 1 FROM MI_USER_MAP_USER_MANAGE_ORG M, MI_USER_MAP_MANAGE_ORG_GROUP_MANAGE_ORG N
						WHERE 
							M.USER_ID = F.USER_ID AND	
							M.MNG_ORG_GROUP_ID = N.MNG_ORG_GROUP_ID AND
							N.ORG_GROUP_ID = A.ORGANIZATION_ID )
			) AND
			F.USER_ID =	A.CREATOR_ID AND
			IS_ACTIVE = 'Y' AND
			A.IS_DELETED = 'N' 
			AND B.MEDIA_TYPE NOT IN 
			<foreach item="item" index="index" collection="ConstMEDIA_TYPE_FOR_AUTHOR" open="(" separator="," close=")">
				#{item}
			</foreach>
			<if test="organizationId != null and !organizationId.equals('')">
				AND A.ORGANIZATION_ID = #{organizationId}
			</if>
			<if test="groupID != null and !groupID.equals('')">
				AND D.GROUP_ID = #{groupID}
			</if>
			<if test="searchText != null and searchText.length() > 0">
				<bind name="searchText" value="'%' + searchText + '%'" />
				AND (UPPER(A.CONTENT_NAME) LIKE #{searchText} ESCAPE '^' OR
				UPPER(A.CONTENT_META_DATA) LIKE #{searchText} ESCAPE '^')
			</if>
			<if test="search != null">
				<include refid="getContentSearch" />
			</if>
	</sql>

	<sql id="getContentSearch">
		<if
			test="search.getSearch_keyword() != null and search.getSearch_keyword().length() > 0">
			AND (UPPER(A.CONTENT_NAME) LIKE
			'%${search.getSearch_keyword().toUpperCase().replaceAll("_", "^_")}%'
			ESCAPE '^'
			OR UPPER(A.CONTENT_META_DATA) LIKE
			'%${search.getSearch_keyword().toUpperCase().replaceAll("_", "^_")}%'
			ESCAPE '^')
		</if>
		<if
			test="search.getSearch_group_type() != null and search.getSearch_group_type().length() > 0">
			<choose>
				<when
					test="search.getSearch_group_type().equalsIgnoreCase(searchConstGROUPED)">
					<if
						test="search.getSearch_group_name() != null and search.getSearch_group_name().length() > 0">
						AND C.GROUP_NAME = #{search.search_group_name}
					</if>
					<if
						test="search.getSearch_creator() != null and search.getSearch_creator().length() > 0">
						AND A.CREATOR_ID = #{search.search_creator}
					</if>
					<if
						test="search.getSearch_creator() == null or search.getSearch_creator().length() == 0">
						AND A.CREATOR_ID = #{search.creator_id}
					</if>
				</when>
				<when
					test="search.getSearch_group_type().equalsIgnoreCase(searchConstUNGROUPED)">
					AND D.GROUP_ID = #{searchRootId}
					<if
						test="search.getSearch_creator() != null and search.getSearch_creator().length() > 0">
						AND A.CREATOR_ID = #{search.search_creator}
					</if>
					<if
						test="search.getSearch_creator() == null or search.getSearch_creator().length() == 0">
						AND A.CREATOR_ID = #{search.creator_id}
					</if>
				</when>
				<when
					test="search.getSearch_group_type().equalsIgnoreCase(searchConstSHARED)">
					<if
						test="search.getSearch_creator() != null and search.getSearch_creator().length() == 0">
						AND (A.SHARE_FLAG = #{searchConstSHARE_FLAG_DEFAULT} OR
						A.CREATOR_ID = #{search.creator_id})
					</if>
					<if
						test="search.getSearch_creator().equals(search.getCreator_id())">
						AND A.CREATOR_ID = #{search.search_creator}
					</if>
					<if
						test="search.getSearch_creator() != null and search.getSearch_creator().length() > 0 and !search.getSearch_creator().equals(search.getCreator_id())">
						AND A.SHARE_FLAG = #{searchConstSHARE_FLAG_DEFAULT} AND
						A.CREATOR_ID = #{search.search_creator}
					</if>
					<if
						test="search.getSearch_creator() != null and search.getSearch_creator().length() > 0">
						AND A.CREATOR_ID LIKE
						'%${search.getSearch_creator().replaceAll("_", "^_")}%' ESCAPE '^'
					</if>
				</when>
				<when
					test="search.getSearch_group_type().equalsIgnoreCase(searchConstORGAN)">
					<if
						test="search.getSearch_creator() != null and search.getSearch_creator().length() > 0">
						AND A.CREATOR_ID LIKE
						'%${search.getSearch_creator().replaceAll("_", "^_")}%' ESCAPE '^'
					</if>
				</when>
				<otherwise>
					<if
						test="search.getSearch_creator() != null and search.getSearch_creator().length() > 0">
						AND A.CREATOR_ID = #{search.search_creator}
					</if>
					<if
						test="search.getSearch_creator() == null or search.getSearch_creator().length() == 0">
						AND A.CREATOR_ID = #{search.creator_id}
					</if>
				</otherwise>
			</choose>
		</if>
		<if
			test="search.getSearch_media_type() != null and search.getSearch_media_type().length() > 0">
			AND B.MEDIA_TYPE = #{search.search_media_type}
		</if>
		<if test="search.search_media_type == ''">
			AND B.MEDIA_TYPE !=
			#{searchConstMEDIA_TYPE_TEMPLATE_EXTENSION}
		</if>
		<if test="search.getSearch_start_date() != null">
			<bind name="safe_startDate"
				value="@com.samsung.common.utils.DaoTools@safeDateTimeString(searchStartDate)" />
			AND A.LAST_MODIFIED_DATE &gt; '${safe_startDate}'
		</if>
		<if test="search.getSearch_end_date() != null">
			<bind name="safe_endDate"
				value="@com.samsung.common.utils.DaoTools@safeDateTimeString(searchEndDate)" />
			AND A.LAST_MODIFIED_DATE &lt; '${safe_endDate}'
		</if>
	</sql>

	<sql id="getBaseOrderByContentSearch">
		<choose>
			<when test="sortColumn != null and sortColumn.length() > 0">
				<choose>
					<when test="sortColumn.equalsIgnoreCase('CREATOR_ID')">
						ORDER BY B.CREATOR_ID
					</when>
					<when test="sortColumn.equalsIgnoreCase('TOTAL_SIZE')">
						ORDER BY B.TOTAL_SIZE
					</when>
					<otherwise>
						ORDER BY ${sortColumn}
					</otherwise>
				</choose>
				<if test="sortOrder != null and sortOrder.length() > 0">
					<bind name="safe_sortOrder"
						value="@com.samsung.common.utils.DaoTools@safeSortOrder(sortOrder)" />
					${safe_sortOrder}
				</if>
			</when>
			<otherwise>
				ORDER BY A.LAST_MODIFIED_DATE DESC
			</otherwise>
		</choose>
		</sql>

	<select id="getAllDeletedContentListWithoutCreatorId"
		resultType="map">
		SELECT
		CONTENT_ID
		FROM
		MI_CMS_INFO_CONTENT
		WHERE
		IS_DELETED = 'Y'

		<choose>
			<when test="organizationId != null and organizationId > 0">
				AND ORGANIZATION_ID = #{organizationId}
			</when>
			<otherwise>
				<if
					test="userManageGroupList != null and userManageGroupList.size() > 0">
					AND ORGANIZATION_ID IN
					<foreach item="group" collection="userManageGroupList"
						open=" (" separator="," close=")">
						#{group.group_id}
					</foreach>
				</if>
			</otherwise>
		</choose>
	</select>
	<select id="getAllDeletedContentList" resultType="map">
		SELECT
		CONTENT_ID
		FROM
		MI_CMS_INFO_CONTENT
		WHERE
			IS_DELETED = 'Y' 
			AND CREATOR_ID = #{creatorId} 
			AND ORGANIZATION_ID = #{organizationId}
	</select>

	<select id="getAllContentList" resultType="map">
		SELECT
			A.CONTENT_ID
		FROM
			MI_CMS_INFO_CONTENT A,
			MI_CMS_MAP_GROUP_CONTENT B,
			MI_CMS_INFO_CONTENT_VERSION C
		WHERE
			A.CONTENT_ID = B.CONTENT_ID AND
			A.CONTENT_ID = C.CONTENT_ID AND C.IS_ACTIVE = 'Y' AND 
			A.IS_DELETED = 'N' 
			AND A.CREATOR_ID = #{creatorId} AND
			A.ORGANIZATION_ID = #{organizationId}
			<if test="mediaType != null and mediaType.length() > 0">
				AND C.MEDIA_TYPE = #{mediaType}
				<if test="!mediaType.equalsIgnoreCase(mediaTypeTemplate)">
					AND C.IS_TEMPLATE = 'N'
				</if>
			</if>
			<if test="groupId != null">
				AND GROUP_ID = #{groupId}
			</if>
	</select>

	<select id="getContentList"
		resultType="com.samsung.magicinfo.framework.content.entity.Content">
		<include refid="getContentListSelect" />
		<include refid="getContentListFrom" />
		<include refid="getContentListWhere" />		
		<include refid="getContentListFilter" />
		<include refid="getTagAndCategoryFilterForPostgreSQL" />
	</select>

	<select id="getContentList"
			resultType="com.samsung.magicinfo.framework.content.entity.Content"
			databaseId="mssql">
		<include refid="getContentListSelect" />
		<include refid="getContentListFrom" />
		<include refid="getContentListWhere" />
		<include refid="getContentListFilter" />
		<include refid="getTagAndCategoryFilterForMSSQL" />
	</select>

	<sql id="getContentListFrom">
		FROM
			MI_CMS_INFO_CONTENT A LEFT OUTER JOIN MI_CMS_MAP_SHARE_FOLDER_CONTENT Y ON A.CONTENT_ID = Y.CONTENT_ID
			<if
				test="contentUsingStatusFilter != null and (contentUsingStatusFilter.equalsIgnoreCase('used_content') or contentUsingStatusFilter.equalsIgnoreCase('unused_content'))">
				LEFT OUTER JOIN (
					SELECT CONTENT_ID FROM
					MI_CMS_MAP_PLAYLIST_CONTENT
					UNION SELECT CONTENT_ID FROM
					MI_CMS_MAP_DLKCONTENT_LFDCONTENT
					UNION SELECT TO_DATA AS CONTENT_ID
					FROM MI_CMS_MAP_CONVERT_DATA_LIST
					UNION SELECT CONTENT_ID FROM
					MI_EVENT_INFO_CONDITION
					UNION SELECT BGM_CONTENT_ID AS CONTENT_ID FROM
					MI_CDS_INFO_PROGRAM
					UNION SELECT DEFAULT_CONTENT_ID AS CONTENT_ID FROM
					MI_CDS_INFO_FRAME
					UNION SELECT CONTENT_ID FROM MI_CDS_INFO_SCHEDULE
					WHERE CONTENT_TYPE !='PLAYLIST'
					UNION SELECT CONTENTS_ID AS CONTENT_ID FROM
					MI_RULE_MAP_RULESET_RESULT X JOIN MI_RULE_MAP_RESULT_CONTENT Z ON X.RESULT_ID = Z.RESULT_ID
					) K
					ON A.CONTENT_ID = K.CONTENT_ID
			</if>
			,
            MI_CMS_INFO_CONTENT_VERSION B LEFT OUTER JOIN MI_CMS_INFO_FILE E ON E.FILE_ID = B.THUMB_FILE_ID,
        <choose>
				<when test="isTLFD != null and isTLFD.equalsIgnoreCase(true)">
					MI_CMS_INFO_TLFD_GROUP C,
					MI_CMS_MAP_GROUP_TLFD D,
				</when>
				<otherwise>
					MI_CMS_INFO_CONTENT_GROUP C,
					MI_CMS_MAP_GROUP_CONTENT D,
				</otherwise>
			</choose>
	
			MI_USER_INFO_USER F
			<if
				test="isMultiApproval != null and isMultiApproval and listType.equalsIgnoreCase(ConstGROUP_TYPE_SUBMITTED)">
				, MI_CMS_MAP_CONTENT_APPROVER H
			</if>

		
	</sql>

	<sql id="getContentListDeviceFrom">
		FROM
			MI_CMS_INFO_CONTENT A LEFT OUTER JOIN MI_CMS_MAP_SHARE_FOLDER_CONTENT Y ON A.CONTENT_ID = Y.CONTENT_ID
			
			<if
			test="contentUsingStatusFilter != null and (contentUsingStatusFilter.equalsIgnoreCase('used_content') or contentUsingStatusFilter.equalsIgnoreCase('unused_content'))">
				LEFT OUTER JOIN (
				SELECT CONTENT_ID FROM
				MI_CMS_MAP_PLAYLIST_CONTENT
				UNION SELECT CONTENT_ID FROM
				MI_CMS_MAP_DLKCONTENT_LFDCONTENT
				UNION SELECT TO_DATA AS CONTENT_ID
				FROM MI_CMS_MAP_CONVERT_DATA_LIST
				UNION SELECT CONTENT_ID FROM
				MI_EVENT_INFO_CONDITION
				UNION SELECT BGM_CONTENT_ID AS CONTENT_ID FROM
				MI_CDS_INFO_PROGRAM
				UNION SELECT DEFAULT_CONTENT_ID AS CONTENT_ID FROM
				MI_CDS_INFO_FRAME
				UNION SELECT CONTENT_ID FROM MI_CDS_INFO_SCHEDULE
				WHERE CONTENT_TYPE !='PLAYLIST'
				UNION SELECT CONTENTS_ID AS CONTENT_ID FROM
				MI_RULE_MAP_RULESET_RESULT X JOIN MI_RULE_MAP_RESULT_CONTENT Z ON X.RESULT_ID = Z.RESULT_ID
				) K
				ON A.CONTENT_ID = K.CONTENT_ID
			</if>
			,
            MI_CMS_INFO_CONTENT_VERSION B LEFT OUTER JOIN MI_CMS_INFO_FILE E ON E.FILE_ID = B.THUMB_FILE_ID,
			MI_CMS_INFO_CONTENT_GROUP C,
			MI_CMS_MAP_GROUP_CONTENT D,

		<choose>
			<when test="listType != null and listType.equalsIgnoreCase(ConstGROUP_TYPE_DELETED)">
				MI_USER_INFO_USER F
			</when>
			<otherwise>
				MI_USER_INFO_USER F,
				MI_CMS_MAP_FILE G
				<if
					test="isMultiApproval != null and isMultiApproval and listType.equalsIgnoreCase(ConstGROUP_TYPE_SUBMITTED)">
					, MI_CMS_MAP_CONTENT_APPROVER H
				</if>
			</otherwise>
		</choose>
	</sql>

	<sql id="getContentListWhere">
		WHERE
		A.CONTENT_ID = B.CONTENT_ID AND
		C.GROUP_ID = D.GROUP_ID AND
		D.CONTENT_ID = A.CONTENT_ID AND
		F.USER_ID = A.CREATOR_ID AND
		B.IS_ACTIVE = 'Y' AND

		<!-- KDH RQ190703-00340 플레이리스트 컨텐츠 조회 알림 S -->
        <if test="source != null and source.length() > 0">
        A.EXPIRATION_DATE <![CDATA[>=]]> #{curDate2} AND
        </if>
        <!-- KDH RQ190703-00340 플레이리스트 컨텐츠 조회 알림 E -->

		( F.ROOT_GROUP_ID = A.ORGANIZATION_ID or exists (				
		SELECT 1 FROM MI_USER_MAP_USER_MANAGE_ORG M, MI_USER_MAP_MANAGE_ORG_GROUP_MANAGE_ORG N
		WHERE 
			M.USER_ID = F.USER_ID AND	
			M.MNG_ORG_GROUP_ID = N.MNG_ORG_GROUP_ID AND
			N.ORG_GROUP_ID = A.ORGANIZATION_ID ))
			
		<choose>
			<when
				test="listType.equalsIgnoreCase(ConstGROUP_TYPE_DELETED) or myContentDelete == true">
				AND A.IS_DELETED ='Y'
			</when>
			<otherwise>
				AND A.IS_DELETED ='N'
			</otherwise>
		</choose>
		<if test="isContentApprove != null and isContentApprove">
			<choose>
				<when
					test="approvalFilter != null and !approvalFilter.equals('')">
					<if test="approvalFilter.equals('approval_content')">
						AND A.APPROVAL_STATUS = 'APPROVED'
					</if>
					<if test="approvalFilter.equals('unapproval_content')">
						AND A.APPROVAL_STATUS = 'UNAPPROVED'
					</if>
					<if test="approvalFilter.equals('reject_content')">
						AND A.APPROVAL_STATUS = 'REJECTED'
					</if>
				</when>
				<otherwise>
					AND (A.APPROVAL_STATUS = #{ConstAPPROVED} OR A.CREATOR_ID
					= #{creatorID})
				</otherwise>
			</choose>
		</if>
		<if test="listType.equalsIgnoreCase(ConstGROUP_TYPE_SUBMITTED)">
			<if test="contentApprovalFilter == null or contentApprovalFilter.equals('')">
				AND (A.APPROVAL_STATUS = #{ConstUNAPPROVED} OR A.APPROVAL_STATUS =
				#{ConstREJECTED})
			</if>
			<if test="isMultiApproval != null and isMultiApproval and !'reject_content'.equals(contentApprovalFilter)">
				AND H.CONTENT_ID = A.CONTENT_ID
			</if>
			<choose>
				<when test="isServerAdmin != null and isServerAdmin == true">
				</when>
				<otherwise>
					<if test="!creatorID.equals('admin')">
						AND H.USER_ID = #{creatorID}
					</if>
				</otherwise>
			</choose>
		</if>
		<if test="approval_status!=null">
			AND A.APPROVAL_STATUS = #{approval_status}
		</if>
		<choose>
			<when
				test="contentApprovalFilter != null and !contentApprovalFilter.equals('')">
				<if test="contentApprovalFilter.equals('approval_content')">
					AND A.APPROVAL_STATUS = 'APPROVED'
				</if>
				<if test="contentApprovalFilter.equals('unapproval_content')">
					AND A.APPROVAL_STATUS = 'UNAPPROVED'
				</if>
				<if test="contentApprovalFilter.equals('reject_content')">
					AND A.APPROVAL_STATUS = 'REJECTED'
				</if>
			</when>
			<!-- <otherwise> AND (A.APPROVAL_STATUS = #{ConstAPPROVED} OR A.CREATOR_ID 
				= #{creatorID}) </otherwise> -->
		</choose>
		<if test="media_type_filter == null">
			AND B.MEDIA_TYPE NOT IN 
			<foreach item="item" index="index" collection="ConstMEDIA_TYPE_FOR_AUTHOR" open="(" separator="," close=")">
				#{item}
			</foreach>	
		</if>
		<if test="listType.equalsIgnoreCase(ConstGROUP_TYPE_SHAREFOLDER)">
			AND Y.share_folder_id = #{share_folder_id}
			AND A.content_id = Y.content_id
		</if>
	</sql>

	<sql id="getContentListDeviceWhere">
		WHERE
		<choose>
			<when test="listType.equalsIgnoreCase(ConstGROUP_TYPE_DELETED)">
				A.CONTENT_ID = B.CONTENT_ID AND
				C.GROUP_ID = D.GROUP_ID AND
				D.CONTENT_ID = A.CONTENT_ID AND
				( F.ROOT_GROUP_ID = A.ORGANIZATION_ID or exists (
					SELECT 1 FROM MI_USER_MAP_USER_MANAGE_ORG M, MI_USER_MAP_MANAGE_ORG_GROUP_MANAGE_ORG N
					WHERE 
						M.USER_ID = F.USER_ID AND	
						M.MNG_ORG_GROUP_ID = N.MNG_ORG_GROUP_ID AND
						N.ORG_GROUP_ID = A.ORGANIZATION_ID )
				) AND
				F.USER_ID =	A.CREATOR_ID AND
				IS_ACTIVE = 'Y' AND
				A.IS_DELETED ='Y'
			</when>
			<otherwise>
				A.CONTENT_ID = B.CONTENT_ID AND
				C.GROUP_ID = D.GROUP_ID AND
				D.CONTENT_ID = A.CONTENT_ID AND
				( F.ROOT_GROUP_ID = A.ORGANIZATION_ID or exists (
					SELECT 1 FROM MI_USER_MAP_USER_MANAGE_ORG M, MI_USER_MAP_MANAGE_ORG_GROUP_MANAGE_ORG N
					WHERE 
						M.USER_ID = F.USER_ID AND	
						M.MNG_ORG_GROUP_ID = N.MNG_ORG_GROUP_ID AND
						N.ORG_GROUP_ID = A.ORGANIZATION_ID )
				) AND
				F.USER_ID = A.CREATOR_ID AND
				IS_ACTIVE = 'Y' AND
				A.IS_DELETED ='N'
			
				<if test="deviceTypeArr != null">
		        AND B.MAIN_FILE_EXTENSION = G.FILE_TYPE  AND 
		            <foreach item="deviceType" collection="deviceTypeArr" separator=" OR " open="(" close=")">
		                <choose>
		                	<when test="deviceType == ConstTYPE_SOC">
		                		(G.DEVICE_TYPE = #{ConstTYPE_SOC} AND G.DEVICE_TYPE_VERSION = #{TYPE_VERSION_1_0}) 
		                	</when>
		                	<when test="deviceType == ConstTYPE_SOC2">
		                		(G.DEVICE_TYPE = #{ConstTYPE_SOC} AND G.DEVICE_TYPE_VERSION = #{TYPE_VERSION_2_0}) 
		                	</when>
		                	<when test="deviceType == ConstTYPE_SOC3">
		                		(G.DEVICE_TYPE = #{ConstTYPE_SOC} AND G.DEVICE_TYPE_VERSION = #{TYPE_VERSION_3_0}) 
		                	</when>
		                	<when test="deviceType == ConstTYPE_SOC4">
		                		(G.DEVICE_TYPE = #{ConstTYPE_SOC} AND G.DEVICE_TYPE_VERSION = #{TYPE_VERSION_4_0}) 
		                	</when>
		                	<when test="deviceType == ConstTYPE_SOC5">
		                		(G.DEVICE_TYPE = #{ConstTYPE_SOC} AND G.DEVICE_TYPE_VERSION = #{TYPE_VERSION_5_0}) 
		                	</when>
		                	<when test="deviceType == ConstTYPE_SOC6">
		                		(G.DEVICE_TYPE = #{ConstTYPE_SOC} AND G.DEVICE_TYPE_VERSION = #{TYPE_VERSION_6_0}) 
		                	</when>
		                	<when test="deviceType == ConstTYPE_SOC7">
		                		(G.DEVICE_TYPE = #{ConstTYPE_SOC} AND G.DEVICE_TYPE_VERSION = #{TYPE_VERSION_7_0}) 
		                	</when>
		                	<when test="deviceType == ConstTYPE_SOC9">
		                		(G.DEVICE_TYPE = #{ConstTYPE_SOC} AND G.DEVICE_TYPE_VERSION = #{TYPE_VERSION_9_0})
		                	</when>
		                	<when test="deviceType == ConstTYPE_SOC10">
		                		(G.DEVICE_TYPE = #{ConstTYPE_SOC} AND G.DEVICE_TYPE_VERSION = #{TYPE_VERSION_10_0})
		                	</when>
		                	<when test="deviceType == ConstTYPE_PREMIUM">
		                		( G.DEVICE_TYPE = #{deviceType} ) 
		                	</when>
		                	<otherwise>
		                        ( G.DEVICE_TYPE = #{deviceType} ) 
		                    </otherwise>
		                </choose>
		            </foreach>
		       </if>
			</otherwise>
		</choose>
		<if test="isContentApprove != null and isContentApprove">
			<choose>
				<when
					test="approvalFilter != null and !approvalFilter.equals('')">
					<if test="approvalFilter.equals('approval_content')">
						AND A.APPROVAL_STATUS = 'APPROVED'
					</if>
					<if test="approvalFilter.equals('unapproval_content')">
						AND A.APPROVAL_STATUS = 'UNAPPROVED'
					</if>
				</when>
				<otherwise>
					AND (A.APPROVAL_STATUS = #{ConstAPPROVED} OR A.CREATOR_ID
					= #{creatorID})
				</otherwise>
			</choose>
		</if>
		<if test="listType.equalsIgnoreCase(ConstGROUP_TYPE_SUBMITTED)">
			AND (A.APPROVAL_STATUS = #{ConstUNAPPROVED} OR A.APPROVAL_STATUS =
			#{ConstREJECTED})
			<if test="isMultiApproval != null and isMultiApproval">
				AND H.CONTENT_ID = A.CONTENT_ID
			</if>
			<choose>
				<when test="isServerAdmin != null and isServerAdmin == true">
				</when>
				<otherwise>
					<if test="!creatorID.equals('admin')">
						AND H.USER_ID = #{creatorID}
					</if>
				</otherwise>
			</choose>
		</if>
		<if test="approval_status!=null">
			AND A.APPROVAL_STATUS = #{approval_status}
		</if>

		<choose>
			<when
				test="contentApprovalFilter != null and !contentApprovalFilter.equals('')">
				<if test="contentApprovalFilter.equals('approval_content')">
					AND A.APPROVAL_STATUS = 'APPROVED'
				</if>
				<if test="contentApprovalFilter.equals('unapproval_content')">
					AND A.APPROVAL_STATUS = 'UNAPPROVED'
				</if>
			</when>
			<otherwise>
				AND (A.APPROVAL_STATUS = #{ConstAPPROVED} OR A.CREATOR_ID
				= #{creatorID})
			</otherwise>
		</choose>
		<if test="media_type_filter == null">
			AND B.MEDIA_TYPE NOT IN 
			<foreach item="item" index="index" collection="ConstMEDIA_TYPE_FOR_AUTHOR" open="(" separator="," close=")">
      			  #{item}
			</foreach>	
		</if>
        <if test="listType.equalsIgnoreCase(ConstGROUP_TYPE_SHAREFOLDER)">
            AND Y.share_folder_id = #{share_folder_id}
            AND A.content_id = Y.content_id
        </if>
		<!-- KDH RQ190703-00340 플레이리스트 컨텐츠 조회 알림 S -->
		<if test="source != null and source.length() > 0">
			AND A.EXPIRATION_DATE <![CDATA[>=]]> #{curDate2}
		</if>
		<!-- KDH RQ190703-00340 플레이리스트 컨텐츠 조회 알림 E -->
	</sql>

	<sql id="getContentListWhereOrgan">
		<if test="whereOrganMap != null">
			<foreach item="item" index="key" collection="whereOrganMap"
				open=" AND ((" separator=") OR (" close="))">
				A.CREATOR_ID = #{key}
				<if test="item != null">
					AND A.SHARE_FLAG = #{item}
				</if>
			</foreach>
		</if>
	</sql>

	<select id="getContentListPage"
		resultType="com.samsung.magicinfo.framework.content.entity.Content">
		<include refid="getContentListSelect" />
		<include refid="getContentListFromWhere" />
		<include refid="getTagAndCategoryFilterForPostgreSQL" />
		<if test="isMediaTypeFilter == null">
			<include refid="getBaseOrderByContentSearch" />
		</if>
		LIMIT #{pageSize} OFFSET #{startPos}
	</select>

    <!-- KDH RQ190703-00340 MSSQL 쿼리에 EXPIRATION_DATE 추가 -->
	<select id="getContentListPage"
		resultType="com.samsung.magicinfo.framework.content.entity.Content"
		databaseId="mssql">
		SELECT * FROM
		(
		<include refid="getContentListSelect" />
		,
		ROW_NUMBER() OVER(
		<include refid="getBaseOrderByContentSearch" />
		) as rownum
		<include refid="getContentListFromWhere" />
		<include refid="getTagAndCategoryFilterForMSSQL" />
		GROUP BY
			A.CONTENT_ID, A.CONTENT_NAME,
			A.CONTENT_META_DATA, A.LAST_MODIFIED_DATE,
			A.IS_DELETED, A.SHARE_FLAG,
			A.ORGANIZATION_ID,
			A.POLLING_INTERVAL, A.APPROVAL_STATUS,
			A.APPROVAL_OPINION,
			B.VERSION_ID, B.CREATOR_ID, B.CREATE_DATE,
			B.MEDIA_TYPE, B.TOTAL_SIZE,
			B.PLAY_TIME,
			B.RESOLUTION, B.IS_ACTIVE,
			B.MAIN_FILE_ID, B.THUMB_FILE_ID,
			B.SFI_FILE_ID,
			B.IS_LINEAR_VWL,
			B.MODEL_COUNT_INFO,
			B.SCREEN_COUNT,
			B.X_COUNT, B.Y_COUNT, B.X_RANGE,
			B.Y_RANGE,
			B.IS_STREAMING, B.IS_TEMPLATE, B.IS_PARSED,
			B.IS_USED_TEMPLATE, B.DEVICE_TYPE, B.DEVICE_TYPE_VERSION,
			B.TEMPLATE_PAGE_COUNT,
			B.MAIN_FILE_EXTENSION,
			B.VWL_VERSION,
			B.MULTI_VWL,
			B.PLAY_TIME_MILLI,
			B.HTML_START_PAGE, B.URL_ADDRESS,
			B.REFRESH_INTERVAL,
			C.GROUP_NAME,
			D.GROUP_ID,
			E.FILE_NAME,
			A.IS_IN_SHARE_FOLDER,
			A.EXPIRATION_DATE,
			B.IS_AISR
		) as SubQuery
		WHERE rownum > ${startPos}
		and rownum &lt;= ${startPos + pageSize}
		ORDER BY rownum
	</select>

	<select id="getContentListCnt" resultType="int">
		SELECT COUNT(DISTINCT A.CONTENT_ID)
		<include refid="getContentListFromWhere" />
		<include refid="getTagAndCategoryFilterForPostgreSQL" />
	</select>

	<select id="getContentListCnt" resultType="int" databaseId="mssql">
		SELECT COUNT(DISTINCT A.CONTENT_ID)
		<include refid="getContentListFromWhere" />
		<include refid="getTagAndCategoryFilterForMSSQL" />
	</select>

	<sql id="getContentListFromWhere">
		<choose>
			<when test="deviceTypeArr != null" >
				<include refid="getContentListDeviceFrom" />
				<include refid="getContentListDeviceWhere" />
			</when>
			<otherwise>
				<include refid="getContentListFrom" />
				<include refid="getContentListWhere" />
			</otherwise>
		</choose>
		<include refid="getContentListFilter" />
	</sql>
	
	<sql id="getContentListFilter">
	
		<choose>
			<when test="organizationId != null">
				AND ( A.ORGANIZATION_ID = #{organizationId} OR exists (				
				SELECT 1 FROM MI_CMS_MAP_SHARE_FOLDER_CONTENT Q, MI_CMS_MAP_SHARE_FOLDER_ROOT_GROUP R
				WHERE 
					Q.SHARE_FOLDER_ID = R.SHARE_FOLDER_ID AND	
					R.ORG_GROUP_ID = #{organizationId} AND
					A.CONTENT_ID = Q.CONTENT_ID ))
			</when>
		</choose>
		
		<choose>
			<when
				test="listType.equalsIgnoreCase(ConstGROUP_TYPE_UNGROUPED)">
				AND D.GROUP_ID = #{groupRootId}

			</when>
			<when test="listType.equalsIgnoreCase(ConstGROUP_TYPE_GROUPED)">
				<if test="groupIDLong != null">
					AND D.GROUP_ID = #{groupIDLong}					
				</if>
				<if test="groupIds != null">
					<foreach item="item" index="index" collection="groupIds"
						open=" AND (" separator=" OR " close=")">
						D.GROUP_ID = #{item}
					</foreach>
				</if>
			</when>
			<when test="listType.equalsIgnoreCase(ConstGROUP_TYPE_ORGAN)">
				<include refid="getContentListWhereOrgan" />
			</when>
		</choose>

		<if test="listType.equalsIgnoreCase(ConstGROUP_TYPE_USER)">
			AND A.CREATOR_ID = #{creatorID}
		</if>

		<if
			test="isSelectContent != null and isSelectContent.equalsIgnoreCase('TRUE') and selId != null and selId.length() > 0 and !selId.equalsIgnoreCase('null')">
			AND A.CONTENT_ID = #{selId}
		</if>
		<if test="searchText != null and searchText.length() > 0">
			<bind name="searchText" value="'%' + searchText + '%'" />
			AND (UPPER(A.CONTENT_NAME) LIKE #{searchText} ESCAPE '^')
		</if>
		<if test="searchCreator != null and searchCreator.length() > 0">
			<bind name="searchCreator" value="'%' + searchCreator + '%'" />
			AND (UPPER(B.CREATOR_ID) LIKE #{searchCreator} ESCAPE '^')
		</if>
		<if test="media_type_filter != null">
			<foreach item="item" index="index"
				collection="media_type_filter" open=" AND (" separator=" OR "
				close=")">
				B.MEDIA_TYPE = #{item}
			</foreach>
		</if>
		<if test="media_type_filter == null">
			AND B.MEDIA_TYPE NOT IN 
			<foreach item="item" index="index" collection="ConstMEDIA_TYPE_FOR_AUTHOR" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="searchStartDate != null and searchStartDate.length() > 0">
			<bind name="safe_startDate"
				value="@com.samsung.common.utils.DaoTools@safeDateTimeString(searchStartDate)" />
			AND A.LAST_MODIFIED_DATE &gt; '${safe_startDate}'
		</if>
		<if test="searchEndDate != null and searchEndDate.length() > 0">
			<bind name="safe_endDate"
				value="@com.samsung.common.utils.DaoTools@safeDateTimeString(searchEndDate)" />
			AND A.LAST_MODIFIED_DATE &lt; '${safe_endDate}'
		</if>
		<if test="createdDateFrom != null and createdDateFrom.length() > 0">
			<bind name="safe_createdDateFrom"
				  value="@com.samsung.common.utils.DaoTools@safeDateTimeString(createdDateFrom)" />
			AND B.CREATE_DATE &gt; '${safe_createdDateFrom}'
		</if>
		<if test="createdDateTo != null and createdDateTo.length() > 0">
			<bind name="safe_createdDateTo"
				  value="@com.samsung.common.utils.DaoTools@safeDateTimeString(createdDateTo)" />
			AND B.CREATE_DATE &lt; '${safe_createdDateTo}'
		</if>
		<choose>
			<when test="mediaType != null and mediaType.length() > 0">
				<choose>
					<when test="mediaType.equalsIgnoreCase('VISUAL')">
						<if
							test="deviceType != null and !deviceType.equalsIgnoreCase(ConstTYPE_PREMIUM)">
							AND B.MEDIA_TYPE != #{ConstMEDIA_TYPE_SOUND}
						</if>
						AND B.MEDIA_TYPE != #{ConstMEDIA_TYPE_VWL} AND B.MEDIA_TYPE !=
						#{ConstMEDIA_TYPE_TEMPLATE_EXTENSION}
						<if
							test="deviceType != null and deviceType.equalsIgnoreCase(ConstTYPE_SOC) and is_multi_frame.equalsIgnoreCase('true')">
							AND B.MEDIA_TYPE != #{ConstMEDIA_TYPE_LFD} AND B.MEDIA_TYPE
							!= #{ConstMEDIA_TYPE_DLK}
						</if>
					</when>
					<when test="mediaType.equalsIgnoreCase('IMAGE')">
						AND B.MEDIA_TYPE = #{ConstMEDIA_TYPE_IMAGE}
					</when>
					<when test="mediaType.equalsIgnoreCase('MEDIASLIDE')">
						<choose>
							<when
								test="content_type != null and content_type.equalsIgnoreCase('TEMPLATE_CONTENT')">
								AND (
								B.MEDIA_TYPE = #{ConstMEDIA_TYPE_IMAGE} OR
								B.MEDIA_TYPE =
								#{ConstMEDIA_TYPE_MOVIE}
								OR B.MEDIA_TYPE =
								#{ConstMEDIA_TYPE_FLASH} OR B.MEDIA_TYPE =
								#{ConstMEDIA_TYPE_PDF}
								OR B.MEDIA_TYPE =
								#{ConstMEDIA_TYPE_OFFICE} OR B.MEDIA_TYPE =
								#{ConstMEDIA_TYPE_FTP}
								OR B.MEDIA_TYPE = #{ConstMEDIA_TYPE_CIFS}
								)
							</when>
							<otherwise>
								AND (
								B.MEDIA_TYPE = #{ConstMEDIA_TYPE_FTP} OR
								B.MEDIA_TYPE =
								#{ConstMEDIA_TYPE_CIFS}
								)
							</otherwise>
						</choose>
					</when>
					<when test="mediaType.equalsIgnoreCase('VIDEO')">
						<choose>
							<when
								test="content_type != null and content_type.equalsIgnoreCase('TEMPLATE_CONTENT')">
								AND B.MEDIA_TYPE = #{ConstMEDIA_TYPE_MOVIE}
							</when>
							<otherwise>
								AND ( B.MEDIA_TYPE = #{ConstMEDIA_TYPE_MOVIE} OR
								B.MEDIA_TYPE = #{ConstMEDIA_TYPE_FLASH} )
							</otherwise>
						</choose>
					</when>
					<when test="mediaType.equalsIgnoreCase('UVM')">
						AND ( B.MEDIA_TYPE = #{ConstMEDIA_TYPE_IMAGE} OR
						B.MEDIA_TYPE = #{ConstMEDIA_TYPE_FLASH} OR (B.MEDIA_TYPE =
						#{ConstMEDIA_TYPE_MOVIE} AND MAIN_FILE_EXTENSION = 'FLV') )
					</when>
					<when test="mediaType.equalsIgnoreCase('FLV')">
						AND ( B.MEDIA_TYPE = #{ConstMEDIA_TYPE_MOVIE} AND
						MAIN_FILE_EXTENSION = 'FLV' )
					</when>
					<when test="mediaType.equalsIgnoreCase('VWL')">
						<if test="screen_count != null">
							AND SCREEN_COUNT = #{screen_count}
						</if>
						<if test="is_linear != null and is_linear.length() > 0">
							<choose>
								<when test="is_linear.equals('true')">
									AND IS_LINEAR_VWL = 'Y'
								</when>
								<otherwise>
									AND IS_LINEAR_VWL = 'N'
								</otherwise>
							</choose>
						</if>
						<if test="x_count != null">
							AND X_COUNT = #{x_count}
						</if>
						<if test="y_count != null">
							AND Y_COUNT = #{y_count}
						</if>
						<if test="x_range != null">
							AND X_RANGE = #{x_range}
						</if>
						<if test="y_range != null">
							AND Y_RANGE = #{y_range}
						</if>
					</when>
					<when test="mediaType.equalsIgnoreCase('STRM')">
						AND B.MEDIA_TYPE = #{ConstMEDIA_TYPE_STRM}
					</when>
					<when test="mediaType.equalsIgnoreCase('PLAYLIST')">
						<choose>
							<when test="playlist_type == ConstPLAYLIST_TYPE_VWL">
								AND (B.MEDIA_TYPE = #{ConstMEDIA_TYPE_IMAGE} OR
								B.MEDIA_TYPE =
								#{ConstMEDIA_TYPE_MOVIE})
							</when>
							<when test="playlist_type == ConstPLAYLIST_TYPE_SYNCPLAY">
								<choose>
									<when test="sync_content_type != null">
										AND (B.MEDIA_TYPE = #{sync_content_type})
									</when>
									<otherwise>
										AND (B.MEDIA_TYPE = #{ConstMEDIA_TYPE_IMAGE} OR
										B.MEDIA_TYPE =
										#{ConstMEDIA_TYPE_MOVIE} OR B.MEDIA_TYPE =
										#{ConstMEDIA_TYPE_LFD} OR B.MEDIA_TYPE =
										#{ConstMEDIA_TYPE_DLK})
									</otherwise>
								</choose>
							</when>
							<when test="playlist_type == ConstPLAYLIST_TYPE_AMS">
								AND (B.MEDIA_TYPE = #{ConstMEDIA_TYPE_IMAGE} OR
								B.MEDIA_TYPE =
								#{ConstMEDIA_TYPE_MOVIE})
							</when>
							<when
								test="playlist_type == ConstPLAYLIST_TYPE_ADVERTISEMENT">
								AND (B.MEDIA_TYPE = #{ConstMEDIA_TYPE_IMAGE} OR
								B.MEDIA_TYPE =
								#{ConstMEDIA_TYPE_MOVIE} OR B.MEDIA_TYPE =
								#{ConstMEDIA_TYPE_LFD} OR B.MEDIA_TYPE = #{ConstMEDIA_TYPE_DLK}
								OR B.MEDIA_TYPE = #{ConstMEDIA_TYPE_SOUND})
							</when>
						</choose>
						AND B.MEDIA_TYPE != #{ConstMEDIA_TYPE_TEMPLATE_EXTENSION} AND
						B.MEDIA_TYPE != #{ConstMEDIA_TYPE_VWL}
					</when>
					<when test="mediaType.equalsIgnoreCase('LFD')">
						AND B.MEDIA_TYPE = #{mediaType} AND B.IS_TEMPLATE
						!= 'Y'
					</when>
					<when test="mediaType.equalsIgnoreCase('VISUAL_VWL')">
					</when>
					<otherwise>
						AND B.MEDIA_TYPE = #{mediaType}
					</otherwise>
				</choose>
			</when>
			<when test="isMain != null and isMain.equals('true')">
			</when>
			<otherwise>
				<choose>
					<when
						test="content_type != null and content_type.length() > 0">
						<choose>
							<when test="content_type.equals(ConstCONTENT_TYPE_CONTENT)">
								AND B.IS_TEMPLATE != 'Y'
							</when>
							<when test="content_type.equals(ConstCONTENT_TYPE_TEMPLATE)">
								AND B.IS_TEMPLATE = 'Y'
							</when>
						</choose>
					</when>
				</choose>
			</otherwise>
		</choose>

		<choose>
			<when
				test="mediaType != null and mediaType.equalsIgnoreCase('VISUAL_VWL')">
				<choose>
					<when test="useMultiVWL.equalsIgnoreCase('Y')">
						AND (B.MULTI_VWL = 'true')
					</when>
					<when test="deviceType != null">
						<choose>
							<when test="deviceType.equals('')">
								AND (B.MEDIA_TYPE = #{ConstMEDIA_TYPE_VWL} AND
								(B.VWL_VERSION = 1
								OR B.VWL_VERSION = 0)
								OR (B.MEDIA_TYPE =
								#{ConstMEDIA_TYPE_IMAGE} OR B.MEDIA_TYPE =
								#{ConstMEDIA_TYPE_MOVIE}))
							</when>
							<otherwise>
								<choose>
									<when test="deviceType.equals(ConstTYPE_PREMIUM)">
										AND ((B.MEDIA_TYPE = #{ConstMEDIA_TYPE_VWL} AND
										B.VWL_VERSION = 2
										)
										OR B.MEDIA_TYPE = #{ConstMEDIA_TYPE_IMAGE}
										OR B.MEDIA_TYPE = #{ConstMEDIA_TYPE_MOVIE})

									</when>
									<when test="deviceType.equals(ConstTYPE_SOC)">
										AND ((B.MEDIA_TYPE = #{ConstMEDIA_TYPE_VWL} AND
										B.VWL_VERSION =
										11 )
										OR B.MEDIA_TYPE = #{ConstMEDIA_TYPE_IMAGE}
										OR B.MEDIA_TYPE = #{ConstMEDIA_TYPE_MOVIE})

									</when>
								</choose>
							</otherwise>
						</choose>
					</when>
				</choose>
			</when>
			<when
				test="mediaType != null and mediaType.equalsIgnoreCase('VWL')">
				<choose>
					<when test="useMultiVWL.equalsIgnoreCase('Y')">
						AND B.MULTI_VWL = 'true'
					</when>
					<when test="deviceType != null">
						<choose>
							<when test="deviceType.equals('')">
								AND B.MEDIA_TYPE = #{ConstMEDIA_TYPE_VWL} AND
								(B.VWL_VERSION = 1 OR
								B.VWL_VERSION = 0)
							</when>
							<otherwise>
								AND B.MEDIA_TYPE = #{ConstMEDIA_TYPE_VWL}
								<choose>
									<when test="deviceType.equals(ConstTYPE_PREMIUM)">
										AND B.VWL_VERSION = 2
									</when>
									<when test="deviceType.equals(ConstTYPE_SOC)">
										AND B.VWL_VERSION = 11
									</when>
									<otherwise>
										AND B.VWL_VERSION = 0
									</otherwise>
								</choose>
							</otherwise>
						</choose>
					</when>
				</choose>
			</when>
 			
			<when test="deviceTypeArr != null">
			
	 			AND (B.DEVICE_TYPE = '' OR B.DEVICE_TYPE IS NULL  
	 			OR
	 			 <foreach item="deviceType" collection="deviceTypeArr" separator=" OR " open="" close="">
	                <choose>
	                	<when test="deviceType == ConstTYPE_SOC">
	                		(B.DEVICE_TYPE = #{ConstTYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{TYPE_VERSION_1_0}) 
	                	</when>
	                	<when test="deviceType == ConstTYPE_SOC2">
	                		(B.DEVICE_TYPE = #{ConstTYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{TYPE_VERSION_2_0}) 
	                	</when>
	                	<when test="deviceType == ConstTYPE_SOC3">
	                		(B.DEVICE_TYPE = #{ConstTYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{TYPE_VERSION_3_0}) 
	                	</when>
	                	<when test="deviceType == ConstTYPE_SOC4">
	                		(B.DEVICE_TYPE = #{ConstTYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{TYPE_VERSION_4_0}) 
	                	</when>
	                	<when test="deviceType == ConstTYPE_SOC5">
	                		(B.DEVICE_TYPE = #{ConstTYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{TYPE_VERSION_5_0}) 
	                	</when>
	                	<when test="deviceType == ConstTYPE_SOC6">
	                		(B.DEVICE_TYPE = #{ConstTYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{TYPE_VERSION_6_0}) 
	                	</when>
	                	<when test="deviceType == ConstTYPE_SOC7">
	                		(B.DEVICE_TYPE = #{ConstTYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{TYPE_VERSION_7_0}) 
	                	</when>
	                	<when test="deviceType == ConstTYPE_SOC9">
	                		(B.DEVICE_TYPE = #{ConstTYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{TYPE_VERSION_9_0})
	                	</when>
	                	<when test="deviceType == ConstTYPE_SOC10">
	                		(B.DEVICE_TYPE = #{ConstTYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{TYPE_VERSION_10_0})
	                	</when>
	                	<when test="deviceType == ConstTYPE_PREMIUM">
	                		( B.DEVICE_TYPE = #{deviceType} ) 
	                	</when>
	                	<otherwise>
	                        ( B.DEVICE_TYPE = #{deviceType} ) 
	                    </otherwise>
	                </choose>
		        </foreach>
		        )
				<choose>
					<when
						test="deviceType.equalsIgnoreCase(ConstTYPE_THIRDPARTY)">
						AND B.DEVICE_TYPE = #{ConstTYPE_THIRDPARTY}
					</when>
					<otherwise>
						AND B.DEVICE_TYPE != #{ConstTYPE_THIRDPARTY}
					</otherwise>
			   </choose>
			</when>
	 	</choose>
	 	
		<if
			test="!listType.equalsIgnoreCase(ConstGROUP_TYPE_SHARED)
				and !listType.equalsIgnoreCase(ConstGROUP_TYPE_ORGAN) 
				and !listType.equalsIgnoreCase(ConstGROUP_TYPE_SHAREFOLDER)">
			<choose>
				<when test="listType.equalsIgnoreCase(ConstGROUP_TYPE_ALL)">
					<if test="creatorID != null and creatorID.length() > 0">
						<choose>
							<when test="canReadUnshared">
								<if test="!hasPermission">
									AND A.CREATOR_ID = #{creatorID}
								</if>
							</when>
							<otherwise>
								AND (A.CREATOR_ID = #{creatorID} OR A.SHARE_FLAG = 1)
							</otherwise>
						</choose>
					</if>
				</when>
				<otherwise>
					<if
						test="creatorID != null and creatorID.length() > 0 and organization_id != 0">
						<choose>
							<when test="ConstGROUP_TYPE_ALL.equalsIgnoreCase(listType)">
								<if test="!canReadUnshared">
									AND (A.CREATOR_ID = #{creatorID} OR A.SHARE_FLAG =
									1)
								</if>
							</when>
							<otherwise>
								<choose>
									<when test="canReadUnshared">
										<if test="!hasPermission">
											AND A.CREATOR_ID = #{creatorID}
										</if>
									</when>
									<otherwise>
										AND (A.CREATOR_ID = #{creatorID} OR A.SHARE_FLAG =
										1)
									</otherwise>
								</choose>
							</otherwise>
						</choose>
					</if>
				</otherwise>
			</choose>
		</if>

		<if test="userFilter != null and !userFilter.equals('')">
			<foreach item="item" index="index" collection="userFilter"
				open=" AND (" separator=" OR " close=")">
				A.CREATOR_ID = #{item}
			</foreach>
			<if test="viewRange != null and !viewRange.equals('all')">
				AND A.SHARE_FLAG = 1
			</if>
		</if>

		<if test="sizeFilter != null">
			<foreach item="item" index="index" collection="sizeFilter"
				open=" AND (" separator=" OR " close=")">
				<if test="item.start != null">
					B.TOTAL_SIZE &gt;= #{item.start}
				</if>
				<if test="item.end != null">
					AND B.TOTAL_SIZE &lt; #{item.end}
				</if>
			</foreach>
		</if>

		<if test="contentUsingStatusFilter != null">
			<choose>
				<when
					test="contentUsingStatusFilter.equalsIgnoreCase('used_content')">
					AND K.CONTENT_ID IS NOT NULL
				</when>
				<when
					test="contentUsingStatusFilter.equalsIgnoreCase('unused_content')">
					AND K.CONTENT_ID IS NULL
				</when>
			</choose>
		</if>

		<if test="contentIdList != null and !contentIdList.equals('') and !(contentIdList.size==1 and contentIdList[0].equals('null'))">
			<foreach item="item" index="index" collection="contentIdList"
				open=" AND (" separator=" OR " close=")">
				<if test="item != null and !item.equals('') and !item.equalsIgnoreCase('null')">
					A.CONTENT_ID                           = #{item}
				</if>
			</foreach>
		</if>
		<!-- KDH RQ190703-00340 만료상태 필터링 옵션 추가 S -->
		<if test="expirationStatusFilter != null">
			<choose>
				<when test="expirationStatusFilter.equalsIgnoreCase('expired_content')">
				AND A.EXPIRATION_DATE <![CDATA[<]]> #{curDate}
				</when>
				<when test="expirationStatusFilter.equalsIgnoreCase('valid_content')">
				AND A.EXPIRATION_DATE <![CDATA[>=]]> #{curDate}
				</when>
			</choose>
		</if>
		<!-- KDH RQ190703-00340 만료상태 필터링 옵션 추가 E -->
		AND B.MEDIA_TYPE != 'TLFD'
	</sql>

	<sql id="getTagAndCategoryFilterForPostgreSQL">
		<if test="tagFilter != null and !tagFilter.equals('')">
			<foreach item="item" index="index" collection="tagFilter"
					 open=" AND EXISTS ( SELECT 1 FROM MI_TAG_MAP_CONTENT WHERE CONTENT_ID = A.CONTENT_ID AND TAG_ID IN ( " separator=" , " close=" ) )">
				#{item} :: Integer
			</foreach>
		</if>

		<if test="categoryFilterList != null and !categoryFilterList.equals('')">
			<foreach item="item" index="index" collection="categoryFilterList"
					 open=" AND EXISTS ( SELECT 1 FROM MI_CATEGORY_MAP_CONTENT WHERE CONTENT_ID = A.CONTENT_ID AND GROUP_ID IN ( " separator=" , " close=" ) )">
				#{item} :: Integer
			</foreach>
		</if>
	</sql>

	<sql id="getTagAndCategoryFilterForMSSQL">
		<if test="tagFilter != null and !tagFilter.equals('')">
			<foreach item="item" index="index" collection="tagFilter"
					 open=" AND EXISTS ( SELECT 1 FROM MI_TAG_MAP_CONTENT WHERE CONTENT_ID = A.CONTENT_ID AND TAG_ID IN ( " separator=" , " close=" ) )">
				#{item}
			</foreach>
		</if>

		<if test="categoryFilterList != null and !categoryFilterList.equals('')">
			<foreach item="item" index="index" collection="categoryFilterList"
					 open=" AND EXISTS ( SELECT 1 FROM MI_CATEGORY_MAP_CONTENT WHERE CONTENT_ID = A.CONTENT_ID AND GROUP_ID IN ( " separator=" , " close=" ) )">
				#{item}
			</foreach>
		</if>
	</sql>

	<select id="getFileInfo"
		resultType="com.samsung.magicinfo.framework.content.entity.ContentFile">
		SELECT
		FILE_ID, FILE_NAME, FILE_SIZE, FILE_PATH, HASH_CODE, FILE_TYPE,
		IS_STREAMING
		FROM
		MI_CMS_INFO_FILE
		<if test="fileId">
			WHERE
			FILE_ID = #{fileId}
		</if>
	</select>

	<select id="getFilesByIds"
		resultType="com.samsung.magicinfo.framework.content.entity.ContentFile">
		SELECT
		FILE_ID, FILE_NAME, FILE_SIZE, FILE_PATH, HASH_CODE, FILE_TYPE,
		IS_STREAMING
		FROM
		MI_CMS_INFO_FILE
		<if test="fileIds != null">
			WHERE
			FILE_ID IN
			<foreach item="fileId" collection="fileIds" open="("
				separator="," close=")">
				#{fileId}
			</foreach>
		</if>
	</select>

	<select id="getMainFileInfo"
		resultType="com.samsung.magicinfo.framework.content.entity.ContentFile">
		SELECT
		FILE_ID, FILE_NAME, FILE_SIZE, FILE_PATH, HASH_CODE, FILE_TYPE,
		A.IS_STREAMING, B.MEDIA_TYPE
		FROM
		MI_CMS_INFO_FILE A,
		MI_CMS_INFO_CONTENT_VERSION B
		WHERE
		A.FILE_ID = B.MAIN_FILE_ID AND B.IS_ACTIVE = 'Y'
		<if test="contentId != null">
			AND B.CONTENT_ID = #{contentId}
		</if>
	</select>
	
	<select id="getRulesetFileInfo"
		resultType="com.samsung.magicinfo.framework.content.entity.ContentFile">
		SELECT
		A.FILE_ID, A.FILE_NAME, A.FILE_SIZE, A.FILE_PATH, A.HASH_CODE, A.FILE_TYPE,
		A.IS_STREAMING
		FROM
		MI_CMS_INFO_FILE A,
		MI_RULE_INFO_RULESET B
		WHERE
		A.FILE_ID = B.FILE_ID
		<if test="rulesetId != null">
			AND B.RULESET_ID = #{rulesetId}
		</if>
	</select>
	
	<select id="getMainFileInfoTemporary"
		resultType="com.samsung.magicinfo.framework.content.entity.ContentFile">
		SELECT
		FILE_ID, FILE_NAME, FILE_SIZE, FILE_PATH, HASH_CODE, FILE_TYPE,
		A.IS_STREAMING
		FROM
		MI_CMS_INFO_FILE A,
		MI_CMS_INFO_CONTENT_VERSION B
		WHERE
		A.FILE_ID = B.MAIN_FILE_ID
		<if test="contentId != null">
			AND B.CONTENT_ID = #{contentId}
		</if>
	</select>

	<select id="getMainFileName" resultType="string">
		SELECT
		FILE_NAME
		FROM
		MI_CMS_INFO_FILE A,
		MI_CMS_INFO_CONTENT_VERSION B
		WHERE
		A.FILE_ID = B.MAIN_FILE_ID AND B.IS_ACTIVE = 'Y'
		<if test="contentId != null">
			AND B.CONTENT_ID = #{contentId}
		</if>
	</select>

	<select id="getThumbFileInfo"
		resultType="com.samsung.magicinfo.framework.content.entity.ContentFile">
		SELECT
		FILE_ID, FILE_NAME, FILE_SIZE, FILE_PATH, HASH_CODE, FILE_TYPE
		FROM
		MI_CMS_INFO_FILE A,
		MI_CMS_INFO_CONTENT_VERSION B
		WHERE
		A.FILE_ID =
		B.THUMB_FILE_ID AND B.IS_ACTIVE = 'Y'
		<if test="contentId != null">
			AND B.CONTENT_ID = #{contentId}
		</if>
	</select>

	<select id="getSfiFileInfo"
		resultType="com.samsung.magicinfo.framework.content.entity.ContentFile">
		SELECT
		FILE_ID, FILE_NAME, FILE_SIZE, FILE_PATH, HASH_CODE, FILE_TYPE
		FROM
		MI_CMS_INFO_FILE A,
		MI_CMS_INFO_CONTENT_VERSION B
		WHERE
		A.FILE_ID =
		B.SFI_FILE_ID AND B.IS_ACTIVE = 'Y'
		<if test="contentId != null">
			AND B.CONTENT_ID = #{contentId}
		</if>
	</select>

	<select id="getMainFileInfoOfTmpVer"
		resultType="com.samsung.magicinfo.framework.content.entity.ContentFile">
		SELECT
		FILE_ID, FILE_NAME, FILE_SIZE, FILE_PATH, HASH_CODE, FILE_TYPE
		FROM
		MI_CMS_INFO_FILE A,
		MI_CMS_INFO_CONTENT_VERSION B
		WHERE
		A.FILE_ID =
		B.MAIN_FILE_ID AND B.VERSION_ID = 0
		<if test="contentId != null">
			AND B.CONTENT_ID = #{contentId}
		</if>
	</select>

	<select id="getThumbFileInfoOfTempVer"
		resultType="com.samsung.magicinfo.framework.content.entity.ContentFile">
		SELECT
		FILE_ID, FILE_NAME, FILE_SIZE, FILE_PATH, HASH_CODE, FILE_TYPE
		FROM
		MI_CMS_INFO_FILE A,
		MI_CMS_INFO_CONTENT_VERSION B
		WHERE
		A.FILE_ID =
		B.THUMB_FILE_ID AND B.VERSION_ID = 0
		<if test="contentId != null">
			AND B.CONTENT_ID = #{contentId}
		</if>
	</select>

	<select id="getFileHash" resultType="string">
		SELECT
		HASH_CODE
		FROM
		MI_CMS_INFO_FILE
		<if test="fileId != null">
			WHERE FILE_ID = #{fileId}
		</if>
	</select>

	<select id="getFileSize" resultType="long">
		SELECT
		FILE_SIZE
		FROM
		MI_CMS_INFO_FILE
		<if test="fileId != null">
			WHERE FILE_ID = #{fileId}
		</if>
	</select>

	<select id="getFileName" resultType="string">
		SELECT
		FILE_NAME
		FROM
		MI_CMS_INFO_FILE
		<if test="fileId != null">
			WHERE FILE_ID = #{fileId}
		</if>
	</select>

	<select id="getThumbFileList" resultType="java.util.Map">
		SELECT
		THUMB_FILE_ID
		FROM
		MI_CMS_INFO_CONTENT_VERSION
		WHERE
		CONTENT_ID = #{contentId}
	</select>

	<select id="getFileList"
		resultType="com.samsung.magicinfo.framework.content.entity.ContentFile">
		SELECT
		DISTINCT(A.FILE_ID), FILE_NAME , FILE_SIZE,
		FILE_PATH, HASH_CODE, FILE_TYPE, CREATOR_ID,
		CREATE_DATE
		FROM
		MI_CMS_INFO_FILE A,
		MI_CMS_MAP_VERSION_FILE B
		WHERE
		A.FILE_ID = B.FILE_ID
		AND CONTENT_ID = #{contentId}
		ORDER BY
		FILE_NAME ASC
	</select>

	<select id="getFileListForApi"
		resultType="com.samsung.magicinfo.framework.content.entity.ContentFile">
		SELECT
		DISTINCT(A.FILE_ID), FILE_NAME , FILE_SIZE,
		HASH_CODE, FILE_TYPE, CREATOR_ID, CREATE_DATE
		FROM
		MI_CMS_INFO_FILE A,
		MI_CMS_MAP_VERSION_FILE B
		WHERE
		A.FILE_ID = B.FILE_ID AND CONTENT_ID =
		#{contentId}
		ORDER BY
		FILE_NAME ASC
	</select>

	<select id="getFileListByContentAndVersion"
		resultType="com.samsung.magicinfo.framework.content.entity.ContentFile">
		SELECT
		A.FILE_ID, FILE_NAME , FILE_SIZE, FILE_PATH, HASH_CODE,
		FILE_TYPE, CREATOR_ID,
		CREATE_DATE
		FROM
		MI_CMS_INFO_FILE A,
		MI_CMS_MAP_VERSION_FILE B
		WHERE
		A.FILE_ID = B.FILE_ID
		<if test="contentId != null and contentId.length() > 0">
			AND CONTENT_ID = #{contentId}
		</if>
		<if test="versionId != null">
			AND VERSION_ID = #{versionId}
		</if>

	</select>

	<sql id="getFileListPage_body">
		FROM
		MI_CMS_INFO_FILE A,
		MI_CMS_MAP_VERSION_FILE B
		WHERE
		A.FILE_ID =
		B.FILE_ID
		<if test="contentId != null and contentId.length() > 0">
			AND CONTENT_ID = #{contentId}
		</if>
		<if test="versionId != null">
			AND VERSION_ID = #{versionId}
		</if>
	</sql>

	<select id="getFileListPage"
		resultType="com.samsung.magicinfo.framework.content.entity.ContentFile">
		SELECT
		A.FILE_ID, FILE_NAME , FILE_SIZE, FILE_PATH, HASH_CODE,
		FILE_TYPE, CREATOR_ID,
		CREATE_DATE
		<include refid="getFileListPage_body" />
		LIMIT #{pageSize} OFFSET #{startPos}
	</select>

	<select id="getFileListPage"
		resultType="com.samsung.magicinfo.framework.content.entity.ContentFile"
		databaseId="mssql">
		SELECT * FROM
		(
		SELECT A.FILE_ID, FILE_NAME , FILE_SIZE, FILE_PATH,
		HASH_CODE, FILE_TYPE,
		CREATOR_ID, CREATE_DATE,
		ROW_NUMBER() OVER(ORDER
		BY A.FILE_ID) as rownum
		<include refid="getFileListPage_body" />
		) as SubQuery
		WHERE rownum > ${startPos} and rownum &lt;= ${startPos +
		pageSize}
		ORDER BY rownum
	</select>

	<select id="getFileListCnt" resultType="int">
		SELECT
		COUNT(A.FILE_ID)
		FROM
		MI_CMS_INFO_FILE A,
		MI_CMS_MAP_VERSION_FILE B
		WHERE
		A.FILE_ID = B.FILE_ID
		<if test="contentId != null and contentId.length() > 0">
			AND CONTENT_ID = #{contentId}
		</if>
		<if test="versionId != null">
			AND VERSION_ID = #{versionId}
		</if>
	</select>


	<select id="getActiveFileList"
		resultType="com.samsung.magicinfo.framework.content.entity.ContentFile">
		SELECT
		A.FILE_ID, A.FILE_NAME , A.FILE_SIZE, A.FILE_PATH,
		A.HASH_CODE, A.FILE_TYPE,
		A.CREATE_DATE
		FROM
		MI_CMS_INFO_FILE A,
		MI_CMS_MAP_VERSION_FILE B,
		MI_CMS_INFO_CONTENT_VERSION C
		WHERE
		A.FILE_ID
		= B.FILE_ID AND B.CONTENT_ID = C.CONTENT_ID AND B.VERSION_ID =
		C.VERSION_ID
		AND C.IS_ACTIVE = 'Y' AND C.CONTENT_ID = #{contentId}
	</select>


	<select id="getActiveFileListByType"
		resultType="com.samsung.magicinfo.framework.content.entity.ContentFile">
		SELECT
		A.FILE_ID, A.FILE_NAME , A.FILE_SIZE, A.FILE_PATH, A.HASH_CODE,
		A.FILE_TYPE,
		A.CREATE_DATE
		FROM
		MI_CMS_INFO_FILE A,
		MI_CMS_MAP_VERSION_FILE B,
		MI_CMS_INFO_CONTENT_VERSION C
		WHERE
		A.FILE_ID
		= B.FILE_ID AND B.CONTENT_ID = C.CONTENT_ID AND B.VERSION_ID =
		C.VERSION_ID
		AND C.IS_ACTIVE = 'Y' AND C.CONTENT_ID = #{contentId}
		<if test="isThumbnail != null">
			<if test="isThumbnail == false">
				AND A.FILE_TYPE != 'THUMBNAIL'
			</if>
			<if test="isThumbnail == true">
				AND A.FILE_TYPE = 'THUMBNAIL'
			</if>
		</if>
	</select>




	<sql id="getActiveFileListPage_body">
		FROM
		MI_CMS_INFO_FILE A,
		MI_CMS_MAP_VERSION_FILE B,
		MI_CMS_INFO_CONTENT_VERSION C
		WHERE
		A.FILE_ID = B.FILE_ID AND
		B.CONTENT_ID = C.CONTENT_ID
		AND C.IS_ACTIVE = 'Y' AND C.CONTENT_ID =
		#{contentId}
	</sql>

	<select id="getActiveFileListPage"
		resultType="com.samsung.magicinfo.framework.content.entity.ContentFile">
		SELECT
		A.FILE_ID, FILE_NAME , FILE_SIZE, FILE_PATH, HASH_CODE,
		FILE_TYPE
		<include refid="getActiveFileListPage_body" />
		LIMIT #{pageSize} OFFSET #{startPos}
	</select>

	<select id="getActiveFileListPage"
		resultType="com.samsung.magicinfo.framework.content.entity.ContentFile"
		databaseId="mssql">
		SELECT * FROM
		(
		SELECT A.FILE_ID, FILE_NAME , FILE_SIZE, FILE_PATH,
		HASH_CODE, FILE_TYPE,
		ROW_NUMBER() OVER(ORDER BY A.FILE_ID) as rownum
		<include refid="getActiveFileListPage_body" />
		) as SubQuery
		WHERE rownum > ${startPos} and rownum &lt;= ${startPos +
		pageSize}
		ORDER BY rownum
	</select>

	<select id="getActiveFileListCnt" resultType="int">
		SELECT
		COUNT(*)
		FROM
		MI_CMS_INFO_FILE A,
		MI_CMS_MAP_VERSION_FILE B,
		MI_CMS_INFO_CONTENT_VERSION C
		WHERE
		A.FILE_ID = B.FILE_ID AND
		B.CONTENT_ID = C.CONTENT_ID
		AND C.IS_ACTIVE = 'Y' AND C.CONTENT_ID =
		#{contentId}
	</select>

	<select id="existFileByIDCnt" resultType="int">
		SELECT
		COUNT(FILE_ID)
		FROM
		MI_CMS_INFO_FILE
		WHERE
		FILE_ID = #{fileId}
	</select>

	<select id="numberOfExistingFileByHash" resultType="int">
		SELECT
		COUNT(FILE_ID)
		FROM
		MI_CMS_INFO_FILE
		WHERE
		FILE_NAME = #{fileName} AND
		FILE_SIZE = #{fileSize} AND HASH_CODE = #{hashCode}
	</select>

	<select id="getFileIdByHash" resultType="string">
		SELECT
		FILE_ID
		FROM
		MI_CMS_INFO_FILE
		WHERE
		FILE_NAME = #{fileName} AND HASH_CODE =
		#{hashCode} LIMIT 1
	</select>

	<select id="getFileIdByHash" resultType="string"
		databaseId="mssql">
		SELECT TOP 1
		FILE_ID
		FROM
		MI_CMS_INFO_FILE
		WHERE
		FILE_NAME =
		#{fileName} AND HASH_CODE = #{hashCode}
	</select>

	<select id="getFileIDByHash" resultType="string"
		databaseId="mssql">
		SELECT TOP 1
		FILE_ID
		FROM
		MI_CMS_INFO_FILE
		WHERE
		FILE_NAME =
		#{fileName} AND FILE_SIZE = #{fileSize} AND HASH_CODE = #{hashCode}
	</select>

	<select id="getFileIDByHash" resultType="string">
		SELECT
		FILE_ID
		FROM
		MI_CMS_INFO_FILE
		WHERE
		FILE_NAME = #{fileName} AND FILE_SIZE =
		#{fileSize} AND HASH_CODE = #{hashCode}
		LIMIT 1
	</select>

	<select id="getFileIDByHashCreator" resultType="string">
		SELECT
		FILE_ID
		FROM
		MI_CMS_INFO_FILE
		WHERE
		FILE_NAME = #{fileName} AND FILE_SIZE =
		#{fileSize} AND HASH_CODE = #{hashCode}
		AND CREATOR_ID = #{creatorId}
		LIMIT 1
	</select>

	<select id="getFileIDByHashCreator" resultType="string"
		databaseId="mssql">
		SELECT TOP 1
		FILE_ID
		FROM
		MI_CMS_INFO_FILE
		WHERE
		FILE_NAME =
		#{fileName} AND FILE_SIZE = #{fileSize} AND HASH_CODE = #{hashCode}
		AND CREATOR_ID = #{creatorId}
	</select>
	<select id="numberOfExistingFileByIDAndHash" resultType="int">
		SELECT
		COUNT(FILE_ID)
		FROM
		MI_CMS_INFO_FILE
		WHERE
		FILE_NAME = #{fileName} AND
		FILE_ID = #{fileId} AND HASH_CODE = #{hashCode}
	</select>

	<select id="numberOfExistActiveContentID" resultType="int">
		SELECT
		COUNT(A.CONTENT_ID)
		FROM
		MI_CMS_INFO_CONTENT A,
		MI_CMS_INFO_CONTENT_VERSION B
		WHERE
		A.CONTENT_ID = B.CONTENT_ID AND
		A.CONTENT_ID = #{contentId} AND A.CREATOR_ID =
		#{creatorId}
		AND
		A.ORGANIZATION_ID = #{organizationId} AND A.IS_DELETED = 'N' AND
		B.IS_ACTIVE = 'Y'
	</select>

	<select id="numberOfExistContentForCidMapping" resultType="int">
		SELECT
		COUNT(A.CONTENT_ID)
		FROM
		MI_CMS_INFO_CONTENT A,
		MI_CMS_INFO_CONTENT_VERSION B
		WHERE
		A.CONTENT_ID = B.CONTENT_ID AND
		A.CONTENT_ID = #{contentId} AND A.CREATOR_ID =
		#{creatorId}
		AND
		A.ORGANIZATION_ID = #{organizationId} AND A.IS_DELETED = 'N'
	</select>

	<select id="numberOfExistContentID" resultType="int">
		SELECT
		COUNT(CONTENT_ID)
		FROM
		MI_CMS_INFO_CONTENT
		WHERE
		CONTENT_ID = #{contentId}
		AND IS_DELETED = 'N'
	</select>

	<select id="numberOfExistContentVersion" resultType="int">
		SELECT
		COUNT(CONTENT_ID)
		FROM
		MI_CMS_INFO_CONTENT_VERSION
		WHERE
		CONTENT_ID =
		#{contentId} AND VERSION_ID = #{versionId}
	</select>

	<select id="numberOfUpdatableContent" resultType="int">
		SELECT
		COUNT(CONTENT_ID)
		FROM
		MI_CMS_INFO_CONTENT
		WHERE
		CONTENT_ID = #{contentId}
	</select>

	<select id="numberOfDeletableFile" resultType="int">
		SELECT
		COUNT(CONTENT_ID)
		FROM
		MI_CMS_MAP_VERSION_FILE A,
		MI_CMS_INFO_FILE B
		WHERE
		A.FILE_ID = #{fileId} AND A.FILE_ID = B.FILE_ID AND (A.CONTENT_ID
		!=
		#{contentId})
	</select>

	<select id="numberOfDeletableFileByVersion" resultType="int">
		SELECT
		COUNT(CONTENT_ID)
		FROM
		MI_CMS_MAP_VERSION_FILE A,
		MI_CMS_INFO_FILE B
		WHERE
		A.FILE_ID = #{fileId} AND A.FILE_ID = B.FILE_ID AND (A.CONTENT_ID
		!=
		#{contentId} OR (A.CONTENT_ID = #{contentId} AND A.VERSION_ID !=
		#{versionId}))
	</select>

	<select id="numberOfLockedContent" resultType="int">
		SELECT
		COUNT(CONTENT_ID)
		FROM
		MI_CMS_INFO_CONTENT
		WHERE
		SESSION_ID IS NOT NULL
		AND CONTENT_ID = #{contentId} AND SESSION_ID !=
		#{sessionId}
	</select>

	<select id="numberOfUsedForPlaylist" resultType="int">
		SELECT
		COUNT(PLAYLIST_ID)
		FROM
		MI_CMS_MAP_PLAYLIST_CONTENT
		WHERE
		CONTENT_ID =
		#{contentId}
	</select>

	<select id="numberOfUsedForLitePlaylist" resultType="int">
		SELECT
		COUNT(PLAYLIST_ID)
		FROM
		MI_CMS_MAP_LITE_PLAYLIST_CONTENT
		WHERE
		CONTENT_ID
		= #{contentId}
	</select>

	<insert id="addContentInfo"
		parameterType="com.samsung.magicinfo.framework.content.entity.Content">
		INSERT INTO MI_CMS_INFO_CONTENT (CONTENT_ID,
		CONTENT_NAME, CONTENT_META_DATA, SHARE_FLAG, POLLING_INTERVAL,
		IS_DELETED, LAST_MODIFIED_DATE, CREATOR_ID, CREATE_DATE,
		ORGANIZATION_ID, APPROVAL_STATUS )
		VALUES (#{content_id},
		#{content_name}, #{content_meta_data}, #{share_flag},
		#{polling_interval}, #{is_deleted}, CURRENT_TIMESTAMP, #{creator_id},
		CURRENT_TIMESTAMP, #{organization_id}, #{approval_status})
	</insert>

	<update id="setContentModifiedDate">
		UPDATE MI_CMS_INFO_CONTENT SET LAST_MODIFIED_DATE =
		CURRENT_TIMESTAMP WHERE CONTENT_ID = #{contentId}
	</update>

	<update id="updateContentForStartPageRefreshInterval">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET
		HTML_START_PAGE = #{content.html_start_page}, REFRESH_INTERVAL =
		#{content.refresh_interval} WHERE CONTENT_ID = #{content.content_id}
		AND VERSION_ID = #{content.version_id}
	</update>

	<insert id="addAdsContentVersionInfo">
		INSERT INTO MI_CMS_INFO_ADS_SETTING(CONTENT_ID ,VERSION_ID, PUBLISHER_ID,
		PUBLISHER_NAME ,API_KEY, API_KEY_SECRET, AD_UNIT_ID, IMAGE_TYPE_SET, IMAGE_DURATION,
		VIDEO_TYPE_SET, VIDEO_DURATION , DEFAULT_CONTENT, DEFAULT_CONTENT_FILE_ID,
		IS_ACTIVE, IS_DELETED)

		VALUES(#{content_id}, #{version_id}, #{publisher_id}, #{publisher_name}, #{api_key},
		#{api_key_secret}, #{ad_unit_id}, #{image_type_set}, #{image_duration}, #{video_type_set},
		#{video_duration}, #{default_content}, #{default_content_file_id},
		#{is_active}, 'N')
	</insert>

	<insert id="addAdsContentPublisherInfoSuggestion">
		INSERT INTO MI_CMS_INFO_ADS_PUBLISHER_INFO_SUGGESTION
		(PUBLISHER_ID, PUBLISHER_NAME, API_KEY, API_KEY_SECRET, USER_ID, LAST_MODIFIED_DATE)

		VALUES(#{publisherInfo.publisherId}, #{publisherInfo.publisherName}, #{publisherInfo.apiKey}, #{publisherInfo.apiKeySecret}, #{userId}, CURRENT_TIMESTAMP)
	</insert>

	<insert id="addAdsContentAdUnitIdSuggestion">
		INSERT INTO MI_CMS_INFO_ADS_AD_UNIT_ID_SUGGESTION
		(AD_UNIT_ID, USER_ID, LAST_MODIFIED_DATE)

		VALUES(#{adUnitId}, #{userId}, CURRENT_TIMESTAMP)
	</insert>

	<update id="updateAdsContentPublisherSuggestionInfo">
		UPDATE MI_CMS_INFO_ADS_PUBLISHER_INFO_SUGGESTION
		SET PUBLISHER_NAME = #{publisherInfo.publisherName}, API_KEY = #{publisherInfo.apiKey},
		API_KEY_SECRET = #{publisherInfo.apiKeySecret}, LAST_MODIFIED_DATE = CURRENT_TIMESTAMP
		WHERE PUBLISHER_ID = #{publisherInfo.publisherId} AND USER_ID = #{userId}
	</update>

	<update id="updateAdsContentAdUnitIdSuggestionInfo">
		UPDATE MI_CMS_INFO_ADS_AD_UNIT_ID_SUGGESTION
		SET LAST_MODIFIED_DATE = CURRENT_TIMESTAMP
		WHERE AD_UNIT_ID = #{adUnitId} AND USER_ID = #{userId}
	</update>

	<select id="existPublisherInfoByPublisherId">
		SELECT COUNT(PUBLISHER_ID)
		FROM MI_CMS_INFO_ADS_PUBLISHER_INFO_SUGGESTION
		WHERE PUBLISHER_ID = #{publisherId} AND USER_ID = #{userId}
	</select>

	<select id="existAdUnitIdById" resultType="int">
		SELECT COUNT(AD_UNIT_ID)
		FROM MI_CMS_INFO_ADS_AD_UNIT_ID_SUGGESTION
		WHERE AD_UNIT_ID = #{adUnitId} AND USER_ID = #{userId}
	</select>

	<delete id="deletePublisherInfoById">
		DELETE FROM MI_CMS_INFO_ADS_PUBLISHER_INFO_SUGGESTION
		WHERE PUBLISHER_ID = #{publisherId} AND USER_ID = #{userId}
	</delete>

	<delete id="deleteAdUnitIdInfoById">
		DELETE FROM MI_CMS_INFO_ADS_AD_UNIT_ID_SUGGESTION
		WHERE AD_UNIT_ID = #{adUnitId} AND USER_ID = #{userId}
	</delete>

	<select id="getAdsContentPublisherInfoSuggestionListByUser" resultType="com.samsung.magicinfo.restapi.contents.model.V2AdsContentPublisherInfo">
	 	SELECT PUBLISHER_ID as publisherId, PUBLISHER_NAME as publisherName, API_KEY as apiKey, API_KEY_SECRET as apiKeySecret
		FROM MI_CMS_INFO_ADS_PUBLISHER_INFO_SUGGESTION
		WHERE USER_ID = #{userId}
		ORDER BY LAST_MODIFIED_DATE DESC
		LIMIT #{count}
	</select>

	<select id="getAdsContentPublisherInfoSuggestionListByUser" databaseId="mssql" resultType="com.samsung.magicinfo.restapi.contents.model.V2AdsContentPublisherInfo">
		SELECT TOP (#{count}) PUBLISHER_ID as publisherId, PUBLISHER_NAME as publisherName, API_KEY as apiKey, API_KEY_SECRET as apiKeySecret
		FROM MI_CMS_INFO_ADS_PUBLISHER_INFO_SUGGESTION
		WHERE USER_ID = #{userId}
		ORDER BY LAST_MODIFIED_DATE DESC
	</select>

	<select id="getPublisherInfoById" resultType="com.samsung.magicinfo.restapi.contents.model.V2AdsContentPublisherInfo">
		SELECT PUBLISHER_ID as publisherId, PUBLISHER_NAME as publisherName, API_KEY as apiKey, API_KEY_SECRET as apiKeySecret
		FROM MI_CMS_INFO_ADS_PUBLISHER_INFO_SUGGESTION
		WHERE PUBLISHER_ID = #{publisherId} AND USER_ID = #{userId}
	</select>

	<select id="getAdsContentAdUnitIdSuggestionListByUser" resultType="string">
		SELECT AD_UNIT_ID
		FROM MI_CMS_INFO_ADS_AD_UNIT_ID_SUGGESTION
		WHERE USER_ID = #{userId}
		ORDER BY LAST_MODIFIED_DATE DESC
		LIMIT #{count}
	</select>

	<select id="getAdsContentAdUnitIdSuggestionListByUser" databaseId="mssql" resultType="string">
		SELECT TOP (#{count}) AD_UNIT_ID
		FROM MI_CMS_INFO_ADS_AD_UNIT_ID_SUGGESTION
		WHERE USER_ID = #{userId}
		ORDER BY LAST_MODIFIED_DATE DESC
	</select>

	<select id="getAdUnitIdById">
		SELECT AD_UNIT_ID
		FROM MI_CMS_INFO_ADS_AD_UNIT_ID_SUGGESTION
		WHERE AD_UNIT_ID = #{adUnitId} AND USER_ID = #{userId}
	</select>

	<insert id="addContentVersionInfo">
		INSERT INTO MI_CMS_INFO_CONTENT_VERSION (CONTENT_ID,
		VERSION_ID, CREATOR_ID, CREATE_DATE, MEDIA_TYPE, PLAY_TIME
		,
		RESOLUTION, TOTAL_SIZE, IS_ACTIVE, MAIN_FILE_ID, THUMB_FILE_ID,
		SFI_FILE_ID
		, IS_LINEAR_VWL, MODEL_COUNT_INFO, SCREEN_COUNT, X_COUNT,
		Y_COUNT, X_RANGE, Y_RANGE
		, IS_STREAMING, MAIN_FILE_EXTENSION,
		VWL_VERSION, DEVICE_TYPE, DEVICE_TYPE_VERSION, PLAY_TIME_MILLI,
		HTML_START_PAGE, URL_ADDRESS, REFRESH_INTERVAL, IS_AISR)
		VALUES (#{content_id},
		#{version_id}, #{creator_id}, CURRENT_TIMESTAMP , #{media_type},
		#{play_time}
		, #{resolution}, #{total_size}, #{is_active},
		#{main_file_id}, #{thumb_file_id}, #{sfi_file_id}
		, #{is_linear_vwl},
		#{model_count_info}, #{screen_count}, #{x_count}, #{y_count},
		#{x_range}, #{y_range}
		, #{is_streaming}, #{main_file_extension},
		#{vwl_version}, #{device_type}, #{device_type_version},
		#{play_time_milli}, #{html_start_page}, #{url_address},
		#{refresh_interval}, #{is_aisr})
	</insert>

	<insert id="addFile">
		INSERT INTO MI_CMS_INFO_FILE (FILE_ID, FILE_NAME,
		FILE_PATH, FILE_SIZE, HASH_CODE, FILE_TYPE, CREATOR_ID, CREATE_DATE,
		IS_STREAMING)
		VALUES (#{file_id}, #{file_name}, #{file_path},
		#{file_size}, #{hash_code}, #{file_type}, #{creator_id},
		CURRENT_TIMESTAMP, #{is_streaming})
	</insert>

	<insert id="addMapGroupContent">
		INSERT INTO MI_CMS_MAP_GROUP_CONTENT (CONTENT_ID,
		GROUP_ID) VALUES (#{contentId}, #{groupId})
	</insert>

	<insert id="addMapContentFile">
		INSERT INTO MI_CMS_MAP_VERSION_FILE (CONTENT_ID,
		VERSION_ID, FILE_ID) VALUES (#{contentId}, #{versionId}, #{fileId})
	</insert>

	<delete id="deleteMapContentVersionFile">
		DELETE FROM MI_CMS_MAP_VERSION_FILE WHERE CONTENT_ID
		= #{contentId} AND VERSION_ID = #{versionId}
	</delete>

	<delete id="deleteMapContentFile">
		DELETE FROM MI_CMS_MAP_VERSION_FILE WHERE CONTENT_ID
		= #{contentId} AND FILE_ID = #{fileId}
	</delete>

	<delete id="deleteContentVersion">
		DELETE FROM MI_CMS_INFO_CONTENT_VERSION WHERE
		CONTENT_ID = #{contentId} AND VERSION_ID = #{versionId}
	</delete>

	<delete id="deleteMapGroupContent">
		DELETE FROM MI_CMS_MAP_GROUP_CONTENT WHERE CONTENT_ID
		= #{contentId}
	</delete>

	<select id="numberOfMapContentFile" resultType="int">
		SELECT
		COUNT(CONTENT_ID) FROM MI_CMS_MAP_VERSION_FILE WHERE CONTENT_ID =
		#{contentId} AND VERSION_ID = #{versionId} AND FILE_ID = #{fileId}
	</select>

	<update id="setContentInfo">
		UPDATE MI_CMS_INFO_CONTENT
		<set>
			<if test="contentName != null and contentName.length() > 0">
				CONTENT_NAME = #{contentName},
			</if>
			<if
				test="contentMetaData != null and contentMetaData.length() > 0">
				CONTENT_META_DATA = #{contentMetaData},
			</if>
			<if test="shareFlag >= 0">
				SHARE_FLAG = #{shareFlag},
			</if>
			<if test="pollingInterval > 0">
				POLLING_INTERVAL = #{pollingInterval}
			</if>
		</set>
		WHERE CONTENT_ID = #{contentId}
	</update>

	<select id="getContentNextVer" resultType="long">
		SELECT MAX(VERSION_ID)
		FROM MI_CMS_INFO_CONTENT_VERSION WHERE CONTENT_ID = #{contentId}
	</select>

	<update id="setOtherAdsContentVersionInactive">
		UPDATE MI_CMS_INFO_ADS_SETTING SET IS_ACTIVE =
		'N' WHERE CONTENT_ID = #{contentId} AND VERSION_ID NOT IN
		(#{versionId})
	</update>

	<update id="setOtherVersionInactive">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET IS_ACTIVE =
		'N' WHERE CONTENT_ID = #{contentId} AND VERSION_ID NOT IN
		(#{versionId})
	</update>

	<select id="getActivePlaylistListUsingContent" resultType="map">
		SELECT
			DISTINCT(B.PLAYLIST_ID), B.PLAYLIST_NAME, B.PLAYLIST_TYPE
		FROM
			MI_CMS_MAP_PLAYLIST_CONTENT A,
			MI_CMS_INFO_PLAYLIST B,
			MI_CMS_INFO_PLAYLIST_VERSION C
		WHERE
			A.PLAYLIST_ID = B.PLAYLIST_ID 
			AND A.CONTENT_ID = #{contentId} 
			AND B.PLAYLIST_ID = C.PLAYLIST_ID 
			AND A.VERSION_ID = (SELECT DISTINCT(VERSION_ID) FROM MI_CMS_INFO_PLAYLIST_VERSION WHERE PLAYLIST_ID = B.PLAYLIST_ID AND IS_ACTIVE = 'Y')
		UNION
			SELECT DISTINCT(C.PLAYLIST_ID), C.PLAYLIST_NAME, C.PLAYLIST_TYPE
		FROM 
			MI_TAG_MAP_CONTENT A,
			MI_CMS_MAP_PLAYLIST_TAG B,
			MI_CMS_INFO_PLAYLIST C,
			MI_CMS_INFO_PLAYLIST_VERSION D
		WHERE 
			A.CONTENT_ID = #{contentId}
			AND B.TAG_ID = A.TAG_ID
			AND C.PLAYLIST_ID = B.PLAYLIST_ID
			AND C.PLAYLIST_ID = D.PLAYLIST_ID 
			AND B.VERSION_ID = (SELECT DISTINCT(VERSION_ID) FROM MI_CMS_INFO_PLAYLIST_VERSION WHERE PLAYLIST_ID = B.PLAYLIST_ID AND IS_ACTIVE = 'Y')
	</select>
	
	<select id="getPlaylistListUsingContent" resultType="map">
		SELECT
		DISTINCT(B.PLAYLIST_ID), B.PLAYLIST_NAME, B.PLAYLIST_TYPE
		FROM
		MI_CMS_MAP_PLAYLIST_CONTENT A,
		MI_CMS_INFO_PLAYLIST B
		WHERE
		A.PLAYLIST_ID = B.PLAYLIST_ID AND A.CONTENT_ID = #{contentId}
		/* Issue
		00153719 */
		UNION
		SELECT DISTINCT(C.PLAYLIST_ID), C.PLAYLIST_NAME,
		C.PLAYLIST_TYPE
		FROM MI_TAG_MAP_CONTENT A /* 컨텐츠_태그 매핑 */
		,MI_CMS_MAP_PLAYLIST_TAG B /* 플레이리스트_태그 매핑 */
		,MI_CMS_INFO_PLAYLIST C
		/* 플레이리스트 기본 */
		WHERE A.CONTENT_ID = #{contentId}
		AND B.TAG_ID =
		A.TAG_ID
		AND C.PLAYLIST_ID = B.PLAYLIST_ID
	</select>

	<select id="getPlaylistVersionListUsingContent" resultType="map">
		SELECT
		DISTINCT(VERSION_ID)
		FROM
		MI_CMS_MAP_PLAYLIST_CONTENT
		WHERE
		PLAYLIST_ID = #{playlistId} AND CONTENT_ID = #{contentId}
	</select>

	<update id="setAdsContentActiveVersion">
		UPDATE MI_CMS_INFO_ADS_SETTING SET IS_ACTIVE =
		'Y' WHERE CONTENT_ID = #{contentId} AND VERSION_ID = #{versionId}
	</update>

	<update id="setVersionContentActive">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET IS_ACTIVE =
		'Y' WHERE CONTENT_ID = #{contentId} AND VERSION_ID = #{versionId}
	</update>

	<select id="getLitePlaylistListUsingContent" resultType="map">
		SELECT
		DISTINCT(B.PLAYLIST_ID), B.PLAYLIST_NAME
		FROM
		MI_CMS_MAP_LITE_PLAYLIST_CONTENT A,
		MI_CMS_INFO_LITE_PLAYLIST B
		WHERE
		A.PLAYLIST_ID = B.PLAYLIST_ID AND A.CONTENT_ID = #{contentId}
	</select>

	<select id="getContentMaxVer" resultType="long">
		SELECT MAX(VERSION_ID)
		FROM MI_CMS_INFO_CONTENT_VERSION WHERE CONTENT_ID = #{contentId}
	</select>

	<select id="getContentActiveVer" resultType="long">
		SELECT VERSION_ID
		FROM MI_CMS_INFO_CONTENT_VERSION WHERE CONTENT_ID = #{contentId} AND
		IS_ACTIVE = 'Y'
	</select>

	<update id="setIsdeleteContent">
		UPDATE MI_CMS_INFO_CONTENT SET IS_DELETED = 'Y' WHERE
		CONTENT_ID = #{contentId}
	</update>

	<update id="setRootGroupToMapContentGroup">
		UPDATE MI_CMS_MAP_GROUP_CONTENT SET GROUP_ID =
		${groupId} WHERE CONTENT_ID = #{contentId}
	</update>

	<update id="setContentUnlockBySessionID">
		UPDATE MI_CMS_INFO_CONTENT SET SESSION_ID = NULL
		WHERE SESSION_ID = #{sessionId}
	</update>

	<update id="restoreContent">
		UPDATE MI_CMS_INFO_CONTENT SET IS_DELETED = 'N' WHERE
		CONTENT_ID = #{contentId}
	</update>

	<delete id="deleteContentCompletely">
		DELETE FROM MI_CMS_INFO_CONTENT WHERE CONTENT_ID =
		#{contentId}
	</delete>

	<update id="setContentLock">
		UPDATE MI_CMS_INFO_CONTENT SET SESSION_ID =
		#{sessionId} WHERE CONTENT_ID = #{contentId} AND SESSION_ID IS NULL
	</update>

	<delete id="deleteAllContentLockData">
		UPDATE MI_CMS_INFO_CONTENT SET SESSION_ID = NULL
	</delete>

	<delete id="deleteAllPlaylistLockData">
		UPDATE MI_CMS_INFO_PLAYLIST SET SESSION_ID = NULL
	</delete>

	<update id="setContentGroup">
		UPDATE MI_CMS_MAP_GROUP_CONTENT SET GROUP_ID =
		#{groupId} WHERE CONTENT_ID = #{contentId}
	</update>

	<update id="setContentShare">
		UPDATE MI_CMS_INFO_CONTENT SET SHARE_FLAG =
		#{shareFlag} WHERE CONTENT_ID = #{contentId}
	</update>

	<update id="setContentMetaData">
		UPDATE MI_CMS_INFO_CONTENT SET CONTENT_META_DATA =
		#{metaData} WHERE CONTENT_ID = #{contentId}
	</update>

	<delete id="deleteFile">
		DELETE FROM MI_CMS_INFO_FILE WHERE FILE_ID =
		#{fileId}
	</delete>

	<delete id="deleteFileInfoIfNoExistFile">
		DELETE FROM MI_CMS_INFO_FILE WHERE FILE_ID =
		#{fileId} AND <![CDATA[ CREATE_DATE < (now() - interval '1 days')]]>
	</delete>

	<delete id="deleteFileInfoIfNoExistFile" databaseId="mssql">
		DELETE FROM MI_CMS_INFO_FILE WHERE FILE_ID =
		#{fileId} AND <![CDATA[ CREATE_DATE < DATEADD(DD, -1, GETDATE())]]>
	</delete>

	<update id="setFileHashCode">
		UPDATE MI_CMS_INFO_FILE SET HASH_CODE = #{hashCode}
		WHERE FILE_ID = #{fileId}
	</update>

	<update id="setFilePath">
		UPDATE MI_CMS_INFO_FILE SET FILE_PATH = #{filePath}
		WHERE FILE_ID = #{fileId}
	</update>

	<select id="getHashCodeFromContentByFileName"
		resultType="string">
		SELECT HASH_CODE FROM MI_CMS_INFO_FILE WHERE FILE_NAME =
		#{fileName} LIMIT 1
	</select>

	<select id="getHashCodeFromContentByFileName"
		resultType="string" databaseId="mssql">
		SELECT TOP 1 HASH_CODE FROM
		MI_CMS_INFO_FILE WHERE FILE_NAME = #{fileName}
	</select>

	<select id="getHashCodeFromContentByFileNameAndSize"
		resultType="string">
		SELECT HASH_CODE FROM MI_CMS_INFO_FILE WHERE FILE_NAME =
		#{fileName} AND FILE_SIZE = #{fileSize} LIMIT 1
	</select>

	<select id="getHashCodeFromContentByFileNameAndSize"
		resultType="string" databaseId="mssql">
		SELECT TOP 1 HASH_CODE FROM
		MI_CMS_INFO_FILE WHERE FILE_NAME = #{fileName}
		AND FILE_SIZE =
		#{fileSize}
	</select>

	<select id="getRootId" resultType="long">
		SELECT DISTINCT
		GROUP_ID
		FROM
		MI_CMS_INFO_CONTENT_GROUP
		WHERE
		CREATOR_ID = #{userId} AND
		ORGANIZATION_ID = #{organizationId}
		AND P_GROUP_ID = #{ungrouped}
	</select>

	<select id="numberOfExistingGroupName" resultType="int">
		SELECT
		COUNT(GROUP_ID) FROM MI_CMS_INFO_CONTENT_GROUP WHERE GROUP_NAME =
		#{groupName} AND CREATOR_ID = #{userId} AND ORGANIZATION_ID =
		#{organizationId}
	</select>

	<select id="getGroupId" resultType="long">
		SELECT GROUP_ID FROM
		MI_CMS_INFO_CONTENT_GROUP WHERE GROUP_NAME = #{groupName} AND
		CREATOR_ID = #{userId} AND ORGANIZATION_ID = #{organizationId}
	</select>

	<select id="getGroupIdByContentId" resultType="long">
		SELECT GROUP_ID FROM MI_CMS_MAP_GROUP_CONTENT
		<where>
			<if test="contentId != null">
				CONTENT_ID = #{contentId}
			</if>
		</where>
	</select>

	<select id="getGroupName" resultType="string">
		SELECT GROUP_NAME FROM
		MI_CMS_INFO_CONTENT_GROUP WHERE GROUP_ID = #{groupId}
	</select>

	<select id="getGroupInfo"
		resultType="com.samsung.magicinfo.framework.content.entity.Group">
		SELECT
		GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME,
		CREATOR_ID, CREATE_DATE, ORGANIZATION_ID
		FROM
		MI_CMS_INFO_CONTENT_GROUP
		WHERE
		GROUP_ID =
		#{groupId}
	</select>

	<select id="getGroupList"
		resultType="com.samsung.magicinfo.framework.content.entity.Group">
		SELECT
		GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME,
		CREATOR_ID, CREATE_DATE
		FROM
		MI_CMS_INFO_CONTENT_GROUP
		WHERE
		CREATOR_ID =
		#{creatorId} AND ORGANIZATION_ID = #{organizationId}
		ORDER BY
		GROUP_DEPTH, GROUP_NAME, GROUP_ID, P_GROUP_ID
	</select>

    <select id="getOrganizationList"
		resultType="com.samsung.magicinfo.framework.content.entity.Group">
		SELECT * FROM MI_USER_INFO_GROUP WHERE ROOT_GROUP_ID = 1
	</select>

	<insert id="addGroup"
		parameterType="com.samsung.magicinfo.framework.content.entity.Group">
		INSERT INTO MI_CMS_INFO_CONTENT_GROUP (GROUP_ID,
		P_GROUP_ID, GROUP_DEPTH, GROUP_NAME, CREATOR_ID, CREATE_DATE,
		ORGANIZATION_ID)
		VALUES (#{group_id}, #{p_group_id}, #{group_depth},
		#{group_name},
		#{creator_id}, CURRENT_TIMESTAMP , #{organization_id})
	</insert>

	<insert id="addGroupTable"
		parameterType="com.samsung.magicinfo.framework.content.entity.Group">
		INSERT INTO ${table} (GROUP_ID, P_GROUP_ID, GROUP_DEPTH,
		GROUP_NAME, CREATOR_ID, CREATE_DATE, ORGANIZATION_ID)
		VALUES
		(#{group.group_id}, #{group.p_group_id}, #{group.group_depth},
		#{group.group_name}, #{group.creator_id}, CURRENT_TIMESTAMP ,
		#{group.organization_id})
	</insert>

	<select id="isExistDefaultGroup" resultType="int">
		SELECT COUNT(*) from MI_CMS_INFO_CONTENT_GROUP
		WHERE
			CREATOR_ID = #{creator_id} AND
			ORGANIZATION_ID = #{organization_id}
	</select>

	<update id="setGroupInfo"
		parameterType="com.samsung.magicinfo.framework.content.entity.Group">
		UPDATE MI_CMS_INFO_CONTENT_GROUP
		<set>
			<if test="p_group_id != null">
				P_GROUP_ID = #{p_group_id},
			</if>
			<if test="group_name != null and group_name.length() > 0">
				GROUP_NAME = #{group_name},
			</if>
			<if test="group_depth != null">
				GROUP_DEPTH = #{group_depth},
			</if>
		</set>
		WHERE GROUP_ID = #{group_id}
	</update>

	<update id="setGroupInfoTable"
		parameterType="com.samsung.magicinfo.framework.content.entity.Group">
		UPDATE ${table}
		<set>
			<if test="group.p_group_id != null">
				P_GROUP_ID = #{group.p_group_id},
			</if>
			<if
				test="group.group_name != null and group.group_name.length() > 0">
				GROUP_NAME = #{group.group_name},
			</if>
			<if test="group.group_depth != null">
				GROUP_DEPTH = #{group.group_depth},
			</if>
		</set>
		WHERE GROUP_ID = #{group.group_id}
	</update>

	<select id="getCountDeletableGroup" resultType="long">
		SELECT
		COUNT(GROUP_ID) FROM MI_CMS_INFO_CONTENT_GROUP WHERE P_GROUP_ID =
		#{groupId}
	</select>

	<delete id="deleteGroup">
		DELETE FROM MI_CMS_INFO_CONTENT_GROUP WHERE GROUP_ID
		= #{groupId}
	</delete>
	
	<delete id="deleteGroupByCreatorId">
		DELETE FROM MI_CMS_INFO_CONTENT_GROUP WHERE CREATOR_ID
		= #{creatorId}
	</delete>

	<delete id="deleteGroupTable">
		DELETE FROM ${table} WHERE GROUP_ID = #{groupId}
	</delete>

	<select id="getGroupedContentIdList" resultType="map">
		SELECT
		A.CONTENT_ID
		FROM
		MI_CMS_INFO_CONTENT A,
		MI_CMS_INFO_CONTENT_GROUP B,
		MI_CMS_MAP_GROUP_CONTENT C
		WHERE
		B.GROUP_ID = C.GROUP_ID AND
		C.CONTENT_ID = A.CONTENT_ID AND C.GROUP_ID =
		#{groupId} AND IS_DELETED
		='N'
	</select>

	<select id="getChildGroupList"
		resultType="com.samsung.magicinfo.framework.content.entity.Group">
		SELECT *
		FROM
		MI_CMS_INFO_CONTENT_GROUP
		WHERE
		P_GROUP_ID =
		#{groupId} AND CREATOR_ID = #{creatorId} AND ORGANIZATION_ID =
		#{organizationId}
		ORDER BY
		GROUP_NAME ASC
	</select>

	<select id="getChildGroupIdList" resultType="map">
		SELECT GROUP_ID FROM
		MI_CMS_INFO_CONTENT_GROUP WHERE P_GROUP_ID = #{groupId}
	</select>

	<select id="getCmsGroupItems" resultType="map">
		SELECT *
		FROM
		MI_CMS_INFO_CONTENT_GROUP
		WHERE
		P_GROUP_ID = #{pGroupId} AND GROUP_DEPTH
		!= #{groupDepth} AND CREATOR_ID =
		#{creatorId} AND ORGANIZATION_ID =
		#{organizationId}
		ORDER BY
		GROUP_NAME ASC
	</select>

	<select id="getCmsSelfGroupItems" resultType="map">
		SELECT *
		FROM
		MI_CMS_INFO_CONTENT_GROUP
		WHERE
		GROUP_ID = #{groupId} AND GROUP_DEPTH !=
		#{groupDepth} AND CREATOR_ID =
		#{creatorId} AND ORGANIZATION_ID =
		#{organizationId}
		ORDER BY
		GROUP_NAME ASC
	</select>

	<select id="getAllContentByGroupIdForAuth" parameterType="map"
		resultType="map">
		SELECT
		A.CONTENT_ID, VERSION_ID , CONTENT_NAME, B.MEDIA_TYPE,
		B.CREATOR_ID, B.CREATE_DATE,
		A.LAST_MODIFIED_DATE,
		TOTAL_SIZE,
		PLAY_TIME, RESOLUTION, IS_DELETED, IS_ACTIVE, SHARE_FLAG,
		IS_LINEAR_VWL, SCREEN_COUNT, X_COUNT,
		Y_COUNT, X_RANGE, Y_RANGE,
		A.CONTENT_META_DATA , MAIN_FILE_ID, THUMB_FILE_ID,
		SFI_FILE_ID, FILE_NAME
		AS THUMB_FILE_NAME,
		GROUP_NAME, B.IS_STREAMING, B.IS_TEMPLATE
		FROM
		MI_CMS_INFO_CONTENT A,
		MI_CMS_INFO_CONTENT_VERSION B,
		MI_CMS_INFO_FILE
		C,
		MI_CMS_INFO_CONTENT_GROUP D,
		MI_CMS_MAP_GROUP_CONTENT E
		WHERE
		A.CONTENT_ID = B.CONTENT_ID AND C.FILE_ID = B.THUMB_FILE_ID AND
		D.GROUP_ID =
		E.GROUP_ID AND E.CONTENT_ID = A.CONTENT_ID
		AND IS_ACTIVE =
		'Y' AND IS_DELETED = 'N' AND A.CREATOR_ID = #{creatorId}
		AND E.GROUP_ID
		= #{groupId} AND A.ORGANIZATION_ID = #{organizationId}
		AND B.MEDIA_TYPE NOT IN 
		<foreach item="item" index="index" collection="ConstMEDIA_TYPE_FOR_AUTHOR" open="(" separator="," close=")">
			#{item}
		</foreach>
		<choose>
			<when test="deviceType.equals(ConstTYPE_PREMIUM)">
				AND B.DEVICE_TYPE != #{ConstTYPE_SOC}
			</when>
			<when test="deviceType.equals(ConstTYPE_SOC)">
				AND B.DEVICE_TYPE != #{ConstTYPE_PREMIUM}
			</when>
		</choose>
		ORDER BY A.LAST_MODIFIED_DATE DESC
	</select>

	<select id="getMaxDepth" resultType="long">
		SELECT MAX(GROUP_DEPTH) FROM
		${table}
	</select>

	<select id="getDefaultContentGroup"
		resultType="com.samsung.magicinfo.framework.content.entity.Group">
		<bind name="safe_sortOrder"
			value="@com.samsung.common.utils.DaoTools@safeSortOrder(sortType)" />
		SELECT *
		FROM
		${table}
		WHERE
		(GROUP_DEPTH = 0 OR GROUP_DEPTH = 1 ) AND
		CREATOR_ID = #{creatorId} AND
		ORGANIZATION_ID = #{organizationId}
		<if test="!skipId.equals('')">
			AND GROUP_ID != #{skipId}
		</if>
		ORDER BY
		GROUP_DEPTH ASC , GROUP_NAME ${safe_sortOrder}
	</select>

	<select id="getSpecificDepthUserGroupList"
		resultType="com.samsung.magicinfo.framework.content.entity.Group">
		<bind name="safe_sortOrder"
			value="@com.samsung.common.utils.DaoTools@safeSortOrder(sortType)" />
		SELECT *
		FROM
		${table}
		WHERE
		GROUP_DEPTH = #{depth} AND CREATOR_ID =
		#{creatorId} AND ORGANIZATION_ID =
		#{organizationId}
		<if test="!skipId.equals('')">
			AND GROUP_ID != #{skipId}
		</if>
		ORDER BY
		GROUP_DEPTH ASC , GROUP_NAME ${safe_sortOrder}
	</select>

	<update id="updateVwlVersion">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET VWL_VERSION =
		#{vwlVersion} WHERE CONTENT_ID = #{contentId}
	</update>

	<select id="getAllContentListByUser" parameterType="map"
		resultType="com.samsung.magicinfo.framework.content.entity.Content">
		SELECT *
		FROM
		MI_CMS_INFO_CONTENT A,
		MI_CMS_MAP_GROUP_CONTENT B,
		MI_CMS_INFO_CONTENT_VERSION C
		WHERE
		A.CONTENT_ID = B.CONTENT_ID AND
		A.CONTENT_ID = C.CONTENT_ID
		AND C.MEDIA_TYPE NOT IN 
		<foreach item="item" index="index" collection="ConstMEDIA_TYPE_FOR_AUTHOR" open="(" separator="," close=")">
			#{item}
		</foreach>
		AND C.IS_ACTIVE =
		'Y' AND A.IS_DELETED = 'N' AND A.CREATOR_ID =
		#{creatorId}
		<if
			test="(currentUserOrganId != null) and (currentUserOrganId != ConstROOT_GROUP_ID)">
			AND A.ORGANIZATION_ID = #{currentUserOrganId}
		</if>
		<if test="!canReadUnsharedContent">
			AND A.SHARE_FLAG = #{ConstSHARE_FLAG_DEFAULT}
		</if>
	</select>

	<select id="getAllContentListByType" parameterType="map"
		resultType="com.samsung.magicinfo.framework.content.entity.Content">
		SELECT *
		FROM
		MI_CMS_INFO_CONTENT A,
		MI_CMS_MAP_GROUP_CONTENT B,
		MI_CMS_INFO_CONTENT_VERSION C
		WHERE
		A.CONTENT_ID = B.CONTENT_ID AND
		A.CONTENT_ID = C.CONTENT_ID AND C.IS_ACTIVE = 'Y'
		AND A.IS_DELETED =
		'N'
		AND MEDIA_TYPE = #{mediaType}
		<if
			test="currentUserOrganId != null and currentUserOrganId != ConstROOT_GROUP_ID">
			AND A.ORGANIZATION_ID = #{currentUserOrganId}
		</if>
		<choose>
			<when test="byAll">
				<if test="!canReadUnsharedContent">
					AND A.SHARE_FLAG = #{ConstSHARE_FLAG_DEFAULT}
				</if>
			</when>
			<otherwise>
				AND A.CREATOR_ID = #{currentUserId}
			</otherwise>
		</choose>
	</select>

	<update id="updateDatalinkLFDToContentInfo">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET IS_TEMPLATE =
		#{isTemplate}, TEMPLATE_PAGE_COUNT = #{pageCount} WHERE CONTENT_ID =
		#{contentId}
	</update>

	<update id="updateDatalinkLFDToPageCount">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET
		TEMPLATE_PAGE_COUNT = #{pageCount} WHERE CONTENT_ID = #{contentId} AND
		VERSION_ID = #{versionId}
	</update>

	<select id="getFtpContentSettingList" resultType="map">
		SELECT * FROM
		MI_CMS_INFO_FTP_SETTING
	</select>

	<select id="getFtpContentSettingByContentId" resultType="map">
		SELECT *
		FROM MI_CMS_INFO_FTP_SETTING WHERE CONTENT_ID = #{contentId}
	</select>

	<select id="getContentFileInfoByFileId"
		resultType="com.samsung.magicinfo.framework.content.entity.ContentFile">
		SELECT * FROM MI_CMS_INFO_FILE WHERE FILE_ID = #{fileId}
	</select>

	<select id="getFileListByContentId" resultType="map">
		SELECT FILE_ID
		FROM MI_CMS_MAP_VERSION_FILE WHERE CONTENT_ID = #{contentId}
	</select>

	<select id="getFileListByContentIdAndVersion" resultType="map">
		SELECT
		FILE_ID FROM MI_CMS_MAP_VERSION_FILE WHERE CONTENT_ID = #{contentId}
		AND VERSION_ID = #{versionId}
	</select>

	<select id="getContentInfoByContentName"
		resultType="com.samsung.magicinfo.framework.content.entity.Content">
		SELECT * FROM MI_CMS_INFO_CONTENT WHERE CONTENT_NAME =
		#{contentName}
	</select>

	<select id="getVersionInfoByContentId" resultType="long">
		SELECT
		VERSION_ID FROM MI_CMS_INFO_CONTENT_VERSION WHERE CONTENT_ID =
		#{contentId} ORDER BY VERSION_ID
	</select>

	<update id="updateContentVersionInfoWithFileId">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET VERSION_ID =
		#{version}, MAIN_FILE_ID = #{mainFileId} WHERE CONTENT_ID =
		#{contentId}
	</update>

	<select id="getCifsContentSettingList" resultType="map">
		SELECT * FROM
		MI_CMS_INFO_CIFS_SETTING
	</select>

	<select id="getCifsContentSettingByContentId" resultType="map">
		SELECT
		* FROM MI_CMS_INFO_CIFS_SETTING WHERE CONTENT_ID = #{contentId}
	</select>

	<select id="getUrlContentSettingByContentId" resultType="map">
		SELECT *
		FROM MI_CMS_INFO_URL_SETTING WHERE CONTENT_ID = #{contentId}
	</select>

	<select id="getAdsContentSettingByContentId" resultType="map">
		SELECT *
		FROM MI_CMS_INFO_ADS_SETTING WHERE CONTENT_ID = #{contentId}
	</select>

	<update id="updateAdsSettingAsDeleted">
		UPDATE MI_CMS_INFO_ADS_SETTING SET IS_DELETED =
		#{isDeleted} WHERE CONTENT_ID = #{contentId}
	</update>

	<update id="updateAdsSettingContentName">
		UPDATE MI_CMS_INFO_ADS_SETTING SET CONTENT_NAME =
		#{contentName} WHERE CONTENT_ID = #{contentId}
	</update>

	<insert id="addFtpSetting" parameterType="map">
		INSERT
          INTO MI_CMS_INFO_FTP_SETTING(CONTENT_ID  ,CONTENT_NAME ,CREATE_DATE       ,LAST_MODIFIED_DATE    ,SERVER_IP
                                      ,SERVER_PORT ,FTP_USER_ID  ,FTP_USER_PASSWORD ,FTP_PATH              ,REFRESH_INTERVAL
                                      ,IS_SSL      ,IS_DELETED   ,CAN_REFRESH       ,LOGIN_RETRY_MAX_COUNT ,LOGIN_RETRY_COUNT
                                      ,CAN_LOGIN_RETRY)
        VALUES(#{contentId} ,#{ftpContentName} ,CURRENT_TIMESTAMP ,CURRENT_TIMESTAMP     ,#{ftpIP}
              ,#{port}      ,#{ftpLoginId}     ,#{ftpPassword}    ,#{ftpDirectory}       ,#{ftpRefreshInterval}
              ,#{isSsl}     ,'N'               ,#{canRefresh}     ,#{loginRetryMaxCount} ,#{loginRetryCount}
              ,#{canLoginRetry})
	</insert>

	<insert id="addCifsSetting">
		INSERT 
          INTO MI_CMS_INFO_CIFS_SETTING(CONTENT_ID   ,CONTENT_NAME          ,CREATE_DATE       ,LAST_MODIFIED_DATE ,SERVER_IP
                                       ,CIFS_USER_ID ,CIFS_USER_PASSWORD    ,REMOTE_DIR        ,IS_DELETED         ,REFRESH_INTERVAL
                                       ,CAN_REFRESH  ,LOGIN_RETRY_MAX_COUNT ,LOGIN_RETRY_COUNT ,CAN_LOGIN_RETRY)
        VALUES(#{contentId}   ,#{cifsContentName}    ,CURRENT_TIMESTAMP  ,CURRENT_TIMESTAMP ,#{cifsIP}
              ,#{cifsLoginId} ,#{cifsPassword}       ,#{cifsDirectory}   ,'N'               ,#{cifsRefreshInterval}
              ,#{canRefresh}  ,#{loginRetryMaxCount} ,#{loginRetryCount} ,#{canLoginRetry})
	</insert>

	<insert id="addUrlSetting">
		INSERT INTO MI_CMS_INFO_URL_SETTING (CONTENT_ID,
		CONTENT_NAME, URL, CREATE_DATE, LAST_MODIFIED_DATE, IS_DELETED)
		VALUES
		(#{contentId}, #{urlContentName}, #{urlAddress}, CURRENT_TIMESTAMP,
		CURRENT_TIMESTAMP, 'N')
	</insert>

	<update id="updateFtpSettingAsDeleted">
		UPDATE MI_CMS_INFO_FTP_SETTING SET IS_DELETED =
		#{isDeleted} WHERE CONTENT_ID = #{contentId}
	</update>

	<update id="updateCifsSettingAsDeleted">
		UPDATE MI_CMS_INFO_CIFS_SETTING SET IS_DELETED =
		#{isDeleted} WHERE CONTENT_ID = #{contentId}
	</update>

	<update id="updateUrlSettingAsDeleted">
		UPDATE MI_CMS_INFO_URL_SETTING SET IS_DELETED =
		#{isDeleted} WHERE CONTENT_ID = #{contentId}
	</update>

	<select id="getCodeFile" parameterType="map" resultType="string">
		SELECT
		MEDIA_TYPE
		FROM
		MI_CMS_CODE_FILE
		WHERE
		(MEDIA_TYPE =
		#{ConstMEDIA_TYPE_IMAGE} OR MEDIA_TYPE = #{ConstMEDIA_TYPE_MOVIE}
		OR
		MEDIA_TYPE = #{ConstMEDIA_TYPE_FLASH} OR MEDIA_TYPE =
		#{ConstMEDIA_TYPE_OFFICE}
		OR MEDIA_TYPE = #{ConstMEDIA_TYPE_PDF}) AND
		FILE_TYPE = #{fileType}
	</select>

	<select id="getContentIdListByContentFileId" resultType="map">
		SELECT
		CONTENT_ID FROM MI_CMS_INFO_CONTENT_VERSION WHERE MAIN_FILE_ID =
		#{mainFileId} AND IS_ACTIVE = 'Y' ORDER BY CONTENT_ID, VERSION_ID
	</select>

	<select id="countContentForCidMappingOfUploader"
		resultType="int">
		SELECT
		COUNT(A.CONTENT_ID)
		FROM
		MI_CMS_INFO_CONTENT A,
		MI_CMS_INFO_CONTENT_VERSION B
		WHERE
		A.CONTENT_ID = B.CONTENT_ID AND
		A.CONTENT_ID = #{contentId} AND A.CREATOR_ID =
		#{creatorId}
		AND
		A.ORGANIZATION_ID = #{organizationId} AND B.IS_ACTIVE = 'Y'
	</select>

	<select id="getDeletedContentByContentId" resultType="string">
		SELECT
		IS_DELETED FROM MI_CMS_INFO_CONTENT WHERE CONTENT_ID = #{contentId}
	</select>

	<insert id="addMapTemplateContent">
		INSERT INTO MI_CMS_MAP_DLKCONTENT_LFDCONTENT
		(DLK_IDX, DLK_CONTENT_ID, VERSION_ID, CONTENT_TYPE, CONTENT_ID)
		VALUES
		(#{dlkIdx}, #{dlkContentId}, #{versionId} , #{contentType} ,
		#{contentId})
	</insert>

	<select id="getLfdContentIdByDlkContentId" resultType="string">
		SELECT
		CONTENT_ID FROM MI_CMS_MAP_DLKCONTENT_LFDCONTENT WHERE DLK_CONTENT_ID
		= #{dlkContentId} AND CONTENT_TYPE = #{constant} ORDER BY VERSION_ID
		ASC LIMIT 1
	</select>

	<select id="getLfdContentIdByDlkContentId" resultType="string"
		databaseId="mssql">
		SELECT TOP 1 CONTENT_ID FROM
		MI_CMS_MAP_DLKCONTENT_LFDCONTENT WHERE DLK_CONTENT_ID =
		#{dlkContentId} AND CONTENT_TYPE = #{constant} ORDER BY VERSION_ID ASC
	</select>

	<select id="getContentIdListByDlkContentId" resultType="map">
		SELECT
		CONTENT_ID FROM MI_CMS_MAP_DLKCONTENT_LFDCONTENT WHERE DLK_CONTENT_ID
		= #{dlkContentId} AND VERSION_ID = #{versionId} AND CONTENT_TYPE !=
		#{constant}
	</select>

	<select id="countExistTempateContentMapping" resultType="int">
		SELECT
		COUNT(DLK_IDX) FROM MI_CMS_MAP_DLKCONTENT_LFDCONTENT WHERE CONTENT_ID
		= #{contentId} AND CONTENT_TYPE = #{constant}
	</select>

	<update id="updateUsedTemplateByContentId">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET
		IS_USED_TEMPLATE = #{usedTemplate} WHERE CONTENT_ID = #{lfdContentId}
	</update>

	<select id="getContentIdByTemplateThumbnailFileId"
		resultType="string">
		SELECT
		DISTINCT(A.CONTENT_ID)
		FROM
		MI_CMS_INFO_CONTENT A,
		MI_CMS_INFO_CONTENT_VERSION B,
		MI_CMS_MAP_VERSION_FILE D,
		MI_CMS_INFO_FILE E,
		MI_CMS_INFO_CONTENT_GROUP F,
		MI_CMS_MAP_GROUP_CONTENT G
		WHERE
		A.CONTENT_ID = B.CONTENT_ID AND
		D.CONTENT_ID = A.CONTENT_ID
		AND E.FILE_ID = D.FILE_ID AND F.GROUP_ID =
		G.GROUP_ID
		AND E.FILE_NAME = #{fileName} AND B.CREATOR_ID =
		#{creatorId}
	</select>

	<update id="updateAsTemplateByContentId">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET IS_TEMPLATE =
		'Y' WHERE CONTENT_ID = #{contentId} AND CREATOR_ID = #{creatorId}
	</update>

	<update id="updateAsTemplateByContentIdAndVersionId">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET IS_TEMPLATE =
		#{isTemplate} WHERE CONTENT_ID = #{contentId} AND VERSION_ID =
		#{versionId}
	</update>

	<select id="getFtpUserIdByContentId" resultType="string">
		SELECT
		FTP_USER_ID FROM MI_CMS_INFO_FTP_SETTING WHERE CONTENT_ID =
		#{contentId} ORDER BY FTP_USER_ID
	</select>

	<select id="getFtpIpByContentId" resultType="string">
		SELECT SERVER_IP
		FROM MI_CMS_INFO_FTP_SETTING WHERE CONTENT_ID = #{contentId}
	</select>

	<select id="getFtpPathByContentId" resultType="string">
		SELECT FTP_PATH
		FROM MI_CMS_INFO_FTP_SETTING WHERE CONTENT_ID = #{contentId}
	</select>

	<select id="getCifsUserIdByContentId" resultType="string">
		SELECT
		CIFS_USER_ID FROM MI_CMS_INFO_CIFS_SETTING WHERE CONTENT_ID =
		#{contentId}
	</select>

	<select id="getCifsIpByContentId" resultType="string">
		SELECT SERVER_IP
		FROM MI_CMS_INFO_CIFS_SETTING WHERE CONTENT_ID = #{contentId}
	</select>

	<select id="getCifsPathByContentId" resultType="string">
		SELECT REMOTE_DIR
		FROM MI_CMS_INFO_CIFS_SETTING WHERE CONTENT_ID = #{contentId}
	</select>

	<update id="updateContentVersionInfoByContentId">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET TOTAL_SIZE =
		#{totalSize} WHERE CONTENT_ID = #{contentId}
	</update>

	<update id="updateFtpSettingByContentId">
		UPDATE MI_CMS_INFO_FTP_SETTING
           SET CONTENT_NAME          = #{ftpContentName}
              ,LAST_MODIFIED_DATE    = CURRENT_TIMESTAMP
              ,SERVER_IP             = #{ftpIP}
              ,SERVER_PORT           = #{port}
              ,FTP_USER_ID           = #{ftpLoginId}
              ,FTP_USER_PASSWORD     = #{ftpPassword}
              ,FTP_PATH              = #{ftpDirectory}
              ,REFRESH_INTERVAL      = #{ftpRefreshInterval}
              ,CAN_REFRESH           = #{canRefresh}
              ,LOGIN_RETRY_MAX_COUNT = #{loginRetryMaxCount}
              ,LOGIN_RETRY_COUNT     = #{loginRetryCount}
              ,CAN_LOGIN_RETRY       = #{canLoginRetry}
         WHERE CONTENT_ID = #{contentId}
	</update>

	<update id="updateCifsSettingByContentId">
		UPDATE MI_CMS_INFO_CIFS_SETTING
           SET CONTENT_NAME          = #{cifsContentName}
              ,LAST_MODIFIED_DATE    = CURRENT_TIMESTAMP
              ,SERVER_IP             = #{cifsIP}
              ,CIFS_USER_ID          = #{cifsLoginId}
              ,CIFS_USER_PASSWORD    = #{cifsPassword}
              ,REMOTE_DIR            = #{cifsDirectory}
              ,REFRESH_INTERVAL      = #{cifsRefreshInterval}
              ,CAN_REFRESH           = #{canRefresh}
              ,LOGIN_RETRY_MAX_COUNT = #{loginRetryMaxCount}
              ,LOGIN_RETRY_COUNT     = #{loginRetryCount}
              ,CAN_LOGIN_RETRY       = #{canLoginRetry}
         WHERE CONTENT_ID = #{contentId}
	</update>

	<update id="updateUrlSetting">
		UPDATE
		MI_CMS_INFO_URL_SETTING
		SET
		CONTENT_NAME =
		#{urlContentName},
		LAST_MODIFIED_DATE = CURRENT_TIMESTAMP,
		URL =
		#{urlAddress}
		WHERE
		CONTENT_ID = #{contentId}
	</update>

	<select id="selectForDeleteFileByFileNameByContentId"
		resultType="string">
		SELECT
		F.FILE_ID
		FROM
		MI_CMS_INFO_FILE F,
		MI_CMS_MAP_VERSION_FILE M,
		MI_CMS_INFO_CONTENT C
		WHERE
		C.CONTENT_ID =
		M.CONTENT_ID AND F.FILE_ID = M.FILE_ID
		AND C.CONTENT_ID = #{contentId}
		AND F.FILE_NAME = #{fileName}
	</select>

	<delete id="deleteFileByFileNameByContentId">
		DELETE FROM MI_CMS_INFO_FILE WHERE FILE_ID =
		#{fileId}
	</delete>

	<select id="getContentByTemplateId" resultType="map">
		SELECT DISTINCT
		DLK_CONTENT_ID FROM MI_CMS_MAP_DLKCONTENT_LFDCONTENT WHERE CONTENT_ID
		= #{templateId} AND CONTENT_TYPE = #{contentType}
	</select>

	<select id="getDlkContentsIncludedElementContentId"
		resultType="map">
		SELECT DISTINCT DLK_CONTENT_ID FROM
		MI_CMS_MAP_DLKCONTENT_LFDCONTENT WHERE CONTENT_ID = #{contentId} AND
		CONTENT_TYPE = #{contentType}
	</select>

	<insert id="addTemplateElement">
		INSERT INTO MI_CMS_INFO_CONTENT_TEMPLATE_ELEMENT
		(CONTENT_ID, VERSION_ID, PAGE_NO, ELEMENT_NO, ITEM_NO, ELEMENT_NAME,
		ELEMENT_TYPE, ITEM_TYPE, ITEM_NAME, IS_INNER_DATALINK,
		SPLIT_GROUP_ID,
		POSITION_X, POSITION_Y, WIDTH, HEIGHT, SPLIT_GROUP_NAME,
		CHANGE_DURATION, KEEP_LAST_VALUE)
		VALUES (#{contentId}, #{versionId},
		#{temp.page_no}, #{temp.element_no}, #{temp.item_no},
		#{temp.element_name}, #{temp.element_type}, #{temp.item_type},
		#{temp.item_name}, #{temp.is_inner_datalink}, #{temp.split_group_id},
		#{temp.position_x}, #{temp.position_y}, #{temp.width},
		#{temp.height},
		#{temp.split_group_name}, #{temp.change_duration},
		#{temp.keep_last_value})
	</insert>

	<insert id="addTemplateDisplaySize">
		INSERT INTO MI_CMS_INFO_CONTENT_TEMPLATE (CONTENT_ID,
		VERSION_ID, DISPLAY_WIDTH, DISPLAY_HEIGHT)
		VALUES (#{contentId},
		#{versionId}, #{displayWidth}, #{displayHeight})
	</insert>

	<select id="getTemplateDisplaySize"
		resultType="com.samsung.magicinfo.framework.content.entity.TemplateDisplay">
		SELECT
		DISPLAY_WIDTH, DISPLAY_HEIGHT
		FROM
		MI_CMS_INFO_CONTENT_TEMPLATE
		WHERE
		CONTENT_ID = #{contentId} AND
		VERSION_ID = #{versionId}
	</select>

	<select id="getTemplateElementList"
		resultType="com.samsung.magicinfo.framework.content.entity.TemplateElement">
		SELECT *
		FROM
		MI_CMS_INFO_CONTENT_TEMPLATE_ELEMENT
		WHERE
		CONTENT_ID = #{contentId} AND VERSION_ID = #{versionId}
		ORDER BY
		PAGE_NO, SPLIT_GROUP_ID, ELEMENT_NO, ELEMENT_NAME ASC
	</select>

	<select id="getTemplateElementDataList"
		resultType="com.samsung.magicinfo.framework.content.entity.TemplateElementData">
		SELECT *
		FROM
		MI_CMS_INFO_CONTENT_TEMPLATE_ELEMENT_DATA
		WHERE
		DLK_CONTENT_ID = #{contentId} AND DLK_VERSION_ID = #{versionId}
		ORDER
		BY DATA_NO
	</select>

	<insert id="addTemplateElementData">
		INSERT INTO MI_CMS_INFO_CONTENT_TEMPLATE_ELEMENT_DATA
		(LFT_CONTENT_ID, LFT_VERSION_ID, DLK_CONTENT_ID, DLK_VERSION_ID,
		PAGE_NO, ELEMENT_NO, ITEM_NO, DATA_NO, ELEMENT_NAME, INPUT_TYPE,
		ELEMENT_TYPE, ITEM_TYPE, ITEM_NAME, IS_INNER_DATALINK, INPUT_DATA,
		VALUE_LOCATION, CONTENT_SRC, CONTENT_ID, MAIN_TAG, TAG_MATCH_TYPE,
		VIEW_H_V, SERVER_ADDRESS, SUB_TAG, CONVERT_TABLE)
		VALUES
		(#{lftContentId}, #{lftVersionId}, #{dlkContentId}, #{dlkVersionId},
		#{temp.page_no}, #{temp.element_no}, #{temp.item_no}, #{temp.data_no},
		#{temp.element_name}, #{temp.input_type}, #{temp.element_type},
		#{temp.item_type}, #{temp.item_name}, #{temp.is_inner_datalink},
		#{temp.input_data}, #{temp.value_location}, #{temp.content_src},
		#{temp.content_id}, #{temp.main_tag}, #{temp.tag_match_type},
		#{temp.view_h_v}, #{temp.server_address}, #{temp.sub_tag},
		#{temp.convert_table})
	</insert>

	<insert id="addMapVwlDevice">
		INSERT INTO MI_CMS_MAP_MULTI_VWL_DEVICE ( CONTENT_ID,
		DEVICE_GROUP_ID )
		VALUES ( #{content_id}, #{deivce_group_id})
	</insert>

	<update id="setTemplateContentParsed">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET IS_PARSED =
		'Y' WHERE CONTENT_ID = #{contentId} AND VERSION_ID = #{versionId}
	</update>

	<update id="setContentFileSize">
		UPDATE MI_CMS_INFO_FILE SET FILE_SIZE = #{fileSize}
		WHERE FILE_ID = #{fileId}
	</update>

	<update id="setContentTotalSize">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET TOTAL_SIZE =
		#{totalSize} WHERE CONTENT_ID = #{contentId} AND VERSION_ID =
		#{versionId}
	</update>

	<select id="getContentParsingState" resultType="int">
		SELECT
		COUNT(CONTENT_ID) FROM MI_CMS_INFO_CONTENT_VERSION WHERE CONTENT_ID =
		#{contentId} AND IS_PARSED ='Y' AND VERSION_ID IN (SELECT VERSION_ID
		FROM MI_CMS_INFO_CONTENT_VERSION WHERE CONTENT_ID = #{contentId} LIMIT
		1)
	</select>

	<select id="getContentParsingState" resultType="int"
		databaseId="mssql">
		SELECT COUNT(CONTENT_ID) FROM MI_CMS_INFO_CONTENT_VERSION
		WHERE CONTENT_ID = #{contentId} AND IS_PARSED ='Y' AND VERSION_ID IN
		(SELECT TOP 1 VERSION_ID FROM MI_CMS_INFO_CONTENT_VERSION WHERE
		CONTENT_ID = #{contentId})
	</select>

	<select id="getAllTemplateElementDataList" resultType="map">
		SELECT *
		FROM
		MI_CMS_INFO_CONTENT_TEMPLATE_ELEMENT A,
		MI_CMS_INFO_CONTENT_TEMPLATE_ELEMENT_DATA B
		WHERE
		A.CONTENT_ID =
		B.LFT_CONTENT_ID AND A.VERSION_ID = B.LFT_VERSION_ID AND A.PAGE_NO
		=
		B.PAGE_NO
		AND A.ELEMENT_NAME = B.ELEMENT_NAME AND B.DLK_CONTENT_ID =
		#{dlkContentId} AND B.DLK_VERSION_ID = #{dlkVersionId}
		ORDER BY
		A.PAGE_NO, A.SPLIT_GROUP_ID, A.ELEMENT_NO, B.ELEMENT_NO, B.ITEM_NO,
		B.DATA_NO
		ASC
	</select>

	<update id="updateThumbnailIdOfDlkByContentId">
		UPDATE
		MI_CMS_INFO_CONTENT_VERSION
		SET
		THUMB_FILE_ID = (
		SELECT
		THUMB_FILE_ID
		FROM
		MI_CMS_INFO_CONTENT_VERSION
		WHERE
		CONTENT_ID =
		#{templateContentId} AND VERSION_ID = #{versionId}
		)
		WHERE
		CONTENT_ID =
		#{contentId}
	</update>

	<select id="getDlkContentIdByTemplateId" resultType="map">
		SELECT
		DISTINCT DLK_CONTENT_ID
		FROM
		MI_CMS_MAP_DLKCONTENT_LFDCONTENT
		WHERE
		CONTENT_ID = #{templateContentId}
	</select>

	<select id="getFileInfoByContentIdVersionId" resultType="string">
		SELECT
		MAIN_FILE_ID FROM MI_CMS_INFO_CONTENT_VERSION WHERE CONTENT_ID =
		#{contentId} AND VERSION_ID = #{versionId}
	</select>

	<select id="getActiveVersionByContentId" resultType="string">
		SELECT
		VERSION_ID FROM MI_CMS_INFO_CONTENT_VERSION WHERE CONTENT_ID =
		#{contentId} AND IS_ACTIVE = 'Y'
	</select>

	<select id="getMediaTypeByContentId" resultType="string">
		SELECT DISTINCT
		MEDIA_TYPE FROM MI_CMS_INFO_CONTENT_VERSION WHERE CONTENT_ID =
		#{contentId} AND IS_ACTIVE = 'Y'
	</select>

	<update id="updateHashCodeByMainFileId">
		UPDATE MI_CMS_INFO_FILE SET HASH_CODE = #{hashCode},
		FILE_SIZE = #{fileSize} WHERE FILE_ID = #{dlkMainFileId}
	</update>

	<update id="updateVersionAndMainFileIdInContentVersionInfo">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET VERSION_ID =
		#{version}, MAIN_FILE_ID = #{mainFileId} WHERE CONTENT_ID =
		#{contentId}
	</update>

	<select id="getFtpPasswordByContentId" resultType="string">
		SELECT
		FTP_USER_PASSWORD FROM MI_CMS_INFO_FTP_SETTING WHERE CONTENT_ID =
		#{contentId}
	</select>

	<select id="getCifsPasswordByContentId" resultType="string">
		SELECT
		CIFS_USER_PASSWORD FROM MI_CMS_INFO_CIFS_SETTING WHERE CONTENT_ID =
		#{contentId}
	</select>

	<select id="getDlkContentIdByIputDataContentId" resultType="map">
		SELECT DISTINCT DLK_CONTENT_ID FROM
		MI_CMS_INFO_CONTENT_TEMPLATE_ELEMENT_DATA WHERE CONTENT_ID =
		#{iputDataContentId}
	</select>

	<select id="getCreatorIdByContentId" resultType="string">
		SELECT
		CREATOR_ID FROM MI_CMS_INFO_CONTENT WHERE CONTENT_ID = #{contentId}
	</select>

	<select id="getIsReadyForNextFtpThread" resultType="string">
		SELECT
		IS_READY FROM MI_CMS_INFO_FTP_SETTING WHERE CONTENT_ID = #{contentId}
	</select>

	<update id="setIsReadyForNextFtpThread">
		UPDATE MI_CMS_INFO_FTP_SETTING SET IS_READY =
		#{isReady} WHERE CONTENT_ID = #{contentId}
	</update>

	<select id="getIsReadyForNextCifsThread" resultType="string">
		SELECT
		IS_READY FROM MI_CMS_INFO_CIFS_SETTING WHERE CONTENT_ID = #{contentId}
	</select>

	<update id="setIsReadyForNextCifsThread">
		UPDATE MI_CMS_INFO_CIFS_SETTING SET IS_READY =
		#{isReady} WHERE CONTENT_ID = #{contentId}
	</update>

	<select id="getTempContentList"
		resultType="com.samsung.magicinfo.framework.content.entity.Content">
		SELECT
			A.CONTENT_ID, VERSION_ID, CONTENT_NAME, B.MEDIA_TYPE,
			B.CREATOR_ID, B.CREATE_DATE,
			A.LAST_MODIFIED_DATE,
			B.TOTAL_SIZE,
			B.PLAY_TIME, B.RESOLUTION, A.IS_DELETED, B.IS_ACTIVE, A.SHARE_FLAG,
			IS_LINEAR_VWL, SCREEN_COUNT,
			X_COUNT, Y_COUNT, X_RANGE, Y_RANGE,
			A.CONTENT_META_DATA, B.MAIN_FILE_ID,
			B.THUMB_FILE_ID, B.SFI_FILE_ID,
			C.GROUP_NAME, E.FILE_NAME AS MAIN_FILE_NAME, B.IS_STREAMING,
			MAIN_FILE_EXTENSION,
			B.IS_USED_TEMPLATE,
			B.TEMPLATE_PAGE_COUNT
		FROM
			MI_CMS_INFO_CONTENT A,
			MI_CMS_INFO_CONTENT_VERSION B,
			MI_CMS_INFO_CONTENT_GROUP C,
			MI_CMS_MAP_GROUP_CONTENT D,
			MI_CMS_INFO_FILE E
		WHERE
			A.CONTENT_ID = B.CONTENT_ID AND C.GROUP_ID =
			D.GROUP_ID AND D.CONTENT_ID =
			A.CONTENT_ID
			AND E.FILE_ID =
			B.MAIN_FILE_ID AND VERSION_ID = 0
			ORDER BY
			VERSION_ID DESC
	</select>

	<delete id="deleteTempContentCompletely">
		DELETE FROM MI_CMS_INFO_CONTENT WHERE CONTENT_ID =
		#{contentId}
	</delete>

	<update id="setContentContentUnlock">
		UPDATE MI_CMS_INFO_CONTENT SET SESSION_ID = NULL
	</update>

	<update id="setPlaylistContentUnlock">
		UPDATE MI_CMS_INFO_PLAYLIST SET SESSION_ID = NULL
	</update>

	<update id="setContentUnlock">
		UPDATE MI_CMS_INFO_CONTENT SET SESSION_ID = NULL
		WHERE CONTENT_ID = #{contentId} AND SESSION_ID = #{sessionId}
	</update>

	<update id="setIsActive">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET IS_ACTIVE =
		#{isActive} WHERE CONTENT_ID = #{contentId}
	</update>

	<update id="setApprovalStatus">
		UPDATE MI_CMS_INFO_CONTENT SET APPROVAL_STATUS =
		#{approvalStatus}, APPROVAL_OPINION = #{approvalOpinion} WHERE
		CONTENT_ID = #{contentId}
	</update>

	<update id="setTemplateElementDataVersionUp">
		UPDATE MI_CMS_INFO_CONTENT_TEMPLATE_ELEMENT_DATA SET
		DLK_VERSION_ID = #{newDlkVersionId} WHERE DLK_CONTENT_ID =
		#{dlkContentId} AND DLK_VERSION_ID = #{dlkVersionId}
	</update>

	<update id="setMappingDlkContentVersionUp">
		UPDATE MI_CMS_MAP_DLKCONTENT_LFDCONTENT SET
		VERSION_ID = #{newVersionId} WHERE DLK_CONTENT_ID = #{contentId} AND
		VERSION_ID = #{versionId}
	</update>

	<select id="getMediaTypeByDeviceType" resultType="map">
		SELECT
		DISTINCT(MEDIA_TYPE) FROM MI_CMS_MAP_FILE WHERE DEVICE_TYPE =
		#{deviceType}
	</select>

	<select id="getFileTypeByDeviceTypeAndVersion"
		resultType="string">
		SELECT DISTINCT(FILE_TYPE) FROM MI_CMS_MAP_FILE WHERE
		DEVICE_TYPE =
		#{deviceType} AND DEVICE_TYPE_VERSION =
		#{deviceTypeVersion}
	</select>

	<select id="getAllDeviceType" resultType="map">
		SELECT
		DISTINCT(DEVICE_TYPE) FROM MI_CMS_MAP_FILE
	</select>

	<sql id="checkFileTypeByDeviceTypeAndContendID_subselect">
		SELECT
		MAIN_FILE_EXTENSION
		FROM
		MI_CMS_INFO_CONTENT_VERSION
		WHERE
		CONTENT_ID = #{contentId} LIMIT 1
	</sql>

	<sql id="checkFileTypeByDeviceTypeAndContendID_subselect"
		databaseId="mssql">
		SELECT
		TOP 1 MAIN_FILE_EXTENSION
		FROM
		MI_CMS_INFO_CONTENT_VERSION
		WHERE
		CONTENT_ID = #{contentId}
	</sql>

	<select id="checkFileTypeByDeviceTypeAndContendID"
		resultType="string">
		SELECT
		FILE_TYPE
		FROM
		MI_CMS_MAP_FILE
		WHERE
		DEVICE_TYPE = #{deviceType} AND
		DEVICE_TYPE_VERSION = #{deviceTypeVersion}
		AND FILE_TYPE = (
		<include
			refid="checkFileTypeByDeviceTypeAndContendID_subselect" />
		)
	</select>

	<update id="setAMSLastMemory">
		UPDATE MI_CMS_INFO_CONTENT SET GENDER = #{gender},
		AGE = #{age} WHERE CONTENT_ID = #{contentId}
	</update>

	<update id="update_UpdateFileSize">
		UPDATE MI_CMS_INFO_FILE SET FILE_SIZE = #{fileSize}
		WHERE FILE_ID =
		#{fileID}
	</update>

	<update id="update2_UpdateFileSize">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET TOTAL_SIZE =
		#{totalSize} WHERE
		MAIN_FILE_ID = #{fileID}
	</update>

	<update id="setContentActive">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET IS_ACTIVE =
		'Y'
		WHERE CONTENT_ID = #{contentID} AND VERSION_ID = #{versionID}
	</update>

	<update id="setDeviceTypeVersionByMediaType">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET
		DEVICE_TYPE_VERSION =
		#{device_type_version}
		WHERE CONTENT_ID =
		#{content_id}
	</update>

	<update id="updateContentVersionInfoWithFileIdContentIdVersion">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET VERSION_ID =
		#{version} WHERE
		CONTENT_ID = #{contentId}
	</update>

	<update id="updateVwlModelCountInfo">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET
		MODEL_COUNT_INFO = #{modelCountInfo}
		WHERE CONTENT_ID = #{content_id}
		AND IS_ACTIVE = 'Y'
	</update>

	<update id="updateMultiVWL">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET MULTI_VWL =
		'TRUE' WHERE CONTENT_ID =
		#{content_id}
	</update>

	<update id="updateMultiVWL" databaseId="mysql">
		UPDATE
		MI_CMS_INFO_CONTENT_VERSION SET MULTI_VWL = 1 WHERE CONTENT_ID =
		#{content_id}
	</update>

	<select id="getAMSLastMemory" resultType="map">
		SELECT GENDER, AGE FROM
		MI_CMS_INFO_CONTENT WHERE CONTENT_ID = #{contentId}
	</select>

	<select id="isDelete" resultType="string">
		SELECT IS_DELETED FROM
		MI_CMS_INFO_CONTENT WHERE CONTENT_ID = #{contentId}
	</select>

	<select id="getTotalSize" resultType="java.lang.Long">
		SELECT TOTAL_SIZE FROM MI_CMS_INFO_CONTENT_VERSION
		<if test="fileID != null"> WHERE MAIN_FILE_ID = #{fileID} </if>
		ORDER BY CONTENT_ID, VERSION_ID
		LIMIT 1
	</select>

	<select id="getTotalSize" resultType="java.lang.Long"
		databaseId="mssql">
		SELECT TOP 1 TOTAL_SIZE FROM MI_CMS_INFO_CONTENT_VERSION
		<if test="fileID != null"> WHERE FILE_ID = #{fileID} </if>
		ORDER BY CONTENT_ID, VERSION_ID
	</select>

	<select id="getFilePath" resultType="java.lang.String">
		SELECT FILE_PATH FROM MI_CMS_INFO_FILE
		<if test="fileID != null">
			WHERE FILE_ID = #{fileID}
		</if>
		LIMIT 1
	</select>

	<select id="getFilePath" resultType="java.lang.String"
		databaseId="mssql">
		SELECT TOP 1 FILE_PATH FROM MI_CMS_INFO_FILE
		<if test="fileID != null">
			WHERE FILE_ID = #{fileID}
		</if>
	</select>

	<select id="getRoot_GroupId" resultType="java.lang.Long">
		SELECT P_GROUP_ID FROM
		MI_CMS_INFO_CONTENT_GROUP WHERE GROUP_ID = #{groupId}
	</select>

	<select id="getMainFileIdFromContentId"
		resultType="java.lang.String">
		SELECT MAIN_FILE_ID FROM MI_CMS_INFO_CONTENT_VERSION WHERE
		CONTENT_ID =
		#{contentId} LIMIT 1
	</select>

	<select id="getMainFileIdFromContentId"
		resultType="java.lang.String" databaseId="mssql">
		SELECT TOP 1 MAIN_FILE_ID
		FROM MI_CMS_INFO_CONTENT_VERSION WHERE CONTENT_ID
		= #{contentId}
	</select>

	<select id="getModelCountInfo" resultType="java.lang.String">
		SELECT
		MODEL_COUNT_INFO FROM MI_CMS_INFO_CONTENT_VERSION WHERE CONTENT_ID =
		#{content_id} LIMIT 1
	</select>

	<select id="getModelCountInfo" resultType="java.lang.String"
		databaseId="mssql">
		SELECT TOP 1 MODEL_COUNT_INFO FROM
		MI_CMS_INFO_CONTENT_VERSION WHERE
		CONTENT_ID = #{content_id}
	</select>

	<select id="getMappingDevicesFilePath"
		resultType="java.util.Map">
		SELECT FILE_NAME, FILE_PATH FROM MI_CMS_INFO_FILE
		WHERE
		FILE_ID = (SELECT MAIN_FILE_ID FROM MI_CMS_INFO_CONTENT_VERSION WHERE
		CONTENT_ID = #{content_id} LIMIT 1)
		LIMIT 1
	</select>

	<select id="getMappingDevicesFilePath"
		resultType="java.util.Map" databaseId="mssql">
		SELECT TOP 1 FILE_NAME,
		FILE_PATH FROM MI_CMS_INFO_FILE
		WHERE FILE_ID = (SELECT TOP 1
		MAIN_FILE_ID FROM MI_CMS_INFO_CONTENT_VERSION
		WHERE CONTENT_ID =
		#{content_id})
	</select>

	<select id="getMappingDeviceInformation"
		resultType="java.util.Map">
		SELECT MEDIA_TYPE, IS_LINEAR_VWL, X_COUNT, Y_COUNT FROM
		MI_CMS_INFO_CONTENT_VERSION
		WHERE CONTENT_ID = #{contentID} LIMIT 1
	</select>

	<select id="getMappingDeviceInformation"
		resultType="java.util.Map" databaseId="mssql">
		SELECT TOP 1 MEDIA_TYPE,
		IS_LINEAR_VWL, X_COUNT, Y_COUNT FROM
		MI_CMS_INFO_CONTENT_VERSION
		WHERE
		CONTENT_ID = #{contentID}
	</select>

	<select id="getMediaTypeByDeviceTypeAndVersion"
		resultType="java.util.Map">
		SELECT DISTINCT(MEDIA_TYPE) FROM MI_CMS_MAP_FILE
		WHERE
		DEVICE_TYPE = #{deviceType} AND DEVICE_TYPE_VERSION =
		#{deviceTypeVersion}
	</select>

	<select id="getDeviceGroupListByMultiVwlContentId"
		resultType="java.util.Map">
		SELECT DEVICE_GROUP_ID FROM MI_CMS_MAP_MULTI_VWL_DEVICE
		WHERE CONTENT_ID =
		#{content_id}
	</select>

	<select id="getContentPriority" resultType="java.util.Map">
		SELECT DEVICE_TYPE,
		DEVICE_TYPE_VERSION FROM MI_CMS_INFO_CONTENT_VERSION
		WHERE CONTENT_ID =
		#{content_id} LIMIT 1
	</select>

	<select id="getContentPriority" resultType="java.util.Map"
		databaseId="mssql">
		SELECT TOP 1 DEVICE_TYPE, DEVICE_TYPE_VERSION FROM
		MI_CMS_INFO_CONTENT_VERSION
		WHERE CONTENT_ID = #{content_id}
	</select>

	<select id="getContentIdListByFileAndContentIds"
		resultType="java.util.Map">
		select content_id from mi_cms_info_content_version where
		main_file_id =
		#{mainFileId} and is_active = 'Y' and content_id !=
		#{content_id}
	</select>

	<select id="isUsedAfterVersion" resultType="boolean">
		select
		count(content_id) from mi_cms_info_content_version where main_file_id
		= #{mainFileId} and version_id > #{versionId}
	</select>

	<select id="isUsedThumbAfterVersion" resultType="int">
		select
		count(content_id) from mi_cms_info_content_version where thumb_file_id
		= #{thumbFileId} and version_id > #{versionId}
	</select>


	<select id="setDeleteLock">
		UPDATE MI_CMS_INFO_FILE SET DELETE_LOCK =
		#{deleteLock} WHERE FILE_ID = #{fileID}
	</select>

	<select id="getDeleteLock" resultType="java.lang.String">
		SELECT DELETE_LOCK FROM MI_CMS_INFO_FILE
		<if test="value != null">
			WHERE FILE_ID = #{fileID}
		</if>
	</select>

	<update id="setVersionId">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET VERSION_ID =
		#{newVersionId} WHERE CONTENT_ID = #{contentId} AND VERSION_ID =
		#{oldVersionId}
	</update>

	<delete id="deleteContentFromDLK">
		DELETE FROM MI_CMS_INFO_CONTENT_TEMPLATE_ELEMENT_DATA
		WHERE CONTENT_ID = #{contentId}
	</delete>

	<select id="getPlaylistInfoUsingContent" resultType="map">
		SELECT
		B.PLAYLIST_ID, B.VERSION_ID, B.PLAY_TIME, SUM(A.CONTENT_DURATION) AS CONTENT_DURATION
		FROM
		MI_CMS_MAP_PLAYLIST_CONTENT A,
		MI_CMS_INFO_PLAYLIST_VERSION B
		WHERE
		A.PLAYLIST_ID = B.PLAYLIST_ID
		AND
		A.VERSION_ID = B.VERSION_ID
		AND
		A.CONTENT_ID = #{contentId}
		GROUP BY B.PLAYLIST_ID,B.VERSION_ID, B.PLAY_TIME
	</select>

	<select id="getLitePlaylistInfoUsingContent" resultType="map">
		SELECT
		B.PLAYLIST_ID, B.VERSION_ID
		FROM
		MI_CMS_MAP_LITE_PLAYLIST_CONTENT A,
		MI_CMS_INFO_LITE_PLAYLIST_VERSION B
		WHERE
		A.PLAYLIST_ID = B.PLAYLIST_ID
		AND
		A.VERSION_ID = B.VERSION_ID
		AND
		A.CONTENT_ID = #{contentId}

	</select>

	<delete id="deleteContentIdFromDlkContentIdMap">
		DELETE FROM MI_CMS_MAP_DLKCONTENT_LFDCONTENT WHERE
		CONTENT_ID = #{contentId}
	</delete>

	<select id="getLastestDlkDataVersionId" resultType="long">
		SELECT
		MAX(DLK_VERSION_ID)
		FROM
		MI_CMS_INFO_CONTENT_TEMPLATE_ELEMENT_DATA
		WHERE
		DLK_CONTENT_ID = #{contentId}
	</select>

	<delete id="deleteOldVersionContent">
		DELETE FROM MI_CMS_INFO_CONTENT_VERSION WHERE
		CONTENT_ID = #{contentId} AND VERSION_ID != #{maxVersionId}
	</delete>

	<select id="isExistMapVersionfile" resultType="int">
		SELECT
		COUNT(CONTENT_ID) FROM MI_CMS_MAP_VERSION_FILE WHERE CONTENT_ID =
		#{contentId} AND VERSION_ID = #{versionId} AND FILE_ID=#{fileId}
	</select>


	<select id="getContentInfoByContentIdAndVersion"
		resultType="map">
		SELECT * FROM MI_CMS_INFO_CONTENT_VERSION WHERE CONTENT_ID
		= #{contentId} AND VERSION_ID = #{versionId}
	</select>

	<select id="getUsedContentCount" resultType="int">
		SELECT COUNT(DISTINCT CONTENT.CONTENT_ID)
		FROM MI_CMS_INFO_CONTENT
		CONTENT
		LEFT JOIN MI_CDS_INFO_SCHEDULE SCHEDULE ON CONTENT.CONTENT_ID =
		SCHEDULE.CONTENT_ID
		WHERE
		<if test="organizationId != 0">
			CONTENT.ORGANIZATION_ID = ${organizationId} AND
		</if>
		SCHEDULE.CONTENT_ID IS NOT NULL
	</select>

	<select id="getUnapprovedContentCnt" resultType="int">
		SELECT COUNT(DISTINCT CONTENT_ID)
		FROM MI_CMS_INFO_CONTENT A,
		     MI_USER_INFO_USER B
		WHERE
			B.USER_ID = A.CREATOR_ID AND
			B.IS_DELETED = 'N' AND
			A.APPROVAL_STATUS='UNAPPROVED' AND
			A.IS_DELETED='N'
		<if test="organizationId != null and organizationId != 0">
			AND ORGANIZATION_ID = ${organizationId}
		</if>
	</select>

	<select id="getRejectCnt" resultType="int">
		SELECT COUNT(CONTENT_ID)
		FROM MI_CMS_INFO_CONTENT
		WHERE
		APPROVAL_STATUS='REJECTED' AND IS_DELETED='N'
		<if test="organizationId != null and organizationId != 0">
			AND ORGANIZATION_ID = ${organizationId}
		</if>
	</select>

	<select id="getcontentIdfromTag" resultType="java.util.Map">
		SELECT DISTINCT ON (CONTENT_ID) CONTENT_ID
		FROM
		MI_TAG_MAP_CONTENT A,
		MI_TAG_INFO_TAG B
		WHERE
		A.TAG_ID = B.TAG_ID
		<choose>
			<when
				test="tagInputType != null and tagInputType.equalsIgnoreCase('NAME')">
				<foreach item="tag" index="index" collection="tagList"
					open=" AND (" separator=" OR " close=")">
					B.TAG_NAME = #{tag}
				</foreach>
			</when>
			<otherwise>
				<foreach item="tag" index="index" collection="tagList"
					open=" AND (" separator=" OR " close=")">
					A.TAG_ID = #{tag} :: Integer
				</foreach>
			</otherwise>
		</choose>
	</select>

	<select id="getcontentIdfromTag" resultType="java.util.Map"
		databaseId="mssql">
		SELECT DISTINCT CONTENT_ID FROM
		MI_TAG_MAP_CONTENT A,
		MI_TAG_INFO_TAG B
		WHERE
		A.TAG_ID = B.TAG_ID
		<choose>
			<when
				test="tagInputType != null and tagInputType.equalsIgnoreCase('NAME')">
				<foreach item="tag" index="index" collection="tagList"
					open=" AND (" separator=" OR " close=")">
					B.TAG_NAME = #{tag}
				</foreach>
			</when>
			<otherwise>
				<foreach item="tag" index="index" collection="tagList"
					open=" AND (" separator=" OR " close=")">
					A.TAG_ID = #{tag}
				</foreach>
			</otherwise>
		</choose>
	</select>

	<select id="getcontentIdfromCategory" resultType="java.util.Map">
		SELECT DISTINCT ON (CONTENT_ID) CONTENT_ID FROM
		MI_CATEGORY_MAP_CONTENT
		WHERE
		<foreach item="item" index="index" collection="categoryList"
			open="" separator=" OR " close="">
			GROUP_ID = #{item} :: Integer
		</foreach>
	</select>

	<select id="getcontentIdfromCategory" resultType="java.util.Map"
		databaseId="mssql">
		SELECT DISTINCT CONTENT_ID CONTENT_ID FROM MI_CATEGORY_MAP_CONTENT
		WHERE
		<foreach item="item" index="index" collection="categoryList"
			open="" separator=" OR " close="">
			GROUP_ID = #{item}
		</foreach>
	</select>

	<select id="getCategorywithTagFilter" resultType="java.util.Map">
		SELECT DISTINCT ON (CATEGORY.CONTENT_ID) CATEGORY.CONTENT_ID
		FROM
		MI_CATEGORY_MAP_CONTENT CATEGORY
		LEFT JOIN MI_TAG_MAP_CONTENT TAG ON
		CATEGORY.CONTENT_ID=TAG.CONTENT_ID
		LEFT JOIN MI_TAG_INFO_TAG TAG_INFO
		ON
		TAG_INFO.TAG_ID=TAG.TAG_ID
		WHERE
		<foreach item="item" index="index" collection="categoryList"
			open="" separator=" OR " close="">
			CATEGORY.GROUP_ID = #{item} :: Integer
		</foreach>
		<choose>
			<when
				test="tagInputType != null and tagInputType.equalsIgnoreCase('NAME')">
				<foreach item="tag" index="index" collection="tagList"
					open=" AND (" separator=" OR " close=")">
					TAG_INFO.TAG_NAME = #{tag}
				</foreach>
			</when>
			<otherwise>
				<foreach item="tag" index="index" collection="tagList"
					open=" AND (" separator=" OR " close=")">
					TAG.TAG_ID = #{tag} :: Integer
				</foreach>
			</otherwise>
		</choose>
	</select>

	<select id="getCategorywithTagFilter" resultType="java.util.Map"
		databaseId="mssql">
		SELECT DISTINCT CATEGORY.CONTENT_ID
		FROM MI_CATEGORY_MAP_CONTENT
		CATEGORY
		LEFT JOIN MI_TAG_MAP_CONTENT TAG ON
		CATEGORY.CONTENT_ID=TAG.CONTENT_ID
		LEFT JOIN MI_TAG_INFO_TAG TAG_INFO
		ON
		TAG_INFO.TAG_ID=TAG.TAG_ID
		WHERE
		<foreach item="item" index="index" collection="categoryList"
			open="" separator=" OR " close="">
			CATEGORY.GROUP_ID = #{item}
		</foreach>
		<choose>
			<when
				test="tagInputType != null and tagInputType.equalsIgnoreCase('NAME')">
				<foreach item="tag" index="index" collection="tagList"
					open=" AND (" separator=" OR " close=")">
					TAG_INFO.TAG_NAME = #{tag}
				</foreach>
			</when>
			<otherwise>
				<foreach item="tag" index="index" collection="tagList"
					open=" AND (" separator=" OR " close=")">
					TAG.TAG_ID = #{tag}
				</foreach>
			</otherwise>
		</choose>
	</select>


	<select id="getContentListWithThumbnailFromTagNumber"
		resultType="java.util.Map">
		SELECT DISTINCT THUMBNAILS.*
		FROM MI_TAG_MAP_CONTENT CONTENT
		LEFT JOIN
		MI_TAG_INFO_TAG_CONDITION
		TAG_CONDITION ON CONTENT.TAG_CONDITION_ID =
		TAG_CONDITION.TAG_CONDITION_ID
		LEFT JOIN (
		SELECT
		A.THUMB_FILE_ID,
		B.FILE_NAME AS THUMB_FILE_NAME, A.CONTENT_ID, C.CONTENT_NAME,
		A.MEDIA_TYPE, A.VERSION_ID, A.CREATE_DATE, A.VERSION_ID, A.RESOLUTION,
		A.CREATOR_ID
		FROM
		MI_CMS_INFO_CONTENT_VERSION A,
		MI_CMS_INFO_FILE B,
		MI_CMS_INFO_CONTENT C
		WHERE
		A.IS_ACTIVE = 'Y' AND B.FILE_ID =
		A.THUMB_FILE_ID
		AND A.CONTENT_ID = C.CONTENT_ID
		ORDER BY CONTENT_NAME
		)
		THUMBNAILS ON THUMBNAILS.CONTENT_ID =
		CONTENT.CONTENT_ID
		WHERE
		CONTENT.TAG_ID = #{tagId}

		<if
			test="(tagConditionEqual != null and tagConditionEqual.length > 0) or (tagConditionUp != null and tagConditionUp.length > 0) or (tagConditionDown != null and tagConditionDown.length > 0)">
			and (
			<choose>
				<when
					test="(tagConditionUp != null and tagConditionUp.length > 0) or (tagConditionDown != null and tagConditionDown.length > 0)">
					<if test="tagConditionUp != null and tagConditionUp.length > 0">
						<foreach item="item" index="index"
							collection="tagConditionUp" open=" (" separator=" OR " close=")">
							TAG_CONDITION <![CDATA[<]]>
							#{item}::VARCHAR
						</foreach>
					</if>
					<if
						test="tagConditionDown != null and tagConditionDown.length > 0">
						<foreach item="item" index="index"
							collection="tagConditionDown" open=" AND (" separator=" OR "
							close=")">
							TAG_CONDITION <![CDATA[>]]>
							#{item}::VARCHAR
						</foreach>
					</if>
					<if
						test="tagConditionEqual != null and tagConditionEqual.length > 0">
						<foreach item="item" index="index"
							collection="tagConditionEqual" open=" OR (" separator=" OR "
							close=")">
							TAG_CONDITION = #{item}::VARCHAR
						</foreach>
					</if>
				</when>
				<otherwise>
					<if
						test="tagConditionEqual != null and tagConditionEqual.length > 0">
						<foreach item="item" index="index"
							collection="tagConditionEqual" open=" (" separator=" OR "
							close=")">
							TAG_CONDITION = #{item}::VARCHAR
						</foreach>
					</if>
				</otherwise>

				<!-- <if test="(tagConditionEqual != null and tagConditionEqual.length 
					> 0) and ((tagConditionUp != null and tagConditionUp.length < 1) and (tagConditionDown 
					!= null and tagConditionDown.length < 1))"> <foreach item="item" index="index" 
					collection="tagConditionEqual" open=" (" separator=" OR " close=")"> TAG_CONDITION 
					= #{item}::VARCHAR </foreach> </if> -->
			</choose>
			)
		</if>


		<!-- <if test="(tagConditionEqual != null and tagConditionEqual.length 
			> 0) or (tagConditionUp != null and tagConditionUp.length > 0) or (tagConditionDown 
			!= null and tagConditionDown.length > 0)"> AND ( <if test="tagConditionEqual 
			!= null and tagConditionEqual.length > 0"> <foreach item="item" index="index" 
			collection="tagConditionEqual" open=" (" separator=" OR " close=")"> TAG_CONDITION 
			= #{item}::VARCHAR </foreach> </if> <if test="tagConditionUp != null and 
			tagConditionUp.length > 0"> <foreach item="item" index="index" collection="tagConditionUp" 
			open=" (" separator=" OR " close=")"> TAG_CONDITION <![CDATA[<]]> #{item}::VARCHAR 
			</foreach> </if> <if test="tagConditionDown != null and tagConditionDown.length 
			> 0"> <foreach item="item" index="index" collection="tagConditionDown" open=" 
			(" separator=" OR " close=")"> TAG_CONDITION <![CDATA[>]]> #{item}::VARCHAR 
			</foreach> </if> ) </if> -->
	</select>


	<select id="getContentListWithThumbnailFromTagId"
		resultType="java.util.Map">
		SELECT DISTINCT THUMBNAILS.*
		FROM MI_TAG_MAP_CONTENT CONTENT
		LEFT JOIN (
		SELECT
		A.THUMB_FILE_ID, B.FILE_NAME AS THUMB_FILE_NAME,
		A.CONTENT_ID, C.CONTENT_NAME, C.EXPIRATION_DATE, A.PLAY_TIME,
		C.LAST_MODIFIED_DATE, A.MEDIA_TYPE, A.VERSION_ID,
		A.CREATE_DATE, A.VERSION_ID, A.RESOLUTION,
		A.CREATOR_ID
		FROM
		MI_CMS_INFO_CONTENT_VERSION A,
		MI_CMS_INFO_FILE B,
		MI_CMS_INFO_CONTENT C
		WHERE
		A.IS_ACTIVE = 'Y' AND B.FILE_ID = A.THUMB_FILE_ID
		AND A.CONTENT_ID
		= C.CONTENT_ID
		ORDER BY CONTENT_NAME
		) THUMBNAILS ON
		THUMBNAILS.CONTENT_ID =
		CONTENT.CONTENT_ID
		WHERE CONTENT.TAG_ID =
		#{tagId}
	</select>

	<select id="getContentListWithThumbnailFromTagId"
		resultType="java.util.Map" databaseId="mssql">
		SELECT DISTINCT THUMBNAILS.*
		FROM MI_TAG_MAP_CONTENT CONTENT
		LEFT JOIN (
		SELECT
		A.THUMB_FILE_ID,
		B.FILE_NAME AS THUMB_FILE_NAME, A.CONTENT_ID, C.CONTENT_NAME,
		C.EXPIRATION_DATE, A.PLAY_TIME,
		A.MEDIA_TYPE, A.VERSION_ID, A.CREATE_DATE, A.RESOLUTION, A.CREATOR_ID
		FROM
		MI_CMS_INFO_CONTENT_VERSION A,
		MI_CMS_INFO_FILE B,
		MI_CMS_INFO_CONTENT C
		WHERE
		A.IS_ACTIVE = 'Y' AND B.FILE_ID =
		A.THUMB_FILE_ID
		AND A.CONTENT_ID = C.CONTENT_ID

		) THUMBNAILS ON
		THUMBNAILS.CONTENT_ID = CONTENT.CONTENT_ID
		WHERE CONTENT.TAG_ID =
		#{tagId}
		ORDER BY THUMBNAILS.CONTENT_NAME
	</select>

	<select id="getContentListWithThumbnailFromTagBoolean"
		resultType="java.util.Map" databaseId="mssql">
		SELECT DISTINCT THUMBNAILS.*
		FROM MI_TAG_MAP_CONTENT CONTENT
		LEFT JOIN (
		SELECT
		A.THUMB_FILE_ID,
		B.FILE_NAME AS THUMB_FILE_NAME, A.CONTENT_ID, C.CONTENT_NAME,
		A.MEDIA_TYPE, A.VERSION_ID, A.CREATE_DATE, A.RESOLUTION, A.CREATOR_ID
		FROM
		MI_CMS_INFO_CONTENT_VERSION A,
		MI_CMS_INFO_FILE B,
		MI_CMS_INFO_CONTENT C
		WHERE
		A.IS_ACTIVE = 'Y' AND B.FILE_ID =
		A.THUMB_FILE_ID
		AND A.CONTENT_ID = C.CONTENT_ID

		) THUMBNAILS ON
		THUMBNAILS.CONTENT_ID = CONTENT.CONTENT_ID
		WHERE CONTENT.TAG_ID =
		#{tagId} AND CONTENT.TAG_CONDITION_ID =
		#{tagConditionId}
		ORDER BY
		THUMBNAILS.CONTENT_NAME
	</select>

	<select id="getContentListWithThumbnailFromTagBoolean"
		resultType="java.util.Map">
		SELECT DISTINCT THUMBNAILS.*
		FROM MI_TAG_MAP_CONTENT CONTENT
		LEFT JOIN (
		SELECT
		A.THUMB_FILE_ID, B.FILE_NAME AS THUMB_FILE_NAME,
		A.CONTENT_ID, C.CONTENT_NAME,
		A.MEDIA_TYPE, A.VERSION_ID,
		A.CREATE_DATE, A.VERSION_ID, A.RESOLUTION,
		A.CREATOR_ID
		FROM
		MI_CMS_INFO_CONTENT_VERSION A,
		MI_CMS_INFO_FILE B,
		MI_CMS_INFO_CONTENT C
		WHERE
		A.IS_ACTIVE = 'Y' AND B.FILE_ID = A.THUMB_FILE_ID
		AND A.CONTENT_ID
		= C.CONTENT_ID
		ORDER BY CONTENT_NAME
		) THUMBNAILS ON
		THUMBNAILS.CONTENT_ID =
		CONTENT.CONTENT_ID AND CONTENT.TAG_CONDITION_ID
		= #{tagConditionId}
		WHERE CONTENT.TAG_ID = #{tagId}
	</select>

	<select id="getTagContentList"
		resultType="com.samsung.magicinfo.framework.content.entity.Content">
		SELECT DISTINCT CONTENT.*
		FROM MI_TAG_MAP_CONTENT TAG
		LEFT
		JOIN MI_CMS_INFO_CONTENT CONTENT ON CONTENT.CONTENT_ID =
		TAG.CONTENT_ID
		WHERE TAG.TAG_ID = ${tagId}
	</select>
	<select id="getTagFromContentId"
		resultType="com.samsung.magicinfo.framework.setup.entity.TagEntity">
		SELECT DISTINCT MAP.CONTENT_ID, TAG.*
		FROM
		MI_TAG_MAP_CONTENT MAP
		LEFT JOIN MI_TAG_INFO_TAG TAG ON MAP.TAG_ID =
		TAG.TAG_ID
		WHERE MAP.CONTENT_ID = #{contentId}
	</select>

	<select id="getContentApproverListByContentId" resultType="map">
		SELECT USER_ID FROM MI_CMS_MAP_CONTENT_APPROVER WHERE CONTENT_ID =
		#{contentId}
	</select>

	<select id="getContentApproverInfoByUserId" resultType="map">
		SELECT
		CONTENT_ID FROM MI_CMS_MAP_CONTENT_APPROVER WHERE USER_ID = #{userId}
	</select>

	<insert id="addContentApproverMap">
		INSERT INTO MI_CMS_MAP_CONTENT_APPROVER (CONTENT_ID,
		USER_ID) VALUES (#{contentId}, #{userId})
	</insert>

	<delete id="deleteContentApproverMap">
		DELETE FROM MI_CMS_MAP_CONTENT_APPROVER WHERE
		CONTENT_ID = #{contentId} AND USER_ID = #{userId}
	</delete>

	<delete id="deleteContentApproverMapByContentId">
		DELETE FROM MI_CMS_MAP_CONTENT_APPROVER WHERE
		CONTENT_ID = #{contentId}
	</delete>

	<insert id="setThumbnailMap">
		INSERT INTO MI_CMS_MAP_CONTENT_THUMBNAIL (CONTENT_ID,
		FILE_ID, IDX, MODE) VALUES (#{contentId}, #{fileId}, #{idx}, #{mode})
	</insert>

	<select id="getMovieThumbnails" resultType="map">
		SELECT FILES.FILE_ID,
		FILES.FILE_NAME
		FROM MI_CMS_MAP_CONTENT_THUMBNAIL THUMBNAILS
		LEFT JOIN
		MI_CMS_INFO_FILE FILES ON THUMBNAILS.FILE_ID = FILES.FILE_ID
		WHERE
		THUMBNAILS.CONTENT_ID = #{contentId} AND MODE = #{mode}
		ORDER BY IDX
	</select>

	<delete id="deleteContentApproverMapByUserId">
		DELETE FROM MI_CMS_MAP_CONTENT_APPROVER WHERE USER_ID
		= #{userId}
	</delete>

	<select id="getThumbMovieFileList" resultType="java.util.Map">
		SELECT
		FILE_ID
		FROM
		MI_CMS_MAP_CONTENT_THUMBNAIL
		WHERE
		CONTENT_ID = #{contentId}
	</select>

	<delete id="deleteThumbMovieMap">
		DELETE FROM MI_CMS_MAP_CONTENT_THUMBNAIL WHERE
		CONTENT_ID = #{contentId}
	</delete>

	<select id="getThumbIdByMainFileId"
		resultType="java.lang.String">
		SELECT THUMB_FILE_ID FROM MI_CMS_INFO_CONTENT_VERSION WHERE
		MAIN_FILE_ID =
		#{main_file_id} LIMIT 1
	</select>

	<select id="getThumbIdByMainFileId" databaseId="mssql"
		resultType="java.lang.String">
		SELECT TOP 1 THUMB_FILE_ID FROM MI_CMS_INFO_CONTENT_VERSION
		WHERE
		MAIN_FILE_ID = #{main_file_id}
	</select>

	<select id="getTLFDListCnt" resultType="int">
		SELECT COUNT(DISTINCT A.CONTENT_ID)
		<include refid="getTLFDListFromWhere" />
	</select>

	<sql id="getTLFDListFromWhere">

		<include refid="getContentListFrom" />
		<include refid="getContentListWhere" />

		<if test="organizationId != null">
			AND A.ORGANIZATION_ID = #{organizationId}
		</if>
		<choose>
			<when
				test="listType.equalsIgnoreCase(ConstGROUP_TYPE_UNGROUPED)">
				AND D.GROUP_ID = #{groupRootId}

			</when>
			<when test="listType.equalsIgnoreCase(ConstGROUP_TYPE_GROUPED)">
				<if test="groupIDLong != null">
					AND D.GROUP_ID = #{groupIDLong}
				</if>
				<if test="groupIds != null">
					<foreach item="item" index="index" collection="groupIds"
						open=" AND (" separator=" OR " close=")">
						D.GROUP_ID = #{item}
					</foreach>
				</if>
			</when>
			<when test="listType.equalsIgnoreCase(ConstGROUP_TYPE_ORGAN)">
				<include refid="getContentListWhereOrgan" />
			</when>
		</choose>

		<if test="listType.equalsIgnoreCase(ConstGROUP_TYPE_USER)">
			AND A.CREATOR_ID = #{creatorID}
		</if>

		<if
			test="isSelectContent != null and isSelectContent.equalsIgnoreCase('TRUE') and selId != null and selId.length() > 0 and !selId.equalsIgnoreCase('null')">
			AND A.CONTENT_ID = #{selId}
		</if>
		<if test="searchText != null and searchText.length() > 0">
			<bind name="searchText" value="'%' + searchText + '%'" />
			AND (UPPER(A.CONTENT_NAME) LIKE #{searchText} ESCAPE '^')
		</if>
		<if test="searchCreator != null and searchCreator.length() > 0">
			<bind name="searchCreator" value="'%' + searchCreator + '%'" />
			AND (UPPER(B.CREATOR_ID) LIKE #{searchCreator} ESCAPE '^')
		</if>
		<if test="media_type_filter != null">
			<foreach item="item" index="index"
				collection="media_type_filter" open=" AND (" separator=" OR "
				close=")">
				B.MEDIA_TYPE = #{item}
			</foreach>
		</if>
		<if test="searchStartDate != null and searchStartDate.length() > 0">
			<bind name="safe_startDate"
				value="@com.samsung.common.utils.DaoTools@safeDateTimeString(searchStartDate)" />
			AND A.LAST_MODIFIED_DATE &gt; '${safe_startDate}'
		</if>
		<if test="searchEndDate != null and searchEndDate.length() > 0">
			<bind name="safe_endDate"
				value="@com.samsung.common.utils.DaoTools@safeDateTimeString(searchEndDate)" />
			AND A.LAST_MODIFIED_DATE &lt; '${safe_endDate}'
		</if>
		<choose>
			<when test="mediaType != null and mediaType.length() > 0">
				AND B.MEDIA_TYPE = 'TLFD'
			</when>
			<when test="isMain != null and isMain.equals('true')">
			</when>
			<otherwise>
				<choose>
					<when
						test="content_type != null and content_type.length() > 0">
						<choose>
							<when test="content_type.equals(ConstCONTENT_TYPE_CONTENT)">
								AND B.IS_TEMPLATE != 'Y'
							</when>
							<when test="content_type.equals(ConstCONTENT_TYPE_TEMPLATE)">
								AND B.IS_TEMPLATE = 'Y'
							</when>
						</choose>
					</when>
				</choose>
			</otherwise>
		</choose>
 			
		<if test="deviceTypeArr != null">
		
 			AND (B.DEVICE_TYPE = '' OR B.DEVICE_TYPE IS NULL  
 			OR
 			 <foreach item="deviceType" collection="deviceTypeArr" separator=" OR " open="" close="">
                <choose>
                	<when test="deviceType == ConstTYPE_SOC">
                		(B.DEVICE_TYPE = #{ConstTYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{TYPE_VERSION_1_0}) 
                	</when>
                	<when test="deviceType == ConstTYPE_SOC2">
                		(B.DEVICE_TYPE = #{ConstTYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{TYPE_VERSION_2_0}) 
                	</when>
                	<when test="deviceType == ConstTYPE_SOC3">
                		(B.DEVICE_TYPE = #{ConstTYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{TYPE_VERSION_3_0}) 
                	</when>
                	<when test="deviceType == ConstTYPE_SOC4">
                		(B.DEVICE_TYPE = #{ConstTYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{TYPE_VERSION_4_0}) 
                	</when>
                	<when test="deviceType == ConstTYPE_SOC5">
                		(B.DEVICE_TYPE = #{ConstTYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{TYPE_VERSION_5_0}) 
                	</when>
                	<when test="deviceType == ConstTYPE_SOC6">
                		(B.DEVICE_TYPE = #{ConstTYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{TYPE_VERSION_6_0}) 
                	</when>
                	<when test="deviceType == ConstTYPE_SOC7">
                		(B.DEVICE_TYPE = #{ConstTYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{TYPE_VERSION_7_0}) 
                	</when>
                	<when test="deviceType == ConstTYPE_SOC9">
                		(B.DEVICE_TYPE = #{ConstTYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{TYPE_VERSION_9_0})
                	</when>
                	<when test="deviceType == ConstTYPE_SOC10">
                		(B.DEVICE_TYPE = #{ConstTYPE_SOC} AND B.DEVICE_TYPE_VERSION = #{TYPE_VERSION_10_0})
                	</when>
                	<when test="deviceType == ConstTYPE_PREMIUM">
                		( B.DEVICE_TYPE = #{deviceType} ) 
                	</when>
                	<otherwise>
                        ( B.DEVICE_TYPE = #{deviceType} ) 
                    </otherwise>
                </choose>
	        </foreach>
	        )
			<choose>
				<when
					test="deviceType.equalsIgnoreCase(ConstTYPE_THIRDPARTY)">
					AND B.DEVICE_TYPE = #{ConstTYPE_THIRDPARTY}
				</when>
				<otherwise>
					AND B.DEVICE_TYPE != #{ConstTYPE_THIRDPARTY}
				</otherwise>
		   </choose>
		</if>

		<if
			test="!listType.equalsIgnoreCase(ConstGROUP_TYPE_SHARED) and !listType.equalsIgnoreCase(ConstGROUP_TYPE_ORGAN)">
			<choose>
				<when test="listType.equalsIgnoreCase(ConstGROUP_TYPE_ALL)">
					<if test="creatorID != null and creatorID.length() > 0">
						<choose>
							<when test="canReadUnshared">
								<if test="!hasPermission">
									AND A.CREATOR_ID = #{creatorID}
								</if>
							</when>
							<otherwise>
								AND (A.CREATOR_ID = #{creatorID} OR A.SHARE_FLAG = 1)
							</otherwise>
						</choose>
					</if>
				</when>
				<otherwise> 
					<if
						test="creatorID != null and creatorID.length() > 0 and organization_id != 0">
						<choose>
							<when test="ConstGROUP_TYPE_ALL.equalsIgnoreCase(listType)">
								<if test="!canReadUnshared">
									AND (A.CREATOR_ID = #{creatorID} OR A.SHARE_FLAG =
									1)
								</if>
							</when>
							<otherwise>
								<choose>
									<when test="canReadUnshared">
										<if test="!hasPermission">
											AND A.CREATOR_ID = #{creatorID}
										</if>
									</when>
									<otherwise>
										AND (A.CREATOR_ID = #{creatorID} AND A.SHARE_FLAG =
										1)
									</otherwise>
								</choose>
							</otherwise>
						</choose>
					</if>
				</otherwise>
			</choose>
		</if>

		<if test="userFilter != null and !userFilter.equals('')">
			<foreach item="item" index="index" collection="userFilter"
				open=" AND (" separator=" OR " close=")">
				A.CREATOR_ID = #{item}
			</foreach>
			AND A.SHARE_FLAG = 1
		</if>

		<if test="sizeFilter != null">
			<foreach item="item" index="index" collection="sizeFilter"
				open=" AND (" separator=" OR " close=")">
				<if test="item.start != null">
					B.TOTAL_SIZE &gt;= #{item.start}
				</if>
				<if test="item.end != null">
					AND B.TOTAL_SIZE &lt; #{item.end}
				</if>
			</foreach>
		</if>



		<if test="contentIdList != null and !contentIdList.equals('') and !(contentIdList.size==1 and contentIdList[0].equals('null'))">
			<foreach item="item" index="index" collection="contentIdList"
				open=" AND (" separator=" OR " close=")">
				<if test="item != null and !item.equals('') and !item.equalsIgnoreCase('null')">
					A.CONTENT_ID             = #{item}
				</if>
			</foreach>
		</if>
	</sql>

	<select id="getTLFDListPage"
		resultType="com.samsung.magicinfo.framework.content.entity.Content">
		<include refid="getContentListSelect" />
		<include refid="getTLFDListFromWhere" />
		<if test="isMediaTypeFilter == null">
			<include refid="getBaseOrderByContentSearch" />
		</if>
		LIMIT #{pageSize} OFFSET #{startPos}
	</select>

	<select id="getTLFDListPage"
		resultType="com.samsung.magicinfo.framework.content.entity.Content"
		databaseId="mssql">
		SELECT * FROM
		(
		<include refid="getContentListSelect" />
		,
		ROW_NUMBER() OVER(
		<include refid="getBaseOrderByContentSearch" />
		) as rownum
		<include refid="getTLFDListFromWhere" />
		) as SubQuery
		WHERE rownum > ${startPos} and rownum &lt;= ${startPos +
		pageSize}
		ORDER BY rownum
	</select>

	<select id="getTLFDGroupList"
		resultType="com.samsung.magicinfo.framework.content.entity.Group">
		SELECT
		GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME, CREATOR_ID,
		CREATE_DATE
		FROM
		MI_CMS_INFO_TLFD_GROUP

		<choose>
			<when test="organizationId != null and organizationId > 0">
				WHERE ORGANIZATION_ID = #{organizationId}
			</when>
			<otherwise>
				<if
					test="userManageGroupList != null and userManageGroupList.size() > 0">
					WHERE ORGANIZATION_ID IN
					<foreach item="group" collection="userManageGroupList"
						open=" (" separator="," close=")">
						#{group.group_id}
					</foreach>
				</if>
			</otherwise>
		</choose>

		ORDER BY GROUP_NAME, P_GROUP_ID
	</select>

	<select id="getTLFDChildGroupList"
		resultType="com.samsung.magicinfo.framework.content.entity.Group">
		SELECT *
		FROM
		MI_CMS_INFO_TLFD_GROUP
		WHERE
		P_GROUP_ID = #{groupId}
		<choose>
			<when test="organizationId != null and organizationId > 0">
				AND ORGANIZATION_ID = #{organizationId}
			</when>
			<otherwise>
				<if
					test="userManageGroupList != null and userManageGroupList.size() > 0">
					AND ORGANIZATION_ID IN
					<foreach item="group" collection="userManageGroupList"
						open=" (" separator="," close=")">
						#{group.group_id}
					</foreach>
				</if>
			</otherwise>
		</choose>
		ORDER BY
		GROUP_NAME ASC
	</select>
	<select id="getTLFDOrganizationIdByGroupId" resultType="long">
		SELECT
		ORGANIZATION_ID FROM MI_CMS_INFO_TLFD_GROUP WHERE GROUP_ID =
		#{groupId}
	</select>

	<select id="getTLFDListByGroupId" resultType="map">
		SELECT
		A.CONTENT_ID
		FROM
		MI_CMS_INFO_CONTENT A,
		MI_CMS_MAP_GROUP_TLFD B
		WHERE
		B.CONTENT_ID =
		A.CONTENT_ID AND B.GROUP_ID = #{groupId} AND IS_DELETED ='N'
	</select>

	<insert id="addMapGroupTLFD">
		INSERT INTO MI_CMS_MAP_GROUP_TLFD (CONTENT_ID,
		GROUP_ID) VALUES (#{contentId}, #{groupId})
	</insert>

	<update id="setTLFDGroup">
		UPDATE MI_CMS_MAP_GROUP_TLFD SET GROUP_ID =
		#{groupId} WHERE CONTENT_ID = #{contentId}
	</update>

	<select id="getTLFDInfo"
		resultType="com.samsung.magicinfo.framework.content.entity.Content">
		SELECT
			A.CONTENT_ID, VERSION_ID , A.CONTENT_NAME,
			B.DEVICE_TYPE, B.DEVICE_TYPE_VERSION,
			B.MEDIA_TYPE, B.CREATOR_ID
			,
			B.CREATE_DATE, A.LAST_MODIFIED_DATE, C.GROUP_ID, B.TOTAL_SIZE,
			B.PLAY_TIME, B.RESOLUTION, A.IS_DELETED
			, B.IS_ACTIVE, A.SHARE_FLAG,
			IS_LINEAR_VWL, SCREEN_COUNT, X_COUNT,
			Y_COUNT, X_RANGE, Y_RANGE
			,
			A.POLLING_INTERVAL, A.CONTENT_META_DATA , B.MAIN_FILE_ID,
			B.THUMB_FILE_ID, B.SFI_FILE_ID
			, C.GROUP_NAME, E.FILE_NAME AS
			MAIN_FILE_NAME , B.IS_STREAMING,
			MAIN_FILE_EXTENSION,
			B.IS_USED_TEMPLATE
			, B.TEMPLATE_PAGE_COUNT, B.MULTI_VWL,
			B.PLAY_TIME_MILLI,
			A.APPROVAL_STATUS, A.APPROVAL_OPINION
		FROM
			MI_CMS_INFO_CONTENT A,
			MI_CMS_INFO_CONTENT_VERSION B,
			MI_CMS_INFO_TLFD_GROUP C,
			MI_CMS_MAP_GROUP_TLFD D,
			MI_CMS_INFO_FILE E
		WHERE
			A.CONTENT_ID = B.CONTENT_ID AND IS_ACTIVE = 'Y'
			AND C.GROUP_ID =
			D.GROUP_ID AND D.CONTENT_ID = A.CONTENT_ID
			AND E.FILE_ID =
			B.MAIN_FILE_ID AND A.CONTENT_ID = #{contentID}
	</select>

	<select id="getTLFDRoot_GroupId" resultType="java.lang.Long">
		SELECT P_GROUP_ID
		FROM MI_CMS_INFO_TLFD_GROUP WHERE GROUP_ID = #{groupId}
	</select>

    <select id="getTLFDRootId" resultType="long">
		SELECT DISTINCT
		GROUP_ID
		FROM
		MI_CMS_INFO_TLFD_GROUP
		WHERE
		ORGANIZATION_ID = #{organizationId}
		AND P_GROUP_ID = #{ungrouped}
	</select>

    <select id="getTLFDGroupIdsByOrgId" resultType="java.lang.Long">
		SELECT GROUP_ID
		FROM MI_CMS_INFO_TLFD_GROUP WHERE ORGANIZATION_ID=#{orgId}
	</select> 

	<insert id="addTLFDGroup">
		INSERT INTO MI_CMS_INFO_TLFD_GROUP (GROUP_ID,
		GROUP_NAME, P_GROUP_ID,
		GROUP_DEPTH, CREATOR_ID,
		CREATE_DATE,ORGANIZATION_ID) VALUES
		(#{groupId}, #{groupName},
		#{pGroupId}, #{groupDepth}, #{creatorId},
		NOW(), #{organizationId})
	</insert>

	<insert id="addTLFDGroup" databaseId="mssql">
		INSERT INTO
		MI_CMS_INFO_TLFD_GROUP (GROUP_ID, GROUP_NAME, P_GROUP_ID,
		GROUP_DEPTH,
		CREATOR_ID, CREATE_DATE,ORGANIZATION_ID) VALUES
		(#{groupId},
		#{groupName}, #{pGroupId}, #{groupDepth}, #{creatorId},
		GETDATE(),
		#{organizationId})
	</insert>

	<select id="getSupportedDeviceTypeByContentType"
		resultType="map">
		SELECT * FROM MI_CMS_MAP_FILE WHERE MEDIA_TYPE =
		#{contentType}
		ORDER BY DEVICE_TYPE, DEVICE_TYPE_VERSION DESC
	</select>

	<select id="numberOfSyncPlayUsingContent" resultType="int">
		SELECT
		COUNT(A.PLAYLIST_ID)
		FROM
		MI_CMS_INFO_PLAYLIST A,
		MI_CMS_MAP_PLAYLIST_CONTENT B
		WHERE
		A.PLAYLIST_ID = B.PLAYLIST_ID AND
		A.PLAYLIST_TYPE = '3' AND B.CONTENT_ID =
		#{contentId}
	</select>
	<select id="getAllContentGroups"
		resultType="com.samsung.magicinfo.framework.content.entity.Group">
		WITH RECURSIVE GROUP_IDS AS (
		SELECT *
		FROM
		MI_CMS_INFO_CONTENT_GROUP
		WHERE GROUP_ID = #{groupId}
		UNION ALL
		SELECT
		CHILD_GROUP.*
		FROM MI_CMS_INFO_CONTENT_GROUP CHILD_GROUP
		JOIN GROUP_IDS
		ON CHILD_GROUP.P_GROUP_ID = GROUP_IDS.GROUP_ID
		)
		SELECT GROUP_ID
		FROM
		GROUP_IDS
		ORDER BY P_GROUP_ID, GROUP_DEPTH, GROUP_NAME ASC
	</select>

	<select id="getAllContentGroups"
		resultType="com.samsung.magicinfo.framework.content.entity.Group"
		databaseId="mssql">
		WITH GROUP_IDS AS (
		SELECT *
		FROM MI_CMS_INFO_CONTENT_GROUP
		WHERE GROUP_ID = #{groupId}
		UNION ALL
		SELECT CHILD_GROUP.*
		FROM
		MI_CMS_INFO_CONTENT_GROUP CHILD_GROUP
		JOIN GROUP_IDS ON
		CHILD_GROUP.P_GROUP_ID = GROUP_IDS.GROUP_ID
		)
		SELECT GROUP_ID
		FROM
		GROUP_IDS
		ORDER BY P_GROUP_ID, GROUP_DEPTH, GROUP_NAME ASC
	</select>

	<select id="getThumbnailByFileId"
		resultType="com.samsung.magicinfo.framework.content.entity.Content">
		SELECT FILES.FILE_ID AS thumb_file_id, FILES.FILE_NAME AS
		thumb_file_name
		FROM MI_CMS_INFO_CONTENT_VERSION VERSIONS
		LEFT JOIN
		MI_CMS_INFO_FILE FILES ON VERSIONS.THUMB_FILE_ID = FILES.FILE_ID
		WHERE
		MAIN_FILE_ID = #{fileId}
		LIMIT 1
	</select>

	<select id="getThumbnailByFileId"
		resultType="com.samsung.magicinfo.framework.content.entity.Content"
		databaseId="mssql">
		SELECT TOP 1 FILES.FILE_ID AS thumb_file_id,
		FILES.FILE_NAME AS thumb_file_name
		FROM MI_CMS_INFO_CONTENT_VERSION
		VERSIONS
		LEFT JOIN MI_CMS_INFO_FILE FILES ON VERSIONS.THUMB_FILE_ID =
		FILES.FILE_ID
		WHERE MAIN_FILE_ID = #{fileId}
	</select>

    <select id="getThumbnailByThumbnailFileId" resultType="map">
		SELECT FILE_ID AS thumb_file_id, FILE_NAME AS thumb_file_name
		FROM MI_CMS_INFO_FILE
		WHERE FILE_ID = #{thumbnailFileId}
	</select>

	<select id="getShareFolderList"
		resultType="com.samsung.magicinfo.framework.content.entity.ShareFolder">
		SELECT DISTINCT A.*
		FROM MI_CMS_INFO_SHARE_FOLDER A LEFT OUTER JOIN MI_CMS_MAP_SHARE_FOLDER_ROOT_GROUP B ON A.SHARE_FOLDER_ID = B.SHARE_FOLDER_ID
		<if test="groupId != null and groupId != 0">
			WHERE B.ORG_GROUP_ID = #{groupId}
		</if>
		ORDER BY A.SHARE_FOLDER_NAME
	</select>

    <!-- ftp history -->
    <resultMap type="com.samsung.magicinfo.framework.content.entity.ContentPollingLog" id="contentPollingLog">
        <result column="CONTENT_ID"  property="contentId" />
        <result column="CONTENT_POLLING_TIME" property="pollingTime" />
        <result column="POLLING_STATUS" property="pollingStatus" />
        <result column="FILE_COUNT" property="fileCount" />
        <result column="STATUS_DESCRIPTION" property="statusDescription" />
        <result column="CONTENT_CREATOR_ID" property="creatorId" />
        <collection property="filePollingLogs" javaType="ArrayList"
                    ofType="com.samsung.magicinfo.framework.content.entity.ContentFilePollingLog" resultMap="filePollingLog"/>
    </resultMap>

    <resultMap 	type="com.samsung.magicinfo.framework.content.entity.ContentFilePollingLog" id="filePollingLog">
        <result column="F_CONTENT_ID" property="contentId" />
        <result column="FILE_POLLING_TIME" property="pollingTime" />
        <result column="FILE_NAME" property="fileName" />
        <result column="FILE_SIZE" property="fileSize" />
        <result column="FILE_STATUS" property="fileStatus" />
        <result column="FILE_CREATOR_ID" property="creatorId" />
    </resultMap>

    <sql id="getContentPollingHistorySelect">
		SELECT CPL.CONTENT_ID AS CONTENT_ID
			 , CPL.POLLING_TIME AS CONTENT_POLLING_TIME
			 , CPL.POLLING_STATUS
			 , CPL.FILE_COUNT
			 , CPL.STATUS_DESCRIPTION
			 , CPL.CREATOR_ID AS CONTENT_CREATOR_ID
			 , FPL.CONTENT_ID AS F_CONTENT_ID
			 , FPL.POLLING_TIME AS FILE_POLLING_TIME
			 , FPL.FILE_NAME
			 , FPL.FILE_SIZE
			 , FPL.FILE_STATUS
			 , FPL.CREATOR_ID AS FILE_CREATOR_ID
		  FROM
	</sql>

    <sql id="getContentPollingHistory_sub_Limit">
		  (
			  SELECT A.CONTENT_ID
				, A.POLLING_TIME
				, A.POLLING_STATUS
				, A.FILE_COUNT
				, A.STATUS_DESCRIPTION
				, A.CREATOR_ID
			  FROM MI_CMS_LOG_POLLING AS A
			  WHERE A.CONTENT_ID = #{contentId}
			  ORDER BY POLLING_TIME DESC
			  LIMIT 20
		  ) AS CPL
	</sql>

    <sql id="getContentPollingHistory_sub_Top">
		  (
			  SELECT TOP(20)
			      A.CONTENT_ID
				, A.POLLING_TIME
				, A.POLLING_STATUS
				, A.FILE_COUNT
				, A.STATUS_DESCRIPTION
				, A.CREATOR_ID
			  FROM MI_CMS_LOG_POLLING AS A
			  WHERE A.CONTENT_ID = #{contentId}
			  ORDER BY POLLING_TIME DESC
		  ) AS CPL
	</sql>

    <sql id="getContentPollingHistory_Condition">
		 LEFT OUTER JOIN MI_CMS_LOG_POLLING_FILE AS FPL
			ON CPL.POLLING_TIME = FPL.POLLING_TIME
		   AND FPL.CONTENT_ID = #{contentId}
		 ORDER BY CPL.POLLING_TIME DESC
	</sql>

    <select id="getContentPollingHistory" resultMap="contentPollingLog">
        <include refid="getContentPollingHistorySelect" />
        <include refid="getContentPollingHistory_sub_Limit" />
        <include refid="getContentPollingHistory_Condition" />
    </select>
    <select id="getContentPollingHistory" resultMap="contentPollingLog" databaseId="mssql">
        <include refid="getContentPollingHistorySelect" />
        <include refid="getContentPollingHistory_sub_Top" />
        <include refid="getContentPollingHistory_Condition" />
    </select>
    
    <insert id="addPollingInfo" parameterType="map">
        INSERT
          INTO MI_CMS_LOG_POLLING (CONTENT_ID, POLLING_TIME, POLLING_STATUS, FILE_COUNT, STATUS_DESCRIPTION, CREATOR_ID)
        VALUES (#{contentId}, #{pollingTime}, #{pollingStatus}, #{fileCount}, #{statusDescription}, #{creatorId})
    </insert>
    
    <insert id="addPollingFileInfo" parameterType="map">
        INSERT
          INTO MI_CMS_LOG_POLLING_FILE (CONTENT_ID, POLLING_TIME, FILE_NAME, FILE_SIZE, FILE_STATUS, CREATOR_ID)
        VALUES (#{contentId}, #{pollingTime}, #{fileName}, #{fileSize}, #{fileStatus}, #{creatorId})
    </insert>
    
    <delete id="deletePollingInfo">
        DELETE
          FROM MI_CMS_LOG_POLLING
         WHERE CONTENT_ID = #{contentId}
    </delete>
    
    <delete id="deletePollingFileInfo">
        DELETE
          FROM MI_CMS_LOG_POLLING_FILE
         WHERE CONTENT_ID = #{contentId}
    </delete>

	<delete id="deleteOldPollingInfo">
		DELETE FROM MI_CMS_LOG_POLLING WHERE (CONTENT_ID, POLLING_TIME) IN (
			SELECT CONTENT_ID, POLLING_TIME FROM MI_CMS_LOG_POLLING WHERE CONTENT_ID = #{contentId} ORDER BY POLLING_TIME DESC OFFSET 20
		)
	</delete>

	<delete id="deleteOldPollingInfo" databaseId="mssql">
		DELETE FROM MI_CMS_LOG_POLLING WHERE EXISTS (
			SELECT A.*
			FROM (
				SELECT * FROM MI_CMS_LOG_POLLING WHERE CONTENT_ID = #{contentId} ORDER BY POLLING_TIME DESC OFFSET 20 ROWS
			) A
			WHERE
				MI_CMS_LOG_POLLING.CONTENT_ID = A.CONTENT_ID AND MI_CMS_LOG_POLLING.POLLING_TIME = A.POLLING_TIME
		)
	</delete>

	<delete id="deleteOldPollingFileInfo">
		DELETE FROM MI_CMS_LOG_POLLING_FILE WHERE (CONTENT_ID, POLLING_TIME) IN (
			SELECT
				A.CONTENT_ID, A.POLLING_TIME
			FROM
				MI_CMS_LOG_POLLING_FILE A,
				(SELECT * FROM MI_CMS_LOG_POLLING WHERE CONTENT_ID = #{contentId} ORDER BY POLLING_TIME DESC OFFSET 20) B
			WHERE
				A.CONTENT_ID = B.CONTENT_ID AND A.POLLING_TIME = B.POLLING_TIME
		)
	</delete>

	<delete id="deleteOldPollingFileInfo" databaseId="mssql">
		DELETE FROM MI_CMS_LOG_POLLING_FILE WHERE EXISTS (
			SELECT
				A.CONTENT_ID, A.POLLING_TIME
			FROM
				MI_CMS_LOG_POLLING_FILE A,
				(SELECT * FROM MI_CMS_LOG_POLLING WHERE CONTENT_ID = #{contentId} ORDER BY POLLING_TIME DESC OFFSET 20 ROWS) B
			WHERE
				MI_CMS_LOG_POLLING_FILE.CONTENT_ID = A.CONTENT_ID AND MI_CMS_LOG_POLLING_FILE.POLLING_TIME = A.POLLING_TIME
				AND A.CONTENT_ID = B.CONTENT_ID AND A.POLLING_TIME = B.POLLING_TIME
		)
	</delete>
    
    <select id="getOneContentByFileId" resultType="string">
        SELECT CONTENT_ID
          FROM MI_CMS_MAP_VERSION_FILE
         WHERE FILE_ID = #{fileId}
         LIMIT 1
    </select>

    <select id="getOneContentByFileId" resultType="string" databaseId="mssql">
        SELECT TOP 1
               CONTENT_ID
          FROM MI_CMS_MAP_VERSION_FILE
         WHERE FILE_ID = #{fileId}
    </select>
    
    <select id="getFileIdFromContentByFileNameAndSize" resultType="string">
        SELECT T1.FILE_ID
          FROM MI_CMS_MAP_VERSION_FILE T1
              ,MI_CMS_INFO_FILE        T2
         WHERE T1.CONTENT_ID = #{contentId}
           AND T2.FILE_ID    = T1.FILE_ID
           AND T2.FILE_NAME  = #{fileName}
           AND T2.FILE_SIZE  = #{fileSize}
         LIMIT 1
    </select>

    <select id="getFileIdFromContentByFileNameAndSize" resultType="string" databaseId="mssql">
        SELECT TOP 1 T1.FILE_ID
          FROM MI_CMS_MAP_VERSION_FILE T1
              ,MI_CMS_INFO_FILE        T2
         WHERE T1.CONTENT_ID = #{contentId}
           AND T2.FILE_ID    = T1.FILE_ID
           AND T2.FILE_NAME  = #{fileName}
           AND T2.FILE_SIZE  = #{fileSize}
    </select>
    
    <select id="getPollableCifsContentSettingList" resultType="map">
        SELECT *
          FROM MI_CMS_INFO_CIFS_SETTING
         WHERE IS_DELETED  = 'N'
           AND CAN_REFRESH = 'Y'
    </select>
    
    <select id="getPollableFtpContentSettingList" resultType="map">
        SELECT *
          FROM MI_CMS_INFO_FTP_SETTING
         WHERE IS_DELETED  = 'N'
           AND CAN_REFRESH = 'Y'
    </select>
    
    <update id="setIsReadyForAllCifsThread">
        UPDATE MI_CMS_INFO_CIFS_SETTING
           SET IS_READY   = #{isReady}
         WHERE IS_DELETED = 'N'
    </update>
    
    <update id="setIsReadyForAllFtpThread">
        UPDATE MI_CMS_INFO_FTP_SETTING
           SET IS_READY   = #{isReady}
         WHERE IS_DELETED = 'N'
    </update>
    
    <select id="getCntContentByOrganizationId" resultType="int">
        SELECT COUNT(*) FROM MI_CMS_INFO_CONTENT
		WHERE ORGANIZATION_ID = #{organizationId}
    </select>
    
    <update id="changeGroupIdOf_MI_CMS_MAP_GROUP_CONTENT">
		UPDATE MI_CMS_MAP_GROUP_CONTENT SET GROUP_ID = #{groupId}
		WHERE 
			CONTENT_ID IN 
			(
				SELECT CONTENT_ID 
				FROM MI_CMS_INFO_CONTENT
				WHERE 
					CREATOR_ID = #{fromUserId} AND
					ORGANIZATION_ID = #{organizationId}
			)
	</update>

	<update id="changeCreatorIdOf_MI_CMS_INFO_CONTENT_VERSION">
		UPDATE MI_CMS_INFO_CONTENT_VERSION SET CREATOR_ID = #{toUserId}
		WHERE 
			CONTENT_ID IN 
			(
				SELECT CONTENT_ID 
				FROM MI_CMS_INFO_CONTENT
				WHERE 
					CREATOR_ID = #{fromUserId} AND
					ORGANIZATION_ID = #{organizationId}
			)
	</update>

	<update id="changeCreatorIdOf_MI_CMS_INFO_FILE">
		UPDATE MI_CMS_INFO_FILE SET CREATOR_ID = #{toUserId}
		WHERE 
			FILE_ID IN 
			(
				SELECT MAIN_FILE_ID 
				FROM MI_CMS_INFO_CONTENT_VERSION
				WHERE 
					CONTENT_ID IN 
					(
						SELECT CONTENT_ID 
						FROM MI_CMS_INFO_CONTENT
						WHERE 
							CREATOR_ID = #{fromUserId} AND
							ORGANIZATION_ID = #{organizationId}
					)	
			)
			OR
			FILE_ID IN 
			(
				SELECT THUMB_FILE_ID 
				FROM MI_CMS_INFO_CONTENT_VERSION
				WHERE 
					CONTENT_ID IN 
					(
						SELECT CONTENT_ID 
						FROM MI_CMS_INFO_CONTENT
						WHERE 
							CREATOR_ID = #{fromUserId} AND
							ORGANIZATION_ID = #{organizationId}
					)
			)
	</update>

	<update id="changeCreatorIdOf_MI_CMS_INFO_CONTENT">
		UPDATE MI_CMS_INFO_CONTENT SET CREATOR_ID = #{toUserId}
		WHERE 
			CREATOR_ID = #{fromUserId} AND
			ORGANIZATION_ID = #{organizationId}
	</update>

	<update id="updateRulesetGroup">
		UPDATE MI_RULE_INFO_RULESET
		SET creator= #{new_creator}
		WHERE creator = #{current_creator}
	</update>
	
	<select id="getCntAllContents" resultType="long">
		SELECT
			COUNT(*)
		FROM
			MI_CMS_INFO_CONTENT A
		WHERE			 
			A.CREATOR_ID = #{creatorId} AND
			A.ORGANIZATION_ID = #{organizationId}
	</select>

    <!-- KDH DF190722-00163 DLK라면 관련 LFT의 재생시간 적용 -->
    <select id="getPlayTimeOfLftByDlk" resultType="string">
        SELECT T1.PLAY_TIME
        FROM MI_CMS_INFO_CONTENT_VERSION T1, MI_CMS_MAP_DLKCONTENT_LFDCONTENT T2
        WHERE T1.CONTENT_ID = T2.CONTENT_ID
            AND NOT EXISTS (
                SELECT 1
                FROM MI_CMS_MAP_DLKCONTENT_LFDCONTENT
                WHERE DLK_CONTENT_ID = T2.DLK_CONTENT_ID AND DLK_IDX > T2.DLK_IDX
            )
            AND T1.IS_ACTIVE = 'Y'
            AND T2.DLK_CONTENT_ID = #{contentId}
    </select>

    <!-- KDH DF190722-00163 DLK라면 관련 LFT의 재생시간 적용 -->
    <update id="setContentPlayTime">
        UPDATE MI_CMS_INFO_CONTENT_VERSION
           SET PLAY_TIME  = #{playTime}
         WHERE CONTENT_ID = #{contentId}
           AND IS_ACTIVE  = 'Y'
	</update>

	<!--  KDH [RQ190703-00340][19.10 RC] 컨텐츠 사용기간 추가(만료) S -->
	<update id="setExpirationDate">
		UPDATE MI_CMS_INFO_CONTENT
		   SET EXPIRATION_DATE    = #{expirationDate}
		      ,LAST_MODIFIED_DATE = CURRENT_TIMESTAMP
		 WHERE CONTENT_ID      = #{contentId}
	</update>

    <select id="getExpiredContentList" resultType="map">
        SELECT T1.CONTENT_ID
              ,T1.CONTENT_NAME
              ,T2.VERSION_ID
              ,T2.MEDIA_TYPE
              ,T1.EXPIRATION_DATE
          FROM MI_CMS_INFO_CONTENT         T1
              ,MI_CMS_INFO_CONTENT_VERSION T2
         WHERE T1.EXPIRATION_DATE <![CDATA[<]]> #{curDate}
           AND T1.IS_DELETED      = 'N'
           AND T2.CONTENT_ID      = T1.CONTENT_ID
           AND T2.IS_ACTIVE       = 'Y'
	</select>
	<!--  KDH [RQ190703-00340][19.10 RC] 컨텐츠 사용기간 추가(만료) E -->
    <!-- KDH DF191104-00447 컨텐츠와 승인자 매핑정보의 존재여부 체크 S -->
    <select id="countContentApproverMap" resultType="int">
        SELECT COUNT(1) AS cnt
          FROM MI_CMS_MAP_CONTENT_APPROVER
         WHERE CONTENT_ID = #{contentId}
           AND USER_ID    = #{userId}
    </select>
    <!-- KDH DF191104-00447 컨텐츠와 승인자 매핑정보의 존재여부 체크 E -->

    <select id="getGroupListByOrganizationId"
            resultType="com.samsung.magicinfo.framework.content.entity.Group">
		SELECT
		GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME,
		CREATOR_ID, CREATE_DATE
		FROM
		MI_CMS_INFO_CONTENT_GROUP
		WHERE ORGANIZATION_ID = #{organizationId}
		ORDER BY
		GROUP_DEPTH, GROUP_NAME, GROUP_ID, P_GROUP_ID
	</select>


    <select id="getTLFDGroupListByOrgId"
            resultType="com.samsung.magicinfo.framework.content.entity.Group">
        SELECT
        GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME, CREATOR_ID,
        CREATE_DATE
        FROM
        MI_CMS_INFO_TLFD_GROUP
        WHERE ORGANIZATION_ID = #{organizationId}
    </select>
    
    	<select id="getContentIdListByContentName" resultType="string">
		SELECT DISTINCT ON (CONTENT_ID) CONTENT_ID
		FROM
		MI_CMS_INFO_CONTENT
		WHERE 1=1 <if test="contentNameList != null"> AND 
		<foreach item="content_name" collection="contentNameList" separator="or">
		CONTENT_NAME LIKE #{content_name} </foreach></if>
	</select>
	
		
	<select id="getContentIdListByRegex" resultType="string">
		SELECT DISTINCT ON (CONTENT_ID) CONTENT_ID
		FROM
		MI_CMS_INFO_CONTENT
		WHERE CONTENT_NAME ~ #{regex} AND
			  IS_DELETED = 'N'
	</select>
	
	
	<select id="getContentGroupBySearch" resultType="com.samsung.magicinfo.framework.content.entity.Group">
        SELECT GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME,
        CREATOR_ID, CREATE_DATE
        FROM MI_CMS_INFO_CONTENT_GROUP
        WHERE ORGANIZATION_ID = #{organizationId}

        <if test="userId != null and userId.length() > 0">
            AND CREATOR_ID = #{userId}
        </if>
        
        <if test="searchText != null and searchText.length() > 0">
              <bind name="searchText" value="'%' + searchText + '%'" />
               AND GROUP_NAME LIKE #{searchText} ESCAPE '^'
        </if> 
    </select>
    
    <!-- SF[00207800] -->
    <select id="getTLFDGroupBySearch" resultType="com.samsung.magicinfo.framework.content.entity.Group">
        SELECT GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME,
        CREATOR_ID, CREATE_DATE
        FROM MI_CMS_INFO_TLFD_GROUP
        <choose>
			<when test="organizationId != null and organizationId > 0">
				WHERE ORGANIZATION_ID = #{organizationId}
        		<if test="searchText != null and searchText.length() > 0">
             		<bind name="searchText" value="'%' + searchText + '%'" />
               			AND GROUP_NAME LIKE #{searchText} ESCAPE '^'
        		</if> 	
			</when>
			<otherwise>
				<if test="searchText != null and searchText.length() > 0">
             		<bind name="searchText" value="'%' + searchText + '%'" />
               			WHERE GROUP_NAME LIKE #{searchText} ESCAPE '^'
        		</if>
			</otherwise>
		</choose>
    </select>
    
	<select id="getParentsGroupList" resultType="com.samsung.magicinfo.restapi.common.model.V2ParentsGroup">
        WITH RECURSIVE GROUP_IDS AS (
            SELECT GROUPS.*
            FROM MI_CMS_INFO_CONTENT_GROUP GROUPS
            WHERE GROUP_ID = #{pGroupId}
            UNION ALL
            SELECT PARENT_GROUP.*
            FROM MI_CMS_INFO_CONTENT_GROUP PARENT_GROUP
            JOIN GROUP_IDS ON PARENT_GROUP.GROUP_ID = GROUP_IDS.P_GROUP_ID
        )
        SELECT GROUP_IDS.GROUP_ID as groupId, 
            GROUP_IDS.P_GROUP_ID as pGroupId, 
            GROUP_IDS.GROUP_DEPTH as groupDepth, 
            GROUP_IDS.GROUP_NAME as groupName, 
            GROUP_IDS.CREATOR_ID as creatorId, 
            GROUP_IDS.ORGANIZATION_ID as organizationId
        FROM GROUP_IDS
        WHERE P_GROUP_ID > -1
        ORDER BY group_depth asc
    </select>

    <select id="getParentsGroupList" resultType="com.samsung.magicinfo.restapi.common.model.V2ParentsGroup" databaseId="mssql">
        WITH GROUP_IDS AS (
            SELECT GROUPS.*
            FROM MI_CMS_INFO_CONTENT_GROUP GROUPS
            WHERE GROUP_ID = #{pGroupId}
            UNION ALL
            SELECT PARENT_GROUP.*
            FROM MI_CMS_INFO_CONTENT_GROUP PARENT_GROUP
            JOIN GROUP_IDS ON PARENT_GROUP.GROUP_ID = GROUP_IDS.P_GROUP_ID
        )
        SELECT GROUP_IDS.GROUP_ID as groupId,
            GROUP_IDS.P_GROUP_ID as pGroupId,
            GROUP_IDS.GROUP_DEPTH as groupDepth,
            GROUP_IDS.GROUP_NAME as groupName,
            GROUP_IDS.CREATOR_ID as creatorId,
            GROUP_IDS.ORGANIZATION_ID as organizationId
        FROM GROUP_IDS
        WHERE P_GROUP_ID > -1
        ORDER BY group_depth asc
    </select>


    <select id="getOrganizationIdByGroupId" resultType="java.lang.Long">
        SELECT
        ORGANIZATION_ID
        FROM
        MI_CMS_INFO_CONTENT_GROUP
        WHERE GROUP_ID = #{groupId}
    </select>
    
    <select id="isExistTLFDGroup" resultType="int">
        SELECT COUNT(GROUP_ID)
        FROM MI_CMS_INFO_TLFD_GROUP
        WHERE GROUP_ID = #{groupId}
    </select>

    <select id="getContentIdsByFileId" resultType="string">
        SELECT DISTINCT
          A.CONTENT_ID
        FROM
          MI_CMS_INFO_CONTENT A,
          MI_CMS_INFO_CONTENT_VERSION B
        WHERE A.CONTENT_ID = B.CONTENT_ID
          AND B.IS_ACTIVE = 'Y'
          AND A.CONTENT_ID IN
              (
                SELECT DISTINCT CONTENT_ID
                  FROM MI_CMS_MAP_VERSION_FILE
                 WHERE FILE_ID = #{fileId}
              )
    </select>
    
    <select id="getSubGroupList" resultType="com.samsung.magicinfo.framework.content.entity.Group">
        SELECT *
        FROM
            MI_CMS_INFO_CONTENT_GROUP
        WHERE
        P_GROUP_ID = #{groupId}
        <if test="organizationId != null">
            AND ORGANIZATION_ID = #{organizationId}
        </if>
        ORDER BY
        GROUP_NAME ASC
    </select>
    
    <select id="getTLFDGroupInfo" resultType="com.samsung.magicinfo.framework.content.entity.Group">
        SELECT
        GROUP_ID, P_GROUP_ID, GROUP_DEPTH, GROUP_NAME, CREATOR_ID,
        CREATE_DATE
        FROM
        MI_CMS_INFO_TLFD_GROUP 
        where GROUP_ID = #{groupId}
    </select>
	
    <select id="V2GetUnapprovedContentCnt" resultType="int">
        SELECT COUNT(DISTINCT A.CONTENT_ID)
        FROM MI_CMS_INFO_CONTENT A,
            MI_USER_INFO_USER B,
            MI_CMS_MAP_CONTENT_APPROVER C
        WHERE
            B.USER_ID = A.CREATOR_ID AND
            B.IS_DELETED = 'N' AND
            A.APPROVAL_STATUS='UNAPPROVED' AND
            A.IS_DELETED='N' AND
            B.ROOT_GROUP_ID = A.ORGANIZATION_ID AND
            C.CONTENT_ID = A.CONTENT_ID
        <if test="organizationId != null and organizationId != 0">
            AND ORGANIZATION_ID = ${organizationId}
        </if>
    </select>
    
	<select id="getAllContentCount" resultType="int">
		SELECT COUNT(CONTENT_ID)
		FROM MI_CMS_INFO_CONTENT
	</select>

	<select id="getContentCountByContentType" resultType="Map">
  		SELECT  MEDIA_TYPE, COUNT(CONTENT_ID) AS CONTENT_COUNT
		FROM MI_CMS_INFO_CONTENT_VERSION AS V
		WHERE IS_ACTIVE = 'Y'
		GROUP BY  MEDIA_TYPE
	</select>


	<select id="getContentCountByContentResolution" resultType="Map">
		SELECT RESOLUTION, COUNT(RESOLUTION) AS CONTENT_COUNT
		FROM MI_CMS_INFO_CONTENT_VERSION
		GROUP BY RESOLUTION ORDER BY CONTENT_COUNT DESC
	</select>

  <select id="getContentGroupTotalCount" resultType="int">

		SELECT COUNT(GROUP_ID) FROM MI_CMS_INFO_CONTENT_GROUP WHERE P_GROUP_ID != -1

	</select>
  
	<select id="getValueIdsByContentIdAndIndexId" resultType="com.samsung.magicinfo.framework.content.entity.ContentInsightIndexMapEntity">
		SELECT *
		FROM
			MI_CMS_MAP_CONTENT_INSIGHTINDEX
		WHERE
			CONTENT_ID = #{contentId} AND INDEX_ID = #{indexId}
	</select>

	<select id="getAssignedAdvertisementByContentId" resultType="com.samsung.magicinfo.framework.setup.entity.InsightIndexValueWithLastModifiedDateEntity">
		SELECT
			A.VALUE_ID, B.VALUE,
			(	SELECT CREATE_DATE
				FROM MI_CMS_INFO_CONTENT_PRODUCT_CODE_HISTORY
				WHERE CONTENT_ID = #{contentId}
				ORDER BY CREATE_DATE DESC LIMIT 1 )
		FROM
			MI_CMS_MAP_CONTENT_INSIGHTINDEX A,
			MI_INSIGHTINDEX_INFO_VALUE B
		WHERE
			A.VALUE_ID = B.VALUE_ID
			<choose>
				<when test="type != null and type.equals('PRODUCT_CODE')">
					AND A.INDEX_ID = 'PRODUCT_CODE'
				</when>
				<when test="type != null and type.equals('INSIGHT_INDEX')">
					AND A.INDEX_ID != 'PRODUCT_CODE'
				</when>
			</choose>
			AND A.CONTENT_ID = #{contentId}
	</select>

	<select id="getAssignedAdvertisementByContentId" resultType="com.samsung.magicinfo.framework.setup.entity.InsightIndexValueWithLastModifiedDateEntity" databaseId="mssql">
		SELECT
			A.VALUE_ID, B.VALUE,
			(	SELECT TOP 1 CREATE_DATE
				FROM MI_CMS_INFO_CONTENT_PRODUCT_CODE_HISTORY
				WHERE CONTENT_ID = #{contentId}
				ORDER BY CREATE_DATE DESC ) AS CREATE_DATE
		FROM
			MI_CMS_MAP_CONTENT_INSIGHTINDEX A,
			MI_INSIGHTINDEX_INFO_VALUE B
		WHERE
			A.VALUE_ID = B.VALUE_ID
			<choose>
				<when test="type != null and type.equals('PRODUCT_CODE')">
					AND A.INDEX_ID = 'PRODUCT_CODE'
				</when>
				<when test="type != null and type.equals('INSIGHT_INDEX')">
					AND A.INDEX_ID != 'PRODUCT_CODE'
				</when>
			</choose>
			AND A.CONTENT_ID = #{contentId}
	</select>

	<insert id="assignAdvertisement">
		INSERT INTO MI_CMS_MAP_CONTENT_INSIGHTINDEX (CONTENT_ID, INDEX_ID, VALUE_ID)
		VALUES (#{contentId}, #{indexId}, #{valueId})
	</insert>

	<delete id="removeAssignedAdvertisement">
		DELETE FROM MI_CMS_MAP_CONTENT_INSIGHTINDEX
		WHERE
			CONTENT_ID = #{contentId}
			<if test="indexId != null and indexId != ''">
				AND INDEX_ID = #{indexId}
			</if>
			<if test="valueId != null">
				AND VALUE_ID = #{valueId}
			</if>
	</delete>

	<!-- Advertisement 의 Sale Data 안의 Product Code 에 대한 변경 이력 -->
	<insert id="addProductCodeHistory">
		INSERT INTO MI_CMS_INFO_CONTENT_PRODUCT_CODE_HISTORY (CONTENT_ID, HISTORY_ID, CREATE_DATE)
		VALUES (#{contentId}, #{historyId}, CURRENT_TIMESTAMP)
	</insert>

	<insert id="addProductCodeHistoryValue">
		INSERT INTO MI_CMS_INFO_CONTENT_PRODUCT_CODE_HISTORY_VALUE (HISTORY_ID, VALUE_ID, VALUE)
		VALUES (#{historyId}, #{valueId}, #{value})
	</insert>

	<select id="getContentIdListByMappedValueId" resultType="java.lang.String">
        SELECT CONTENT_ID FROM MI_CMS_MAP_CONTENT_INSIGHTINDEX WHERE VALUE_ID = #{valueId}
    </select>

	<delete id="deleteProductCodeHistoryByDate">
		DELETE FROM MI_CMS_INFO_CONTENT_PRODUCT_CODE_HISTORY
		WHERE
		<bind name="safe_date"
			  value="@com.samsung.common.utils.DaoTools@safeDateTimeString(date)" />
		CREATE_DATE &lt; '${safe_date}'
	</delete>

	<select id="getProductCodeHistoryByContentId" resultType="com.samsung.magicinfo.framework.content.entity.ContentProductCodeHistoryEntity">
		SELECT * FROM MI_CMS_INFO_CONTENT_PRODUCT_CODE_HISTORY
		WHERE CONTENT_ID = #{contentId}
		ORDER BY CREATE_DATE DESC
	</select>

	<select id="getProductCodeHistoryValueListByHistoryId" resultType="java.lang.String">
		SELECT VALUE FROM MI_CMS_INFO_CONTENT_PRODUCT_CODE_HISTORY_VALUE
		WHERE HISTORY_ID = #{historyId}
	</select>

	<select id="getContentFileCount" resultType="int">
		SELECT COUNT(MAIN_FILE_ID) FROM MI_CMS_INFO_CONTENT_VERSION
		<if test="type != null and type.equals('AISR')">
			WHERE IS_AISR = 'Y'
		</if>
	</select>

	<select id="getUnapprovedContentIdList" resultType="java.lang.String">
		SELECT DISTINCT A.CONTENT_ID
		FROM
			MI_CMS_INFO_CONTENT A,
			MI_CMS_MAP_CONTENT_APPROVER B
		WHERE
			A.CONTENT_ID = B.CONTENT_ID
			AND A.APPROVAL_STATUS = 'UNAPPROVED'
			AND A.ORGANIZATION_ID = #{organizationId}
	</select>
</mapper>
