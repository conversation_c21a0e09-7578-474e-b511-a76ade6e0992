package com.samsung.magicinfo.webauthor2.util;

public enum OperatingSystem {
  Windows, MacOS, Linux, Other;
  
  public static boolean isWindows() {
    return check(Windows);
  }
  
  public static boolean isWindows32() {
    if (check(Windows) && 
      checkArch() < 64)
      return true; 
    return false;
  }
  
  public static boolean isWindows64() {
    if (check(Windows) && 
      checkArch() >= 64)
      return true; 
    return false;
  }
  
  public static boolean isLinux() {
    return check(Linux);
  }
  
  public static boolean check(OperatingSystem operatingSystem) {
    return System.getProperty("os.name").toLowerCase().contains(operatingSystem.toString().toLowerCase());
  }
  
  public static int checkArch() {
    String arch = System.getenv("PROCESSOR_ARCHITECTURE");
    String wow64Arch = System.getenv("PROCESSOR_ARCHITEW6432");
    int realArch = ((arch != null && arch.endsWith("64")) || (wow64Arch != null && wow64Arch.endsWith("64"))) ? 64 : 32;
    return realArch;
  }
}
