package com.samsung.magicinfo.framework.ruleset.entity;

import java.sql.Timestamp;
import java.util.List;

public class RuleSet {
   private String ruleset_id;
   private Long version;
   private Boolean is_deleted;
   private String name;
   private Timestamp create_date;
   private Timestamp modify_date;
   private String creator;
   private Boolean multi_rule;
   private Boolean sync_enable;
   private Long sync_duration;
   private String default_play;
   private String default_play_name;
   private String file_id;
   private Long organization_id;
   private Long group_id;
   private String group_name;
   private String device_type;
   private Float device_type_version;
   private String rule_tree;
   private String description;
   private Boolean is_temp;
   private Long number_of_rules;
   private String program_id;
   private List conditions;
   private List results;

   public RuleSet() {
      super();
   }

   public String getRuleset_id() {
      return this.ruleset_id;
   }

   public void setRuleset_id(String ruleset_id) {
      this.ruleset_id = ruleset_id;
   }

   public Long getVersion() {
      return this.version;
   }

   public void setVersion(Long version) {
      this.version = version;
   }

   public Boolean getIs_deleted() {
      return this.is_deleted;
   }

   public void setIs_deleted(Boolean is_deleted) {
      this.is_deleted = is_deleted;
   }

   public String getName() {
      return this.name;
   }

   public void setName(String name) {
      this.name = name;
   }

   public Timestamp getCreate_date() {
      return this.create_date;
   }

   public void setCreate_date(Timestamp create_date) {
      this.create_date = create_date;
   }

   public Timestamp getModify_date() {
      return this.modify_date;
   }

   public void setModify_date(Timestamp modify_date) {
      this.modify_date = modify_date;
   }

   public String getCreator() {
      return this.creator;
   }

   public void setCreator(String creator) {
      this.creator = creator;
   }

   public Boolean getMulti_rule() {
      return this.multi_rule;
   }

   public void setMulti_rule(Boolean multi_rule) {
      this.multi_rule = multi_rule;
   }

   public Boolean getSync_enable() {
      return this.sync_enable;
   }

   public void setSync_enable(Boolean sync_enable) {
      this.sync_enable = sync_enable;
   }

   public Long getSync_duration() {
      return this.sync_duration;
   }

   public void setSync_duration(Long sync_duration) {
      this.sync_duration = sync_duration;
   }

   public String getDefault_play() {
      return this.default_play;
   }

   public void setDefault_play(String default_play) {
      this.default_play = default_play;
   }

   public String getFile_id() {
      return this.file_id;
   }

   public void setFile_id(String file_id) {
      this.file_id = file_id;
   }

   public Long getOrganization_id() {
      return this.organization_id;
   }

   public void setOrganization_id(Long organization_id) {
      this.organization_id = organization_id;
   }

   public Long getGroup_id() {
      return this.group_id;
   }

   public void setGroup_id(Long group_id) {
      this.group_id = group_id;
   }

   public String getDevice_type() {
      return this.device_type;
   }

   public void setDevice_type(String device_type) {
      this.device_type = device_type;
   }

   public Float getDevice_type_version() {
      return this.device_type_version;
   }

   public void setDevice_type_version(Float device_type_version) {
      this.device_type_version = device_type_version;
   }

   public List getConditions() {
      return this.conditions;
   }

   public void setConditions(List conditions) {
      this.conditions = conditions;
   }

   public String getDefault_play_name() {
      return this.default_play_name;
   }

   public void setDefault_play_name(String default_play_name) {
      this.default_play_name = default_play_name;
   }

   public String getGroup_name() {
      return this.group_name;
   }

   public void setGroup_name(String group_name) {
      this.group_name = group_name;
   }

   public String getRule_tree() {
      return this.rule_tree;
   }

   public void setRule_tree(String rule_tree) {
      this.rule_tree = rule_tree;
   }

   public List getResults() {
      return this.results;
   }

   public void setResults(List results) {
      this.results = results;
   }

   public String getDescription() {
      return this.description;
   }

   public void setDescription(String description) {
      this.description = description;
   }

   public Boolean getIs_temp() {
      return this.is_temp;
   }

   public void setIs_temp(Boolean is_temp) {
      this.is_temp = is_temp;
   }

   public Long getNumber_of_rules() {
      return this.number_of_rules;
   }

   public void setNumber_of_rules(Long number_of_rules) {
      this.number_of_rules = number_of_rules;
   }

   public String getProgram_id() {
      return this.program_id;
   }

   public void setProgram_id(String program_id) {
      this.program_id = program_id;
   }
}
