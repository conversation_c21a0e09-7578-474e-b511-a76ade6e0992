package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.exception.repository.ContentNotFoundException;
import com.samsung.magicinfo.webauthor2.repository.model.OpenAPIResponseData;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

public class GetLedLayoutUrlOpenApiMethod extends OpenApiMethod<String, OpenAPIResponseData> {
  private static final Logger logger = LoggerFactory.getLogger(GetLedLayoutUrlOpenApiMethod.class);
  
  private final String deviceId;
  
  private final String type;
  
  public GetLedLayoutUrlOpenApiMethod(RestTemplate restTemplate, String deviceId, String type) {
    super(restTemplate);
    this.deviceId = deviceId;
    this.type = type;
  }
  
  protected String getOpenApiClassName() {
    return "PremiumDeviceService";
  }
  
  protected String getOpenApiMethodName() {
    return "getVWTFileByDeviceId";
  }
  
  protected Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("deviceId", this.deviceId);
    vars.put("", this.type);
    return vars;
  }
  
  Class<OpenAPIResponseData> getResponseClass() {
    return OpenAPIResponseData.class;
  }
  
  String convertResponseData(OpenAPIResponseData responseData) {
    if (responseData.getErrorMessage() != null)
      throw new ContentNotFoundException(responseData.getCode(), responseData.getErrorMessage()); 
    return responseData.getResponseClass();
  }
}
