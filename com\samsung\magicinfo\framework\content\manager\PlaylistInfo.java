package com.samsung.magicinfo.framework.content.manager;

import com.samsung.common.db.DBListExecuter;
import com.samsung.common.db.PagedListInfo;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.Effect;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.entity.PlaylistContent;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;

public interface PlaylistInfo extends DBListExecuter {
   Boolean getCanEditOthers(String var1, String var2, HttpServletRequest var3) throws SQLException;

   String getPlaylistName(String var1) throws SQLException;

   List getPlaylistAllVerInfo(String var1) throws SQLException;

   Playlist getPlaylistActiveVerInfo(String var1) throws SQLException;

   Long getPlaylistActiveVersionId(String var1) throws SQLException;

   Playlist getPlaylistVerInfo(String var1, Long var2) throws SQLException;

   List getContentListOfPlaylist(String var1, Long var2) throws SQLException;

   List getContentListOfSyncGroup(String var1, Long var2) throws SQLException;

   List getSearchList(Map var1) throws SQLException;

   int getSearchListCnt(Map var1) throws SQLException;

   List getPlaylistList(Map var1) throws SQLException;

   List getAllDeletedPlaylistList(String var1) throws SQLException;

   List getAllPlaylistList(String var1) throws SQLException;

   List getPlaylistListByUser(String var1, boolean var2) throws SQLException;

   List getAllPlaylistList(String var1, Long var2) throws SQLException;

   List getContentList(String var1, Long var2) throws SQLException;

   List getTagList(String var1, Long var2) throws SQLException;

   List getTagListWithExpiredDate(String var1, Long var2, String var3) throws SQLException;

   int getContentCount(String var1, Long var2) throws SQLException;

   int getCountSyncGroup(String var1, Long var2) throws SQLException;

   String getMainContentId(String var1) throws SQLException;

   List getActiveVerContentList(String var1) throws SQLException;

   List getActiveVerContentListForDownloadCheck(String var1) throws SQLException;

   PlaylistContent getContentEffectInfo(String var1, Long var2, String var3) throws SQLException;

   PlaylistContent getContentEffectInfoByOrder(String var1, Long var2, String var3) throws SQLException;

   Effect getEffectInfoByEffectName(String var1) throws SQLException;

   Effect getSocEffectInfoByEffectName(String var1) throws SQLException;

   Effect getVWLEffectInfoByEffectName(String var1) throws SQLException;

   Boolean isExistPlaylistID(String var1) throws SQLException;

   Boolean isExistPlaylistVersion(String var1, Long var2) throws SQLException;

   Boolean isUpdatablePlaylist(String var1) throws SQLException;

   Boolean isDeletablePlaylist(String var1, String var2, String var3) throws SQLException;

   Boolean isLockedPlaylist(String var1, String var2) throws SQLException;

   int addPlaylist(Playlist var1) throws SQLException, ConfigException, Exception;

   List getPlaylistList(String var1, int var2, int var3);

   int deleteContentFromPlaylist(String var1, String[] var2, String var3, String var4) throws SQLException, ConfigException, Exception;

   int deleteContentFromPlaylist(String[] var1, String[] var2, String var3, String var4) throws SQLException, ConfigException, Exception;

   int addContentToPlaylist(String var1, String[] var2, String var3) throws SQLException, ConfigException, Exception;

   int addContentToPlaylist(String[] var1, String[] var2, String var3) throws SQLException, ConfigException, Exception;

   int setMaxVersionPlaylistActive(String var1) throws SQLException;

   int addPlaylistInfo(Playlist var1) throws SQLException, ConfigException;

   int addMapPlaylistContent(PlaylistContent var1) throws SQLException;

   int addMapGroupPlaylist(String var1, Long var2) throws SQLException;

   int setPlaylistInfo(String var1, String var2, String var3, int var4, int var5, int var6) throws SQLException, ConfigException;

   int setActiveVersion(String var1, Long var2) throws SQLException, ConfigException, Exception;

   int deletePlaylist(String var1, String var2, String var3) throws SQLException, ConfigException;

   int restorePlaylist(String var1) throws SQLException, ConfigException;

   int deletePlaylistCompletely(String var1) throws SQLException;

   int setPlaylistLock(String var1, String var2) throws SQLException;

   int setPlaylistGroup(String var1, Long var2) throws SQLException, ConfigException;

   int setPlaylistShare(String var1, Long var2) throws SQLException, ConfigException;

   int setPlaylistMetaData(String var1, String var2) throws SQLException, ConfigException;

   int setPlaylistEffect(PlaylistContent var1) throws SQLException, ConfigException;

   Long getRootId(String var1) throws SQLException;

   Long getRootId(String var1, long var2) throws SQLException;

   Boolean isExistGroupName(String var1, String var2) throws SQLException;

   Long getGroupId(String var1) throws SQLException;

   String getGroupName(Long var1) throws SQLException;

   Group getGroupInfo(Long var1) throws SQLException;

   List getGroupList(String var1) throws SQLException;

   List getGroupList(String var1, long var2) throws SQLException;

   Long addGroup(Group var1) throws SQLException, ConfigException;

   Long addDefaultGroup(String var1) throws SQLException, ConfigException;

   Long addDefaultGroup(String var1, long var2) throws SQLException, ConfigException;

   int setGroupInfo(Group var1) throws SQLException;

   Boolean isDeletableGroup(Long var1) throws SQLException;

   List getGroupedPlaylistIdList(Long var1) throws SQLException;

   int deleteGroup(Long var1) throws SQLException;

   List getChildGroupList(Long var1, boolean var2, String var3) throws SQLException;

   List getChildGroupList(Long var1, boolean var2, String var3, long var4) throws SQLException;

   List getChildGroupIdList(Long var1, boolean var2) throws SQLException;

   PagedListInfo getPagedList(int var1, int var2, Map var3, String var4) throws Exception;

   int deletePlaylistVersion(String var1, String var2, String var3, String var4) throws SQLException, ConfigException;

   List getPlaylistEffectList(String var1) throws SQLException, ConfigException;

   int deleteContentTag(String var1, String var2, int var3) throws SQLException;

   int deleteContentTagAll(String var1, String var2) throws SQLException;

   List getPlaylistListToDeleteContent(String var1, String[] var2, int var3, int var4);

   int setContentTag(String var1, Long var2, String var3, int var4, String var5, int var6) throws SQLException;

   List getContentTag(String var1, String var2, Long var3) throws SQLException;

   String getTagValueListString(String var1, String var2, Long var3);

   String getTagValueListString(String var1, String var2, Long var3, int var4);

   String getTagValueListStringInSchedule(String var1, Long var2);

   String getTagIdListString(String var1, Long var2, String var3, int var4);

   List getContentTag(String var1, Long var2, String var3, int var4) throws SQLException;

   String getPlaylistContentTagList(String var1, Long var2) throws SQLException;

   int setContentDuraionByContentID(String var1, Long var2);

   int setContentDuraionMilliByContentID(String var1, String var2);

   List getPlaylistIDListByContentID(String var1);

   int setPlaytime(String var1, Long var2, String var3);

   Long getSumOfContentDuration(String var1, Long var2);

   String isDeleted(String var1) throws SQLException;

   String getCreatorIdByPlaylistId(String var1) throws SQLException;

   int unlockAllSession() throws SQLException;

   int setPlaylistUnlock(String var1, String var2) throws SQLException;

   int setPlaylistUnlockBySessionID(String var1) throws SQLException;

   List getPlaylistListByDeviceType(String var1, int var2, int var3, String var4, float var5);

   List getPlaylistListByDeviceType(String var1, int var2, int var3, String var4, float var5, List var6);

   Integer getPlaylistListCountByDeviceType(String var1, String var2, float var3);

   Integer getPlaylistListCountByDeviceType(String var1, String var2, float var3, List var4);

   void deleteContentFromPlaylist(String var1);

   List getContentOrderListOfPlaylist(String var1, Long var2);

   List getSubPlaylistContentOrderListOfPlaylist(String var1, Long var2);

   void updateContentOrder(int var1, String var2, Long var3, Long var4);

   void updateSubPlaylistContentOrder(int var1, String var2, Long var3, Long var4);

   void updateContentCount(int var1, String var2, Long var3);

   List getSyncGroupInfo(String var1, Long var2) throws SQLException, Exception;

   int getUsedPlaylistCount(long var1) throws SQLException;

   ContentFile getThumbFileInfo(String var1) throws SQLException;

   List getPlaylistContentTagEntityList(String var1, Long var2) throws SQLException;

   List getPlaylistActiveVerInfoForSync(String var1) throws SQLException;

   List getTagContentListOfPlaylist(String var1, Long var2) throws SQLException;

   int getTagConditionWithTagIdListSize(String var1, long var2, long var4) throws SQLException;

   List getPlaylistTagConditionList(String var1, long var2, long var4) throws SQLException;

   int getTagConditionWithTagIdListSize(String var1, long var2, long var4, String var6) throws SQLException;

   boolean addTagConditionMapping(String var1, long var2, long var4, long var6) throws SQLException;

   ContentFile getTagPlaylistThumbFileInfo(String var1) throws SQLException;

   List getTagConditionWithTagIdList(String var1, long var2, long var4) throws SQLException;

   List getPlaylistTag(String var1, Long var2, long var3, long var5) throws SQLException;

   void checkExistTagCondition(String var1, Long var2, Long var3, Long var4) throws SQLException;

   List getCntContentAtTagPlaylist(Long var1, String var2) throws SQLException;

   List getThumbContentAtTagPlaylist(Long var1, String var2) throws SQLException;

   List getThumbContentAtTagPlaylistAll(Long var1, String var2) throws SQLException;

   List getTagPlaylistTagList(String var1, Long var2) throws SQLException;

   List getTagPlaylistTagConditionList(String var1, Long var2) throws SQLException;

   String getConditionIdWithTagNumber(long var1, String[] var3, String[] var4, String[] var5) throws SQLException;

   List getTagConditionWithTagIdList(String var1, long var2, long var4, String var6) throws SQLException;

   List getCntContentAtTagPlaylist(Long var1, String[] var2) throws SQLException;

   List getContentListFromTagId(List var1) throws SQLException;

   String getTagConditionIdWithTagNumber(String var1, Long var2, Long var3, String var4) throws SQLException;

   List getListLinkedPlaylistProgramId(String var1) throws SQLException;

   List getListLinkedPlaylistPlaylistId(String var1) throws SQLException;

   List getContentTagListWithPlaylistId(String var1) throws SQLException;

   List getContentListWithTag(String var1, String var2, Long var3, Long var4, List var5) throws SQLException;

   boolean chkOrganizationByPlaylistId(String var1, String var2) throws SQLException;

   boolean checkMappingSchedule(long var1, String var3) throws SQLException;

   int getCountPlaylistToExpire(String var1, Long var2, String var3, SelectCondition var4) throws SQLException;

   List getListPlaylistToExpire(int var1, int var2, String var3, Long var4, String var5, SelectCondition var6) throws SQLException;

   int deleteContentTagFromPlaylist(String var1) throws SQLException;

   void updateContentOrderOfTag(String var1, Long var2, String var3, int var4);

   long getDefaultPlaylistGroupId(String var1, long var2) throws SQLException;

   void transferPlaylistToAdmin(User var1) throws SQLException;

   void transferPlaylistToAdmin(User var1, String[] var2) throws SQLException;

   void transferPlaylistToAdmin(User var1, long var2) throws SQLException;

   void deleteGroupByCreatorId(String var1) throws SQLException;

   long getCntAllPlaylists(String var1, Long var2) throws SQLException;

   int deleteContentFromPlaylist(String var1, String var2) throws Exception;

   List getUpperPlaylist(String var1) throws Exception;

   int getContentCountInTagPlaylist(String var1) throws Exception;

   List getPlaylistInfoByContentId(String var1) throws Exception;

   int setTotalSize(String var1, long var2, long var4) throws Exception;

   int setContentDurationByVersionOfPlaylist(String var1, long var2, String var4, long var5) throws Exception;

   int setContentDurationMilliByVersionOfPlaylist(String var1, long var2, String var4, String var5) throws Exception;

   List getGroupList(Long var1) throws SQLException;

   List getPlaylistIdListByPlaylistName(String[] var1) throws SQLException;

   List getPlaylistIdListByRegex(String var1) throws SQLException;

   List getPlaylistScheduleMapping(List var1) throws SQLException;

   List getPlayListGroupBySearch(String var1, long var2, String var4) throws SQLException;

   List getPlayListGroupBySearch(String var1, long var2) throws SQLException;

   List getParentsGroupList(int var1) throws SQLException;

   Long getOrganizationIdByGroupId(Long var1) throws SQLException;

   List getSubGroupList(Long var1, boolean var2, Long var3) throws SQLException;

   int deletePlaylistVersion(String var1, String var2, String var3) throws SQLException;

   long getPlaylistMaxVer(String var1) throws SQLException;

   int setVersionPlaylistActive(String var1, Long var2) throws SQLException;

   List getActivePlaylistCountOne(String var1);

   Boolean isExistMapPlaylistID(String var1, Long var2) throws SQLException;

   int getPlaylistListCnt(Map var1) throws SQLException;

   int setPlaylistModifiedDate(String var1) throws SQLException, ConfigException;

   List getPlaylistCountByPlaylistType() throws SQLException;

   int getPlaylistGroupTotalCount() throws SQLException;
}
