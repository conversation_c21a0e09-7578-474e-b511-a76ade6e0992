package com.samsung.magicinfo.webauthor2.service.upload;

import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UploadServiceImpl implements UploadService {
  private HttpContentUploadService httpContentUploadService;
  
  @Autowired
  public UploadServiceImpl(HttpContentUploadService httpContentUploadService) {
    this.httpContentUploadService = httpContentUploadService;
  }
  
  public void upload(List<MediaSource> mediaSources, String contentId) {
    this.httpContentUploadService.uploadListOfMediaSources(mediaSources, contentId);
  }
  
  public void upload(MediaSource mediaSource, String contentId) {
    this.httpContentUploadService.uploadSingleMediaSource(mediaSource, contentId);
  }
}
