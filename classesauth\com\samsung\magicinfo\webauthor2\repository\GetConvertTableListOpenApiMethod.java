package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.model.datalink.ConvertTableData;
import com.samsung.magicinfo.webauthor2.repository.model.datalink.ConvertTableResultListData;
import com.samsung.magicinfo.webauthor2.repository.model.datalink.ResponseConvertTableData;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.web.client.RestTemplate;

public class GetConvertTableListOpenApiMethod extends OpenApiMethod<List<ConvertTableData>, ResponseConvertTableData> {
  private final String userId;
  
  private final String token;
  
  public GetConvertTableListOpenApiMethod(RestTemplate restTemplate, String userId, String token) {
    super(restTemplate);
    this.userId = userId;
    this.token = token;
  }
  
  protected String getOpenApiClassName() {
    return "CommonContentService";
  }
  
  protected String getOpenApiMethodName() {
    return "getConvertTableList";
  }
  
  protected Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("userId", this.userId);
    vars.put("token", this.token);
    return vars;
  }
  
  Class<ResponseConvertTableData> getResponseClass() {
    return ResponseConvertTableData.class;
  }
  
  List<ConvertTableData> convertResponseData(ResponseConvertTableData responseData) {
    List<ConvertTableData> convertTableData;
    ConvertTableResultListData resultListData = responseData.getResponseClass();
    if (resultListData != null && resultListData.getResultList() != null) {
      convertTableData = resultListData.getResultList();
    } else {
      convertTableData = new ArrayList<>();
    } 
    return convertTableData;
  }
}
