package com.samsung.magicinfo.webauthor2.model.datalink;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

public class DynamicDLKData extends DLKData {
  private final String serverName;
  
  private final ValueLocation valueLocation;
  
  private final TagListDynamicData tagList;
  
  private ConvertTable convertTable;
  
  @JsonCreator
  public DynamicDLKData(@JsonProperty("serverName") String serverName, @JsonProperty("valueLocation") ValueLocation valueLocation, @JsonProperty("tagList") TagListDynamicData tagList, @JsonProperty("convertTable") ConvertTable convertTable) {
    super(DLKDataType.Dynamic);
    this.serverName = serverName;
    this.valueLocation = valueLocation;
    this.tagList = tagList;
    this.convertTable = convertTable;
  }
  
  public String getServerName() {
    return this.serverName;
  }
  
  public TagListDynamicData getTagList() {
    return this.tagList;
  }
  
  public ConvertTable getConvertTable() {
    return this.convertTable;
  }
  
  public void setConvertTable(ConvertTable convertTable) {
    this.convertTable = convertTable;
  }
  
  public ValueLocation getValueLocation() {
    return this.valueLocation;
  }
}
