package com.samsung.common.export;

import com.samsung.common.logger.LoggingManagerV2;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Method;
import java.util.Map;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFFooter;
import org.apache.poi.hssf.usermodel.HSSFHeader;
import org.apache.poi.hssf.usermodel.HSSFPrintSetup;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.CellRangeAddress;

public class ExcelBuilder {
   Logger logger = LoggingManagerV2.getLogger(ExcelBuilder.class);
   private HSSFWorkbook wb;
   HSSFCellStyle[] style;
   short[] styleAlign;
   short[] styleVAlign;
   boolean[] styleBold;
   boolean[] styleItalic;
   boolean[] styleStrikeout;
   int[] styleUnderline;
   short[] styleFontColor;
   String[] styleFontName = new String[]{"돋움"};
   short[] styleFontSize;
   short[] styleBgColor;
   boolean[] styleBorder;
   boolean styleModified = true;
   float rowHeight = 15.0F;
   short[][] columnWidth;
   int[] countCell;
   public static final String MERGE_LEFT = null;
   public static final String MERGE_UP = "(^___^)/";
   public static final String ROW_INDEX = "(-_-^)";
   public static final short ALIGN_LEFT = 1;
   public static final short ALIGN_CENTER = 2;
   public static final short ALIGN_JUSTIFY = 5;
   public static final short ALIGN_RIGHT = 3;
   public static final short VALIGN_TOP = 0;
   public static final short VALIGN_CENTER = 1;
   public static final short VALIGN_BOTTOM = 2;
   public static final short VALIGN_JUSTIFY = 3;
   public static final short COLOR_AQUA = 49;
   public static final short COLOR_AUTOMATIC = 64;
   public static final short COLOR_BLACK = 8;
   public static final short COLOR_BLUE = 12;
   public static final short COLOR_BLUE_GREY = 54;
   public static final short COLOR_BRIGHT_GREEN = 11;
   public static final short COLOR_BROWN = 60;
   public static final short COLOR_CORAL = 29;
   public static final short COLOR_CORNFLOWER_BLUE = 24;
   public static final short COLOR_DARK_BLUE = 18;
   public static final short COLOR_DARK_GREEN = 58;
   public static final short COLOR_DARK_RED = 16;
   public static final short COLOR_DARK_TEAL = 56;
   public static final short COLOR_DARK_YELLOW = 19;
   public static final short COLOR_GOLD = 51;
   public static final short COLOR_GREEN = 17;
   public static final short COLOR_GREY_25_PERCENT = 22;
   public static final short COLOR_GREY_40_PERCENT = 55;
   public static final short COLOR_GREY_50_PERCENT = 23;
   public static final short COLOR_GREY_80_PERCENT = 63;
   public static final short COLOR_INDIGO = 62;
   public static final short COLOR_LAVENDER = 46;
   public static final short COLOR_LEMON_CHIFFON = 26;
   public static final short COLOR_LIGHT_BLUE = 48;
   public static final short COLOR_LIGHT_CORNFLOWER_BLUE = 31;
   public static final short COLOR_LIGHT_GREEN = 42;
   public static final short COLOR_LIGHT_ORANGE = 52;
   public static final short COLOR_LIGHT_TURQUOISE = 41;
   public static final short COLOR_LIGHT_YELLOW = 43;
   public static final short COLOR_LIME = 50;
   public static final short COLOR_MAROON = 25;
   public static final short COLOR_OLIVE_GREEN = 59;
   public static final short COLOR_ORANGE = 53;
   public static final short COLOR_ORCHID = 28;
   public static final short COLOR_PALE_BLUE = 44;
   public static final short COLOR_PINK = 14;
   public static final short COLOR_PLUM = 61;
   public static final short COLOR_RED = 10;
   public static final short COLOR_ROSE = 45;
   public static final short COLOR_ROYAL_BLUE = 30;
   public static final short COLOR_SEA_GREEN = 57;
   public static final short COLOR_SKY_BLUE = 40;
   public static final short COLOR_TAN = 47;
   public static final short COLOR_TEAL = 21;
   public static final short COLOR_TURQUOISE = 15;
   public static final short COLOR_VIOLET = 20;
   public static final short COLOR_WHITE = 9;
   public static final short COLOR_YELLOW = 13;
   public static final short FONTSTYLE_NONE = 0;
   public static final short FONTSTYLE_BOLD = 1;
   public static final short FONTSTYLE_ITALIC = 2;
   public static final short FONTSTYLE_BOLD_ITALIC = 3;

   public ExcelBuilder() {
      super();
   }

   public ExcelBuilder(String sheetName) {
      super();
      if (sheetName != null) {
         this.wb = new HSSFWorkbook();
         HSSFSheet[] sheets = new HSSFSheet[]{this.wb.createSheet(sheetName)};
         this.columnWidth = new short[1][];
         this.countCell = new int[]{0};
      }

   }

   public ExcelBuilder(String[] sheetNames) {
      super();
      if (sheetNames != null && sheetNames.length > 0) {
         this.wb = new HSSFWorkbook();
         int sheetsCount = sheetNames.length;
         HSSFSheet[] sheets = new HSSFSheet[sheetsCount];
         this.columnWidth = new short[sheetsCount][];
         this.countCell = new int[sheetsCount];

         for(int i = 0; i < sheetsCount; ++i) {
            sheets[i] = this.wb.createSheet(sheetNames[i]);
            this.countCell[i] = 0;
         }
      }

   }

   public int getSheetsCount() {
      return this.wb.getNumberOfSheets();
   }

   public void setLandscape(int sheetIndex, boolean isLandscape) {
      HSSFSheet sheet = this.wb.getSheetAt(sheetIndex);
      HSSFPrintSetup ps = sheet.getPrintSetup();
      ps.setLandscape(isLandscape);
   }

   public void setSheetProtect(int sheetIndex, String password) {
      HSSFSheet sheet = this.wb.getSheetAt(sheetIndex);
      sheet.protectSheet(password);
   }

   public void setDefaultStyle() {
      this.style = null;
      this.styleAlign = null;
      this.styleVAlign = null;
      this.styleBold = null;
      this.styleItalic = null;
      this.styleStrikeout = null;
      this.styleUnderline = null;
      this.styleFontColor = null;
      this.styleFontName = new String[]{"돋움"};
      this.styleFontSize = null;
      this.styleBgColor = null;
      this.styleBorder = null;
      this.rowHeight = 15.0F;
      this.styleModified = true;
   }

   public void setAlign(short align) {
      this.setAlign(new short[]{align});
   }

   public void setAlign(short[] align) {
      this.styleAlign = align;
      this.styleModified = true;
   }

   public void setVAlign(short vAlign) {
      this.setVAlign(new short[]{vAlign});
   }

   public void setVAlign(short[] vAlign) {
      this.styleVAlign = vAlign;
      this.styleModified = true;
   }

   public void setBold(boolean bold) {
      this.setBold(new boolean[]{bold});
   }

   public void setBold(boolean[] bold) {
      this.styleBold = bold;
      this.styleModified = true;
   }

   public void setItalic(boolean italic) {
      this.setItalic(new boolean[]{italic});
   }

   public void setItalic(boolean[] italic) {
      this.styleItalic = italic;
      this.styleModified = true;
   }

   public void setStrikeout(boolean strikeout) {
      this.setStrikeout(new boolean[]{strikeout});
   }

   public void setStrikeout(boolean[] strikeout) {
      this.styleStrikeout = strikeout;
      this.styleModified = true;
   }

   public void setUnderline(int underline) {
      this.setUnderline(new int[]{underline});
   }

   public void setUnderline(int[] underline) {
      this.styleUnderline = underline;
      this.styleModified = true;
   }

   public void setFontColor(short color) {
      this.setFontColor(new short[]{color});
   }

   public void setFontColor(short[] color) {
      this.styleFontColor = color;
      this.styleModified = true;
   }

   public void setFontName(String fontName) {
      this.setFontName(new String[]{fontName});
   }

   public void setFontName(String[] fontName) {
      this.styleFontName = fontName;
      this.styleModified = true;
   }

   public void setFontSize(short size) {
      this.setFontSize(new short[]{size});
   }

   public void setFontSize(short[] size) {
      this.styleFontSize = size;
      this.styleModified = true;
   }

   public void setBgColor(short color) {
      this.setBgColor(new short[]{color});
   }

   public void setBgColor(short[] color) {
      this.styleBgColor = color;
      this.styleModified = true;
   }

   public void setBorder(boolean border) {
      this.setBorder(new boolean[]{border});
   }

   public void setBorder(boolean[] border) {
      this.styleBorder = border;
      this.styleModified = true;
   }

   public void setColumnWidth(double[] width) {
      this.setColumnWidth(0, width);
   }

   public void setColumnWidth(int sheetIndex, double[] width) {
      int columnCount = width.length;
      this.columnWidth[sheetIndex] = new short[columnCount];

      for(int i = 0; i < columnCount; ++i) {
         double w = 256.0D * width[i];
         this.columnWidth[sheetIndex][i] = (short)((int)w);
      }

   }

   public void setRowHeight(double height) {
      this.rowHeight = (float)height;
   }

   private void setCountCell(int sheetIndex, int count) {
      if (this.countCell[sheetIndex] < count) {
         this.countCell[sheetIndex] = count;
      }

   }

   private void setColumnWidth() {
      int sheetCount = this.wb.getNumberOfSheets();

      for(int i = 0; i < sheetCount; ++i) {
         HSSFSheet sheet = this.wb.getSheetAt(i);
         int cellCount = this.countCell[i];

         for(int j = 0; j < cellCount; ++j) {
            if (this.columnWidth[i] == null) {
               sheet.autoSizeColumn(20);
            } else if (this.columnWidth[i].length == 1) {
               sheet.setColumnWidth(j, this.columnWidth[i][0]);
            } else if (this.columnWidth[i].length > j && this.columnWidth[i][j] > 0) {
               sheet.setColumnWidth(j, this.columnWidth[i][j]);
            } else {
               sheet.autoSizeColumn(20);
            }
         }
      }

   }

   private void setRowHeight(HSSFRow row) {
      if (!(this.rowHeight <= 0.0F)) {
         row.setHeightInPoints(this.rowHeight);
      }
   }

   public void save(String filePath) throws IOException {
      this.setColumnWidth();
      FileOutputStream fileOut = null;

      try {
         fileOut = new FileOutputStream(filePath);
         this.wb.write(fileOut);
      } catch (FileNotFoundException var8) {
      } catch (IOException var9) {
         throw var9;
      } finally {
         if (fileOut != null) {
            fileOut.close();
         }

      }

   }

   public void download(String fileName, HttpServletResponse response) {
      this.setColumnWidth();

      try {
         fileName = new String(fileName.getBytes("EUC-KR"), "8859_1");
      } catch (UnsupportedEncodingException var16) {
      }

      response.setHeader("Content-Type", "application/vnd.ms-xls");
      response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
      response.setHeader("Content-Description", "JSP Generated Data");
      response.setHeader("Content-Transfer-Encoding", "binary;");
      ServletOutputStream outStream = null;

      try {
         outStream = response.getOutputStream();
         this.wb.write(outStream);
         outStream.close();
      } catch (IOException var14) {
      } finally {
         try {
            if (outStream != null) {
               outStream.close();
            }
         } catch (IOException var13) {
         }

      }

   }

   public void setHeader(int sheetIndex, String leftHeader, String centerHeader, String rightHeader) {
      HSSFSheet sheet = this.wb.getSheetAt(sheetIndex);
      HSSFHeader header = sheet.getHeader();
      if (leftHeader != null) {
         header.setLeft(leftHeader);
      }

      if (centerHeader != null) {
         header.setCenter(centerHeader);
      }

      if (rightHeader != null) {
         header.setRight(rightHeader);
      }

   }

   public void setFooter(int sheetIndex, String leftFooter, String centerFooter, String rightFooter) {
      HSSFSheet sheet = this.wb.getSheetAt(sheetIndex);
      HSSFFooter footer = sheet.getFooter();
      if (leftFooter != null) {
         footer.setLeft(leftFooter);
      }

      if (centerFooter != null) {
         footer.setCenter(centerFooter);
      }

      if (rightFooter != null) {
         footer.setRight(rightFooter);
      }

   }

   public static String buildHFString(String fontName, short fontStyle, int fontSize, String text) {
      String hfString = "&";
      if (fontName != null) {
         hfString = hfString + "\"" + fontName.replaceAll(",", "") + ",";
      } else {
         hfString = hfString + "\",";
      }

      switch(fontStyle) {
      case 1:
         hfString = hfString + "Bold\"";
         break;
      case 2:
         hfString = hfString + "Italic\"";
         break;
      case 3:
         hfString = hfString + "Bold Italic\"";
         break;
      default:
         hfString = hfString + "\"";
      }

      if (fontSize > 0) {
         hfString = hfString + "&" + fontSize;
      }

      return hfString + text;
   }

   public void addTitleRow(int sheetIndex, String[] titles) {
      HSSFSheet sheet = this.wb.getSheetAt(sheetIndex);
      this.setCountCell(sheetIndex, titles.length);
      if (sheet.rowIterator().hasNext()) {
         sheet.shiftRows(sheet.getFirstRowNum() - 1, sheet.getLastRowNum(), 1, true, true);
      }

      HSSFRow row = sheet.createRow(0);
      this.setRowHeight(row);
      this.initCellStyle(titles.length);
      int maxLines = 1;
      if (titles != null && titles.length > 0) {
         int cellCount = titles.length;
         HSSFCell[] cells = new HSSFCell[cellCount];
         int mergeStart = 0;

         for(int i = 0; i < cellCount; ++i) {
            if (titles[i] == MERGE_LEFT) {
               if (i > 0) {
                  cells[i] = row.createCell(i);
                  cells[i].setCellStyle(this.applyCellStyle(cells[i], mergeStart));
                  sheet.addMergedRegion(new CellRangeAddress(0, 0, (short)(i - 1), (short)i));
               }
            } else if (!titles[i].equals("(^___^)/")) {
               mergeStart = i;
               cells[i] = row.createCell(i);
               cells[i].setCellValue(new HSSFRichTextString(titles[i]));
               cells[i].setCellStyle(this.applyCellStyle(cells[i], i));
               if (titles[i].indexOf("\n") > 0) {
                  cells[i].getCellStyle().setWrapText(true);
                  if (this.rowHeight == 0.0F) {
                     String[] lines = titles[i].split("\n");
                     if (lines.length > maxLines) {
                        row.setHeight((short)(row.getHeight() * lines.length));
                        maxLines = lines.length;
                     }
                  }
               }
            }
         }
      }

   }

   public void addRow(int sheetIndex, String[] cellValues) {
      this.addRow(sheetIndex, cellValues, -1, false);
   }

   public void addRow(int sheetIndex, String[] cellValues, int rowNum, boolean isShift) {
      HSSFSheet sheet = this.wb.getSheetAt(sheetIndex);
      this.setCountCell(sheetIndex, cellValues.length);
      if (rowNum > -1) {
         if (isShift && sheet.rowIterator().hasNext() && sheet.getLastRowNum() > rowNum) {
            sheet.shiftRows(rowNum, sheet.getLastRowNum(), 1, true, true);
         }
      } else {
         rowNum = sheet.rowIterator().hasNext() ? sheet.getLastRowNum() + 1 : 0;
      }

      HSSFRow row = sheet.createRow(rowNum);
      this.setRowHeight(row);
      this.initCellStyle(cellValues.length);
      int maxLines = 1;
      if (cellValues != null && cellValues.length > 0) {
         int cellCount = cellValues.length;
         HSSFCell[] cells = new HSSFCell[cellCount];
         int mergeStart = 0;

         for(int i = 0; i < cellCount; ++i) {
            if (cellValues[i] == MERGE_LEFT) {
               if (i > 0) {
                  cells[i] = row.createCell(i);
                  cells[i].setCellStyle(this.applyCellStyle(cells[i], mergeStart));
                  sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, (short)(i - 1), (short)i));
               }
            } else if (cellValues[i].equals("(^___^)/")) {
               if (rowNum > 0) {
                  cells[i] = row.createCell(i);
                  cells[i].setCellStyle(this.applyCellStyle(cells[i], i));
                  sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum, (short)i, (short)i));
               }
            } else {
               mergeStart = i;
               cells[i] = row.createCell(i);
               cells[i].setCellValue(new HSSFRichTextString(cellValues[i]));
               cells[i].setCellStyle(this.applyCellStyle(cells[i], i));
               if (cellValues[i].indexOf("\n") > 0) {
                  cells[i].getCellStyle().setWrapText(true);
                  if (this.rowHeight == 0.0F) {
                     String[] lines = cellValues[i].split("\n");
                     if (lines.length > maxLines) {
                        row.setHeight((short)(row.getHeight() * lines.length));
                        maxLines = lines.length;
                     }
                  }
               }
            }
         }
      }

   }

   public void addRow(int sheetIndex, String[] fieldNames, Object vo) {
      HSSFSheet sheet = this.wb.getSheetAt(sheetIndex);
      int currentRowNum = sheet.rowIterator().hasNext() ? sheet.getLastRowNum() + 1 : 0;
      this.setCountCell(sheetIndex, fieldNames.length);
      if (fieldNames != null && vo != null && fieldNames.length > 0) {
         int fieldCount = fieldNames.length;
         HSSFRow row = sheet.createRow(currentRowNum);
         this.setRowHeight(row);
         this.initCellStyle(fieldCount);
         int maxLines = 1;
         HSSFCell[] cells = new HSSFCell[fieldCount];
         int mergeStart = 0;

         for(int i = 0; i < fieldCount; ++i) {
            if (fieldNames[i] == MERGE_LEFT) {
               if (i > 0) {
                  cells[i] = row.createCell(i);
                  cells[i].setCellStyle(this.applyCellStyle(cells[i], mergeStart));
                  sheet.addMergedRegion(new CellRangeAddress(currentRowNum, currentRowNum, (short)(i - 1), (short)i));
               }
            } else if (fieldNames[i].equals("(^___^)/")) {
               if (currentRowNum > 0) {
                  cells[i] = row.createCell(i);
                  cells[i].setCellStyle(this.applyCellStyle(cells[i], i));
                  sheet.addMergedRegion(new CellRangeAddress(currentRowNum - 1, currentRowNum, (short)i, (short)i));
               }
            } else {
               mergeStart = i;
               Object listObj = null;
               Object value = null;

               try {
                  listObj = vo;
                  Method[] methods = vo.getClass().getMethods();

                  for(int index = 0; index < methods.length; ++index) {
                     if ("get".equalsIgnoreCase(methods[index].getName())) {
                        value = methods[index].invoke(listObj, fieldNames[i]);
                        this.logger.debug("Invoke Result" + value);
                     }
                  }

                  value = value == null ? "" : value;
               } catch (Exception var16) {
                  value = "";
               }

               cells[i] = row.createCell(i);
               cells[i].setCellStyle(this.applyCellStyle(cells[i], i));
               if (!value.getClass().equals(Short.class) && !value.getClass().equals(Long.class) && !value.getClass().equals(Integer.class)) {
                  if (!value.getClass().equals(Double.class) && !value.getClass().equals(Float.class)) {
                     String cellValue = value.toString();
                     cells[i].setCellValue(new HSSFRichTextString(cellValue));
                     if (cellValue.indexOf("\n") > 0) {
                        cells[i].getCellStyle().setWrapText(true);
                        if (this.rowHeight == 0.0F) {
                           String[] lines = cellValue.split("\n");
                           if (lines.length > maxLines) {
                              row.setHeight((short)(row.getHeight() * lines.length));
                              maxLines = lines.length;
                           }
                        }
                     }
                  } else {
                     cells[i].setCellType(0);
                     cells[i].setCellValue(Double.parseDouble(value.toString()));
                  }
               } else {
                  cells[i].setCellType(0);
                  cells[i].setCellValue((double)Long.parseLong(value.toString()));
               }
            }
         }
      }

   }

   public void addRows(int sheetIndex, String[] fieldNames, Object[] vos) {
      HSSFSheet sheet = this.wb.getSheetAt(sheetIndex);
      int currentRowNum = sheet.rowIterator().hasNext() ? sheet.getLastRowNum() + 1 : 0;
      this.setCountCell(sheetIndex, fieldNames.length);
      this.initCellStyle(fieldNames.length);
      if (fieldNames != null && vos != null && fieldNames.length > 0 && vos.length > 0) {
         int voCount = vos.length;
         int fieldCount = fieldNames.length;
         long rowIndex = 0L;

         for(int i = 0; i < voCount; ++i) {
            HSSFRow row = sheet.createRow(currentRowNum);
            this.setRowHeight(row);
            int maxLines = 1;
            HSSFCell[] cells = new HSSFCell[fieldCount];
            int mergeStart = 0;
            ++rowIndex;

            for(int j = 0; j < fieldCount; ++j) {
               if (fieldNames[j] == MERGE_LEFT) {
                  if (j > 0) {
                     cells[j] = row.createCell(j);
                     cells[j].setCellStyle(this.applyCellStyle(cells[j], mergeStart));
                     sheet.addMergedRegion(new CellRangeAddress(currentRowNum, (short)(j - 1), currentRowNum, (short)j));
                  }
               } else if (fieldNames[j].equals("(^___^)/")) {
                  if (currentRowNum > 0) {
                     cells[j] = row.createCell(j);
                     cells[j].setCellStyle(this.applyCellStyle(cells[j], j));
                     sheet.addMergedRegion(new CellRangeAddress(currentRowNum - 1, (short)j, currentRowNum, (short)j));
                  }
               } else if (fieldNames[j].equals("(-_-^)")) {
                  cells[j] = row.createCell(j);
                  cells[j].setCellType(0);
                  cells[j].setCellValue((double)rowIndex);
                  cells[j].setCellStyle(this.applyCellStyle(cells[j], j));
               } else {
                  mergeStart = j;
                  Object value = null;

                  String cellValue;
                  try {
                     if (vos[i] instanceof Map) {
                        value = ((Map)vos[i]).get(fieldNames[j]);
                     } else {
                        cellValue = "get" + fieldNames[j].substring(0, 1).toUpperCase();
                        cellValue = cellValue + (fieldNames[j].length() > 1 ? fieldNames[j].substring(1) : "");
                        value = vos[i].getClass().getMethod(cellValue).invoke(vos[i]);
                     }

                     value = value == null ? "" : value;
                  } catch (Exception var19) {
                     value = "";
                  }

                  cells[j] = row.createCell(j);
                  cells[j].setCellStyle(this.applyCellStyle(cells[j], j));
                  if (!value.getClass().equals(Short.class) && !value.getClass().equals(Long.class) && !value.getClass().equals(Integer.class)) {
                     if (!value.getClass().equals(Double.class) && !value.getClass().equals(Float.class)) {
                        cellValue = value.toString();
                        cells[j].setCellValue(new HSSFRichTextString(cellValue));
                        if (cellValue.indexOf("\n") > 0) {
                           cells[j].getCellStyle().setWrapText(true);
                           if (this.rowHeight == 0.0F) {
                              String[] lines = cellValue.split("\n");
                              if (lines.length > maxLines) {
                                 row.setHeight((short)(row.getHeight() * lines.length));
                                 maxLines = lines.length;
                              }
                           }
                        }
                     } else {
                        cells[j].setCellType(0);
                        cells[j].setCellValue(Double.parseDouble(value.toString()));
                     }
                  } else {
                     cells[j].setCellType(0);
                     cells[j].setCellValue((double)Long.parseLong(value.toString()));
                  }
               }
            }

            ++currentRowNum;
         }
      }

   }

   private HSSFCellStyle applyCellStyle(HSSFCell cell, int index) {
      return this.style[index];
   }

   private void initCellStyle(int length) {
      if (this.styleModified) {
         this.style = null;
         this.style = new HSSFCellStyle[length];

         for(int index = 0; index < length; ++index) {
            HSSFCellStyle style = this.wb.createCellStyle();
            HSSFFont font = this.wb.createFont();
            if (this.styleAlign != null && this.styleAlign.length > 0) {
               if (this.styleAlign.length == 1) {
                  style.setAlignment(this.styleAlign[0]);
               } else if (this.styleAlign.length > index) {
                  style.setAlignment(this.styleAlign[index]);
               }
            }

            if (this.styleVAlign != null && this.styleVAlign.length > 0) {
               if (this.styleVAlign.length == 1) {
                  style.setVerticalAlignment(this.styleVAlign[0]);
               } else if (this.styleVAlign.length > index) {
                  style.setVerticalAlignment(this.styleVAlign[index]);
               }
            }

            if (this.styleBold != null && this.styleBold.length > 0) {
               if (this.styleBold.length == 1) {
                  if (this.styleBold[0]) {
                     font.setBoldweight((short)700);
                  } else {
                     font.setBoldweight((short)400);
                  }

                  style.setFont(font);
               } else if (this.styleBold.length > index) {
                  if (this.styleBold[index]) {
                     font.setBoldweight((short)700);
                  } else {
                     font.setBoldweight((short)400);
                  }

                  style.setFont(font);
               }
            }

            if (this.styleItalic != null && this.styleItalic.length > 0) {
               if (this.styleItalic.length == 1) {
                  font.setItalic(this.styleItalic[0]);
                  style.setFont(font);
               } else if (this.styleItalic.length > index) {
                  font.setItalic(this.styleItalic[index]);
                  style.setFont(font);
               }
            }

            if (this.styleStrikeout != null && this.styleStrikeout.length > 0) {
               if (this.styleStrikeout.length == 1) {
                  font.setStrikeout(this.styleStrikeout[0]);
                  style.setFont(font);
               } else if (this.styleStrikeout.length > index) {
                  font.setStrikeout(this.styleStrikeout[index]);
                  style.setFont(font);
               }
            }

            if (this.styleUnderline != null && this.styleUnderline.length > 0) {
               if (this.styleUnderline.length == 1) {
                  if (this.styleUnderline[0] == 1) {
                     font.setUnderline((byte)1);
                  } else if (this.styleUnderline[0] == 2) {
                     font.setUnderline((byte)2);
                  } else {
                     font.setUnderline((byte)0);
                  }

                  style.setFont(font);
               } else if (this.styleUnderline.length > index) {
                  if (this.styleUnderline[index] == 1) {
                     font.setUnderline((byte)1);
                  } else if (this.styleUnderline[index] == 2) {
                     font.setUnderline((byte)2);
                  } else {
                     font.setUnderline((byte)0);
                  }

                  style.setFont(font);
               }
            }

            if (this.styleFontColor != null && this.styleFontColor.length > 0) {
               if (this.styleFontColor.length == 1) {
                  if (this.styleFontColor[0] > 0) {
                     font.setColor(this.styleFontColor[0]);
                     style.setFont(font);
                  }
               } else if (this.styleFontColor.length > index && this.styleFontColor[index] > 0) {
                  font.setColor(this.styleFontColor[index]);
                  style.setFont(font);
               }
            }

            if (this.styleFontName != null && this.styleFontName.length > 0) {
               if (this.styleFontName.length == 1) {
                  if (this.styleFontName[0] != null) {
                     font.setFontName(this.styleFontName[0]);
                     style.setFont(font);
                  }
               } else if (this.styleFontName.length > index && this.styleFontName[index] != null) {
                  font.setFontName(this.styleFontName[index]);
                  style.setFont(font);
               }
            }

            if (this.styleFontSize != null && this.styleFontSize.length > 0) {
               if (this.styleFontSize.length == 1) {
                  if (this.styleFontSize[0] > 0) {
                     font.setFontHeightInPoints(this.styleFontSize[0]);
                     style.setFont(font);
                  }
               } else if (this.styleFontSize.length > index && this.styleFontSize[index] > 0) {
                  font.setFontHeightInPoints(this.styleFontSize[index]);
                  style.setFont(font);
               }
            }

            if (this.styleBgColor != null && this.styleBgColor.length > 0) {
               if (this.styleBgColor.length == 1) {
                  style.setFillForegroundColor(this.styleBgColor[0]);
                  if (this.styleBgColor[0] > 0) {
                     style.setFillPattern((short)1);
                  } else {
                     style.setFillPattern((short)0);
                  }
               } else if (this.styleBgColor.length > index) {
                  style.setFillForegroundColor(this.styleBgColor[index]);
                  if (this.styleBgColor[index] > 0) {
                     style.setFillPattern((short)1);
                  } else {
                     style.setFillPattern((short)0);
                  }
               }
            }

            if (this.styleBorder != null && this.styleBorder.length > 0) {
               if (this.styleBorder.length == 1) {
                  if (this.styleBorder[0]) {
                     style.setBorderTop((short)1);
                     style.setBorderBottom((short)1);
                     style.setBorderLeft((short)1);
                     style.setBorderRight((short)1);
                  }
               } else if (this.styleBorder.length > index && this.styleBorder[index]) {
                  style.setBorderTop((short)1);
                  style.setBorderBottom((short)1);
                  style.setBorderLeft((short)1);
                  style.setBorderRight((short)1);
               }
            }

            this.style[index] = style;
         }

         this.styleModified = false;
      }
   }
}
