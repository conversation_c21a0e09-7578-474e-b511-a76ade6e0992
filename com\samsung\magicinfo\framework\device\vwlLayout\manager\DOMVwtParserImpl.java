package com.samsung.magicinfo.framework.device.vwlLayout.manager;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.vwlLayout.entity.VwlLayout;
import com.samsung.magicinfo.framework.device.vwlLayout.entity.VwlLayoutMonitor;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.util.VWLParser;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.io.StringReader;
import java.io.StringWriter;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import org.apache.logging.log4j.Logger;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

public class DOMVwtParserImpl implements VwtXmlParserInfo {
   Logger logger = LoggingManagerV2.getLogger(DOMVwtParserImpl.class);

   public DOMVwtParserImpl() {
      super();
   }

   public VwlLayout getVwtParsing(String vwtId, String fileName, String userId) {
      this.logger.info("=== start getVWTParsing ===");
      VwlLayout vwlLayout = new VwlLayout();
      VwlLayoutManager vwtDao = VwlLayoutManagerImpl.getInstance();
      FileInputStream inputStreamTemplate = null;

      try {
         String VWTPath = CommonConfig.get("VWT_HOME") + File.separator + vwtId + File.separator + fileName;
         DocumentBuilderFactory docFactory = DocumentBuilderFactory.newInstance();
         DocumentBuilder builder = docFactory.newDocumentBuilder();
         inputStreamTemplate = new FileInputStream(SecurityUtils.directoryTraversalChecker(VWTPath, (String)null));
         Document doc = builder.parse(inputStreamTemplate);
         Long device_num = 0L;
         NodeList monitoringLayout = doc.getDocumentElement().getElementsByTagName("Monitor");
         NodeList regularForm = doc.getDocumentElement().getElementsByTagName("RegularForm");
         Node regular = regularForm.item(0);
         NamedNodeMap regular_map = regular.getAttributes();
         Node monitor = monitoringLayout.item(0);
         Element element = (Element)monitor;
         String modelName = element.getElementsByTagName("Name").item(0).getChildNodes().item(0).getNodeValue();
         String isRegular = regular.getTextContent().toString();
         String x_count = regular_map.getNamedItem("width").getTextContent();
         String y_count = regular_map.getNamedItem("height").getTextContent();
         device_num = Long.parseLong(x_count) * Long.parseLong(y_count);
         vwlLayout.setVwt_id(vwtId);
         vwlLayout.setVwt_file_name(fileName);
         vwlLayout.setCreator_id(userId);
         if (isRegular.equalsIgnoreCase("true")) {
            vwlLayout.setIs_linear(true);
            vwlLayout.setDevice_model_name(modelName);
         } else {
            vwlLayout.setIs_linear(false);
         }

         vwlLayout.setX_count(Long.parseLong(x_count));
         vwlLayout.setY_count(Long.parseLong(y_count));
         vwlLayout.setDevice_number(device_num);
         NodeList modelList = doc.getDocumentElement().getElementsByTagName("Model");
         Map map = VWLParser.getModelCountMap(modelList);
         vwlLayout.setModelCountMap(map);
         vwtDao.addVwlLayoutInfo(vwlLayout);
      } catch (OutOfMemoryError var44) {
      } catch (ParserConfigurationException var45) {
         this.logger.error(var45.toString());
      } catch (FileNotFoundException var46) {
         this.logger.error(var46.toString());
      } catch (SAXException var47) {
         this.logger.error(var47.toString());
      } catch (IOException var48) {
         this.logger.error(var48.toString());
      } catch (ConfigException var49) {
         this.logger.error("", var49);
      } catch (SQLException var50) {
         this.logger.error("", var50);
      } finally {
         try {
            if (inputStreamTemplate != null) {
               inputStreamTemplate.close();
               inputStreamTemplate = null;
            }
         } catch (Exception var43) {
            this.logger.error("", var43);
         }

      }

      return vwlLayout;
   }

   public String getVwlDevMappingParsing(String fileName) throws FileNotFoundException {
      DocumentBuilderFactory docFactory = DocumentBuilderFactory.newInstance();
      DocumentBuilder builder = null;

      try {
         builder = docFactory.newDocumentBuilder();
      } catch (ParserConfigurationException var22) {
         this.logger.error("", var22);
      }

      InputStream inputStreamTemplate = null;
      inputStreamTemplate = new FileInputStream(fileName);
      Document doc = null;

      try {
         if (builder != null) {
            doc = builder.parse(inputStreamTemplate);
         }
      } catch (SAXException var19) {
         this.logger.error("", var19);
      } catch (IOException var20) {
         this.logger.error("", var20);
      } finally {
         try {
            inputStreamTemplate.close();
         } catch (IOException var18) {
            this.logger.error("", var18);
         }

      }

      if (doc != null) {
         Element contentElement = doc.getDocumentElement();
         NamedNodeMap contentAttributes = contentElement.getAttributes();
         return contentAttributes.getNamedItem("id").getChildNodes().item(0).getNodeValue();
      } else {
         this.logger.error("doc is null " + fileName);
         return null;
      }
   }

   public List getVwtMonitorInfo(String fileId, String fileName) {
      InputStream inputStreamTemplate = null;
      ArrayList vwlLayoutMonitors = new ArrayList();

      try {
         int index = fileName.lastIndexOf(".");
         String extension = fileName.substring(index + 1);
         if (!extension.equalsIgnoreCase("VWL")) {
            fileName = fileName + ".VWL";
         }

         String VWTPath = CommonConfig.get("VWT_HOME") + "/" + fileId + "/" + fileName;
         DocumentBuilderFactory docFactory = DocumentBuilderFactory.newInstance();
         DocumentBuilder builder = docFactory.newDocumentBuilder();
         inputStreamTemplate = new FileInputStream(SecurityUtils.directoryTraversalChecker(VWTPath, (String)null));
         Document doc = builder.parse(inputStreamTemplate);
         NodeList monitoringLayout = doc.getDocumentElement().getElementsByTagName("Monitor");

         for(int i = 0; i < monitoringLayout.getLength(); ++i) {
            VwlLayoutMonitor vwlLayoutMonitor = new VwlLayoutMonitor();
            Node monitor = monitoringLayout.item(i);
            Element element = (Element)monitor;
            String panel_width = element.getElementsByTagName("Width").item(1).getChildNodes().item(0).getNodeValue();
            String panel_height = element.getElementsByTagName("Height").item(1).getChildNodes().item(0).getNodeValue();
            String position_x = element.getElementsByTagName("PositionX").item(0).getChildNodes().item(0).getNodeValue();
            String position_y = element.getElementsByTagName("PositionY").item(0).getChildNodes().item(0).getNodeValue();
            String angle = element.getElementsByTagName("Angle").item(0).getChildNodes().item(0).getNodeValue();
            String map_id = element.getElementsByTagName("MapID").item(0).getChildNodes().item(0).getNodeValue();
            String LeftRight = element.getElementsByTagName("LeftRight").item(0).getChildNodes().item(0).getNodeValue();
            String TopBottom = element.getElementsByTagName("TopBottom").item(0).getChildNodes().item(0).getNodeValue();
            vwlLayoutMonitor.setPanel_width(panel_width);
            vwlLayoutMonitor.setPanel_height(panel_height);
            vwlLayoutMonitor.setAngle(angle);
            vwlLayoutMonitor.setPosition_x(position_x);
            vwlLayoutMonitor.setPosition_y(position_y);
            vwlLayoutMonitor.setVwt_id(fileName);
            vwlLayoutMonitor.setMap_id(map_id);
            vwlLayoutMonitor.setBezel_leftright(LeftRight);
            vwlLayoutMonitor.setBezel_topbottom(TopBottom);
            vwlLayoutMonitors.add(vwlLayoutMonitor);
         }
      } catch (OutOfMemoryError var42) {
      } catch (ParserConfigurationException var43) {
         this.logger.error(var43.toString());
      } catch (FileNotFoundException var44) {
         this.logger.error(var44.toString());
      } catch (SAXException var45) {
         this.logger.error(var45.toString());
      } catch (IOException var46) {
         this.logger.error(var46.toString());
      } catch (ConfigException var47) {
         this.logger.error("", var47);
      } finally {
         try {
            if (inputStreamTemplate != null) {
               inputStreamTemplate.close();
               inputStreamTemplate = null;
            }
         } catch (Exception var41) {
            this.logger.error("", var41);
         }

      }

      return vwlLayoutMonitors;
   }

   public List getVwlMappingDevices(String filePath) {
      InputStream inputStreamTemplate = null;
      ArrayList vwlLayoutMonitors = new ArrayList();

      DocumentBuilderFactory docFactory;
      try {
         docFactory = DocumentBuilderFactory.newInstance();
         DocumentBuilder builder = docFactory.newDocumentBuilder();
         inputStreamTemplate = new FileInputStream(filePath);
         Document doc = builder.parse(inputStreamTemplate);
         NodeList monitoringLayout = doc.getDocumentElement().getElementsByTagName("Monitor");

         for(int i = 0; i < monitoringLayout.getLength(); ++i) {
            VwlLayoutMonitor vwlLayoutMonitor = new VwlLayoutMonitor();
            Node monitor = monitoringLayout.item(i);
            Element element = (Element)monitor;
            String panel_width = element.getElementsByTagName("Width").item(1).getChildNodes().item(0).getNodeValue();
            String panel_height = element.getElementsByTagName("Height").item(1).getChildNodes().item(0).getNodeValue();
            String position_x = element.getElementsByTagName("PositionX").item(0).getChildNodes().item(0).getNodeValue();
            String position_y = element.getElementsByTagName("PositionY").item(0).getChildNodes().item(0).getNodeValue();
            String angle = element.getElementsByTagName("Angle").item(0).getChildNodes().item(0).getNodeValue();
            String map_id = element.getElementsByTagName("MapID").item(0).getChildNodes().item(0).getNodeValue();
            String LeftRight = element.getElementsByTagName("LeftRight").item(0).getChildNodes().item(0).getNodeValue();
            String TopBottom = element.getElementsByTagName("TopBottom").item(0).getChildNodes().item(0).getNodeValue();
            String DevID = element.getElementsByTagName("DevID").item(0).getChildNodes().item(0).getNodeValue();
            String modelName = element.getElementsByTagName("Name").item(0).getChildNodes().item(0).getNodeValue();
            vwlLayoutMonitor.setPanel_width(panel_width);
            vwlLayoutMonitor.setPanel_height(panel_height);
            vwlLayoutMonitor.setAngle(angle);
            vwlLayoutMonitor.setPosition_x(position_x);
            vwlLayoutMonitor.setPosition_y(position_y);
            vwlLayoutMonitor.setMap_id(map_id);
            vwlLayoutMonitor.setBezel_leftright(LeftRight);
            vwlLayoutMonitor.setBezel_topbottom(TopBottom);
            vwlLayoutMonitor.setDevId(DevID);
            vwlLayoutMonitor.setModelName(modelName);
            vwlLayoutMonitors.add(vwlLayoutMonitor);
         }

         return vwlLayoutMonitors;
      } catch (OutOfMemoryError var41) {
         this.logger.error(var41.toString());
         docFactory = null;
         return docFactory;
      } catch (ParserConfigurationException var42) {
         this.logger.error(var42.toString());
         docFactory = null;
      } catch (FileNotFoundException var43) {
         this.logger.error(var43.toString());
         docFactory = null;
         return docFactory;
      } catch (SAXException var44) {
         this.logger.error(var44.toString());
         docFactory = null;
         return docFactory;
      } catch (IOException var45) {
         this.logger.error(var45.toString());
         docFactory = null;
         return docFactory;
      } catch (NullPointerException var46) {
         this.logger.error(var46.toString());
         docFactory = null;
         return docFactory;
      } finally {
         try {
            if (inputStreamTemplate != null) {
               inputStreamTemplate.close();
               inputStreamTemplate = null;
            }
         } catch (Exception var40) {
            this.logger.error("", var40);
         }

      }

      return docFactory;
   }

   public String getVwtMonitorInfo(String filePath) {
      String RegularForm = null;
      FileInputStream inputStreamTemplate = null;

      try {
         DocumentBuilderFactory docFactory = DocumentBuilderFactory.newInstance();
         DocumentBuilder builder = docFactory.newDocumentBuilder();
         inputStreamTemplate = new FileInputStream(filePath);
         Document doc = builder.parse(inputStreamTemplate);
         NodeList regularForm = doc.getDocumentElement().getElementsByTagName("RegularForm");
         Node regular = regularForm.item(0);
         NamedNodeMap regular_map = regular.getAttributes();
         Element contentElement = doc.getDocumentElement();
         contentElement.getAttributes();
         String isRegular = regular.getTextContent().toString();
         if (isRegular.equalsIgnoreCase("true")) {
            String x_count = regular_map.getNamedItem("width").getTextContent();
            String y_count = regular_map.getNamedItem("height").getTextContent();
            RegularForm = x_count + "X" + y_count;
         } else {
            RegularForm = "nonlinear";
         }
      } catch (OutOfMemoryError var31) {
      } catch (ParserConfigurationException var32) {
         this.logger.error(var32.toString());
      } catch (FileNotFoundException var33) {
         this.logger.error(var33.toString());
      } catch (SAXException var34) {
         this.logger.error(var34.toString());
      } catch (IOException var35) {
         this.logger.error(var35.toString());
      } finally {
         try {
            if (inputStreamTemplate != null) {
               inputStreamTemplate.close();
               inputStreamTemplate = null;
            }
         } catch (Exception var30) {
            this.logger.error("", var30);
         }

      }

      return RegularForm;
   }

   public String getTempVwtParsing(String fileName, String userId) {
      VwlLayout vwlLayout = new VwlLayout();
      VwlLayoutManager vwtDao = VwlLayoutManagerImpl.getInstance();
      String vwtId = UUID.randomUUID().toString();
      InputStream inputStreamTemplate = null;
      BufferedReader in = null;
      BufferedWriter out = null;

      try {
         String VWTPath = CommonConfig.get("VWT_HOME") + "/temp/" + fileName;
         DocumentBuilderFactory docFactory = DocumentBuilderFactory.newInstance();
         DocumentBuilder builder = docFactory.newDocumentBuilder();
         inputStreamTemplate = new FileInputStream(SecurityUtils.directoryTraversalChecker(VWTPath, (String)null));
         Document doc = builder.parse(inputStreamTemplate);
         NodeList regularForm = doc.getDocumentElement().getElementsByTagName("RegularForm");
         NodeList monitoringLayout = doc.getDocumentElement().getElementsByTagName("Monitor");
         Node regular = regularForm.item(0);
         NamedNodeMap regular_map = regular.getAttributes();
         Element contentElement = doc.getDocumentElement();
         NamedNodeMap contentAttributes = contentElement.getAttributes();
         contentAttributes.getNamedItem("id").setTextContent(vwtId);
         String isRegular = regular.getTextContent().toString();
         String x_count = regular_map.getNamedItem("width").getTextContent();
         String y_count = regular_map.getNamedItem("height").getTextContent();
         Long device_number = Long.valueOf(String.valueOf(monitoringLayout.getLength()));
         NodeList modelList = doc.getDocumentElement().getElementsByTagName("Model");
         Map map = VWLParser.getModelCountMap(modelList);
         vwlLayout.setVwt_id(vwtId);
         vwlLayout.setVwt_file_name(fileName);
         vwlLayout.setCreator_id(userId);
         vwlLayout.setDevice_number(device_number);
         vwlLayout.setModelCountMap(map);
         if (isRegular.equalsIgnoreCase("true")) {
            vwlLayout.setIs_linear(true);
         } else {
            vwlLayout.setIs_linear(false);
         }

         vwlLayout.setX_count(Long.parseLong(x_count));
         vwlLayout.setY_count(Long.parseLong(y_count));
         vwtDao.addVwlLayoutInfo(vwlLayout);
         TransformerFactory transfac = TransformerFactory.newInstance();
         Transformer trans = transfac.newTransformer();
         trans.setOutputProperty("method", "xml");
         trans.setOutputProperty("encoding", "UTF-8");
         trans.setOutputProperty("indent", "yes");
         StringWriter sw = new StringWriter();
         StreamResult result = new StreamResult(sw);
         DOMSource source = new DOMSource(doc);
         trans.transform(source, result);
         String xmlString = sw.toString();
         in = new BufferedReader(new StringReader(xmlString));
         out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(VWTPath), "UTF-8"));

         String s;
         while((s = in.readLine()) != null) {
            out.write(s);
            out.newLine();
         }
      } catch (OutOfMemoryError var80) {
      } catch (ParserConfigurationException var81) {
         this.logger.error(var81.toString());
      } catch (FileNotFoundException var82) {
         this.logger.error(var82.toString());
      } catch (SAXException var83) {
         this.logger.error(var83.toString());
      } catch (IOException var84) {
         this.logger.error(var84.toString());
      } catch (ConfigException var85) {
         this.logger.error("", var85);
      } catch (SQLException var86) {
         this.logger.error("", var86);
      } catch (TransformerConfigurationException var87) {
         this.logger.error("", var87);
      } catch (TransformerException var88) {
         this.logger.error("", var88);
      } finally {
         try {
            if (inputStreamTemplate != null) {
               inputStreamTemplate.close();
               inputStreamTemplate = null;
            }
         } catch (Exception var79) {
            this.logger.error("", var79);
         }

         try {
            if (in != null) {
               in.close();
            }
         } catch (Exception var78) {
            this.logger.error("", var78);
         }

         try {
            if (out != null) {
               out.close();
            }
         } catch (Exception var77) {
            this.logger.error("", var77);
         }

      }

      return vwtId;
   }

   public boolean setVwtMonitorInfo(String fileId, String fileName) {
      this.logger.info("=== start setVwtMonitorInfo ===");
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      String deviceId = null;
      FileInputStream inputStreamTemplate = null;

      try {
         String VWTPath = CommonConfig.get("VWT_HOME") + File.separator + fileId + File.separator + fileName;
         DocumentBuilderFactory docFactory = DocumentBuilderFactory.newInstance();
         DocumentBuilder builder = docFactory.newDocumentBuilder();
         inputStreamTemplate = new FileInputStream(SecurityUtils.directoryTraversalChecker(VWTPath, (String)null));
         Document doc = builder.parse(inputStreamTemplate);
         NodeList monitoringLayout = doc.getDocumentElement().getElementsByTagName("Monitor");
         NodeList BezelSizeLayout = doc.getDocumentElement().getElementsByTagName("BezelSize");

         for(int i = 0; i < monitoringLayout.getLength(); ++i) {
            VwlLayoutMonitor vwlLayoutMonitor = new VwlLayoutMonitor();
            Node monitor = monitoringLayout.item(i);
            Element element = (Element)monitor;
            Node bezel = BezelSizeLayout.item(i);
            Element element_bezel = (Element)bezel;
            String panel_width = element.getElementsByTagName("Width").item(1).getChildNodes().item(0).getNodeValue();
            String panel_height = element.getElementsByTagName("Height").item(1).getChildNodes().item(0).getNodeValue();
            String position_x = element.getElementsByTagName("PositionX").item(0).getChildNodes().item(0).getNodeValue();
            String position_y = element.getElementsByTagName("PositionY").item(0).getChildNodes().item(0).getNodeValue();
            String angle = element.getElementsByTagName("Angle").item(0).getChildNodes().item(0).getNodeValue();
            String bezel_leftright = element_bezel.getElementsByTagName("LeftRight").item(0).getChildNodes().item(0).getNodeValue();
            String bezel_topbottom = element_bezel.getElementsByTagName("TopBottom").item(0).getChildNodes().item(0).getNodeValue();
            deviceId = element.getElementsByTagName("DevID").item(0).getChildNodes().item(0).getNodeValue();
            String map_id = element.getElementsByTagName("MapID").item(0).getChildNodes().item(0).getNodeValue();
            Long device_number = Long.valueOf(String.valueOf(monitoringLayout.getLength()));
            vwlLayoutMonitor.setPanel_width(panel_width);
            vwlLayoutMonitor.setPanel_height(panel_height);
            vwlLayoutMonitor.setAngle(angle);
            vwlLayoutMonitor.setPosition_x(position_x);
            vwlLayoutMonitor.setPosition_y(position_y);
            vwlLayoutMonitor.setVwt_id(fileId);
            vwlLayoutMonitor.setBezel_leftright(bezel_leftright);
            vwlLayoutMonitor.setBezel_topbottom(bezel_topbottom);
            vwlLayoutMonitor.setDevice_number(device_number);
            vwlLayoutMonitor.setMap_id(map_id);
            deviceDao.addVwtInfo(vwlLayoutMonitor, deviceId);
         }

         DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
         Element contentElement = doc.getDocumentElement();
         NamedNodeMap contentAttributes = contentElement.getAttributes();
         String vwtId = contentAttributes.getNamedItem("id").getTextContent();
         deviceGroupInfo.setVwtId(deviceId, vwtId);
      } catch (OutOfMemoryError var47) {
      } catch (ParserConfigurationException var48) {
         this.logger.error(var48.toString());
      } catch (FileNotFoundException var49) {
         this.logger.error(var49.toString());
      } catch (SAXException var50) {
         this.logger.error(var50.toString());
      } catch (IOException var51) {
         this.logger.error(var51.toString());
      } catch (ConfigException var52) {
         this.logger.error("", var52);
      } catch (SQLException var53) {
         this.logger.error("", var53);
      } finally {
         try {
            if (inputStreamTemplate != null) {
               inputStreamTemplate.close();
               inputStreamTemplate = null;
            }
         } catch (Exception var46) {
            this.logger.error("", var46);
         }

      }

      return true;
   }

   public boolean setMapIdInfo(String deviceID) {
      this.logger.info("=== DB update map_id ===");
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      String deviceId = deviceID;
      FileInputStream inputStreamTemplate = null;

      try {
         String fileName = deviceDao.getVwtIdByDeviceId(deviceId);
         String VWTPath = CommonConfig.get("VWT_HOME") + File.separator + fileName + File.separator + fileName + ".VWL";
         DocumentBuilderFactory docFactory = DocumentBuilderFactory.newInstance();
         DocumentBuilder builder = docFactory.newDocumentBuilder();
         inputStreamTemplate = new FileInputStream(SecurityUtils.directoryTraversalChecker(VWTPath, (String)null));
         Document doc = builder.parse(inputStreamTemplate);
         NodeList monitoringLayout = doc.getDocumentElement().getElementsByTagName("Monitor");
         NodeList BezelSizeLayout = doc.getDocumentElement().getElementsByTagName("BezelSize");

         for(int i = 0; i < monitoringLayout.getLength(); ++i) {
            VwlLayoutMonitor vwlLayoutMonitor = new VwlLayoutMonitor();
            Node monitor = monitoringLayout.item(i);
            Element element = (Element)monitor;
            Node bezel = BezelSizeLayout.item(i);
            Element element_bezel = (Element)bezel;
            String panel_width = element.getElementsByTagName("Width").item(1).getChildNodes().item(0).getNodeValue();
            String panel_height = element.getElementsByTagName("Height").item(1).getChildNodes().item(0).getNodeValue();
            String position_x = element.getElementsByTagName("PositionX").item(0).getChildNodes().item(0).getNodeValue();
            String position_y = element.getElementsByTagName("PositionY").item(0).getChildNodes().item(0).getNodeValue();
            String angle = element.getElementsByTagName("Angle").item(0).getChildNodes().item(0).getNodeValue();
            String bezel_leftright = element_bezel.getElementsByTagName("LeftRight").item(0).getChildNodes().item(0).getNodeValue();
            String bezel_topbottom = element_bezel.getElementsByTagName("TopBottom").item(0).getChildNodes().item(0).getNodeValue();
            deviceId = element.getElementsByTagName("DevID").item(0).getChildNodes().item(0).getNodeValue();
            String map_id = element.getElementsByTagName("MapID").item(0).getChildNodes().item(0).getNodeValue();
            Long device_number = Long.valueOf(String.valueOf(monitoringLayout.getLength()));
            vwlLayoutMonitor.setPanel_width(panel_width);
            vwlLayoutMonitor.setPanel_height(panel_height);
            vwlLayoutMonitor.setAngle(angle);
            vwlLayoutMonitor.setPosition_x(position_x);
            vwlLayoutMonitor.setPosition_y(position_y);
            vwlLayoutMonitor.setVwt_id(fileName);
            vwlLayoutMonitor.setBezel_leftright(bezel_leftright);
            vwlLayoutMonitor.setBezel_topbottom(bezel_topbottom);
            vwlLayoutMonitor.setDevice_number(device_number);
            vwlLayoutMonitor.setMap_id(map_id);
            deviceDao.addVwtInfo(vwlLayoutMonitor, deviceId);
         }
      } catch (OutOfMemoryError var47) {
      } catch (ParserConfigurationException var48) {
         this.logger.error(var48.toString());
      } catch (FileNotFoundException var49) {
         this.logger.error(var49.toString());
      } catch (SAXException var50) {
         this.logger.error(var50.toString());
      } catch (IOException var51) {
         this.logger.error(var51.toString());
      } catch (ConfigException var52) {
         this.logger.error("", var52);
      } catch (SQLException var53) {
         this.logger.error("", var53);
      } finally {
         try {
            if (inputStreamTemplate != null) {
               inputStreamTemplate.close();
               inputStreamTemplate = null;
            }
         } catch (Exception var46) {
            this.logger.error("", var46);
         }

      }

      return true;
   }

   public List getVwtWithModelList(List vwlLayouts, Map modelList) {
      List vwlLayoutList = new ArrayList();
      FileInputStream inputStreamTemplate = null;

      try {
         for(int i = 0; i < vwlLayouts.size(); ++i) {
            HashMap modelListInFile = new HashMap();
            String VWTPath = CommonConfig.get("VWT_HOME") + File.separator + ((VwlLayout)vwlLayouts.get(i)).getVwt_id() + File.separator + ((VwlLayout)vwlLayouts.get(i)).getVwt_file_name();
            int index = VWTPath.lastIndexOf(".");
            String extension = VWTPath.substring(index + 1);
            if (!extension.equalsIgnoreCase("VWL")) {
               VWTPath = VWTPath + ".VWL";
            }

            DocumentBuilderFactory docFactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = docFactory.newDocumentBuilder();
            inputStreamTemplate = new FileInputStream(SecurityUtils.directoryTraversalChecker(VWTPath, (String)null));
            Document doc = builder.parse(inputStreamTemplate);
            NodeList monitoringLayout = doc.getDocumentElement().getElementsByTagName("Monitor");

            for(int j = 0; j < monitoringLayout.getLength(); ++j) {
               new VwlLayoutMonitor();
               Node monitor = monitoringLayout.item(j);
               Element element = (Element)monitor;
               String deviceModelName = element.getElementsByTagName("Name").item(0).getChildNodes().item(0).getNodeValue();
               if (modelListInFile.containsKey(deviceModelName)) {
                  modelListInFile.put(deviceModelName, Integer.valueOf((Integer)modelListInFile.get(deviceModelName)) + 1);
               } else {
                  modelListInFile.put(deviceModelName, 1);
               }
            }

            Iterator itr = modelList.keySet().iterator();

            while(itr.hasNext()) {
               String key = (String)itr.next();
               if (!modelListInFile.containsKey(key) || modelListInFile.get(key) != modelList.get(key)) {
                  break;
               }

               vwlLayoutList.add(vwlLayouts.get(i));
            }
         }

         ArrayList var43 = vwlLayoutList;
         return var43;
      } catch (ParserConfigurationException var36) {
         this.logger.error(var36.toString());
      } catch (ConfigException var37) {
         this.logger.error("", var37);
      } catch (FileNotFoundException var38) {
         this.logger.error("", var38);
      } catch (SAXException var39) {
         this.logger.error("", var39);
      } catch (IOException var40) {
         this.logger.error("", var40);
      } catch (Exception var41) {
         this.logger.error("", var41);
      } finally {
         if (inputStreamTemplate != null) {
            try {
               inputStreamTemplate.close();
            } catch (Exception var35) {
            }
         }

      }

      return null;
   }
}
