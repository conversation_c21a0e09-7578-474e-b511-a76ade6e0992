package com.samsung.magicinfo.webauthor2.repository.model;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.FIELD)
public class ServerSystemInfoDataResponse implements Serializable {
  private static final long serialVersionUID = 1631346442386561632L;
  
  @XmlAttribute
  private String code;
  
  @XmlElement
  private String errorMessage;
  
  @XmlElement(name = "responseClass")
  private ServerSystemInfoData responseClass;
  
  public String getCode() {
    return this.code;
  }
  
  public String getErrorMessage() {
    return this.errorMessage;
  }
  
  public ServerSystemInfoData getResponseClass() {
    return this.responseClass;
  }
}
