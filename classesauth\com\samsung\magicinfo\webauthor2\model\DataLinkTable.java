package com.samsung.magicinfo.webauthor2.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.samsung.magicinfo.webauthor2.repository.model.DatalinkTableEntityData;
import com.samsung.magicinfo.webauthor2.repository.model.dlk.DLKTableData;
import java.util.ArrayList;
import java.util.List;

public class DataLinkTable {
  @JsonIgnore
  private final String serverName;
  
  @JsonIgnore
  private final String type;
  
  @JsonIgnore
  private final String svrcName;
  
  private final String dynaName;
  
  private final String name;
  
  private final Boolean isDataView;
  
  public DataLinkTable(String serverName, String type, String svrcName, String dynaName, String name, Boolean isDataView) {
    this.serverName = serverName;
    this.type = type;
    this.svrcName = svrcName;
    this.dynaName = dynaName;
    this.name = name;
    this.isDataView = Boolean.valueOf((isDataView == null) ? false : isDataView.booleanValue());
  }
  
  public String getType() {
    return this.type;
  }
  
  public String getServerName() {
    return this.serverName;
  }
  
  public String getSvrcName() {
    return this.svrcName;
  }
  
  public String getDynaName() {
    return this.dynaName;
  }
  
  public String getName() {
    return this.name;
  }
  
  public Boolean getIsDataView() {
    return this.isDataView;
  }
  
  public static DataLinkTable fromData(String serverName, DLKTableData data) {
    return new DataLinkTable(serverName, data.getType(), data.getSvrcName(), data.getDynaName(), data.getName(), data.getIsDataView());
  }
  
  public static List<DataLinkTable> fromData(String serverName, List<DLKTableData> datas) {
    List<DataLinkTable> dataLinkTables = new ArrayList<>();
    for (DLKTableData data : datas)
      dataLinkTables.add(fromData(serverName, data)); 
    return dataLinkTables;
  }
  
  public static DataLinkTable fromDatalinkTableEntity(String serverName, DatalinkTableEntityData data) {
    return new DataLinkTable(serverName, "", data.getServiceName(), data.getDynaName(), data.getTableName(), data.getIsDataView());
  }
  
  public static List<DataLinkTable> fromDatalinkTableEntityList(String serverName, List<DatalinkTableEntityData> datas) {
    List<DataLinkTable> dataLinkTables = new ArrayList<>();
    for (DatalinkTableEntityData data : datas)
      dataLinkTables.add(fromDatalinkTableEntity(serverName, data)); 
    return dataLinkTables;
  }
}
