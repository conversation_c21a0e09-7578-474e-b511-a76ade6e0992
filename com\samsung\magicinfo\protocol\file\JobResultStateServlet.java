package com.samsung.magicinfo.protocol.file;

import com.samsung.common.exception.ExceptionCode;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.device.job.manager.JobManager;
import com.samsung.magicinfo.framework.device.job.manager.JobManagerImpl;
import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.Logger;

public class JobResultStateServlet extends HttpServlet {
   private static final long serialVersionUID = -1446563829819803140L;
   private Logger logger = LoggingManagerV2.getLogger(JobResultStateServlet.class);

   public JobResultStateServlet() {
      super();
   }

   public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");
      this.doPost(request, response);
   }

   public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");

      try {
         String jobId = request.getHeader("JobID");
         String jobState = request.getHeader("JOB_STATE");
         if (!jobState.equals("COMPLETE")) {
            JobManager jobMgr = JobManagerImpl.getInstance();
            boolean rt = jobMgr.deleteJob(jobId);
            if (!rt) {
               response.sendError(500, ExceptionCode.HTTP500[2]);
            }
         }
      } catch (Exception var7) {
         response.sendError(600, var7.toString());
         this.logger.error(var7);
      }

   }
}
