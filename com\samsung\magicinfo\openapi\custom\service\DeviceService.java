package com.samsung.magicinfo.openapi.custom.service;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.exception.BasicException;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.MDCTimeStrUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.WakeOnLan;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemInfoConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemSetupConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTimeConf;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.log.entity.RemoteJobLogEntity;
import com.samsung.magicinfo.framework.device.ruleProcessing.entity.Alarm;
import com.samsung.magicinfo.framework.device.ruleProcessing.entity.Fault;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManager;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManagerImpl;
import com.samsung.magicinfo.framework.monitoring.entity.ContentList;
import com.samsung.magicinfo.framework.monitoring.entity.CurrentPlayingEntity;
import com.samsung.magicinfo.framework.monitoring.entity.DashboardEntity;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.scheduler.entity.SelectConditionScheduleAdmin;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleAdminInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleAdminInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManager;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManagerImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.openapi.auth.TokenRegistry;
import com.samsung.magicinfo.openapi.custom.domain.device.DeviceIF;
import com.samsung.magicinfo.openapi.custom.domain.device.DeviceImpl;
import com.samsung.magicinfo.openapi.custom.openEntity.ResultList;
import com.samsung.magicinfo.openapi.custom.openEntity.device.Coordinates;
import com.samsung.magicinfo.openapi.custom.openEntity.device.CurrentPlayingInfo;
import com.samsung.magicinfo.openapi.custom.openEntity.device.DashBoard;
import com.samsung.magicinfo.openapi.custom.openEntity.device.DeviceAlarm;
import com.samsung.magicinfo.openapi.custom.openEntity.device.DeviceBasicInfo;
import com.samsung.magicinfo.openapi.custom.openEntity.device.DeviceCondition;
import com.samsung.magicinfo.openapi.custom.openEntity.device.DeviceContent;
import com.samsung.magicinfo.openapi.custom.openEntity.device.DeviceDisplayInfo;
import com.samsung.magicinfo.openapi.custom.openEntity.device.DeviceFault;
import com.samsung.magicinfo.openapi.custom.openEntity.device.DeviceSystemInfo;
import com.samsung.magicinfo.openapi.custom.openEntity.device.DeviceTimeClockInfo;
import com.samsung.magicinfo.openapi.custom.openEntity.device.DeviceTimeHolidayInfo;
import com.samsung.magicinfo.openapi.custom.openEntity.device.DeviceTimeInfo;
import com.samsung.magicinfo.openapi.custom.openEntity.device.DeviceTimeTimerInfo;
import com.samsung.magicinfo.openapi.custom.openEntity.device.RemoteJob;
import com.samsung.magicinfo.openapi.custom.service.builder.DeviceVncViewerJnlpStringBuilder;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.interfaces.WSCall;
import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import org.apache.logging.log4j.Logger;
import org.apache.xml.security.utils.Base64;
import org.springframework.context.annotation.Scope;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;

@Service("openApiDeviceService")
@Scope("prototype")
public class DeviceService {
   static Logger logger = LoggingManagerV2.getLogger(DeviceService.class);
   String token = null;
   private TokenRegistry tokenRegistry = TokenRegistry.getTokenRegistry();

   public DeviceService() {
      super();
   }

   public boolean deleteDeviceInfo(String deviceId) {
      DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
      boolean result = false;

      try {
         result = deviceMgr.deleteDevice(deviceId);
         if (result) {
            MonitoringManager monMgr = MonitoringManagerImpl.getInstance();
            monMgr.connectionReload(deviceId, 0);
            monMgr.scheduleReload(deviceId, 0);
            WSCall.setPlayerRequest(deviceId, "agent restart");
         }
      } catch (SQLException var5) {
         logger.error(var5);
      } catch (BasicException var6) {
         logger.error(var6);
      }

      return result;
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public DeviceAlarm getAlarmInfo(String alarmId) {
      DeviceAlarm result = new DeviceAlarm();

      try {
         DeviceIF mgr = DeviceImpl.getInstance();
         Alarm fault = mgr.getDeviceAlarmInfo(alarmId);
         if (fault != null) {
            result.setAlarmId(alarmId);
            result.setAlarmStatus(fault.getAlarm_status());
            result.setAlarmType(fault.getAlarm_type());
            result.setDeviceId(fault.getDevice_id());
            result.setDeviceModelName(fault.getDevice_model_name());
            result.setDeviceName(fault.getDevice_name());
            result.setEventDate(fault.getEvent_date());
            result.setEventDescription(fault.getEvent_description());
            result.setEventId(fault.getEvent_id());
            result.setEventName(fault.getEvent_name());
            result.setFaultId(fault.getFault_id());
         }
      } catch (SQLException var5) {
         logger.error(var5);
      }

      return result;
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public ResultList getAlarmList(int startPos, int pageSize, String sortName, String sortDir, String searchText) {
      DeviceIF mgr = DeviceImpl.getInstance();
      ResultList resultList = new ResultList();
      PagedListInfo pagedList = null;
      ArrayList openDeviceList = new ArrayList();

      try {
         pagedList = mgr.getPagedAlarmDeviceList(startPos, pageSize, sortName, sortDir, searchText, (Timestamp)null);
         List devList = pagedList.getPagedResultList();

         for(int i = 0; i < devList.size(); ++i) {
            Alarm alarm = (Alarm)devList.get(i);
            DeviceAlarm device = new DeviceAlarm();
            device.setAlarmId(alarm.getAlarm_id());
            device.setDeviceId(alarm.getDevice_id());
            device.setDeviceName(alarm.getDevice_name());
            device.setDeviceModelName(alarm.getDevice_model_name());
            device.setEventDate(alarm.getEvent_date());
            device.setEventName(alarm.getEvent_name());
            device.setEventDescription(alarm.getEvent_description());
            device.setFaultId(alarm.getFault_id());
            device.setAlarmDetailListId(alarm.getAlarm_detail_list_id());
            device.setAlarmStatus(alarm.getAlarm_status());
            device.setAlarmType(alarm.getAlarm_type());
            device.setMoEvent(alarm.getMo_event());
            device.setMopath(alarm.getMopath());
            device.setMovalue(alarm.getMovalue());
            openDeviceList.add(device);
         }

         resultList.setResultList(openDeviceList);
         resultList.setTotalCount(pagedList.getTotalRowCount());
      } catch (SQLException var14) {
         logger.error(var14);
      } catch (ConfigException var15) {
         logger.error(var15);
      }

      return resultList;
   }

   public DashBoard getDashBoardInfo() {
      DashBoard dashboard = new DashBoard();
      boolean canShowDevice = true;
      if (canShowDevice) {
         MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
         DashboardEntity entity = motMgr.getDashboardStatus();
         if (entity != null) {
            dashboard.setConnectCount(entity.getConnectionCount());
            dashboard.setDisconnectCount(entity.getDisconnectionCount());
            dashboard.setFaultCount(entity.getFaultDeviceCount());
            dashboard.setNonApprovalCount((long)entity.getNonApprovalCount());
            DeviceIF devIF = DeviceImpl.getInstance();
            DeviceCondition condition = new DeviceCondition();
            condition.setListMode("DEVICE_LIST_BY_ALARM_STATE");
            int alarmCnt = 0;

            try {
               alarmCnt = devIF.getCntAlarmDeviceList();
            } catch (ConfigException var9) {
               logger.error(var9);
            } catch (SQLException var10) {
               logger.error(var10);
            }

            dashboard.setAlarmCount((long)alarmCnt);
         }
      }

      return dashboard;
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public DeviceBasicInfo getDeviceBasicInfo(String deviceId) {
      DeviceBasicInfo result = null;

      try {
         DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
         MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
         DeviceGeneralConf info = deviceInfo.getDeviceGeneralConf(deviceId);
         CurrentPlayingEntity tempEntity = motMgr.getPlayingContent(deviceId);
         CurrentPlayingInfo playingEntity = new CurrentPlayingInfo();
         if (tempEntity != null) {
            playingEntity.setScheduleId(tempEntity.getProgramId());
            playingEntity.setScheduleName(tempEntity.getProgramName());
            playingEntity.setVersion(tempEntity.getVersion());
            playingEntity.setPanelStatus(tempEntity.getPanelStatus());
            playingEntity.setInputSource((long)tempEntity.getInputSource());
            playingEntity.setDirectChannel(tempEntity.getDirectChannel());
            List conList = new ArrayList();

            for(int i = 0; i < tempEntity.getContentLists().size(); ++i) {
               DeviceContent con = new DeviceContent();
               con.setContentId(((ContentList)tempEntity.getContentLists().get(i)).getContentId());
               con.setContentName(((ContentList)tempEntity.getContentLists().get(i)).getContentName());
               con.setFrameIndex(((ContentList)tempEntity.getContentLists().get(i)).getFrameIndex());
               con.setFrameName(((ContentList)tempEntity.getContentLists().get(i)).getFrameName());
               con.setMainFrame(((ContentList)tempEntity.getContentLists().get(i)).isMainFrame());
               con.setThumbnailFileId(((ContentList)tempEntity.getContentLists().get(i)).getThumbnailFileId());
               con.setThumbnailFileName(((ContentList)tempEntity.getContentLists().get(i)).getThumbnailFileName());
               conList.add(con);
            }

            playingEntity.setContentLists(conList);
            SelectConditionScheduleAdmin conObj = new SelectConditionScheduleAdmin();
            conObj.setProgram_id(tempEntity.getProgramId());
            ScheduleAdminInfo schMgr = ScheduleAdminInfoImpl.getInstance();
            List frameList = schMgr.getProgramFramesInfo(conObj);
            if (frameList != null) {
               playingEntity.setFrameCount((long)frameList.size());
            }
         }

         if (info != null) {
            result = new DeviceBasicInfo();
            result.setApplicationVersion(info.getApplication_version());
            result.setCreateDate(info.getCreate_date());
            result.setDeviceId(info.getDevice_id());
            result.setDeviceModelCode(info.getDevice_model_code());
            result.setDeviceModelName(info.getDevice_model_name());
            result.setDeviceName(info.getDevice_name());
            result.setEwfState(info.getEwf_state());
            result.setFirmwareVersion(info.getFirmware_version());
            result.setGroupId(info.getGroup_id());
            result.setGroupName(info.getGroup_name());
            result.setIpAddress(info.getIp_address());
            result.setLocation(info.getLocation());
            result.setMacAddress(info.getMac_address());
            result.setNetworkAdapter(info.getNetwork_adapter());
            result.setNetworkDriver(info.getNetwork_driver());
            result.setOsImageVersion(info.getOs_image_version());
            result.setResolution(info.getResolution());
            result.setRuleVersion(info.getRule_version());
            result.setScreenSize(info.getScreen_size());
            result.setSerialDecimal(info.getSerial_decimal());
            result.setTunnelingServer(info.getTunneling_server());
            result.setVideoAdapter(info.getVideo_adapter());
            result.setVideoDriver(info.getVideo_driver());
            result.setVideoMemory(info.getVideo_memory());
            result.setCurrentPlayingInfo(playingEntity);
            result.setIsConnected(motMgr.isConnected(deviceId));
            Coordinates coordinates = new Coordinates();
            coordinates.setLatitude(info.getLatitude());
            coordinates.setLongitude(info.getLongitude());
            coordinates.setAltitude(info.getAltitude());
            result.setCoordinates(coordinates);
         }
      } catch (Exception var12) {
         logger.error(var12);
      }

      return result;
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public DeviceDisplayInfo getDeviceDisplayInfo(String deviceId, String mode, int reqTimeOut) {
      DeviceDisplayInfo result = new DeviceDisplayInfo();
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      if (mode == null) {
         mode = "LIST";
      }

      try {
         confMgr.reqGetDisplayFromDevice(deviceId, this.getToken(), mode);
      } catch (Exception var9) {
         logger.error(var9);
         return result;
      }

      DeviceDisplayConf info = null;

      for(int i = 0; i < reqTimeOut; ++i) {
         info = confMgr.getDisplayResultSet(deviceId, this.getToken(), "GET_DEVICE_DISPLAY_CONF", mode);
         if (info != null) {
            result.setBasicDirectChannel(info.getBasic_direct_channel());
            result.setBasicMute(info.getBasic_mute());
            result.setBasicPanelStatus(info.getBasic_panel_status());
            result.setBasicPower(info.getBasic_power());
            result.setBasicSource(info.getBasic_source());
            result.setBasicVolume(info.getBasic_volume());
            result.setPvSplPictureMode(info.getSpecialized_picture_mode());
            result.setPvBrightness(info.getPv_brightness());
            result.setPvColor(info.getPv_color());
            result.setPvColorTemperature(info.getPv_color_temperature());
            result.setPvColortone(info.getPv_colortone());
            result.setPvContrast(info.getPv_contrast());
            result.setPvDigitalNr(info.getPv_digitalnr());
            result.setPvFilmmode(info.getPv_filmmode());
            result.setPvHdmiBlackLevel(info.getPv_hdmi_black_level());
            result.setPvMode(info.getPv_mode());
            result.setPvSharpness(info.getPv_sharpness());
            result.setPvSize(info.getPv_size());
            result.setPvTint(info.getPv_tint());
            result.setPvVideoPicturePositionSize(info.getPv_video_picture_position_size());
            result.setPpcBlue(info.getPpc_blue());
            result.setPpcBrightness(info.getPpc_brightness());
            result.setPpcColorTemperature(info.getPpc_color_temperature());
            result.setPpcColortone(info.getPpc_colortone());
            result.setPpcContrast(info.getPpc_contrast());
            result.setPpcGamma(info.getPpc_gamma());
            result.setPpcGreen(info.getPpc_green());
            result.setPpcHdmiBlackLevel(info.getPpc_hdmi_black_level());
            result.setPpcMagicBright(info.getPpc_magic_bright());
            result.setPpcRed(info.getPpc_red());
            result.setPpcSize(info.getPpc_size());
            result.setSoundBalance(info.getSound_balance());
            result.setSoundBass(info.getSound_bass());
            result.setSoundMode(info.getSound_mode());
            result.setSoundSrs(info.getSound_srs());
            result.setSoundTreble(info.getSound_treble());
            result.setSbBgain(info.getSb_b_offset());
            result.setSbBOffset(info.getSb_bgain());
            result.setSbGain(info.getSb_gain());
            result.setSbGgain(info.getSb_ggain());
            result.setSbGOffset(info.getSb_g_offset());
            result.setSbRgain(info.getSb_rgain());
            result.setSbROffset(info.getSb_r_offset());
            result.setSbSharp(info.getSb_sharp());
            result.setSbStatus(info.getSb_status());
            result.setMntAuto(info.getMnt_auto());
            result.setMntManual(info.getMnt_manual());
            result.setMntPixelShift(info.getMnt_pixel_shift());
            result.setMntSafetyLock(info.getMnt_safety_lock());
            result.setMntSafetyScreenRun(info.getMnt_safety_screen_run());
            result.setMntSafetyScreenTimer(info.getMnt_safety_screen_timer());
            result.setMiscAllLock(info.getMisc_all_lock());
            result.setMiscOsd(info.getMisc_osd());
            result.setMiscPanelLock(info.getMisc_panel_lock());
            result.setMiscRemocon(info.getMisc_remocon());
            result.setDiagnosisAlarmTemperature(info.getDiagnosis_alarm_temperature());
            result.setDiagnosisDisplayStatus(info.getDiagnosis_display_status());
            result.setDiagnosisMonitorTemperature(info.getDiagnosis_monitor_temperature());
            result.setDiagnosisPanelOnTime(info.getDiagnosis_panel_on_time());
            result.setAdvancedAutoPower(info.getAdvanced_auto_power());
            result.setAdvancedFanControl(info.getAdvanced_fan_control());
            result.setAdvancedFanSpeed(info.getAdvanced_fan_speed());
            result.setAdvancedOsdDisplayType(info.getAdvanced_osd_display_type());
            result.setAdvancedReset(info.getAdvanced_reset());
            result.setAdvancedStandBy(info.getAdvanced_stand_by());
            result.setAdvancedUserAutoColor(info.getAdvanced_user_auto_color());
            break;
         }

         try {
            Thread.sleep(1000L);
         } catch (InterruptedException var10) {
            logger.error(var10);
            break;
         }
      }

      return result;
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public ResultList getDeviceList(DeviceCondition condition) {
      new ArrayList();
      List openDeviceList = new ArrayList();
      ResultList resultList = new ResultList();
      DeviceIF devIF = DeviceImpl.getInstance();
      MonitoringManager motMgr = MonitoringManagerImpl.getInstance();

      try {
         try {
            List deviceList = devIF.getPagedDeviceList(condition, "iPLAYER");
            int cntDeviceList = devIF.getCntDeviceList(condition);
            Device sourceEntity = null;
            com.samsung.magicinfo.openapi.custom.openEntity.device.Device targetEntity = null;
            CurrentPlayingEntity tempEntity = null;
            CurrentPlayingInfo playingEntity = null;
            if (deviceList != null) {
               for(int i = 0; i < deviceList.size(); ++i) {
                  new Device();
                  targetEntity = new com.samsung.magicinfo.openapi.custom.openEntity.device.Device();
                  sourceEntity = (Device)deviceList.get(i);
                  targetEntity.setDeviceId(sourceEntity.getDevice_id());
                  targetEntity.setDeviceName(sourceEntity.getDevice_name());
                  targetEntity.setGroupName(sourceEntity.getGroup_name());
                  targetEntity.setGroupId(sourceEntity.getGroup_id());
                  targetEntity.setDeviceModelName(sourceEntity.getDevice_model_name());
                  targetEntity.setDeviceModelCode(sourceEntity.getDevice_model_code());
                  targetEntity.setScreenSize(sourceEntity.getScreen_size());
                  targetEntity.setResolution(sourceEntity.getResolution());
                  targetEntity.setFirmwareVersion(sourceEntity.getFirmware_version());
                  targetEntity.setOsImageVersion(sourceEntity.getOs_image_version());
                  targetEntity.setEwfState(sourceEntity.getEwf_state());
                  targetEntity.setApplicationVersion(sourceEntity.getApplication_version());
                  targetEntity.setRuleVersion(sourceEntity.getRule_version());
                  targetEntity.setIpAddress(sourceEntity.getIp_address());
                  targetEntity.setMacAddress(sourceEntity.getMac_address());
                  targetEntity.setLocation(sourceEntity.getLocation());
                  targetEntity.setTunnelingServer(sourceEntity.getTunneling_server());
                  targetEntity.setCreateDate(sourceEntity.getCreate_date());
                  targetEntity.setCreatorId(sourceEntity.getCreator_id());
                  targetEntity.setIsApproved(sourceEntity.getIs_approved());
                  targetEntity.setCapturedFileName(sourceEntity.getCaptured_file_name());
                  targetEntity.setCapturedDate(sourceEntity.getCaptured_date());
                  Coordinates coordinates = new Coordinates();
                  coordinates.setLatitude(sourceEntity.getLatitude());
                  coordinates.setLongitude(sourceEntity.getLongitude());
                  coordinates.setAltitude(sourceEntity.getAltitude());
                  targetEntity.setCoordinates(coordinates);
                  tempEntity = motMgr.getPlayingContent(sourceEntity.getDevice_id());
                  if (tempEntity != null) {
                     playingEntity = new CurrentPlayingInfo();
                     playingEntity.setScheduleId(tempEntity.getProgramId());
                     playingEntity.setScheduleName(tempEntity.getProgramName());
                     playingEntity.setVersion(tempEntity.getVersion());
                     playingEntity.setPanelStatus(tempEntity.getPanelStatus());
                     playingEntity.setInputSource((long)tempEntity.getInputSource());
                     playingEntity.setDirectChannel(tempEntity.getDirectChannel());
                     List conList = new ArrayList();

                     for(int j = 0; j < tempEntity.getContentLists().size(); ++j) {
                        DeviceContent con = new DeviceContent();
                        con.setContentId(((ContentList)tempEntity.getContentLists().get(j)).getContentId());
                        con.setContentName(((ContentList)tempEntity.getContentLists().get(j)).getContentName());
                        con.setFrameIndex(((ContentList)tempEntity.getContentLists().get(j)).getFrameIndex());
                        con.setFrameName(((ContentList)tempEntity.getContentLists().get(j)).getFrameName());
                        con.setMainFrame(((ContentList)tempEntity.getContentLists().get(j)).isMainFrame());
                        con.setThumbnailFileId(((ContentList)tempEntity.getContentLists().get(j)).getThumbnailFileId());
                        con.setThumbnailFileName(((ContentList)tempEntity.getContentLists().get(j)).getThumbnailFileName());
                        conList.add(con);
                     }

                     playingEntity.setContentLists(conList);
                  }

                  targetEntity.setCurrentPlayingInfo(playingEntity);
                  targetEntity.setConnected(motMgr.isConnected(sourceEntity.getDevice_id()));
                  openDeviceList.add(targetEntity);
               }
            }

            resultList.setTotalCount(cntDeviceList);
            resultList.setResultList(openDeviceList);
         } catch (RuntimeException var21) {
            throw var21;
         } catch (Exception var22) {
            resultList = null;
            logger.error(var22);
         }

         return resultList;
      } finally {
         ;
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public DeviceSystemInfo getDeviceSystemInfo(String deviceId, int reqTimeOut) {
      DeviceSystemInfoConf info = null;
      DeviceSystemInfo result = new DeviceSystemInfo();
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();

      try {
         confMgr.reqGetSystemInfoFromDevice(deviceId, this.getToken());
      } catch (Exception var8) {
         logger.error(var8);
         return result;
      }

      for(int i = 0; i < reqTimeOut; ++i) {
         info = confMgr.getSystemInfoResultSet(deviceId, this.getToken(), "GET_DEVICE_SYSTEM_INFO_CONF");
         if (info != null) {
            result.setCpuType(info.getCpu_type());
            result.setCpuUsage(info.getCpu_usage());
            result.setDeviceId(deviceId);
            result.setDiskSpaceAvailable(info.getDisk_space_available());
            result.setDiskSpaceUsage(info.getDisk_space_usage());
            result.setHddSize(info.getHdd_size());
            result.setLastConnectionTime(info.getLast_connection_time());
            result.setMemSize(info.getMem_size());
            result.setMonitoringInterval(info.getMonitoring_interval());
            result.setNetworkUsage(info.getNetwork_usage());
            result.setRamUsage(info.getRam_usage());
            result.setRebootFlag(info.getReboot_flag());
            result.setSystemTime(info.getSystem_time());
            result.setVideoAdapter(info.getVideo_adapter());
            result.setVideoDriver(info.getVideo_driver());
            result.setVideoMemory(info.getVideo_memory());
            break;
         }

         try {
            Thread.sleep(1000L);
         } catch (InterruptedException var9) {
            logger.error(var9);
            break;
         }
      }

      return result;
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public DeviceTimeInfo getDeviceTimeInfo(String deviceId, int reqTimeOut) {
      DeviceTimeConf info = null;
      DeviceTimeInfo result = new DeviceTimeInfo();
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();

      try {
         confMgr.reqGetTimeFromDevice(deviceId, this.getToken());
      } catch (Exception var21) {
         logger.error(var21);
         return null;
      }

      for(int i = 0; i < reqTimeOut; ++i) {
         info = confMgr.getTimeResultSet(deviceId, this.getToken(), "GET_DEVICE_TIME_CONF");
         if (info != null) {
            DeviceInfo mgr = DeviceInfoImpl.getInstance();
            String deviceModelCode = "";

            try {
               deviceModelCode = mgr.getDeviceModelCodeByDeviceId(deviceId);
            } catch (Exception var19) {
               logger.error(var19);
               return null;
            }

            result.setDeviceId(deviceId);
            result.setDeviceModelCode(deviceModelCode);
            DeviceTimeClockInfo clock = new DeviceTimeClockInfo();
            List timerList = new ArrayList();
            List holidayList = new ArrayList();
            String modelKind = MDCTimeStrUtils.getModelKind(deviceModelCode);
            String[] arClock;
            String[] arHoliday;
            if (!modelKind.equalsIgnoreCase("NEW")) {
               arClock = StrUtils.nvl(info.getTime_current_time()).split(";");
               clock.setClockHour(StrUtils.nvl(arClock[1]));
               clock.setClockMinute(StrUtils.nvl(arClock[2]));
               clock.setClockAmpm(StrUtils.nvl(arClock[0]));
               String[] arOnTime = StrUtils.nvl(info.getTime_on_time()).split(";");
               arHoliday = StrUtils.nvl(info.getTime_off_time()).split(";");
               DeviceTimeTimerInfo timer1 = new DeviceTimeTimerInfo();
               timer1.setTimerCount(0);
               timer1.setTimerOnAmpm(StrUtils.nvl(arOnTime[0]));
               timer1.setTimerOnHour(StrUtils.nvl(arOnTime[1]));
               timer1.setTimerOnMinute(StrUtils.nvl(arOnTime[2]));
               timer1.setTimerOnStatus(StrUtils.nvl(arOnTime[4]));
               timer1.setTimerOffAmpm(StrUtils.nvl(arHoliday[0]));
               timer1.setTimerOffHour(StrUtils.nvl(arHoliday[1]));
               timer1.setTimerOffMinute(StrUtils.nvl(arHoliday[2]));
               timer1.setTimerOffStatus(StrUtils.nvl(arHoliday[3]));
               timer1.setTimerSource(StrUtils.nvl(arOnTime[5]));
               timer1.setTimerVolume(StrUtils.nvl(arOnTime[3]));
               timerList.add(timer1);
            } else {
               arClock = StrUtils.nvl(info.getTimer_clock()).split(";");
               int year = 0;
               if (arClock[4] != null && arClock[5] != null) {
                  year = Integer.parseInt(arClock[4]) * 256 + Integer.parseInt(arClock[5]);
               }

               clock.setClockYear(String.valueOf(year));
               clock.setClockMonth(StrUtils.nvl(arClock[3]));
               clock.setClockDay(StrUtils.nvl(arClock[0]));
               clock.setClockHour(StrUtils.nvl(arClock[1]));
               clock.setClockMinute(StrUtils.nvl(arClock[2]));
               clock.setClockAmpm(StrUtils.nvl(arClock[6]));

               String[] arrDuration;
               for(int iTimer = 1; iTimer <= 3; ++iTimer) {
                  arrDuration = null;
                  switch(iTimer) {
                  case 1:
                     arrDuration = StrUtils.nvl(info.getTimer_timer1()).split(";");
                     break;
                  case 2:
                     arrDuration = StrUtils.nvl(info.getTimer_timer2()).split(";");
                     break;
                  case 3:
                     arrDuration = StrUtils.nvl(info.getTimer_timer3()).split(";");
                  }

                  DeviceTimeTimerInfo timer = new DeviceTimeTimerInfo();
                  timer.setTimerCount(iTimer);
                  timer.setTimerOnHour(StrUtils.nvl(arrDuration[0]));
                  timer.setTimerOnMinute(StrUtils.nvl(arrDuration[1]));
                  timer.setTimerOnAmpm(StrUtils.nvl(arrDuration[2]));
                  timer.setTimerOnStatus(StrUtils.nvl(arrDuration[3]));
                  timer.setTimerOffHour(StrUtils.nvl(arrDuration[4]));
                  timer.setTimerOffMinute(StrUtils.nvl(arrDuration[5]));
                  timer.setTimerOffAmpm(StrUtils.nvl(arrDuration[6]));
                  timer.setTimerOffStatus(StrUtils.nvl(arrDuration[7]));
                  timer.setTimerRepeat(StrUtils.nvl(arrDuration[8]));
                  timer.setTimerManualWeekday(StrUtils.nvl(arrDuration[9]));
                  if (arrDuration.length == 15) {
                     timer.setTimerOffRepeat(StrUtils.nvl(arrDuration[10]));
                     timer.setTimerOffManualWeekday(StrUtils.nvl(arrDuration[11]));
                     timer.setTimerVolume(StrUtils.nvl(arrDuration[12]));
                     timer.setTimerSource(StrUtils.nvl(arrDuration[13]));
                     timer.setTimerHolidayEnable(StrUtils.nvl(arrDuration[14]));
                     timer.setTimerDataCount(15);
                  } else {
                     timer.setTimerVolume(StrUtils.nvl(arrDuration[10]));
                     timer.setTimerSource(StrUtils.nvl(arrDuration[11]));
                     timer.setTimerHolidayEnable(StrUtils.nvl(arrDuration[12]));
                     timer.setTimerDataCount(13);
                  }

                  timerList.add(timer);
               }

               arHoliday = StrUtils.nvl(info.getTimer_holiday()).split(",");
               if (arHoliday != null && arHoliday.length > 0) {
                  arrDuration = null;

                  for(int j = 0; j < arHoliday.length; ++j) {
                     arrDuration = arHoliday[j].split(";");
                     if (arrDuration.length > 3) {
                        DeviceTimeHolidayInfo holiday = new DeviceTimeHolidayInfo();
                        holiday.setMonth1(StrUtils.nvl(arrDuration[1]));
                        holiday.setDay1(StrUtils.nvl(arrDuration[2]));
                        holiday.setMonth2(StrUtils.nvl(arrDuration[3]));
                        holiday.setDay2(StrUtils.nvl(arrDuration[4]));
                        holidayList.add(holiday);
                     }
                  }
               }
            }

            result.setTimeClock(clock);
            result.setTimeTimerList(timerList);
            result.setTimeHolidayList(holidayList);
            break;
         }

         try {
            Thread.sleep(1000L);
         } catch (InterruptedException var20) {
            logger.error(var20);
            return null;
         }
      }

      return result;
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public DeviceFault getFaultInfo(Long faultId) {
      DeviceFault result = new DeviceFault();

      try {
         DeviceIF mgr = DeviceImpl.getInstance();
         Fault fault = mgr.getDeviceFaultInfo(faultId);
         if (fault != null) {
            result.setDeviceId(fault.getDevice_id());
            result.setDeviceName(fault.getDevice_name());
            result.setDeviceModelName(fault.getDevice_model_name());
            result.setEventDate(fault.getEvent_date());
            result.setEventName(fault.getEvent_name());
            result.setFaultId(faultId);
            result.setFaultLevel(fault.getFault_level());
            result.setProcessDate(fault.getProcess_date());
            result.setProcessorId(fault.getProcessor_id());
            result.setReceiptorId(fault.getReceiptor_id());
            result.setReceiptDate(fault.getReceipt_date());
            result.setApplicationVersion(fault.getApplication_version());
            result.setFirmwareVersion(fault.getFirmware_version());
            result.setSendMailYn(fault.getSend_mail_yn());
            result.setVendor(fault.getVendor());
         }
      } catch (SQLException var5) {
         logger.error(var5);
      }

      return result;
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public ResultList getFaultList(int startPos, int pageSize, String sortName, String sortDir, String searchText) {
      DeviceIF mgr = DeviceImpl.getInstance();
      ResultList resultList = new ResultList();
      PagedListInfo pagedList = null;
      ArrayList openDeviceList = new ArrayList();

      try {
         pagedList = mgr.getPagedFaultDeviceList(startPos, pageSize, sortName, sortDir, searchText, (Timestamp)null);
         List devList = pagedList.getPagedResultList();

         for(int i = 0; i < devList.size(); ++i) {
            Fault fault = (Fault)devList.get(i);
            if (fault.getProcessor_id() == null || fault.getProcessor_id().length() == 0) {
               DeviceFault device = new DeviceFault();
               device.setDeviceId(fault.getDevice_id());
               device.setDeviceName(fault.getDevice_name());
               device.setDeviceModelName(fault.getDevice_model_name());
               device.setEventDate(fault.getEvent_date());
               device.setEventName(fault.getEvent_name());
               device.setFaultId(fault.getFault_id());
               device.setFaultLevel(fault.getFault_level());
               device.setProcessDate(fault.getProcess_date());
               device.setProcessorId(fault.getProcessor_id());
               device.setReceiptorId(fault.getReceiptor_id());
               device.setReceiptDate(fault.getReceipt_date());
               openDeviceList.add(device);
            }
         }

         resultList.setResultList(openDeviceList);
         resultList.setTotalCount(openDeviceList.size());
      } catch (SQLException var14) {
         logger.error(var14);
      } catch (ConfigException var15) {
         logger.error(var15);
      }

      return resultList;
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public ResultList getGroupList(String userId, long groupId, Boolean recursive) {
      UserInfo userDao = UserInfoImpl.getInstance();
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      ResultList resultList = new ResultList();
      ArrayList groupList = new ArrayList();

      try {
         Long orgId = userDao.getAllByUserId(userId).getRoot_group_id();
         Long compOrgId = new Long(0L);
         if ((int)groupId != 0) {
            groupDao.getOrgIdByGroupId(groupId);
         }

         List deviceGroup = null;
         if (orgId != null && compOrgId != null && orgId.intValue() == compOrgId.intValue()) {
            if (recursive != null) {
               deviceGroup = groupDao.getChildGroupList((int)groupId, recursive);
            } else {
               deviceGroup = groupDao.getChildGroupList((int)groupId, false);
            }
         }

         if (deviceGroup != null) {
            DeviceGroup sourceEntity = null;
            com.samsung.magicinfo.openapi.custom.openEntity.device.DeviceGroup targetEntity = null;

            for(int i = 0; i < deviceGroup.size(); ++i) {
               sourceEntity = (DeviceGroup)deviceGroup.get(i);
               targetEntity = new com.samsung.magicinfo.openapi.custom.openEntity.device.DeviceGroup();
               targetEntity.setGroupId(sourceEntity.getGroup_id());
               targetEntity.setpGroupId(sourceEntity.getP_group_id());
               targetEntity.setGroupDepth(sourceEntity.getGroup_depth());
               targetEntity.setGroupName(sourceEntity.getGroup_name());
               targetEntity.setDescription(sourceEntity.getDescription());
               targetEntity.setIsRoot(sourceEntity.getIs_root());
               targetEntity.setGroupType(sourceEntity.getGroup_type());
               targetEntity.setCreatorId(sourceEntity.getCreator_id());
               targetEntity.setCreateDate(sourceEntity.getCreate_date());
               targetEntity.setDefaultProgramId(sourceEntity.getDefault_program_id());
               groupList.add(targetEntity);
            }

            resultList.setResultList(groupList);
            resultList.setTotalCount(groupList.size());
         }
      } catch (SQLException var15) {
         logger.error(var15);
      }

      return resultList;
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public String getMagicInfoServer(String deviceId) {
      DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
      Device device = null;
      String magicInfoServerUrl = "";

      try {
         device = deviceMgr.getDevice(deviceId);
         if (device != null) {
            magicInfoServerUrl = device.getMagicinfo_server_url();
         }
      } catch (SQLException var6) {
         logger.error(var6);
      }

      return magicInfoServerUrl;
   }

   @PreAuthorize("hasRole('Remote Job Read Authority')")
   public ResultList getRemoteJobList(String deviceId, int startPos, int pageSize) {
      new ArrayList();
      List openRemoteJobList = new ArrayList();
      ResultList resultList = new ResultList();
      DeviceIF devIF = DeviceImpl.getInstance();

      try {
         try {
            List remoteJobList = devIF.getReadyJobList(deviceId, startPos, pageSize);
            RemoteJobLogEntity sourceEntity = null;
            RemoteJob targetEntity = null;
            if (remoteJobList != null) {
               for(int i = 0; i < remoteJobList.size(); ++i) {
                  new RemoteJobLogEntity();
                  targetEntity = new RemoteJob();
                  sourceEntity = (RemoteJobLogEntity)remoteJobList.get(i);
                  targetEntity.setJobId(sourceEntity.getJob_id());
                  targetEntity.setJobName(sourceEntity.getJob_name());
                  targetEntity.setJobType(sourceEntity.getJob_type());
                  targetEntity.setDeviceId(sourceEntity.getDevice_id());
                  targetEntity.setResult(sourceEntity.getResult());
                  targetEntity.setExeDate(sourceEntity.getExe_date());
                  targetEntity.setResultFileName(sourceEntity.getResult_file_name());
                  targetEntity.setFileName(sourceEntity.getFile_name());
                  targetEntity.setFileSize(sourceEntity.getFile_size());
                  targetEntity.setStartDate(sourceEntity.getStart_date());
                  targetEntity.setStopDate(sourceEntity.getStop_date());
                  targetEntity.setStartTime(sourceEntity.getStart_time());
                  targetEntity.setRepeatType(sourceEntity.getRepeat_type());
                  targetEntity.setWeekdays(sourceEntity.getWeekdays());
                  targetEntity.setMonthdays(sourceEntity.getMonthdays());
                  openRemoteJobList.add(targetEntity);
               }
            }

            if (remoteJobList != null) {
               resultList.setTotalCount(remoteJobList.size());
            } else {
               logger.error("DeviceService.getRemoteJobList() remoteJobList is null " + deviceId + " " + startPos + " " + pageSize);
               resultList.setTotalCount(0);
            }

            resultList.setResultList(openRemoteJobList);
         } catch (Exception var14) {
            logger.error(var14);
         }

         return resultList;
      } finally {
         ;
      }
   }

   public String getToken() {
      return this.token;
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public DeviceDisplayInfo setDeviceDisplayInfo(DeviceDisplayInfo info, String mode, int reqTimeOut) {
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      if (mode == null) {
         mode = "LIST";
      }

      if (reqTimeOut < 1) {
         reqTimeOut = 10;
      }

      DeviceDisplayInfo result = new DeviceDisplayInfo();
      DeviceDisplayConf conf = new DeviceDisplayConf();
      if (info != null && info.getDeviceId() != null) {
         try {
            conf.setAdvanced_auto_power(info.getAdvancedAutoPower());
            conf.setAdvanced_fan_control(info.getAdvancedFanControl());
            conf.setAdvanced_fan_speed(info.getAdvancedFanSpeed());
            conf.setAdvanced_osd_display_type(info.getAdvancedOsdDisplayType());
            conf.setAdvanced_reset(info.getAdvancedReset());
            conf.setAdvanced_rj45_setting_refresh(info.getAdvancedRj45SettingRefresh());
            conf.setAdvanced_stand_by(info.getAdvancedStandBy());
            conf.setAdvanced_user_auto_color(info.getAdvancedUserAutoColor());
            conf.setBasic_direct_channel(info.getBasicDirectChannel());
            conf.setBasic_mute(info.getBasicMute());
            conf.setBasic_panel_status(info.getBasicPanelStatus());
            conf.setBasic_power(info.getBasicPower());
            conf.setBasic_source(info.getBasicSource());
            conf.setBasic_volume(info.getBasicVolume());
            conf.setDiagnosis_alarm_temperature(info.getDiagnosisAlarmTemperature());
            conf.setDiagnosis_display_status(info.getDiagnosisDisplayStatus());
            conf.setDiagnosis_monitor_temperature(info.getDiagnosisMonitorTemperature());
            conf.setDiagnosis_panel_on_time(info.getDiagnosisPanelOnTime());
            conf.setImage_auto(info.getImageAuto());
            conf.setImage_coarse(info.getImageCoarse());
            conf.setImage_fine(info.getImageFine());
            conf.setImage_hpos(info.getImageHpos());
            conf.setImage_vpos(info.getImageVpos());
            conf.setMisc_all_lock(info.getMiscAllLock());
            conf.setMisc_osd(info.getMiscOsd());
            conf.setMisc_panel_lock(info.getMiscPanelLock());
            conf.setMisc_remocon(info.getMiscRemocon());
            conf.setMnt_auto(info.getMntAuto());
            conf.setMnt_format(info.getMntFormat());
            conf.setMnt_manual(info.getMntManual());
            conf.setMnt_pixel_shift(info.getMntPixelShift());
            conf.setMnt_safety_lock(info.getMntSafetyLock());
            conf.setMnt_safety_screen_run(info.getMntSafetyScreenRun());
            conf.setMnt_safety_screen_timer(info.getMntSafetyScreenTimer());
            conf.setMnt_video_wall(info.getMntVideoWall());
            conf.setPpc_blue(info.getPpcBlue());
            conf.setPpc_brightness(info.getPpcBrightness());
            conf.setPpc_color_temperature(info.getPpcColorTemperature());
            conf.setPpc_colortone(info.getPpcColortone());
            conf.setPpc_contrast(info.getPpcContrast());
            conf.setPpc_gamma(info.getPpcGamma());
            conf.setPpc_green(info.getPpcGreen());
            conf.setPpc_hdmi_black_level(info.getPpcHdmiBlackLevel());
            conf.setPpc_magic_bright(info.getPpcMagicBright());
            conf.setPpc_red(info.getPpcRed());
            conf.setPpc_size(info.getPpcSize());
            conf.setSpecialized_picture_mode(info.getPvSplPictureMode());
            conf.setPv_brightness(info.getPvBrightness());
            conf.setPv_color(info.getPvColor());
            conf.setPv_color_temperature(info.getPvColorTemperature());
            conf.setPv_colortone(info.getPvColortone());
            conf.setPv_contrast(info.getPvContrast());
            conf.setPv_digitalnr(info.getPvDigitalNr());
            conf.setPv_filmmode(info.getPvFilmmode());
            conf.setPv_hdmi_black_level(info.getPvHdmiBlackLevel());
            conf.setPv_mode(info.getPvMode());
            conf.setPv_sharpness(info.getPvSharpness());
            conf.setPv_size(info.getPvSize());
            conf.setPv_tint(info.getPvTint());
            conf.setPv_video_picture_position_size(info.getPvVideoPicturePositionSize());
            conf.setSb_b_offset(info.getSbBOffset());
            conf.setSb_bgain(info.getSbBgain());
            conf.setSb_g_offset(info.getSbGOffset());
            conf.setSb_gain(info.getSbGain());
            conf.setSb_ggain(info.getSbGgain());
            conf.setSb_r_offset(info.getSbROffset());
            conf.setSb_rgain(info.getSbRgain());
            conf.setSb_sharp(info.getSbSharp());
            conf.setSb_status(info.getSbStatus());
            conf.setSound_balance(info.getSoundBalance());
            conf.setSound_bass(info.getSoundBass());
            conf.setSound_mode(info.getSoundMode());
            conf.setSound_srs(info.getSoundSrs());
            conf.setSound_treble(info.getSoundTreble());
            conf.setDevice_id(info.getDeviceId());
            confMgr.reqSetDisplayToDevice(conf, this.getToken(), mode);
         } catch (Exception var11) {
            logger.error(var11);
            return result;
         }

         DeviceDisplayConf resultEntity = null;

         for(int i = 0; i < reqTimeOut; ++i) {
            try {
               resultEntity = confMgr.getSettingResultByDisplay(info.getDeviceId(), this.getToken(), "SET_DEVICE_DISPLAY_CONF");
            } catch (SQLException var10) {
               logger.error(var10);
            }

            if (resultEntity != null) {
               break;
            }

            try {
               Thread.sleep(1000L);
            } catch (InterruptedException var12) {
               logger.error(var12);
               break;
            }
         }

         if (resultEntity != null && !"0".equals(info.getBasicPower())) {
            return this.getDeviceDisplayInfo(info.getDeviceId(), mode, reqTimeOut);
         }
      }

      return result;
   }

   @PreAuthorize("hasRole('Device Write Authority')")
   public Boolean setMagicInfoServer(String deviceId, String serverUrl) {
      DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
      DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
      DeviceSystemSetupConf info = new DeviceSystemSetupConf();
      info.setDevice_id(deviceId);
      info.setMagicinfo_server_url(serverUrl);
      boolean result = false;

      try {
         Device device = deviceMgr.getDeviceMinInfo(deviceId);
         if (device != null) {
            confManager.reqSetSystemSetupToDevice(info, this.getToken());
            result = deviceMgr.deleteDevice(deviceId);
         }
      } catch (SQLException var9) {
         logger.error(var9);
      } catch (Exception var10) {
         logger.error(var10);
      }

      if (result) {
         try {
            WSCall.setPlayerRequest(deviceId, "agent restart");
         } catch (BasicException var8) {
            logger.error(var8);
         }
      }

      return result;
   }

   public boolean setMultiDeviceDisplayInfo(DeviceDisplayInfo info, String deviceIds, String groupId, String mode) {
      String[] arrDeviceIds = null;
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      boolean result = true;
      if (info != null) {
         if (groupId != null && !groupId.equals("") && (deviceIds == null || deviceIds.equals(""))) {
            DeviceGroupInfo groupMgr = DeviceGroupInfoImpl.getInstance();

            try {
               List devIds = groupMgr.getChildDeviceIdList(Integer.parseInt(groupId));
               String[] arrTemp = null;
               if (devIds != null && devIds.size() > 0) {
                  arrTemp = new String[devIds.size()];

                  for(int i = 0; i < devIds.size(); ++i) {
                     arrTemp[i] = (String)devIds.get(i);
                  }

                  arrDeviceIds = arrTemp;
               }
            } catch (SQLException var13) {
               result = false;
               logger.error(var13);
            }
         } else if (deviceIds != null) {
            arrDeviceIds = deviceIds.split(";");
         }

         if (arrDeviceIds != null) {
            for(int j = 0; j < arrDeviceIds.length; ++j) {
               try {
                  DeviceDisplayConf conf = new DeviceDisplayConf();
                  conf.setDevice_id(arrDeviceIds[j]);
                  conf.setAdvanced_auto_power(info.getAdvancedAutoPower());
                  conf.setAdvanced_fan_control(info.getAdvancedFanControl());
                  conf.setAdvanced_fan_speed(info.getAdvancedFanSpeed());
                  conf.setAdvanced_osd_display_type(info.getAdvancedOsdDisplayType());
                  conf.setAdvanced_reset(info.getAdvancedReset());
                  conf.setAdvanced_rj45_setting_refresh(info.getAdvancedRj45SettingRefresh());
                  conf.setAdvanced_stand_by(info.getAdvancedStandBy());
                  conf.setAdvanced_user_auto_color(info.getAdvancedUserAutoColor());
                  conf.setBasic_direct_channel(info.getBasicDirectChannel());
                  conf.setBasic_mute(info.getBasicMute());
                  conf.setBasic_panel_status(info.getBasicPanelStatus());
                  conf.setBasic_power(info.getBasicPower());
                  conf.setBasic_source(info.getBasicSource());
                  conf.setBasic_volume(info.getBasicVolume());
                  conf.setDiagnosis_alarm_temperature(info.getDiagnosisAlarmTemperature());
                  conf.setDiagnosis_display_status(info.getDiagnosisDisplayStatus());
                  conf.setDiagnosis_monitor_temperature(info.getDiagnosisMonitorTemperature());
                  conf.setDiagnosis_panel_on_time(info.getDiagnosisPanelOnTime());
                  conf.setImage_auto(info.getImageAuto());
                  conf.setImage_coarse(info.getImageCoarse());
                  conf.setImage_fine(info.getImageFine());
                  conf.setImage_hpos(info.getImageHpos());
                  conf.setImage_vpos(info.getImageVpos());
                  conf.setMisc_all_lock(info.getMiscAllLock());
                  conf.setMisc_osd(info.getMiscOsd());
                  conf.setMisc_panel_lock(info.getMiscPanelLock());
                  conf.setMisc_remocon(info.getMiscRemocon());
                  conf.setMnt_auto(info.getMntAuto());
                  conf.setMnt_format(info.getMntFormat());
                  conf.setMnt_manual(info.getMntManual());
                  conf.setMnt_pixel_shift(info.getMntPixelShift());
                  conf.setMnt_safety_lock(info.getMntSafetyLock());
                  conf.setMnt_safety_screen_run(info.getMntSafetyScreenRun());
                  conf.setMnt_safety_screen_timer(info.getMntSafetyScreenTimer());
                  conf.setMnt_video_wall(info.getMntVideoWall());
                  conf.setPpc_blue(info.getPpcBlue());
                  conf.setPpc_brightness(info.getPpcBrightness());
                  conf.setPpc_color_temperature(info.getPpcColorTemperature());
                  conf.setPpc_colortone(info.getPpcColortone());
                  conf.setPpc_contrast(info.getPpcContrast());
                  conf.setPpc_gamma(info.getPpcGamma());
                  conf.setPpc_green(info.getPpcGreen());
                  conf.setPpc_hdmi_black_level(info.getPpcHdmiBlackLevel());
                  conf.setPpc_magic_bright(info.getPpcMagicBright());
                  conf.setPpc_red(info.getPpcRed());
                  conf.setPpc_size(info.getPpcSize());
                  conf.setSpecialized_picture_mode(info.getPvSplPictureMode());
                  conf.setPv_brightness(info.getPvBrightness());
                  conf.setPv_color(info.getPvColor());
                  conf.setPv_color_temperature(info.getPvColorTemperature());
                  conf.setPv_colortone(info.getPvColortone());
                  conf.setPv_contrast(info.getPvContrast());
                  conf.setPv_digitalnr(info.getPvDigitalNr());
                  conf.setPv_filmmode(info.getPvFilmmode());
                  conf.setPv_hdmi_black_level(info.getPvHdmiBlackLevel());
                  conf.setPv_mode(info.getPvMode());
                  conf.setPv_sharpness(info.getPvSharpness());
                  conf.setPv_size(info.getPvSize());
                  conf.setPv_tint(info.getPvTint());
                  conf.setPv_video_picture_position_size(info.getPvVideoPicturePositionSize());
                  conf.setSb_b_offset(info.getSbBOffset());
                  conf.setSb_bgain(info.getSbBgain());
                  conf.setSb_g_offset(info.getSbGOffset());
                  conf.setSb_gain(info.getSbGain());
                  conf.setSb_ggain(info.getSbGgain());
                  conf.setSb_r_offset(info.getSbROffset());
                  conf.setSb_rgain(info.getSbRgain());
                  conf.setSb_sharp(info.getSbSharp());
                  conf.setSb_status(info.getSbStatus());
                  conf.setSound_balance(info.getSoundBalance());
                  conf.setSound_bass(info.getSoundBass());
                  conf.setSound_mode(info.getSoundMode());
                  conf.setSound_srs(info.getSoundSrs());
                  conf.setSound_treble(info.getSoundTreble());
                  confMgr.reqSetDisplayToDevice(conf, this.getToken(), mode);
               } catch (Exception var12) {
                  result = false;
                  logger.error(var12);
               }
            }
         } else {
            result = false;
         }
      }

      return result;
   }

   public DeviceTimeInfo setDeviceTimeInfo(DeviceTimeInfo info, int reqTimeOut) {
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();

      DeviceTimeConf conf;
      try {
         conf = new DeviceTimeConf();
         conf.setDevice_id(info.getDeviceId());
         String modelKind = MDCTimeStrUtils.getModelKind(info.getDeviceModelCode());
         DeviceTimeClockInfo clock;
         String tmpYY1;
         String onTimeStr;
         String timerStr;
         List timerList;
         if (!modelKind.equalsIgnoreCase("NEW")) {
            clock = info.getTimeClock();
            if (clock != null) {
               tmpYY1 = StrUtils.nvl(clock.getClockAmpm()) + ";" + StrUtils.nvl(clock.getClockHour()) + ";" + StrUtils.nvl(clock.getClockMinute());
               conf.setTime_current_time(tmpYY1);
            }

            timerList = info.getTimeTimerList();
            if (timerList != null) {
               DeviceTimeTimerInfo timer = (DeviceTimeTimerInfo)timerList.get(0);
               onTimeStr = StrUtils.nvl(timer.getTimerOnAmpm()) + ";" + StrUtils.nvl(timer.getTimerOnHour()) + ";" + StrUtils.nvl(timer.getTimerOnMinute()) + ";" + StrUtils.nvl(timer.getTimerVolume()) + ";" + StrUtils.nvl(timer.getTimerOnStatus()) + ";" + StrUtils.nvl(timer.getTimerSource());
               timerStr = StrUtils.nvl(timer.getTimerOffAmpm()) + ";" + StrUtils.nvl(timer.getTimerOffHour()) + ";" + StrUtils.nvl(timer.getTimerOffMinute()) + ";" + StrUtils.nvl(timer.getTimerOffStatus());
               conf.setTime_on_time(onTimeStr);
               conf.setTime_off_time(timerStr);
            }
         } else {
            clock = info.getTimeClock();
            if (clock != null) {
               tmpYY1 = Integer.toString(Integer.parseInt(clock.getClockYear()) / 256);
               String tmpYY2 = Integer.toString(Integer.parseInt(clock.getClockYear()) % 256);
               onTimeStr = StrUtils.nvl(clock.getClockDay()) + ";" + StrUtils.nvl(clock.getClockHour()) + ";" + StrUtils.nvl(clock.getClockMinute()) + ";" + StrUtils.nvl(clock.getClockMonth()) + ";" + StrUtils.nvl(tmpYY1) + ";" + StrUtils.nvl(tmpYY2) + ";" + StrUtils.nvl(clock.getClockAmpm());
               conf.setTimer_clock(onTimeStr);
            }

            timerList = info.getTimeTimerList();
            if (timerList != null) {
               for(int k = 0; k < timerList.size(); ++k) {
                  DeviceTimeTimerInfo timer = (DeviceTimeTimerInfo)timerList.get(k);
                  if (timer != null) {
                     if (timer.getTimerOffRepeat() == null && timer.getTimerOffManualWeekday() == null) {
                        timer.setTimerDataCount(13);
                     } else {
                        timer.setTimerDataCount(15);
                     }

                     timerStr = StrUtils.nvl(timer.getTimerOnHour()) + ";" + StrUtils.nvl(timer.getTimerOnMinute()) + ";" + StrUtils.nvl(timer.getTimerOnAmpm()) + ";" + StrUtils.nvl(timer.getTimerOnStatus()) + ";" + StrUtils.nvl(timer.getTimerOffHour()) + ";" + StrUtils.nvl(timer.getTimerOffMinute()) + ";" + StrUtils.nvl(timer.getTimerOffAmpm()) + ";" + StrUtils.nvl(timer.getTimerOffStatus()) + ";" + StrUtils.nvl(timer.getTimerRepeat()) + ";" + StrUtils.nvl(timer.getTimerManualWeekday()) + ";";
                     if (timer.getTimerDataCount() == 15) {
                        timerStr = timerStr + StrUtils.nvl(timer.getTimerOffRepeat()) + ";" + StrUtils.nvl(timer.getTimerOffManualWeekday()) + ";" + StrUtils.nvl(timer.getTimerVolume()) + ";" + StrUtils.nvl(timer.getTimerSource()) + ";" + StrUtils.nvl(timer.getTimerHolidayEnable());
                     } else {
                        timerStr = timerStr + StrUtils.nvl(timer.getTimerVolume()) + ";" + StrUtils.nvl(timer.getTimerSource()) + ";" + StrUtils.nvl(timer.getTimerHolidayEnable());
                     }

                     int timerCount = timer.getTimerCount();
                     if (timerCount == 1) {
                        conf.setTimer_timer1(timerStr);
                     } else if (timerCount == 2) {
                        conf.setTimer_timer2(timerStr);
                     } else if (timerCount == 3) {
                        conf.setTimer_timer3(timerStr);
                     }
                  }
               }
            }
         }

         confMgr.reqSetTimeToDevice(conf, this.getToken());
      } catch (Exception var14) {
         logger.error(var14);
      }

      conf = null;

      for(int i = 0; i < reqTimeOut; ++i) {
         try {
            conf = confMgr.getTimeResultSet(info.getDeviceId(), this.getToken(), "SET_DEVICE_TIME_CONF");
         } catch (Exception var12) {
            logger.error(var12);
         }

         if (conf != null) {
            break;
         }

         try {
            Thread.sleep(1000L);
         } catch (InterruptedException var13) {
            logger.error(var13);
            break;
         }
      }

      return this.getDeviceTimeInfo(info.getDeviceId(), reqTimeOut);
   }

   public boolean setMultiDeviceTimeInfo(DeviceTimeInfo info, String deviceIds, String groupId) {
      String[] arrDeviceIds = null;
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      boolean result = true;
      if (info != null) {
         if (groupId == null || groupId.equals("") || deviceIds != null && !deviceIds.equals("")) {
            if (deviceIds != null) {
               arrDeviceIds = deviceIds.split(";");
            }
         } else {
            DeviceGroupInfo groupMgr = DeviceGroupInfoImpl.getInstance();

            try {
               List devIds = groupMgr.getChildDeviceIdList(Integer.parseInt(groupId));
               String[] arrTemp = null;
               if (devIds != null && devIds.size() > 0) {
                  arrTemp = new String[devIds.size()];

                  for(int i = 0; i < devIds.size(); ++i) {
                     arrTemp[i] = (String)devIds.get(i);
                  }

                  arrDeviceIds = arrTemp;
               }
            } catch (SQLException var18) {
               result = false;
               logger.error(var18);
            }
         }

         if (arrDeviceIds != null) {
            for(int j = 0; j < arrDeviceIds.length; ++j) {
               try {
                  DeviceTimeConf conf = new DeviceTimeConf();
                  conf.setDevice_id(arrDeviceIds[j]);
                  String modelKind = MDCTimeStrUtils.getModelKind(info.getDeviceModelCode());
                  String tmpYY1;
                  String onTimeStr;
                  String timerStr;
                  DeviceTimeClockInfo clock;
                  List timerList;
                  if (modelKind.equalsIgnoreCase("NEW")) {
                     clock = info.getTimeClock();
                     if (clock != null) {
                        tmpYY1 = Integer.toString(Integer.parseInt(clock.getClockYear()) / 256);
                        String tmpYY2 = Integer.toString(Integer.parseInt(clock.getClockYear()) % 256);
                        onTimeStr = StrUtils.nvl(clock.getClockDay()) + ";" + StrUtils.nvl(clock.getClockHour()) + ";" + StrUtils.nvl(clock.getClockMinute()) + ";" + StrUtils.nvl(clock.getClockMonth()) + ";" + StrUtils.nvl(tmpYY1) + ";" + StrUtils.nvl(tmpYY2) + ";" + StrUtils.nvl(clock.getClockAmpm());
                        conf.setTimer_clock(onTimeStr);
                     }

                     timerList = info.getTimeTimerList();
                     if (timerList != null) {
                        for(int k = 0; k < timerList.size(); ++k) {
                           DeviceTimeTimerInfo timer = (DeviceTimeTimerInfo)timerList.get(k);
                           timerStr = StrUtils.nvl(timer.getTimerOnHour()) + ";" + StrUtils.nvl(timer.getTimerOnMinute()) + ";" + StrUtils.nvl(timer.getTimerOnAmpm()) + ";" + StrUtils.nvl(timer.getTimerOnStatus()) + ";" + StrUtils.nvl(timer.getTimerOffHour()) + ";" + StrUtils.nvl(timer.getTimerOffMinute()) + ";" + StrUtils.nvl(timer.getTimerOffAmpm()) + ";" + StrUtils.nvl(timer.getTimerOffStatus()) + ";" + StrUtils.nvl(timer.getTimerRepeat()) + ";" + StrUtils.nvl(timer.getTimerManualWeekday()) + ";";
                           if (timer.getTimerDataCount() == 15) {
                              timerStr = timerStr + StrUtils.nvl(timer.getTimerOffRepeat()) + ";" + StrUtils.nvl(timer.getTimerOffManualWeekday()) + ";" + StrUtils.nvl(timer.getTimerVolume()) + ";" + StrUtils.nvl(timer.getTimerSource()) + ";" + StrUtils.nvl(timer.getTimerHolidayEnable());
                           } else {
                              timerStr = timerStr + StrUtils.nvl(timer.getTimerVolume()) + ";" + StrUtils.nvl(timer.getTimerSource()) + ";" + StrUtils.nvl(timer.getTimerHolidayEnable());
                           }

                           if (k == 0) {
                              conf.setTimer_timer1(timerStr);
                           } else if (k == 1) {
                              conf.setTimer_timer2(timerStr);
                           } else {
                              conf.setTimer_timer3(timerStr);
                           }
                        }
                     }

                     List holidayList = info.getTimeHolidayList();
                     if (holidayList != null) {
                        StringBuffer holidayTotalStr = new StringBuffer();

                        for(int l = 0; l < holidayList.size(); ++l) {
                           DeviceTimeHolidayInfo holiday = (DeviceTimeHolidayInfo)holidayList.get(l);
                           String holidayStr = "" + holiday.getCommand() + ";" + StrUtils.nvl(holiday.getMonth1()) + ";" + StrUtils.nvl(holiday.getDay1()) + ";" + StrUtils.nvl(holiday.getMonth2()) + ";" + StrUtils.nvl(holiday.getDay2());
                           if (l > 0) {
                              holidayTotalStr.append(",");
                           }

                           holidayTotalStr.append(holidayStr);
                        }

                        conf.setTimer_holiday(holidayTotalStr.toString());
                     }
                  } else {
                     clock = info.getTimeClock();
                     if (clock != null) {
                        tmpYY1 = StrUtils.nvl(clock.getClockAmpm()) + ";" + StrUtils.nvl(clock.getClockHour()) + ";" + StrUtils.nvl(clock.getClockMinute());
                        conf.setTime_current_time(tmpYY1);
                     }

                     timerList = info.getTimeTimerList();
                     if (timerList != null) {
                        DeviceTimeTimerInfo timer = (DeviceTimeTimerInfo)timerList.get(0);
                        onTimeStr = StrUtils.nvl(timer.getTimerOnAmpm()) + ";" + StrUtils.nvl(timer.getTimerOnHour()) + ";" + StrUtils.nvl(timer.getTimerOnMinute()) + ";" + StrUtils.nvl(timer.getTimerVolume()) + ";" + StrUtils.nvl(timer.getTimerOnStatus()) + ";" + StrUtils.nvl(timer.getTimerSource());
                        timerStr = StrUtils.nvl(timer.getTimerOffAmpm()) + ";" + StrUtils.nvl(timer.getTimerOffHour()) + ";" + StrUtils.nvl(timer.getTimerOffMinute()) + ";" + StrUtils.nvl(timer.getTimerOffStatus());
                        conf.setTime_on_time(onTimeStr);
                        conf.setTime_off_time(timerStr);
                     }
                  }

                  confMgr.reqSetTimeToDevice(conf, this.getToken());
               } catch (Exception var17) {
                  result = false;
                  logger.error(var17);
               }
            }
         } else {
            result = false;
         }
      }

      return result;
   }

   public DeviceTimeInfo setDeviceTimeHolidyInfo(DeviceTimeInfo info, int reqTimeOut) {
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();

      try {
         String modelKind = MDCTimeStrUtils.getModelKind(info.getDeviceModelCode());
         if (modelKind.equalsIgnoreCase("NEW")) {
            List holidayList = info.getTimeHolidayList();
            if (holidayList != null) {
               for(int l = 0; l < holidayList.size(); ++l) {
                  DeviceTimeConf conf = new DeviceTimeConf();
                  conf.setDevice_id(info.getDeviceId());
                  DeviceTimeHolidayInfo holiday = (DeviceTimeHolidayInfo)holidayList.get(l);
                  String holidayStr = "" + holiday.getCommand() + ";" + StrUtils.nvl(holiday.getMonth1()) + ";" + StrUtils.nvl(holiday.getDay1()) + ";" + StrUtils.nvl(holiday.getMonth2()) + ";" + StrUtils.nvl(holiday.getDay2());
                  conf.setTimer_holiday(holidayStr);
                  confMgr.reqSetTimeToDevice(conf, this.getToken());
               }
            }
         }
      } catch (Exception var10) {
         logger.error(var10);
      }

      return this.getDeviceTimeInfo(info.getDeviceId(), reqTimeOut * 2);
   }

   public void setToken(String token) {
      this.token = token;
   }

   public boolean setDevicePowerOn(String deviceIds) {
      String[] arrDeviceIds = null;
      if (deviceIds == null) {
         return false;
      } else {
         arrDeviceIds = deviceIds.split(";");
         if (arrDeviceIds.length <= 0) {
            return false;
         } else {
            int sendCount = 0;
            DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
            WakeOnLan wol = new WakeOnLan();

            for(int i = 0; i < arrDeviceIds.length; ++i) {
               if (!arrDeviceIds[i].equals("")) {
                  try {
                     Device device = deviceMgr.getDevice(arrDeviceIds[i]);
                     if (device != null) {
                        wol.wol(device.getIp_address(), device.getSubnet_mask(), device.getDevice_id());
                        ++sendCount;
                     }
                  } catch (SQLException var8) {
                     logger.error(var8);
                  }
               }
            }

            return sendCount > 0;
         }
      }
   }

   @PreAuthorize("hasRole('Device Read Authority')")
   public String getDeviceVncInfo(String deviceId, String tunnelingIP, String serverUrl) {
      DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
      SlmLicenseManager slmLicenseMgr = SlmLicenseManagerImpl.getInstance();
      String jnlpPasswordParam = "";
      String jnlpFileName = "";
      String realPath = "";
      String urlToken = Base64.encode(this.tokenRegistry.issueToken(SecurityUtils.getUserContainer()).getBytes());
      String serverUrlPath = serverUrl + "/MagicInfo/uploader/" + urlToken + "/jnlp/";

      try {
         String miHome = slmLicenseMgr.getRegistryValue("path");
         if (miHome != null && !miHome.equals("")) {
            realPath = miHome + File.separator + "server" + File.separator + "uploader" + File.separator + "jnlp" + File.separator;
         }
      } catch (Exception var20) {
      }

      if (realPath.equals("")) {
         realPath = "C:/MagicInfo-i Premium/server/uploader/jnlp/";
         logger.error("RegisteryValue(path) is invalid. new path=" + realPath);
      }

      try {
         Device device = deviceMgr.getDevice(deviceId);
         if (device.getVnc_password() != null && !device.getVnc_password().equals("")) {
            jnlpPasswordParam = "\t\t<param name='PASSWORD' value='" + device.getVnc_password() + "'>\n";
         }

         SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
         Calendar cal = Calendar.getInstance();
         jnlpFileName = "openApiVncViewer_" + formatter.format(cal.getTime()) + ".jnlp";
         SecurityUtils.getSafeFile(realPath).mkdirs();
         File jnlpFile = SecurityUtils.getSafeFile(realPath + jnlpFileName);
         jnlpFile.createNewFile();
         String browserHeight = CommonConfig.get("VNC_VIEWER_HEIGHT");
         String browserWidth = CommonConfig.get("VNC_VIEWER_WIDTH");
         if (browserHeight == null) {
            browserHeight = "800";
         }

         if (browserWidth == null) {
            browserWidth = "1500";
         }

         DeviceVncViewerJnlpStringBuilder vncViewerJnlpBuilder = (new DeviceVncViewerJnlpStringBuilder()).addServerUrlPath(serverUrlPath).addJnlpFilename(jnlpFileName).addBrowserWidth(browserWidth).addBrowserHeight(browserHeight).addDeviceId(deviceId).addTunnelingIp(tunnelingIP).addJnlpPasswordParam(jnlpPasswordParam);
         FileWriter writer = new FileWriter(jnlpFile);
         PrintWriter fout = new PrintWriter(writer);
         fout.write(vncViewerJnlpBuilder.build());
         fout.close();
         writer.close();
      } catch (Exception var21) {
         logger.error(var21);
         return null;
      }

      return serverUrlPath + jnlpFileName;
   }
}
