package com.samsung.magicinfo.webauthor2.util;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import javax.imageio.ImageIO;
import javax.servlet.ServletContext;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
public class AppStartup {
  private static final Logger logger = LoggerFactory.getLogger(AppStartup.class);
  
  private ServletContext servletContext;
  
  @Autowired
  public AppStartup(ServletContext servletContext) {
    this.servletContext = servletContext;
  }
  
  @EventListener
  public final void onApplicationEvent(ContextRefreshedEvent event) {
    ImageIO.scanForPlugins();
    deleteInsertContentsDirectory();
    deletePreviewDirectory();
    logger.info("WebAuthor context refresh successful.");
  }
  
  private void deleteInsertContentsDirectory() {
    Path insertContents = Paths.get(this.servletContext.getRealPath("insertContents"), new String[0]);
    if (Files.exists(insertContents, new java.nio.file.LinkOption[0]))
      FileUtils.deleteQuietly(insertContents.toFile()); 
  }
  
  private void deletePreviewDirectory() {
    Path previewDirectory = Paths.get(this.servletContext.getRealPath("preview"), new String[0]);
    if (Files.exists(previewDirectory, new java.nio.file.LinkOption[0]))
      FileUtils.deleteQuietly(previewDirectory.toFile()); 
  }
}
