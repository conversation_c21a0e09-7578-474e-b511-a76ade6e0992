package com.samsung.magicinfo.rms.service;

import com.samsung.common.exception.BasicException;
import com.samsung.common.model.ResponseBody;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.rms.model.DeviceApproveResource;
import com.samsung.magicinfo.rms.model.DeviceDisplayConfResource;
import com.samsung.magicinfo.rms.model.DeviceFilter;
import com.samsung.magicinfo.rms.model.DeviceGeneralConfResource;
import com.samsung.magicinfo.rms.model.DeviceLedCabinetResource;
import com.samsung.magicinfo.rms.model.DeviceSecurityConfResource;
import com.samsung.magicinfo.rms.model.DeviceSystemSetupConfResource;
import com.samsung.magicinfo.rms.model.DeviceTagAssignment;
import com.samsung.magicinfo.rms.model.DeviceTimeconfResource;
import java.sql.SQLException;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.multipart.MultipartFile;

public interface DeviceService {
   ResponseBody getDashboardDeviceInfo() throws SQLException;

   ResponseBody getAllDeviceList(DeviceFilter var1) throws SQLException;

   ResponseBody deleteDevice(String var1) throws SQLException, BasicException, ConfigException;

   ResponseBody moveDevice(String var1, DeviceFilter var2) throws SQLException, BasicException, ConfigException;

   ResponseBody approveDevice(String var1, DeviceApproveResource var2) throws SQLException, BasicException, ConfigException;

   ResponseBody getGeneralInfo(String var1);

   ResponseBody getTimeInfo(String var1);

   ResponseBody getSetupInfo(String var1);

   ResponseBody getDisplayControlInfo(String var1);

   ResponseBody getSecurityControlInfo(String var1);

   ResponseBody updateGeneralInfo(String var1, DeviceGeneralConfResource var2);

   ResponseBody updateGeneral(String var1, DeviceGeneralConfResource var2);

   ResponseBody getUpdatedGeneralInfoResult(String var1, String var2);

   ResponseBody updateTimeInfo(String var1, DeviceTimeconfResource var2);

   ResponseBody getUpdatedTimeInfoResult(String var1, String var2);

   ResponseBody updateSetupInfo(String var1, DeviceSystemSetupConfResource var2);

   ResponseBody getUpdatedSetupInfoResult(String var1, String var2);

   ResponseBody updateDisplayInfo(String var1, DeviceDisplayConfResource var2);

   ResponseBody getUpdatedDisplayInfoResult(String var1, String var2);

   ResponseBody updateSecurityInfo(String var1, DeviceSecurityConfResource var2);

   ResponseBody getDevice(String var1);

   ResponseBody getCurrentStatusTime(String var1, String var2);

   ResponseBody getCurrentStatusDisplay(String var1, String var2);

   ResponseBody getCurrentStatusSecurity(String var1, String var2);

   ResponseBody getUnapprovedDeviceList(DeviceFilter var1);

   ResponseBody getCabinetList(String var1, DeviceFilter var2);

   ResponseBody updateCabinetInfo(String var1, DeviceLedCabinetResource var2);

   ResponseBody getRmServerVnc(String var1, String var2, HttpServletRequest var3) throws Exception;

   Long uploadCustomizeFile(String var1, String var2, MultipartFile var3) throws Exception;

   void publishCustomizeFile(String var1, String var2) throws Exception;

   String saveRmRuleFile(MultipartFile var1) throws Exception;

   ResponseBody getDeviceMonitoring(String[] var1, Map var2) throws Exception;

   ResponseBody getError(String[] var1, String var2, Integer var3, Integer var4) throws SQLException;

   ResponseBody assignTag(String var1, DeviceTagAssignment var2) throws Exception;
}
