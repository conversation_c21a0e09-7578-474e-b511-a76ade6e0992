package com.samsung.magicinfo.protocol.util;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.page.ListManager;
import com.samsung.common.utils.page.PageManager;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceGroupDao;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.ruleProcessing.Manager.AlarmManager;
import com.samsung.magicinfo.framework.device.ruleProcessing.Manager.AlarmManagerImpl;
import com.samsung.magicinfo.framework.monitoring.entity.ErrorWarning;
import com.samsung.magicinfo.framework.scheduler.entity.ScheduleAdminEntity;
import com.samsung.magicinfo.framework.setup.entity.NotificationTypeEntity;
import com.samsung.magicinfo.framework.setup.manager.NotificationInfo;
import com.samsung.magicinfo.framework.setup.manager.NotificationInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.protocol.entity.EmailAlarm;
import java.io.FileOutputStream;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.apache.logging.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.context.support.ResourceBundleMessageSource;

public class EmailAlarmJob implements Job {
   public static final int LIST_SIZE = 100;
   private static Logger logger = LoggingManagerV2.getLogger(EmailAlarmJob.class);
   protected final ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
   private String[] unSupportedDevice = new String[]{"FLIP", "RKIOSK"};

   public EmailAlarmJob() {
      super();
      this.rms.setBasename("resource/messages");
   }

   public void execute(JobExecutionContext jobexecutioncontext) throws JobExecutionException {
      String trigger_group = jobexecutioncontext.getTrigger().getKey().getGroup();
      String orgId = trigger_group.split("_")[1];
      this.sendEmailAlarm(Long.valueOf(orgId));
   }

   private boolean isSupported(String deviceType) {
      String[] var2 = this.unSupportedDevice;
      int var3 = var2.length;

      for(int var4 = 0; var4 < var3; ++var4) {
         String type = var2[var4];
         if (deviceType.equals(type)) {
            return false;
         }
      }

      return true;
   }

   private boolean isDevicePermission(Map serverInfoMap) {
      boolean devicePermissions = false;

      try {
         devicePermissions = Boolean.valueOf(serverInfoMap.get("device_permissions").toString());
      } catch (Exception var4) {
      }

      return devicePermissions;
   }

   private boolean isDeviceGroupType(Map serverInfoMap) {
      boolean isDeviceGroupType = false;
      String groupsNotifyMenuType = null;

      try {
         groupsNotifyMenuType = serverInfoMap.get("ALARM_GROUPS_NOTIFY_TYPE").toString();
         if (0 != Integer.valueOf(groupsNotifyMenuType)) {
            isDeviceGroupType = true;
         }
      } catch (Exception var5) {
         logger.debug("ALARM_GROUPS_NOTIFY_TYPE ERROR : [" + groupsNotifyMenuType + "]");
      }

      return isDeviceGroupType;
   }

   public List getDevicePermissionGroupList(User user) {
      try {
         if (DeviceUtils.isDeviceGroupAuth(user)) {
            DeviceGroupInfo deviceGroup = DeviceGroupInfoImpl.getInstance();
            List checkGroupList = deviceGroup.getPermissionsDeviceGroupList(user.getUser_id());
            return checkGroupList;
         }
      } catch (SQLException var4) {
         logger.error(var4);
      }

      return null;
   }

   private long getDisconnectedDuration(Map serverInfoMap) {
      long disconnectedDuration = 0L;
      if (serverInfoMap.get("device_disconnected_duration") != null) {
         disconnectedDuration = Long.valueOf(serverInfoMap.get("device_disconnected_duration").toString());
         if (disconnectedDuration > 0L) {
            disconnectedDuration = disconnectedDuration * 60L * 60L * 1000L;
         }
      }

      return disconnectedDuration;
   }

   public boolean isEmpty(List list) {
      return list == null || list.size() <= 0;
   }

   private String getMailContent() {
      return this.rms.getMessage("MIS_SID_SERVER_EMAIL_SENT_FORM_MIS_CHECK", (Object[])null, new Locale("en"));
   }

   public void sendEmailAlarm(long orgId) {
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      UserGroupInfo userGroupDao = UserGroupInfoImpl.getInstance();
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      Map serverInfoMap = null;
      EmailAlarm emailAlarm = new EmailAlarm();
      emailAlarm.setOrgId(orgId);
      logger.info("EmailAlarmJob execute orgId : " + emailAlarm.getOrgId());

      try {
         serverInfoMap = serverSetupDao.getServerInfoByOrgId(emailAlarm.getOrgId());
         emailAlarm.setOrgName(userGroupDao.getGroupNameByGroupId(emailAlarm.getOrgId()));
      } catch (SQLException | NumberFormatException var21) {
         logger.error("serverInfomap or orgName error");
         return;
      }

      if (null != serverInfoMap) {
         Timestamp startTime = (Timestamp)serverInfoMap.get("DISCONNECT_START_TIME");
         Timestamp endTime = (Timestamp)serverInfoMap.get("DISCONNECT_END_TIME");
         String activatedDays = (String)serverInfoMap.get("DISCONNECT_ACTIVATED_DAYS");
         emailAlarm.setExpirePlaylistDay(Integer.parseInt(serverInfoMap.get("EXPIRE_PLAYLIST_DAY").toString()));
         emailAlarm.setExpireScheduleDay(Integer.parseInt(serverInfoMap.get("EXPIRE_SCHEDULE_DAY").toString()));
         if (!MailUtil.checkTimeValidation(startTime, endTime)) {
            logger.debug("Current time is not in operation time.");
         } else if (!MailUtil.checkDayValidation(activatedDays)) {
            logger.debug("Current day is not in operation days.");
         } else {
            emailAlarm.setDevicePermission(this.isDevicePermission(serverInfoMap));
            emailAlarm.setDisconnectedDuration(this.getDisconnectedDuration(serverInfoMap));
            emailAlarm.setDeviceGroupType(this.isDeviceGroupType(serverInfoMap));
            DeviceGroupInfo deviceGroup = DeviceGroupInfoImpl.getInstance();

            try {
               emailAlarm.setGroupAllDeviceList(deviceGroup.getGroupDeviceListByOrganName(emailAlarm.getOrgName()));
            } catch (Exception var20) {
               logger.error("select deviceGroup.getGroupDeviceList", var20);
            }

            if (emailAlarm.isDeviceGroupType()) {
               try {
                  emailAlarm.setCheckGroupList(deviceGroup.getAlarmDeviceGroupListByName(emailAlarm.getOrgName()));
               } catch (Exception var19) {
                  logger.error("select deviceGroup.getAlarmDeviceGroupListByName", var19);
               }
            }

            ListManager listMgr = null;
            PageManager pageMgr = null;
            SelectCondition condition = new SelectCondition();
            condition.setRole_name("Server Administrator");
            condition.setUser_id("admin");
            condition.setOrg_id(orgId);

            try {
               listMgr = new ListManager(deviceInfo, "list");
               listMgr.addSearchInfo("condition", condition);
               listMgr.setLstSize(100);
               DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
               emailAlarm.setDeviceOrgId(deviceGroupDao.getOrganGroupIdByName(emailAlarm.getOrgName()));
               List deviceGroupList = deviceGroupDao.getChildGroupList((int)emailAlarm.getDeviceOrgId(), true);
               listMgr.addSearchInfo("deviceGroupList", deviceGroupList);
               condition.setOrg_id(emailAlarm.getDeviceOrgId());
            } catch (Exception var18) {
               logger.error("", var18);
            }

            try {
               emailAlarm.setListMgr(listMgr);
               this.sendDisconnectDevice(emailAlarm);
               this.sendTimezoneNotSet(emailAlarm);
               this.sendScheduleNotPublish(emailAlarm);
               this.sendContentDownloadError(emailAlarm);
               this.sendDefaultContentPlaying(emailAlarm);
               this.sendErrorWarning(emailAlarm);
               this.sendExpirePlaylist(emailAlarm);
               this.sendExpireSchedule(emailAlarm);
            } catch (Exception var17) {
               logger.error(var17);
            }

         }
      }
   }

   private void sendErrorWarning(EmailAlarm emailAlarm) {
      AlarmManager alarmInfo = AlarmManagerImpl.getInstance();
      NotificationInfo notificationInfo = NotificationInfoImpl.getInstance();
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      List clientFaultList = null;

      try {
         clientFaultList = alarmInfo.getErrorWarningListByOrganization(emailAlarm.getOrgName(), false, "SOFTWAREWARNING");
         clientFaultList.addAll(alarmInfo.getErrorWarningListByOrganization(emailAlarm.getOrgName(), false, "DEVICEWARNING"));
         clientFaultList.addAll(alarmInfo.getErrorWarningListByOrganization(emailAlarm.getOrgName(), false, "DEVICEERROR"));
         if (this.isEmpty(clientFaultList)) {
            return;
         }
      } catch (Exception var17) {
         logger.error("EmailAlarmJob Error : ", var17);
      }

      try {
         List notiTypeList = notificationInfo.getAllNotificationType();
         Iterator var7 = notiTypeList.iterator();

         while(true) {
            String type;
            do {
               if (!var7.hasNext()) {
                  return;
               }

               NotificationTypeEntity notiType = (NotificationTypeEntity)var7.next();
               type = notiType.getType();
            } while(org.apache.commons.lang3.StringUtils.isBlank(type));

            List ewList = new ArrayList();
            Iterator var11;
            ErrorWarning ew;
            if (type.length() == 3 && type.charAt(0) == 'C') {
               var11 = clientFaultList.iterator();

               while(var11.hasNext()) {
                  ew = (ErrorWarning)var11.next();
                  if (ew.getCode().indexOf(type) == 0) {
                     ewList.add(ew);
                  }
               }
            } else if (type.length() == 5 && org.apache.commons.lang3.StringUtils.isNumeric(type)) {
               var11 = clientFaultList.iterator();

               while(var11.hasNext()) {
                  ew = (ErrorWarning)var11.next();
                  if (ew.getCode().equals(type)) {
                     ewList.add(ew);
                  }
               }
            }

            List ewGroupList = new ArrayList();
            if (emailAlarm.isDeviceGroupType() && ewList.size() > 0) {
               Iterator var20 = ewList.iterator();

               label89:
               while(true) {
                  while(true) {
                     if (!var20.hasNext()) {
                        break label89;
                     }

                     ErrorWarning ew = (ErrorWarning)var20.next();
                     Long groupId = Long.parseLong(deviceInfo.getDeviceGroupIdByDeviceId(ew.getDevice_id()));
                     Iterator var15 = emailAlarm.getCheckGroupList().iterator();

                     while(var15.hasNext()) {
                        DeviceGroup group = (DeviceGroup)var15.next();
                        if (groupId.equals(group.getGroup_id())) {
                           ewGroupList.add(ew);
                           break;
                        }
                     }
                  }
               }
            } else {
               ewGroupList.addAll(ewList);
            }

            if (!this.isEmpty(ewGroupList)) {
               this.sendErrorWarningMail(emailAlarm, type, ewGroupList);
            }
         }
      } catch (SQLException var18) {
         logger.error("", var18);
      }
   }

   private void sendDefaultContentPlaying(EmailAlarm emailAlarm) {
      UserInfoImpl userDao = UserInfoImpl.getInstance();

      try {
         emailAlarm.setMailingUserList(userDao.getAlarmUserListByOrgIdAndType(emailAlarm.getOrgId(), "DEFAULT_CONTENT_PLAYED"));
      } catch (SQLException var17) {
         logger.error(var17);
      }

      if (!this.isEmpty(emailAlarm.getMailingUserList())) {
         DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
         List fetchPlayingDefaultContentList = null;

         try {
            fetchPlayingDefaultContentList = deviceInfo.getPlayingDefaultContentHistoryList(emailAlarm.getOrgName());
         } catch (SQLException var16) {
            logger.error(var16);
         }

         List playingDefaultContentList = new ArrayList();
         Iterator var6 = fetchPlayingDefaultContentList.iterator();

         while(true) {
            while(true) {
               Map userId;
               while(var6.hasNext()) {
                  userId = (Map)var6.next();
                  Long group_id = (Long)userId.get("group_id");
                  if (emailAlarm.isDeviceGroupType()) {
                     Iterator var9 = emailAlarm.getCheckGroupList().iterator();

                     while(var9.hasNext()) {
                        DeviceGroup group = (DeviceGroup)var9.next();
                        if (group_id.equals(group.getGroup_id())) {
                           playingDefaultContentList.add(userId);
                           break;
                        }
                     }
                  } else {
                     playingDefaultContentList.add(userId);
                  }
               }

               if (this.isEmpty(playingDefaultContentList)) {
                  return;
               }

               try {
                  deviceInfo.deletePlayingDefaultContentHistoryByOrganizationName(emailAlarm.getOrgName());
               } catch (SQLException var15) {
                  logger.error(var15);
               }

               String toUserIdList = MailUtil.getUserIdList(emailAlarm.getMailingUserList());
               userId = null;
               String filename = null;
               if (!emailAlarm.isDevicePermission()) {
                  filename = this.makeExcelFileDefaultContentPlaying(emailAlarm.getOrgId(), emailAlarm.getOrgName(), userId, playingDefaultContentList);
                  MailUtil.makeMail("DEFAULT_CONTENT_PLAYED", this.getMailContent(), toUserIdList, emailAlarm.getOrgId(), filename);
                  return;
               }

               DeviceGroupDao deviceGroupDao = new DeviceGroupDao();

               for(int i = 0; i < emailAlarm.getMailingUserList().size(); ++i) {
                  String userId = (String)((Map)emailAlarm.getMailingUserList().get(i)).get("USER_ID");

                  try {
                     List deviceIdListByPermission = deviceGroupDao.getDeviceIdListByDeviceGroupPermission(userId);
                     if (this.isEmpty(deviceIdListByPermission)) {
                        continue;
                     }

                     List playingDefaultContentListByUser = new ArrayList();
                     Iterator var13 = playingDefaultContentList.iterator();

                     while(var13.hasNext()) {
                        Map item = (Map)var13.next();
                        if (deviceIdListByPermission.contains(item.get("device_id").toString())) {
                           playingDefaultContentListByUser.add(item);
                        }
                     }

                     filename = this.makeExcelFileDefaultContentPlaying(emailAlarm.getOrgId(), emailAlarm.getOrgName(), userId, playingDefaultContentListByUser);
                  } catch (SQLException var18) {
                     logger.error(var18);
                  }

                  MailUtil.makeMail("DEFAULT_CONTENT_PLAYED", this.getMailContent(), userId, emailAlarm.getOrgId(), filename);
               }

               return;
            }
         }
      }
   }

   private String getDeviceGroupNameList(String deviceId) {
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      String groupName = "";
      int groupId = 0;

      try {
         groupId = Integer.parseInt(deviceInfo.getDeviceGroupIdByDeviceId(deviceId));
      } catch (SQLException var8) {
         logger.error(var8);
      }

      List groups = DeviceUtils.getGroupNamePath(groupId);
      StringBuilder sb = new StringBuilder();

      for(int j = 0; j < groups.size(); ++j) {
         if (j > 0) {
            sb.append(" > ");
         }

         sb.append(((DeviceGroup)groups.get(j)).getGroup_name());
      }

      groupName = sb.toString();
      return groupName;
   }

   private void sendExpireSchedule(EmailAlarm emailAlarm) {
      UserInfoImpl userDao = UserInfoImpl.getInstance();

      try {
         emailAlarm.setMailingUserList(userDao.getAlarmUserListByOrgIdAndType(emailAlarm.getOrgId(), "EXPIRE_SCHEDULE"));
      } catch (SQLException var24) {
         logger.error(var24);
      }

      if (!this.isEmpty(emailAlarm.getMailingUserList())) {
         DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
         FileOutputStream fileOut = null;

         try {
            List expireSchList = deviceInfo.getCheckUpcomingExpiryDate(emailAlarm.getExpireScheduleDay(), emailAlarm.getDeviceOrgId());
            if (this.isEmpty(expireSchList)) {
               return;
            }

            String filename = MailUtil.getFileName(emailAlarm.getOrgName(), "EXPIRE_SCHEDULE");
            String[] excelHeaderNames = new String[]{"Schedule Name", "Schedule Type", "Device Type", "Create Date", "Date Modified", "Expiration Date"};
            HSSFWorkbook workbook = MailUtil.createExcelFile("EXPIRE_SCHEDULE", excelHeaderNames);
            HSSFSheet sheet = workbook.getSheetAt(0);
            short rowIndex = 1;
            Iterator var11 = expireSchList.iterator();

            while(var11.hasNext()) {
               ScheduleAdminEntity item = (ScheduleAdminEntity)var11.next();
               HSSFRow row = sheet.createRow(rowIndex++);
               row.createCell(0).setCellValue(item.getProgram_name());
               row.createCell(1).setCellValue(item.getProgram_type());
               row.createCell(2).setCellValue(item.getDevice_type());
               row.createCell(3).setCellValue(DateUtils.timestamp2String(item.getCreate_date(), "yyyy-MM-dd HH:mm:ss"));
               row.createCell(4).setCellValue(DateUtils.timestamp2String(item.getModify_date(), "yyyy-MM-dd HH:mm:ss"));
               row.createCell(5).setCellValue(item.getStop_date());
            }

            fileOut = new FileOutputStream(filename);
            workbook.write(fileOut);
            fileOut.close();
            String toUserIdList = MailUtil.getUserIdList(emailAlarm.getMailingUserList());
            MailUtil.makeMail("EXPIRE_SCHEDULE", this.getMailContent(), toUserIdList, emailAlarm.getOrgId(), filename);
         } catch (Exception var25) {
            logger.error("", var25);
         } finally {
            if (fileOut != null) {
               try {
                  fileOut.close();
               } catch (Exception var23) {
               }
            }

         }

      }
   }

   private void sendExpirePlaylist(EmailAlarm emailAlarm) {
      UserInfoImpl userDao = UserInfoImpl.getInstance();

      try {
         emailAlarm.setMailingUserList(userDao.getAlarmUserListByOrgIdAndType(emailAlarm.getOrgId(), "EXPIRE_PLAYLIST"));
      } catch (SQLException var24) {
         logger.error(var24);
      }

      if (!this.isEmpty(emailAlarm.getMailingUserList())) {
         List expirePlayList = null;
         DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
         FileOutputStream fileOut = null;

         try {
            expirePlayList = deviceInfo.getCheckUpcomingExpiryDatePlaylistList(emailAlarm.getExpirePlaylistDay(), emailAlarm.getDeviceOrgId() + "");
            if (!this.isEmpty(expirePlayList)) {
               String filename = MailUtil.getFileName(emailAlarm.getOrgName(), "EXPIRE_PLAYLIST");
               String[] excelHeaderNames = new String[]{"Playlist Name", "Total Size", "Play Time", "Playlist Type", "Date Modified", "Creator"};
               HSSFWorkbook workbook = MailUtil.createExcelFile("EXPIRE_PLAYLIST", excelHeaderNames);
               HSSFSheet sheet = workbook.getSheetAt(0);
               short rowIndex = 1;
               Iterator var11 = expirePlayList.iterator();

               while(var11.hasNext()) {
                  Playlist item = (Playlist)var11.next();
                  HSSFRow row = sheet.createRow(rowIndex++);
                  row.createCell(0).setCellValue(item.getPlaylist_name());
                  row.createCell(1).setCellValue((double)item.getTotal_size());
                  row.createCell(2).setCellValue(item.getPlay_time());
                  row.createCell(3).setCellValue(item.getPlaylist_type());
                  row.createCell(4).setCellValue(DateUtils.timestamp2String(item.getLast_modified_date(), "yyyy-MM-dd HH:mm:ss"));
                  row.createCell(5).setCellValue(item.getCreator_id());
               }

               fileOut = new FileOutputStream(filename);
               workbook.write(fileOut);
               fileOut.close();
               String toUserIdList = MailUtil.getUserIdList(emailAlarm.getMailingUserList());
               MailUtil.makeMail("EXPIRE_PLAYLIST", this.getMailContent(), toUserIdList, emailAlarm.getOrgId(), filename);
               return;
            }
         } catch (Exception var25) {
            logger.error("", var25);
            return;
         } finally {
            if (fileOut != null) {
               try {
                  fileOut.close();
               } catch (Exception var23) {
               }
            }

         }

      }
   }

   private void sendContentDownloadError(EmailAlarm emailAlarm) {
      UserInfoImpl userDao = UserInfoImpl.getInstance();

      try {
         emailAlarm.setMailingUserList(userDao.getAlarmUserListByOrgIdAndType(emailAlarm.getOrgId(), "CONTENT_DOWNLOAD_ERROR"));
      } catch (SQLException var15) {
         logger.error(var15);
      }

      if (!this.isEmpty(emailAlarm.getMailingUserList())) {
         List fetchContentDownloadErrDeviceList = new ArrayList();
         emailAlarm.getListMgr().setSection("getCheckDeviceListContent");

         try {
            fetchContentDownloadErrDeviceList = emailAlarm.getListMgr().dbexecute();
         } catch (Exception var14) {
            logger.error(var14);
         }

         List contentDownloadErrDeviceList = new ArrayList();
         Iterator var5 = ((List)fetchContentDownloadErrDeviceList).iterator();

         while(true) {
            while(true) {
               DeviceGeneralConf device;
               while(var5.hasNext()) {
                  device = (DeviceGeneralConf)var5.next();
                  Iterator var7;
                  DeviceGroup group;
                  if (device.getGroup_id() == null) {
                     var7 = emailAlarm.getGroupAllDeviceList().iterator();

                     while(var7.hasNext()) {
                        group = (DeviceGroup)var7.next();
                        if (group.getDeviceId().equals(device.getDevice_id())) {
                           device.setGroup_id(group.getGroup_id());
                           device.setGroup_name(group.getGroup_name());
                           break;
                        }
                     }
                  }

                  if (emailAlarm.isDeviceGroupType()) {
                     var7 = emailAlarm.getCheckGroupList().iterator();

                     while(var7.hasNext()) {
                        group = (DeviceGroup)var7.next();
                        if (device.getGroup_id().equals(group.getGroup_id())) {
                           contentDownloadErrDeviceList.add(device);
                           break;
                        }
                     }
                  } else {
                     contentDownloadErrDeviceList.add(device);
                  }
               }

               if (this.isEmpty(contentDownloadErrDeviceList)) {
                  return;
               }

               String toUserIdList = MailUtil.getUserIdList(emailAlarm.getMailingUserList());
               device = null;
               String filename = null;
               if (!emailAlarm.isDevicePermission()) {
                  filename = this.makeExcelFileDeviceGeneralConf(emailAlarm.getOrgId(), emailAlarm.getOrgName(), device, contentDownloadErrDeviceList, "CONTENT_DOWNLOAD_ERROR");
                  MailUtil.makeMail("CONTENT_DOWNLOAD_ERROR", this.getMailContent(), toUserIdList, emailAlarm.getOrgId(), filename);
                  return;
               }

               DeviceGroupDao deviceGroupDao = new DeviceGroupDao();

               for(int i = 0; i < emailAlarm.getMailingUserList().size(); ++i) {
                  String userId = (String)((Map)emailAlarm.getMailingUserList().get(i)).get("USER_ID");

                  try {
                     List deviceIdListByPermission = deviceGroupDao.getDeviceIdListByDeviceGroupPermission(userId);
                     if (this.isEmpty(deviceIdListByPermission)) {
                        continue;
                     }

                     List contentDownloadErrDeviceListByUser = new ArrayList();
                     Iterator var12 = contentDownloadErrDeviceList.iterator();

                     while(var12.hasNext()) {
                        DeviceGeneralConf device = (DeviceGeneralConf)var12.next();
                        if (deviceIdListByPermission.contains(device.getDevice_id())) {
                           contentDownloadErrDeviceListByUser.add(device);
                        }
                     }

                     filename = this.makeExcelFileDeviceGeneralConf(emailAlarm.getOrgId(), emailAlarm.getOrgName(), userId, contentDownloadErrDeviceListByUser, "CONTENT_DOWNLOAD_ERROR");
                  } catch (SQLException var16) {
                     logger.error(var16);
                  }

                  MailUtil.makeMail("CONTENT_DOWNLOAD_ERROR", this.getMailContent(), userId, emailAlarm.getOrgId(), filename);
               }

               return;
            }
         }
      }
   }

   private void sendScheduleNotPublish(EmailAlarm emailAlarm) {
      UserInfoImpl userDao = UserInfoImpl.getInstance();

      try {
         emailAlarm.setMailingUserList(userDao.getAlarmUserListByOrgIdAndType(emailAlarm.getOrgId(), "SCHEDULE_NOT_PUBLISHED"));
      } catch (Exception var16) {
         logger.error(var16);
      }

      if (!this.isEmpty(emailAlarm.getMailingUserList())) {
         DeviceGroupDao deviceGroupDao = new DeviceGroupDao();
         List fetchScheduleNotDeployDeviceList = new ArrayList();
         emailAlarm.getListMgr().setSection("getCheckDeviceListSchedule");

         try {
            fetchScheduleNotDeployDeviceList = emailAlarm.getListMgr().dbexecute();
         } catch (Exception var15) {
            logger.error(var15);
         }

         List scheduleNotDeployDeviceList = new ArrayList();
         Iterator var6 = ((List)fetchScheduleNotDeployDeviceList).iterator();

         while(true) {
            while(true) {
               DeviceGeneralConf device;
               while(var6.hasNext()) {
                  device = (DeviceGeneralConf)var6.next();
                  Iterator var8;
                  DeviceGroup group;
                  if (device.getGroup_id() == null) {
                     var8 = emailAlarm.getGroupAllDeviceList().iterator();

                     while(var8.hasNext()) {
                        group = (DeviceGroup)var8.next();
                        if (group.getDeviceId().equals(device.getDevice_id())) {
                           device.setGroup_id(group.getGroup_id());
                           device.setGroup_name(group.getGroup_name());
                           break;
                        }
                     }
                  }

                  if (emailAlarm.isDeviceGroupType()) {
                     var8 = emailAlarm.getCheckGroupList().iterator();

                     while(var8.hasNext()) {
                        group = (DeviceGroup)var8.next();
                        if (device.getGroup_id().equals(group.getGroup_id())) {
                           scheduleNotDeployDeviceList.add(device);
                           break;
                        }
                     }
                  } else {
                     scheduleNotDeployDeviceList.add(device);
                  }
               }

               if (this.isEmpty(scheduleNotDeployDeviceList)) {
                  return;
               }

               String toUserIdList = MailUtil.getUserIdList(emailAlarm.getMailingUserList());
               device = null;
               String filename = null;
               if (!emailAlarm.isDevicePermission()) {
                  filename = this.makeExcelFileDeviceGeneralConf(emailAlarm.getOrgId(), emailAlarm.getOrgName(), device, scheduleNotDeployDeviceList, "SCHEDULE_NOT_PUBLISHED");
                  MailUtil.makeMail("SCHEDULE_NOT_PUBLISHED", this.getMailContent(), toUserIdList, emailAlarm.getOrgId(), filename);
                  return;
               }

               for(int i = 0; i < emailAlarm.getMailingUserList().size(); ++i) {
                  String userId = (String)((Map)emailAlarm.getMailingUserList().get(i)).get("USER_ID");

                  try {
                     User user = userDao.getAllByUserId(userId);
                     if (org.apache.commons.lang3.StringUtils.equals(user.getRole_name(), "Administrator")) {
                        filename = this.makeExcelFileDeviceGeneralConf(emailAlarm.getOrgId(), emailAlarm.getOrgName(), userId, scheduleNotDeployDeviceList, "SCHEDULE_NOT_PUBLISHED");
                        MailUtil.makeMail("SCHEDULE_NOT_PUBLISHED", this.getMailContent(), userId, emailAlarm.getOrgId(), filename);
                        continue;
                     }

                     List deviceIdListByPermission = deviceGroupDao.getDeviceIdListByDeviceGroupPermission(userId);
                     if (this.isEmpty(deviceIdListByPermission)) {
                        continue;
                     }

                     List scheduleNotDeployDeviceListByUser = new ArrayList();
                     Iterator var13 = scheduleNotDeployDeviceList.iterator();

                     while(var13.hasNext()) {
                        DeviceGeneralConf device = (DeviceGeneralConf)var13.next();
                        if (deviceIdListByPermission.contains(device.getDevice_id())) {
                           scheduleNotDeployDeviceListByUser.add(device);
                        }
                     }

                     filename = this.makeExcelFileDeviceGeneralConf(emailAlarm.getOrgId(), emailAlarm.getOrgName(), userId, scheduleNotDeployDeviceListByUser, "SCHEDULE_NOT_PUBLISHED");
                  } catch (SQLException var17) {
                     logger.error(var17);
                  }

                  MailUtil.makeMail("SCHEDULE_NOT_PUBLISHED", this.getMailContent(), userId, emailAlarm.getOrgId(), filename);
               }

               return;
            }
         }
      }
   }

   private void sendTimezoneNotSet(EmailAlarm emailAlarm) {
      UserInfoImpl userDao = UserInfoImpl.getInstance();

      try {
         emailAlarm.setMailingUserList(userDao.getAlarmUserListByOrgIdAndType(emailAlarm.getOrgId(), "TIMEZONE_NOT_SET"));
      } catch (SQLException var15) {
         logger.error(var15);
      }

      if (!this.isEmpty(emailAlarm.getMailingUserList())) {
         DeviceGroupDao deviceGroupDao = new DeviceGroupDao();
         List fetchTimezoneNotSetDeviceList = new ArrayList();
         emailAlarm.getListMgr().setSection("getCheckDeviceListTimezone");

         try {
            fetchTimezoneNotSetDeviceList = emailAlarm.getListMgr().dbexecute();
         } catch (Exception var14) {
            logger.error(var14);
         }

         List timezoneNotSetDeviceList = new ArrayList();
         Iterator var6 = ((List)fetchTimezoneNotSetDeviceList).iterator();

         while(true) {
            while(true) {
               DeviceGeneralConf device;
               while(var6.hasNext()) {
                  device = (DeviceGeneralConf)var6.next();
                  Iterator var8;
                  DeviceGroup group;
                  if (device.getGroup_id() == null) {
                     var8 = emailAlarm.getGroupAllDeviceList().iterator();

                     while(var8.hasNext()) {
                        group = (DeviceGroup)var8.next();
                        if (group.getDeviceId().equals(device.getDevice_id())) {
                           device.setGroup_id(group.getGroup_id());
                           device.setGroup_name(group.getGroup_name());
                           break;
                        }
                     }
                  }

                  if (emailAlarm.isDeviceGroupType()) {
                     var8 = emailAlarm.getCheckGroupList().iterator();

                     while(var8.hasNext()) {
                        group = (DeviceGroup)var8.next();
                        if (device.getGroup_id().equals(group.getGroup_id())) {
                           timezoneNotSetDeviceList.add(device);
                           break;
                        }
                     }
                  } else {
                     timezoneNotSetDeviceList.add(device);
                  }
               }

               if (this.isEmpty(timezoneNotSetDeviceList)) {
                  return;
               }

               String toUserIdList = MailUtil.getUserIdList(emailAlarm.getMailingUserList());
               device = null;
               String filename = null;
               if (!emailAlarm.isDevicePermission()) {
                  filename = this.makeExcelFileDeviceGeneralConf(emailAlarm.getOrgId(), emailAlarm.getOrgName(), device, timezoneNotSetDeviceList, "TIMEZONE_NOT_SET");
                  MailUtil.makeMail("TIMEZONE_NOT_SET", this.getMailContent(), toUserIdList, emailAlarm.getOrgId(), filename);
                  return;
               }

               for(int i = 0; i < emailAlarm.getMailingUserList().size(); ++i) {
                  String userId = (String)((Map)emailAlarm.getMailingUserList().get(i)).get("USER_ID");

                  try {
                     List deviceIdListByPermission = deviceGroupDao.getDeviceIdListByDeviceGroupPermission(userId);
                     if (this.isEmpty(deviceIdListByPermission)) {
                        continue;
                     }

                     List fetchTimezoneNotSetDeviceListByUser = new ArrayList();
                     Iterator var12 = timezoneNotSetDeviceList.iterator();

                     while(var12.hasNext()) {
                        DeviceGeneralConf device = (DeviceGeneralConf)var12.next();
                        if (deviceIdListByPermission.contains(device.getDevice_id())) {
                           fetchTimezoneNotSetDeviceListByUser.add(device);
                        }
                     }

                     filename = this.makeExcelFileDeviceGeneralConf(emailAlarm.getOrgId(), emailAlarm.getOrgName(), userId, fetchTimezoneNotSetDeviceListByUser, "TIMEZONE_NOT_SET");
                  } catch (SQLException var16) {
                     logger.error(var16);
                  }

                  MailUtil.makeMail("TIMEZONE_NOT_SET", this.getMailContent(), userId, emailAlarm.getOrgId(), filename);
               }

               return;
            }
         }
      }
   }

   private String makeExcelFileErrorWarning(long orgId, String orgName, String userId, List ewList, String title, String description) {
      FileOutputStream fileOut = null;
      String filename = null;

      try {
         DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
         filename = MailUtil.getFileName(orgName, title, userId);
         String[] excelHeaderNames = new String[]{"Device Name", "Mac Address", "Code", "Error Description", "Client Time"};
         HSSFWorkbook workbook = MailUtil.createExcelFile(title, excelHeaderNames);
         HSSFSheet sheet = workbook.getSheetAt(0);
         short rowIndex = 1;
         HSSFRow row = null;
         Iterator var16 = ewList.iterator();

         while(var16.hasNext()) {
            ErrorWarning item = (ErrorWarning)var16.next();
            row = sheet.createRow(rowIndex++);
            Device device = deviceInfo.getDeviceMinInfo(item.getDevice_id());
            row.createCell(0).setCellValue(device.getDevice_name());
            row.createCell(1).setCellValue(device.getDevice_id());
            row.createCell(2).setCellValue(item.getCode());
            row.createCell(3).setCellValue(description);
            row.createCell(4).setCellValue(item.getLast_reported());
         }

         if (rowIndex > 1) {
            fileOut = new FileOutputStream(filename);
            workbook.write(fileOut);
            fileOut.close();
            logger.debug("Sending an alarm mail for detecting disconnected devices.");
         } else {
            workbook.close();
            logger.debug("no disconnected device. org id = " + orgId);
         }
      } catch (Exception var27) {
         logger.error("", var27);
      } finally {
         if (fileOut != null) {
            try {
               fileOut.close();
            } catch (Exception var26) {
               logger.error(var26);
            }
         }

      }

      return filename;
   }

   private String makeExcelFileDefaultContentPlaying(long orgId, String orgName, String userId, List playingDefaultContentList) {
      FileOutputStream fileOut = null;
      String filename = null;

      try {
         DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
         filename = MailUtil.getFileName(orgName, "DEFAULT_CONTENT_PLAYED", userId);
         String[] excelHeaderNames = new String[]{"Device Id", "Device Name", "Group", "Time"};
         HSSFWorkbook workbook = MailUtil.createExcelFile("DEFAULT_CONTENT_PLAYED", excelHeaderNames);
         HSSFSheet sheet = workbook.getSheetAt(0);
         short rowIndex = 1;
         HSSFRow row = null;
         Iterator var14 = playingDefaultContentList.iterator();

         while(true) {
            Map item;
            String deviceId;
            String deviceName;
            String groupName;
            while(true) {
               if (!var14.hasNext()) {
                  if (rowIndex > 1) {
                     fileOut = new FileOutputStream(filename);
                     workbook.write(fileOut);
                     fileOut.close();
                     logger.debug("Sending an alarm mail for detecting disconnected devices.");
                  } else {
                     workbook.close();
                     logger.debug("no disconnected device. org id = " + orgId);
                  }

                  return filename;
               }

               item = (Map)var14.next();
               deviceId = item.get("device_id").toString();
               deviceName = "";
               groupName = "";

               try {
                  Device device = deviceInfo.getDevice(deviceId);
                  if (this.isSupported(device.getDevice_type())) {
                     deviceName = device.getDevice_name();
                     groupName = this.getDeviceGroupNameList(deviceId);
                     break;
                  }

                  playingDefaultContentList.remove(item);
               } catch (Exception var29) {
                  logger.error(var29);
                  break;
               }
            }

            row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue(deviceId);
            row.createCell(1).setCellValue(deviceName);
            row.createCell(2).setCellValue(groupName);
            row.createCell(3).setCellValue(DateUtils.timestamp2String((Timestamp)item.get("time"), "yyyy-MM-dd HH:mm:ss"));
         }
      } catch (Exception var30) {
         logger.error("", var30);
      } finally {
         if (fileOut != null) {
            try {
               fileOut.close();
            } catch (Exception var28) {
               logger.error(var28);
            }
         }

      }

      return filename;
   }

   private String makeExcelFileDeviceGeneralConf(long orgId, String orgName, String userId, List deviceList, String notificationType) {
      FileOutputStream fileOut = null;
      String filename = null;

      try {
         filename = MailUtil.getFileName(orgName, notificationType, userId);
         String[] excelHeaderNames = new String[]{"Name", "Model Name", "Device ID", "IP Address", "Group Name", "Create Date"};
         HSSFWorkbook workbook = MailUtil.createExcelFile(notificationType, excelHeaderNames);
         HSSFSheet sheet = workbook.getSheetAt(0);
         short rowIndex = 1;
         HSSFRow row = null;
         Iterator var14 = deviceList.iterator();

         while(var14.hasNext()) {
            DeviceGeneralConf device = (DeviceGeneralConf)var14.next();
            row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue(device.getDevice_name());
            row.createCell(1).setCellValue(device.getDevice_model_name());
            row.createCell(2).setCellValue(device.getDevice_id());
            row.createCell(3).setCellValue(device.getIp_address());
            row.createCell(4).setCellValue(device.getGroup_name());
            row.createCell(5).setCellValue(DateUtils.timestamp2String(device.getCreate_date(), "yyyy-MM-dd HH:mm:ss"));
         }

         if (rowIndex > 1) {
            fileOut = new FileOutputStream(filename);
            workbook.write(fileOut);
            fileOut.close();
            logger.debug("Sending an alarm mail for detecting " + notificationType);
         } else {
            workbook.close();
            logger.debug("org id = " + orgId);
         }
      } catch (Exception var24) {
         logger.error("", var24);
      } finally {
         if (fileOut != null) {
            try {
               fileOut.close();
            } catch (Exception var23) {
               logger.error(var23);
            }
         }

      }

      return filename;
   }

   private String makeExcelFileDisconnectDevice(long orgId, String orgName, String userId, List disconnectedDeviceList) {
      FileOutputStream fileOut = null;
      String filename = null;

      try {
         filename = MailUtil.getFileName(orgName, "DISCONNECTED_DEVICE", userId);
         String[] excelHeaderNames = new String[]{"Name", "Model Name", "Device ID", "IP Address", "Group Name", "Disconnected Date"};
         HSSFWorkbook workbook = MailUtil.createExcelFile("DISCONNECTED_DEVICE", excelHeaderNames);
         HSSFSheet sheet = workbook.getSheetAt(0);
         short rowIndex = 1;
         HSSFRow row = null;
         Iterator var13 = disconnectedDeviceList.iterator();

         while(var13.hasNext()) {
            Device device = (Device)var13.next();
            row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue(device.getDevice_name());
            row.createCell(1).setCellValue(device.getDevice_model_name());
            row.createCell(2).setCellValue(device.getDevice_id());
            row.createCell(3).setCellValue(device.getIp_address());
            row.createCell(4).setCellValue(device.getGroup_name());
            row.createCell(5).setCellValue(DateUtils.timestamp2String(device.getLast_connection_time(), "yyyy-MM-dd HH:mm:ss"));
         }

         if (rowIndex > 1) {
            fileOut = new FileOutputStream(filename);
            workbook.write(fileOut);
            fileOut.close();
            logger.debug("Sending an alarm mail for detecting disconnected devices.");
         } else {
            workbook.close();
            logger.debug("no disconnected device. org id = " + orgId);
         }
      } catch (Exception var23) {
         logger.error("", var23);
      } finally {
         if (fileOut != null) {
            try {
               fileOut.close();
            } catch (Exception var22) {
               logger.error(var22);
            }
         }

      }

      return filename;
   }

   private void sendDisconnectDevice(EmailAlarm emailAlarm) {
      UserInfoImpl userDao = UserInfoImpl.getInstance();

      try {
         emailAlarm.setMailingUserList(userDao.getAlarmUserListByOrgIdAndType(emailAlarm.getOrgId(), "DISCONNECTED_DEVICE"));
      } catch (SQLException var14) {
         logger.error(var14);
      }

      if (!this.isEmpty(emailAlarm.getMailingUserList())) {
         DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
         DeviceGroupDao deviceGroupDao = new DeviceGroupDao();
         ArrayList disconnectedDeviceList = new ArrayList();

         ArrayList deviceList;
         try {
            List fetchDeviceList = deviceInfo.getDeviceListByOrgName(emailAlarm.getOrgName());
            deviceList = new ArrayList();
            Iterator var10;
            if (!emailAlarm.isDeviceGroupType()) {
               deviceList.addAll(fetchDeviceList);
            } else {
               Iterator var8 = fetchDeviceList.iterator();

               label111:
               while(true) {
                  while(true) {
                     if (!var8.hasNext()) {
                        break label111;
                     }

                     Device device = (Device)var8.next();
                     var10 = emailAlarm.getCheckGroupList().iterator();

                     while(var10.hasNext()) {
                        DeviceGroup group = (DeviceGroup)var10.next();
                        if (device.getGroup_id().equals(group.getGroup_id())) {
                           deviceList.add(device);
                           break;
                        }
                     }
                  }
               }
            }

            long currentTime = System.currentTimeMillis();
            var10 = deviceList.iterator();

            label96:
            while(true) {
               Device device;
               do {
                  do {
                     if (!var10.hasNext()) {
                        break label96;
                     }

                     device = (Device)var10.next();
                  } while(DeviceUtils.isConnected(device.getDevice_id()));
               } while(emailAlarm.getDisconnectedDuration() > 0L && currentTime - device.getLast_connection_time().getTime() < emailAlarm.getDisconnectedDuration());

               disconnectedDeviceList.add(device);
            }
         } catch (SQLException var16) {
            logger.error("", var16);
         }

         if (!this.isEmpty(disconnectedDeviceList)) {
            String toUserIdList = MailUtil.getUserIdList(emailAlarm.getMailingUserList());
            deviceList = null;
            String filename = null;
            if (!emailAlarm.isDevicePermission()) {
               filename = this.makeExcelFileDisconnectDevice(emailAlarm.getOrgId(), emailAlarm.getOrgName(), deviceList, disconnectedDeviceList);
               MailUtil.makeMail("DISCONNECTED_DEVICE", this.getMailContent(), toUserIdList, emailAlarm.getOrgId(), filename);
            } else {
               for(int i = 0; i < emailAlarm.getMailingUserList().size(); ++i) {
                  String userId = (String)((Map)emailAlarm.getMailingUserList().get(i)).get("USER_ID");

                  try {
                     List deviceIdListByPermission = deviceGroupDao.getDeviceIdListByDeviceGroupPermission(userId);
                     if (this.isEmpty(deviceIdListByPermission)) {
                        continue;
                     }

                     List disconnectedDeviceListByUser = new ArrayList();
                     Iterator var12 = disconnectedDeviceList.iterator();

                     while(var12.hasNext()) {
                        Device device = (Device)var12.next();
                        if (deviceIdListByPermission.contains(device.getDevice_id())) {
                           disconnectedDeviceListByUser.add(device);
                        }
                     }

                     filename = this.makeExcelFileDisconnectDevice(emailAlarm.getOrgId(), emailAlarm.getOrgName(), userId, disconnectedDeviceListByUser);
                  } catch (SQLException var15) {
                     logger.error(var15);
                  }

                  MailUtil.makeMail("DISCONNECTED_DEVICE", this.getMailContent(), userId, emailAlarm.getOrgId(), filename);
               }

            }
         }
      }
   }

   private void sendErrorWarningMail(EmailAlarm emailAlarm, String type, List ewList) {
      UserInfo userInfo = UserInfoImpl.getInstance();
      NotificationInfo notificationInfo = NotificationInfoImpl.getInstance();
      String description = "";

      try {
         description = notificationInfo.getNotificationType(type).getDescription();
      } catch (SQLException var19) {
         logger.error(var19);
      }

      String message = type + " " + description;
      if (!this.isEmpty(ewList)) {
         try {
            emailAlarm.setMailingUserList(userInfo.getAlarmUserListByOrgIdAndType(emailAlarm.getOrgId(), type));
         } catch (SQLException var18) {
            logger.error("EmailAlarmJob Error : ", var18);
         }

         if (!this.isEmpty(emailAlarm.getMailingUserList())) {
            String toUserIdList = MailUtil.getUserIdList(emailAlarm.getMailingUserList());
            String userId = null;
            String filename = null;
            String title = "Device_Health_" + type;
            if (!emailAlarm.isDevicePermission()) {
               filename = this.makeExcelFileErrorWarning(emailAlarm.getOrgId(), emailAlarm.getOrgName(), userId, ewList, title, description);
               MailUtil.makeMail(message, this.getMailContent(), toUserIdList, emailAlarm.getOrgId(), filename);
            } else {
               DeviceGroupDao deviceGroupDao = new DeviceGroupDao();

               for(int i = 0; i < emailAlarm.getMailingUserList().size(); ++i) {
                  userId = (String)((Map)emailAlarm.getMailingUserList().get(i)).get("USER_ID");

                  try {
                     List deviceIdListByPermission = deviceGroupDao.getDeviceIdListByDeviceGroupPermission(userId);
                     if (this.isEmpty(deviceIdListByPermission)) {
                        continue;
                     }

                     List ewListByUser = new ArrayList();
                     Iterator var16 = ewList.iterator();

                     while(var16.hasNext()) {
                        ErrorWarning item = (ErrorWarning)var16.next();
                        if (deviceIdListByPermission.contains(item.getDevice_id())) {
                           ewListByUser.add(item);
                        }
                     }

                     filename = this.makeExcelFileErrorWarning(emailAlarm.getOrgId(), emailAlarm.getOrgName(), userId, ewListByUser, title, description);
                  } catch (SQLException var20) {
                     logger.error(var20);
                  }

                  MailUtil.makeMail(message, this.getMailContent(), userId, emailAlarm.getOrgId(), filename);
               }

            }
         }
      }
   }
}
