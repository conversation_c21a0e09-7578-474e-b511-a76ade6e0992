package com.samsung.magicinfo.webauthor2.webapi.controller;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.exception.WebAuthorAbstractException;
import com.samsung.magicinfo.webauthor2.exception.service.DownloaderException;
import com.samsung.magicinfo.webauthor2.exception.service.FileItemValidationException;
import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.exception.service.WebContentValidationException;
import com.samsung.magicinfo.webauthor2.model.CidMappingResponse;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.FileItemsDescriptor;
import com.samsung.magicinfo.webauthor2.model.HtmlPreviewResponse;
import com.samsung.magicinfo.webauthor2.model.PreviewUsageResponse;
import com.samsung.magicinfo.webauthor2.model.UploadResponse;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import com.samsung.magicinfo.webauthor2.service.UploadFileNameValidationService;
import com.samsung.magicinfo.webauthor2.service.WebContentService;
import com.samsung.magicinfo.webauthor2.service.WebContentValidationService;
import com.samsung.magicinfo.webauthor2.service.upload.WebContentUploadService;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

@RestController
@RequestMapping({"/webContent"})
public class WebContentController {
  private static final Logger logger = LoggerFactory.getLogger(WebContentController.class);
  
  private final WebContentValidationService webContentValidationService;
  
  private final WebContentUploadService webContentUploadService;
  
  private final WebContentService webContentService;
  
  public static final String PREVIEW_RESOURCE_MAPPED_NAME = "/preview";
  
  @Autowired
  public WebContentController(WebContentValidationService webContentValidationService, WebContentUploadService webContentUploadService, UploadFileNameValidationService uploadFileNameValidationService, WebContentService webContentService) {
    this.webContentValidationService = webContentValidationService;
    this.webContentUploadService = webContentUploadService;
    this.webContentService = webContentService;
  }
  
  @PostMapping({"/thumbnailFromImportFile"})
  public HttpEntity<MediaSource> updateWebContentThumbnail(@RequestParam String thumbFilePath) throws FileItemValidationException, WebContentValidationException, IOException {
    MediaSource thumbnailMS = this.webContentUploadService.setWebContentThumbnail(thumbFilePath);
    return (HttpEntity<MediaSource>)ResponseEntity.ok(thumbnailMS);
  }
  
  @PostMapping(value = {"/thumbnail"}, consumes = {"multipart/form-data"})
  public HttpEntity<MediaSource> updateWebContentThumbnail(@RequestParam("upload") MultipartFile thumbnail) throws FileItemValidationException, WebContentValidationException, IOException {
    MediaSource thumbnailMS = this.webContentUploadService.setWebContentThumbnail(thumbnail);
    return (HttpEntity<MediaSource>)ResponseEntity.ok(thumbnailMS);
  }
  
  @PostMapping({"/zipPrerequisiteFromImportFile"})
  public HttpEntity<List<MediaSource>> requestMediaSource(@RequestParam String contentName, @RequestParam String startupPage, @RequestParam String playerType, @RequestParam String contentWidth, @RequestParam String contentHeight, @RequestParam int lfdSizeExceptMediaFileSizes, @RequestParam String zipFilePath) throws FileItemValidationException, WebContentValidationException, IOException {
    this.webContentValidationService.validateWebContentInZip(zipFilePath, startupPage);
    String contentId = "";
    List<MediaSource> updatedMediaSources = this.webContentUploadService.getUpdatedMediaSources(zipFilePath, contentId, contentName, DeviceType.valueOf(playerType), startupPage, contentWidth, contentHeight, lfdSizeExceptMediaFileSizes);
    return (HttpEntity<List<MediaSource>>)ResponseEntity.ok(updatedMediaSources);
  }
  
  @PostMapping(value = {"/zipPrerequisite"}, consumes = {"multipart/form-data"})
  public HttpEntity<List<MediaSource>> requestMediaSource(@RequestParam(defaultValue = "") String contentId, @RequestParam String contentName, @RequestParam String startupPage, @RequestParam String playerType, @RequestParam String contentWidth, @RequestParam String contentHeight, @RequestParam int lfdSizeExceptMediaFileSizes, @RequestParam("upload") MultipartFile zip) throws FileItemValidationException, WebContentValidationException, IOException {
    this.webContentValidationService.validateWebContentInZip(zip, startupPage);
    List<MediaSource> updatedMediaSources = this.webContentUploadService.getUpdatedMediaSources(zip, contentId, contentName, DeviceType.valueOf(playerType), startupPage, contentWidth, contentHeight, lfdSizeExceptMediaFileSizes);
    return (HttpEntity<List<MediaSource>>)ResponseEntity.ok(updatedMediaSources);
  }
  
  @PostMapping({"/filePrerequisite"})
  public HttpEntity<CidMappingResponse> initializeUploadProcess(@RequestParam String contentId, @RequestParam String contentName, @RequestParam String startupPage) throws IOException {
    return (HttpEntity<CidMappingResponse>)ResponseEntity.ok(new CidMappingResponse(this.webContentUploadService.initializeFileUploadProcess(contentId, contentName, startupPage)));
  }
  
  @PostMapping(value = {"/supportfile"}, consumes = {"multipart/form-data"})
  public HttpEntity<MediaSource> uploadfile(@RequestParam(required = true) String contentId, @RequestParam(required = true) String path, @RequestParam("upload") MultipartFile file) throws IOException {
    return (HttpEntity<MediaSource>)ResponseEntity.ok(this.webContentUploadService.storeSupportFileItem(contentId, path, file));
  }
  
  @PostMapping({"/supportfileFromImportFile"})
  public HttpEntity<MediaSource> uploadfile(@RequestParam(required = true) String contentId, @RequestParam(required = true) String relativePath, @RequestParam(required = true) String importedFilePath) throws IOException {
    return (HttpEntity<MediaSource>)ResponseEntity.ok(this.webContentUploadService.storeSupportFileItem(contentId, relativePath, importedFilePath));
  }
  
  @PostMapping({"/fileItems"})
  public HttpEntity<List<MediaSource>> requestMediaSource(@RequestBody FileItemsDescriptor fileItemsDescriptor, HttpServletRequest request) throws IOException {
    List<MediaSource> updatedMediaSources = this.webContentUploadService.getUpdatedMediaSources(fileItemsDescriptor);
    return (HttpEntity<List<MediaSource>>)ResponseEntity.ok(updatedMediaSources);
  }
  
  @PostMapping({"/verifyNames"})
  public HttpEntity<String> verifyFileNames(@RequestBody List<String> fileNames, HttpServletRequest request) throws IOException {
    for (String name : fileNames) {
      String result = this.webContentUploadService.validateFileName(name);
      if (!Strings.isNullOrEmpty(result))
        return (HttpEntity<String>)ResponseEntity.badRequest().body(result); 
    } 
    return (HttpEntity<String>)ResponseEntity.ok("All files are valid");
  }
  
  @PostMapping({"/submitLfd"})
  public HttpEntity<UploadResponse> xmlUpload(@RequestParam String lfdXml) throws UploaderException {
    String contentId = this.webContentUploadService.uploadWebContent(lfdXml);
    UploadResponse response = new UploadResponse(HttpStatus.OK.value(), contentId);
    return (HttpEntity<UploadResponse>)ResponseEntity.ok(response);
  }
  
  @GetMapping({"/uploadPreview"})
  public HttpEntity<String> getUploadPreviewUrl(@RequestParam(name = "contentId", required = true) String contentId, @RequestParam(name = "startupPage", required = true) String startupPage) throws DownloaderException {
    ServletUriComponentsBuilder builder = ServletUriComponentsBuilder.fromCurrentServletMapping();
    builder.path("/preview/" + contentId + "/" + startupPage);
    return (HttpEntity<String>)ResponseEntity.ok(builder.build().toString());
  }
  
  @GetMapping({"/contentPreview"})
  public HttpEntity<HtmlPreviewResponse> getContentPreviewUrl(@RequestParam(name = "contentId", required = true) String contentId, @RequestParam(name = "startupPage", required = false) String startupPage) throws DownloaderException {
    ServletUriComponentsBuilder builder = ServletUriComponentsBuilder.fromCurrentServletMapping();
    HtmlPreviewResponse result = this.webContentService.getPreviewInfo(contentId);
    builder.path("/preview/" + contentId + "/" + result.getUri());
    result.setUri((result.getProgress() == 100) ? builder.build().toString() : null);
    return (HttpEntity<HtmlPreviewResponse>)ResponseEntity.ok(result);
  }
  
  @GetMapping({"/contentPreviewCache"})
  public HttpEntity<List<PreviewUsageResponse>> getContentPreviewCache() {
    return (HttpEntity<List<PreviewUsageResponse>>)ResponseEntity.ok(this.webContentService.getPreviewUsages());
  }
  
  @ExceptionHandler({WebAuthorAbstractException.class})
  public ResponseEntity<UploadResponse> webAuthorExceptionHandler(WebAuthorAbstractException ex) {
    logger.error(ex.getMessage(), (Throwable)ex);
    UploadResponse uploadResponse = new UploadResponse(ex.getErrorCode(), ex.getMessage());
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(uploadResponse);
  }
  
  @ExceptionHandler({Exception.class})
  public ResponseEntity<UploadResponse> generalExceptionHandler(Exception ex) {
    logger.error(ex.getMessage(), ex);
    UploadResponse uploadResponse = new UploadResponse(HttpStatus.INTERNAL_SERVER_ERROR.value(), ex.getMessage());
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(uploadResponse);
  }
}
