package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.properties.MagicInfoProperties;
import java.io.File;
import java.io.FileNotFoundException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class ContentFileRepositoryImpl implements ContentFileRepository {
  private MagicInfoProperties magicInfoProperties;
  
  @Autowired
  public ContentFileRepositoryImpl(MagicInfoProperties magicInfoProperties) {
    this.magicInfoProperties = magicInfoProperties;
  }
  
  public File getContentFile(String fileId, String fileName) throws FileNotFoundException {
    Path contentFilePath = Paths.get(this.magicInfoProperties.getMagicInfoContentsLocationPath().toString(), new String[] { fileId, fileName });
    if (Files.notExists(contentFilePath, new java.nio.file.LinkOption[0]))
      throw new FileNotFoundException("Cannot found file with id=" + fileId + " name=" + fileName); 
    return contentFilePath.toFile();
  }
}
