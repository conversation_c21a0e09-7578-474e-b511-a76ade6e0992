package com.samsung.magicinfo.webauthor2.repository.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;

@XmlAccessorType(XmlAccessType.FIELD)
public class DatalinkServerEntityData implements Serializable {
  @XmlElement(name = "server_name")
  private String serverName;
  
  @XmlElement(name = "ip_address")
  private String ipAddress;
  
  @XmlElement
  private Integer port;
  
  @XmlElement
  private Integer period;
  
  @XmlElement(name = "use_ssl")
  private Boolean useSsl;
  
  @XmlElement(name = "ftp_port")
  private Integer ftpPort;
  
  @XmlElement
  private Boolean bypass;
  
  @XmlElement(name = "private_ip_address")
  private String privateIpAddress;
  
  @XmlElement(name = "private_web_port")
  private Integer privateWebPort;
  
  @XmlElement(name = "private_mode")
  private Boolean privateMode;
  
  @XmlElementWrapper(name = "table")
  @XmlElement(name = "com.samsung.magicinfo.framework.setup.entity.DatalinkServerTableEntity")
  private List<DatalinkTableEntityData> tableList = new ArrayList<>();
  
  public String getServerName() {
    return this.serverName;
  }
  
  public void setServerName(String serverName) {
    this.serverName = serverName;
  }
  
  public String getIpAddress() {
    return this.ipAddress;
  }
  
  public void setIpAddress(String ipAddress) {
    this.ipAddress = ipAddress;
  }
  
  public Integer getPort() {
    return this.port;
  }
  
  public void setPort(Integer port) {
    this.port = port;
  }
  
  public Integer getPeriod() {
    return this.period;
  }
  
  public void setPeriod(Integer period) {
    this.period = period;
  }
  
  public Boolean getUseSsl() {
    return this.useSsl;
  }
  
  public void setUseSsl(Boolean useSsl) {
    this.useSsl = useSsl;
  }
  
  public Integer getFtpPort() {
    return this.ftpPort;
  }
  
  public void setFtpPort(Integer ftpPort) {
    this.ftpPort = ftpPort;
  }
  
  public Boolean getBypass() {
    return this.bypass;
  }
  
  public void setBypass(Boolean bypass) {
    this.bypass = bypass;
  }
  
  public String getPrivateIpAddress() {
    return this.privateIpAddress;
  }
  
  public void setPrivateIpAddress(String privateIpAddress) {
    this.privateIpAddress = privateIpAddress;
  }
  
  public Integer getPrivateWebPort() {
    return this.privateWebPort;
  }
  
  public void setPrivateWebPort(Integer privateWebPort) {
    this.privateWebPort = privateWebPort;
  }
  
  public Boolean getPrivateMode() {
    return this.privateMode;
  }
  
  public void setPrivateMode(Boolean privateMode) {
    this.privateMode = privateMode;
  }
  
  public List<DatalinkTableEntityData> getTableList() {
    return this.tableList;
  }
}
