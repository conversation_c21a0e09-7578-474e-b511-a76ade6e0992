package com.samsung.magicinfo.webauthor2.webapi.assembler;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.webapi.controller.ContentQueryController;
import com.samsung.magicinfo.webauthor2.webapi.controller.ContentThumbnailController;
import com.samsung.magicinfo.webauthor2.webapi.controller.DataLinkQueryController;
import com.samsung.magicinfo.webauthor2.webapi.controller.LFDContentQueryController;
import com.samsung.magicinfo.webauthor2.webapi.controller.VWLContentQueryController;
import com.samsung.magicinfo.webauthor2.webapi.resource.ContentResource;
import java.io.IOException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.hateoas.Link;
import org.springframework.hateoas.ResourceSupport;
import org.springframework.hateoas.mvc.BasicLinkBuilder;
import org.springframework.hateoas.mvc.ControllerLinkBuilder;
import org.springframework.hateoas.mvc.ResourceAssemblerSupport;
import org.springframework.stereotype.Component;

@Component
public class ContentResourceAssembler extends ResourceAssemblerSupport<Content, ContentResource> {
  private static final Logger logger = LoggerFactory.getLogger(ContentResourceAssembler.class);
  
  public ContentResourceAssembler() {
    super(ContentQueryController.class, ContentResource.class);
  }
  
  public ContentResourceAssembler(Class<?> controllerClass, Class<ContentResource> resourceType) {
    super(controllerClass, resourceType);
  }
  
  public ContentResource toResource(Content content) {
    ContentResource contentResource = new ContentResource(content, new Link[0]);
    addThumbnailUrlLink(content, contentResource);
    addMainUrlLink(content, contentResource);
    addLfdContentLink(content, contentResource);
    addVwlContentLink(content, contentResource);
    addModelUrlLink(content, contentResource);
    return contentResource;
  }
  
  private void addThumbnailUrlLink(Content content, ContentResource contentResource) {
    if (content.getThumbnailName() != null)
      if (content.getFileId().equals(content.getThumbnailId())) {
        Link thumbnailUrl = ((BasicLinkBuilder)((BasicLinkBuilder)((BasicLinkBuilder)BasicLinkBuilder.linkToCurrentMapping().slash("content")).slash(content.getThumbnailId())).slash(content.getThumbnailName())).withRel("thumbnailUrl");
        contentResource.add(thumbnailUrl);
      } else {
        try {
          Link thumbnailUrl = ((ControllerLinkBuilder)((ControllerLinkBuilder)ControllerLinkBuilder.linkTo(((ContentThumbnailController)ControllerLinkBuilder.methodOn(ContentThumbnailController.class, new Object[0])).getFileThumbnail(content.getThumbnailId(), content.getThumbnailName())).slash(content.getThumbnailId())).slash(content.getThumbnailName())).withRel("thumbnailUrl");
          contentResource.add(thumbnailUrl);
        } catch (IOException e) {
          logger.error("Error: ", e);
        } 
      }  
  }
  
  private void addMainUrlLink(Content content, ContentResource contentResource) {
    contentResource.add(((BasicLinkBuilder)((BasicLinkBuilder)((BasicLinkBuilder)BasicLinkBuilder.linkToCurrentMapping().slash("content"))
        .slash(content.getFileId())).slash(content.getFileName())).withRel("mainUrl"));
  }
  
  private void addLfdContentLink(Content content, ContentResource contentResource) {
    if (content.getType() == MediaType.LFD || content.getType() == MediaType.LFT || content
      .getType() == MediaType.TLFD) {
      if (Strings.isNullOrEmpty(content.getId()))
        throw new IllegalStateException("Content id cannot be null or empty!"); 
      contentResource.add(ControllerLinkBuilder.linkTo(((LFDContentQueryController)ControllerLinkBuilder.methodOn(LFDContentQueryController.class, new Object[0])).getLFDContent(content.getId()))
          .withRel("lfdContent"));
    } 
  }
  
  private void addVwlContentLink(Content content, ContentResource contentResource) {
    if (content.getType() == MediaType.VWL) {
      if (Strings.isNullOrEmpty(content.getId()))
        throw new IllegalStateException("Content id cannot be null or empty!"); 
      contentResource.add(ControllerLinkBuilder.linkTo(((VWLContentQueryController)ControllerLinkBuilder.methodOn(VWLContentQueryController.class, new Object[0])).getVWLContent(content.getId()))
          .withRel("vwlContent"));
    } 
  }
  
  private void addModelUrlLink(Content content, ContentResource contentResource) {
    if (content.getType() == MediaType.DLK) {
      if (Strings.isNullOrEmpty(content.getId()))
        throw new IllegalStateException("Content id cannot be null or empty!"); 
      contentResource.add(ControllerLinkBuilder.linkTo(((DataLinkQueryController)ControllerLinkBuilder.methodOn(DataLinkQueryController.class, new Object[0])).getDataLinkDescriptor(content.getId()))
          .withRel("modelUrl"));
    } 
  }
}
