package com.samsung.magicinfo.framework.device.deviceInfo.dao;

import com.samsung.magicinfo.framework.device.deviceInfo.entity.LedCabinet;
import java.sql.SQLException;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface LedCabinetConfDaoMapper {
   LedCabinet getLedCabinet(@Param("parentDeviceId") String var1, @Param("cabinetGroupId") Long var2, @Param("cabinetId") Long var3) throws SQLException;

   boolean addLedCabinet(@Param("ledCabinet") LedCabinet var1) throws SQLException;

   boolean setLedCabinetPowerOff(@Param("parentDeviceId") String var1, @Param("cabinetGroupId") Long var2, @Param("cabinetId") Long var3) throws SQLException;

   boolean setLedCabinet(@Param("info") LedCabinet var1) throws SQLException;

   int getLedCabinetListCount(@Param("parentDeviceId") String var1, @Param("cabinetGroupId") Long var2) throws SQLException;

   List getLedCabinetList(@Param("parentDeviceId") String var1, @Param("cabinetGroupId") Long var2, @Param("dir") String var3, @Param("src") String var4, @Param("sort") String var5, @Param("startPos") int var6, @Param("pageSize") int var7) throws SQLException;

   List getLedCabinetGroupIds(@Param("parentDeviceId") String var1) throws SQLException;

   List getLedCabinetGroupLayoutInfo(@Param("parentDeviceId") String var1) throws SQLException;

   List getLedCabinetListByIdList(@Param("parentDeviceId") String var1, @Param("childIds") List var2) throws SQLException;

   boolean deleteLedCabinets(@Param("parentDeviceId") String var1) throws SQLException;

   boolean deleteLedCabinet(@Param("parentDeviceId") String var1, @Param("cabinetGroupId") Long var2, @Param("cabinetId") Long var3) throws SQLException;

   boolean addLedCabinetGroupInfo(@Param("parentDeviceId") String var1, @Param("cabinetGroupId") Long var2, @Param("groupResolution") String var3, @Param("positionX") Long var4, @Param("positionY") Long var5) throws SQLException;

   boolean setLedCabinetGroupInfo(@Param("parentDeviceId") String var1, @Param("cabinetGroupId") Long var2, @Param("groupResolution") String var3, @Param("positionX") Long var4, @Param("positionY") Long var5) throws SQLException;

   int getErrorLedCabinetCntByGroupId(@Param("parentDeviceId") String var1, @Param("groupId") Long var2) throws SQLException;

   int getLedCabinetCountByGroup(@Param("parentDeviceId") String var1, @Param("groupId") Long var2) throws SQLException;
}
