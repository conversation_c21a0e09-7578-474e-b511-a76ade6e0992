package com.samsung.magicinfo.webauthor2.repository.model;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "response")
public class ResponseFileInfoListData implements Serializable {
  private static final long serialVersionUID = 7919616444256409736L;
  
  @XmlAttribute
  private String code;
  
  @XmlElement
  private String errorMessage;
  
  @XmlElement
  private FileInfoListData responseClass;
  
  public String getCode() {
    return this.code;
  }
  
  public String getErrorMessage() {
    return this.errorMessage;
  }
  
  public FileInfoListData getResponseClass() {
    return this.responseClass;
  }
}
