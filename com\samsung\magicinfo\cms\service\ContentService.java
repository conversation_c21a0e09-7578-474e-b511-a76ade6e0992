package com.samsung.magicinfo.cms.service;

import com.samsung.common.model.ResponseBody;
import com.samsung.magicinfo.cms.model.ContentApproveResource;
import com.samsung.magicinfo.cms.model.ContentFilter;
import com.samsung.magicinfo.cms.model.UrlContentSettingResource;
import java.sql.SQLException;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.multipart.MultipartFile;

public interface ContentService {
   ResponseBody getDashboardContentInfo() throws SQLException;

   ResponseBody getContentInfoList(String[] var1) throws SQLException;

   ResponseBody getActiveContentInfo(String var1) throws SQLException;

   ResponseBody listContent(ContentFilter var1) throws Exception;

   ResponseBody listContentFile(ContentFilter var1) throws Exception;

   ResponseBody listAllContent(int var1, int var2) throws Exception;

   ResponseBody updateContentFile(String var1, HttpServletRequest var2) throws SQLException;

   ResponseBody deleteContent(String var1) throws SQLException;

   ResponseBody forceDeleteContent(String var1) throws SQLException;

   ResponseBody approveContents(ContentApproveResource var1) throws SQLException;

   ResponseBody uploadContent(String var1, HttpServletRequest var2) throws SQLException;

   ResponseBody getContentFileInfo(String var1) throws SQLException;

   ResponseBody renameContent(String var1, String var2) throws SQLException;

   ResponseBody getTagMapping(String[] var1) throws SQLException;

   ResponseBody assignTags(String var1, List var2) throws SQLException;

   UrlContentSettingResource createUrlContent(UrlContentSettingResource var1) throws Exception;

   List uploadContentFile(MultipartFile var1, String var2, String var3, String var4, String var5, String var6, String var7, String var8, HttpServletRequest var9) throws Exception;
}
