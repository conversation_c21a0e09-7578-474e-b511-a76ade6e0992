<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.samsung.magicinfo.framework.setup.dao.SlmLicenseDaoMapper">

    <insert id="addSlmLicenseInfo">
        INSERT INTO MI_SYSTEM_INFO_SLM_LICENSE
        (LICENSE_KEY, ACTIVATION_KEY, PRODUCT_CODE, PRODUCT_NAME, LICENSE_TYPE, MAX_CLIENTS, START_DATE, END_DATE,
        REG_DATE, VALID, MAINT_END_DATE)
        VALUES
        (#{license.license_key}, #{license.activation_key}, #{license.product_code},
        #{license.product_name}, #{license.license_type}, #{license.max_clients},
        #{license.start_date}, #{license.end_date}, #{reg_date},<include refid="utils.false"/>,
        #{license.maint_end_date})
    </insert>

    <insert id="addSlmLicenseInfoForE2E">
	 	INSERT INTO MI_SYSTEM_INFO_SLM_LICENSE_E2E VALUES ( #{deviceId}, #{license.license_key},#{license.issued_no}, #{license.activation_key}, #{license.start_date}, #{license.end_date}, #{reg_date})
	</insert>

    <insert id="addLicenseStatus">
		INSERT INTO MI_SYSTEM_INFO_SLM_STATUS VALUES (#{status.product_code}, #{status.expired_key}, #{status.reg_date})
	</insert>

    <delete id="deleteLicenseInfo">
		DELETE FROM MI_SYSTEM_INFO_SLM_LICENSE WHERE LICENSE_KEY = #{license_key}
	</delete>

    <delete id="deleteMappingInfoOfSlmLicenseOrg">
		DELETE FROM MI_SYSTEM_MAP_SLM_LICENSE_ORG WHERE PRODUCT_CODE = #{productCode}
	</delete>

    <delete id="deleteLicenseInfoForE2EByDeviceId">
		DELETE FROM MI_SYSTEM_INFO_SLM_LICENSE_E2E WHERE DEVICE_ID = #{deviceId}
	</delete>

    <insert id="setSlmLicenseHistory">
	    INSERT INTO MI_SYSTEM_INFO_SLM_HISTORY VALUES (#{licenseHistory.type}, #{licenseHistory.licensekey},
	    	#{licenseHistory.description}, #{reg_date}, #{licenseHistory.confirm})
	</insert>


    <delete id="deleteSlmLicenseHistory">
		DELETE FROM MI_SYSTEM_INFO_SLM_HISTORY WHERE LICENSEKEY = #{license_key} AND CONFIRM = #{confirm}
	</delete>

    <update id="updateSlmLicenseInfo">
        UPDATE MI_SYSTEM_INFO_SLM_LICENSE
        SET ACTIVATION_KEY = #{license.activation_key}, PRODUCT_CODE = #{license.product_code},
        PRODUCT_NAME = #{license.product_name}, LICENSE_TYPE = #{license.license_type} ,
        MAX_CLIENTS = #{license.max_clients}, START_DATE = #{license.start_date},
        END_DATE = #{license.end_date}, REG_DATE =<include refid="utils.currentTimestamp"/>,
        MAINT_END_DATE = #{license.maint_end_date}
        WHERE LICENSE_KEY = #{license.license_key}
    </update>

    <update id="updateSlmLicenseInfoForE2E">
        UPDATE MI_SYSTEM_INFO_SLM_LICENSE_E2E
        SET ACTIVATION_KEY = #{license.activation_key}, START_DATE = #{license.start_date},
        END_DATE = #{license.end_date}, REG_DATE =
        <include refid="utils.currentTimestamp"/>
        WHERE LICENSE_KEY = #{license.license_key}
    </update>

    <update id="extendSlmLicenseInfoForE2E">
		UPDATE MI_SYSTEM_INFO_SLM_LICENSE_E2E
		SET ACTIVATION_KEY = #{license.activation_key}, START_DATE = #{license.start_date},
		END_DATE = #{license.end_date}
		WHERE LICENSE_KEY = #{license.license_key}
	</update>

    <select id="getActivationKey" resultType="java.lang.String">
		SELECT ACTIVATION_KEY FROM MI_SYSTEM_INFO_SLM_LICENSE WHERE LICENSE_KEY = #{licenseKey}
	</select>

    <delete id="deleteAllLicense">
		DELETE FROM MI_SYSTEM_INFO_SLM_LICENSE
	</delete>

    <select id="getLicenseList" resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseEntity">
        SELECT * FROM MI_SYSTEM_INFO_SLM_LICENSE
        <if test="sort != null and dir != null">
            <bind name="safe_columnName" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(sort)"/>
            <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(dir)"/>
            ORDER BY ${safe_columnName} ${safe_sortOrder}
        </if>
    </select>

    <select id="getLicenseListCount" resultType="int">
		SELECT COUNT(ACTIVATION_KEY) FROM MI_SYSTEM_INFO_SLM_LICENSE
	</select>

    <select id="getLicenseListForE2E" resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseEntity">
        SELECT A.*, B.DEVICE_NAME FROM MI_SYSTEM_INFO_SLM_LICENSE_E2E A, MI_DMS_INFO_DEVICE B WHERE A.DEVICE_ID =
        B.DEVICE_ID

        <if test="searchText != null and searchText.length() > 0">
            <bind name="searchText" value="'%' + searchText + '%'"/>
            AND (UPPER(B.DEVICE_ID) LIKE #{searchText} ESCAPE '^' OR UPPER(B.DEVICE_NAME) LIKE #{searchText} ESCAPE '^')
        </if>

        ORDER BY END_DATE ASC

    </select>

    <select id="getLicenseListCountForE2E" resultType="int">
		SELECT COUNT(ACTIVATION_KEY) FROM MI_SYSTEM_INFO_SLM_LICENSE_E2E
	</select>

    <select id="getLicenseFromProductAndType" resultType="int">
		SELECT COUNT(ACTIVATION_KEY) FROM MI_SYSTEM_INFO_SLM_LICENSE
		WHERE LICENSE_TYPE = #{license_type} AND PRODUCT_CODE = #{productCode}
	</select>

    <select id="getSlmLicense" resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseEntity">
		SELECT * FROM MI_SYSTEM_INFO_SLM_LICENSE WHERE LICENSE_KEY = #{licenseKey}
	</select>

    <select id="getSlmLicenseForE2E" resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseEntity">
		SELECT * FROM MI_SYSTEM_INFO_SLM_LICENSE_E2E WHERE LICENSE_KEY = #{licenseKey}
	</select>

    <select id="getSlmLicenseForE2EByDeviceId"
            resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseEntity">
		SELECT * FROM MI_SYSTEM_INFO_SLM_LICENSE_E2E WHERE DEVICE_ID = #{deviceId}
	</select>

    <select id="getAllSlmlLicense" resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseEntity">
		SELECT * FROM MI_SYSTEM_INFO_SLM_LICENSE
	</select>

    <select id="getAllSlmlLicenseForE2E" resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseEntity">
		SELECT * FROM MI_SYSTEM_INFO_SLM_LICENSE_E2E
	</select>

    <select id="getProductCodeSlmLicense" resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseEntity">
		SELECT * FROM MI_SYSTEM_INFO_SLM_LICENSE WHERE PRODUCT_CODE = #{productCode}
	</select>

    <select id="getCntSlmLicenseByProductCode" resultType="int">
		SELECT COALESCE(SUM(MAX_CLIENTS),0) FROM MI_SYSTEM_INFO_SLM_LICENSE WHERE PRODUCT_CODE = #{productCode}
	</select>

    <select id="getCntSlmLicenseByProductCodeForAssigningLicenseEachOrganization" resultType="int">
		SELECT COALESCE(SUM(MAX_CLIENTS),0) FROM MI_SYSTEM_INFO_SLM_LICENSE WHERE PRODUCT_CODE = #{productCode} AND
        <foreach item="licenseType" collection="licenseTypeList" separator=" OR ">
            LICENSE_TYPE = #{licenseType}
        </foreach>
	</select>

    <select id="getCntSlmLicensebyProductCodeList"
            resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseEntity">
        SELECT MAX_CLIENTS FROM MI_SYSTEM_INFO_SLM_LICENSE
        <if test="productCodeList != null and productCodeList.size() > 0">
            WHERE
            <foreach item="productCode" collection="productCodeList" separator=" OR ">
                PRODUCT_CODE = #{productCode}
            </foreach>
        </if>

    </select>

    <select id="getCntSlmLicensebyProductCodeAndLicenseType"
            resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseEntity">
		SELECT MAX_CLIENTS FROM MI_SYSTEM_INFO_SLM_LICENSE WHERE PRODUCT_CODE = #{productCode} AND LICENSE_TYPE = #{licenseType}
	</select>

    <insert id="addDeviceSamples">
        <bind name="safe_num" value="@com.samsung.common.utils.DaoTools@safeNumeric(num)"/>
        insert into mi_dms_info_device (device_id, device_model_code, device_model_name, device_name, serial_decimal,
        screen_size, firmware_version, application_version, rule_version,
        os_image_version, cpu_type, hdd_size, mem_size, video_adapter, video_memory,
        video_driver, network_adapter, network_driver, mac_address, ip_setting_type,
        ip_address, subnet_mask, gateway, dns_server_main, dns_server_sub, port,
        trigger_interval, monitoring_interval, last_connection_time, location,
        is_approved, tunneling_server, bg_color, auto_time_setting, on_timer_setting,
        off_timer_setting, time_zone_index, day_light_saving, ftp_connect_mode,
        repository_path, magicinfo_server_url, screen_capture_interval, ewf_state,
        resolution, device_type, is_reverse, proxy_setting, connection_limit_time,
        mnt_folder_path, system_restart_interval, log_mnt, proof_of_play_mnt,
        content_mnt, screen_rotation, play_mode, reset_password, time_zone_version,
        disk_space_repository, latitude, longitude, altitude, creator_id, create_date,
        auto_ip_set, auto_computer_name_set, computer_name, vnc_password,
        use_mpplayer, direct_channel)
        values (#{mac}, '59', '460EXn', 'i_${safe_num}', null, null, null, 'V1.0 Build NA-MIIPP-1006.5', null, null,
        'AMD Turion(tm) II Dual-Core Mobile M520', 39061980, 784356, null, null, null, null, null,
        '00-00-5a-00-02-6a', null, '************', '*************', null, null, null, 6055, null, null,
        '2011-07-01 12:53:00', '',<include refid="utils.false"/>, '************', null,<include refid="utils.false"/>,
        <include refid="utils.false"/>,<include refid="utils.false"/>, null,<include refid="utils.false"/>,
        null, null, null, null,<include refid="utils.true"/>, null, 'iPLAYER',<include refid="utils.true"/>, null, null,
        null, null, null, null, null,
        null, null, null, null, 24921242612, '', '', '', null, '2011-06-15 16:05:00',<include refid="utils.false"/>,
        <include refid="utils.false"/>, null, null,<include refid="utils.true"/>, 'N')
    </insert>

    <insert id="addDeviceSamplesSql2">
		INSERT INTO mi_dms_map_group_device(group_id, device_id) VALUES(999999, #{mac})
	</insert>

    <insert id="addSocDeviceSamples">
        <bind name="safe_num" value="@com.samsung.common.utils.DaoTools@safeNumeric(num)"/>
        insert into mi_dms_info_device (device_id, device_model_code, device_model_name, device_name, serial_decimal,
        screen_size, firmware_version, application_version, rule_version,
        os_image_version, cpu_type, hdd_size, mem_size, video_adapter, video_memory,
        video_driver, network_adapter, network_driver, mac_address, ip_setting_type,
        ip_address, subnet_mask, gateway, dns_server_main, dns_server_sub, port,
        trigger_interval, monitoring_interval, last_connection_time, location,
        is_approved, tunneling_server, bg_color, auto_time_setting, on_timer_setting,
        off_timer_setting, time_zone_index, day_light_saving, ftp_connect_mode,
        repository_path, magicinfo_server_url, screen_capture_interval, ewf_state,
        resolution, device_type, is_reverse, proxy_setting, connection_limit_time,
        mnt_folder_path, system_restart_interval, log_mnt, proof_of_play_mnt,
        content_mnt, screen_rotation, play_mode, reset_password, time_zone_version,
        disk_space_repository, latitude, longitude, altitude, creator_id, create_date,
        auto_ip_set, auto_computer_name_set, computer_name,
        vnc_password, use_mpplayer, direct_channel)
        values (#{mac}, '59', '460EXn', 's_${safe_num}', null, null, null, 'V1.0 Build NA-MIIPP-1006.5', null, null,
        'AMD Turion(tm) II Dual-Core Mobile M520', 39061980, 784356, null, null, null, null, null,
        '00-00-5a-00-02-6a', null, '************', '*************', null, null, null, 6055, null, null,
        '2011-07-01 12:53:00', '',<include refid="utils.false"/>, '************', null,<include refid="utils.false"/>,
        <include refid="utils.false"/>,<include refid="utils.false"/>, null,<include refid="utils.false"/>,
        null, null, null, null,<include refid="utils.true"/>, null, 'SPLAYER',<include refid="utils.true"/>, null, null,
        null, null, null, null, null, null,
        null, null, null, 24921242612, '', '', '', null, '2011-06-15 16:05:00',<include refid="utils.false"/>,<include
            refid="utils.false"/>,
        null, null,<include refid="utils.true"/>, 'N')
    </insert>

    <insert id="addSocDeviceSamplesSql2">
		INSERT INTO mi_dms_map_group_device(group_id, device_id) VALUES(999999, #{mac})
	</insert>

    <insert id="addLiteDeviceSamples">
        <bind name="safe_num" value="@com.samsung.common.utils.DaoTools@safeNumeric(num)"/>
        insert into mi_dms_info_lite_device (device_id, device_model_code, device_model_name, device_name,
        serial_decimal, screen_size, firmware_version, application_version,
        rule_version, os_image_version, cpu_type, hdd_size, mem_size,
        video_adapter, video_memory, video_driver, network_adapter,
        network_driver, mac_address, ip_setting_type, ip_address,
        subnet_mask, gateway, dns_server_main, dns_server_sub, port,
        trigger_interval, monitoring_interval, last_connection_time,
        location, is_approved, tunneling_server, bg_color,
        auto_time_setting, on_timer_setting, off_timer_setting,
        time_zone_index, day_light_saving, ftp_connect_mode,
        repository_path, magicinfo_server_url, screen_capture_interval,
        ewf_state, resolution, device_type, is_reverse, proxy_setting,
        connection_limit_time, mnt_folder_path, system_restart_interval,
        log_mnt, proof_of_play_mnt, content_mnt, screen_rotation,
        play_mode, reset_password, time_zone_version, disk_space_repository,
        creator_id, create_date, auto_ip_set, auto_computer_name_set,
        computer_name, vnc_password, use_mpplayer, direct_channel,
        mdc_update_time, cpu_usage, ram_usage, network_usage,
        disk_space_usage, disk_space_available, system_time)
        values (#{mac}, '68', 'LITE_ME40A', 'l_${safe_num}', #{mac}, '52', 'A-VNUSCDSP-1005.8', 'T-GAPLBAKUC-0804',
        '0.1.1',
        'Linux;********;', 'ARMv7 Processor rev 2 (v7l)', 39061980, 784356, 'Unknown', '200', 'Unknown',
        'No description', 'Unknown', '00-00-5a-00-02-6a', 'DHCP', '*************', '*************',
        null, null, null, 6055, null, null, '2011-07-01 12:53:00', '0.0.0.0',<include refid="utils.false"/>,
        '************', null,
        <include refid="utils.false"/>,<include refid="utils.false"/>,<include refid="utils.false"/>, '55',<include
            refid="utils.false"/>, 'ACTIVE', '/mtd_rwcommon/magicinfo/repository', null, 0,
        <include refid="utils.true"/>, '1920*1080', 'LFD',<include refid="utils.true"/>, '0;;', 5,
        '/mtd_rwcommon/magicinfo/data', 'Sun 00:00', '0;30;50',
        '30;50', '365;200', 0, 0, 0, 0, 24921242612, null, '2011-06-15 16:05:00',<include refid="utils.false"/>,<include
            refid="utils.false"/>, null, null,
        <include refid="utils.true"/>, null, '2012-01-06 03:10:31', '4', '40', '50', 'Internal:760268;',
        'Internal:159348;','2012-01-06 03:10:31')
    </insert>

    <insert id="addLiteDeviceSamplesSql2">
		INSERT INTO mi_dms_map_lite_group_device(group_id, device_id) VALUES(999999, #{mac})
	</insert>

    <insert id="TestAddLicenseKey">
        INSERT INTO MI_SYSTEM_INFO_SLM_LICENSE (ACTIVATION_KEY, LICENSE_KEY, PRODUCT_CODE, PRODUCT_NAME, LICENSE_TYPE,
        MAX_CLIENTS, START_DATE, END_DATE, REG_DATE)
        VALUES(#{license.activation_key}, #{license.license_key}, #{license.product_code}, #{license.product_name},
        #{license.license_type}, #{license.max_clients},
        <include refid="utils.currentTimestamp"/>,<include refid="utils.currentTimestamp"/>,<include
            refid="utils.currentTimestamp"/>)
    </insert>

    <select id="getHwUniqueKey" resultType="map">
		SELECT * FROM MI_SYSTEM_INFO_SLM_HWUNIQUEKEY LIMIT 1
	</select>

    <select id="getHwUniqueKey" resultType="map" databaseId="mssql">
		SELECT TOP 1 * FROM MI_SYSTEM_INFO_SLM_HWUNIQUEKEY
	</select>

    <insert id="setHwUniqueKey">
		INSERT INTO MI_SYSTEM_INFO_SLM_HWUNIQUEKEY (HWUNIQUE_KEY) VALUES( #{hwuniqueKey} )
	</insert>

    <select id="getAllSlmLicenseHistory"
            resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseHistoryEntity">
		SELECT * FROM MI_SYSTEM_INFO_SLM_HISTORY WHERE TYPE !=  '02' AND LICENSEKEY != 'HW KEY'
	</select>

    <select id="getAllSlmLicenseHistoryByPageNumber"
            resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseHistoryEntity">
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(offset)"/>
        <bind name="safe_limit" value="@com.samsung.common.utils.DaoTools@safeNumeric(limit)"/>
        SELECT * FROM MI_SYSTEM_INFO_SLM_HISTORY
        WHERE TYPE != '02' ORDER BY DATE DESC
        LIMIT ${safe_limit} OFFSET ${safe_startPos}
    </select>

    <select id="getAllSlmLicenseHistoryByPageNumber"
            resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseHistoryEntity" databaseId="mssql">
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(offset)"/>
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(offset + limit)"/>
        SELECT * FROM (
        SELECT
        *,
        ROW_NUMBER() OVER(ORDER BY DATE DESC) as RowNum
        FROM MI_SYSTEM_INFO_SLM_HISTORY
        WHERE TYPE != '02'
        ) as SubQuery
        WHERE RowNum > ${safe_startPos} and RowNum &lt;= ${safe_rownumLimit}
        ORDER BY RowNum
    </select>

    <select id="getCntSlmLicenseHistory" resultType="int">
		SELECT COUNT(licensekey)
		FROM MI_SYSTEM_INFO_SLM_HISTORY
		WHERE type != '00'
	</select>

    <select id="getSlmLicenseHistory" resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseHistoryEntity">
		SELECT * FROM MI_SYSTEM_INFO_SLM_HISTORY WHERE LICENSEKEY = #{licenseKey} AND TYPE='00'
	</select>

    <select id="getCntAllSlmLicenseHistoryForE2E" resultType="int">
		SELECT COUNT(*)
		FROM MI_SYSTEM_INFO_SLM_HISTORY_E2E
	</select>

    <select id="getAllSlmLicenseHistoryForE2E"
            resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseHistoryEntity">
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(offset)"/>
        <bind name="safe_limit" value="@com.samsung.common.utils.DaoTools@safeNumeric(limit)"/>
        SELECT * FROM MI_SYSTEM_INFO_SLM_HISTORY_E2E
        ORDER BY DATE DESC
        LIMIT ${safe_limit} OFFSET ${safe_startPos}
    </select>

    <select id="getAllSlmLicenseHistoryForE2E"
            resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseHistoryEntity" databaseId="mssql">
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(offset)"/>
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(offset + limit)"/>
        SELECT * FROM (
        SELECT
        *,
        ROW_NUMBER() OVER(ORDER BY DATE DESC) as RowNum
        FROM MI_SYSTEM_INFO_SLM_HISTORY_E2E
        ) as SubQuery
        WHERE RowNum > ${safe_startPos} and RowNum &lt;= ${safe_rownumLimit}
        ORDER BY RowNum
    </select>

    <select id="getCntSlmLicenseHistoryForE2EByDeviceId" resultType="int">
		SELECT COUNT(*)
		FROM MI_SYSTEM_INFO_SLM_HISTORY_E2E
		WHERE DEVICE_ID = #{deviceId}
	</select>


    <select id="getSlmLicenseHistoryForE2EByDeviceId"
            resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseHistoryEntity">
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(offset)"/>
        <bind name="safe_limit" value="@com.samsung.common.utils.DaoTools@safeNumeric(limit)"/>
        SELECT * FROM MI_SYSTEM_INFO_SLM_HISTORY_E2E WHERE DEVICE_ID = #{deviceId} ORDER BY DATE DESC
        LIMIT ${safe_limit} OFFSET ${safe_startPos}
    </select>

    <select id="getSlmLicenseHistoryForE2EByDeviceId"
            resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseHistoryEntity" databaseId="mssql">
        SELECT * FROM MI_SYSTEM_INFO_SLM_HISTORY_E2E
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(offset)"/>
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(offset + limit)"/>
        SELECT * FROM (
        SELECT *, ROW_NUMBER() OVER(ORDER BY DATE DESC) as RowNum
        FROM MI_SYSTEM_INFO_SLM_HISTORY_E2E WHERE DEVICE_ID = #{deviceId}
        ) as SubQuery
        WHERE RowNum > ${safe_startPos} and RowNum &lt;= ${safe_rownumLimit}
        ORDER BY RowNum
    </select>

    <select id="getCntSlmlicense" resultType="java.lang.Integer">
        SELECT COUNT(license_key) FROM MI_SYSTEM_INFO_SLM_LICENSE
    </select>

    <update id="setValid">
		UPDATE MI_SYSTEM_INFO_SLM_LICENSE
		SET VALID = #{state}
		WHERE LICENSE_KEY = #{licenseKey}
	</update>

    <select id="getListLicenseStatus" resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseStatusEntity">
		SELECT * FROM MI_SYSTEM_INFO_SLM_STATUS
		ORDER BY REG_DATE
	</select>

    <select id="chkLicenseStatus" resultType="int">
		SELECT COUNT(PRODUCT_CODE) FROM MI_SYSTEM_INFO_SLM_STATUS WHERE PRODUCT_CODE = #{productCode}
	</select>

    <delete id="delLicenseStatus">
		DELETE FROM MI_SYSTEM_INFO_SLM_STATUS WHERE PRODUCT_CODE = #{productCode}
	</delete>

    <select id="getLicenseStaus" resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseStatusEntity">
		SELECT * FROM MI_SYSTEM_INFO_SLM_STATUS
		WHERE PRODUCT_CODE = #{productCode}
	</select>

    <select id="getCompanyInfo" resultType="com.samsung.magicinfo.framework.setup.entity.CompanyInfoEntity">
		SELECT * FROM MI_SYSTEM_INFO_SLM_LICENSE_COMPANY
	</select>

    <delete id="deleteCompanyInfo">
		DELETE FROM MI_SYSTEM_INFO_SLM_LICENSE_COMPANY
	</delete>

    <insert id="insertCompanyInfo">
		INSERT INTO MI_SYSTEM_INFO_SLM_LICENSE_COMPANY  VALUES (
		#{companyInfoEntity.sold_to_code}, #{companyInfoEntity.company_name},
		#{companyInfoEntity.division}, #{companyInfoEntity.address},
		#{companyInfoEntity.phone},  #{companyInfoEntity.email},#{companyInfoEntity.domain_name},#{companyInfoEntity.sec_org_id},#{companyInfoEntity.model_code})
	</insert>

    <update id="setSlmLicenseHistoryForE2E">
	    INSERT INTO MI_SYSTEM_INFO_SLM_HISTORY_E2E VALUES (#{licenseHistory.type},#{licenseHistory.device_id}, #{licenseHistory.licensekey}, #{licenseHistory.issued_no},
	    	#{licenseHistory.description}, #{reg_date})
	</update>
    <update id="updateDeviceTypeListByProductCode">
		UPDATE MI_SYSTEM_INFO_SLM_LICENSE_ORG
		SET DEVICE_TYPE_LIST = #{deviceTypeList}
		WHERE PRODUCT_CODE = #{productCode}
	</update>
    <insert id="addDeviceTypeListByProductCode">
		INSERT INTO MI_SYSTEM_INFO_SLM_LICENSE_ORG (PRODUCT_CODE, PRODUCT_NAME, DEVICE_TYPE_LIST)
			SELECT #{productCode}, #{productName}, #{deviceTypeList}
			WHERE NOT EXISTS ( SELECT 1 FROM MI_SYSTEM_INFO_SLM_LICENSE_ORG WHERE PRODUCT_CODE = #{productCode} )
	</insert>
    <select id="getLicenseInfoAssignedToOrganization"
            resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseOrgEntity" databaseId="mssql">
		SELECT
			DISTINCT AA.GROUP_ID AS ORGANIZATION_ID,
			AA.GROUP_NAME AS ORGANIZATION_NAME,
			AA.PRODUCT_CODE AS PRODUCT_CODE,
			AA.PRODUCT_NAME AS PRODUCT_NAME,
			COALESCE(AA.MAX_LICENSE_COUNT, 0) AS MAX_LICENSE_COUNT,
			COALESCE(BB.SUM, 0) AS USED_LICENSE_COUNT
		FROM
		(
			SELECT
				CC.GROUP_ID,
				CC.GROUP_NAME,
				CC.PRODUCT_CODE,
				CC.PRODUCT_NAME,
				ISNULL(MAX_LICENSE_COUNT, -1) AS MAX_LICENSE_COUNT
			FROM
			(
				SELECT
					TOP (SELECT count(*) FROM MI_DMS_INFO_GROUP)
					DIG.GROUP_ID,
					DIG.GROUP_NAME,
					ISLO.PRODUCT_CODE,
					ISLO.PRODUCT_NAME
				FROM MI_DMS_INFO_GROUP AS DIG,
					MI_SYSTEM_INFO_SLM_LICENSE_ORG AS ISLO
				WHERE GROUP_DEPTH = 1 AND GROUP_ID != 999999
				ORDER BY DIG.GROUP_ID
			) AS CC LEFT OUTER JOIN MI_SYSTEM_MAP_SLM_LICENSE_ORG AS MSLO ON
				CC.GROUP_ID = MSLO.GROUP_ID AND
				CC.PRODUCT_CODE = MSLO.PRODUCT_CODE
		) AS AA LEFT OUTER JOIN (
			SELECT
				PRODUCT_CODE AS PRODUCT_CODE,
				PRODUCT_NAME AS PRODUCT_NAME,
				ORGANIZATION AS ORGANIZATION,
				SUM(COUNT) AS SUM
			FROM MI_SYSTEM_INFO_SLM_LICENSE_ORG AS ISLO,
			(
				SELECT
					DEVICE_TYPE, COUNT(*) AS COUNT, MGD.ORGANIZATION AS ORGANIZATION
				FROM MI_DMS_INFO_DEVICE AS ID
					INNER JOIN MI_DMS_MAP_GROUP_DEVICE AS MGD ON
						ID.DEVICE_ID = MGD.DEVICE_ID
				WHERE
					ID.IS_APPROVED = 'TRUE'
				GROUP BY
					ID.DEVICE_TYPE, MGD.ORGANIZATION
			) AS DD
			WHERE
                <!-- Taking the count of substring only. -->
                DD.DEVICE_TYPE IN (
                    SELECT VALUE FROM STRING_SPLIT (ISLO.DEVICE_TYPE_LIST,',')
                )
			GROUP BY PRODUCT_CODE, PRODUCT_NAME, ORGANIZATION
		) AS BB ON
			AA.GROUP_NAME = BB.ORGANIZATION AND
			AA.PRODUCT_CODE = BB.PRODUCT_CODE
	</select>

    <select id="getLicenseInfoAssignedToOrganization"
            resultType="com.samsung.magicinfo.framework.setup.entity.SlmLicenseOrgEntity">
		SELECT
			DISTINCT ON(AA.GROUP_ID, AA.PRODUCT_CODE)
			AA.GROUP_ID AS ORGANIZATION_ID,
			AA.GROUP_NAME AS ORGANIZATION_NAME,
			AA.PRODUCT_CODE AS PRODUCT_CODE,
			AA.PRODUCT_NAME AS PRODUCT_NAME,
			COALESCE(AA.MAX_LICENSE_COUNT, 0) AS MAX_LICENSE_COUNT,
			COALESCE(BB.SUM, 0) AS USED_LICENSE_COUNT
		FROM
		(
			SELECT
				CC.GROUP_ID,
				CC.GROUP_NAME,
				CC.PRODUCT_CODE,
				CC.PRODUCT_NAME,
				COALESCE(MAX_LICENSE_COUNT, -1) AS MAX_LICENSE_COUNT
			FROM
			(
				SELECT
					DIG.GROUP_ID,
					DIG.GROUP_NAME,
					ISLO.PRODUCT_CODE,
					ISLO.PRODUCT_NAME

				FROM MI_DMS_INFO_GROUP AS DIG,
					MI_SYSTEM_INFO_SLM_LICENSE_ORG AS ISLO

				WHERE GROUP_DEPTH = 1 AND GROUP_ID != 999999

				ORDER BY DIG.GROUP_ID

			) AS CC LEFT OUTER JOIN MI_SYSTEM_MAP_SLM_LICENSE_ORG AS MSLO ON
				CC.GROUP_ID = MSLO.GROUP_ID AND
				CC.PRODUCT_CODE = MSLO.PRODUCT_CODE
		) AS AA LEFT OUTER JOIN (
			SELECT
				PRODUCT_CODE AS PRODUCT_CODE,
				PRODUCT_NAME AS PRODUCT_NAME,
				ORGANIZATION AS ORGANIZATION,
				SUM(COUNT) AS SUM
			FROM MI_SYSTEM_INFO_SLM_LICENSE_ORG AS ISLO,
			(
				SELECT
					DEVICE_TYPE, COUNT(*) AS COUNT, MGD.ORGANIZATION AS ORGANIZATION
				FROM MI_DMS_INFO_DEVICE AS ID
					INNER JOIN MI_DMS_MAP_GROUP_DEVICE AS MGD ON
						ID.DEVICE_ID = MGD.DEVICE_ID
				WHERE
					ID.IS_APPROVED = 'TRUE'
				GROUP BY
					ID.DEVICE_TYPE, MGD.ORGANIZATION
			) AS DD
			WHERE
				DD.DEVICE_TYPE IN (
					SELECT UNNEST(STRING_TO_ARRAY(ISLO.DEVICE_TYPE_LIST,','))
				)
			GROUP BY PRODUCT_CODE, PRODUCT_NAME, ORGANIZATION
		) AS BB ON
			AA.GROUP_NAME = BB.ORGANIZATION AND
			AA.PRODUCT_CODE = BB.PRODUCT_CODE
	</select>
    <update id="updateMaxLicenseCount">
		UPDATE MI_SYSTEM_MAP_SLM_LICENSE_ORG
		SET MAX_LICENSE_COUNT = #{maxLicenseCount}
		WHERE PRODUCT_CODE = #{productCode} AND GROUP_ID = #{groupId}
	</update>

    <delete id="deleteMaxLicenseCount">
		DELETE FROM MI_SYSTEM_MAP_SLM_LICENSE_ORG
		WHERE PRODUCT_CODE = #{productCode} AND GROUP_ID = #{groupId}
	</delete>

    <insert id="addOrgSlmLicenseInfo">
		INSERT INTO MI_SYSTEM_MAP_SLM_LICENSE_ORG (PRODUCT_CODE, GROUP_ID,MAX_LICENSE_COUNT)
			SELECT #{productCode}, #{groupId}, #{maxLicenseCount}
			WHERE NOT EXISTS ( SELECT 1 FROM MI_SYSTEM_MAP_SLM_LICENSE_ORG WHERE PRODUCT_CODE = #{productCode} AND GROUP_ID = #{groupId} )
	</insert>
</mapper>