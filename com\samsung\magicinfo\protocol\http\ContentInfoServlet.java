package com.samsung.magicinfo.protocol.http;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Iterator;
import java.util.Map;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.Logger;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

public class ContentInfoServlet extends HttpServlet {
   private static final long serialVersionUID = -1143056137010460527L;
   private Logger logger = LoggingManagerV2.getLogger(ContentInfoServlet.class);

   public ContentInfoServlet() {
      super();
   }

   public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");
      this.doPost(request, response);
   }

   public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");
      PrintWriter out = response.getWriter();

      try {
         String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
         if (!SecurityUtils.getSafeFile(CONTENTS_HOME).exists() && !SecurityUtils.getSafeFile(CONTENTS_HOME).mkdirs()) {
            this.logger.error("mkdir Fail");
         }

         CommonsMultipartResolver multiPartResolver = new CommonsMultipartResolver();
         MultipartHttpServletRequest multiRequest = multiPartResolver.resolveMultipart(request);
         Iterator files = multiRequest.getFileNames();

         String cid;
         while(files.hasNext()) {
            cid = (String)files.next();
            MultipartFile multipartFile = multiRequest.getFile(cid);
            if (multipartFile != null) {
               multipartFile.transferTo(SecurityUtils.getSafeFile(CONTENTS_HOME + File.separator + cid));
            }
         }

         multiPartResolver.cleanupMultipart(multiRequest);
         cid = multiRequest.getParameter("CID");
         ContentInfo info = ContentInfoImpl.getInstance();
         ContentFile contentFileInfo = info.getMainFileInfo(cid);
         if (contentFileInfo == null) {
            contentFileInfo = info.getMainFileInfoTemporary(cid);
         }

         Content content = info.getContentActiveVerInfo(cid);
         if (content == null) {
            content = info.getContentActiveVerInfoTemporary(cid);
         }

         Map map = info.getThumbFileInfoOfActiveVersion(cid);
         if (map == null) {
            map = info.getThumbFileInfoOfActiveVersionTemporary(cid);
         }

         String thumbFileId = (String)map.get("FILE_ID");
         ContentFile sfiFileInfo = info.getFileInfo(content.getSfi_file_id());
         ContentFile thumbFileInfo = info.getFileInfo(thumbFileId);
         response.setHeader("ContentSize", content.getTotal_size().toString());
         response.setHeader("FileID", contentFileInfo.getFile_id());
         response.setHeader("FileHash", contentFileInfo.getHash_code());
         response.setHeader("FileSize", contentFileInfo.getFile_size().toString());
         if (thumbFileInfo.getFile_path() == null) {
            response.setHeader("ThumbFileID", "");
            response.setHeader("ThumbFileSize", "");
            response.setHeader("ThumbFileHash", "");
         } else {
            response.setHeader("ThumbFileID", thumbFileId);
            response.setHeader("ThumbFileSize", thumbFileInfo.getFile_size().toString());
            response.setHeader("ThumbFileHash", thumbFileInfo.getHash_code());
         }

         response.setHeader("SFIFileId", content.getSfi_file_id());
         if (sfiFileInfo != null) {
            response.setHeader("SFIFileSize", sfiFileInfo.getFile_size().toString());
            response.setHeader("SFIFileHash", sfiFileInfo.getHash_code());
         } else {
            response.setHeader("SFIFileSize", "");
            response.setHeader("SFIFileHash", "");
         }

         response.setHeader("IsShare", Integer.toString(content.getShare_flag()));
         response.setHeader("GroupNum", Long.toString(content.getGroup_id()));
         if (map.get("FILE_SIZE") == null) {
            response.setHeader("ThumbFileSize", "0");
         } else {
            response.setHeader("ThumbFileSize", ((Long)map.get("FILE_SIZE")).toString());
         }

         out.println("ContentName↓" + content.getContent_name());
         out.println("ContentMeta↓" + content.getContent_meta_data());
         out.println("ContentGroupName↓" + content.getGroup_name());
         out.println("FileName↓" + contentFileInfo.getFile_name());
         out.println("ThumbFileName↓" + thumbFileInfo.getFile_name());
         if (sfiFileInfo != null) {
            out.println("SFIFileName↓" + sfiFileInfo.getFile_name());
         } else {
            out.println("SFIFileName↓");
         }

         out.close();
      } catch (Exception var16) {
         this.logger.error(var16.getMessage());
         response.sendError(600, var16.toString());
      }

   }
}
