## generateContentOrTemplateEditorJnlp
##
<?xml version='1.0' encoding='UTF-8' ?>
<jnlp spec='1.0'
	codebase='${serverUrl}/servlet/'
	href='${JNLP_SERVLET}${jnlpFileName}'>
	<information>
#if ($isContentTypeContent)
		<title>ContentEditor</title>	
#elseif ($isContentTypeTemplate)
		<title>TemplateEditor</title>
#end
##		<title>ContentEditor</title>
		<vendor>MagicInfo</vendor>
	</information>
	<update check="always" policy="always"/> 
	<security>
		<all-permissions/>
	</security>
	<resources os="Windows" arch="amd64">
		<j2se version='1.6+' initial-heap-size="64m" max-heap-size="512m"/>
##		<jar href='../editor.jar' main='true' />
#if ($isContentTypeContent)
		<jar href='${serverUrl}/uploader/${urlToken}/editor.jar?version=${version}' main='true' />
#elseif ($isContentTypeTemplate)
		<jar href='${serverUrl}/uploader/${urlToken}/templateEditor.jar' main='true' />
#end
		<jar href='${serverUrl}/uploader/${urlToken}/lib/jnlp.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/transform.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-httpclient-3.1.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-codec-1.3.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-logging-1.1.1.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-net-3.3.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/swt64.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-ssl.jar?version=${version}' download="eager" />
	</resources>
	<resources os="Windows" arch="x86_64">
		<j2se version='1.6+' initial-heap-size="64m" max-heap-size="512m"/>
##		<jar href='../editor.jar' main='true' />
#if ($isContentTypeContent)
		<jar href='${serverUrl}/uploader/${urlToken}/editor.jar?version=${version}' main='true' />	
#elseif ($isContentTypeTemplate)
		<jar href='${serverUrl}/uploader/${urlToken}/templateEditor.jar' main='true' />
#end
		<jar href='${serverUrl}/uploader/${urlToken}/lib/jnlp.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/transform.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-httpclient-3.1.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-codec-1.3.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-logging-1.1.1.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-net-3.3.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/swt64.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-ssl.jar?version=${version}' download="eager" />
	</resources>
	<resources os="Windows" arch="x86">
		<j2se version='1.6+' initial-heap-size="64m" max-heap-size="512m"/>
##		<jar href='../editor.jar' main='true' />
#if ($isContentTypeContent)
		<jar href='${serverUrl}/uploader/${urlToken}/editor.jar?version=${version}' main='true' />	
#elseif ($isContentTypeTemplate)
		<jar href='${serverUrl}/uploader/${urlToken}/templateEditor.jar' main='true' />
#end
		<jar href='${serverUrl}/uploader/${urlToken}/lib/jnlp.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/transform.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-httpclient-3.1.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-codec-1.3.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-logging-1.1.1.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-net-3.3.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/swt.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-ssl.jar?version=${version}' download="eager" />
	</resources>
	<resources os="Windows" arch="i386">
		<j2se version='1.6+' initial-heap-size="64m" max-heap-size="512m"/>
##		<jar href='../editor.jar' main='true' />
#if ($isContentTypeContent)
		<jar href='${serverUrl}/uploader/${urlToken}/editor.jar?version=${version}' main='true' />
#elseif ($isContentTypeTemplate)
		<jar href='${serverUrl}/uploader/${urlToken}/templateEditor.jar' main='true' />
#end
		<jar href='${serverUrl}/uploader/${urlToken}/lib/jnlp.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/transform.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-httpclient-3.1.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-codec-1.3.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-logging-1.1.1.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-net-3.3.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/swt.jar?version=${version}' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-ssl.jar?version=${version}' download="eager" />
	</resources>
	<application-desc main-class='com.samsung.magicinfo.editor.EditorApplication'>
		<argument>${requestLocale}</argument>
		<argument>${ftpPort}</argument>
		<argument>${sessionId}</argument>
		<argument>${loginUserId}</argument>
		<argument>${issuedToken}</argument>
		<argument>${org_creator_id}</argument>
		<argument>${jnlpFileName}</argument>
		<argument>${contentId}</argument>
	</application-desc>
</jnlp>
