package com.samsung.magicinfo.protocol.constants;

public class CommonConstants {
   public static final String INSERT_SUCCESS = "Register in DB successfully.";
   public static final String INSERT_FAIL = "Don't register data in DB.";
   public static final String NOT_BOOT = "Please device connect to network after turned on power of device.";
   public static final String EVENT_ERROR = "The device is don't subscribe because it is requested event of cancel.";
   public static final String GET_FAIL = "It doesn't get information from device. Please check network or device.";
   public static final String SET_FAIL = "It failed to set up information to deivce. Please check network or device.";
   public static final String DEFAULT_UNSUBSCRIBE_YN = "N";
   public static final String UNSUBSCRIBE_YN = "Y";
   public static final String UNSUBSCRIBE_ASYNC = "A";
   public static final String MODEL_APPLY_Y = "Y";
   public static final String MODEL_APPLY_N = "N";
   public static final String PROCESSRULE_LAST_VERSION_Y = "Y";
   public static final String PROCESSRULE_LAST_VERSION_N = "N";
   public static final String SOFTWARE_LAST_VERSION_N = "N";
   public static final String SOFTWARE_LAST_VERSION_Y = "Y";
   public static final String SOFTWARE_INSTALL_N = "N";
   public static final String SOFTWARE_AUTO_UPDATE_Y = "Y";
   public static final String SOFTWARE_AUTO_UPDATE_N = "N";
   public static final String EVENT_BOOT = "BOOT";
   public static final String EVENT_APPLY = "APPLY";
   public static final String SOFTWARE_TYPE = "SOFTWARE";
   public static final String FIRMWARE_TYPE = "FIRMWARE";
   public static final String RULE_DOWNLOAD_TYPE = "RULE";
   public static final String RULE_CONTENTS_TYPE = "CONTENT";
   public static final String INSTALL_DEVICE_UPDATE_METHOD_IMMEDIATE = "immediate";
   public static final String INSTALL_DEVICE_UPDATE_METHOD_NEXT_RESET = "next_reset";
   public static final String INSTALL_DEVICE_UPDATE_METHOD_RESERVED = "reserved";
   public static final String RULE_TYPE_CREATE = "00";
   public static final String RULE_TYPE_PROCESS = "01";
   public static final String RULE_TYPE_MONITORING = "02";
   public static final String COMMAND_RESULT_SUCCESS = "SUCCESS";
   public static final String COMMAND_RESULT_FAIL = "FAIL";
   public static final String NO_DATA = "Data isn't existed.";
   public static final String ADMIN_ADDRESS = "<EMAIL>";
   public static final String APPLICATION_TYPE_AS = "as";
   public static final String APPLICATION_TYPE_CO = "co";
   public static final String APPLICATION_TYPE_SWUPDATE = "swupdate";
   public static final String APPLICATION_TYPE_ASENGINEER = "asengineer";
   public static final Long COMMON_ORGAN_ID = -2L;
   public static final String COMMON_TAG = "Common";
   public static final int PARTITION_SIZE = 1500;
   public static final long ROOT_ORG_ID = 0L;
   public static final int NETWORK_STANDBY_MODE = 1000;
   public static final String NETWORK_STANDBY_MODE_STR = "1000";
   public static final int LOCATION_MAX_LENGTH = 200;
   public static final int USER_ID_MIN_LENGTH = 3;
   public static final int USER_ID_MAX_LENGTH = 20;

   public CommonConstants() {
      super();
   }
}
