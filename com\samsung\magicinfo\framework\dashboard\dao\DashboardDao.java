package com.samsung.magicinfo.framework.dashboard.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.dashboard.entity.UserDashboardEntity;
import java.sql.SQLException;
import java.util.List;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class DashboardDao extends SqlSessionBaseDao {
   private Logger logger = LoggingManagerV2.getLogger(DashboardDao.class);

   public DashboardDao() {
      super();
   }

   public boolean removeDashboardWidget(String userId, String widgetName) throws SQLException {
      SqlSession session = this.openNewSession(false);
      DashboardDaoMapper mapper = (DashboardDaoMapper)this.getMapper(session);

      boolean var5;
      try {
         if (!mapper.removeDashboardWidget(userId, widgetName)) {
            session.rollback();
            var5 = false;
            return var5;
         }

         session.commit();
         var5 = true;
      } catch (SQLException var9) {
         session.rollback();
         throw var9;
      } finally {
         session.close();
      }

      return var5;
   }

   public boolean removeAllDashboardWidget(String userId) throws SQLException {
      SqlSession session = this.openNewSession(false);
      DashboardDaoMapper mapper = (DashboardDaoMapper)this.getMapper(session);

      boolean var4;
      try {
         if (mapper.removeAllDashboardWidget(userId)) {
            session.commit();
            var4 = true;
            return var4;
         }

         session.rollback();
         var4 = false;
      } catch (SQLException var8) {
         session.rollback();
         throw var8;
      } finally {
         session.close();
      }

      return var4;
   }

   public void getDashboardList(String userId) throws SQLException {
   }

   public int addDashboardWidget(String userId, int widgetId) throws SQLException {
      SqlSession session = this.openNewSession(false);
      DashboardDaoMapper mapper = (DashboardDaoMapper)this.getMapper(session);
      int priority = this.getPriority(userId);

      byte var6;
      try {
         if (mapper.addDashboardWidget(userId, widgetId, priority)) {
            session.commit();
            int var12 = priority;
            return var12;
         }

         session.rollback();
         var6 = -1;
      } catch (SQLException var10) {
         this.logger.error("", var10);
         session.rollback();
         throw var10;
      } finally {
         session.close();
      }

      return var6;
   }

   public int getPriority(String userId) throws SQLException {
      int count = ((DashboardDaoMapper)this.getMapper()).getPriority(userId);
      return count + 1;
   }

   public List getDashboardAll() throws SQLException {
      return ((DashboardDaoMapper)this.getMapper()).getDashboardAll();
   }

   public int checkDashbordById(String userId, int dashboardId) throws SQLException {
      return ((DashboardDaoMapper)this.getMapper()).checkDashbordById(userId, dashboardId);
   }

   public int getDashboardWidgetId(String widgetName) throws SQLException {
      return ((DashboardDaoMapper)this.getMapper()).getDashboardWidgetId(widgetName);
   }

   public boolean updateDashboardPriority(UserDashboardEntity dashboard) throws SQLException {
      return ((DashboardDaoMapper)this.getMapper()).updateDashboardPriority(dashboard.getUser_id(), dashboard.getDashboard_name(), dashboard.getPriority());
   }

   public Boolean removeDashboardEntity(String userId, int priority, int dashboardId) throws SQLException {
      return ((DashboardDaoMapper)this.getMapper()).removeDashboardEntity(userId, priority, dashboardId);
   }

   public Boolean setDashboardPriority(String userId, int priority, int dashboardId) throws SQLException {
      return ((DashboardDaoMapper)this.getMapper()).setDashboardPriority(userId, priority, dashboardId);
   }
}
