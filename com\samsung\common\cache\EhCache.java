package com.samsung.common.cache;

import akka.actor.ActorSystem;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.protocol.queue.RequestContext;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Map.Entry;
import net.sf.ehcache.Cache;
import net.sf.ehcache.CacheManager;
import net.sf.ehcache.Element;
import net.sf.ehcache.config.CacheConfiguration;
import net.sf.ehcache.config.Configuration;
import net.sf.ehcache.config.FactoryConfiguration;
import net.sf.ehcache.config.CacheConfiguration.CacheEventListenerFactoryConfiguration;
import net.sf.ehcache.event.CacheEventListener;
import net.spy.memcached.CASResponse;
import net.spy.memcached.CASValue;
import org.apache.logging.log4j.Logger;

public class EhCache implements BasicCache {
   private static final String CACHE_NAME = "MagicInfoCache";
   static Logger logger = LoggingManagerV2.getLogger(EhCache.class);
   private static EhCache instance = null;
   private static Cache localcache = null;
   private static CacheManager manager;
   private static boolean use_monitoring = false;

   private EhCache() {
      super();

      try {
         String replicationType = CommonConfig.get("ehcache.replication.type");
         Configuration config = new Configuration();
         String replicationNodes;
         String replicationPort;
         if (!"manual".equalsIgnoreCase(replicationType)) {
            replicationNodes = CommonConfig.get("ehcache.replication.auto.group_addr") != null ? CommonConfig.get("ehcache.replication.auto.group_addr") : "*********";
            replicationPort = CommonConfig.get("ehcache.replication.auto.group_port") != null ? CommonConfig.get("ehcache.replication.auto.group_port") : "4446";
            FactoryConfiguration providerFactory = new FactoryConfiguration();
            providerFactory.setClass("net.sf.ehcache.distribution.RMICacheManagerPeerProviderFactory");
            providerFactory.setProperties("peerDiscovery=automatic, multicastGroupAddress=" + replicationNodes + ", multicastGroupPort=" + replicationPort + ", multicastPacketTimeToLive=255");
            providerFactory.setPropertySeparator(",");
            config.addCacheManagerPeerProviderFactory(providerFactory);
            FactoryConfiguration listenerFactory = new FactoryConfiguration();
            listenerFactory.setClass("net.sf.ehcache.distribution.RMICacheManagerPeerListenerFactory");
            config.addCacheManagerPeerListenerFactory(listenerFactory);
         } else {
            replicationNodes = CommonConfig.get("ehcache.replication.manual.targets");
            replicationPort = CommonConfig.get("ehcache.replication.manual.port") != null ? CommonConfig.get("ehcache.replication.manual.port") : "40002";
            if (replicationNodes == null) {
               throw new Exception("ehcache.replication.targets is not defined.");
            }

            String[] nodes = null;
            if (replicationNodes != null && !"".equals(replicationNodes)) {
               nodes = replicationNodes.split(",");
            }

            String rmiUrls = "";

            for(int i = 0; i < nodes.length; ++i) {
               if (nodes[i] != null && !nodes[i].equals("")) {
                  String node = nodes[i].trim();
                  rmiUrls = rmiUrls + "//" + node + ":" + replicationPort + "/" + "MagicInfoCache" + "|";
               }
            }

            FactoryConfiguration providerFactory = new FactoryConfiguration();
            providerFactory.setClass("net.sf.ehcache.distribution.RMICacheManagerPeerProviderFactory");
            providerFactory.setProperties("peerDiscovery=manual, rmiUrls=" + rmiUrls);
            providerFactory.setPropertySeparator(",");
            config.addCacheManagerPeerProviderFactory(providerFactory);
            FactoryConfiguration listenerFactory = new FactoryConfiguration();
            listenerFactory.setClass("net.sf.ehcache.distribution.RMICacheManagerPeerListenerFactory");
            listenerFactory.setProperties("hostname=localhost, port=" + replicationPort + ", socketTimeoutMillis=3000");
            config.addCacheManagerPeerListenerFactory(listenerFactory);
         }

         CacheEventListenerFactoryConfiguration evtListenerFactory = new CacheEventListenerFactoryConfiguration();
         evtListenerFactory.setClass("net.sf.ehcache.distribution.RMICacheReplicatorFactory");
         CacheConfiguration magicinfoCache = (new CacheConfiguration("MagicInfoCache", 100000)).eternal(true);
         magicinfoCache.addCacheEventListenerFactory(evtListenerFactory);
         config.addCache(magicinfoCache);
         manager = CacheManager.create(config);
         localcache = manager.getCache("MagicInfoCache");
         if (use_monitoring) {
            CacheEventListener listener = new EhCacheEventListener();
            localcache.getCacheEventNotificationService().registerListener(listener);
            ActorSystem var15 = ActorSystem.create();
         }
      } catch (Exception var9) {
         logger.error("EhCache Error : ", var9);
      }

   }

   public static EhCache getInstance(boolean monitoring) {
      use_monitoring = monitoring;
      if (instance == null) {
         Class var1 = EhCache.class;
         synchronized(EhCache.class) {
            logger.info("Creating a new EhCache instance");
            instance = new EhCache();
         }
      }

      return instance;
   }

   public void set(String key, Object o) throws Exception {
      try {
         Element element = new Element(key, o);
         localcache.put(element);
      } catch (Exception var4) {
         logger.error("EhCache error: ", var4);
      }

   }

   public void set(String key, int timeToLive, Object o) throws Exception {
   }

   public CASValue gets(String key, Object autoInitObj) throws Exception {
      CASValue casValue = null;

      try {
         Element element = localcache.get(key);
         if (element == null || element.getObjectValue() == null || element.isExpired()) {
            if (autoInitObj == null) {
               return null;
            }

            element = new Element(key, autoInitObj);
            localcache.put(element);
         }

         casValue = new CASValue(1L, element.getObjectValue());
      } catch (Exception var5) {
      }

      return casValue;
   }

   public Object get(String key) throws Exception {
      Object object = null;

      try {
         Element element = localcache.get(key);
         if (element == null) {
            return null;
         }

         object = element.getObjectValue();
      } catch (Exception var4) {
      }

      return object;
   }

   public void clean() throws Exception {
      localcache.flush();
   }

   public void delete(String key) throws Exception {
      try {
         localcache.remove(key);
      } catch (Exception var3) {
         logger.error("EhCache error: ", var3);
      }

   }

   public CASResponse cas(String key, long casId, Object o) throws Exception {
      CASResponse casResponse = CASResponse.OBSERVE_MODIFIED;

      try {
         Object object = this.get(key);
         if (object != null && object.equals(o)) {
            this.set(key, object);
            casResponse = CASResponse.OK;
         } else {
            this.set(key, o);
            casResponse = CASResponse.OBSERVE_MODIFIED;
         }
      } catch (Exception var7) {
         logger.error("EhCache error: ", var7);
      }

      return casResponse;
   }

   public Object cas(String key, Object item, MutatorOperation operation) {
      try {
         Element element = localcache.get(key);
         if (operation instanceof FullMapMerger || operation instanceof MapItemMerger) {
            Map currentMap = (Map)element.getObjectValue();
            if (currentMap == null) {
               currentMap = new HashMap();
            }

            if (item != null) {
               Entry newItem = (Entry)item;
               ((Map)currentMap).put(newItem.getKey(), newItem.getValue());
               element = new Element(key, currentMap);
               localcache.put(element);
               return currentMap;
            }

            return currentMap;
         }

         if (operation instanceof MapItemRemover) {
            Map currentMap = (Map)element.getObjectValue();
            Map resultMap = new HashMap();
            if (currentMap != null) {
               resultMap.putAll(currentMap);
            }

            if (item != null) {
               resultMap.remove((String)item);
               element = new Element(key, currentMap);
               localcache.put(element);
               return currentMap;
            }

            return currentMap;
         }
      } catch (Exception var7) {
         logger.error("EhCache error: " + var7.getMessage());
      }

      logger.error("EhCache error: Couldn't get a CAS");
      return null;
   }

   public boolean isEmpty(String key) throws Exception {
      Boolean result = true;

      try {
         Element element = localcache.get(key);
         result = element == null;
         result = result || element.getObjectValue() == null;
      } catch (Exception var4) {
         logger.error("EhCache error: ", var4);
      }

      return result;
   }

   public boolean putMapCache(String cacaheKey, String hashKey, Object o) {
      return false;
   }

   public boolean enQueue(String cacheKey, Object o) {
      return false;
   }

   public Object deQueue(String cacheKey) {
      return null;
   }

   public List readAllQueue(String cacheKey) {
      return null;
   }

   public Object getMapCache(String cacheKey, String hashKey, int command) {
      return null;
   }

   public void removeMapCache(String cacheKey, String hashKey) {
   }

   public List readAllMap(String cacheKey) {
      return null;
   }

   public int getSizeMap(String cacheKey) {
      return 0;
   }

   public Object getQueue(String cacheKey) {
      return null;
   }

   public boolean isExistServiceInQueue(String cacheKey, String service) throws Exception {
      try {
         LinkedList queue = (LinkedList)this.get(cacheKey);
         if (queue != null) {
            ListIterator iterator = (ListIterator)queue.iterator();

            while(iterator.hasNext()) {
               RequestContext request = (RequestContext)iterator.next();
               if (request != null && request.getWebServiceContext().getServiceID().equals(service)) {
                  return true;
               }
            }
         }

         return false;
      } catch (Exception var6) {
         logger.error("[EhCache] fail isExistServiceInQueue e : " + var6.getMessage());
         throw var6;
      }
   }
}
