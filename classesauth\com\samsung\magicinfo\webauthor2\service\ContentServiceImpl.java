package com.samsung.magicinfo.webauthor2.service;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.exception.repository.ContentNotFoundException;
import com.samsung.magicinfo.webauthor2.model.BasicFileInfo;
import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.ContentGroup;
import com.samsung.magicinfo.webauthor2.model.ContentThumbnailBasic;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.model.factory.ContentFactory;
import com.samsung.magicinfo.webauthor2.repository.OpenAPIContentRepository2;
import com.samsung.magicinfo.webauthor2.repository.model.ContentData;
import com.samsung.magicinfo.webauthor2.repository.model.criteria.ContentSearchCriteria;
import com.samsung.magicinfo.webauthor2.repository.model.criteria.ContentSearchCriteriaBuilder;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.util.ArrayList;
import java.util.List;
import java.util.ListIterator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
public class ContentServiceImpl implements ContentService {
  private static final Logger logger = LoggerFactory.getLogger(ContentServiceImpl.class);
  
  private OpenAPIContentRepository2 contentRepository;
  
  private ContentFactory contentFactory;
  
  private UserData userData;
  
  private static final String VALIDCONTENT = "valid_content";
  
  @Autowired
  public ContentServiceImpl(OpenAPIContentRepository2 contentRepository, ContentFactory contentFactory, UserData userData) {
    this.contentRepository = contentRepository;
    this.contentFactory = contentFactory;
    this.userData = userData;
  }
  
  public Page<Content> getContentResources(Pageable pageable, DeviceType deviceType) {
    return getContentResources(null, pageable, deviceType, MediaType.BASIC_MEDIA_TYPE_LIST, null);
  }
  
  public Page<Content> getContentResources(Pageable pageable, DeviceType playerType, List<MediaType> mediaTypeList) {
    return getContentResources(null, pageable, playerType, mediaTypeList, null);
  }
  
  public Page<Content> getContentResources(String searchText, Pageable pageable, DeviceType playerType, List<MediaType> mediaTypeList) {
    return getContentResources(searchText, pageable, playerType, mediaTypeList, null);
  }
  
  public Page<Content> getContentResources(String searchText, Pageable pageable, DeviceType deviceType, List<MediaType> mediaTypeList, String searchType) {
    if (Strings.isNullOrEmpty(searchType))
      searchType = "all"; 
    ContentSearchCriteria contentSearchCriteria = (new ContentSearchCriteriaBuilder()).setPageSize(pageable.getPageSize()).setSearchType(searchType).setPage(pageable.getPageNumber()).setSearchMediaType(mediaTypeList).setSearchText(searchText).setExpirationStatusFilter("valid_content").createContentSearchCriteria();
    return this.contentRepository.getContentList(pageable, deviceType, contentSearchCriteria);
  }
  
  public Content getContent(String contentId) {
    ContentData contentData = this.contentRepository.getContent(contentId);
    if (contentData == null) {
      logger.error("getContent, Failed to find content id : " + contentId);
      return new Content(MediaType.IMAGE);
    } 
    return this.contentFactory.fromData(contentData);
  }
  
  public List<Content> findContents(List<BasicFileInfo> fileInfos) {
    List<Content> resultContents = new ArrayList<>();
    int requiredOpenApiVersionMajor = 6002;
    Boolean canGetThumbnailInfoWithFileId = this.userData.isSessionMisOpenApiVersionHigherOrEqualThan(requiredOpenApiVersionMajor);
    if (canGetThumbnailInfoWithFileId.booleanValue() == true) {
      updateThumbnailInfoFast(fileInfos, resultContents);
    } else {
      PageRequest pageRequest = new PageRequest(0, 20);
      while (fileInfos.size() != 0) {
        Page<Content> page = getContentResources((Pageable)pageRequest, DeviceType.iPLAYER);
        searchFilesInPage(fileInfos, page, resultContents);
        Pageable pageable = page.nextPageable();
        if (!page.hasNext())
          break; 
      } 
    } 
    createContentStubsForNotFoundElements(fileInfos, resultContents);
    return resultContents;
  }
  
  private void updateThumbnailInfoFast(List<BasicFileInfo> fileInfos, List<Content> resultContentList) {
    ListIterator<BasicFileInfo> iterator = fileInfos.listIterator();
    while (iterator.hasNext()) {
      BasicFileInfo fileInfo = iterator.next();
      ContentThumbnailBasic thumbnailBasic = this.contentRepository.getContentThumbnail(fileInfo.getFileId(), fileInfo.getSize());
      if (!thumbnailBasic.isEmpty().booleanValue()) {
        Content content = new Content(MediaType.IMAGE);
        content.setThumbnailId(thumbnailBasic.getFileId());
        content.setThumbnailName(thumbnailBasic.getFileName());
        content.setFileId(fileInfo.getFileId());
        content.setFileName(fileInfo.getFileName());
        resultContentList.add(content);
        iterator.remove();
      } 
    } 
  }
  
  private void createContentStubsForNotFoundElements(List<BasicFileInfo> fileInfos, List<Content> resultContentList) {
    for (BasicFileInfo fileInfo : fileInfos)
      resultContentList.add(buildFakeContent(fileInfo)); 
  }
  
  private void searchFilesInPage(List<BasicFileInfo> fileInfos, Page<Content> page, List<Content> resultContentList) {
    for (Content c : page) {
      ListIterator<BasicFileInfo> iterator = fileInfos.listIterator();
      while (iterator.hasNext()) {
        BasicFileInfo fileInfo = iterator.next();
        if (fileInfo.getFileId().equalsIgnoreCase(c.getFileId())) {
          resultContentList.add(c);
          iterator.remove();
        } 
      } 
    } 
  }
  
  private Content buildFakeContent(BasicFileInfo basicFileInfo) {
    Content content = new Content(MediaType.IMAGE);
    content.setThumbnailId(basicFileInfo.getFileId());
    content.setThumbnailName(basicFileInfo.getFileName());
    content.setFileId(basicFileInfo.getFileId());
    content.setFileName(basicFileInfo.getFileName());
    return content;
  }
  
  public List<Content> findContentsByIdList(List<String> conentIds) {
    List<Content> contents = new ArrayList<>();
    for (String id : conentIds)
      contents.add(getContent(id)); 
    return contents;
  }
  
  public List<Content> getRelatedDLKContent(String lftContentId) {
    List<ContentData> contentDatas = this.contentRepository.getRelatedDLKContent(lftContentId);
    return this.contentFactory.fromData(contentDatas);
  }
  
  public List<MediaType> getMediaTypeList(DeviceType deviceType) {
    List<MediaType> mediaTypes = new ArrayList<>();
    for (String mediaTypeString : this.contentRepository.getMediaTypeList(deviceType))
      mediaTypes.add(MediaType.valueOf(mediaTypeString)); 
    return mediaTypes;
  }
  
  public List<ContentThumbnailBasic> getContentThumbnails(String contentId, String resolution) {
    List<ContentThumbnailBasic> thumbnails = this.contentRepository.getContentThumbnails(contentId, resolution);
    if (thumbnails == null || thumbnails.size() == 0)
      throw new ContentNotFoundException(contentId); 
    return thumbnails;
  }
  
  public ContentThumbnailBasic getContentThumbnailBySize(String fileId, String size) {
    ContentThumbnailBasic thumbnail = this.contentRepository.getContentThumbnail(fileId, size);
    if (thumbnail == null)
      throw new ContentNotFoundException(fileId); 
    return thumbnail;
  }
  
  public String deleteContent(String contentId) {
    return this.contentRepository.deleteContent(contentId);
  }
  
  public List<ContentGroup> getContentGroupResources() {
    return this.contentRepository.getContentGroupList();
  }
}
