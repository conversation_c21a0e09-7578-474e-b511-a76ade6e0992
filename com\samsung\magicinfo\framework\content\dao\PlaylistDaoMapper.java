package com.samsung.magicinfo.framework.content.dao;

import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.Effect;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.entity.PlaylistContent;
import com.samsung.magicinfo.framework.content.entity.PlaylistTag;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;

public interface PlaylistDaoMapper {
   Group getGroup(@Param("groupId") Long var1) throws SQLException;

   String getPlaylistName(@Param("playlistId") String var1) throws SQLException;

   List getPlaylistAllVerInfo(@Param("playlistId") String var1) throws SQLException;

   Playlist getPlaylistActiveVerInfo(@Param("playlistId") String var1) throws SQLException;

   List getPlaylistActiveVerInfoForSync(@Param("playlistId") String var1) throws SQLException;

   Long getPlaylistActiveVersionId(@Param("playlistId") String var1) throws SQLException;

   int getCountPlaylistVersionId(@Param("playlistId") String var1) throws SQLException;

   Playlist getPlaylistVerInfo(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   List getContentListOfPlaylist(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   List getContentListOfSyncGroup(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   List getSearchList(Map var1) throws SQLException;

   List getSearchListPage(Map var1) throws SQLException;

   int getSearchListCnt(Map var1) throws SQLException;

   List getPlaylistList(Map var1) throws SQLException;

   List getPlaylistListPaged(@Param("userId") String var1, @Param("startIndex") int var2, @Param("resultsCount") int var3) throws SQLException;

   List getPlaylistListToDeleteContent(@Param("userId") String var1, @Param("contentIdList") String[] var2, @Param("startIndex") int var3, @Param("resultsCount") int var4) throws SQLException;

   List getPlaylistListPage(Map var1) throws SQLException;

   int getPlaylistListCnt(Map var1) throws SQLException;

   List getAllDeletedPlaylistList(@Param("creatorId") String var1, @Param("organizationId") Long var2) throws SQLException;

   List getAllPlaylistList(@Param("creatorId") String var1, @Param("organizationId") Long var2) throws SQLException;

   List getAllPlaylistListGroup(@Param("creatorId") String var1, @Param("organizationId") Long var2, @Param("groupId") Long var3) throws SQLException;

   List getPlaylistListByUser(@Param("creatorId") String var1, @Param("canReadUnsharedPlaylist") boolean var2, @Param("organizationId") Long var3, @Param("constFLAG_DEFAULT") Long var4) throws SQLException;

   List getContentList(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   List getTagList(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   List getTagListWithExpiredDate(@Param("playlistId") String var1, @Param("versionId") Long var2, @Param("expiredDate") String var3) throws SQLException;

   int getContentCount(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   int getCountSyncGroup(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   int getMaxCountSyncContent(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   String getMainContentId(@Param("playlistId") String var1) throws SQLException;

   List getActiveVerContentList(@Param("playlistId") String var1) throws SQLException;

   List getActiveVerContentListForDownloadCheck(@Param("playlistId") String var1) throws SQLException;

   PlaylistContent getContentEffectInfo(@Param("playlistId") String var1, @Param("versionId") Long var2, @Param("contentId") String var3) throws SQLException;

   PlaylistContent getContentEffectInfoByOrder(@Param("playlistId") String var1, @Param("versionId") Long var2, @Param("contentOrder") Long var3) throws SQLException;

   Effect getEffectInfoByEffectName(@Param("effectName") String var1) throws SQLException;

   Effect getSocEffectInfoByEffectName(@Param("effectName") String var1) throws SQLException;

   int countPlaylistID(String var1) throws SQLException;

   int countPlaylistVersion(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   int countUpdatablePlaylist(@Param("playlistId") String var1) throws SQLException;

   int countLockedPlaylist(@Param("playlistId") String var1, @Param("sessionId") String var2) throws SQLException;

   int addPlaylist(Playlist var1) throws SQLException;

   int setPlaylistModifiedDate(String var1) throws SQLException;

   Long getMaxContentOrder(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   Long getPlaylistMaxVer(String var1) throws SQLException;

   int setVersionPlaylistActive(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   int setMaxVersionPlaylistActive(String var1) throws SQLException;

   int addPlaylistInfo(Map var1) throws SQLException;

   Long getPlaylistNextVer(@Param("playlistId") String var1) throws SQLException;

   int setOtherVersionInactive(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   int deleteMapGroupPlaylist(String var1) throws SQLException;

   int addPlaylistVersionInfo(Playlist var1) throws SQLException;

   int addMapPlaylistContentInsert(PlaylistContent var1) throws SQLException;

   int addMapPlaylistTagInsert(@Param("tag") PlaylistTag var1) throws SQLException;

   int addMapPlaylistContentUpdate(PlaylistContent var1) throws SQLException;

   int addMapPlaylistTagUpdate(@Param("tag") PlaylistTag var1) throws SQLException;

   int setPlaylistEffect(PlaylistContent var1) throws SQLException;

   int addMapGroupPlaylist(@Param("playlistId") String var1, @Param("groupId") Long var2) throws SQLException;

   int setPlaylistInfo(@Param("playlistId") String var1, @Param("playlistName") String var2, @Param("playlistMetaData") String var3, @Param("shareFlag") int var4, @Param("ignoreTag") int var5, @Param("evenessPlayback") int var6) throws SQLException;

   int setActiveVersion(String var1, Long var2) throws SQLException;

   int deletePlaylistChangeStatus(@Param("playlistId") String var1) throws SQLException;

   int deletePlaylistChangeGroup(@Param("playlistId") String var1, @Param("groupId") Long var2) throws SQLException;

   int restorePlaylist(@Param("playlistId") String var1) throws SQLException;

   int deletePlaylistCompletely(@Param("playlistId") String var1) throws SQLException;

   int setPlaylistLock(@Param("playlistId") String var1, @Param("sessionId") String var2) throws SQLException;

   int setPlaylistGroup(@Param("playlistId") String var1, @Param("groupId") Long var2) throws SQLException;

   int setPlaylistShare(@Param("playlistId") String var1, @Param("shareFlag") Long var2) throws SQLException;

   int setPlaylistMetaData(@Param("playlistId") String var1, @Param("metaData") String var2) throws SQLException;

   Long getRootId(@Param("userId") String var1, @Param("organizationId") Long var2, @Param("pGroupId") Long var3) throws SQLException;

   int countExistGroupName(@Param("groupName") String var1, @Param("userId") String var2, @Param("organizationId") Long var3) throws SQLException;

   Long getGroupId(@Param("playlistId") String var1) throws SQLException;

   String getGroupName(@Param("groupId") Long var1) throws SQLException;

   Group getGroupInfo(@Param("groupId") Long var1) throws SQLException;

   List getGroupList(@Param("creatorId") String var1, @Param("organizationId") Long var2) throws SQLException;

   List getGroupListPage(@Param("creatorId") String var1, @Param("organizationId") Long var2, @Param("startPos") int var3, @Param("pageSize") int var4) throws SQLException;

   int getGroupListCnt(@Param("creatorId") String var1, @Param("organizationId") Long var2) throws SQLException;

   Long addGroup(Group var1) throws SQLException;

   int setGroupInfo(Group var1) throws SQLException;

   int deleteGroup(@Param("groupId") Long var1) throws SQLException;

   long countDeletableGroup(@Param("groupId") Long var1) throws SQLException;

   List getGroupedPlaylistIdList(@Param("groupId") Long var1) throws SQLException;

   List getChildGroupList(@Param("groupId") Long var1, @Param("creatorId") String var2, @Param("organizationId") Long var3) throws SQLException;

   List getChildGroupIdList(@Param("groupId") Long var1, @Param("pGroupId") Long var2) throws SQLException;

   int deletePlaylistVersionMap(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   int deleteTagPlaylistVersionMap(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   int deleteTagPlaylistCondition(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   int deletePlaylistVersion(@Param("playlistId") String var1, @Param("versionId") Long var2, @Param("userId") String var3) throws SQLException;

   List getPlaylistEffectList(@Param("deviceType") String var1, @Param("soc") String var2, @Param("premium") String var3) throws SQLException;

   int deleteContentTag(@Param("playlistId") String var1, @Param("contentId") String var2, @Param("tagId") int var3) throws SQLException;

   int setContentTag(@Param("playlistId") String var1, @Param("versionId") Long var2, @Param("contentId") String var3, @Param("contentOrder") int var4, @Param("matchType") String var5, @Param("tagId") int var6) throws SQLException;

   int setPlaylistTag(@Param("playlistId") String var1, @Param("versionId") Long var2, @Param("playlistTagId") long var3, @Param("tagOrder") int var5, @Param("matchType") String var6, @Param("tagId") int var7) throws SQLException;

   List getContentTag(@Param("playlistId") String var1, @Param("contentId") String var2, @Param("versionId") Long var3) throws SQLException;

   List getContentTagOrder(@Param("playlistId") String var1, @Param("versionId") Long var2, @Param("contentId") String var3, @Param("contentOrder") int var4) throws SQLException;

   List getTagPlaylistTagOrder(@Param("playlistId") String var1, @Param("versionId") Long var2, @Param("playlistTagId") long var3, @Param("tagOrder") long var5) throws SQLException;

   List getPlaylistContentTagList(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   int getContentOrderForExpiredContent(@Param("playlistId") String var1, @Param("versionId") Long var2, @Param("contentId") String var3) throws SQLException;

   int setContentDuraionByContentID(@Param("contentId") String var1, @Param("contentDuration") Long var2) throws SQLException;

   int setContentDuraionMilliByContentID(@Param("contentId") String var1, @Param("milli") String var2) throws SQLException;

   List getPlaylistIDListByContentID(@Param("contentId") String var1) throws SQLException;

   int setPlaytime(@Param("playlistId") String var1, @Param("versionId") Long var2, @Param("playTime") String var3) throws SQLException;

   Long getSumOfContentDuration(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   String isDelete(@Param("playlistId") String var1) throws SQLException;

   String getCreatorIdByPlaylistId(@Param("playlistId") String var1) throws SQLException;

   int unlockAllSession() throws SQLException;

   int setPlaylistUnlock(@Param("playlistId") String var1, @Param("sessionId") String var2) throws SQLException;

   int setPlaylistUnlockBySessionID(@Param("sessionId") String var1) throws SQLException;

   List getPlaylistListByDeviceType(@Param("userId") String var1, @Param("startIndex") int var2, @Param("resultsCount") int var3, @Param("deviceType") String var4, @Param("type") String var5, @Param("deviceTypeVersion") float var6, @Param("selectedDeviceType") List var7) throws SQLException;

   int getPlaylistListCountByDeviceType(@Param("userId") String var1, @Param("deviceType") String var2, @Param("type") String var3, @Param("deviceTypeVersion") float var4, @Param("selectedDeviceType") List var5) throws SQLException;

   List getContentTagInSchedule(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   Effect getVWLEffectInfoByEffectName(@Param("effectName") String var1);

   void deleteContentFromPlaylist(@Param("contentId") String var1);

   List getContentOrderListOfPlaylist(@Param("playlistId") String var1, @Param("versionId") Long var2);

   List getSubPlaylistContentOrderListOfPlaylist(@Param("playlistId") String var1, @Param("versionId") Long var2);

   void updateContentOrder(@Param("newContentOrder") int var1, @Param("playlistId") String var2, @Param("versionId") Long var3, @Param("oldContentOrder") Long var4);

   void updateSubPlaylistContentOrder(@Param("newContentOrder") int var1, @Param("playlistId") String var2, @Param("versionId") Long var3, @Param("oldContentOrder") Long var4);

   void updateContentCount(@Param("contentCount") int var1, @Param("playlistId") String var2, @Param("versionId") Long var3);

   int addMapSyncGroupInfo(@Param("playlistId") String var1, @Param("versionId") Long var2, @Param("syncPlayId") String var3, @Param("isSync") String var4) throws SQLException, Exception;

   List getSyncGroupInfo(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException, Exception;

   String updateMapSyncGroupInfo(@Param("playlistId") String var1, @Param("versionId") Long var2, @Param("syncPlayId") String var3) throws SQLException, Exception;

   int deleteMapSyncGroupInfo(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   int getUsedPlaylistCount(@Param("organizationId") long var1) throws SQLException;

   ContentFile getThumbFileInfo(@Param("contentId") String var1) throws SQLException;

   List getTagContentListOfPlaylist(@Param("listMap") List var1) throws SQLException;

   int getTagContentListOfPlaylistSize(@Param("listMap") List var1) throws SQLException;

   List getContentListFromTagId(@Param("listMap") List var1) throws SQLException;

   boolean addTagConditionMapping(@Param("playlistId") String var1, @Param("versionId") long var2, @Param("tagId") long var4, @Param("tagConditionId") long var6) throws SQLException;

   List getPlaylistTagConditionList(@Param("playlistId") String var1, @Param("versionId") long var2, @Param("tagId") long var4) throws SQLException;

   List getTagConditionList(@Param("playlistId") String var1, @Param("versionId") long var2);

   List getTagConditionWithTagIdList(@Param("playlistId") String var1, @Param("versionId") long var2, @Param("tagId") long var4);

   ContentFile getTagPlaylistThumbFileInfo(@Param("playlistId") String var1) throws SQLException;

   List getPlaylistIdfromCategory(@Param("categoryList") String[] var1) throws SQLException;

   int checkExistTagCondition(@Param("playlistId") String var1, @Param("versionId") Long var2, @Param("tagId") Long var3, @Param("conditionId") Long var4) throws SQLException;

   int checkTagPlaylist(@Param("playlistId") String var1, @Param("playlistType") String var2) throws SQLException;

   int deleteTagPlaylistVersion(@Param("playlistId") String var1) throws SQLException;

   int deleteTagPlaylistConditionPerm(@Param("playlistId") String var1) throws SQLException;

   List getCntContentAtTagPlaylist(@Param("tagId") Long var1, @Param("conditionIds") String[] var2) throws SQLException;

   List getThumbContentAtTagPlaylistAll(@Param("tagId") Long var1, @Param("conditionIds") String[] var2) throws SQLException;

   List getThumbContentAtTagPlaylist(@Param("tagId") Long var1, @Param("conditionIds") String[] var2) throws SQLException;

   List getTagPlaylistTagList(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   List getTagPlaylistTagConditionList(@Param("playlistId") String var1, @Param("versionId") Long var2) throws SQLException;

   List getConditionIdWithTagNumber(@Param("tagId") long var1, @Param("tagConditionEqual") String[] var3, @Param("tagConditionUp") String[] var4, @Param("tagConditionDown") String[] var5) throws SQLException;

   List getListLinkedPlaylistProgramId(@Param("playlistId") String var1) throws SQLException;

   List getListLinkedPlaylistPlaylistId(@Param("playlistId") String var1) throws SQLException;

   List getContentTagListWithPlaylistId(@Param("playlistId") String var1) throws SQLException;

   List getContentListWithTag(@Param("playlistId") String var1, @Param("contentId") String var2, @Param("versionId") Long var3, @Param("contentOrder") Long var4, @Param("tagList") List var5) throws SQLException;

   String getOrganizationByPlaylistId(@Param("playlistId") String var1) throws SQLException;

   boolean deleteMapSubPlaylist(@Param("subPlaylistId") String var1) throws SQLException;

   List getPlaylistListBySubPlaylist(@Param("subPlaylistId") String var1) throws SQLException;

   List getAllPlaylistGroups(@Param("groupId") long var1) throws SQLException;

   int getCountSubPlaylistOfPlaylist(@Param("playlistId") String var1) throws SQLException;

   int setHasSubPlaylist(@Param("playlistId") String var1, @Param("versionId") Long var2, @Param("hasSubPlaylist") Boolean var3) throws SQLException;

   int getCountPlaylistToExpire(@Param("groupList") List var1, @Param("userId") String var2, @Param("stopDate") String var3, @Param("condition") SelectCondition var4) throws SQLException;

   List getListPlaylistToExpire(@Param("startPos") int var1, @Param("pageSize") int var2, @Param("groupList") List var3, @Param("stopDate") String var4, @Param("condition") SelectCondition var5) throws SQLException;

   int deleteContentTagFromPlaylist(@Param("contentId") String var1) throws SQLException;

   void updateContentOrderOfTag(@Param("playlistId") String var1, @Param("versionId") Long var2, @Param("contentId") String var3, @Param("newContentOrder") int var4) throws SQLException;

   int isExistDefaultGroup(@Param("creator_id") String var1, @Param("organization_id") long var2) throws SQLException;

   long getDefaultPlaylistGroupId(@Param("creator_id") String var1, @Param("organization_id") long var2) throws SQLException;

   void changeGroupIdOf_MI_CMS_MAP_GROUP_PLAYLIST(@Param("groupId") Long var1, @Param("fromUserId") String var2, @Param("organizationId") Long var3) throws SQLException;

   void changeCreatorIdOf_MI_CMS_INFO_PLAYLIST(@Param("fromUserId") String var1, @Param("toUserId") String var2, @Param("organizationId") Long var3) throws SQLException;

   void changeCreatorIdOf_MI_CMS_INFO_PLAYLIST_VERSION(@Param("fromUserId") String var1, @Param("toUserId") String var2, @Param("organizationId") Long var3) throws SQLException;

   int deleteGroupByCreatorId(@Param("creatorId") String var1) throws SQLException;

   long getCntAllPlaylists(@Param("creatorId") String var1, @Param("organizationId") Long var2) throws SQLException;

   List getUpperPlaylist(@Param("playlistId") String var1) throws Exception;

   int getContentCountInTagPlaylist(@Param("playlistId") String var1) throws Exception;

   List getPlaylistInfoByContentId(@Param("contentId") String var1) throws Exception;

   int setTotalSize(@Param("playlistId") String var1, @Param("versionId") long var2, @Param("totalSize") long var4) throws Exception;

   int setContentDurationByVersionOfPlaylist(@Param("playlistId") String var1, @Param("versionId") long var2, @Param("contentId") String var4, @Param("contentDuration") long var5) throws Exception;

   int setContentDurationMilliByVersionOfPlaylist(@Param("playlistId") String var1, @Param("versionId") long var2, @Param("contentId") String var4, @Param("contentDurationMilli") String var5) throws Exception;

   List getGroupListByOrganizationId(@Param("organizationId") Long var1) throws SQLException;

   List getPlaylistIdListByPlaylistName(@Param("playlistNameList") String[] var1) throws SQLException;

   List getPlaylistIdListByRegex(@Param("regex") String var1) throws SQLException;

   List getPlaylistScheduleMapping(@Param("playlistIds") List var1) throws SQLException;

   List getPlayListGroupBySearch(@Param("searchText") String var1, @Param("organizationId") Long var2, @Param("userId") String var3) throws SQLException;

   List getParentsGroupList(@Param("pGroupId") int var1) throws SQLException;

   Long getOrganizationIdByGroupId(@Param("groupId") Long var1) throws SQLException;

   List getSubGroupList(@Param("groupId") Long var1, @Param("organizationId") Long var2) throws SQLException;

   List getActivePlaylistCountOne(@Param("contentId") String var1) throws SQLException;

   int isExistMapPlaylistID(@Param("playlistId") String var1, @Param("playlistVersionId") Long var2) throws SQLException;

   List getContentCountByPlaylist() throws SQLException;

   List getContentTypeCountByPlaylist() throws SQLException;

   List getPlaylistCountByPlaylistType() throws SQLException;

   int getPlaylistGroupTotalCount() throws SQLException;
}
