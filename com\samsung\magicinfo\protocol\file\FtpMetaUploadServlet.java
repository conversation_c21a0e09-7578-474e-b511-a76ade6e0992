package com.samsung.magicinfo.protocol.file;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.exception.CMSExceptionCode;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.AbilityUtils;
import com.samsung.common.utils.ContentUtils;
import com.samsung.common.utils.RequestUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.content.constants.ContentConstants;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.entity.PlaylistContent;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.ContentMetaManager;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

public class FtpMetaUploadServlet extends HttpServlet {
   private static final long serialVersionUID = -1937920273302329385L;
   private Logger logger = LoggingManagerV2.getLogger(FtpMetaUploadServlet.class);

   public FtpMetaUploadServlet() {
      super();
   }

   public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");
      this.doPost(request, response);
   }

   public void setContentGroupId(Content content, Long newGrpId, Long defaultGrpId) {
      if (0L == content.getGroup_id()) {
         if (newGrpId == null) {
            content.setGroup_id(defaultGrpId);
         } else {
            content.setGroup_id(newGrpId);
         }
      }
   }

   public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");

      try {
         String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
         String userId = SecurityUtils.getUserIdFromRequest(request);
         String token = SecurityUtils.getTokenFromRequest(request);
         String cid = request.getHeader("CID");
         ContentInfo cmsDao = ContentInfoImpl.getInstance();
         String orgCreatorId = request.getParameter("org_creator_id");
         if (orgCreatorId == null || orgCreatorId.equals("")) {
            String tmpId = cmsDao.getContentOrgCreatorId(cid);
            if (StringUtils.isNotBlank(tmpId)) {
               orgCreatorId = tmpId;
            } else {
               orgCreatorId = userId;
            }
         }

         if (cid == null) {
            this.logger.error("[MagicInfo_FtpMetaUploadServelet][" + cid + "] " + CMSExceptionCode.APP602[2]);
            response.setHeader("code", CMSExceptionCode.APP602[0]);
            response.setHeader("message", CMSExceptionCode.APP602[2]);
            response.sendError(602, CMSExceptionCode.APP602[2]);
            return;
         }

         UserInfo uInfo = UserInfoImpl.getInstance();
         long orgId = uInfo.getRootGroupIdByUserId(userId);
         ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
         Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
         boolean contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
         PrintWriter out = null;
         out = response.getWriter();
         boolean bExistContent = false;
         boolean isDeleted = false;

         try {
            bExistContent = cmsDao.existContentForCidMappingOfUploader(cid, orgCreatorId);
         } catch (SQLException var85) {
            this.logger.error("[MagicInfo_FtpMetaUploadServelet][" + cid + "] " + CMSExceptionCode.APP612[2] + " : " + var85.getMessage());
            response.setHeader("code", CMSExceptionCode.APP612[0]);
            response.setHeader("message", CMSExceptionCode.APP612[2]);
            response.sendError(612, CMSExceptionCode.APP612[2]);
            return;
         }

         if (bExistContent) {
            try {
               isDeleted = cmsDao.isDeletedContentByContentId(cid);
            } catch (SQLException var84) {
               this.logger.error("[MagicInfo_FtpMetaUploadServelet][" + cid + "] " + CMSExceptionCode.APP612[2] + " : " + var84.getMessage());
               response.setHeader("code", CMSExceptionCode.APP612[0]);
               response.setHeader("message", CMSExceptionCode.APP612[2]);
               response.sendError(612, CMSExceptionCode.APP612[2]);
               return;
            }
         }

         if (bExistContent && isDeleted && cmsDao.restoreContent(cid) == 1) {
            this.logger.info("[MagicInfo_FtpMetaUploadServelet] FtpMetaUploadServlet existedContent, isDeleted " + cid + "revived");
         }

         File cmsHome = null;

         try {
            cmsHome = SecurityUtils.getSafeFile(CONTENTS_HOME);
            if (!cmsHome.exists()) {
               cmsHome.mkdir();
            }
         } catch (Exception var89) {
            this.logger.error("[MagicInfo_FtpMetaUploadServelet][" + cid + "] " + CMSExceptionCode.APP613[2] + " : " + var89.getMessage());
            response.setHeader("code", CMSExceptionCode.APP613[0]);
            response.setHeader("message", CMSExceptionCode.APP613[2]);
            response.sendError(613, CMSExceptionCode.APP613[2]);
            return;
         }

         String cmsMetaPath = null;

         try {
            cmsMetaPath = CONTENTS_HOME + File.separator + "contents_meta";
            File cms_meta = SecurityUtils.getSafeFile(CONTENTS_HOME + File.separator + "contents_meta");
            if (!cms_meta.exists()) {
               cms_meta.mkdir();
            }
         } catch (Exception var88) {
            this.logger.error("[MagicInfo_FtpMetaUploadServelet][" + cid + "] " + CMSExceptionCode.APP613[2] + " : " + var88.getMessage());
            response.setHeader("code", CMSExceptionCode.APP613[0]);
            response.setHeader("message", CMSExceptionCode.APP613[2]);
            response.sendError(613, CMSExceptionCode.APP613[2]);
            return;
         }

         String abs_path = cmsMetaPath + File.separator + cid;
         File abs_path_file = null;

         try {
            abs_path_file = SecurityUtils.getSafeFile(abs_path);
            if (!abs_path_file.exists()) {
               abs_path_file.mkdir();
            }
         } catch (Exception var87) {
            this.logger.error("[MagicInfo_FtpMetaUploadServelet][" + cid + "] " + CMSExceptionCode.APP613[2] + " : " + var87.getMessage());
            response.setHeader("code", CMSExceptionCode.APP613[0]);
            response.setHeader("message", CMSExceptionCode.APP613[2]);
            response.sendError(613, CMSExceptionCode.APP613[2]);
            return;
         }

         long vid = 0L;
         Hashtable hash = new Hashtable();
         CommonsMultipartResolver multiPartResolver = new CommonsMultipartResolver();
         MultipartHttpServletRequest multiRequest = multiPartResolver.resolveMultipart(request);
         Iterator files = multiRequest.getFileNames();

         String pCategory;
         while(files.hasNext()) {
            pCategory = (String)files.next();
            MultipartFile multipartFile = multiRequest.getFile(pCategory);
            multipartFile.transferTo(SecurityUtils.getSafeFile(abs_path + File.separator + pCategory));

            try {
               if (pCategory.toLowerCase().indexOf(".csd") > -1) {
                  hash = FileUploadHelper.getInstance().readCSD(abs_path + File.separator + pCategory, cid, vid);
                  this.logger.info("[MagicInfo_FtpMetaUploadServelet] csd file path : abs_path + File.separator + fileName : " + abs_path + File.separator + pCategory);
               }
            } catch (Exception var83) {
               this.logger.error("[MagicInfo_FtpMetaUploadServelet][" + cid + "] " + CMSExceptionCode.APP614[2] + " : " + var83.getMessage());
               response.setHeader("code", CMSExceptionCode.APP614[0]);
               response.setHeader("message", CMSExceptionCode.APP614[2]);
               response.sendError(614, CMSExceptionCode.APP614[2]);
               return;
            }
         }

         multiPartResolver.cleanupMultipart(multiRequest);
         pCategory = "0";
         Content content = new Content();
         List fileMeta = new ArrayList();
         Boolean bMustAddContent = false;
         Long version = 0L;
         String PROMThumbnailType = (String)hash.get("PROMThumbnailType");
         if (!bExistContent && !isDeleted) {
            bMustAddContent = true;
         }

         String strHost;
         String strPort;
         String content_play_time;
         String contentMeta;
         if (hash != null) {
            String fileIDToSave;
            String template_id;
            try {
               strHost = (String)hash.get("Share");
               strPort = (String)hash.get("Meta");
               int shareFlag = 1;
               if (strHost != null) {
                  shareFlag = Integer.parseInt(strHost);
               }

               content.setContent_id(cid);
               content.setShare_flag(shareFlag);
               content.setContent_meta_data(strPort);
               content.setCreator_id(userId);
               content.setOrg_creator_id(orgCreatorId);
               new Date(System.currentTimeMillis());
               content.setVersion_id(vid);
               content_play_time = (String)hash.get("PlayTime");
               fileIDToSave = "";
               contentMeta = "";
               this.logger.info("[MagicInfo_FtpMetaUploadServelet][" + cid + "] content_play_time : " + content_play_time);
               if (content_play_time != null && !content_play_time.equals("") && !content_play_time.equals("-")) {
                  fileIDToSave = content_play_time.substring(0, 8);
                  if (content_play_time.length() > 8) {
                     contentMeta = content_play_time.substring(9, content_play_time.length());
                  }
               }

               String contentName = (String)hash.get("Title");
               if (contentName.length() > 100) {
                  contentName = contentName.substring(0, 100);
               }

               content.setContent_name((String)hash.get("Title"));
               content.setMedia_type((String)hash.get("MediaType"));
               content.setTotal_size((Long)hash.get("TotalSize"));
               content.setPlay_time(fileIDToSave);
               content.setPlay_time_milli(contentMeta);
               content.setResolution((String)hash.get("Resolution"));
               if (content.getMedia_type().equals("LFD") || content.getMedia_type().equals("VWL") || content.getMedia_type().equals("LFT") || content.getMedia_type().equals("PLUGIN_EFFECT")) {
                  template_id = "iPLAYER";
                  String playerType = (String)hash.get("PlayerType");
                  String strdeviceTypeVersion = (String)hash.get("PlayerTypeVersion");
                  float deviceTypeVersion = CommonDataConstants.TYPE_VERSION_1_0;
                  if (strdeviceTypeVersion != null && !strdeviceTypeVersion.equals("")) {
                     deviceTypeVersion = Float.parseFloat(strdeviceTypeVersion);
                  }

                  if (!playerType.equals("")) {
                     template_id = playerType;
                  }

                  if (template_id.equalsIgnoreCase("\"iPLAYER\"")) {
                     template_id = "iPLAYER";
                  } else if (template_id.equalsIgnoreCase("\"SPLAYER\"")) {
                     template_id = "SPLAYER";
                  } else if (template_id.equalsIgnoreCase("\"S2PLAYER\"")) {
                     template_id = "S2PLAYER";
                  } else if (template_id.equalsIgnoreCase("\"APLAYER\"")) {
                     template_id = "APLAYER";
                  } else if (template_id.equalsIgnoreCase("\"WPLAYER\"")) {
                     template_id = "WPLAYER";
                  }

                  content.setDevice_type(template_id);
                  content.setDevice_type_version(deviceTypeVersion);
               }

               if ((Boolean)hash.get("IsStreaming")) {
                  content.setIs_streaming("Y");
               } else {
                  content.setIs_streaming("N");
               }

               if (((String)hash.get("LinearVWL")).equalsIgnoreCase("true")) {
                  content.setIs_linear_vwl("Y");
               } else {
                  content.setIs_linear_vwl("N");
               }

               if (hash.get("X_Count").toString().length() > 0) {
                  content.setX_count(Integer.valueOf(hash.get("X_Count").toString()));
               }

               if (hash.get("Y_Count").toString().length() > 0) {
                  content.setY_count(Integer.valueOf(hash.get("Y_Count").toString()));
               }

               if (hash.get("SCREEN_COUNT").toString().length() > 0) {
                  content.setScreen_count(Integer.valueOf(hash.get("SCREEN_COUNT").toString()));
               }

               if (hash.get("X_Range").toString().length() > 0) {
                  content.setX_range(Integer.valueOf(hash.get("X_Range").toString()));
               }

               if (hash.get("Y_Range").toString().length() > 0) {
                  content.setY_range(Integer.valueOf(hash.get("Y_Range").toString()));
               }
            } catch (Exception var86) {
               this.logger.error("[MagicInfo_FtpMetaUploadServelet][" + cid + "] error hash e : " + var86.getMessage());
               response.setHeader("code", CMSExceptionCode.APP614[0]);
               response.setHeader("message", CMSExceptionCode.APP614[2]);
               response.sendError(614, CMSExceptionCode.APP614[2]);
               return;
            }

            if (contentsApprovalEnable && !ContentConstants.getMediaTypeForAuthor().contains(content.getMedia_type())) {
               if (content.getMain_file_Extension().equalsIgnoreCase("LFT")) {
                  content.setApproval_status("APPROVED");
               } else {
                  AbilityUtils abilityUtils = new AbilityUtils();
                  if (abilityUtils.isContentApprovalAuthority(userId)) {
                     content.setApproval_status("APPROVED");
                  } else {
                     content.setApproval_status("UNAPPROVED");
                  }
               }
            } else {
               content.setApproval_status("APPROVED");
            }

            Long grpId = cmsDao.getGroupId(content.getContent_id());
            if (pCategory == null || pCategory.equals("") || pCategory.equals("0") || pCategory.equals("null")) {
               try {
                  pCategory = (String)hash.get("Category");
                  if (StringUtils.isBlank(pCategory)) {
                     if (grpId != null) {
                        pCategory = grpId.toString();
                     } else {
                        pCategory = cmsDao.getRootId(orgCreatorId).toString();
                     }
                  }

                  content.setGroup_id(Long.parseLong(pCategory));
               } catch (SQLException var82) {
                  this.logger.error("[MagicInfo_FtpMetaUploadServelet][" + cid + "] fail pCategory : " + var82.getMessage());
                  response.setHeader("code", CMSExceptionCode.APP620[0]);
                  response.setHeader("message", CMSExceptionCode.APP620[2]);
                  response.sendError(620, CMSExceptionCode.APP620[2]);
                  return;
               }
            }

            ArrayList flist = (ArrayList)hash.get("ContentFileList");
            Map isNewMap = (Map)hash.get("isNewMap");
            Boolean bExistFile = false;
            List fileListToSave = new ArrayList();
            fileIDToSave = "";
            if (flist != null && flist.size() > 0) {
               Map hashFileMap = new HashMap();

               String fileExtension;
               int i;
               for(i = 0; i < flist.size(); ++i) {
                  ContentFile cmsContentFile = (ContentFile)flist.get(i);
                  Map map = new HashMap();
                  map.put("fileID", cmsContentFile.getFile_id());
                  map.put("hashCode", cmsContentFile.getHash_code());
                  map.put("fileName", cmsContentFile.getFile_name());
                  bExistFile = false;
                  if (cmsDao.isExistFileByHash(cmsContentFile.getFile_name(), cmsContentFile.getFile_size(), cmsContentFile.getHash_code())) {
                     bExistFile = true;
                  }

                  ContentFile file;
                  if (i == 0 && bExistContent) {
                     if (bExistFile) {
                        file = cmsDao.getMainFileInfo(content.getContent_id());
                        if (file == null) {
                           file = cmsDao.getMainFileInfoOfTmpVer(content.getContent_id());
                        }

                        if (file != null) {
                           if (file.getFile_name().equals(cmsContentFile.getFile_name()) && file.getFile_size().equals(cmsContentFile.getFile_size()) && file.getHash_code().equalsIgnoreCase(cmsContentFile.getHash_code())) {
                              bMustAddContent = false;
                           } else {
                              bMustAddContent = true;
                           }
                        }
                     } else {
                        bMustAddContent = true;
                     }
                  }

                  if (!bMustAddContent && (content.getMedia_type().equalsIgnoreCase("FLASH") || content.getMedia_type().equalsIgnoreCase("OFFICE")) && i == 1) {
                     if (bExistFile) {
                        file = cmsDao.getSfiFileInfo(content.getContent_id());
                        if (file != null) {
                           if (file.getFile_name().equals(cmsContentFile.getFile_name()) && file.getFile_size().equals(cmsContentFile.getFile_size()) && file.getHash_code().equalsIgnoreCase(cmsContentFile.getHash_code())) {
                              bMustAddContent = false;
                           } else {
                              bMustAddContent = true;
                           }
                        }
                     } else {
                        bMustAddContent = true;
                     }
                  }

                  Map fileMap = new HashMap();
                  fileMap.put("reqFileId", cmsContentFile.getFile_id());
                  if (bExistFile) {
                     fileIDToSave = cmsDao.getFileIDByHash(cmsContentFile.getFile_name(), cmsContentFile.getFile_size(), cmsContentFile.getHash_code());
                     ContentFile contentFile = cmsDao.getFileInfo(fileIDToSave);
                     fileExtension = contentFile.getFile_path() + File.separator + contentFile.getFile_name();
                     File file = SecurityUtils.getSafeFile(fileExtension);
                     fileMap.put("fileId", fileIDToSave);
                     fileMap.put("fileOffset", file.length());
                     if (isNewMap.containsKey(cmsContentFile.getFile_id())) {
                        Boolean isNew = (Boolean)isNewMap.get(cmsContentFile.getFile_id());
                        fileMap.put("forceIsNew", isNew);
                     }

                     fileMap.put("isNew", false);
                     fileMap.put("reqIndex", cmsContentFile.getReqIndex());
                  } else if ((content.getMedia_type().equalsIgnoreCase("LFD") || content.getMedia_type().equalsIgnoreCase("LFT") || content.getMedia_type().equalsIgnoreCase("VWL")) && i == 1 && !bExistFile && !bMustAddContent) {
                     fileIDToSave = cmsContentFile.getFile_id();
                     fileMap.put("fileId", fileIDToSave);
                     fileMap.put("fileOffset", cmsContentFile.getFile_size());
                     fileMap.put("isNew", true);
                     fileMap.put("reqIndex", cmsContentFile.getReqIndex());
                  } else {
                     if (hashFileMap.containsKey(cmsContentFile.getHash_code() + cmsContentFile.getFile_name())) {
                        fileIDToSave = (String)hashFileMap.get(cmsContentFile.getHash_code() + cmsContentFile.getFile_name());
                     } else {
                        fileIDToSave = UUID.randomUUID().toString().toUpperCase();
                     }

                     fileMap.put("fileId", fileIDToSave);
                     fileMap.put("fileOffset", 0L);
                     fileMap.put("isNew", true);
                     fileMap.put("reqIndex", cmsContentFile.getReqIndex());
                  }

                  cmsContentFile.setFile_id(fileIDToSave);
                  if (i == 0) {
                     content.setMain_file_id(fileIDToSave);
                     String mainFileName = cmsContentFile.getFile_name();
                     fileExtension = mainFileName.substring(mainFileName.lastIndexOf(".") + 1, mainFileName.length());
                     if (content.getMedia_type().equalsIgnoreCase("HTML")) {
                        fileExtension = "HTML";
                     }

                     content.setMain_file_Extension(fileExtension.toUpperCase());
                     cmsContentFile.setFile_type("MAIN");
                  }

                  if (cmsContentFile.getFile_type().equalsIgnoreCase("THUMBNAIL")) {
                     if (content.getThumb_file_id() == null || content.getThumb_file_id().equals("")) {
                        content.setThumb_file_id(fileIDToSave);
                     }

                     cmsContentFile.setFile_type("THUMBNAIL");
                  }

                  if (cmsContentFile.getFile_type().equalsIgnoreCase("SFI")) {
                     content.setSfi_file_id(fileIDToSave);
                     cmsContentFile.setFile_type("SFI");
                  }

                  if (!hashFileMap.containsKey(cmsContentFile.getHash_code() + cmsContentFile.getFile_name())) {
                     hashFileMap.put(cmsContentFile.getHash_code() + cmsContentFile.getFile_name(), fileMap.get("fileId"));
                  }

                  fileMeta.add(fileMap);
                  cmsContentFile.setFile_path(CONTENTS_HOME.replace('/', File.separatorChar) + File.separatorChar + fileIDToSave);
                  cmsContentFile.setCreator_id(userId);
                  fileListToSave.add(cmsContentFile);
               }

               if (!bMustAddContent) {
                  this.setContentGroupId(content, grpId, cmsDao.getRootId(orgCreatorId));
                  cmsDao.setContentInfo(content.getContent_id(), content.getContent_name(), content.getContent_meta_data(), content.getShare_flag());
                  cmsDao.setContentGroup(content.getContent_id(), content.getGroup_id());
               } else {
                  if (!content.getMedia_type().equalsIgnoreCase("IMAGE") && !ContentConstants.getMediaTypeForAuthor().contains(content.getMedia_type()) && !content.getMedia_type().equalsIgnoreCase("MOVIE") && !content.getMedia_type().equalsIgnoreCase("LFD") && !content.getMedia_type().equalsIgnoreCase("LFT") && !content.getMedia_type().equalsIgnoreCase("VWL") && !content.getMedia_type().equalsIgnoreCase("PROM") && !content.getMedia_type().equalsIgnoreCase("HTML") || content.getMedia_type().equalsIgnoreCase("PROM") && (PROMThumbnailType.equalsIgnoreCase("OFFICE") || PROMThumbnailType.equalsIgnoreCase("FLASH") || PROMThumbnailType.equalsIgnoreCase("PDF") || PROMThumbnailType.equalsIgnoreCase("SOUND") || PROMThumbnailType.equalsIgnoreCase("ETC"))) {
                     if (!content.getMedia_type().equalsIgnoreCase("OFFICE") && !PROMThumbnailType.equalsIgnoreCase("OFFICE")) {
                        if (!content.getMedia_type().equalsIgnoreCase("FLASH") && !PROMThumbnailType.equalsIgnoreCase("FLASH")) {
                           if (!content.getMedia_type().equalsIgnoreCase("PDF") && !PROMThumbnailType.equalsIgnoreCase("PDF")) {
                              if (!content.getMedia_type().equalsIgnoreCase("SOUND") && !PROMThumbnailType.equalsIgnoreCase("SOUND")) {
                                 if (content.getMedia_type().equalsIgnoreCase("DLK")) {
                                    template_id = request.getHeader("TEMPLATE_ID");
                                    if (template_id != null) {
                                       Content templateContent = cmsDao.getContentActiveVerInfo(template_id);
                                       if (templateContent != null) {
                                          content.setThumb_file_id(templateContent.getThumb_file_id());
                                          content.setThumb_file_name(templateContent.getThumb_file_name());
                                          content.setDevice_type(templateContent.getDevice_type());
                                          content.setDevice_type_version(templateContent.getDevice_type_version());
                                       } else {
                                          content.setThumb_file_id("ETC_THUMBNAIL");
                                          content.setThumb_file_name("ETC_THUMBNAIL.PNG");
                                       }
                                    } else {
                                       content.setThumb_file_id("ETC_THUMBNAIL");
                                       content.setThumb_file_name("ETC_THUMBNAIL.PNG");
                                    }
                                 } else {
                                    content.setThumb_file_id("ETC_THUMBNAIL");
                                    content.setThumb_file_name("ETC_THUMBNAIL.PNG");
                                 }
                              } else {
                                 content.setThumb_file_id("SOUND_THUMBNAIL");
                                 content.setThumb_file_name("SOUND_THUMBNAIL.PNG");
                              }
                           } else {
                              content.setThumb_file_id("PDF_THUMBNAIL");
                              content.setThumb_file_name("PDF_THUMBNAIL.PNG");
                           }
                        } else {
                           content.setThumb_file_id("FLASH_THUMBNAIL");
                           content.setThumb_file_name("FLASH_THUMBNAIL.PNG");
                        }
                     } else {
                        content.setThumb_file_id("OFFICE_THUMBNAIL");
                        content.setThumb_file_name("OFFICE_THUMBNAIL.PNG");
                     }
                  }

                  this.setContentGroupId(content, grpId, cmsDao.getRootId(orgCreatorId));
                  content.setArr_file_list(fileListToSave);
                  content.setOrganization_id(uInfo.getRootGroupIdByUserId(orgCreatorId));
                  if (content.getThumb_file_id() == null || content.getThumb_file_id().equals("")) {
                     content.setThumb_file_id("ETC_THUMBNAIL");
                     content.setThumb_file_name("ETC_THUMBNAIL.PNG");
                  }

                  i = cmsDao.addContent(content);
                  if (i <= 0) {
                     this.logger.info("Insertion a content Error");
                     response.sendError(603, CMSExceptionCode.APP603[2]);
                     return;
                  }

                  if ("APPROVED".equals(content.getApproval_status())) {
                     PlaylistInfo pInfo = PlaylistInfoImpl.getInstance();
                     Long oldVersion = cmsDao.getContentNextVer(content.getContent_id()) - 1L;
                     Content oldContent = cmsDao.getContentVerInfo(content.getContent_id(), oldVersion);
                     fileExtension = content.getPlay_time();
                     String oldContentDuration = "0";
                     if (oldContent != null) {
                        oldContentDuration = oldContent.getPlay_time();
                     }

                     boolean existPlaytime = true;
                     if (fileExtension == null || fileExtension.equals("") || fileExtension.equals("-")) {
                        if (oldContentDuration == null || oldContentDuration.equals("") || oldContentDuration.equals("-")) {
                           existPlaytime = false;
                        }

                        fileExtension = "00:00:30";
                     }

                     String pretokens = fileExtension.substring(0, 8);
                     String[] tokens = pretokens.split(":");
                     int hours = Integer.parseInt(tokens[0]);
                     int minutes = Integer.parseInt(tokens[1]);
                     int seconds = Integer.parseInt(tokens[2]);
                     int duration = 3600 * hours + 60 * minutes + seconds;
                     List dlkContentList = null;
                     int dlkContentlistSize;
                     int idx;
                     if (existPlaytime) {
                        if (content.getMedia_type().equalsIgnoreCase("LFT")) {
                           dlkContentList = cmsDao.getContentByTemplateId(content.getContent_id());
                           if (dlkContentList != null) {
                              dlkContentlistSize = dlkContentList.size();

                              for(idx = 0; idx < dlkContentlistSize; ++idx) {
                                 pInfo.setContentDuraionByContentID(((Content)dlkContentList.get(idx)).getContent_id(), (long)duration);
                              }
                           }
                        } else {
                           pInfo.setContentDuraionByContentID(content.getContent_id(), (long)duration);
                           pInfo.setContentDuraionMilliByContentID(content.getContent_id(), content.getPlay_time_milli());
                        }
                     }

                     if (dlkContentList != null && content.getMedia_type().equalsIgnoreCase("LFT")) {
                        dlkContentlistSize = dlkContentList.size();

                        for(idx = 0; idx < dlkContentlistSize; ++idx) {
                           List pIDList = cmsDao.getPlaylistListUsingContent(((Content)dlkContentList.get(idx)).getContent_id());
                           if (pIDList != null && pIDList.size() != 0) {
                              for(int j = 0; j < pIDList.size(); ++j) {
                                 Map map = (Map)pIDList.get(j);
                                 String pId = (String)map.get("PLAYLIST_ID");
                                 List pList = pInfo.getPlaylistAllVerInfo(pId);
                                 Iterator var63 = pList.iterator();

                                 while(var63.hasNext()) {
                                    Playlist p = (Playlist)var63.next();
                                    Long playTime = pInfo.getSumOfContentDuration(p.getPlaylist_id(), p.getVersion_id());
                                    long totalPlayTime = 0L;
                                    Long versionId = pInfo.getPlaylistActiveVersionId(pId);
                                    List playlistContents = pInfo.getContentList(pId, versionId);
                                    if (playlistContents != null) {
                                       for(int k = 0; k < playlistContents.size(); ++k) {
                                          PlaylistContent playlistContent = (PlaylistContent)playlistContents.get(k);
                                          if (playlistContent.getContent_duration() != null && !playlistContent.getContent_duration().equals("")) {
                                             totalPlayTime += playlistContent.getContent_duration();
                                          }
                                       }

                                       playTime = totalPlayTime;
                                    }

                                    if (existPlaytime) {
                                       pInfo.setPlaytime(p.getPlaylist_id(), p.getVersion_id(), ContentUtils.getPlayTimeFormattedStr(playTime));
                                    }
                                 }
                              }
                           }
                        }
                     }

                     List pIDList = cmsDao.getPlaylistListUsingContent(content.getContent_id());
                     if (pIDList != null && pIDList.size() != 0) {
                        for(idx = 0; idx < pIDList.size(); ++idx) {
                           Map map = (Map)pIDList.get(idx);
                           String pId = (String)map.get("PLAYLIST_ID");
                           String pType = (String)map.get("PLAYLIST_TYPE");
                           List pList = pInfo.getPlaylistAllVerInfo(pId);
                           Iterator var121 = pList.iterator();

                           while(var121.hasNext()) {
                              Playlist p = (Playlist)var121.next();
                              Long playTime = pInfo.getSumOfContentDuration(p.getPlaylist_id(), p.getVersion_id());
                              if ("5".equals(pType)) {
                                 long totalPlayTime = 0L;
                                 Long versionId = pInfo.getPlaylistActiveVersionId(pId);
                                 List playlistContents = pInfo.getTagPlaylistTagList(pId, versionId);
                                 if (playlistContents != null) {
                                    for(int k = 0; k < playlistContents.size(); ++k) {
                                       PlaylistContent playlistContent = (PlaylistContent)playlistContents.get(k);
                                       long tagId = playlistContent.getTag_id();
                                       ContentInfo contentInfo = ContentInfoImpl.getInstance();
                                       List involvedContents = contentInfo.getTagContentList(tagId);
                                       if (involvedContents != null) {
                                          for(int l = 0; l < involvedContents.size(); ++l) {
                                             String contentId = ((Content)involvedContents.get(l)).getContent_id();
                                             Content involvedContent = contentInfo.getContentActiveVerInfo(contentId);
                                             if (involvedContent.getPlay_time() != null && !involvedContent.getPlay_time().equals("")) {
                                                String[] arrPlayTime = involvedContent.getPlay_time().split(":");
                                                Long hh = Long.parseLong(arrPlayTime[0]) * 3600L;
                                                Long mm = Long.parseLong(arrPlayTime[1]) * 60L;
                                                Long dd = Long.parseLong(arrPlayTime[2]);
                                                totalPlayTime += hh + mm + dd;
                                             } else {
                                                totalPlayTime += playlistContent.getTag_duration();
                                             }
                                          }
                                       }
                                    }
                                 }

                                 playTime = totalPlayTime;
                              }

                              if (existPlaytime) {
                                 pInfo.setPlaytime(p.getPlaylist_id(), p.getVersion_id(), ContentUtils.getPlayTimeFormattedStr(playTime));
                              }
                           }
                        }
                     }
                  }
               }
            }
         }

         strHost = request.getServerName();
         strPort = String.valueOf(request.getServerPort());
         if ("80".equals(strPort)) {
            strPort = "";
         } else {
            strPort = ":" + strPort;
         }

         String strContextPath = request.getContextPath();
         String url = RequestUtils.getProtocolScheme(request) + strHost + strPort + strContextPath;
         response.setHeader("REDIRECT_URL", url + "/login.htm?cmd=NotNormal&user_id=" + userId + "&password=" + token);
         content_play_time = "";
         if (pCategory != null && Long.parseLong(pCategory) == 0L) {
            content_play_time = cid + "&group_id=" + pCategory + "&group_type=" + "UNGROUPED";
         } else {
            content_play_time = cid + "&group_id=" + pCategory + "&group_type=" + "GROUPED";
         }

         response.setHeader("CID", content_play_time);
         response.setHeader("VERSION_ID", version.toString());
         response.setHeader("TARGET_DIR", cid);
         if (bMustAddContent) {
            response.setHeader("CONTENT_DUP", "NOT_DUPLICATE");
         } else {
            response.setHeader("CONTENT_DUP", "DUPLICATE");
         }

         ContentMetaManager mgr = new ContentMetaManager();
         contentMeta = mgr.createContentMetaResponse(cid, fileMeta);
         out.println(contentMeta);
      } catch (Exception var90) {
         response.sendError(600, var90.toString());
         response.setHeader("code", "600");
         response.setHeader("message", "error : " + var90.getMessage());
         this.logger.error("", var90);
      }

   }
}
