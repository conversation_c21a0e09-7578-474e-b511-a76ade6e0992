package com.samsung.magicinfo.webauthor2.service.thumbnail;

import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import java.awt.AlphaComposite;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.RenderingHints;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.awt.image.ImageObserver;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.Iterator;
import java.util.NoSuchElementException;
import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service
public class ImageThumbnailServiceImpl implements ImageThumbnailService {
  private static Logger logger = LoggerFactory.getLogger(ImageThumbnailServiceImpl.class);
  
  public void createImageThumbnail(MediaSource mediaSource, Path thumbnailPath) throws UploaderException {
    ImageReader reader = null;
    String ext = mediaSource.getFileType();
    try(FileInputStream fileInputStream = new FileInputStream(Paths.get(mediaSource.getPath(), new String[0]).toFile()); 
        ImageInputStream iis = ImageIO.createImageInputStream(fileInputStream)) {
      int widthBig, heightBig;
      Assert.notNull(iis, "Cant't acquire ImageInputStream from image");
      Iterator<ImageReader> readers = ImageIO.getImageReaders(iis);
      reader = readers.next();
      iis.mark();
      reader.setInput(iis, false);
      BufferedImage bufferedImage = reader.read(0);
      int width = bufferedImage.getWidth();
      int height = bufferedImage.getHeight();
      if ((width / 438) > (height / 258)) {
        widthBig = 438;
        heightBig = height * 438 / width;
        if (heightBig % 2 != 0)
          heightBig++; 
      } else {
        widthBig = width * 258 / height;
        heightBig = 258;
        if (widthBig % 2 != 0)
          widthBig++; 
      } 
      Path parent = thumbnailPath.getParent();
      if (parent != null && Files.notExists(parent, new java.nio.file.LinkOption[0]))
        Files.createDirectories(parent, (FileAttribute<?>[])new FileAttribute[0]); 
      File thumbnailFile = thumbnailPath.toFile();
      if (ext.equalsIgnoreCase("png") || ext.equalsIgnoreCase("gif")) {
        ImageIO.write(resizePNG(bufferedImage, widthBig, heightBig), "PNG", thumbnailFile);
      } else {
        ImageIO.write(createResizedCopy(bufferedImage, widthBig, heightBig), "PNG", thumbnailFile);
      } 
    } catch (NoSuchElementException ex) {
      logger.error(ex.getMessage());
      throw new UploaderException(699, "UploadFileCorruptError");
    } catch (IOException e) {
      logger.error(e.getMessage());
      throw new UploaderException(699, "ServerInternalUploadError");
    } finally {
      if (reader != null)
        reader.dispose(); 
    } 
  }
  
  private BufferedImage createResizedCopy(Image originalImage, int scaledWidth, int scaledHeight) {
    BufferedImage scaledBI = new BufferedImage(scaledWidth, scaledHeight, 1);
    Graphics2D g = scaledBI.createGraphics();
    g.setComposite(AlphaComposite.Src);
    g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
    g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
    g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
    g.drawImage(originalImage, 0, 0, scaledWidth, scaledHeight, null);
    g.dispose();
    return scaledBI;
  }
  
  private BufferedImage resizePNG(BufferedImage originalImage, int scaledWidth, int scaledHeight) {
    BufferedImage bufferedThumbnail = new BufferedImage(scaledWidth, scaledHeight, 2);
    Graphics2D graphics2D = bufferedThumbnail.createGraphics();
    double scaleW = scaledWidth / originalImage.getWidth();
    double scaleH = scaledHeight / originalImage.getHeight();
    AffineTransform xform = AffineTransform.getScaleInstance(scaleW, scaleH);
    graphics2D.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
    graphics2D.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
    graphics2D.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
    graphics2D.drawImage(originalImage, xform, (ImageObserver)null);
    graphics2D.dispose();
    return bufferedThumbnail;
  }
}
