package com.samsung.magicinfo.webauthor2.service;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.model.MagicInfoUserAuthority;
import com.samsung.magicinfo.webauthor2.model.MagicInfoUserRole;
import com.samsung.magicinfo.webauthor2.repository.OpenAPIUserInfoRepository;
import com.samsung.magicinfo.webauthor2.repository.model.UserInfo;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service
public class UserServiceImpl implements UserService {
  private OpenAPIUserInfoRepository openAPIUserInfoRepository;
  
  private List<String> authorities = null;
  
  @Autowired
  public UserServiceImpl(OpenAPIUserInfoRepository openAPIUserInfoRepository) {
    this.openAPIUserInfoRepository = openAPIUserInfoRepository;
  }
  
  public boolean isUserAdmin(String userId) {
    Assert.isTrue(!Strings.isNullOrEmpty(userId), "Empty or null userId!");
    UserInfo userInfo = this.openAPIUserInfoRepository.getUserInfo(userId);
    String roleName = userInfo.getRoleName();
    return isAdmin(roleName);
  }
  
  public UserInfo getUserInfo(String userId) {
    Assert.isTrue(!Strings.isNullOrEmpty(userId), "Empty or null userId!");
    UserInfo userInfo = this.openAPIUserInfoRepository.getUserInfo(userId);
    return userInfo;
  }
  
  private boolean isAdmin(String roleName) {
    return (roleName.equals(MagicInfoUserRole.SERVER_ADMINISTRATOR.toString()) || roleName
      .equals(MagicInfoUserRole.ADMINISTRATOR.toString()));
  }
  
  public void setUserAuthority(String userId) {
    Assert.isTrue(!Strings.isNullOrEmpty(userId), "Empty or null userId!");
    UserInfo userInfo = this.openAPIUserInfoRepository.getUserInfo(userId);
    this.authorities = userInfo.getList();
  }
  
  public boolean hasContentLockAuthority() {
    for (String authority : this.authorities) {
      if (authority.equals(MagicInfoUserAuthority.CONTENT_LOCK_AUTHORITY.toString()))
        return true; 
    } 
    return false;
  }
  
  public boolean hasContentUploadAuthority() {
    for (String authority : this.authorities) {
      if (authority.equals(MagicInfoUserAuthority.CONTENT_UPLOAD_AUTHORITY.toString()))
        return true; 
    } 
    return false;
  }
  
  public boolean hasContentAddElementAuthority() {
    for (String authority : this.authorities) {
      if (authority.equals(MagicInfoUserAuthority.CONTENT_ADD_ELEMENT_AUTHORITY.toString()))
        return true; 
    } 
    return false;
  }
}
