package com.samsung.magicinfo.webauthor2.util;

import com.samsung.magicinfo.webauthor2.exception.util.BadMisUrlException;
import java.net.URI;
import java.net.URISyntaxException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpMethod;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.ResponseExtractor;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

@Component
@Primary
public class MisRestTemplate extends RestTemplate {
  private ServerInfo serverInfo;
  
  private static final Logger logger = LoggerFactory.getLogger(MisRestTemplate.class);
  
  @Autowired
  public MisRestTemplate(ClientHttpRequestFactory requestFactory, ServerInfo serverInfo) {
    super(requestFactory);
    this.serverInfo = serverInfo;
  }
  
  protected <T> T doExecute(URI url, HttpMethod method, RequestCallback requestCallback, ResponseExtractor<T> responseExtractor) throws RestClientException {
    String baseUrl = this.serverInfo.getServerUrl();
    try {
      URI uri = new URI(baseUrl + url.toString());
      logger.debug("{} {}", method.toString(), uri.toString());
      return (T)super.doExecute(uri, method, requestCallback, responseExtractor);
    } catch (URISyntaxException e) {
      logger.error("Exception {}", e.toString());
      throw new BadMisUrlException("MagicInfo Url is not valid: " + baseUrl + url);
    } 
  }
}
