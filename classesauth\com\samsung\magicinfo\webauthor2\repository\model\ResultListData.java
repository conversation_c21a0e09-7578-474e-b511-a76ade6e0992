package com.samsung.magicinfo.webauthor2.repository.model;

import java.io.Serializable;
import java.util.List;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;

public class ResultListData implements Serializable {
  @XmlElement
  private Long totalCount;
  
  @XmlElementWrapper(name = "resultList")
  @XmlElement(name = "Content")
  private List<ContentData> resultList;
  
  public Long getTotalCount() {
    return this.totalCount;
  }
  
  public List<ContentData> getResultList() {
    return this.resultList;
  }
}
