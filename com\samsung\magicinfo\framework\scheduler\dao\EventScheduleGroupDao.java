package com.samsung.magicinfo.framework.scheduler.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SequenceDB;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramGroup;
import com.samsung.magicinfo.protocol.exception.ActionNotSupportedException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class EventScheduleGroupDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(EventScheduleGroupDao.class);

   public EventScheduleGroupDao() {
      super();
   }

   public EventScheduleGroupDao(SqlSession session) {
      super(session);
   }

   public int addGroup(ProgramGroup programGroup) throws SQLException, ActionNotSupportedException {
      if (programGroup.getP_group_id() >= -1L && programGroup.getP_group_id() != 999999L) {
         SqlSession session = this.openNewSession(false);

         byte var4;
         try {
            int group_id = false;
            int group_id = this.createNewGroupId(session);
            if (group_id != -1) {
               if (((EventScheduleGroupDaoMapper)this.getMapper(session)).addGroup(new Long((long)group_id), programGroup) > 0) {
                  session.commit();
                  session.close();
                  int var11 = group_id;
                  return var11;
               }

               session.rollback();
               session.close();
               var4 = -1;
               return var4;
            }

            session.rollback();
            session.close();
            var4 = -1;
         } catch (SQLException var8) {
            session.rollback();
            throw var8;
         } finally {
            if (session != null) {
               session.close();
            }

         }

         return var4;
      } else {
         throw new ActionNotSupportedException("MESSAGE_SCHEDULE_CANT_ADD_GROUP_SEL_LOCATION_P");
      }
   }

   public boolean delGroup(int group_id) throws SQLException {
      List groupList = this.getChildGroupIdList(group_id, true);
      int programOrgGroupId = this.getProgramOrgGroupId(group_id);
      SqlSession session = this.openNewSession(false);

      boolean var13;
      try {
         Iterator var5 = groupList.iterator();

         while(var5.hasNext()) {
            Long groupId = (Long)var5.next();
            ((EventScheduleGroupDaoMapper)this.getMapper(session)).update_delGroup(groupId);
            ((EventScheduleGroupDaoMapper)this.getMapper(session)).update_delGroup2(programOrgGroupId, groupId);
            if (((EventScheduleGroupDaoMapper)this.getMapper(session)).delete_delGroup(groupId) <= 0) {
               session.rollback();
               session.close();
               boolean var7 = false;
               return var7;
            }
         }

         ((EventScheduleGroupDaoMapper)this.getMapper(session)).update_delGroup(new Long((long)group_id));
         ((EventScheduleGroupDaoMapper)this.getMapper(session)).update_delGroup2(programOrgGroupId, new Long((long)group_id));
         if (((EventScheduleGroupDaoMapper)this.getMapper(session)).delete_delGroup(new Long((long)group_id)) > 0) {
            return true;
         }

         session.rollback();
         session.close();
         var13 = false;
      } catch (SQLException var11) {
         session.rollback();
         session.close();
         throw var11;
      } finally {
         session.commit();
         session.close();
      }

      return var13;
   }

   public String getProgramOrgNameByGroupId(Long group_id) throws SQLException {
      Map groupMap = ((EventScheduleGroupDaoMapper)this.getMapper()).getProgramOrgNameByGroupId(group_id);
      if (groupMap == null) {
         return null;
      } else {
         return (Long)groupMap.get("group_depth") <= 1L ? (String)groupMap.get("group_name") : this.getProgramOrgNameByGroupId((Long)groupMap.get("p_group_id"));
      }
   }

   public boolean delDeviceWithContents(int group_id) throws SQLException {
      ((EventScheduleGroupDaoMapper)this.getMapper()).update_delDeviceWithContents("dd328c1a-19ab-4bd8-a604-5073dadd1383", group_id);
      return true;
   }

   public List getDeviceGroupMappedInProgramByGroupId(long g_id) throws SQLException {
      return ((EventScheduleGroupDaoMapper)this.getMapper()).getDeviceGroupMappedInProgramByGroupId(g_id);
   }

   public List getChildProgramList(int group_id, boolean recursive) throws SQLException {
      List programList = ((EventScheduleGroupDaoMapper)this.getMapper()).getChildProgramList(group_id);
      if (recursive) {
         List groupIdList = this.getChildGroupIdList(group_id, true);
         Iterator var5 = groupIdList.iterator();

         while(var5.hasNext()) {
            Long group = (Long)var5.next();
            List subGroupProgramList = this.getChildProgramList(group.intValue(), false);
            if (subGroupProgramList != null && subGroupProgramList.size() != 0) {
               programList.addAll(subGroupProgramList);
            }
         }
      }

      return programList;
   }

   public List getActiveChildProgramList(int group_id, boolean recursive) throws SQLException {
      List programList = ((EventScheduleGroupDaoMapper)this.getMapper()).getActiveChildProgramList(group_id);
      if (recursive) {
         List groupIdList = this.getChildGroupIdList(group_id, true);
         Iterator iter = groupIdList.iterator();

         while(iter.hasNext()) {
            Long group = (Long)iter.next();
            List subGroupProgramList = this.getActiveChildProgramList(group.intValue(), false);
            if (subGroupProgramList != null && subGroupProgramList.size() != 0) {
               programList.addAll(subGroupProgramList);
            }
         }
      }

      return programList;
   }

   protected List getChildGroupIdList(int group_id, boolean recursive) throws SQLException {
      List rtList = new ArrayList();
      List groupIdList = ((EventScheduleGroupDaoMapper)this.getMapper()).getChildGroupIdList(group_id, 999999);
      if (groupIdList != null) {
         for(int i = 0; i < groupIdList.size(); ++i) {
            if (recursive) {
               Long group = (Long)((Map)groupIdList.get(i)).get("group_id");
               rtList.add(group);
               List temp = this.getChildGroupIdList(group.intValue(), recursive);
               if (temp != null && temp.size() != 0) {
                  rtList.addAll(temp);
               }
            } else {
               rtList.add((Long)((Map)groupIdList.get(i)).get("group_id"));
            }
         }
      }

      return rtList;
   }

   public List getChildGroupList(int group_id, boolean recursive) throws SQLException {
      List tmp = new ArrayList();
      List groupList = ((EventScheduleGroupDaoMapper)this.getMapper()).getChildGroupList(group_id, 999999);
      if (!recursive) {
         return groupList;
      } else {
         Iterator iter = groupList.iterator();

         while(iter.hasNext()) {
            ProgramGroup programGroup = (ProgramGroup)iter.next();
            tmp.add(programGroup);
            tmp.addAll(this.getChildGroupList(programGroup.getGroup_id().intValue(), recursive));
         }

         return tmp;
      }
   }

   public List getChildGroupList(int group_id, String organization) throws SQLException {
      List groupList = ((EventScheduleGroupDaoMapper)this.getMapper()).getChildGroupList(group_id, 999999);
      return groupList;
   }

   public List getChildGroupList(int group_id, boolean recursive, String deviceType) throws SQLException {
      List tmp = new ArrayList();
      if (deviceType.equals("") || deviceType.equals((Object)null)) {
         deviceType = "iPLAYER";
      }

      List groupList = ((EventScheduleGroupDaoMapper)this.getMapper()).getChildGroupList1(group_id, 999999, deviceType);
      if (!recursive) {
         return groupList;
      } else {
         Iterator var6 = groupList.iterator();

         while(var6.hasNext()) {
            ProgramGroup programGroup = (ProgramGroup)var6.next();
            tmp.add(programGroup);
            tmp.addAll(this.getChildGroupList(programGroup.getGroup_id().intValue(), recursive, deviceType));
         }

         return tmp;
      }
   }

   public ProgramGroup getGroup(int group_id) throws SQLException {
      return ((EventScheduleGroupDaoMapper)this.getMapper()).getGroup(group_id);
   }

   public int getOrgGroupIdByOrgGroupName(String orgGroupName) throws SQLException {
      if (orgGroupName.equalsIgnoreCase("ROOT")) {
         return 0;
      } else {
         Long orgGroupId = ((EventScheduleGroupDaoMapper)this.getMapper()).getOrgGroupIdByOrgGroupName(orgGroupName);
         return orgGroupId == null ? -1 : orgGroupId.intValue();
      }
   }

   public List getGroupList() throws SQLException {
      return ((EventScheduleGroupDaoMapper)this.getMapper()).getGroupList();
   }

   public List getOrganizationGroup(String groupName) throws SQLException {
      return ((EventScheduleGroupDaoMapper)this.getMapper()).getOrganizationGroup(groupName);
   }

   private int createNewGroupId(SqlSession session) throws SQLException {
      int id = false;
      int id = SequenceDB.getNextValue("MI_EVENT_INFO_SCHEDULE_GROUP");
      return id;
   }

   public int getProgramOrgGroupId(int groupId) throws SQLException {
      Map info = ((EventScheduleGroupDaoMapper)this.getMapper()).getProgramOrgGroupId(groupId);
      int newGroupParentId = ((Long)info.get("P_GROUP_ID")).intValue();
      return newGroupParentId != 0 ? this.getProgramOrgGroupId(newGroupParentId) : ((Long)info.get("GROUP_ID")).intValue();
   }

   public int getProgramGroupForOrg(String strOrg) throws SQLException {
      int groupId = ((EventScheduleGroupDaoMapper)this.getMapper()).getProgramGroupForOrg(strOrg);
      return groupId;
   }

   public boolean addGroupForOrg(String strOrgName) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var9;
      try {
         int group_id_base = false;
         int cnt2 = false;
         int group_id = this.createNewGroupId(session);
         int group_id_base = this.createNewGroupId(session);
         if (group_id == -1 || group_id_base == -1) {
            session.rollback();
            boolean var17 = false;
            return var17;
         }

         ProgramGroup programGroup1 = new ProgramGroup();
         ProgramGroup programGroup2 = new ProgramGroup();
         programGroup1.setP_group_id(0L);
         programGroup1.setGroup_depth(1L);
         programGroup1.setGroup_name(strOrgName);
         programGroup1.setDescription("Organization");
         int cnt = ((EventScheduleGroupDaoMapper)this.getMapper(session)).addGroup(new Long((long)group_id), programGroup1);
         if (cnt > 0) {
            session.commit();
         }

         programGroup2.setP_group_id(new Long((long)group_id));
         programGroup2.setGroup_depth(2L);
         programGroup2.setGroup_name("default");
         programGroup2.setDescription("Default Group");
         int cnt2 = ((EventScheduleGroupDaoMapper)this.getMapper(session)).addGroup(new Long((long)group_id_base), programGroup2);
         if (cnt2 > 0) {
            session.commit();
         }

         if (cnt2 > 0 && cnt > 0) {
            var9 = true;
            return var9;
         }

         session.rollback();
         var9 = false;
      } catch (SQLException var13) {
         session.rollback();
         throw var13;
      } finally {
         session.close();
      }

      return var9;
   }

   public List getGroupIdByOrganization(int groupId) throws SQLException {
      Object rtn = new ArrayList();

      try {
         rtn = ((EventScheduleGroupDaoMapper)this.getMapper()).getGroupIdByOrganizationGroupIdWithRecursive(groupId);
      } catch (Exception var7) {
         this.logger.info("[MagicInfo_ProgramGroup] not support with query");
         List list = this.getChildGroupList(groupId, true);
         if (list != null && list.size() > 0) {
            rtn = new ArrayList();
            Iterator var5 = list.iterator();

            while(var5.hasNext()) {
               ProgramGroup group = (ProgramGroup)var5.next();
               ((List)rtn).add(group.getGroup_id());
            }
         }
      }

      return (List)rtn;
   }

   public int getEventScheduleGroupTotalCount() throws SQLException {
      return ((EventScheduleGroupDaoMapper)this.getMapper()).getEventScheduleGroupTotalCount();
   }
}
