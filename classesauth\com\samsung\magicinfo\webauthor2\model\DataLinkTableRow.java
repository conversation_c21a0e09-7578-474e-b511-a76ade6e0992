package com.samsung.magicinfo.webauthor2.model;

import com.samsung.magicinfo.webauthor2.repository.model.dlk.DLKTableRowData;
import java.util.ArrayList;
import java.util.List;

public class DataLinkTableRow {
  private final List<DataLinkTableColumn> columns;
  
  public DataLinkTableRow(List<DataLinkTableColumn> columns) {
    this.columns = columns;
  }
  
  public List<DataLinkTableColumn> getColumns() {
    return this.columns;
  }
  
  public static DataLinkTableRow fromData(DLKTableRowData data) {
    return new DataLinkTableRow(DataLinkTableColumn.fromData(data.getColumns()));
  }
  
  public static List<DataLinkTableRow> fromData(List<DLKTableRowData> dataList) {
    List<DataLinkTableRow> rows = new ArrayList<>();
    for (DLKTableRowData data : dataList)
      rows.add(fromData(data)); 
    return rows;
  }
}
