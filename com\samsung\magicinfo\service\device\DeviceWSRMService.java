package com.samsung.magicinfo.service.device;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemInfoConf;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManager;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManagerImpl;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import javax.servlet.http.HttpServletRequest;
import org.apache.logging.log4j.Logger;
import org.directwebremoting.WebContext;
import org.directwebremoting.WebContextFactory;

public class DeviceWSRMService {
   Logger logger = LoggingManagerV2.getLogger(DeviceWSRMService.class);

   public DeviceWSRMService() {
      super();
   }

   public DeviceSystemInfoConf getDeviceUsage(String deviceId) {
      WebContext ctx = WebContextFactory.get();
      HttpServletRequest request = ctx.getHttpServletRequest();
      DeviceSystemInfoConf deviceSystemInfoConf = null;
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      String sId = request.getSession().getId();

      try {
         confMgr.reqGetSystemInfoFromDevice(deviceId, sId);

         for(int i = 0; i < 30; ++i) {
            deviceSystemInfoConf = confMgr.getSystemInfoResultSet(deviceId, sId, "GET_DEVICE_SYSTEM_INFO_CONF");
            if (deviceSystemInfoConf != null) {
               break;
            }

            Thread.sleep(1000L);
         }
      } catch (Exception var11) {
         this.logger.error("reqGetSystemInfo failed.");
      }

      if (deviceSystemInfoConf == null) {
         deviceSystemInfoConf = new DeviceSystemInfoConf();
         deviceSystemInfoConf.setCpu_usage(0L);
         deviceSystemInfoConf.setMem_size(0L);
         deviceSystemInfoConf.setNetwork_usage(0L);
         SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
         Calendar cal = Calendar.getInstance();
         String today = formatter.format(cal.getTime());
         Timestamp ts = Timestamp.valueOf(today);
         deviceSystemInfoConf.setSystem_time(ts);
         this.logger.error("info is null. set default!!!");
      }

      return deviceSystemInfoConf;
   }

   public String getSoftwareVersion(String zipFile) {
      String VER_FILE_NAME = "info.txt";
      boolean rstFlag = false;
      String version = "";
      int readZipFileCnt = 0;
      String dirName = "C:\tmp";
      if (zipFile.lastIndexOf(File.separator) <= 0) {
         File f = SecurityUtils.getSafeFile(dirName);
         f.mkdirs();
      } else {
         dirName = zipFile.substring(0, zipFile.lastIndexOf(File.separator));
      }

      ZipInputStream in = null;
      FileInputStream zip = null;
      FileOutputStream out = null;

      String entryName;
      try {
         zip = new FileInputStream(zipFile);
         in = new ZipInputStream(zip);
         ZipEntry entry = null;

         while((entry = in.getNextEntry()) != null) {
            ++readZipFileCnt;
            entryName = entry.getName();
            if (entryName.equals("info.txt")) {
               out = new FileOutputStream(dirName + File.separator + entryName);
               byte[] buf = new byte[1024];

               int len;
               while((len = in.read(buf)) > 0) {
                  out.write(buf, 0, len);
               }

               rstFlag = true;
            }

            if (rstFlag) {
               break;
            }
         }
      } catch (Exception var40) {
         this.logger.error("zip file extract fail!!!", var40);
      } finally {
         try {
            if (in != null) {
               in.close();
               in.closeEntry();
            }

            if (out != null) {
               out.close();
            }

            if (zip != null) {
               zip.close();
            }
         } catch (Exception var36) {
            this.logger.error("", var36);
         }

      }

      BufferedReader read = null;

      try {
         if (rstFlag) {
            File vFile = SecurityUtils.getSafeFile(dirName + File.separator + "info.txt");
            if (vFile.exists()) {
               read = new BufferedReader(new FileReader(vFile.getPath()));
               version = read.readLine();
               vFile.delete();
            }
         }
      } catch (Exception var38) {
         this.logger.error("version file readline fail!!!", var38);
      } finally {
         try {
            if (read != null) {
               read.close();
               entryName = null;
            }
         } catch (Exception var37) {
            this.logger.error("", var37);
         }

      }

      this.logger.error("getSoftWare Result.\nzipFile:" + zipFile + "\nDir=" + dirName + "\nreadZipFile:" + readZipFileCnt + "\nversionFileExist:" + rstFlag);
      return version;
   }
}
