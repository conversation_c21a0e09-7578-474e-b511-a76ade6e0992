package com.samsung.magicinfo.webauthor2.util;

import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import com.sun.jna.WString;
import com.sun.jna.platform.win32.Shell32;
import com.sun.jna.platform.win32.WinDef;
import com.sun.jna.platform.win32.WinNT;
import com.sun.jna.platform.win32.WinReg;
import com.sun.jna.win32.W32APIOptions;
import java.util.Arrays;
import java.util.List;

public interface Shell32X extends Shell32 {
  public static final Shell32X INSTANCE = (Shell32X)Native.loadLibrary("shell32", Shell32X.class, W32APIOptions.UNICODE_OPTIONS);
  
  public static final int SW_HIDE = 0;
  
  public static final int SW_MAXIMIZE = 3;
  
  public static final int SW_MINIMIZE = 6;
  
  public static final int SW_RESTORE = 9;
  
  public static final int SW_SHOW = 5;
  
  public static final int SW_SHOWDEFAULT = 10;
  
  public static final int SW_SHOWMAXIMIZED = 3;
  
  public static final int SW_SHOWMINIMIZED = 2;
  
  public static final int SW_SHOWMINNOACTIVE = 7;
  
  public static final int SW_SHOWNA = 8;
  
  public static final int SW_SHOWNOACTIVATE = 4;
  
  public static final int SW_SHOWNORMAL = 1;
  
  public static final int SE_ERR_FNF = 2;
  
  public static final int SE_ERR_PNF = 3;
  
  public static final int SE_ERR_ACCESSDENIED = 5;
  
  public static final int SE_ERR_OOM = 8;
  
  public static final int SE_ERR_DLLNOTFOUND = 32;
  
  public static final int SE_ERR_SHARE = 26;
  
  public static final int SEE_MASK_NOCLOSEPROCESS = 64;
  
  int ShellExecute(int paramInt1, String paramString1, String paramString2, String paramString3, String paramString4, int paramInt2);
  
  boolean ShellExecuteEx(SHELLEXECUTEINFO paramSHELLEXECUTEINFO);
  
  public static class SHELLEXECUTEINFO extends Structure {
    public int cbSize = size();
    
    public int fMask;
    
    public WinDef.HWND hwnd;
    
    public WString lpVerb;
    
    public WString lpFile;
    
    public WString lpParameters;
    
    public WString lpDirectory;
    
    public int nShow;
    
    public WinDef.HINSTANCE hInstApp;
    
    public Pointer lpIDList;
    
    public WString lpClass;
    
    public WinReg.HKEY hKeyClass;
    
    public int dwHotKey;
    
    public WinNT.HANDLE hMonitor;
    
    public WinNT.HANDLE hProcess;
    
    protected List getFieldOrder() {
      return Arrays.asList(new String[] { 
            "cbSize", "fMask", "hwnd", "lpVerb", "lpFile", "lpParameters", "lpDirectory", "nShow", "hInstApp", "lpIDList", 
            "lpClass", "hKeyClass", "dwHotKey", "hMonitor", "hProcess" });
    }
  }
}
