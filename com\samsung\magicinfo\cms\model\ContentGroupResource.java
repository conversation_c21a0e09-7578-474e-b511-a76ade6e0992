package com.samsung.magicinfo.cms.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.sql.Timestamp;

public class ContentGroupResource {
   private Long index = -10L;
   private Long groupId = 0L;
   private Long parentGroupId = 0L;
   private Long groupDepth = 0L;
   @ApiModelProperty(
      example = "newGroup"
   )
   private String groupName = "";
   @ApiModelProperty(
      example = "admin"
   )
   private String creatorId = "";
   @ApiModelProperty(
      example = "2016-01-01 00:00:00"
   )
   @JsonFormat(
      pattern = "yyyy-mm-dd hh:mm:ss"
   )
   private Timestamp createDate;
   private long organizationId;

   public ContentGroupResource() {
      super();
      Timestamp tmp = new Timestamp(0L);
      this.createDate = tmp;
      this.organizationId = 0L;
   }

   public Long getIndex() {
      return this.index;
   }

   public void setIndex(Long index) {
      this.index = index;
   }

   public Long getGroupId() {
      return this.groupId;
   }

   public void setGroupId(Long groupId) {
      this.groupId = groupId;
   }

   public Long getParentGroupId() {
      return this.parentGroupId;
   }

   public void setParentGroupId(Long parentGroupId) {
      this.parentGroupId = parentGroupId;
   }

   public Long getGroupDepth() {
      return this.groupDepth;
   }

   public void setGroupDepth(Long groupDepth) {
      this.groupDepth = groupDepth;
   }

   public String getGroupName() {
      return this.groupName;
   }

   public void setGroupName(String groupName) {
      this.groupName = groupName;
   }

   public String getCreatorId() {
      return this.creatorId;
   }

   public void setCreatorId(String creatorId) {
      this.creatorId = creatorId;
   }

   public Timestamp getCreateDate() {
      return this.createDate;
   }

   public void setCreateDate(Timestamp createDate) {
      this.createDate = createDate;
   }

   public long getOrganizationId() {
      return this.organizationId;
   }

   public void setOrganizationId(long organizationId) {
      this.organizationId = organizationId;
   }
}
