package com.samsung.magicinfo.webauthor2.model.datalink;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SyncGroup {
  private final int id;
  
  private final List<SplitGroup> splitGroups;
  
  @JsonCreator
  public SyncGroup(@JsonProperty("id") int id, @JsonProperty("splitGroups") List<SplitGroup> splitGroups) {
    this.id = id;
    this.splitGroups = splitGroups;
  }
  
  public int getId() {
    return this.id;
  }
  
  public List<SplitGroup> getSplitGroups() {
    return this.splitGroups;
  }
}
