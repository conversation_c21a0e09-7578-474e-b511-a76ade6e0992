package com.samsung.magicinfo.openapi.scenario.model;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.openapi.impl.ClassUtil;
import com.samsung.magicinfo.openapi.scenario.exception.ItemValidationException;
import com.samsung.magicinfo.openapi.scenario.info.ActionItem;
import com.samsung.magicinfo.openapi.scenario.info.ActionParameter;
import com.samsung.magicinfo.openapi.scenario.info.Entry;
import com.samsung.magicinfo.openapi.scenario.info.EntryField;
import com.samsung.magicinfo.openapi.scenario.info.Field;
import com.samsung.magicinfo.openapi.scenario.info.ScenarioItem;
import com.samsung.magicinfo.openapi.scenario.info.Scenarios;
import com.samsung.magicinfo.openapi.scenario.info.SupportedValueField;
import com.samsung.magicinfo.openapi.scenario.info.TypedField;
import com.samsung.magicinfo.openapi.scenario.result.ActionItemResult;
import com.samsung.magicinfo.openapi.scenario.result.ResultStatus;
import com.samsung.magicinfo.openapi.scenario.type.ScenarioBuilder;
import com.samsung.magicinfo.openapi.scenario.type.SupporterTypeInfo;
import com.samsung.magicinfo.openapi.scenario.type.TypeInfoHolder;
import java.io.File;
import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URISyntaxException;
import java.net.URL;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import org.apache.commons.beanutils.MethodUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

@Service("scenarioOperationProvider")
public class ScenarioOperationProvider {
   static Logger logger = LoggingManagerV2.getLogger(ScenarioOperationProvider.class);
   private static final String OPEN_API_SERVICE_PREFIX = "openApi";
   @Autowired
   private ApplicationContext appContext;
   @Autowired
   private TypeInfoHolder typeInfoHolder;
   @Autowired
   private ScenarioBuilder scenarioBuilder;

   public ScenarioOperationProvider() {
      super();
   }

   public Map getAvailableServices() {
      return this.typeInfoHolder.getServicesMap();
   }

   public Map execute(List scenariosToExecute, String token) {
      Map scenarioResMap = new LinkedHashMap();
      Iterator var4 = scenariosToExecute.iterator();

      while(var4.hasNext()) {
         ScenarioItem scenarioItem = (ScenarioItem)var4.next();
         List actionItemList = scenarioItem.getActionItem();
         List actionResultList = new ArrayList();
         Iterator var8 = actionItemList.iterator();

         while(var8.hasNext()) {
            ActionItem ai = (ActionItem)var8.next();
            List aiParams = ai.getActionParameter();
            Map parameters = new LinkedHashMap();
            String serviceName = ai.getService();
            String methodName = ai.getMethod();

            try {
               this.validateActionItem(token, serviceName, methodName, ai);

               for(int iter = 0; iter < aiParams.size(); ++iter) {
                  ActionParameter actionParam = (ActionParameter)aiParams.get(iter);
                  String type = this.scenarioBuilder.getOriginalType(actionParam.getType());
                  List content = actionParam.getContent();
                  if (content.size() > 0) {
                     Object paramObject = null;
                     if (!(content.get(0) instanceof Field)) {
                        if (content.get(0) instanceof String) {
                           if (actionParam.isDependencyResolveRequired()) {
                              if (!parameters.containsKey(actionParam.getAlias())) {
                                 paramObject = this.getAliasObjectValue(actionParam);
                                 parameters.put(actionParam.getAlias(), paramObject);
                              }
                           } else if (!parameters.containsKey(actionParam.getName())) {
                              paramObject = this.getObjectValue(actionParam, (Serializable)content.get(0));
                              parameters.put(actionParam.getName(), paramObject);
                           }
                        }
                     } else {
                        Map fieldKeyValueMap = new LinkedHashMap();
                        Iterator var20 = content.iterator();

                        while(var20.hasNext()) {
                           Serializable contentField = (Serializable)var20.next();
                           Field field = (Field)contentField;
                           Object paramValue;
                           if (field.isDependencyResolveRequired()) {
                              if (!fieldKeyValueMap.containsKey(field.getAlias())) {
                                 paramValue = this.getAliasObjectValue(field);
                                 fieldKeyValueMap.put(field.getAlias(), paramValue);
                              }
                           } else if (field.isMutiple()) {
                              List entryValue = this.getMultipleEntryValue(field, scenarioItem.getName(), serviceName, methodName);
                              fieldKeyValueMap.put(field.getName(), entryValue);
                           } else {
                              paramValue = null;
                              if (field.getContent() != null && field.getContent().size() > 0) {
                                 paramValue = this.getObjectValue(field, (Serializable)field.getContent().get(0));
                              }

                              fieldKeyValueMap.put(field.getName(), paramValue);
                           }
                        }

                        paramObject = this.initParamObject(type, fieldKeyValueMap, scenarioItem.getName(), serviceName, methodName);
                        parameters.put(actionParam.getName(), paramObject);
                     }
                  }
               }

               ActionItemResult methodResult = this.executeOpenAPIMethod(serviceName, methodName, token, parameters.values().toArray());
               if (methodResult != null) {
                  actionResultList.add(methodResult);
               }
            } catch (ItemValidationException var24) {
               String mainMessage = var24.getItemResultStatus() == ResultStatus.WARN ? "Validation failed" : "Operation failed";
               ActionItemResult failedRes = new ActionItemResult(serviceName, methodName, var24.getItemResultStatus(), mainMessage, var24.getMessage());
               actionResultList.add(failedRes);
            }
         }

         scenarioResMap.put(scenarioItem, actionResultList);
      }

      return scenarioResMap;
   }

   public Scenarios importScenarios(URL scenarioLocation) {
      String errorMessage;
      try {
         File targetFile = SecurityUtils.getSafeFile(scenarioLocation.toURI());
         JAXBContext jaxbContext = JAXBContext.newInstance(new Class[]{Scenarios.class});
         Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
         return (Scenarios)jaxbUnmarshaller.unmarshal(targetFile);
      } catch (JAXBException var5) {
         errorMessage = String.format("Unable to import scenarios from %s - incorrect data format, check if scenario data file is valid and has a correct format", scenarioLocation);
         logger.error(errorMessage, var5);
      } catch (URISyntaxException var6) {
         errorMessage = String.format("Unable to import scenarios from %s - incorrect scenario location", scenarioLocation);
         logger.error(errorMessage, var6);
      }

      return null;
   }

   public boolean exportScenarios(URL scenarioLocation, Scenarios groupsMap) {
      boolean isSuccess = false;

      String errorMessage;
      try {
         File targetFile = SecurityUtils.getSafeFile(scenarioLocation.toURI());
         JAXBContext jaxbContext = JAXBContext.newInstance(new Class[]{Scenarios.class});
         Marshaller jaxbMarshaller = jaxbContext.createMarshaller();
         jaxbMarshaller.marshal(groupsMap, targetFile);
         isSuccess = true;
      } catch (JAXBException var7) {
         errorMessage = String.format("Unable to export scenarios to %s - incorrect data format, check if scenario has a correct format", scenarioLocation);
         logger.error(errorMessage, var7);
      } catch (URISyntaxException var8) {
         errorMessage = String.format("Unable to export scenarios to %s - incorrect scenario location", scenarioLocation);
         logger.error(errorMessage, var8);
      }

      return isSuccess;
   }

   public ActionItemResult executeOpenAPIMethod(String serviceName, String methodName, String token, Object[] parameters) throws ItemValidationException {
      ActionItemResult itemRes = null;
      Object serviceBean = this.appContext.getBean("openApi" + serviceName);

      try {
         ClassUtil.invokeSetToken(serviceBean, token);
         Object res = MethodUtils.invokeMethod(serviceBean, methodName, parameters);
         if (res != null) {
            itemRes = new ActionItemResult(serviceName, methodName, ResultStatus.OK, "Method is successfully executed", res);
            return itemRes;
         } else {
            throw new ItemValidationException(String.format("Method %s for service %s returned 'null' value", methodName, serviceName), methodName, serviceName);
         }
      } catch (ReflectiveOperationException var9) {
         String errorMessage = String.format("Cannot properly execute scenario method %s for service %s. ", methodName, serviceName);
         if (var9.getCause() != null && var9.getCause().getMessage() != null) {
            errorMessage = errorMessage + "Reason: " + var9.getCause().getMessage();
         }

         logger.error(errorMessage, var9);
         throw new ItemValidationException(errorMessage, var9, serviceName, methodName);
      }
   }

   public ScenarioItem buildEmptyScenarioItem(String scenarioName, Collection actionIdList) {
      ScenarioItem scenarioItem = this.scenarioBuilder.buildEmptyScenarioItem(scenarioName, actionIdList);
      return scenarioItem;
   }

   public void validateActionItem(String token, String serviceName, String methodName, ActionItem ai) throws ItemValidationException {
      Map supportMap = this.getSupportersMap(serviceName, methodName, ai);
      Map fieldValuesMap = new LinkedHashMap();
      Map apValuesMap = new LinkedHashMap();
      Iterator var8 = ai.getActionParameter().iterator();

      label80:
      while(var8.hasNext()) {
         ActionParameter ap = (ActionParameter)var8.next();
         if (ap.isDependencyResolveRequired()) {
            String alias;
            if (ap.getContent() == null || ap.getContent().size() <= 0) {
               alias = "Unable to get parameter value to resolve dependency for parameter " + ap.getName();
               throw new ItemValidationException(alias, ai.getService(), ai.getMethod());
            }

            alias = ap.getAlias();
            String aliasValue = this.getActionParamAliasValue(token, alias, ai.getActionParameter(), supportMap, apValuesMap);
            ap.setAliasValue(aliasValue);
         } else {
            List contentList = ap.getContent();
            Iterator var11 = contentList.iterator();

            label78:
            while(true) {
               Serializable content;
               do {
                  if (!var11.hasNext()) {
                     continue label80;
                  }

                  content = (Serializable)var11.next();
               } while(!(content instanceof Field));

               Field field = (Field)content;
               if (field.isDependencyResolveRequired()) {
                  String alias = field.getAlias();
                  String aliasValue = this.getFieldAliasValue(token, alias, supportMap, contentList, fieldValuesMap);
                  if (aliasValue == null || aliasValue.isEmpty()) {
                     String errorMsg = "Unable to get parameter value to resolve dependency for parameter: " + ap.getName() + ", field: " + field.getName();
                     throw new ItemValidationException(errorMsg, ai.getService(), ai.getMethod());
                  }

                  field.setAliasValue(aliasValue);
               }

               List innerFieldList = field.getContent();
               Iterator var29 = innerFieldList.iterator();

               while(true) {
                  Serializable innerField;
                  do {
                     if (!var29.hasNext()) {
                        continue label78;
                     }

                     innerField = (Serializable)var29.next();
                  } while(!(innerField instanceof Entry));

                  Entry entry = (Entry)innerField;
                  List entryContentList = entry.getContent();
                  Map entryValuesMap = new LinkedHashMap();
                  Iterator var20 = entryContentList.iterator();

                  while(var20.hasNext()) {
                     Serializable entryContent = (Serializable)var20.next();
                     if (entryContent instanceof EntryField) {
                        EntryField ef = (EntryField)entryContent;
                        if (ef.isDependencyResolveRequired()) {
                           String alias = ef.getAlias();
                           String aliasValue = this.getEntryAliasValue(token, alias, supportMap, entryContentList, entryValuesMap);
                           if (aliasValue == null || aliasValue.isEmpty()) {
                              String errorMsg = "Unable to get parameter value to resolve dependency for parameter: " + ap.getName() + ", field " + field.getName() + ", entry: " + ef.getName();
                              throw new ItemValidationException(errorMsg, ai.getService(), ai.getMethod());
                           }

                           ef.setAliasValue(aliasValue);
                        }
                     }
                  }
               }
            }
         }
      }

   }

   private Object initParamObject(String type, Map keyValueMap, String scenarioName, String aiServiceName, String aiMethodName) throws ItemValidationException {
      Method targetMethod = null;
      Object paramObject = null;

      String errorMessage;
      try {
         Class clazz = Class.forName(type);
         paramObject = clazz.newInstance();
         Iterator var23 = keyValueMap.entrySet().iterator();

         while(true) {
            while(var23.hasNext()) {
               java.util.Map.Entry entry = (java.util.Map.Entry)var23.next();
               String key = (String)entry.getKey();
               Object param = entry.getValue();
               String setterName;
               if (entry.getValue() == null) {
                  setterName = String.format("Parameter %s wasn't initialized for service %s method %s", key, aiServiceName, aiMethodName);
                  logger.warn(setterName);
               } else {
                  setterName = "set" + key.substring(0, 1).toUpperCase(Locale.US) + key.substring(1);
                  Method[] var14 = clazz.getMethods();
                  int var15 = var14.length;

                  for(int var16 = 0; var16 < var15; ++var16) {
                     Method m = var14[var16];
                     if (m.getName().equals(setterName)) {
                        targetMethod = m;
                        break;
                     }
                  }

                  if (targetMethod == null) {
                     String errorMessage = String.format("Couldn't find method %s of type %s running %s of %s scenario", setterName, type, aiMethodName, scenarioName);
                     throw new ItemValidationException(errorMessage, aiServiceName, aiMethodName);
                  }

                  if (targetMethod.getParameterTypes().length > 0) {
                     targetMethod.invoke(paramObject, param);
                  } else {
                     targetMethod.invoke(paramObject);
                  }
               }
            }

            return paramObject;
         }
      } catch (InstantiationException var18) {
         errorMessage = String.format("Unable to initialize instance of %s  Method: %s Scenario: %s", type, aiMethodName, scenarioName);
         throw new ItemValidationException(errorMessage, var18, aiServiceName, aiMethodName);
      } catch (ClassNotFoundException var19) {
         errorMessage = String.format("Action item type %s is not recognized by MagicInfo Server. Method: %s Scenario: %s", type, aiMethodName, scenarioName);
         throw new ItemValidationException(errorMessage, var19, aiServiceName, aiMethodName);
      } catch (IllegalAccessException var20) {
         errorMessage = String.format("Unable to initialize instance of %s  Method: %s Scenario: %s", type, aiMethodName, scenarioName);
         if (targetMethod != null) {
            errorMessage = errorMessage + ". Perhaps method " + targetMethod.getName() + " is not defined";
         }

         throw new ItemValidationException(errorMessage, var20, aiServiceName, aiMethodName);
      } catch (SecurityException var21) {
         errorMessage = String.format("Unable to get type information for %s. Method: %s Scenario: %s", type, aiMethodName, scenarioName);
         throw new ItemValidationException(errorMessage, var21, aiServiceName, aiMethodName);
      } catch (InvocationTargetException | IllegalArgumentException var22) {
         errorMessage = String.format("Unable to run method %s for instance of type %s  Method: %s Scenario: %s", targetMethod.getName(), type, aiMethodName, scenarioName);
         throw new ItemValidationException(errorMessage, var22, aiServiceName, aiMethodName);
      }
   }

   private List getMultipleEntryValue(Field field, String scenarioName, String serviceName, String methodName) throws ItemValidationException {
      List entries = new LinkedList();
      Iterator var6 = field.getContent().iterator();

      while(true) {
         Serializable fieldContent;
         do {
            if (!var6.hasNext()) {
               return entries;
            }

            fieldContent = (Serializable)var6.next();
         } while(!(fieldContent instanceof Entry));

         Entry entry = (Entry)fieldContent;
         String entryType = entry.getType();
         Map keyValueMap = new LinkedHashMap();
         Iterator var11 = entry.getContent().iterator();

         while(var11.hasNext()) {
            Serializable contentField = (Serializable)var11.next();
            EntryField ef = (EntryField)contentField;
            Object aliasVal;
            if (ef.isDependencyResolveRequired()) {
               if (!keyValueMap.containsKey(ef.getAlias())) {
                  aliasVal = this.getAliasObjectValue(ef);
                  keyValueMap.put(ef.getAlias(), aliasVal);
               }
            } else {
               aliasVal = this.getObjectValue(ef, ef.getValue());
               keyValueMap.put(ef.getName(), aliasVal);
            }
         }

         Object paramObject = this.initParamObject(entryType, keyValueMap, scenarioName, serviceName, methodName);
         entries.add(paramObject);
      }
   }

   private Object getAliasObjectValue(SupportedValueField item) {
      String realType = this.scenarioBuilder.getOriginalType(item.getAliasType());
      Object realObject = this.castToParameter(realType, item.getAliasValue());
      return realObject;
   }

   private Object getObjectValue(TypedField item, Serializable val) {
      String realType = this.scenarioBuilder.getOriginalType(item.getType());
      Object realObject = this.castToParameter(realType, val);
      return realObject;
   }

   private void addToSupporters(String serviceName, String methodName, Map supportMap, String alias) {
      if (!supportMap.containsKey(alias)) {
         SupporterTypeInfo supportInfo = this.scenarioBuilder.getSupporterTypeInfo(serviceName, methodName, alias);
         supportMap.put(alias, supportInfo);
      }

   }

   private String getActionParamAliasValue(String token, String alias, List actionParams, Map supportMap, Map apValuesMap) throws ItemValidationException {
      if (!supportMap.containsKey(alias)) {
         return "";
      } else {
         if (!apValuesMap.containsKey(alias)) {
            List values = new LinkedList();
            Iterator var7 = actionParams.iterator();

            while(var7.hasNext()) {
               ActionParameter paramVal = (ActionParameter)var7.next();
               if (paramVal.isDependencyResolveRequired() && paramVal.getAlias().equals(alias)) {
                  Object val = this.castToParameter(paramVal.getType(), (Serializable)paramVal.getContent().get(0));
                  values.add(val);
               }
            }

            SupporterTypeInfo supportTypeInfo = (SupporterTypeInfo)supportMap.get(alias);
            String serviceName = supportTypeInfo.getAi().getServiceName();
            String methodName = supportTypeInfo.getAi().getActionName();
            ActionItemResult res = this.executeOpenAPIMethod(serviceName, methodName, token, values.toArray());
            apValuesMap.put(alias, res.getOutput());
         }

         return (String)apValuesMap.get(alias);
      }
   }

   private String getFieldAliasValue(String token, String alias, Map supportMap, List fieldList, Map fieldValuesMap) throws ItemValidationException {
      if (!supportMap.containsKey(alias)) {
         return "";
      } else {
         if (!fieldValuesMap.containsKey(alias)) {
            List values = new LinkedList();
            Iterator var7 = fieldList.iterator();

            while(var7.hasNext()) {
               Serializable s_field = (Serializable)var7.next();
               Field f = (Field)s_field;
               if (f.isDependencyResolveRequired() && f.getAlias().equals(alias) && f.getContent().size() > 0) {
                  values.add(f.getContent().get(0));
               }
            }

            SupporterTypeInfo supportTypeInfo = (SupporterTypeInfo)supportMap.get(alias);
            String serviceName = supportTypeInfo.getAi().getServiceName();
            String methodName = supportTypeInfo.getAi().getActionName();
            ActionItemResult res = this.executeOpenAPIMethod(serviceName, methodName, token, values.toArray());
            fieldValuesMap.put(alias, res.getOutput());
         }

         return (String)fieldValuesMap.get(alias);
      }
   }

   private String getEntryAliasValue(String token, String alias, Map supportMap, List entryList, Map entryValuesMap) throws ItemValidationException {
      if (!supportMap.containsKey(alias)) {
         return "";
      } else {
         if (!entryValuesMap.containsKey(alias)) {
            List values = new LinkedList();
            Iterator var7 = entryList.iterator();

            while(var7.hasNext()) {
               Serializable s_entry = (Serializable)var7.next();
               EntryField f = (EntryField)s_entry;
               if (f.isDependencyResolveRequired() && f.getAlias().equals(alias)) {
                  values.add(f.getValue());
               }
            }

            SupporterTypeInfo supportTypeInfo = (SupporterTypeInfo)supportMap.get(alias);
            String serviceName = supportTypeInfo.getAi().getServiceName();
            String methodName = supportTypeInfo.getAi().getActionName();
            ActionItemResult res = this.executeOpenAPIMethod(serviceName, methodName, token, values.toArray());
            entryValuesMap.put(alias, res.getOutput());
         }

         return (String)entryValuesMap.get(alias);
      }
   }

   private Map getSupportersMap(String serviceName, String methodName, ActionItem ai) {
      Map supportMap = new LinkedHashMap();
      Iterator var5 = ai.getActionParameter().iterator();

      while(true) {
         label54:
         while(var5.hasNext()) {
            ActionParameter ap = (ActionParameter)var5.next();
            if (ap.isDependencyResolveRequired()) {
               String alias = ap.getAlias();
               this.addToSupporters(serviceName, methodName, supportMap, alias);
            } else {
               List contentList = ap.getContent();
               Iterator var8 = contentList.iterator();

               while(true) {
                  Serializable innerField;
                  do {
                     List innerFieldList;
                     do {
                        Serializable content;
                        do {
                           if (!var8.hasNext()) {
                              continue label54;
                           }

                           content = (Serializable)var8.next();
                        } while(!(content instanceof Field));

                        Field field = (Field)content;
                        if (field.isDependencyResolveRequired()) {
                           String alias = field.getAlias();
                           this.addToSupporters(serviceName, methodName, supportMap, alias);
                        }

                        innerFieldList = field.getContent();
                     } while(innerFieldList.size() <= 0);

                     innerField = (Serializable)innerFieldList.get(0);
                  } while(!(innerField instanceof Entry));

                  Entry entry = (Entry)innerField;
                  List entryContentList = entry.getContent();
                  Iterator var15 = entryContentList.iterator();

                  while(var15.hasNext()) {
                     Serializable entryContent = (Serializable)var15.next();
                     if (entryContent instanceof EntryField) {
                        EntryField ef = (EntryField)entryContent;
                        if (ef.isDependencyResolveRequired()) {
                           String alias = ef.getAlias();
                           this.addToSupporters(serviceName, methodName, supportMap, alias);
                        }
                     }
                  }
               }
            }
         }

         return supportMap;
      }
   }

   private Object castToParameter(String parameterTypeName, Serializable parameterValue) {
      try {
         if (parameterTypeName.equals(String.class.getName())) {
            return parameterValue.toString();
         } else if (parameterTypeName.equals("boolean")) {
            return Boolean.valueOf(parameterValue.toString());
         } else if (parameterTypeName.equals("long")) {
            return Long.parseLong(parameterValue.toString());
         } else if (parameterTypeName.equals("int")) {
            return Long.valueOf(parameterValue.toString()).intValue();
         } else if (parameterTypeName.equals(Long.class.getName())) {
            return Long.valueOf(parameterValue.toString());
         } else if (parameterTypeName.equals(LinkedList.class.getName())) {
            List list = new LinkedList();
            list.add(parameterValue);
            return list;
         } else if (parameterTypeName.equals(Timestamp.class.getName())) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
            Date parsedDate = dateFormat.parse(parameterValue.toString());
            Timestamp ts = new Timestamp(parsedDate.getTime());
            return ts;
         } else {
            logger.error(String.format("Type %s for parameter %s is not recognized by operation provider", parameterTypeName, parameterValue.toString()));
            return parameterValue;
         }
      } catch (ParseException var6) {
         String errorMessage = String.format("Incorrect Timestamp data format for value %s", parameterValue.toString());
         logger.error(errorMessage, var6);
         return null;
      }
   }
}
