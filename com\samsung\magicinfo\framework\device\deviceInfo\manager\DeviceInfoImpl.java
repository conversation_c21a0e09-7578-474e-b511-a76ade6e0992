package com.samsung.magicinfo.framework.device.deviceInfo.manager;

import com.samsung.common.db.PagedListInfo;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceDao;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.BackupPlayEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceControl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceLoopOutEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMemo;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceModel;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSecurityConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DisasterAlertStatusEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceGeneralConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceGeneralConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSecurityConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSecurityConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManagerImpl;
import com.samsung.magicinfo.framework.device.vwlLayout.entity.VwlLayoutMonitor;
import com.samsung.magicinfo.framework.monitoring.service.MonitoringServiceActivity;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.rms.model.DeviceFilter;
import java.lang.reflect.Method;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class DeviceInfoImpl implements DeviceInfo {
   Logger logger = LoggingManagerV2.getLogger(DeviceInfoImpl.class);
   private static DeviceInfoImpl instance;
   private int daoType = 0;
   private DeviceDao dao = null;

   public static synchronized DeviceInfo getInstance() {
      if (instance == null) {
         instance = new DeviceInfoImpl((SqlSession)null);
      }

      return instance;
   }

   public static DeviceInfo getInstance(SqlSession session) {
      return new DeviceInfoImpl(session);
   }

   private DeviceInfoImpl(SqlSession session) {
      super();
      if (this.dao == null) {
         this.daoType = 0;
         this.dao = new DeviceDao(session);
      }

   }

   public boolean addDevice(Device device) throws SQLException {
      return this.dao.addDevice(device);
   }

   public boolean addChildDevice(Device device) throws SQLException {
      return this.dao.addChildDevice(device);
   }

   public boolean addChildDevice(List devices) throws SQLException {
      return this.dao.addChildDevice(devices);
   }

   public boolean addDeviceModel(DeviceModel deviceModel) throws SQLException, ConfigException {
      return this.dao.addDeviceModel(deviceModel);
   }

   public boolean addDeviceOperationInfo(Device device) throws SQLException {
      if (device.getDevice_model_name() != null && !device.getDevice_model_name().equals("")) {
         DeviceModel deviceModel = this.dao.getDeviceModel(device.getDevice_model_name());
         if (deviceModel == null) {
            deviceModel = new DeviceModel();
            deviceModel.setCreator_id("admin");
            deviceModel.setVendor("SEC");
            deviceModel.setDevice_model_name(device.getDevice_model_name());
            deviceModel.setDevice_model_type(device.getDevice_model_name());
            deviceModel.setDevice_model_code(device.getDevice_model_code());

            try {
               this.dao.addDeviceModel(deviceModel);
            } catch (Exception var4) {
               this.logger.error("", var4);
            }
         }
      }

      return this.dao.addDeviceOperationInfo(device);
   }

   public boolean setUnapprovedGroupCode(String deviceId, Long unapprovedGroupCode) throws SQLException {
      return this.dao.setUnapprovedGroupCode(deviceId, unapprovedGroupCode);
   }

   public boolean deleteDevice(String deviceId) throws SQLException {
      return this.dao.deleteDevice(deviceId);
   }

   public boolean addVwtInfo(VwlLayoutMonitor vwlLayoutMonitor, String deviceId) throws SQLException {
      return this.dao.addVwtInfo(vwlLayoutMonitor, deviceId);
   }

   public boolean deleteDeviceGroupMapping(String deviceId, int group_id) throws SQLException {
      return this.dao.deleteDeviceGroupMapping(deviceId, group_id);
   }

   public boolean deleteDeviceModel(String device_model_name) throws SQLException {
      return this.dao.deleteDeviceModel(device_model_name);
   }

   public boolean deleteDeviceModels(String[] device_model_names) throws SQLException, ConfigException {
      return this.dao.deleteDeviceModels(device_model_names);
   }

   public boolean deleteDevices(String[] deviceSN) throws SQLException {
      return this.dao.deleteDevices(deviceSN);
   }

   public int getCntEqualDevName(String deviceName, Long orgGroupId) throws SQLException {
      return this.dao.getCntEqualDevName(deviceName, orgGroupId);
   }

   public int getCntModelByModelCode(String modelCode) throws Exception {
      return this.dao.getCntModelByModelCode(modelCode);
   }

   public Device getDevice(String deviceId) throws SQLException {
      return this.dao.getDevice(deviceId);
   }

   public Device getDeviceFromDB(String deviceId) throws SQLException {
      return this.dao.getDeviceFromDB(deviceId);
   }

   public Device getDeviceWithGroupId(String deviceId) throws SQLException {
      return this.dao.getDeviceWithGroupId(deviceId);
   }

   public Device getMonitoringViewDevice(String deviceId) throws SQLException {
      return this.dao.getDeviceWithGroupId(deviceId);
   }

   public Map getDeviceAndModel(String deviceId) throws SQLException {
      return this.dao.getDeviceAndModel(deviceId);
   }

   public DeviceGeneralConf getDeviceGeneralConf(String deviceId) throws SQLException {
      return this.dao.getDeviceGeneralConf(deviceId);
   }

   public DeviceGeneralConf getDeviceGeneralConf(String deviceId, boolean refresh) throws SQLException {
      return this.dao.getDeviceGeneralConf(deviceId, refresh);
   }

   public DeviceGeneralConf getDeviceTypeInfo(String deviceId) throws SQLException {
      return this.dao.getDeviceTypeInfo(deviceId);
   }

   public List getListDeviceGeneralConf(List deviceIds) throws SQLException {
      return this.dao.getListDeviceGeneralConf(deviceIds);
   }

   public List getDeviceList() throws SQLException {
      return this.dao.getDeviceList();
   }

   public List getChildDeviceIdList(String deviceId) throws SQLException {
      return this.dao.getChildDeviceIdList(deviceId);
   }

   public PagedListInfo getDeviceList(int startPos, int pageSize) throws SQLException, ConfigException {
      return this.dao.getDeviceList(startPos, pageSize);
   }

   public PagedListInfo getDeviceList(int startPos, int pageSize, String modelName, String modelCode, String deviceId) throws SQLException, ConfigException {
      return this.dao.getDeviceList(startPos, pageSize, modelName, modelCode, deviceId);
   }

   public PagedListInfo getDeviceListByGroup(int startPos, int pageSize, String modelName, Long groupId, String deviceId) throws SQLException, ConfigException {
      return this.dao.getDeviceListByGroup(startPos, pageSize, modelName, groupId, deviceId);
   }

   public List getDeviceListByGroupId(Long groupId) throws SQLException {
      return this.daoType == 0 ? this.dao.getDeviceListByGroupId(groupId) : null;
   }

   public List getDeviceAndTagListByGroupId(Long groupId) throws SQLException {
      return this.dao.getDeviceAndTagListByGroupId(groupId);
   }

   public Long getDeviceUnapprovedGroupCode(String deviceId) throws SQLException {
      return this.dao.getDeviceUnapprovedGroupCode(deviceId);
   }

   public List getDeviceAndTagListByGroupIds(Long[] groupIds) throws SQLException {
      return this.dao.getDeviceAndTagListByGroupIds(groupIds);
   }

   public List getDeviceAndTagListByDeviceIds(String[] deviceIds) throws SQLException {
      return this.dao.getDeviceAndTagListByDeviceIds(deviceIds, false);
   }

   public List getDeviceAndTagListByDeviceIds(String[] deviceIds, Boolean isVarTag) throws SQLException {
      return this.dao.getDeviceAndTagListByDeviceIds(deviceIds, isVarTag);
   }

   public List getDeviceListByModelName(String ModelName) throws SQLException {
      return this.dao.getDeviceListByModelName(ModelName);
   }

   public List getDeviceListByModelNameAndType(String ModelName, String DeviceType, String organization) throws SQLException {
      return this.dao.getDeviceListByModelNameAndType(ModelName, DeviceType, organization);
   }

   public List getDeviceListByModelNameAndGroup(String ModelName, int GroupID) throws SQLException {
      return this.dao.getDeviceListByModelNameAndGroup(ModelName, GroupID);
   }

   public Device getDeviceMinInfo(String deviceId) throws SQLException {
      Device result = null;
      result = this.dao.getDeviceMinInfo(deviceId);
      return result;
   }

   public DeviceModel getDeviceModel(String device_model_name) throws SQLException {
      return this.dao.getDeviceModel(device_model_name);
   }

   public PagedListInfo getDeviceModelList(int startPos, int pageSize, String device_model_code) throws SQLException, ConfigException {
      return this.dao.getDeviceModelList(startPos, pageSize, device_model_code);
   }

   public List getDeviceModelList(String device_model_code) throws SQLException {
      return this.dao.getDeviceModelList(device_model_code);
   }

   public List getConnectedDeviceModelNameList() throws SQLException {
      return this.dao.getConnectedDeviceModelNameList();
   }

   public List getConnectedDeviceModelNameListTypeS(String deviceType, String organization) throws SQLException {
      return this.dao.getConnectedDeviceModelNameListTypeS(deviceType, organization);
   }

   public List getDeviceModelNameList() throws SQLException {
      return this.dao.getDeviceModelNameList();
   }

   public String getDeviceNameById(String deviceId) throws SQLException {
      return this.dao.getDeviceNameById(deviceId);
   }

   public Device getDeviceOperationInfo(String deviceId) throws SQLException {
      return this.dao.getDeviceOperationInfo(deviceId);
   }

   public List getDMInfo(String device_id) throws SQLException {
      return this.dao.getDMInfo(device_id);
   }

   public String getModelNameByDeviceId(String deviceId) throws SQLException {
      return this.dao.getModelNameByDeviceId(deviceId);
   }

   public List getModelNameListByDeviceId(String deviceId) throws SQLException {
      return this.dao.getModelNameListByDeviceId(deviceId);
   }

   public List getMonitoringInfoListByDeviceId(String deviceId) throws SQLException {
      return this.dao.getMonitoringInfoListByDeviceId(deviceId);
   }

   public boolean getDeviceApprovalStatusByDeviceId(String deviceId) throws SQLException {
      return this.dao.getDeviceApprovalStatusByDeviceId(deviceId);
   }

   public List getMonitoringInfoByDeviceIdList(String[] deviceId) throws SQLException {
      return this.dao.getMonitoringInfoByDeviceIdList(deviceId);
   }

   public PagedListInfo getPagedList(int startPos, int pageSize, Map condition, String section) throws SQLException, ConfigException {
      PagedListInfo pagedListInfo = null;
      if (this.daoType == 0) {
         if (section.equals("getApprovedDeviceList")) {
            pagedListInfo = this.dao.getApprovedDeviceList(startPos, pageSize, condition);
         } else if (section.equals("getNonApprovedDeviceList")) {
            pagedListInfo = this.dao.getNonApprovedDeviceList(startPos, pageSize, condition);
         } else if (section.equals("getApprovedDeviceListWithFilter")) {
            pagedListInfo = this.dao.getApprovedDeviceListWithFilter(startPos, pageSize, condition);
         } else if (section.equals("getNonApprovedPremiumOnlyDeviceListLimit")) {
            pagedListInfo = this.dao.getNonApprovedPremiumOnlyDeviceListLimit(startPos, pageSize, condition);
         } else if (section.equals("getNonApprovedPremiumOnlyDeviceListLimitOpenAPI")) {
            pagedListInfo = this.dao.getNonApprovedPremiumOnlyDeviceListLimitOpenAPI(startPos, pageSize, condition);
         } else if (section.equals("getNonApprovedDeviceListLimit")) {
            pagedListInfo = this.dao.getNonApprovedDeviceListLimit(startPos, pageSize, condition);
         } else if (section.equals("getDeviceMonitoringList")) {
            pagedListInfo = this.dao.getDeviceMonitoringList(startPos, pageSize, condition);
         } else if (section.equals("selBindingDeviceList")) {
            int totCount = this.dao.getBindingDeviceListCnt(condition);
            List resList = this.dao.getBindingDeviceListPage(condition, startPos, pageSize);
            pagedListInfo = new PagedListInfo(resList, totCount);
         } else if (section.equals("getNonApprovedDevice")) {
            pagedListInfo = this.dao.getNonApprovedDeviceList(startPos, pageSize, condition);
         } else {
            String[] deviceTypeFilter;
            if (section.equals("getCheckDeviceListTimezone")) {
               deviceTypeFilter = null;
               if (condition.get("device_type") != null) {
                  deviceTypeFilter = condition.get("device_type").toString().split(",");
               }

               pagedListInfo = this.dao.getCheckDeviceListTimezone(startPos, pageSize, condition, deviceTypeFilter);
            } else if (section.equals("getCheckDeviceListStorage")) {
               deviceTypeFilter = null;
               if (condition.get("device_type") != null) {
                  deviceTypeFilter = condition.get("device_type").toString().split(",");
               }

               pagedListInfo = this.dao.getCheckDeviceListStorage(startPos, pageSize, condition, deviceTypeFilter);
            } else if (section.equals("getCheckDeviceListSchedule")) {
               deviceTypeFilter = null;
               if (condition.get("device_type") != null) {
                  deviceTypeFilter = condition.get("device_type").toString().split(",");
               }

               pagedListInfo = this.dao.getCheckDeviceListSchedule(startPos, pageSize, condition, deviceTypeFilter);
            } else if (section.equals("getCheckDeviceListScheduleFail")) {
               deviceTypeFilter = null;
               if (condition.get("device_type") != null) {
                  deviceTypeFilter = condition.get("device_type").toString().split(",");
               }

               pagedListInfo = this.dao.getCheckDeviceListScheduleFail(startPos, pageSize, condition, deviceTypeFilter);
            } else if (section.equals("getCheckDeviceListContent")) {
               deviceTypeFilter = null;
               if (condition.get("device_type") != null) {
                  deviceTypeFilter = condition.get("device_type").toString().split(",");
               }

               pagedListInfo = this.dao.getCheckDeviceListContent(startPos, pageSize, condition, deviceTypeFilter);
            } else if (section.equals("getCheckDeviceListReservationScheduleFail")) {
               deviceTypeFilter = null;
               if (condition.get("device_type") != null) {
                  deviceTypeFilter = condition.get("device_type").toString().split(",");
               }

               pagedListInfo = this.dao.getCheckDeviceListReservationScheduleFail(startPos, pageSize, condition, deviceTypeFilter);
            } else if (section.equals("getCheckUpcomingExpiryDate")) {
               try {
                  pagedListInfo = this.dao.getCheckUpcomingExpiryDate(startPos, pageSize, condition);
               } catch (Exception var9) {
                  this.logger.error("", var9);
               }
            } else if (section.equals("getCheckUpcomingExpiryDateDevice")) {
               try {
                  pagedListInfo = this.dao.getCheckUpcomingExpiryDateDevice(startPos, pageSize, condition);
               } catch (Exception var8) {
                  this.logger.error("", var8);
               }
            } else if (section.equals("checkUpcomingExpiryDatePlaylist")) {
               pagedListInfo = this.dao.getCheckUpcomingExpiryDatePlaylist(startPos, pageSize, condition);
            }
         }
      }

      return pagedListInfo;
   }

   public List getRuleVersionList() throws SQLException {
      return this.dao.getRuleVersionList();
   }

   public List getAppVersionList() throws SQLException {
      return this.dao.getAppVersionList();
   }

   public List getAppVersionListByDeviceType(String device_type) throws SQLException {
      return this.dao.getAppVersionListByDeviceType(device_type);
   }

   public List getAppVersionListBy(Map map) throws SQLException {
      return this.dao.getAppVersionListBy(map);
   }

   public List getDeviceModelNameListBy(Map map) throws SQLException {
      List list = this.dao.getDeviceModelNameListBy(map);
      List deviceModelNameList = new ArrayList();
      Iterator var4 = list.iterator();

      while(var4.hasNext()) {
         Map resultMap = (Map)var4.next();
         deviceModelNameList.add((String)resultMap.get("DEVICE_MODEL_NAME"));
      }

      return deviceModelNameList;
   }

   public boolean moveDevice(String deviceId, int new_parent_group_id) throws SQLException {
      return this.dao.moveDevice(deviceId, new_parent_group_id);
   }

   public boolean setDevice(Device device) throws SQLException {
      return this.dao.setDevice(device, true);
   }

   public boolean setDeviceForApproval(Map map) throws SQLException, ConfigException {
      return this.dao.setDeviceForApproval(map);
   }

   public void deviceTotalCount(long groupId, int count) throws SQLException {
      if (this.addDeviceTotalCount(groupId, count)) {
         List pgroupIdLists = this.dao.getPgorupIdLIsts(groupId);
         if (pgroupIdLists != null) {
            for(int i = 0; i < pgroupIdLists.size(); ++i) {
               long pGroupId = (Long)((Map)pgroupIdLists.get(i)).get("P_GROUP_ID");
               this.deviceTotalCount(pGroupId, count);
            }
         }
      }

   }

   public void removeDeviceTotalCount(long groupId, int count) throws SQLException {
      this.dao.removeDeviceTotalCount(groupId, count);
   }

   public long getDeviceTotalCount(long groupId) throws SQLException {
      return this.dao.getDeviceTotalCount(groupId);
   }

   public boolean addDeviceTotalCount(long groupId, int count) throws SQLException {
      return this.dao.addDeviceTotalCount(groupId, count);
   }

   public int setDeviceGroupId(Map map) throws SQLException {
      return this.dao.setDeviceGroupId(map);
   }

   public boolean setDeviceModel(DeviceModel deviceModel) throws SQLException {
      return this.dao.setDeviceModel(deviceModel);
   }

   public boolean setDeviceNameAndLocation(String device_id, String device_name, String location, String map_location, String deviceModelName) throws SQLException {
      return this.dao.setDeviceNameAndLocation(device_id, device_name, location, map_location, deviceModelName);
   }

   public boolean setDeviceOperationInfo(Device device) throws SQLException {
      if (device.getDevice_model_name() != null && !device.getDevice_model_name().equals("")) {
         String modelName = null;
         if (device.getDevice_model_name().startsWith("LITE_")) {
            modelName = device.getDevice_model_name().substring(5);
         } else {
            modelName = device.getDevice_model_name();
         }

         DeviceModel deviceModel = this.dao.getDeviceModel(modelName);
         if (deviceModel == null) {
            deviceModel = new DeviceModel();
            deviceModel.setCreator_id("admin");
            deviceModel.setVendor("SEC");
            deviceModel.setDevice_model_name(modelName);
            deviceModel.setDevice_model_type(modelName);
            deviceModel.setDevice_model_code(device.getDevice_model_code());

            try {
               DeviceInfo premiumDao = getInstance();
               premiumDao.addDeviceModel(deviceModel);
            } catch (Exception var5) {
               this.logger.error("", var5);
            }
         }
      }

      return this.dao.setDeviceOperationInfo(device);
   }

   public boolean updateDiskspaceChannel(String deviceId, long disk_space, String channel) throws SQLException {
      return this.dao.updateDiskspaceChannel(deviceId, disk_space, channel);
   }

   public boolean setDevicePostBootstrap(Device device) throws SQLException, ConfigException {
      return this.dao.setDevicePostBootstrap(device, true);
   }

   public int setIsApproved(Map map) throws SQLException {
      return this.dao.setIsApproved(map);
   }

   public int setNameDeviceAndModel(Map map) throws SQLException, ConfigException {
      return this.dao.setNameDeviceAndModel(map);
   }

   public boolean setLastConnectionTime(String deviceId) throws SQLException, ConfigException {
      return this.dao.setLastConnectionTime(deviceId);
   }

   public List getFirmwareVersionList() throws SQLException {
      return this.dao.getFirmwareVersionList();
   }

   public List getOSImageVersionList() throws SQLException {
      return this.dao.getOSImageVersionList();
   }

   public List getDeviceResolutionList(String deviceType) throws SQLException {
      return this.dao.getDeviceResolutionList(deviceType);
   }

   public List getBindingDeviceList(Map map) throws SQLException {
      return this.dao.getBindingDeviceList(map);
   }

   public List getBindingDeviceListPage(Map map, int startPos, int pageSize) throws SQLException {
      return this.dao.getBindingDeviceListPage(map, startPos, pageSize);
   }

   public int getBindingDeviceListCnt(Map map) throws SQLException {
      return this.dao.getBindingDeviceListCnt(map);
   }

   public int getAllDeviceCount(String deviceType) throws SQLException {
      return this.dao.getAllDeviceCount(deviceType);
   }

   public int getAllDeviceCountByOrganization(String deviceType, String organization) throws SQLException {
      return "ROOT".equalsIgnoreCase(organization) ? this.dao.getAllDeviceCount(deviceType) : this.dao.getAllDeviceCountByOrganization(deviceType, organization);
   }

   public int getApprovalDeviceCount(String deviceType) throws SQLException {
      return this.dao.getApprovalDeviceCount(deviceType);
   }

   public int getNonApprovalDeviceCount(String deviceType) throws SQLException {
      return this.dao.getNonApprovalDeviceCount(deviceType);
   }

   public int getApprovalPremiumDeviceCount() throws SQLException {
      return this.daoType == 0 ? this.dao.getApprovalPremiumDeviceCount() : 0;
   }

   public int getApprovalExtraDisplayDeviceCount() throws SQLException {
      return this.daoType == 0 ? this.dao.getApprovalExtraDisplayDeviceCount() : 0;
   }

   public List getModelNameListByModelCode(String device_model_code) throws SQLException {
      return this.dao.getModelNameListByModelCode(device_model_code);
   }

   public int setApprovalWithSeq(Map map) throws SQLException, ConfigException {
      return this.dao.setApprovalWithSeq(map);
   }

   public boolean addDeviceBindingInfo(Device device) throws SQLException {
      return this.dao.addDeviceBindingInfo(device);
   }

   public boolean deleteDeviceBindingInfo(String deviceId) throws SQLException {
      return this.dao.deleteDeviceBindingInfo(deviceId);
   }

   public List getDeviceMonitoringFilterList(SelectCondition condition) throws SQLException, ConfigException {
      return this.dao.getDeviceMonitoringFilterList(condition);
   }

   public List getApprovedDeviceFilterList(SelectCondition condition) throws SQLException, ConfigException {
      return this.dao.getApprovedDeviceFilterList(condition);
   }

   public boolean setShutDownConnectionTime(String deviceId) throws SQLException {
      Long monInterval = 10L;

      try {
         monInterval = this.dao.getDeviceMonitoringInterval(deviceId);
      } catch (SQLException var4) {
         this.logger.error("Cannot get Mointoring Interval from DB (Device ID : " + deviceId + ")");
         this.logger.error("Default value 10(min) is passed to dao.setShutDownConnectionTime() to calc last connection time");
      }

      if (monInterval == null) {
         monInterval = 10L;
      }

      return this.dao.setShutDownConnectionTime(deviceId, monInterval);
   }

   public String getDeviceModelCodeByDeviceId(String deviceId) throws SQLException {
      return this.dao.getDeviceModelCodeByDeviceId(deviceId);
   }

   public String getDeviceGroupIdByDeviceId(String deviceId) throws SQLException {
      return this.dao.getDeviceGroupIdByDeviceId(deviceId);
   }

   public boolean deleteDeviceBindingInfo() throws SQLException {
      return this.dao.deleteDeviceBindingInfo();
   }

   public List getDeviceIdGroupIdByDeviceName(String deviceName) throws SQLException {
      return this.dao.getDeviceIdGroupIdByDeviceName(deviceName);
   }

   public int getCntApprovedDeviceList(Map condition) throws SQLException {
      int result = false;
      int result = this.dao.getCntApprovedDeviceList(condition);
      return result;
   }

   public List getDeviceModelTypeList() throws SQLException {
      return this.dao.getDeviceModelTypeList();
   }

   public String getProgramIdByDeviceId(String deviceId) throws SQLException {
      return this.dao.getProgramIdByDeviceId(deviceId);
   }

   public String getEventScheduleIdByDeviceId(String deviceId) throws SQLException {
      return this.daoType == 0 ? this.dao.getEventScheduleIdByDeviceId(deviceId) : "";
   }

   public String getScheduleIdByProgramId(String programId) throws SQLException {
      return this.dao.getScheduleIdByProgramId(programId);
   }

   public long getVersionByProgramId(String programId) throws SQLException {
      return this.dao.getVersionByProgramId(programId);
   }

   public String getApprovalByDeviceId(String deviceId) throws SQLException {
      return this.dao.getApprovalByDeviceId(deviceId);
   }

   public List getDeviceIdByDesc(String deviceType) throws SQLException {
      return this.dao.getDeviceIdByDesc(deviceType);
   }

   public List getDeviceIdByAsc(String deviceType) throws SQLException {
      return this.dao.getDeviceIdByAsc(deviceType);
   }

   public List getApprovalDeviceIdByAsc(String deviceType) throws SQLException {
      return this.dao.getApprovalDeviceIdByAsc(deviceType);
   }

   public List getApprovalDeviceIdByDeviceTypeListAsc(List deviceType) throws SQLException {
      return this.dao.getApprovalDeviceIdByDeviceTypeListAsc(deviceType);
   }

   public List getApprovalDeviceIdByDeviceTypeListInOrgAssignedLicenseAsc(List deviceType) throws SQLException {
      return this.dao.getApprovalDeviceIdByDeviceTypeListInOrgAssignedLicenseAsc(deviceType);
   }

   public boolean isVwlConsole(String deviceId) throws SQLException {
      return this.daoType == 0 ? this.dao.isVwlConsole(deviceId) : false;
   }

   public int getCntDeviceByDeviceType(String deviceType) throws SQLException {
      return this.daoType == 0 ? this.dao.getCntDeviceByDeviceType(deviceType) : 0;
   }

   public boolean refreshDeviceGroupType(int groupId) throws SQLException {
      return this.daoType == 0 ? this.dao.refreshDeviceGroupType(groupId) : false;
   }

   public List getDeviceIdListByGroup(int GroupID) throws SQLException {
      return this.dao.getDeviceIdListByGroup(GroupID);
   }

   public List getNotChildDeviceIdListByGroup(int GroupID) throws SQLException {
      return this.dao.getNotChildDeviceIdListByGroup(GroupID);
   }

   public String getDeviceModelNameByDeviceId(String deviceId) throws SQLException {
      return this.dao.getDeviceModelNameByDeviceId(deviceId);
   }

   public String getIsRedundancy(int GroupID) throws SQLException {
      return this.daoType == 0 ? this.dao.getIsRedundancy(GroupID) : "";
   }

   public boolean addRedundancyStatus(String deviceId, boolean redundancyStatus) throws SQLException {
      return this.dao.addRedundancyStatus(deviceId, redundancyStatus);
   }

   public boolean isRedundancyDevice(String deviceId) throws SQLException {
      return this.dao.isRedundancyDevice(deviceId);
   }

   public List getExpiredDeviceList(int day) throws SQLException {
      return this.dao.getExpiredDeviceList(day);
   }

   public String getVwtIdByDeviceId(String deviceID) throws SQLException {
      return this.dao.getVwtIdByDeviceId(deviceID);
   }

   public String getVwtFileName(String vwt_id) throws SQLException {
      return this.daoType == 0 ? this.dao.getVwtFileName(vwt_id) : null;
   }

   public boolean addDeviceTypeVersion(String deviceId, Float deviceTypeVersion) throws SQLException {
      return this.dao.addDeviceTypeVersion(deviceId, deviceTypeVersion);
   }

   public boolean addDeviceInfoAtApprove(Device device) throws SQLException {
      return this.dao.addDeviceInfoAtApprove(device);
   }

   public Long getMinimalPriorityByGroupId(long groupId) throws SQLException {
      return this.dao.getMinimalPriorityByGroupId(groupId);
   }

   public boolean deleteVwtInfo(String deviceId) throws SQLException {
      return this.dao.deleteVwtInfo(deviceId);
   }

   public Long getDeviceGroupPriority(long groupId) throws SQLException {
      return this.dao.getDeviceGroupPriority(groupId);
   }

   public String getDeviceWaitingMo(String deviceId) throws SQLException {
      return this.dao.getDeviceWaitingMo(deviceId);
   }

   public boolean deleteWaitingMo(String deviceId) throws SQLException {
      return this.dao.deleteWaitingMo(deviceId);
   }

   public int setDeviceWaitingMo(String deviceId, String serviceName, String infoValue) throws SQLException {
      return this.dao.setDeviceWaitingMo(deviceId, serviceName, infoValue);
   }

   public boolean addDeviceWaitingMo(String deviceId) throws SQLException {
      return this.dao.addDeviceWaitingMo(deviceId);
   }

   public String getDayLightSavingManual(String deviceId) throws SQLException {
      return this.dao.getDayLightSavingManual(deviceId);
   }

   public boolean getExistCenterstage() throws SQLException {
      return this.dao.getExistCenterstage();
   }

   public int insertDisasterAlertStatus(DisasterAlertStatusEntity DisasterAlertStatus) throws SQLException {
      return this.dao.insertDisasterAlertStatus(DisasterAlertStatus);
   }

   public List selectDisasterAlertStatus(String alert_id) throws SQLException {
      return this.dao.selectDisasterAlertStatus(alert_id);
   }

   public List selectDisasterAlertStatusDisconnected(String device_id) throws Exception {
      return this.dao.selectDisasterAlertStatusDisconnected(device_id);
   }

   public List selectDisasterAlertStatusByDeviceId(String device_id) throws Exception {
      return this.dao.selectDisasterAlertStatusByDeviceId(device_id);
   }

   public List selectSimpleDisasterAlertStatus(String alert_id) throws Exception {
      return this.dao.selectSimpleDisasterAlertStatus(alert_id);
   }

   public List getDisconnectedDisasterAlertByDeviceIdAndAlertId(String device_id, String alert_id) throws Exception {
      return this.dao.getDisconnectedDisasterAlertByDeviceIdAndAlertId(device_id, alert_id);
   }

   public int updateDisasterAlertStatus(DisasterAlertStatusEntity DisasterAlertStatus) throws SQLException {
      return this.dao.updateDisasterAlertStatus(DisasterAlertStatus);
   }

   public void deleteDisasterAlertStatus(String alert_id) throws SQLException {
      this.dao.deleteDisasterAlertStatus(alert_id);
   }

   public void deleteDisconnectedDisasterAlertStatus(String device_id, String alert_id) throws SQLException {
      this.dao.deleteDisconnectedDisasterAlertStatus(device_id, alert_id);
   }

   public int insertExtDeviceInfo(DeviceLoopOutEntity deviceLoopOutInfo) throws Exception {
      return this.dao.insertExtDeviceInfo(deviceLoopOutInfo);
   }

   public List selectExtDeviceInfo(String device_id, String type) throws Exception {
      return this.dao.selectExtDeviceInfo(device_id, type);
   }

   public int updateExtDeviceInfo(DeviceLoopOutEntity deviceLoopOutInfo) throws Exception {
      return this.dao.updateExtDeviceInfo(deviceLoopOutInfo);
   }

   public void deleteExtDeviceInfo(String device_id) throws Exception {
      this.dao.deleteExtDeviceInfo(device_id);
   }

   public Map getSoftwareUpdate(String device_id) throws Exception {
      return this.dao.getSoftwareUpdate(device_id);
   }

   public boolean setKeepAliveInfo(String deviceId, Long disk_space, String channel, String curr_content_id) throws SQLException {
      return this.dao.setKeepAliveInfo(deviceId, disk_space, channel, curr_content_id);
   }

   public boolean setKeepAliveInfo(String deviceId, Long disk_space, String channel, String curr_content_id, String diskSpaceUsage, String diskSpaceAvailable) throws SQLException {
      return this.dao.setKeepAliveInfo(deviceId, disk_space, channel, curr_content_id, diskSpaceUsage, diskSpaceAvailable);
   }

   public List getCheckDeviceListTimezone(Long groupId, String userId, boolean isDeviceGroupAuth) throws SQLException {
      List groupList = this.dao.getGroupIdsByOrgManagerUserId(userId, groupId);
      return this.dao.getListTimezoneNotSet(groupList, userId, isDeviceGroupAuth);
   }

   public List getCheckDeviceListSchedule(Long groupId, String userId, boolean isDeviceGroupAuth) throws SQLException {
      List groupList = this.dao.getGroupIdsByOrgManagerUserId(userId, groupId);
      return this.dao.getListScheduleNotPublish(groupList, userId, isDeviceGroupAuth);
   }

   public int getCheckDeviceListCntSchedule(String[] deviceTypeFilter, String search, List groupList, boolean isDeviceGroupAuth, String userId) throws SQLException {
      return this.dao.getCheckDeviceListCntSchedule(deviceTypeFilter, search, groupList, isDeviceGroupAuth, userId);
   }

   public List getCheckDeviceListScheduleFail(String[] deviceTypeFilter, String search, List groupList, boolean isDeviceGroupAuth, String userId) throws SQLException {
      return this.dao.getCheckDeviceListScheduleFail(deviceTypeFilter, search, groupList, isDeviceGroupAuth, userId);
   }

   public int getCheckDeviceListCntScheduleFail(String[] deviceTypeFilter, String search, List groupList, boolean isDeviceGroupAuth, String userId) throws SQLException {
      return this.dao.getCheckDeviceListCntScheduleFail(deviceTypeFilter, search, groupList, isDeviceGroupAuth, userId);
   }

   public List getCheckDeviceListReservationScheduleFail(String[] deviceTypeFilter, String search, List groupList, boolean isDeviceGroupAuth, String userId) throws SQLException {
      return this.dao.getCheckDeviceListReservationScheduleFail(deviceTypeFilter, search, groupList, isDeviceGroupAuth, userId);
   }

   public int getCheckDeviceListCntReservationScheduleFail(String[] deviceTypeFilter, String search, List groupList, boolean isDeviceGroupAuth, String userId) throws SQLException {
      return this.dao.getCheckDeviceListCntReservationScheduleFail(deviceTypeFilter, search, groupList, isDeviceGroupAuth, userId);
   }

   public List getCheckDeviceListContent(Long groupId, String userId, boolean isDeviceGroupAuth) throws SQLException {
      List groupList = this.dao.getGroupIdsByOrgManagerUserId(userId, groupId);
      return this.dao.getListContentError(groupList, userId, isDeviceGroupAuth);
   }

   public int getCheckDeviceListCntContent(String[] deviceTypeFilter, String search, List groupList, boolean isDeviceGroupAuth, String userId) throws SQLException {
      return this.dao.getCheckDeviceListCntContent(deviceTypeFilter, search, groupList, isDeviceGroupAuth, userId);
   }

   public List getCheckUpcomingExpiryDate(int addDay, Long deviceOrgId) throws SQLException {
      return this.dao.getCheckUpcomingExpiryDateList(addDay, deviceOrgId);
   }

   public List getCheckUpcomingExpiryDatePlaylistList(int addDay, String orgId) throws SQLException {
      return this.dao.getCheckUpcomingExpiryDatePlaylistList(addDay, orgId);
   }

   public int getCheckUpcomingExpiryDateCnt() throws SQLException {
      return this.dao.getCheckUpcomingExpiryDateCnt();
   }

   public int getCheckUpcomingExpiryDatePlaylistCnt() throws SQLException {
      return this.dao.getCheckUpcomingExpiryDatePlaylistCnt();
   }

   public List getCheckDeviceListStorage(Long groupId, String userId, boolean isDeviceGroupAuth) throws SQLException {
      List groupList = this.dao.getGroupIdsByOrgManagerUserId(userId, groupId);
      return this.dao.getListInsufficientCapacity(groupList, userId, isDeviceGroupAuth);
   }

   public boolean deleteChildDevice(String parentDeviceId) throws SQLException {
      return this.dao.deleteChildDevice(parentDeviceId);
   }

   public boolean addDeviceGroupMapping(int parentGroupId, String deviceId) throws SQLException {
      return this.dao.addDeviceGroupMapping(parentGroupId, deviceId);
   }

   public boolean addDeviceGroupMapping(int parentGroupId, List deviceIdList) throws SQLException {
      return this.dao.addDeviceGroupMapping(parentGroupId, deviceIdList);
   }

   public boolean setConnectChildCnt(String deviceId, Long connChildCnt) throws Exception {
      return this.dao.setConnectChildCnt(deviceId, connChildCnt);
   }

   public DeviceMemo getDeviceMemo(String deviceId) throws SQLException {
      return this.dao.getDeviceMemo(deviceId);
   }

   public boolean setDeviceMemo(String cmd, DeviceMemo memo) throws SQLException {
      return this.dao.setDeviceMemo(cmd, memo);
   }

   public List getServerDeviceReport() throws SQLException {
      return this.dao.getServerDeviceReport();
   }

   public List getDeviceModelCount() throws SQLException {
      return this.dao.getDeviceModelCount();
   }

   public List getDeviceFirmwareCount() throws SQLException {
      return this.dao.getDeviceFirmwareCount();
   }

   public boolean setDeviceAmsCam(boolean isWebCam, String deviceId) throws SQLException {
      return this.dao.setDeviceAmsCam(isWebCam, deviceId);
   }

   public boolean addBackupPlayer(BackupPlayEntity backup) throws SQLException {
      return this.dao.addBackupPlayer(backup);
   }

   public boolean addBackupTargetPlayer(BackupPlayEntity backup) throws SQLException {
      return this.dao.addBackupTargetPlayer(backup);
   }

   public boolean deleteBackupPlayer(int group_id) throws SQLException {
      return this.dao.deleteBackupPlayer(group_id);
   }

   public boolean deleteBackupTargetPlayer(int group_id) throws SQLException {
      return this.dao.deleteBackupTargetPlayer(group_id);
   }

   public boolean setBackupBusyLevel(int busy_level, String backupDeviceId) throws SQLException {
      return this.dao.setBackupBusyLevel(busy_level, backupDeviceId);
   }

   public boolean setWaitingMoCount(int waitingMoCount, String backupDeviceId) throws SQLException {
      return this.dao.setWaitingMoCount(waitingMoCount, backupDeviceId);
   }

   public boolean setBackupDevice(String backupDeviceId, String deviceId) throws SQLException {
      return this.dao.setBackupDevice(backupDeviceId, deviceId);
   }

   public List getBackupPlayers(Long groupId) throws SQLException {
      return this.dao.getBackupPlayers(groupId);
   }

   public List getBackupPlayerByWaitingMoCount(Long groupId) throws SQLException {
      return this.dao.getBackupPlayerByWaitingMoCount(groupId);
   }

   public BackupPlayEntity getBackupPlayerByDeviceId(String deviceId) throws SQLException {
      return this.dao.getBackupPlayerByDeviceId(deviceId);
   }

   public List getBackupTargetPlayers(Long groupId) throws SQLException {
      return this.dao.getBackupTargetPlayers(groupId);
   }

   public DeviceMonitoring getProgramInfoByDeviceGroupId(Long groupId) throws SQLException {
      return this.dao.getProgramInfoByDeviceGroupId(groupId);
   }

   public List getDeviceAndGroupInfoByGroupId(Long groupId) throws SQLException {
      return this.dao.getDeviceAndGroupInfoByGroupId(groupId);
   }

   public int cntSyncPlayDevice(String deviceId) throws SQLException {
      return this.dao.cntSyncPlayDevice(deviceId);
   }

   public boolean updateLastModifiedTime(String deviceId) throws SQLException {
      return this.dao.updateLastModifiedTime(deviceId);
   }

   public boolean updateLogFileName(String deviceId, String categoryScript, String fileName) throws SQLException {
      return this.dao.updateLogFileName(deviceId, categoryScript, fileName);
   }

   public Timestamp getStatisticsFileRequestTime(String deviceId) throws SQLException {
      return this.dao.getStatisticsFileRequestTime(deviceId);
   }

   public List getDeviceLogProcessInfo(String deviceId) throws SQLException {
      return this.dao.getDeviceLogProcessInfo(deviceId);
   }

   public int addDeviceLogProcessInfo(String deviceId, String type, String categoryScript, String status, Timestamp startTime, int duration, int packetSize, String token) throws SQLException {
      return this.dao.addDeviceLogProcessInfo(deviceId, type, categoryScript, status, startTime, duration, packetSize, token);
   }

   public boolean updateDeviceLogProcessStatus(String deviceId, String type, String categoryScript, String status) throws SQLException {
      return this.dao.updateDeviceLogProcessStatus(deviceId, type, categoryScript, status);
   }

   public boolean deleteDeviceLogProcessInfoByDeviceId(String deviceId, String categoryScript) throws SQLException {
      return this.dao.deleteDeviceLogProcessInfoByDeviceId(deviceId, categoryScript);
   }

   public int getLogProcessingDeviceCnt() throws SQLException {
      return this.dao.getLogProcessingDeviceCnt();
   }

   public boolean updateDeviceLogInfo(String deviceId, String type, String categoryScript, String status, Timestamp startTime, int duration, int packetSize, String token, String encryptionKey) throws SQLException {
      return this.dao.updateDeviceLogInfo(deviceId, type, categoryScript, status, startTime, duration, packetSize, token, encryptionKey);
   }

   public List getAllDeviceLogProcess() throws SQLException {
      return this.dao.getAllDeviceLogProcess();
   }

   public List getNewAndModifiedDeviceList(String startDate, String endDate) throws SQLException {
      return this.dao.getNewAndModifiedDeviceList(startDate, endDate);
   }

   public int getDeviceCountBygroupId(long groupId) throws SQLException {
      return this.dao.getDeviceCountBygroupId(groupId);
   }

   public boolean addStatRequestTimeInsertCurrent(String deviceId) throws SQLException {
      return this.dao.addStatRequestTimeInsertCurrent(deviceId);
   }

   public boolean isRequestTimeExist(String deviceId) throws SQLException {
      return this.dao.isRequestTimeExist(deviceId);
   }

   public boolean getIsOverWriteDeviceName(String deviceId) throws SQLException {
      boolean rtn = false;

      try {
         rtn = this.dao.getIsOverWriteDeviceName(deviceId);
      } catch (Exception var4) {
         rtn = false;
      }

      return rtn;
   }

   public boolean checkEmptyEntity(Object o) {
      boolean rtn = false;

      try {
         Method[] var3 = o.getClass().getMethods();
         int var4 = var3.length;

         for(int var5 = 0; var5 < var4; ++var5) {
            Method f = var3[var5];
            if (f.getName().startsWith("get") && !f.getName().equals("getMulti_time_zone_index") && !f.getName().equals("getMulti_day_light_saving") && !f.getName().equals("getClass")) {
               f.invoke(o.getClass(), String.class, Boolean.class, Integer.class);
            }
         }
      } catch (Exception var8) {
         this.logger.error("", var8);
         rtn = true;
      }

      return rtn;
   }

   public List getDeviceListByOrgName(String orgName) throws SQLException {
      return this.dao.getDeviceListByOrgName(orgName);
   }

   public int getDeviceCountForLicense(List map) throws SQLException {
      return this.dao.getDeviceCountForLicense(map);
   }

   public List getDeviceListFromDeviceId(List deviceIds) throws SQLException {
      return this.dao.getDeviceListFromDeviceId(deviceIds);
   }

   public List getFirstChildrenIDsOfSignageGroup(int groupId) throws SQLException {
      return this.dao.getFirstChildrenIDsOfSignageGroup(groupId);
   }

   public int getProgramDeviceTypeByGroupId(long groupId) throws SQLException {
      return this.dao.getProgramDeviceTypeByGroupId(groupId);
   }

   public int checkFirstReceiveProgress(String deviceId) throws SQLException {
      return this.dao.checkFirstReceiveProgress(deviceId);
   }

   public int getContentDownloadMode(String deviceId) throws SQLException {
      Integer ret = this.dao.getContentDownloadMode(deviceId);
      if (ret == null) {
         ret = 0;
      }

      return ret;
   }

   public List getTagFromDeviceId(String deviceId) throws SQLException {
      return this.dao.getTagFromDeviceId(deviceId, false);
   }

   public List getTagFromDeviceId(String deviceId, Boolean isVarTag) throws SQLException {
      return this.dao.getTagFromDeviceId(deviceId, isVarTag);
   }

   public int getAllDeviceCountByDeviceTypeList(List deviceTypeList) throws SQLException {
      return this.dao.getAllDeviceCountByDeviceTypeList(deviceTypeList);
   }

   public boolean chkOrganizationByDeviceId(String organizationFromUser, String id) throws Exception {
      String deviceOrganization = this.dao.getOrganiationByDeviceId(id);
      if (deviceOrganization == null) {
         Long deviceGroupId = this.dao.getDeviceGroupId(id);
         DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
         Map organization = deviceGroupDao.getDeviceOrganizationByGroupId(deviceGroupId.intValue());
         String orgName = organization.get("GROUP_NAME").toString();
         Map deviceGroup = new HashMap();
         deviceGroup.put("device_id", id);
         deviceGroup.put("group_id", deviceGroupId);
         deviceGroup.put("organization", orgName);
         this.dao.setOrganizationByDeviceId(deviceGroup);
         deviceOrganization = this.dao.getOrganiationByDeviceId(id);
      }

      return organizationFromUser.equals(deviceOrganization);
   }

   public boolean updateDeviceMapLocation(String location, String[] deviceIdList) throws SQLException {
      return this.dao.updateDeviceMapLocation(location, deviceIdList);
   }

   public boolean updateDeviceMapLocationByLocation(String location, String[] locationList) throws SQLException {
      return this.dao.updateDeviceMapLocationByLocation(location, locationList);
   }

   public List getDeviceMinList(String[] deviceIdList) throws SQLException {
      return this.dao.getDeviceMinList(deviceIdList);
   }

   public boolean addSboxVwtInfo(String deviceId, String sboxVwtId, String sboxVwtFileName) throws SQLException {
      return this.dao.addSboxVwtInfo(deviceId, sboxVwtId, sboxVwtFileName);
   }

   public boolean deleteSboxVwtInfo(String deviceId) throws SQLException {
      return this.dao.deleteSboxVwtInfo(deviceId);
   }

   public int setKeepaliveChangedStatus(Boolean keepaliveStatus, String deviceId) throws SQLException {
      return this.dao.setKeepaliveChangedStatus(keepaliveStatus, deviceId);
   }

   public int initKeepaliveChangedStatus() throws SQLException {
      return this.dao.initKeepaliveChangedStatus();
   }

   public List getDisconnectedDeviceIdList() throws SQLException {
      return this.dao.getDisconnectedDeviceIdList();
   }

   public boolean setRecommendPlayByDeviceId(String deviceId, boolean value) throws SQLException {
      return this.dao.setRecommendPlayByDeviceId(deviceId, value);
   }

   public boolean getRecommendPlayByDeviceId(String deviceId) throws SQLException {
      return this.dao.getRecommendPlayByDeviceId(deviceId);
   }

   public int getCntRecommendPlayDevice() throws SQLException {
      return this.dao.getCntRecommendPlayDevice();
   }

   public int getCntDeviceMonitoringList(DeviceFilter condition) throws SQLException {
      return this.dao.getCntDeviceMonitoringList(condition);
   }

   public int getCountDeviceAll(String userId, Long groupId, boolean isDeviceGroupAuth) throws SQLException {
      List groupList = this.dao.getGroupIdsByOrgManagerUserId(userId, groupId);
      return this.dao.getCountDeviceAll(groupList, userId, isDeviceGroupAuth);
   }

   public int getCountTimezoneNotSet(String userId, Long groupId, boolean isDeviceGroupAuth) throws SQLException {
      List groupList = this.dao.getGroupIdsByOrgManagerUserId(userId, groupId);
      return this.dao.getCountTimezoneNotSet(groupList, userId, isDeviceGroupAuth);
   }

   public int getCountInsufficientCapacity(String userId, Long groupId, boolean isDeviceGroupAuth) throws SQLException {
      List groupList = this.dao.getGroupIdsByOrgManagerUserId(userId, groupId);
      return this.dao.getCountInsufficientCapacity(groupList, userId, isDeviceGroupAuth);
   }

   public int getCountScheduleNotPublish(String userId, Long groupId, boolean isDeviceGroupAuth) throws SQLException {
      List groupList = this.dao.getGroupIdsByOrgManagerUserId(userId, groupId);
      return this.dao.getCountScheduleNotPublish(groupList, userId, isDeviceGroupAuth);
   }

   public int getCountContentError(String userId, Long groupId, boolean isDeviceGroupAuth) throws SQLException {
      List groupList = this.dao.getGroupIdsByOrgManagerUserId(userId, groupId);
      return this.dao.getCountContentError(groupList, userId, isDeviceGroupAuth);
   }

   public List getRmMonitoringList(String[] deviceIdList, Timestamp errorStandardTime) throws SQLException {
      return this.dao.getRmMonitoringList(deviceIdList, errorStandardTime);
   }

   public List getErrorList(String[] deviceIdList, String type, Timestamp errorPeriod, Integer status) throws SQLException {
      return this.dao.getErrorList(deviceIdList, type, errorPeriod, status);
   }

   public boolean deleteDeviceData(String deviceId) throws SQLException {
      return this.dao.deleteDeviceData(deviceId);
   }

   public List getMaxDeviceTypeVersion() throws SQLException {
      return this.dao.getMaxDeviceTypeVersion();
   }

   public Boolean setDeviceControl(DeviceControl device) throws SQLException {
      try {
         boolean changed = false;
         if (device.getGeneral() != null) {
            DeviceGeneralConfManager deviceGeneralConfDao = DeviceGeneralConfManagerImpl.getInstance();
            deviceGeneralConfDao.setDeviceGeneralConf(device.getGeneral());
            changed = true;
         }

         if (device.getSetup() != null) {
            DeviceSystemSetupConfManager systemSetupDao = DeviceSystemSetupConfManagerImpl.getInstance();
            systemSetupDao.setDeviceSystemSetupConf(device.getSetup());
            changed = true;
         }

         if (device.getDisplay() != null) {
            DeviceDisplayConfManager displayDao = DeviceDisplayConfManagerImpl.getInstance();
            displayDao.setDeviceDisplayConf(device.getDisplay());
            displayDao.setDeviceDisplayExtConf(device.getDisplay());
            changed = true;
         }

         DeviceSecurityConfManager securityDao = DeviceSecurityConfManagerImpl.getInstance();
         new DeviceSecurityConf();
         if (device.getSecurity() != null) {
            DeviceSecurityConf security = device.getSecurity();
            securityDao.setDeviceSecurityConf(security);
            changed = true;
         }

         return true;
      } catch (Exception var5) {
         this.logger.error("[DeviceConfManagerImpl : saveDeviceControl]" + var5);
         return false;
      }
   }

   public List getPlayingDefaultContentDeviceIdList(String orgName) throws SQLException {
      return this.dao.getDeviceIdListByCurrentContentIds(orgName, MonitoringServiceActivity.getDefaultContentIdList());
   }

   public String getCurrentContentIdByDeviceId(String deviceId) throws SQLException {
      return this.dao.getCurrentContentIdByDeviceId(deviceId);
   }

   public boolean isDoneAtLast(String deviceId) throws SQLException {
      return this.dao.isDoneAtLast(deviceId);
   }

   public List getAllNotDonePlayingDefaultContentHistory() throws SQLException {
      return this.dao.getAllNotDonePlayingDefaultContentHistory();
   }

   public List getPlayingDefaultContentHistoryList(String orgName) throws SQLException {
      return this.dao.getPlayingDefaultContentHistoryList(orgName);
   }

   public boolean addPlayingDefaultContentHistory(String deviceId, String orgName) throws SQLException {
      return this.dao.addPlayingDefaultContentHistory(deviceId, orgName);
   }

   public int setPlayingDefaultContentDone(String deviceId) throws SQLException {
      return this.dao.setPlayingDefaultContentDone(deviceId);
   }

   public boolean deletePlayingDefaultContentHistoryByDeviceId(String deviceId) throws SQLException {
      return this.dao.deletePlayingDefaultContentHistoryByDeviceId(deviceId);
   }

   public boolean deletePlayingDefaultContentHistoryByOrganizationName(String orgName) throws SQLException {
      return this.dao.deletePlayingDefaultContentHistoryByOrganizationName(orgName);
   }

   public List getDeviceNameList(String organizationName) throws SQLException {
      return this.dao.getDeviceNameList(organizationName);
   }

   public List getDevicesByGroupIds(List groupIds) throws SQLException {
      return this.dao.getDevicesByGroupIds(groupIds);
   }

   public List getDevicesByProgramId(String programId) throws SQLException {
      return this.dao.getDevicesByProgramId(programId);
   }

   public List getDeviceCountByDeviceType() throws SQLException {
      return this.dao.getDeviceCountByDeviceType();
   }

   public List getDeviceSbox() throws SQLException {
      return this.dao.getDeviceSbox();
   }
}
