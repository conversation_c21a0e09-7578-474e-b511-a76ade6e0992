package com.samsung.magicinfo.webauthor2.model.datalink;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes({@Type(value = StaticDLKData.class, name = "Static"), @Type(value = DynamicDLKData.class, name = "Dynamic")})
public abstract class DLKData {
  @JsonIgnore
  private final DLKDataType type;
  
  protected DLKData(DLKDataType type) {
    this.type = type;
  }
  
  public DLKDataType getType() {
    return this.type;
  }
}
