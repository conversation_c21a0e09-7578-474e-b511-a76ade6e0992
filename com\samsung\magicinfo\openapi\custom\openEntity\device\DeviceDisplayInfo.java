package com.samsung.magicinfo.openapi.custom.openEntity.device;

import com.samsung.magicinfo.openapi.annotation.CdataField;
import java.io.Serializable;

public class DeviceDisplayInfo implements Serializable {
   static final long serialVersionUID = 4205958833454744960L;
   private String deviceId;
   @CdataField
   private String deviceName;
   private String deviceModelCode;
   private String deviceModelName;
   private String basicPower;
   private Long basicVolume;
   private Long basicMute;
   private Long basicSource;
   private String basicDirectChannel;
   private Long basicPanelStatus;
   private Long pvSplPictureMode;
   private Long pvMode;
   private Long pvContrast;
   private Long pvBrightness;
   private Long pvSharpness;
   private Long pvColor;
   private Long pvTint;
   private Long pvColortone;
   private Long pvColorTemperature;
   private Long pvSize;
   private Long pvDigitalNr;
   private Long pvFilmmode;
   private String pvVideoPicturePositionSize;
   private Long pvHdmiBlackLevel;
   private Long ppcGamma;
   private Long ppcHdmiBlackLevel;
   private Long ppcMagicBright;
   private Long ppcContrast;
   private Long ppcBrightness;
   private Long ppcColortone;
   private Long ppcColorTemperature;
   private Long ppcRed;
   private Long ppcGreen;
   private Long ppcBlue;
   private Long ppcSize;
   private Long soundMode;
   private Long soundBass;
   private Long soundTreble;
   private Long soundBalance;
   private Long soundSrs;
   private Long imageCoarse;
   private Long imageFine;
   private Long imageHpos;
   private Long imageVpos;
   private Long imageAuto;
   private Long sbStatus;
   private Long sbRgain;
   private Long sbGgain;
   private Long sbBgain;
   private Long sbROffset;
   private Long sbGOffset;
   private Long sbBOffset;
   private Long sbGain;
   private Long sbSharp;
   private String mntAuto;
   private Long mntManual;
   private Long mntVideoWall;
   private Long mntFormat;
   private String mntSafetyScreenTimer;
   private Long mntSafetyScreenRun;
   private String mntPixelShift;
   private Long mntSafetyLock;
   private Long advancedRj45SettingRefresh;
   private String advancedOsdDisplayType;
   private Long advancedFanControl;
   private Long advancedFanSpeed;
   private Long advancedReset;
   private Long advancedAutoPower;
   private Long advancedUserAutoColor;
   private Long advancedStandBy;
   private Long miscRemocon;
   private Long miscPanelLock;
   private Long miscOsd;
   private Long miscAllLock;
   private String diagnosisDisplayStatus;
   private Long diagnosisMonitorTemperature;
   private Long diagnosisAlarmTemperature;
   private String diagnosisPanelOnTime;

   public DeviceDisplayInfo() {
      super();
   }

   public String getDeviceId() {
      return this.deviceId;
   }

   public void setDeviceId(String deviceId) {
      this.deviceId = deviceId;
   }

   public String getDeviceName() {
      return this.deviceName;
   }

   public void setDeviceName(String deviceName) {
      this.deviceName = deviceName;
   }

   public String getDeviceModelCode() {
      return this.deviceModelCode;
   }

   public void setDeviceModelCode(String deviceModelCode) {
      this.deviceModelCode = deviceModelCode;
   }

   public String getDeviceModelName() {
      return this.deviceModelName;
   }

   public void setDeviceModelName(String deviceModelName) {
      this.deviceModelName = deviceModelName;
   }

   public String getBasicPower() {
      return this.basicPower;
   }

   public void setBasicPower(String basicPower) {
      this.basicPower = basicPower;
   }

   public Long getBasicVolume() {
      return this.basicVolume;
   }

   public void setBasicVolume(Long basicVolume) {
      this.basicVolume = basicVolume;
   }

   public Long getBasicMute() {
      return this.basicMute;
   }

   public void setBasicMute(Long basicMute) {
      this.basicMute = basicMute;
   }

   public Long getBasicSource() {
      return this.basicSource;
   }

   public void setBasicSource(Long basicSource) {
      this.basicSource = basicSource;
   }

   public String getBasicDirectChannel() {
      return this.basicDirectChannel;
   }

   public void setBasicDirectChannel(String basicDirectChannel) {
      this.basicDirectChannel = basicDirectChannel;
   }

   public Long getBasicPanelStatus() {
      return this.basicPanelStatus;
   }

   public void setBasicPanelStatus(Long basicPanelStatus) {
      this.basicPanelStatus = basicPanelStatus;
   }

   public Long getPvMode() {
      return this.pvMode;
   }

   public void setPvSplPictureMode(Long pvSplPictureMode) {
      this.pvSplPictureMode = pvSplPictureMode;
   }

   public Long getPvSplPictureMode() {
      return this.pvSplPictureMode;
   }

   public void setPvMode(Long pvMode) {
      this.pvMode = pvMode;
   }

   public Long getPvContrast() {
      return this.pvContrast;
   }

   public void setPvContrast(Long pvContrast) {
      this.pvContrast = pvContrast;
   }

   public Long getPvBrightness() {
      return this.pvBrightness;
   }

   public void setPvBrightness(Long pvBrightness) {
      this.pvBrightness = pvBrightness;
   }

   public Long getPvSharpness() {
      return this.pvSharpness;
   }

   public void setPvSharpness(Long pvSharpness) {
      this.pvSharpness = pvSharpness;
   }

   public Long getPvColor() {
      return this.pvColor;
   }

   public void setPvColor(Long pvColor) {
      this.pvColor = pvColor;
   }

   public Long getPvTint() {
      return this.pvTint;
   }

   public void setPvTint(Long pvTint) {
      this.pvTint = pvTint;
   }

   public Long getPvColortone() {
      return this.pvColortone;
   }

   public void setPvColortone(Long pvColortone) {
      this.pvColortone = pvColortone;
   }

   public Long getPvColorTemperature() {
      return this.pvColorTemperature;
   }

   public void setPvColorTemperature(Long pvColorTemperature) {
      this.pvColorTemperature = pvColorTemperature;
   }

   public Long getPvSize() {
      return this.pvSize;
   }

   public void setPvSize(Long pvSize) {
      this.pvSize = pvSize;
   }

   public Long getPvDigitalNr() {
      return this.pvDigitalNr;
   }

   public void setPvDigitalNr(Long pvDigitalNr) {
      this.pvDigitalNr = pvDigitalNr;
   }

   public Long getPvFilmmode() {
      return this.pvFilmmode;
   }

   public void setPvFilmmode(Long pvFilmmode) {
      this.pvFilmmode = pvFilmmode;
   }

   public String getPvVideoPicturePositionSize() {
      return this.pvVideoPicturePositionSize;
   }

   public void setPvVideoPicturePositionSize(String pvVideoPicturePositionSize) {
      this.pvVideoPicturePositionSize = pvVideoPicturePositionSize;
   }

   public Long getPvHdmiBlackLevel() {
      return this.pvHdmiBlackLevel;
   }

   public void setPvHdmiBlackLevel(Long pvHdmiBlackLevel) {
      this.pvHdmiBlackLevel = pvHdmiBlackLevel;
   }

   public Long getPpcGamma() {
      return this.ppcGamma;
   }

   public void setPpcGamma(Long ppcGamma) {
      this.ppcGamma = ppcGamma;
   }

   public Long getPpcHdmiBlackLevel() {
      return this.ppcHdmiBlackLevel;
   }

   public void setPpcHdmiBlackLevel(Long ppcHdmiBlackLevel) {
      this.ppcHdmiBlackLevel = ppcHdmiBlackLevel;
   }

   public Long getPpcMagicBright() {
      return this.ppcMagicBright;
   }

   public void setPpcMagicBright(Long ppcMagicBright) {
      this.ppcMagicBright = ppcMagicBright;
   }

   public Long getPpcContrast() {
      return this.ppcContrast;
   }

   public void setPpcContrast(Long ppcContrast) {
      this.ppcContrast = ppcContrast;
   }

   public Long getPpcBrightness() {
      return this.ppcBrightness;
   }

   public void setPpcBrightness(Long ppcBrightness) {
      this.ppcBrightness = ppcBrightness;
   }

   public Long getPpcColortone() {
      return this.ppcColortone;
   }

   public void setPpcColortone(Long ppcColortone) {
      this.ppcColortone = ppcColortone;
   }

   public Long getPpcColorTemperature() {
      return this.ppcColorTemperature;
   }

   public void setPpcColorTemperature(Long ppcColorTemperature) {
      this.ppcColorTemperature = ppcColorTemperature;
   }

   public Long getPpcRed() {
      return this.ppcRed;
   }

   public void setPpcRed(Long ppcRed) {
      this.ppcRed = ppcRed;
   }

   public Long getPpcGreen() {
      return this.ppcGreen;
   }

   public void setPpcGreen(Long ppcGreen) {
      this.ppcGreen = ppcGreen;
   }

   public Long getPpcBlue() {
      return this.ppcBlue;
   }

   public void setPpcBlue(Long ppcBlue) {
      this.ppcBlue = ppcBlue;
   }

   public Long getPpcSize() {
      return this.ppcSize;
   }

   public void setPpcSize(Long ppcSize) {
      this.ppcSize = ppcSize;
   }

   public Long getSoundMode() {
      return this.soundMode;
   }

   public void setSoundMode(Long soundMode) {
      this.soundMode = soundMode;
   }

   public Long getSoundBass() {
      return this.soundBass;
   }

   public void setSoundBass(Long soundBass) {
      this.soundBass = soundBass;
   }

   public Long getSoundTreble() {
      return this.soundTreble;
   }

   public void setSoundTreble(Long soundTreble) {
      this.soundTreble = soundTreble;
   }

   public Long getSoundBalance() {
      return this.soundBalance;
   }

   public void setSoundBalance(Long soundBalance) {
      this.soundBalance = soundBalance;
   }

   public Long getSoundSrs() {
      return this.soundSrs;
   }

   public void setSoundSrs(Long soundSrs) {
      this.soundSrs = soundSrs;
   }

   public Long getImageCoarse() {
      return this.imageCoarse;
   }

   public void setImageCoarse(Long imageCoarse) {
      this.imageCoarse = imageCoarse;
   }

   public Long getImageFine() {
      return this.imageFine;
   }

   public void setImageFine(Long imageFine) {
      this.imageFine = imageFine;
   }

   public Long getImageHpos() {
      return this.imageHpos;
   }

   public void setImageHpos(Long imageHpos) {
      this.imageHpos = imageHpos;
   }

   public Long getImageVpos() {
      return this.imageVpos;
   }

   public void setImageVpos(Long imageVpos) {
      this.imageVpos = imageVpos;
   }

   public Long getImageAuto() {
      return this.imageAuto;
   }

   public void setImageAuto(Long imageAuto) {
      this.imageAuto = imageAuto;
   }

   public Long getSbStatus() {
      return this.sbStatus;
   }

   public void setSbStatus(Long sbStatus) {
      this.sbStatus = sbStatus;
   }

   public Long getSbRgain() {
      return this.sbRgain;
   }

   public void setSbRgain(Long sbRgain) {
      this.sbRgain = sbRgain;
   }

   public Long getSbGgain() {
      return this.sbGgain;
   }

   public void setSbGgain(Long sbGgain) {
      this.sbGgain = sbGgain;
   }

   public Long getSbBgain() {
      return this.sbBgain;
   }

   public void setSbBgain(Long sbBgain) {
      this.sbBgain = sbBgain;
   }

   public Long getSbROffset() {
      return this.sbROffset;
   }

   public void setSbROffset(Long sbROffset) {
      this.sbROffset = sbROffset;
   }

   public Long getSbGOffset() {
      return this.sbGOffset;
   }

   public void setSbGOffset(Long sbGOffset) {
      this.sbGOffset = sbGOffset;
   }

   public Long getSbBOffset() {
      return this.sbBOffset;
   }

   public void setSbBOffset(Long sbBOffset) {
      this.sbBOffset = sbBOffset;
   }

   public Long getSbGain() {
      return this.sbGain;
   }

   public void setSbGain(Long sbGain) {
      this.sbGain = sbGain;
   }

   public Long getSbSharp() {
      return this.sbSharp;
   }

   public void setSbSharp(Long sbSharp) {
      this.sbSharp = sbSharp;
   }

   public String getMntAuto() {
      return this.mntAuto;
   }

   public void setMntAuto(String mntAuto) {
      this.mntAuto = mntAuto;
   }

   public Long getMntManual() {
      return this.mntManual;
   }

   public void setMntManual(Long mntManual) {
      this.mntManual = mntManual;
   }

   public Long getMntVideoWall() {
      return this.mntVideoWall;
   }

   public void setMntVideoWall(Long mntVideoWall) {
      this.mntVideoWall = mntVideoWall;
   }

   public Long getMntFormat() {
      return this.mntFormat;
   }

   public void setMntFormat(Long mntFormat) {
      this.mntFormat = mntFormat;
   }

   public String getMntSafetyScreenTimer() {
      return this.mntSafetyScreenTimer;
   }

   public void setMntSafetyScreenTimer(String mntSafetyScreenTimer) {
      this.mntSafetyScreenTimer = mntSafetyScreenTimer;
   }

   public Long getMntSafetyScreenRun() {
      return this.mntSafetyScreenRun;
   }

   public void setMntSafetyScreenRun(Long mntSafetyScreenRun) {
      this.mntSafetyScreenRun = mntSafetyScreenRun;
   }

   public String getMntPixelShift() {
      return this.mntPixelShift;
   }

   public void setMntPixelShift(String mntPixelShift) {
      this.mntPixelShift = mntPixelShift;
   }

   public Long getMntSafetyLock() {
      return this.mntSafetyLock;
   }

   public void setMntSafetyLock(Long mntSafetyLock) {
      this.mntSafetyLock = mntSafetyLock;
   }

   public Long getAdvancedRj45SettingRefresh() {
      return this.advancedRj45SettingRefresh;
   }

   public void setAdvancedRj45SettingRefresh(Long advancedRj45SettingRefresh) {
      this.advancedRj45SettingRefresh = advancedRj45SettingRefresh;
   }

   public String getAdvancedOsdDisplayType() {
      return this.advancedOsdDisplayType;
   }

   public void setAdvancedOsdDisplayType(String advancedOsdDisplayType) {
      this.advancedOsdDisplayType = advancedOsdDisplayType;
   }

   public Long getAdvancedFanControl() {
      return this.advancedFanControl;
   }

   public void setAdvancedFanControl(Long advancedFanControl) {
      this.advancedFanControl = advancedFanControl;
   }

   public Long getAdvancedFanSpeed() {
      return this.advancedFanSpeed;
   }

   public void setAdvancedFanSpeed(Long advancedFanSpeed) {
      this.advancedFanSpeed = advancedFanSpeed;
   }

   public Long getAdvancedReset() {
      return this.advancedReset;
   }

   public void setAdvancedReset(Long advancedReset) {
      this.advancedReset = advancedReset;
   }

   public Long getAdvancedAutoPower() {
      return this.advancedAutoPower;
   }

   public void setAdvancedAutoPower(Long advancedAutoPower) {
      this.advancedAutoPower = advancedAutoPower;
   }

   public Long getAdvancedUserAutoColor() {
      return this.advancedUserAutoColor;
   }

   public void setAdvancedUserAutoColor(Long advancedUserAutoColor) {
      this.advancedUserAutoColor = advancedUserAutoColor;
   }

   public Long getAdvancedStandBy() {
      return this.advancedStandBy;
   }

   public void setAdvancedStandBy(Long advancedStandBy) {
      this.advancedStandBy = advancedStandBy;
   }

   public Long getMiscRemocon() {
      return this.miscRemocon;
   }

   public void setMiscRemocon(Long miscRemocon) {
      this.miscRemocon = miscRemocon;
   }

   public Long getMiscPanelLock() {
      return this.miscPanelLock;
   }

   public void setMiscPanelLock(Long miscPanelLock) {
      this.miscPanelLock = miscPanelLock;
   }

   public Long getMiscOsd() {
      return this.miscOsd;
   }

   public void setMiscOsd(Long miscOsd) {
      this.miscOsd = miscOsd;
   }

   public Long getMiscAllLock() {
      return this.miscAllLock;
   }

   public void setMiscAllLock(Long miscAllLock) {
      this.miscAllLock = miscAllLock;
   }

   public String getDiagnosisDisplayStatus() {
      return this.diagnosisDisplayStatus;
   }

   public void setDiagnosisDisplayStatus(String diagnosisDisplayStatus) {
      this.diagnosisDisplayStatus = diagnosisDisplayStatus;
   }

   public Long getDiagnosisMonitorTemperature() {
      return this.diagnosisMonitorTemperature;
   }

   public void setDiagnosisMonitorTemperature(Long diagnosisMonitorTemperature) {
      this.diagnosisMonitorTemperature = diagnosisMonitorTemperature;
   }

   public Long getDiagnosisAlarmTemperature() {
      return this.diagnosisAlarmTemperature;
   }

   public void setDiagnosisAlarmTemperature(Long diagnosisAlarmTemperature) {
      this.diagnosisAlarmTemperature = diagnosisAlarmTemperature;
   }

   public String getDiagnosisPanelOnTime() {
      return this.diagnosisPanelOnTime;
   }

   public void setDiagnosisPanelOnTime(String diagnosisPanelOnTime) {
      this.diagnosisPanelOnTime = diagnosisPanelOnTime;
   }
}
