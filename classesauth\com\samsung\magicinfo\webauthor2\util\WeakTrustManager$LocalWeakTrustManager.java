package com.samsung.magicinfo.webauthor2.util;

import java.net.Socket;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import javax.net.ssl.SSLEngine;
import javax.net.ssl.X509ExtendedTrustManager;

class LocalWeakTrustManager extends X509ExtendedTrustManager {
  private LocalWeakTrustManager() {}
  
  public void checkClientTrusted(X509Certificate[] x509Certificates, String s, Socket socket) throws CertificateException {}
  
  public void checkServerTrusted(X509Certificate[] x509Certificates, String s, Socket socket) throws CertificateException {}
  
  public void checkClientTrusted(X509Certificate[] x509Certificates, String s, SSLEngine sslEngine) throws CertificateException {}
  
  public void checkServerTrusted(X509Certificate[] x509Certificates, String s, SSLEngine sslEngine) throws CertificateException {}
  
  public void checkClientTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {}
  
  public void checkServerTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {}
  
  public X509Certificate[] getAcceptedIssuers() {
    return new X509Certificate[0];
  }
}
