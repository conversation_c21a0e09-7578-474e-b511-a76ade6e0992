package com.samsung.magicinfo.protocol.file;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import java.io.File;
import java.io.IOException;
import java.util.Iterator;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.Logger;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

public class JobResultFileUploadServlet extends HttpServlet {
   private static final long serialVersionUID = 6880402483111407112L;
   private Logger logger = LoggingManagerV2.getLogger(JobResultFileUploadServlet.class);

   public JobResultFileUploadServlet() {
      super();
   }

   public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");
      this.doPost(request, response);
   }

   public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");

      try {
         String JOBS_RESULT_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "jobs_home" + File.separatorChar + "result";
         String deviceID = StrUtils.nvl(request.getHeader("DeviceID"));
         File fileJobHome = SecurityUtils.getSafeFile(JOBS_RESULT_HOME);
         if (!fileJobHome.exists()) {
            boolean fSuccess = fileJobHome.mkdir();
            if (!fSuccess) {
               this.logger.error("mkdir Fail");
            }
         }

         String filePath = JOBS_RESULT_HOME + File.separator + deviceID;
         File LogDir = SecurityUtils.getSafeFile(filePath);
         if (!LogDir.exists()) {
            LogDir.mkdir();
         }

         CommonsMultipartResolver multiPartResolver = new CommonsMultipartResolver();
         MultipartHttpServletRequest multiRequest = multiPartResolver.resolveMultipart(request);
         Iterator files = multiRequest.getFileNames();

         while(files.hasNext()) {
            String fileName = (String)files.next();
            MultipartFile multipartFile = multiRequest.getFile(fileName);
            if (multipartFile != null) {
               multipartFile.transferTo(SecurityUtils.getSafeFile(filePath + File.separator + fileName));
            }
         }

         multiPartResolver.cleanupMultipart(multiRequest);
      } catch (Exception var13) {
         response.sendError(600, var13.toString());
         this.logger.error(var13);
      }

   }
}
