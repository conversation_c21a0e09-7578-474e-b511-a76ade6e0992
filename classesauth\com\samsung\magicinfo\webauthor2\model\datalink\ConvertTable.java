package com.samsung.magicinfo.webauthor2.model.datalink;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public class ConvertTable {
  private String name;
  
  private final List<ConvertTableRow> rows;
  
  @JsonCreator
  public ConvertTable(@JsonProperty(value = "name", required = true) String name, @JsonProperty(value = "rows", required = true) List<ConvertTableRow> rows) {
    this.name = name;
    this.rows = rows;
  }
  
  public ConvertTable(List<ConvertTableRow> rows) {
    this.rows = rows;
  }
  
  public List<ConvertTableRow> getRows() {
    return this.rows;
  }
  
  public String getName() {
    return this.name;
  }
  
  public void setName(String name) {
    this.name = name;
  }
}
