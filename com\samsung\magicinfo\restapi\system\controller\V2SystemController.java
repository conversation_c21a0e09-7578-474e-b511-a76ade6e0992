package com.samsung.magicinfo.restapi.system.controller;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.auth.security.TokenUtils;
import com.samsung.magicinfo.framework.kpi.annotation.KPI;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManager;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManagerImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.mvc.handler.ApiVersion;
import com.samsung.magicinfo.restapi.system.model.V2ContentDownloadThrottling;
import com.samsung.magicinfo.restapi.system.model.V2DbSchemeResource;
import com.samsung.magicinfo.restapi.system.model.V2InsightSystemInfoResource;
import com.samsung.magicinfo.restapi.system.model.V2L1MenuResource;
import com.samsung.magicinfo.restapi.system.model.V2LoginConfigResource;
import com.samsung.magicinfo.restapi.system.model.V2MagicInfoConfigResource;
import com.samsung.magicinfo.restapi.system.model.V2MagicInfoVersionResource;
import com.samsung.magicinfo.restapi.system.model.V2RuleManagerSystemInfoResource;
import com.samsung.magicinfo.restapi.system.model.V2SystemAlertResource;
import com.samsung.magicinfo.restapi.system.service.V2SystemService;
import com.samsung.magicinfo.restapi.utils.RestAPIUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.Authorization;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.WebUtils;

@Api(
   value = " APIs to Get System Information",
   description = "System Related API Group",
   tags = {"System API Group"}
)
@RestController
@RequestMapping({"/restapi/v2.0/sms/system"})
@Validated
@ApiVersion({2.0D})
public class V2SystemController {
   @Autowired
   private V2SystemService v2SystemService;
   private final Logger logger = LoggingManagerV2.getLogger(this.getClass());

   public V2SystemController() {
      super();
   }

   @ApiOperation(
      value = "Get all menu information",
      notes = "Get all menu information.",
      authorizations = {@Authorization("api_key")}
   )
   @GetMapping(
      value = {"/menus"},
      produces = {"application/json"}
   )
   public ResponseEntity getMenus(HttpServletRequest request, @RequestParam(value = "onlyMenu",required = false,defaultValue = "false") Boolean onlyMenu) throws Exception {
      this.logger.info("[REST_v2.0][SYSTEM][getMenus] Get all menu information");
      List menus = this.v2SystemService.getMenus(onlyMenu);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(menus);
      this.logger.info("[REST_v2.0][SYSTEM][getMenus] finish successfully.");
      HttpHeaders headers = new HttpHeaders();
      headers.setCacheControl("no-cache, no-store, max-age=0, must-revalidate");
      return new ResponseEntity(responseBody, headers, HttpStatus.OK);
   }

   @KPI
   @ApiOperation(
      value = "Get specific menu information",
      notes = "Get specific menu information.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "menuId",
   required = true,
   dataType = "string",
   value = "Id value of specific menu",
   allowableValues = "RULESET, CONTENT, PLAYLIST, SCHEDULE, DEVICE, STATISTICS, USER, SETTING"
)})
   @GetMapping(
      value = {"/menus/{menuId}"},
      produces = {"application/json"}
   )
   public ResponseEntity getMenu(@Pattern(regexp = "RULESET|CONTENT|PLAYLIST|SCHEDULE|DEVICE|STATISTICS|USER|SETTING|INSIGHT",message = "[menuId] Not allowed value.") @NotNull @NotEmpty @PathVariable("menuId") String menuId, @RequestParam(value = "onlyMenu",required = false,defaultValue = "false") Boolean onlyMenu) throws Exception {
      this.logger.info("[REST_v2.0][SYSTEM][getMenu] Get specific menu information");
      V2L1MenuResource menu = this.v2SystemService.getMenu(menuId, onlyMenu);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(menu);
      this.logger.info("[REST_v2.0][SYSTEM][getMenu] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @KPI
   @ApiOperation(
      value = "Get system information of the 'Insight'.",
      notes = "Get system information of the 'Insight'.",
      authorizations = {@Authorization("api_key")}
   )
   @GetMapping(
      value = {"/insight/systems"},
      produces = {"application/json"}
   )
   public ResponseEntity getInsightSystemInformation(HttpServletRequest request) throws Exception {
      String token = request.getHeader(TokenUtils.tokenHeader);
      Cookie cookie = WebUtils.getCookie(request, "MagicInfoPremiumLanguage");
      String cookieValue = null;
      if (cookie != null) {
         cookieValue = cookie.getValue();
      }

      V2InsightSystemInfoResource resource = this.v2SystemService.getInsightSystemInfo(token, cookieValue);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get alerts.",
      notes = "Get alerts.",
      authorizations = {@Authorization("api_key")}
   )
   @GetMapping(
      value = {"/alerts"},
      produces = {"application/json"}
   )
   public ResponseEntity getAlertList(HttpServletRequest request) throws Exception {
      List result = new ArrayList();
      User currentUser = SecurityUtils.getUserContainer().getUser();
      if (!"Server Administrator".equalsIgnoreCase(currentUser.getRole_name()) && !"Administrator".equalsIgnoreCase(currentUser.getRole_name())) {
         return null;
      } else {
         SlmLicenseManager slmMgr = SlmLicenseManagerImpl.getInstance();
         if (slmMgr.hasOldLicense()) {
            List params = new ArrayList();
            Map temp = new HashMap();
            temp.put("MIS_SID_CAAPR_UNIFIED_LICENSE", "Unified License 2(BW-MIP70PA)");
            params.add(temp);
            V2SystemAlertResource alert = new V2SystemAlertResource(V2SystemAlertResource.AlertLevel.ALERT, "licence_info_1");
            alert.addMultiLanguage("MIS_SID_MIX_CAAPR_CURRENT_SVR_RECOMMEND_USING", params, "Current server recommends using <<A>>.");
            alert.addMultiLanguage("MIS_SID_MIX_CAAPR_FOR_SEAMLESS_UPGRADE_URL_OR_PURCHASE", params, "For seamless server use, such as upgrades, visit www.secslm.com or contact the place of purchage to change to <<A>>.");
            result.add(alert);
         }

         long expired = slmMgr.hasMigrationLicense();
         if (expired >= 0L) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            List params = new ArrayList();
            Map temp = new HashMap();
            temp.put("MIS_SID_CAAPR_MIGRATION_LICENSE", "Migration License");
            params.add(temp);
            temp = new HashMap();
            temp.put("EXPIRED_DATE", sdf.format(new Date(expired)));
            params.add(temp);
            V2SystemAlertResource alert = new V2SystemAlertResource(V2SystemAlertResource.AlertLevel.ALERT, "licence_info_2");
            alert.addMultiLanguage("MIS_SID_MIX_CAAPR_EXPIREATION_DATE_FOR", params, "The expiration date for <<A>> is <<B>>.");
            alert.addMultiLanguage("MIS_SID_CAAPR_U_MUST_NEW_LICENSE_BEFORE_EXPIRATION", (List)null, "You must obtain a new license before it expires.");
            result.add(alert);
         }

         long expiredTime = slmMgr.chkValidationOfServerVersion();
         if (expiredTime != 0L) {
            List params = new ArrayList();
            Map temp = new HashMap();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            temp.put("EXPIRED_DATE", sdf.format(new Date(expiredTime)));
            params.add(temp);
            V2SystemAlertResource alert = new V2SystemAlertResource(V2SystemAlertResource.AlertLevel.ALERT, "maintenance_license_alert");
            alert.addMultiLanguage("MIS_SID_CAFEB_MT_LICENSE_EXPIRED_OR_NOT", (List)null, "Please obtain a new maintenance license.");
            alert.addMultiLanguage("MIS_SID_CAFEB_PLZ_OBTAIN_NEW_MT_LICENSE", (List)null, "Maintenance license does not exist..");
            alert.addMultiLanguage("MIS_SID_MIX_CAFEB_EXPIRATION_CURRENT_IF_NOT_ISSUE_WILL_LIMIT", params, "The expiration date for current server license is <<A>>.\nIf you are not issued with a maintenance license in that time, your server use will be limited.");
            result.add(alert);
         }

         return new ResponseEntity(result, HttpStatus.OK);
      }
   }

   @KPI
   @ApiOperation(
      value = "Get system information of the rule manager.",
      notes = "Get system information of the rule manager."
   )
   @GetMapping(
      value = {"/rulemanager/systems"},
      produces = {"application/json"}
   )
   public ResponseEntity getRuleManagerSystemInformation(HttpServletRequest request) throws Exception {
      V2RuleManagerSystemInfoResource resource = this.v2SystemService.getRuleManagerSystemInfo();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get MagicInfo version.",
      notes = "Get MagicInfo version."
   )
   @GetMapping(
      value = {"/versions"},
      produces = {"application/json"}
   )
   public ResponseEntity getMagicInfoVersion(HttpServletRequest request) throws Exception {
      V2MagicInfoVersionResource resource = this.v2SystemService.getMagicInfoVersion();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get Time of MagicInfo Server.",
      notes = "Get Time of MagicInfo Server.",
      authorizations = {@Authorization("api_key")}
   )
   @GetMapping(
      value = {"/time"},
      produces = {"application/json"}
   )
   public ResponseEntity getTimeOfMagicServer(HttpServletRequest request) throws Exception {
      String timestamp = this.v2SystemService.getTimeOfMagicInfoServer();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(timestamp);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get MagicINFO Server Config",
      notes = "Get MagicINFO Server Config"
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "configIds",
   value = "You can enter it alone, or you can enter multiples separated by ','.\nAvailable IDs: MAGICINFO_VERSION, SCHEDULE_MAX_CHANNEL_COUNT",
   dataType = "string",
   required = true
)})
   @GetMapping(
      value = {"/configs/{configIds}"},
      produces = {"application/json"}
   )
   public ResponseEntity getConfigs(@PathVariable("configIds") String configIds) throws Exception {
      this.logger.info("[REST_v2.0][SYSTEM][getConfigs] start.");
      V2MagicInfoConfigResource resource = this.v2SystemService.getConfig(configIds.split(","));
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      this.logger.info("[REST_v2.0][SYSTEM][getConfigs] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get Content Download Throttling Status",
      notes = "",
      authorizations = {@Authorization("api_key")}
   )
   @GetMapping(
      value = {"/content-download/throttling"},
      produces = {"application/json"}
   )
   public ResponseEntity getDownloadThrottlingStatus() throws Exception {
      V2ContentDownloadThrottling pool = this.v2SystemService.getDownloadThrottlingStatus();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(pool);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get result of checking db-scheme and checking items",
      notes = "",
      authorizations = {@Authorization("api_key")}
   )
   @GetMapping(
      value = {"/db-scheme"},
      produces = {"application/json"}
   )
   public ResponseEntity getDbScheme() throws Exception {
      List dbSchemeResourceList = this.v2SystemService.getResultOfCheckingDbSchemeAndItemsFromJson();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(dbSchemeResourceList);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Check DB scheme and checking items",
      notes = "",
      authorizations = {@Authorization("api_key")}
   )
   @PostMapping(
      value = {"/db-scheme"},
      produces = {"application/json"}
   )
   public ResponseEntity checkDbSchemeAndCheckingItems() throws Exception {
      List v2DbSchemeResource = this.v2SystemService.checkDbSchemeAndItemsFromJson();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(v2DbSchemeResource);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Solve the problematic items.",
      notes = "",
      authorizations = {@Authorization("api_key")}
   )
   @PutMapping(
      value = {"/db-scheme/{testId}"},
      produces = {"application/json"}
   )
   public ResponseEntity resolveDbSchemeIssue(@PathVariable("testId") int testId) throws Exception {
      V2DbSchemeResource v2DbSchemeResource = this.v2SystemService.resolveDbSchemeIssue(testId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(v2DbSchemeResource);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get MagicINFO Server Login Config",
      notes = "Get MagicINFO Server Login Config"
   )
   @GetMapping(
      value = {"/login-config"},
      produces = {"application/json"}
   )
   public ResponseEntity getLoginConfig() throws Exception {
      this.logger.info("[REST_v2.0][SYSTEM][getLoginConfig] start.");
      V2LoginConfigResource resource = this.v2SystemService.getLoginConfig();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      this.logger.info("[REST_v2.0][SYSTEM][getLoginConfig] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Upload login page image file",
      notes = "Upload login page image file",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "file",
   value = "login page image file",
   required = true,
   dataType = "MultipartFile"
)})
   @ApiResponses({@ApiResponse(
   code = 400,
   message = "Bad Request \n errorCode : 400002, The version information of file is invalid value."
), @ApiResponse(
   code = 500,
   message = "Internal Server Error \n errorCode : 500601, It is failed to upload the software file."
)})
   @PostMapping(
      value = {"/upload-login-page-image"},
      produces = {"application/json"}
   )
   public ResponseEntity uploadLoginPageImageFile(@NotNull @RequestPart(value = "file",required = true) MultipartFile file, HttpServletRequest request) throws Exception {
      this.logger.info("[REST_v2.0][SYSTEM][uploadLoginPageImageFile] The function that upload login page image file.");
      V2LoginConfigResource result = this.v2SystemService.saveLoginPageImage(file, request);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }
}
