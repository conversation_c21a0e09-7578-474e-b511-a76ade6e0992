package com.samsung.magicinfo.webauthor2.repository.model.device;

import java.io.Serializable;
import java.util.List;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;

public class DeviceGroupListResultListData implements Serializable {
  @XmlElement
  private Integer totalCount;
  
  @XmlElementWrapper(name = "resultList")
  @XmlElement(name = "com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup")
  private List<DeviceGroupData> resultList;
  
  public Integer getTotalCount() {
    return this.totalCount;
  }
  
  public List<DeviceGroupData> getResultList() {
    return this.resultList;
  }
}
