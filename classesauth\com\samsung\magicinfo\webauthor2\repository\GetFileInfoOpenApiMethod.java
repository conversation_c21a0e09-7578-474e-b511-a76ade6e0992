package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.model.FileInfoData;
import com.samsung.magicinfo.webauthor2.repository.model.ResponseFileInfoData;
import java.util.HashMap;
import java.util.Map;
import org.springframework.web.client.RestTemplate;

public class GetFileInfoOpenApiMethod extends OpenApiMethod<FileInfoData, ResponseFileInfoData> {
  private final String userId;
  
  private final String token;
  
  private final String fileId;
  
  public GetFileInfoOpenApiMethod(RestTemplate restTemplate, String userId, String token, String fileId) {
    super(restTemplate);
    this.userId = userId;
    this.token = token;
    this.fileId = fileId;
  }
  
  protected final String getOpenApiClassName() {
    return "CommonContentService";
  }
  
  protected final String getOpenApiMethodName() {
    return "getFileInfo";
  }
  
  protected final Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("userId", this.userId);
    vars.put("token", this.token);
    vars.put("fileId", this.fileId);
    return vars;
  }
  
  public final Class<ResponseFileInfoData> getResponseClass() {
    return ResponseFileInfoData.class;
  }
  
  public final FileInfoData convertResponseData(ResponseFileInfoData responseFileInfoData) {
    return responseFileInfoData.getResponseClass();
  }
}
