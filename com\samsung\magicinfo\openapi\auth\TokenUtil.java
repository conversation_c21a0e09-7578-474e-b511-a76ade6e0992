package com.samsung.magicinfo.openapi.auth;

import com.samsung.common.logger.LoggingManagerV2;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpMethod;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.logging.log4j.Logger;

public class TokenUtil {
   private static final long RESPONSE_WAIT_TIMEOUT = 5000L;
   protected static Logger logger = LoggingManagerV2.getLogger(TokenUtil.class);

   public TokenUtil() {
      super();
   }

   public static String getAuthToken(HttpServletRequest request, String userName, String password) throws MalformedURLException {
      String address = getCurrentURL(request).toString();
      String token = getAuthToken(address, userName, password);
      return token;
   }

   public static String getAuthToken(String address, String userName, String password) {
      String parameterizedURL = address + "/openapi/auth?cmd=getAuthToken&id=" + userName + "&pw=" + password;
      HttpClient client = new HttpClient();
      HttpMethod request = new GetMethod(parameterizedURL);
      String token = "";

      String var11;
      try {
         TokenUtil.TokenRequest tokenRequest = new TokenUtil.TokenRequest(client, request);
         FutureTask task = new FutureTask(tokenRequest);
         Thread th = new Thread(task);
         th.start();
         int statusCode = (Integer)task.get(5000L, TimeUnit.MILLISECONDS);
         if (statusCode == 200) {
            byte[] responseBody = request.getResponseBody();
            token = new String(responseBody, Charset.defaultCharset());
            int beginIndex = token.lastIndexOf("<responseClass class=\"String\">") + "<responseClass class=\"String\">".length();
            int endIndex = token.indexOf("</response");
            if (endIndex > beginIndex) {
               token = token.substring(beginIndex, endIndex);
               logger.debug("Recieving token " + token + " from the target host. ");
            } else {
               logger.error("Unable to receive token from the target host. Response: " + token);
            }

            return token;
         }

         logger.error("Recieving incorrect response from the target host, unable to get authorization token. " + request.getStatusLine());
         var11 = token;
      } catch (IOException var19) {
         logger.error("Unable to receive token from the target host. Cannot get response body. ", var19);
         return token;
      } catch (ExecutionException | InterruptedException var20) {
         logger.error("Unable to receive token from the target host. Cannot get response from the target host. ", var20);
         return token;
      } catch (TimeoutException var21) {
         logger.error("Unable to receive token from the target host. Waiting response time is up. ", var21);
         return token;
      } finally {
         request.releaseConnection();
      }

      return var11;
   }

   private static URL getCurrentURL(HttpServletRequest request) throws MalformedURLException {
      String currentHostUrlStr = request.getRequestURL().toString();
      String contextPath = request.getContextPath();
      currentHostUrlStr = currentHostUrlStr.replaceFirst(contextPath + ".*", contextPath);
      URL currentUrl = new URL(currentHostUrlStr);
      if (currentUrl == null || currentUrl.getProtocol().isEmpty() || currentUrl.getHost().isEmpty() || currentUrl.getPath().isEmpty() || currentUrl.getProtocol().isEmpty()) {
         currentUrl = null;
      }

      return currentUrl;
   }

   private static class TokenRequest implements Callable {
      private HttpClient client;
      private HttpMethod method;

      TokenRequest(HttpClient cl, HttpMethod m) {
         super();
         this.method = m;
         this.client = cl;
      }

      public Integer call() throws IOException {
         return this.client.executeMethod(this.method);
      }
   }
}
