package com.samsung.magicinfo.webauthor2.util;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.exception.service.FileItemValidationException;
import com.samsung.magicinfo.webauthor2.properties.MagicInfoProperties;
import java.util.List;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

@Component
public class MultipartFilenameValidator {
  private static final Logger logger = LoggerFactory.getLogger(MultipartFilenameValidator.class);
  
  private MagicInfoProperties magicInfoProperties;
  
  private FileNameValidator fileNameValidator;
  
  @Autowired
  public MultipartFilenameValidator(MagicInfoProperties magicInfoProperties, FileNameValidator fileNameValidator) {
    this.magicInfoProperties = magicInfoProperties;
    this.fileNameValidator = fileNameValidator;
  }
  
  public void validateFileItems(List<MultipartFile> fileItems) throws FileItemValidationException {
    if (fileItems.size() > this.magicInfoProperties.getWebauthorUploadMaxFilesQuantity())
      throw new FileItemValidationException(500, "TooManyFiles"); 
  }
  
  public void validateFileItem(MultipartFile fileItem) throws FileItemValidationException {
    if (fileItem == null) {
      logger.error("File doesn't exist");
      throw new FileItemValidationException(500, "FileNotExist");
    } 
    if (fileItem.getSize() <= 0L) {
      logger.error("Invalid file size");
      throw new FileItemValidationException(500, "FileSizeZero");
    } 
    String originalFilename = fileItem.getOriginalFilename();
    if (this.fileNameValidator.filenameContainsSpecialCharacters(originalFilename)) {
      logger.error("Invalid characters in filename");
      throw new FileItemValidationException(500, "SpecialCharFileName");
    } 
    if (this.fileNameValidator.filenameStartsWithDotOrDoesntHaveAny(originalFilename)) {
      logger.error("Filename doesn't have extension or only dot is in the beggining");
      throw new FileItemValidationException(500, "InvalidFileName");
    } 
    if (this.fileNameValidator.filenameHasInvalidType(originalFilename)) {
      logger.error("Invalid file type: " + FilenameUtils.getExtension(originalFilename));
      throw new FileItemValidationException(500, "InvalidFileType");
    } 
  }
  
  public void validateSupportFileItem(MultipartFile fileItem) throws FileItemValidationException {
    if (fileItem == null) {
      logger.error("File doesn't exist");
      throw new FileItemValidationException(500, "FileNotExist");
    } 
    if (fileItem.getSize() <= 0L) {
      logger.error("Invalid file size");
      throw new FileItemValidationException(500, "FileSizeZero");
    } 
    String message = validateName(fileItem.getOriginalFilename());
    if (!Strings.isNullOrEmpty(message)) {
      logger.error(message);
      throw new FileItemValidationException(500, message);
    } 
  }
  
  public void validateZipFile(MultipartFile fileItem) throws FileItemValidationException {
    if (fileItem == null) {
      logger.error("File doesn't exist");
      throw new FileItemValidationException(500, "FileNotExist");
    } 
    if (fileItem.getSize() <= 0L) {
      logger.error("Invalid file size");
      throw new FileItemValidationException(500, "FileSizeZero");
    } 
    String message = validateName(fileItem.getOriginalFilename());
    if (!Strings.isNullOrEmpty(message)) {
      logger.error(message);
      throw new FileItemValidationException(500, message);
    } 
  }
  
  public String validateName(String name) {
    String issue = null;
    if (this.fileNameValidator.filenameContainsSpecialCharacters(name))
      issue = "SpecialCharFileName"; 
    if (this.fileNameValidator.filenameStartsWithDotOrDoesntHaveAny(name))
      issue = "InvalidFileName"; 
    if (this.fileNameValidator.filenameHasExecutableType(name))
      issue = "InvalidFileType [" + FilenameUtils.getExtension(name) + "]"; 
    return issue;
  }
  
  public FileNameValidator getFileNameValidator() {
    return this.fileNameValidator;
  }
}
