package com.samsung.magicinfo.cms.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.samsung.common.constants.CommonDataConstants;
import io.swagger.annotations.ApiModelProperty;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

@JsonInclude(Include.NON_NULL)
public class ContentResource {
   private String contentId;
   private Long versionId;
   private String contentName;
   private String mediaType;
   private String creatorId;
   @ApiModelProperty(
      example = "2016-01-01 00:00:00"
   )
   private String createDate;
   @ApiModelProperty(
      example = "2016-01-01 00:00:00"
   )
   private String lastModifiedDate;
   private Long totalSize;
   private Long playTime;
   private String resolution;
   private String isDeleted;
   private String isActive;
   private int shareFlag;
   private String mainFileUrl;
   @JsonIgnore
   private String sessionId;
   @JsonIgnore
   private String contentMetaData;
   private Long groupId;
   private String groupName;
   private String mainFileId;
   private String thumbFileId;
   private String sfiFileId;
   private List arrFileList;
   private String mainFileName;
   private String thumbFilePath;
   private String thumbFileName;
   private String isLinearVwl;
   private String modelCountInfo;
   private Map modelCountMap;
   private int screenCount;
   private int xCount;
   private int yCount;
   private int xRange;
   private int yRange;
   private String isStreaming;
   private long organizationId;
   private String orgCreatorId;
   private boolean existFlv;
   private String mainFileExtension;
   private int contentOrder;
   private Long contentDuration;
   private int vwlVersion;
   private Boolean multiVwl;
   private String deviceType;
   private float deviceTypeVersion;
   private int pollingInterval;
   private String playTimeMilli;
   private String isUsedTemplate;
   private Long templatePageCount;
   private String approvalStatus;
   private String approvalOpinion;
   private String streamingUrl;
   private List tagList;
   private List categoryList;

   public ContentResource() {
      super();
      this.deviceTypeVersion = CommonDataConstants.TYPE_VERSION_1_0;
      this.contentId = "";
      this.versionId = 0L;
      this.contentName = "";
      this.mediaType = "";
      this.creatorId = "";
      new Timestamp(0L);
      this.createDate = "";
      this.lastModifiedDate = "";
      this.totalSize = 0L;
      this.playTime = 0L;
      this.resolution = "";
      this.isDeleted = "N";
      this.isActive = "N";
      this.shareFlag = 0;
      this.groupId = 0L;
      this.groupName = "";
      this.mainFileId = "";
      this.thumbFileId = "";
      this.sfiFileId = "";
      this.mainFileName = "";
      this.thumbFilePath = "";
      this.thumbFileName = "";
      this.isLinearVwl = "N";
      this.isStreaming = "N";
      this.organizationId = 0L;
      this.orgCreatorId = "";
      this.existFlv = false;
      this.mainFileExtension = "";
      this.contentOrder = 0;
      this.contentDuration = 0L;
      this.vwlVersion = 0;
      this.pollingInterval = 0;
      this.isUsedTemplate = "N";
      this.templatePageCount = 0L;
      this.deviceType = "";
      this.deviceTypeVersion = CommonDataConstants.TYPE_VERSION_1_0;
      this.multiVwl = false;
      this.playTimeMilli = "";
      this.approvalOpinion = "";
      this.streamingUrl = "";
   }

   public List getTagList() {
      return this.tagList;
   }

   public void setTagList(List tagList) {
      this.tagList = tagList;
   }

   public List getCategoryList() {
      return this.categoryList;
   }

   public void setCategoryList(List categoryList) {
      this.categoryList = categoryList;
   }

   public String getStreamingUrl() {
      return this.streamingUrl;
   }

   public void setStreamingUrl(String streamingUrl) {
      this.streamingUrl = streamingUrl;
   }

   public String getContentId() {
      return this.contentId;
   }

   public void setContentId(String contentId) {
      this.contentId = contentId;
   }

   public Long getVersionId() {
      return this.versionId;
   }

   public void setVersionId(Long versionId) {
      this.versionId = versionId;
   }

   public String getContentName() {
      return this.contentName;
   }

   public void setContentName(String contentName) {
      this.contentName = contentName;
   }

   public String getMediaType() {
      return this.mediaType;
   }

   public void setMediaType(String mediaType) {
      this.mediaType = mediaType;
   }

   public String getCreatorId() {
      return this.creatorId;
   }

   public void setCreatorId(String creatorId) {
      this.creatorId = creatorId;
   }

   public String getCreateDate() {
      return this.createDate;
   }

   public void setCreateDate(String createDate) {
      this.createDate = createDate;
   }

   public String getLastModifiedDate() {
      return this.lastModifiedDate;
   }

   public void setLastModifiedDate(String lastModifiedDate) {
      this.lastModifiedDate = lastModifiedDate;
   }

   public Long getTotalSize() {
      return this.totalSize;
   }

   public void setTotalSize(Long totalSize) {
      this.totalSize = totalSize;
   }

   public Long getPlayTime() {
      return this.playTime;
   }

   public void setPlayTime(Long playTime) {
      this.playTime = playTime;
   }

   public String getResolution() {
      return this.resolution;
   }

   public void setResolution(String resolution) {
      this.resolution = resolution;
   }

   public String getIsDeleted() {
      return this.isDeleted;
   }

   public void setIsDeleted(String isDeleted) {
      this.isDeleted = isDeleted;
   }

   public String getIsActive() {
      return this.isActive;
   }

   public void setIsActive(String isActive) {
      this.isActive = isActive;
   }

   public int getShareFlag() {
      return this.shareFlag;
   }

   public void setShareFlag(int shareFlag) {
      this.shareFlag = shareFlag;
   }

   public String getSessionId() {
      return this.sessionId;
   }

   public void setSessionId(String sessionId) {
      this.sessionId = sessionId;
   }

   public String getContentMetaData() {
      return this.contentMetaData;
   }

   public void setContentMetaData(String contentMetaData) {
      this.contentMetaData = contentMetaData;
   }

   public Long getGroupId() {
      return this.groupId;
   }

   public void setGroupId(Long groupId) {
      this.groupId = groupId;
   }

   public String getGroupName() {
      return this.groupName;
   }

   public void setGroupName(String groupName) {
      this.groupName = groupName;
   }

   public String getMainFileId() {
      return this.mainFileId;
   }

   public void setMainFileId(String mainFileId) {
      this.mainFileId = mainFileId;
   }

   public String getThumbFileId() {
      return this.thumbFileId;
   }

   public void setThumbFileId(String thumbFileId) {
      this.thumbFileId = thumbFileId;
   }

   public String getSfiFileId() {
      return this.sfiFileId;
   }

   public void setSfiFileId(String sfiFileId) {
      this.sfiFileId = sfiFileId;
   }

   public List getArrFileList() {
      return this.arrFileList;
   }

   public void setArrFileList(List arrFileList) {
      this.arrFileList = arrFileList;
   }

   public String getMainFileName() {
      return this.mainFileName;
   }

   public void setMainFileName(String mainFileName) {
      this.mainFileName = mainFileName;
   }

   public String getThumbFilePath() {
      return this.thumbFilePath;
   }

   public void setThumbFilePath(String thumbFilePath) {
      this.thumbFilePath = thumbFilePath;
   }

   public String getThumbFileName() {
      return this.thumbFileName;
   }

   public void setThumbFileName(String thumbFileName) {
      this.thumbFileName = thumbFileName;
   }

   public String getIsLinearVwl() {
      return this.isLinearVwl;
   }

   public void setIsLinearVwl(String isLinearVwl) {
      this.isLinearVwl = isLinearVwl;
   }

   public String getModelCountInfo() {
      return this.modelCountInfo;
   }

   public void setModelCountInfo(String modelCountInfo) {
      this.modelCountInfo = modelCountInfo;
   }

   public Map getModelCountMap() {
      return this.modelCountMap;
   }

   public void setModelCountMap(Map modelCountMap) {
      this.modelCountMap = modelCountMap;
   }

   public int getScreenCount() {
      return this.screenCount;
   }

   public void setScreenCount(int screenCount) {
      this.screenCount = screenCount;
   }

   public int getxCount() {
      return this.xCount;
   }

   public void setxCount(int xCount) {
      this.xCount = xCount;
   }

   public int getyCount() {
      return this.yCount;
   }

   public void setyCount(int yCount) {
      this.yCount = yCount;
   }

   public int getxRange() {
      return this.xRange;
   }

   public void setxRange(int xRange) {
      this.xRange = xRange;
   }

   public int getyRange() {
      return this.yRange;
   }

   public void setyRange(int yRange) {
      this.yRange = yRange;
   }

   public String getIsStreaming() {
      return this.isStreaming;
   }

   public void setIsStreaming(String isStreaming) {
      this.isStreaming = isStreaming;
   }

   public long getOrganizationId() {
      return this.organizationId;
   }

   public void setOrganizationId(long organizationId) {
      this.organizationId = organizationId;
   }

   public String getOrgCreatorId() {
      return this.orgCreatorId;
   }

   public void setOrgCreatorId(String orgCreatorId) {
      this.orgCreatorId = orgCreatorId;
   }

   public boolean isExistFlv() {
      return this.existFlv;
   }

   public void setExistFlv(boolean existFlv) {
      this.existFlv = existFlv;
   }

   public String getMainFileExtension() {
      return this.mainFileExtension;
   }

   public void setMainFileExtension(String mainFileExtension) {
      this.mainFileExtension = mainFileExtension;
   }

   public int getContentOrder() {
      return this.contentOrder;
   }

   public void setContentOrder(int contentOrder) {
      this.contentOrder = contentOrder;
   }

   public Long getContentDuration() {
      return this.contentDuration;
   }

   public void setContentDuration(Long contentDuration) {
      this.contentDuration = contentDuration;
   }

   public int getVwlVersion() {
      return this.vwlVersion;
   }

   public void setVwlVersion(int vwlVersion) {
      this.vwlVersion = vwlVersion;
   }

   public Boolean getMultiVwl() {
      return this.multiVwl;
   }

   public void setMultiVwl(Boolean multiVwl) {
      this.multiVwl = multiVwl;
   }

   public String getDeviceType() {
      return this.deviceType;
   }

   public void setDeviceType(String deviceType) {
      this.deviceType = deviceType;
   }

   public float getDeviceTypeVersion() {
      return this.deviceTypeVersion;
   }

   public void setDeviceTypeVersion(float deviceTypeVersion) {
      this.deviceTypeVersion = deviceTypeVersion;
   }

   public int getPollingInterval() {
      return this.pollingInterval;
   }

   public void setPollingInterval(int pollingInterval) {
      this.pollingInterval = pollingInterval;
   }

   public String getPlayTimeMilli() {
      return this.playTimeMilli;
   }

   public void setPlayTimeMilli(String playTimeMilli) {
      this.playTimeMilli = playTimeMilli;
   }

   public String getIsUsedTemplate() {
      return this.isUsedTemplate;
   }

   public void setIsUsedTemplate(String isUsedTemplate) {
      this.isUsedTemplate = isUsedTemplate;
   }

   public Long getTemplatePageCount() {
      return this.templatePageCount;
   }

   public void setTemplatePageCount(Long templatePageCount) {
      this.templatePageCount = templatePageCount;
   }

   public String getApprovalStatus() {
      return this.approvalStatus;
   }

   public void setApprovalStatus(String approvalStatus) {
      this.approvalStatus = approvalStatus;
   }

   public String getApprovalOpinion() {
      return this.approvalOpinion;
   }

   public void setApprovalOpinion(String approvalOpinion) {
      this.approvalOpinion = approvalOpinion;
   }

   public String getMainFileUrl() {
      return this.mainFileUrl;
   }

   public void setMainFileUrl(String mainFileUrl) {
      this.mainFileUrl = mainFileUrl;
   }
}
