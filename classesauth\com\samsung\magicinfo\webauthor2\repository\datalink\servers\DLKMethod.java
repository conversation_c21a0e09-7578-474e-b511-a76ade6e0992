package com.samsung.magicinfo.webauthor2.repository.datalink.servers;

import java.io.StringReader;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

public abstract class DLKMethod<T, R> {
  private static final Logger logger = LoggerFactory.getLogger(DLKMethod.class);
  
  private static final String REL_ADDRESS_TO_METHODS = "/DataLink/html/";
  
  private static final String ADDRESS_METHOD_SUFIX = ".do";
  
  private static final String ADDRESS_PORT_SEPARATOR = ":";
  
  private static final String IPV6_BRACKET_OPEN = "[";
  
  private static final String IPV6_BRACKET_CLOSE = "]";
  
  private final RestTemplate restTemplate;
  
  private final String path;
  
  public DLKMethod(RestTemplate restTemplate, Boolean useSsl, String ipAddress, Integer port, String pathsVariables) {
    this.restTemplate = restTemplate;
    this.path = createPath(useSsl, ipAddress, port, pathsVariables);
  }
  
  private String createPath(Boolean useSsl, String ipAddress, Integer port, String pathsVariables) {
    StringBuilder sb = new StringBuilder();
    if (useSsl.booleanValue()) {
      sb.append("https://");
    } else {
      sb.append("http://");
    } 
    Boolean isIPv6 = Boolean.valueOf((StringUtils.countMatches(ipAddress, ":") > 1));
    if (isIPv6.booleanValue()) {
      Boolean hasOpenBracket = Boolean.valueOf((StringUtils.countMatches(ipAddress, "[") > 0));
      Boolean hasCloseBracket = Boolean.valueOf((StringUtils.countMatches(ipAddress, "]") > 0));
      if (false == hasOpenBracket.booleanValue())
        sb.append("["); 
      sb.append(ipAddress);
      if (false == hasCloseBracket.booleanValue())
        sb.append("]"); 
    } else {
      sb.append(ipAddress);
    } 
    sb.append(":");
    sb.append(port);
    sb.append("/DataLink/html/");
    sb.append(getMethodName());
    sb.append(".do");
    if (pathsVariables != null)
      sb.append(pathsVariables); 
    return sb.toString();
  }
  
  protected abstract String getMethodName();
  
  abstract Class<R> getResponseClass();
  
  abstract T convertResponseData(R paramR);
  
  public T callMethod() throws JAXBException {
    HttpHeaders headers = new HttpHeaders();
    headers.set("Accept", "text/plain;charset=utf-8");
    HttpEntity<String> entity = new HttpEntity((MultiValueMap)headers);
    logger.info("Call DLK method from address: " + this.path);
    ResponseEntity<String> responseData = this.restTemplate.exchange(this.path, HttpMethod.GET, entity, String.class, new Object[0]);
    Unmarshaller jaxbUnmarshaller = createMarshaller();
    R responseObj = (R)jaxbUnmarshaller.unmarshal(new StringReader((String)responseData
          .getBody()));
    return convertResponseData(responseObj);
  }
  
  private Unmarshaller createMarshaller() throws JAXBException {
    JAXBContext jaxbContext = JAXBContext.newInstance(new Class[] { getResponseClass() });
    Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
    return jaxbUnmarshaller;
  }
}
