package com.samsung.magicinfo.framework.setup.manager;

import com.samsung.common.db.DBListExecuter;
import com.samsung.common.db.PagedListInfo;
import com.samsung.magicinfo.framework.setup.entity.CompanyInfoEntity;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseEntity;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseHistoryEntity;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseStatusEntity;
import com.samsung.magicinfo.openapi.custom.openEntity.etc.MobileLicenseEntity;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.io.UnsupportedEncodingException;
import java.security.GeneralSecurityException;
import java.security.NoSuchAlgorithmException;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

public interface SlmLicenseManager extends DBListExecuter {
   String BOOL_REG_LIC_SOC = "BOOL_REG_LIC_SOC";
   String BOOL_REG_LIC_LFD = "BOOL_REG_LIC_LFD";
   String BOOL_REG_LIC_VIDEOWALL = "BOOL_REG_LIC_VIDEOWALL";
   String BOOL_REG_LIC_LITE = "BOOL_REG_LIC_LITE";
   String BOOL_REG_LIC_EXTRA = "BOOL_REG_LIC_EXTRA";
   String BOOL_REG_LIC_DATALINK = "BOOL_REG_LIC_DATALINK";
   String BOOL_REG_LIC_MOBILE = "BOOL_REG_LIC_MOBILE";
   String BOOL_REG_LIC_SIGNAGE = "BOOL_REG_LIC_SIGNAGE";
   String BOOL_REG_LIC_ANDROID = "BOOL_REG_LIC_ANDROID";
   String BOOL_REG_LIC_WPLAYER = "BOOL_REG_LIC_WPLAYER";
   String BOOL_REG_LIC_RMS = "BOOL_REG_LIC_RMS";
   String FREE_LIC_PREMIUM_I = "010120-ISEBNS-WFRI5B-LECS";
   String FREE_LIC_LITE = "010311-6YPNK6-KC3EAO-XHEL";
   String FREE_LIC_PREMIUM_S = "010121-XQGZAG-ERMEJG-QAGF";
   String FREE_LIC_MOBILE = "010125-H5DZEB-O6XA5S-QCCF";
   String FREE_LIC_SIGNAGE = "010V31-Q225OK-WXYFXH-YAUE";
   String FREE_LIC_APLAYER = "01011N-FRDGVS-NCRI4K-T6OS";
   String FREE_LIC_ANDROID = "";
   String FREE_LIC_RM = "01064A-RR6UU4-XE2GE5-JLCW";
   String FREE_LIC_UNIVERSAL = "01014A-QXOL6C-FTYQXL-5ICE";
   String FREE_LIC_NEW_UNIVERSAL = "01015A-L6VPSS-MOE2JC-GRZ5";
   String SLM_LICENSE_HISTORY_TYPE_FREE = "00";
   String SLM_LICENSE_HISTORY_TYPE_FAIL = "01";
   String SLM_LICENSE_HISTORY_TYPE_ALARM = "02";
   String SLM_LICENSE_HISTORY_TYPE_OFFLINE_DEACTIVATION = "03";
   String SLM_LICENSE_HISTORY_TYPE_EXPIRED_DELETE = "04";
   String SLM_LICENSE_HISTORY_TYPE_INVALID_LICENSE = "05";
   String SLM_LICENSE_HISTORY_TYPE_E2E_SUCCESS = "E00";
   String SLM_LICENSE_HISTORY_TYPE_E2E_HTTP_FAIL = "E01";
   String SLM_LICENSE_HISTORY_TYPE_E2E_FAIL = "E02";
   String SLM_LICENSE_HISTORY_TYPE_E2E_EXPIRED_DELETE = "E03";
   String PRODUCT_CODE_PREMIUM_I = "010120";
   String PRODUCT_CODE_LITE = "010311";
   String PRODUCT_CODE_DATALINK = "010126";
   String PRODUCT_CODE_PREMIUM_S = "010121";
   String PRODUCT_CODE_TIZEN = "010121";
   String PRODUCT_CODE_SIGNAGE = "010V31";
   String PRODUCT_CODE_ANDROID = "01011N";
   String PRODUCT_CODE_RMS = "01064A";
   String PRODUCT_CODE_UNIVERSAL = "01014A";
   String PRODUCT_CODE_NEW_UNIVERSAL = "01015A";
   String PRODUCT_CODE_MIGRATION = "01010M";
   String PRODUCT_CODE_E2E_SERVER = "0101CS";
   String PRODUCT_CODE_ANALYTICS_STORE = "010D0E";
   String PRODUCT_CODE_ANALYTICS_DISPLAY = "010D0F";
   String CHARGED = "11";
   String FREE_OF_CHARGED = "12";
   String FREE = "13";
   String LIC_FILE = "license.magic";
   int SLM_SUCCESS = 0;
   int SLM_DB_ERROR = 500;
   int SLM_ACTIVATION_ERROR = 600;
   int SLM_OFFLINE_HWKEY_ERROR = 700;
   int SLM_LICENSE_ERROR = 800;
   int SLM_LICENSE_DUPLICATE = 900;
   int SLM_LICENSE_EXPIRED = 901;
   int SLM_INTERNAL_ERROR = 1000;
   int SLM_SDK_BASIC_EXCEPTION = 4000;
   int SLM_NEW_ACTIVATION = 19002;
   long SLM_VALID_VERSION = 0L;
   String SLM_REST_API_SYSCODE_CLOUD = "MI-CLOUD";
   String SLM_REST_API_LICENSE_KEY_ISSUED_TYPE_NEW_GENERATION = "01";
   String SLM_REST_API_MODEL_CODE_CLOUD = "BW-MCD41PS";
   String SLM_REST_API_HW_TYPE_MAC = "01";
   String SLM_REST_API_HW_TYPE_SN = "02";
   String SLM_REST_API_HW_TYPE_ETC = "03";
   String SLM_REST_API_EXTENSION_Y = "Y";
   String SLM_REST_API_EXTENSION_N = "N";
   String LIC_TRIAL_MAC = "01-01-01-01-01-01";

   PagedListInfo getPagedList(int var1, int var2, Map var3, String var4) throws Exception;

   String getHWUniqueKey() throws Exception;

   SlmLicenseEntity getSlmLicenseEntity(String var1) throws Exception;

   int activationRequest(String var1, String var2, String var3, String var4, String var5, String var6, String var7) throws Exception;

   int changeActivationRequest(String var1) throws Exception;

   int activationActive(String var1, String var2) throws Exception;

   int activationActiveForE2E(String var1, String var2, String var3, String var4) throws Exception;

   int deActivationRequest(String var1) throws Exception;

   void printActivationKeyInfo(String var1) throws Exception;

   String getErrMsg(int var1);

   void SlmlicenseLoadingConfig();

   MobileLicenseEntity getMobileLicenseEntity();

   int getCntLicenseMaxClient(String var1) throws SQLException;

   int getCntSlmLicenseMaxClients(String var1, String var2) throws SQLException;

   void checkDBSlmLicense() throws Exception;

   void checkSlmLicenseExpired() throws Exception;

   boolean deleteAllLicense() throws SQLException;

   boolean addDeviceSamples(String var1, int var2) throws SQLException;

   boolean addSocDeviceSamples(String var1, int var2) throws SQLException;

   boolean addLiteDeviceSamples(String var1, int var2) throws SQLException;

   int licenseKeyVerify(String var1) throws ConfigException, SQLException;

   int licenseKeyDuplicate(String var1);

   String getLicenseKeyfromActivationKey(String var1);

   String getActivationKey(String var1) throws SQLException;

   int changeActivationActive(String var1, String var2) throws Exception;

   String deActivationActive(String var1) throws Exception;

   boolean checkFreeSlmLicense(String var1) throws SQLException;

   String getProductCode(String var1);

   boolean checkProductCode(String var1);

   void setSlmLicenseHistory(String var1, String var2, String var3, Boolean var4) throws ConfigException, SQLException;

   void setSlmLicenseHistoryForE2E(String var1, String var2, String var3, String var4, String var5, Boolean var6) throws ConfigException, SQLException;

   SlmLicenseHistoryEntity getSlmLicenseHistory(String var1) throws SQLException;

   int getCntAllSlmLicenseHistoryForE2E() throws SQLException;

   List getAllSlmLicenseHistoryForE2E(int var1) throws SQLException;

   int getCntSlmLicenseHistoryForE2EByDeviceId(String var1) throws SQLException;

   List getSlmLicenseHistoryForE2EByDeviceId(String var1, int var2) throws SQLException;

   int hwUniqueKeyVerify(String var1);

   SlmLicenseEntity findTrialKeyFromLicenseList(String var1);

   SlmLicenseEntity findFreeOfChargedKeyFromLicenseList(String var1);

   List getAllSlmLicense() throws SQLException;

   List getAllSlmLicenseHistory(int var1);

   List getAllSlmLicenseHistory();

   int getCntSlmLicenseHistory();

   boolean TestAddLicenseKey(String var1, String var2) throws ConfigException, SQLException;

   int getCntSlmLicense() throws SQLException;

   String getRegistryValue(String var1) throws Exception;

   boolean licenseChk();

   boolean licenseChk(String var1) throws SQLException;

   List getInvaildLicenseList() throws SQLException;

   List getListLicenseStatus() throws SQLException;

   boolean addLicenseStatus(String var1) throws SQLException;

   SlmLicenseStatusEntity getLicenseStaus(String var1) throws SQLException;

   String decrypt(String var1) throws NoSuchAlgorithmException, GeneralSecurityException, UnsupportedEncodingException;

   boolean chkHasMaintenance();

   boolean hasChargedLicense(String var1) throws Exception;

   long chkValidationOfServerVersion();

   void chkSlmLicenseStatus(String var1, Timestamp var2, Timestamp var3) throws SQLException;

   boolean delLicenseStatus(String var1) throws SQLException;

   List getDeviceListWithProductCode(String var1) throws SQLException;

   String errorCodeToMessage(int var1, Locale var2);

   boolean isOrgSetLicenseNum(String var1, long var2) throws SQLException;

   long getMaximumNumMovable(String var1, long var2) throws SQLException;

   int getCntSlmLicenseByProductCodeList(List var1) throws SQLException;

   int getLicenseCountByDeviceType(String var1) throws SQLException;

   int getLicenseCountByProductCode(String var1) throws SQLException;

   int getRemainLicenseCountByDeviceType(String var1) throws SQLException;

   int getRemainLicenseCountByProductCode(String var1) throws SQLException;

   List getDeviceListByProductCode(String var1) throws SQLException;

   Set getSupportProductCode();

   String getProductNameByDeviceType(String var1);

   int getDeviceCountByProductCode(String var1) throws SQLException;

   void initSlmLicenseOrgInfo();

   void checkDBSlmLicenseForE2E() throws Exception;

   void activationProcessForE2E(String var1, String var2, String var3, String var4, String var5, String var6) throws Exception;

   boolean reActivationProcessForE2E(String var1) throws Exception;

   boolean deActivationProcessForE2E(String var1) throws Exception;

   boolean swapProcessForE2E(SlmLicenseEntity var1, String var2, String var3, String var4) throws Exception;

   CompanyInfoEntity getCompanyInfo() throws Exception;

   boolean insertCompanyInfo(CompanyInfoEntity var1) throws Exception;

   boolean modifyDeviceLocationProcessForE2E(String var1, String var2) throws Exception;

   boolean hasOldLicense() throws Exception;

   long hasMigrationLicense() throws Exception;

   void activationProcessForE2E_SLMDirect(String var1, String var2, String var3, String var4, String var5, String var6) throws Exception;

   boolean reActivationProcessForE2E_SLMDirect(String var1) throws Exception;

   boolean deActivationProcessForE2E_SLMDirect(String var1) throws Exception;

   boolean swapProcessForE2E_SLMDirect(SlmLicenseEntity var1, String var2, String var3, String var4) throws Exception;

   boolean modifyDeviceLocationProcessForE2E_SLMDirect(String var1, String var2) throws Exception;
}
