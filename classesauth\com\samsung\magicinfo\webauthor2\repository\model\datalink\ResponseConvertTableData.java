package com.samsung.magicinfo.webauthor2.repository.model.datalink;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "response")
public class ResponseConvertTableData implements Serializable {
  private static final long serialVersionUID = 5099397803587960282L;
  
  @XmlAttribute
  private String code;
  
  @XmlElement
  private String errorMessage;
  
  @XmlElement
  private ConvertTableResultListData responseClass;
  
  public String getCode() {
    return this.code;
  }
  
  public String getErrorMessage() {
    return this.errorMessage;
  }
  
  public ConvertTableResultListData getResponseClass() {
    return this.responseClass;
  }
}
