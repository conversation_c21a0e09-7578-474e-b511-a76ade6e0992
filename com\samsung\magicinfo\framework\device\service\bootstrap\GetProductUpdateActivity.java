package com.samsung.magicinfo.framework.device.service.bootstrap;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.DBCacheUtils;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.BackupPlayEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceControl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceNetworkConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemSetupConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTag;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceConnHistoryInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceConnHistoryInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceQrtzCheckInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceQrtzCheckInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManagerImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocInfo;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocInfoImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManager;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManagerImpl;
import com.samsung.magicinfo.framework.device.rmrule.entity.DeviceRMRule;
import com.samsung.magicinfo.framework.device.rmrule.manager.DeviceRMRuleInfo;
import com.samsung.magicinfo.framework.device.rmrule.manager.DeviceRMRuleInfoImpl;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManager;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManagerImpl;
import com.samsung.magicinfo.framework.device.software.manager.SoftwareManager;
import com.samsung.magicinfo.framework.device.software.manager.SoftwareManagerImpl;
import com.samsung.magicinfo.framework.device.vwlLayout.entity.VwlLayout;
import com.samsung.magicinfo.framework.device.vwlLayout.manager.VwlLayoutManager;
import com.samsung.magicinfo.framework.device.vwlLayout.manager.VwlLayoutManagerImpl;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.setup.entity.TagEntity;
import com.samsung.magicinfo.framework.setup.manager.DatalinkServerImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.TagInfoImpl;
import com.samsung.magicinfo.protocol.exception.ServiceException;
import com.samsung.magicinfo.protocol.reservation.ReservationManager;
import com.samsung.magicinfo.protocol.reservation.ReservationManagerImpl;
import com.samsung.magicinfo.protocol.reservation.ScheduleReservation;
import com.samsung.magicinfo.protocol.rmql.RMQL;
import com.samsung.magicinfo.protocol.rmql.ResultSet;
import com.samsung.magicinfo.protocol.servicemanager.ClientOpActivity;
import com.samsung.magicinfo.protocol.util.BeanUtils;
import com.samsung.magicinfo.protocol.util.RMQLInstanceCreator;
import com.samsung.magicinfo.restapi.edge.model.EdgeServer;
import com.samsung.magicinfo.restapi.edge.service.V2EdgeService;
import com.samsung.magicinfo.restapi.edge.service.V2EdgeServiceImpl;
import java.io.File;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;

public class GetProductUpdateActivity extends ClientOpActivity {
   static Logger logger = LoggingManagerV2.getLogger(GetProductUpdateActivity.class);
   static int rcvPostCnt = 0;
   static int sendSysInfoCnt = 0;

   public GetProductUpdateActivity() {
      super();
   }

   public Object response(ResultSet rs) throws ServiceException {
      String appVer = (String)this.ctxt.getServiceParamMap().get("application_version");
      boolean isVendingMachine = false;
      boolean isLiteDevice = false;
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      isLiteDevice = this.ctxt.getDevice().getDevice_type().equals("LPLAYER");
      if (this.ctxt.getDevice() != null && this.ctxt.getDevice().getDevice_model_code().equals("8000")) {
         isVendingMachine = true;
      }

      String deviceId = null;
      DeviceQrtzCheckInfo deviceQrtz = DeviceQrtzCheckInfoImpl.getInstance();

      try {
         deviceId = this.ctxt.getDevice().getDevice_id();
         Device oldDevice = deviceDao.getDevice(deviceId);
         if (rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.INPUT_SOURCE") != null && rs.getString(".MO.MONITORING_INFO.CURRENT_SCHEDULE.INPUT_SOURCE").equals("1000")) {
            deviceQrtz.deleteDeviceQrtzCheckInfo(deviceId, "POST_BOOTSTRAP_SERVICE");
            logger.error("[MagicInfo_PostBootstrap][" + deviceId + "] Delete device info from qrtz check table");
            return appVer;
         } else {
            String serial_decimal = null;
            if (rs.getString(".MO.DEVICE_CONF.GENERAL.SERIAL_DECIMAL") != null && !rs.getString(".MO.DEVICE_CONF.GENERAL.SERIAL_DECIMAL").equals("FAIL") && !rs.getString(".MO.DEVICE_CONF.GENERAL.SERIAL_DECIMAL").equals("")) {
               serial_decimal = rs.getString(".MO.DEVICE_CONF.GENERAL.SERIAL_DECIMAL");
            }

            String screen_size = rs.getString(".MO.DEVICE_CONF.GENERAL.SCREEN_SIZE");
            String resolution = rs.getString(".MO.DEVICE_CONF.GENERAL.RESOLUTION");
            String firmware_version = rs.getString(".MO.DEVICE_CONF.GENERAL.FIRMWARE_VERSION");
            String firmware_indicators = rs.getString(".MO.DEVICE_CONF.GENERAL.FIRMWARE_INDICATORS");
            if (!"iPLAYER".equalsIgnoreCase(oldDevice.getDevice_type()) && StringUtils.isEmpty(firmware_indicators) && StringUtils.isNotEmpty(oldDevice.getApplication_version())) {
               firmware_indicators = DeviceUtils.getFirmwareIndicators(oldDevice.getApplication_version());
            }

            if (firmware_version != null && firmware_version.length() >= 50) {
               firmware_version = firmware_version.substring(0, 45) + " ...";
            }

            String os_image_version = rs.getString(".MO.DEVICE_CONF.GENERAL.OS_VERSION");
            if (os_image_version != null && os_image_version.length() >= 50) {
               os_image_version = os_image_version.substring(0, 45) + " ...";
            }

            String video_adapter = rs.getString(".MO.DEVICE_CONF.GENERAL.VIDEO_ADAPTER");
            if (video_adapter != null && video_adapter.length() >= 50) {
               video_adapter = video_adapter.substring(0, 45) + " ...";
            }

            long video_memory = 0L;
            if (rs.getString(".MO.DEVICE_CONF.GENERAL.VIDEO_MEMORY") != null && !rs.getString(".MO.DEVICE_CONF.GENERAL.VIDEO_MEMORY").equals("")) {
               video_memory = rs.getLong(".MO.DEVICE_CONF.GENERAL.VIDEO_MEMORY");
            }

            String video_driver = rs.getString(".MO.DEVICE_CONF.GENERAL.VIDEO_DRIVER");
            String network_adapter = rs.getString(".MO.DEVICE_CONF.GENERAL.NETWORK_ADAPTER");
            if (network_adapter != null && network_adapter.length() >= 100) {
               network_adapter = network_adapter.substring(0, 95) + " ...";
            }

            String network_driver = rs.getString(".MO.DEVICE_CONF.GENERAL.NETWORK_DRIVER");
            boolean ewf_state = false;
            if (rs.getString(".MO.DEVICE_CONF.GENERAL.EWF_STATE") != null && !rs.getString(".MO.DEVICE_CONF.GENERAL.EWF_STATE").equals("")) {
               if ("true".equalsIgnoreCase(rs.getString(".MO.DEVICE_CONF.GENERAL.EWF_STATE"))) {
                  ewf_state = true;
               } else {
                  ewf_state = false;
               }
            }

            String deviceSeries = null;
            if (rs.getString(".MO.DEVICE_CONF.GENERAL.DEVICE_SERIES") != null && !rs.getString(".MO.DEVICE_CONF.GENERAL.DEVICE_SERIES").equals("")) {
               deviceSeries = rs.getString(".MO.DEVICE_CONF.GENERAL.DEVICE_SERIES");
            }

            String deviceHwPlatform = null;
            if (rs.getString(".MO.DEVICE_CONF.GENERAL.DEVICE_HW_PLATFORM") != null && !rs.getString(".MO.DEVICE_CONF.GENERAL.DEVICE_HW_PLATFORM").equals("")) {
               deviceHwPlatform = rs.getString(".MO.DEVICE_CONF.GENERAL.DEVICE_HW_PLATFORM");
            }

            String peripherals = null;
            if (rs.getString(".MO.DEVICE_CONF.GENERAL.PERIPHERALS") != null && !rs.getString(".MO.DEVICE_CONF.GENERAL.PERIPHERALS").equals("")) {
               peripherals = rs.getString(".MO.DEVICE_CONF.GENERAL.PERIPHERALS");
            }

            String thirdApplicationVersion = null;
            if (rs.getString(".MO.DEVICE_CONF.GENERAL.THIRD_APPLICATION_VERSION") != null && !rs.getString(".MO.DEVICE_CONF.GENERAL.THIRD_APPLICATION_VERSION").equals("")) {
               thirdApplicationVersion = rs.getString(".MO.DEVICE_CONF.GENERAL.THIRD_APPLICATION_VERSION");
            }

            String thirdApplicationLastUpdated = null;
            if (rs.getString(".MO.DEVICE_CONF.GENERAL.THIRD_APPLICATION_LAST_UPDATE_TIME") != null && !rs.getString(".MO.DEVICE_CONF.GENERAL.THIRD_APPLICATION_LAST_UPDATE_TIME").equals("")) {
               thirdApplicationLastUpdated = rs.getString(".MO.DEVICE_CONF.GENERAL.THIRD_APPLICATION_LAST_UPDATE_TIME");
            }

            Long thirdApplicationLogSize = null;
            if (rs.getString(".MO.DEVICE_CONF.GENERAL.THIRD_APPLICATION_LOG_SIZE") != null && !rs.getString(".MO.DEVICE_CONF.GENERAL.THIRD_APPLICATION_LOG_SIZE").equals("")) {
               thirdApplicationLogSize = rs.getLong(".MO.DEVICE_CONF.GENERAL.THIRD_APPLICATION_LOG_SIZE");
            }

            String ipValues = rs.getString(".MO.DEVICE_CONF.NETWORK.IP_ADDRESS");
            long port = 0L;
            if (rs.getString(".MO.DEVICE_CONF.NETWORK.PORT") != null && !rs.getString(".MO.DEVICE_CONF.NETWORK.PORT").equals("")) {
               port = rs.getLong(".MO.DEVICE_CONF.NETWORK.PORT");
            }

            String time_zone_index = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.TIME_ZONE_INDEX");
            boolean day_light_saving = false;
            String day_light_saving_manual = "";
            boolean auto_time_setting = false;
            boolean on_timer_setting = false;
            boolean off_timer_setting = false;
            boolean is_reverse = true;
            long trigger_interval = 10L;
            long monitoring_interval = 10L;
            long screen_capture_interval = 0L;
            long child_monitoring_interval = 60L;
            String switch_time = "0";
            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DAY_LIGHT_SAVING") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DAY_LIGHT_SAVING").equals("")) {
               if ("true".equalsIgnoreCase(rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DAY_LIGHT_SAVING"))) {
                  day_light_saving = true;
               } else {
                  day_light_saving = false;
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DAY_LIGHT_SAVING_MANUAL") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DAY_LIGHT_SAVING_MANUAL").equals("")) {
               day_light_saving_manual = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DAY_LIGHT_SAVING_MANUAL");
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.AUTO_TIME_SETTING") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.AUTO_TIME_SETTING").equals("")) {
               if ("true".equalsIgnoreCase(rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.AUTO_TIME_SETTING"))) {
                  auto_time_setting = true;
               } else {
                  auto_time_setting = false;
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.ON_TIMER_SETTING") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.ON_TIMER_SETTING").equals("")) {
               if ("true".equalsIgnoreCase(rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.ON_TIMER_SETTING"))) {
                  on_timer_setting = true;
               } else {
                  on_timer_setting = false;
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.OFF_TIMER_SETTING") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.OFF_TIMER_SETTING").equals("")) {
               if ("true".equalsIgnoreCase(rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.OFF_TIMER_SETTING"))) {
                  off_timer_setting = true;
               } else {
                  off_timer_setting = false;
               }
            }

            String magicinfo_server_url = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.MAGICINFO_SERVER_URL");
            String cabinet_group_layout_and_display_output = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.CABINET_GROUP_LAYOUT");
            String cabinet_group_layout = cabinet_group_layout_and_display_output;

            try {
               cabinet_group_layout = DeviceUtils.processCabinetGroupLayoutString(this.ctxt.getDevice().getDevice_id(), cabinet_group_layout_and_display_output);
            } catch (Exception var110) {
               logger.error("Failed to update cabinet_group_layout and/or display_output_mode. " + var110);
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.IS_REVERSE") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.IS_REVERSE").equals("")) {
               if ("true".equalsIgnoreCase(rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.IS_REVERSE"))) {
                  is_reverse = true;
               } else {
                  is_reverse = false;
               }
            }

            String tunneling_server = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.TUNNELING_SERVER");
            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.TRIGGER_INTERVAL") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.TRIGGER_INTERVAL").equals("")) {
               trigger_interval = rs.getLong(".MO.DEVICE_CONF.SYSTEM_SETUP.TRIGGER_INTERVAL");
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PAGE_SWITCH_TIME") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PAGE_SWITCH_TIME").equals("")) {
               switch_time = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PAGE_SWITCH_TIME");
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.MONITORING_INTERVAL") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.MONITORING_INTERVAL").equals("")) {
               monitoring_interval = rs.getLong(".MO.DEVICE_CONF.SYSTEM_SETUP.MONITORING_INTERVAL");
            }

            String ftp_connect_mode = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.FTP_CONNECT_MODE");
            String repository_path = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.REPOSITORY_PATH");
            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.SCREEN_CAPTURE_INTERVAL") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.SCREEN_CAPTURE_INTERVAL").equals("")) {
               screen_capture_interval = rs.getLong(".MO.DEVICE_CONF.SYSTEM_SETUP.SCREEN_CAPTURE_INTERVAL");
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.CHILD_MONITORING_INTERVAL") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.CHILD_MONITORING_INTERVAL").equals("")) {
               child_monitoring_interval = rs.getLong(".MO.DEVICE_CONF.SYSTEM_SETUP.CHILD_MONITORING_INTERVAL");
               child_monitoring_interval = child_monitoring_interval < 0L ? 60L : child_monitoring_interval;
            }

            String bg_color = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.BG_COLOR");
            String rule_version = rs.getString(".MO.DEVICE_CONF.GENERAL.RULE_VERSION");
            String proxy_setting = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PROXY_SETTING");
            String proxy_setting_authorization = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PROXY_SETTING_AUTHORIZATION");
            String proxy_exclude_list = null;
            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PROXY_EXCLUDE_LIST") != null) {
               proxy_exclude_list = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PROXY_EXCLUDE_LIST");
            }

            Long connection_limit_time = 0L;
            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.CONNECTION_LIMIT_TIME") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.CONNECTION_LIMIT_TIME").equals("")) {
               connection_limit_time = rs.getLong(".MO.DEVICE_CONF.SYSTEM_SETUP.CONNECTION_LIMIT_TIME");
            }

            String mnt_folder_path = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.MNT_FOLDER_PATH");
            String system_restart_interval = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.SYSTEM_RESTART_INTERVAL");
            String log_mnt = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.LOG_MNT");
            String proof_of_play_mnt = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PROOF_OF_PLAY_MNT");
            String content_mnt = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.CONTENT_MNT");
            String pin_code = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PIN_CODE");
            String cabinet_group_setting = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.CABINET_GROUP_SETTING");

            try {
               if (cabinet_group_setting != null && !"".equals(cabinet_group_setting)) {
                  DeviceSystemSetupConf deviceSystemSetupConf;
                  try {
                     deviceSystemSetupConf = DBCacheUtils.getDeviceSystemSetupConf(deviceId);
                  } catch (Exception var108) {
                     deviceSystemSetupConf = new DeviceSystemSetupConf();
                  }

                  deviceSystemSetupConf.setCabinet_group_setting(cabinet_group_setting);
                  DBCacheUtils.setDeviceSystemSetupConf(deviceSystemSetupConf, deviceId);
               }
            } catch (Exception var109) {
               logger.error("[MagicInfo_PostBootstrap][" + deviceId + "] Exception ", var109);
            }

            Long screen_rotation = null;
            String support_flag = "00000000";
            Long softwareUpdate = 0L;
            boolean useRandomIV = false;
            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.SUPPORT_FLAG") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.SUPPORT_FLAG").equals("")) {
               support_flag = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.SUPPORT_FLAG");
               if (support_flag.charAt(0) == '1') {
                  softwareUpdate = 1L;
               } else {
                  softwareUpdate = 0L;
               }

               support_flag = support_flag.substring(0, 4) + "0" + support_flag.substring(5);
               useRandomIV = DeviceUtils.useRandomIV(support_flag);
            } else {
               Map softwareuUpdate = deviceDao.getSoftwareUpdate(this.ctxt.getDevice().getDevice_id());
               if (softwareuUpdate != null && (Long)softwareuUpdate.get("SOFTWARE_UPDATE_VERSION") == 1L) {
                  SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
                  String reservation_id = null;
                  reservation_id = softwareDao.getUpgradeDeviceReservationId(this.ctxt.getDevice().getDevice_id(), 1);
                  if (reservation_id != null) {
                     softwareDao.setUpgradeDeviceStatus(this.ctxt.getDevice().getDevice_id(), (String)softwareuUpdate.get("APPLICATION_VERSION"), reservation_id);
                  }
               }
            }

            long bandWidth = -1L;
            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.BANDWIDTH_LIMIT") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.BANDWIDTH_LIMIT").equals("")) {
               bandWidth = rs.getLong(".MO.DEVICE_CONF.SYSTEM_SETUP.BANDWIDTH_LIMIT");
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.SCREEN_ROTATION") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.SCREEN_ROTATION").equals("")) {
               screen_rotation = rs.getLong(".MO.DEVICE_CONF.SYSTEM_SETUP.SCREEN_ROTATION");
            }

            Long play_mode = 0L;
            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PLAY_MODE") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PLAY_MODE").equals("")) {
               play_mode = rs.getLong(".MO.DEVICE_CONF.SYSTEM_SETUP.PLAY_MODE");
            }

            Boolean contents_progress_enable = null;
            String contents_progress_unit = null;
            Long contents_progress_interval = null;
            Long contents_download_mode;
            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DOWN_PROGRESS.ENABLE") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DOWN_PROGRESS.ENABLE").equals("")) {
               contents_download_mode = rs.getLong(".MO.DEVICE_CONF.SYSTEM_SETUP.DOWN_PROGRESS.ENABLE");
               if (contents_download_mode == 1L) {
                  contents_progress_enable = true;
               } else {
                  contents_progress_enable = false;
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DOWN_PROGRESS.MEASURE_UNIT") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DOWN_PROGRESS.MEASURE_UNIT").equals("")) {
               contents_progress_unit = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DOWN_PROGRESS.MEASURE_UNIT");
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DOWN_PROGRESS.MEASURE_INTERVAL") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DOWN_PROGRESS.MEASURE_INTERVAL").equals("")) {
               contents_progress_interval = rs.getLong(".MO.DEVICE_CONF.SYSTEM_SETUP.DOWN_PROGRESS.MEASURE_INTERVAL");
            }

            contents_download_mode = null;
            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DOWNLOAD_MODE") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DOWNLOAD_MODE").equals("")) {
               contents_download_mode = rs.getLong(".MO.DEVICE_CONF.SYSTEM_SETUP.DOWNLOAD_MODE");
            }

            Boolean ams_webcam = false;
            String ams_play_mode = "0";
            String player_resolution = null;
            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.FACE_RECOGNITION.WEBCAM") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.FACE_RECOGNITION.WEBCAM").equals("")) {
               if ("true".equalsIgnoreCase(rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.FACE_RECOGNITION.WEBCAM"))) {
                  ams_webcam = true;
               } else {
                  ams_webcam = false;
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.FACE_RECOGNITION.PLAY_MODE") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.FACE_RECOGNITION.PLAY_MODE").equals("")) {
               ams_play_mode = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.FACE_RECOGNITION.PLAY_MODE");
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PLAYER_RESOLUTION") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PLAYER_RESOLUTION").equals("")) {
               player_resolution = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PLAYER_RESOLUTION");
            }

            Boolean has_signage_child = null;
            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.HAS_SIGNAGE_CHILD") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.HAS_SIGNAGE_CHILD").equals("")) {
               if ("true".equalsIgnoreCase(rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.HAS_SIGNAGE_CHILD"))) {
                  has_signage_child = true;
               } else {
                  has_signage_child = false;
               }
            }

            try {
               if ("SIGNAGE".equalsIgnoreCase(this.ctxt.getDevice().getDevice_type()) || "RSIGNAGE".equalsIgnoreCase(this.ctxt.getDevice().getDevice_type())) {
                  has_signage_child = true;
               }
            } catch (Exception var114) {
               logger.error("[MagicInfo_PostBootstrap][" + deviceId + "] Exception ", var114);
            }

            String url_launcher = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.URL_LAUNCHER");
            Device device = new Device();
            device.setSerial_decimal(serial_decimal);
            device.setScreen_size(CommonUtils.isInteger(screen_size) ? screen_size : null);
            device.setResolution(resolution);
            device.setFirmware_version(firmware_version);
            device.setFirmware_indicators(firmware_indicators);
            device.setOs_image_version(os_image_version);
            device.setVideo_adapter(video_adapter);
            device.setVideo_memory(video_memory);
            device.setVideo_driver(video_driver);
            device.setNetwork_adapter(network_adapter);
            device.setNetwork_driver(network_driver);
            device.setEwf_state(ewf_state);
            device.setDevice_series(deviceSeries);
            device.setDevice_hw_platform(deviceHwPlatform);
            device.setPeripherals(peripherals);
            device.setThird_application_version(thirdApplicationVersion);
            device.setThird_application_last_updated(thirdApplicationLastUpdated);
            device.setThird_application_log_size(thirdApplicationLogSize);
            if (ipValues != null) {
               String[] arrVals = ipValues.split(";");
               if (arrVals.length >= 4) {
                  if (arrVals[0].equals("0")) {
                     device.setIp_setting_type("DHCP");
                  } else if (arrVals[0].equals("1")) {
                     device.setIp_setting_type("STATIC");
                  }

                  device.setIp_address(arrVals[1]);
                  device.setSubnet_mask(arrVals[2]);
                  device.setGateway(arrVals[3]);
                  if (arrVals.length >= 5) {
                     device.setDns_server_main(arrVals[4]);
                  }

                  if (arrVals.length >= 6) {
                     device.setDns_server_sub(arrVals[5]);
                  }
               }
            }

            device.setPort(port);
            device.setTime_zone_index(time_zone_index);
            device.setDay_light_saving(day_light_saving);
            if (day_light_saving_manual != null && !day_light_saving_manual.equals("")) {
               device.setDay_light_saving_manual(day_light_saving_manual);
            }

            device.setSwitch_time(switch_time);
            device.setAuto_time_setting(auto_time_setting);
            device.setOn_timer_setting(on_timer_setting);
            device.setOff_timer_setting(off_timer_setting);
            device.setMagicinfo_server_url(magicinfo_server_url);
            device.setCabinet_group_layout(cabinet_group_layout);
            device.setIs_reverse(is_reverse);
            device.setTunneling_server(tunneling_server);
            device.setTrigger_interval(trigger_interval);
            device.setMonitoring_interval(monitoring_interval);
            device.setFtp_connect_mode(ftp_connect_mode);
            device.setRepository_path(repository_path);
            device.setScreen_capture_interval(screen_capture_interval);
            device.setChild_monitoring_interval(child_monitoring_interval);
            device.setBg_color(bg_color);
            device.setProxy_setting(proxy_setting);
            device.setProxy_exclude_list(proxy_exclude_list);
            if (!StringUtils.isEmpty(proxy_setting_authorization)) {
               try {
                  device.setProxy_setting_authorization(DeviceUtils.decode(this.ctxt.getDevice().getDevice_id(), proxy_setting_authorization, useRandomIV));
               } catch (Exception var107) {
                  logger.error("device proxy_setting_authorization decryption failed.", var107);
                  device.setProxy_setting_authorization(":");
               }
            }

            if (!StringUtils.isEmpty(pin_code)) {
               try {
                  String decodedPinCode = DeviceUtils.decode(this.ctxt.getDevice().getDevice_id(), pin_code, useRandomIV);
                  if (!StringUtils.isEmpty(decodedPinCode)) {
                     device.setPin_code(decodedPinCode);
                  }
               } catch (Exception var106) {
                  logger.error("device pin_code decryption failed.", var106);
               }
            }

            device.setConnection_limit_time(connection_limit_time);
            device.setMnt_folder_path(mnt_folder_path);
            device.setSystem_restart_interval(system_restart_interval);
            device.setLog_mnt(log_mnt);
            device.setProof_of_play_mnt(proof_of_play_mnt);
            device.setContent_mnt(content_mnt);
            device.setScreen_rotation(screen_rotation);
            device.setPlay_mode(play_mode);
            device.setReset_password(0L);
            device.setUrl_launcher(url_launcher);
            device.setContents_progress_enable(contents_progress_enable);
            device.setContents_progress_unit(contents_progress_unit);
            device.setContents_progress_interval(contents_progress_interval);
            device.setWebcam(ams_webcam);
            device.setAms_play_mode(ams_play_mode);
            device.setPlayer_resolution(player_resolution);
            device.setHas_child(has_signage_child);
            device.setRule_version(rule_version);
            device.setSupport_flag(support_flag);
            device.setBandwidth(bandWidth);
            device.setSoftwareUpdateVersion(softwareUpdate);
            device.setDevice_id(this.ctxt.getDevice().getDevice_id());
            boolean isSupportPreconfig = false;
            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PRE_CONFIG_VERSION") != null) {
               if ("".equals(rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PRE_CONFIG_VERSION"))) {
                  device.setPre_config_version("-");
               } else {
                  device.setPre_config_version(rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PRE_CONFIG_VERSION"));
               }

               isSupportPreconfig = true;
            }

            String time_zone_version = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.TIME_ZONE_VERSION");
            DeviceSystemSetupConfManager systemSetupDao = DeviceSystemSetupConfManagerImpl.getInstance();
            String configServer;
            ReservationManager reservationManager;
            if (time_zone_version != null && !time_zone_version.equals("") && !time_zone_version.equals("0") && !systemSetupDao.isExistTimeZoneMapList(time_zone_version)) {
               String device_id = this.ctxt.getDevice().getDevice_id();
               configServer = this.ctxt.getDevice().getDevice_model_name();
               long timeZoneWaitingTime = 2000L;
               HashMap timeZoneParams = new HashMap();
               ArrayList moPathList = new ArrayList();
               moPathList.add(".MO.DEVICE_CONF.TIME_ZONE_LIST");
               moPathList.add(".MO.DEVICE_CONF.SYSTEM_SETUP.TIME_ZONE_VERSION");
               timeZoneParams.put("mo_path", moPathList);
               reservationManager = ReservationManagerImpl.getInstance();
               ScheduleReservation timeZoneReservation = new ScheduleReservation();
               timeZoneReservation.setDevice_id(device_id);
               timeZoneReservation.setDevice_model_name(configServer);
               timeZoneReservation.setService_id("GET_DEVICE_COMMON_CONF");
               timeZoneReservation.setService_params(timeZoneParams);
               timeZoneReservation.setService_start_date(new Date(System.currentTimeMillis() + timeZoneWaitingTime));
               reservationManager.insert(timeZoneReservation);
            }

            String computerName;
            String rcvServer;
            String dayLightSavingEnable;
            String dayLightSavingManual;
            boolean sendSetComputerName;
            try {
               if (!isSupportPreconfig && "2013timezone".equals(time_zone_version)) {
                  ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
                  Map infoMap = serverSetupDao.getServerCommonInfo();
                  rcvServer = null;
                  computerName = null;
                  dayLightSavingEnable = null;
                  dayLightSavingManual = null;
                  if (infoMap.get("AUTO_TIME_ZONE_ENABLE") != null) {
                     rcvServer = infoMap.get("AUTO_TIME_ZONE_ENABLE").toString();
                  }

                  if (infoMap.get("AUTO_TIME_ZONE") != null) {
                     computerName = infoMap.get("AUTO_TIME_ZONE").toString();
                  }

                  if (infoMap.get("DAY_LIGHT_SAVING_ENABLE") != null) {
                     dayLightSavingEnable = infoMap.get("DAY_LIGHT_SAVING_ENABLE").toString();
                  }

                  if (infoMap.get("DAY_LIGHT_SAVING_MANUAL") != null) {
                     dayLightSavingManual = infoMap.get("DAY_LIGHT_SAVING_MANUAL").toString();
                  }

                  if ("TRUE".equalsIgnoreCase(rcvServer) && !"".equals(computerName)) {
                     sendSetComputerName = false;
                     DeviceSystemSetupConf autoTimezoneDevice = new DeviceSystemSetupConf();
                     DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
                     if (computerName.equals(time_zone_index)) {
                        autoTimezoneDevice.setDevice_id(this.ctxt.getDevice().getDevice_id());
                        if (dayLightSavingEnable != null && day_light_saving != Boolean.parseBoolean(dayLightSavingEnable)) {
                           autoTimezoneDevice.setDay_light_saving(Boolean.parseBoolean(dayLightSavingEnable));
                           if (dayLightSavingManual != null && !dayLightSavingManual.equals(day_light_saving_manual)) {
                              autoTimezoneDevice.setDay_light_saving_manual(dayLightSavingManual);
                           }

                           sendSetComputerName = true;
                        }
                     } else {
                        autoTimezoneDevice.setDevice_id(this.ctxt.getDevice().getDevice_id());
                        autoTimezoneDevice.setTime_zone_index(computerName);
                        if ("TRUE".equalsIgnoreCase(dayLightSavingEnable) && !"".equals(dayLightSavingManual)) {
                           autoTimezoneDevice.setDay_light_saving(Boolean.parseBoolean(dayLightSavingEnable));
                           autoTimezoneDevice.setDay_light_saving_manual(dayLightSavingManual);
                        } else {
                           autoTimezoneDevice.setDay_light_saving(false);
                        }

                        sendSetComputerName = true;
                     }

                     if (sendSetComputerName && !isLiteDevice) {
                        confManager.reqSetSystemSetupToDevice(autoTimezoneDevice, "");
                     }
                  }
               }
            } catch (Exception var113) {
               logger.error(var113);
            }

            device.setTime_zone_version(time_zone_version);
            DeviceSystemSetupConf systemSetup;
            if (oldDevice.getContents_download_mode() == null) {
               device.setContents_download_mode(contents_download_mode);
            } else {
               device.setContents_download_mode(oldDevice.getContents_download_mode());
               if (!oldDevice.getContents_download_mode().equals(contents_download_mode)) {
                  systemSetup = new DeviceSystemSetupConf();
                  systemSetup.setContents_download_mode(oldDevice.getContents_download_mode());
                  long waitingTime = 3000L;
                  Map params = new HashMap();
                  params.put("device_system_setup_conf", systemSetup);
                  ReservationManager reservationManager = ReservationManagerImpl.getInstance();
                  ScheduleReservation reservation = new ScheduleReservation();
                  reservation.setDevice_id(device.getDevice_id());
                  reservation.setDevice_model_name(this.ctxt.getDevice().getDevice_model_name());
                  reservation.setService_id("SET_DEVICE_SYSTEM_SETUP_CONF");
                  reservation.setService_params(params);
                  reservation.setService_start_date(new Date(System.currentTimeMillis() + waitingTime));
                  reservationManager.insert(reservation);
               }
            }

            HashMap params;
            DeviceSystemSetupConf systemSetup;
            boolean autoComputerNameSet;
            long waitingTime;
            String moVariableTagString;
            ReservationManager reservationManager;
            ArrayList tagIds;
            ScheduleReservation reservation;
            if (!isVendingMachine && !isLiteDevice) {
               systemSetup = null;
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.VWL_LAYOUT.ID") != null) {
                  configServer = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.VWL_LAYOUT.ID");
                  rcvServer = oldDevice.getVwt_id();
                  VwlLayoutManager VwlLayoutManager = VwlLayoutManagerImpl.getInstance();
                  if (rcvServer != null && !rcvServer.equals("")) {
                     if (!configServer.equalsIgnoreCase(rcvServer)) {
                        logger.info("[MagicInfo_PostBootstrap][" + deviceId + "] device_vwtID: " + configServer + ", DB_vwtID: " + rcvServer);
                        VwlLayout vwl = VwlLayoutManager.getVwlLayoutInfo(rcvServer);
                        dayLightSavingManual = CommonConfig.get("VWT_HOME").replace('/', File.separatorChar) + File.separator + rcvServer + File.separator + vwl.getVwt_file_name();
                        VwlLayoutManager.deployVwlLayout(this.ctxt.getDevice().getDevice_id(), "FINISH_LAYOUT", rcvServer, dayLightSavingManual);
                     }
                  } else {
                     dayLightSavingEnable = CommonConfig.get("VWT_HOME").replace('/', File.separatorChar) + File.separator + "EMPTY_LAYOUT.VWL";
                     VwlLayoutManager.deployVwlLayout(this.ctxt.getDevice().getDevice_id(), "REMOVE_LAYOUT", "0", dayLightSavingEnable);
                  }
               }

               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DOWNLOAD_SERVER") != null) {
                  configServer = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DOWNLOAD_SERVER");
                  rcvServer = "";
                  computerName = this.getEdgeServerMOString();
                  DeviceSystemSetupConf deviceSystemSetupConf = systemSetupDao.getDeviceSystemSetupConf(this.ctxt.getDevice().getDevice_id());
                  if (computerName != null && !computerName.equals("")) {
                     rcvServer = computerName + "!" + deviceSystemSetupConf.getOnly_dn_server().toString();
                  }

                  dayLightSavingManual = this.getEdgeServerMOStringFromInfo(rcvServer);
                  if (!configServer.equals(dayLightSavingManual)) {
                     reservationManager = ReservationManagerImpl.getInstance();
                     waitingTime = 3000L;
                     params = new HashMap();
                     systemSetup = new DeviceSystemSetupConf();
                     systemSetup.setDownload_server(rcvServer);
                     params.put("device_system_setup_conf", systemSetup);
                     ScheduleReservation reservation = new ScheduleReservation();
                     reservation.setDevice_id(deviceId);
                     reservation.setDevice_model_name(this.ctxt.getDevice().getDevice_model_name());
                     reservation.setService_id("SET_DEVICE_SYSTEM_SETUP_CONF");
                     reservation.setService_params(params);
                     reservation.setService_start_date(new Date(System.currentTimeMillis() + waitingTime));
                     reservationManager.insert(reservation);
                  }
               }

               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.SERVER") != null) {
                  configServer = "";
                  rcvServer = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.SERVER");
                  DatalinkServerImpl dls = DatalinkServerImpl.getInstance();

                  try {
                     configServer = dls.getDatalinkServerMOString();
                  } catch (Exception var105) {
                     logger.error(var105.getMessage());
                  }

                  if (!configServer.equals(rcvServer)) {
                     logger.info("[MagicInfo_PostBootstrap][" + deviceId + "] DATALINK.SERVER Diff, Rcv serverInfo=" + rcvServer + ",Config serverInfo=" + configServer);
                     if (systemSetup == null) {
                        systemSetup = new DeviceSystemSetupConf();
                     }

                     systemSetup.setDatalink_server(configServer);
                  }
               }

               autoComputerNameSet = false;
               BackupPlayEntity backupDevice = deviceDao.getBackupPlayerByDeviceId(this.ctxt.getDevice().getDevice_id());
               if (backupDevice != null) {
                  autoComputerNameSet = true;
               }

               computerName = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.TAG");
               if (computerName == null) {
                  computerName = "";
               }

               String[] moTagList = computerName.split("@");
               List dbTagList = systemSetupDao.getDeviceTag(this.ctxt.getDevice().getDevice_id());
               TagInfoImpl tagDao = TagInfoImpl.getInstance();
               boolean sendTagMo = false;
               int j;
               if (dbTagList == null && moTagList.length <= 1 && moTagList[0].equals("")) {
                  sendTagMo = false;
               } else if (dbTagList == null && moTagList.length >= 1 && !moTagList[0].equals("")) {
                  sendTagMo = true;
               } else if (dbTagList.size() == 0 && moTagList.length <= 1 && moTagList[0].equals("")) {
                  sendTagMo = false;
               } else if (dbTagList != null && moTagList.length <= 1 && moTagList[0].equals("") && dbTagList.size() > 0) {
                  sendTagMo = true;
               } else if (dbTagList != null && moTagList.length == dbTagList.size() && !autoComputerNameSet) {
                  try {
                     for(int i = 0; i < moTagList.length; ++i) {
                        String[] tagPair = moTagList[i].split(";");
                        int tagId = Integer.parseInt(tagPair[0]);
                        TagEntity tagEntity = tagDao.getTag(tagId);
                        if (tagEntity == null) {
                           logger.error("[MagicInfo_PostBootstrap][" + deviceId + "] TAG MO Invalid [tagId None]. tagId=" + tagId);
                           sendTagMo = true;
                           break;
                        }

                        if (!tagEntity.getTag_value().equals(tagPair[1])) {
                           logger.error("[MagicInfo_PostBootstrap][" + deviceId + "] TAG MO Invalid [value Mismatch]. tagId=" + tagId);
                           sendTagMo = true;
                           break;
                        }

                        boolean mappingDevice = false;

                        for(j = 0; j < dbTagList.size(); ++j) {
                           if (tagId == Integer.parseInt(((Map)dbTagList.get(j)).get("tag_id").toString())) {
                              mappingDevice = true;
                              break;
                           }
                        }

                        if (!mappingDevice) {
                           logger.error("[MagicInfo_PostBootstrap][" + deviceId + "] TAG MO Invalid [Tag Not Mapping in Device]. tagId=" + tagId);
                           sendTagMo = true;
                           break;
                        }
                     }
                  } catch (Exception var112) {
                     logger.error("", var112);
                     sendTagMo = true;
                  }
               } else if (!autoComputerNameSet) {
                  logger.error("[MagicInfo_PostBootstrap][" + deviceId + "] Tag MO Invalid [tag count Mismatch]. dbTagList=" + dbTagList.size() + ", moTagList=" + moTagList.length);
                  sendTagMo = true;
               } else {
                  logger.error("[MagicInfo_PostBootstrap][" + deviceId + "] Tag MO Invalid . dbTagList=" + dbTagList.size() + ", moTagList=" + moTagList.length);
                  sendTagMo = true;
               }

               if (sendTagMo) {
                  if (systemSetup == null) {
                     systemSetup = new DeviceSystemSetupConf();
                  }

                  systemSetup.setTag_id_list(DeviceUtils.getTagIdStringList(this.ctxt.getDevice().getDevice_id()));
               }

               Long player_start_timeout;
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.FILEDATA_DEL_SIZE") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.FILEDATA_DEL_SIZE").equals("")) {
                  player_start_timeout = rs.getLong(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.FILEDATA_DEL_SIZE");
                  device.setFiledata_del_size(player_start_timeout);
               }

               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.CONTENT_READY_INTERVAL") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.CONTENT_READY_INTERVAL").equals("")) {
                  player_start_timeout = rs.getLong(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.CONTENT_READY_INTERVAL");
                  device.setContent_ready_interval(player_start_timeout);
               }

               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.PLAYER_START_TIMEOUT") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.PLAYER_START_TIMEOUT").equals("")) {
                  player_start_timeout = rs.getLong(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.PLAYER_START_TIMEOUT");
                  device.setPlayer_start_timeout(player_start_timeout);
               }

               if (systemSetup != null) {
                  long waitingTime = 3000L;
                  HashMap params = new HashMap();
                  params.put("device_system_setup_conf", systemSetup);
                  reservationManager = ReservationManagerImpl.getInstance();
                  reservation = new ScheduleReservation();
                  reservation.setDevice_id(device.getDevice_id());
                  reservation.setDevice_model_name(this.ctxt.getDevice().getDevice_model_name());
                  reservation.setService_id("SET_DEVICE_SYSTEM_SETUP_CONF");
                  reservation.setService_params(params);
                  reservation.setService_start_date(new Date(System.currentTimeMillis() + waitingTime));
                  reservationManager.insert(reservation);
               }

               moVariableTagString = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.VAR_TAG");
               List varTags = new ArrayList();
               if (moVariableTagString != null) {
                  String[] moTagTemp = moVariableTagString.split(";");

                  for(int i = 0; i < moTagTemp.length; ++i) {
                     varTags.add(moTagTemp[i]);
                  }
               }

               List dbVariableTagList = deviceDao.getTagFromDeviceId(this.ctxt.getDevice().getDevice_id(), true);
               if (this.isNeedSetVarTags(dbVariableTagList, varTags)) {
                  systemSetup = new DeviceSystemSetupConf();
                  tagIds = new ArrayList();
                  List conditionIds = new ArrayList();

                  for(j = 0; j < dbVariableTagList.size(); ++j) {
                     tagIds.add(((DeviceTag)dbVariableTagList.get(j)).getTag_id().toString());
                     conditionIds.add(((DeviceTag)dbVariableTagList.get(j)).getTag_condition_id().toString());
                  }

                  systemSetup.setTag_id_list(tagIds);
                  systemSetup.setTag_condition_list(conditionIds);
                  long waitingTime = 4000L;
                  HashMap params = new HashMap();
                  params.put("device_system_setup_conf", systemSetup);
                  ReservationManager reservationManager = ReservationManagerImpl.getInstance();
                  ScheduleReservation reservation = new ScheduleReservation();
                  reservation.setDevice_id(device.getDevice_id());
                  reservation.setDevice_model_name(this.ctxt.getDevice().getDevice_model_name());
                  reservation.setService_id("SET_DEVICE_SYSTEM_SETUP_CONF");
                  reservation.setService_params(params);
                  reservation.setService_start_date(new Date(System.currentTimeMillis() + waitingTime));
                  reservationManager.insert(reservation);
               }
            }

            boolean autoIpSet = false;
            autoComputerNameSet = false;
            boolean useMpplayer = true;
            computerName = null;
            dayLightSavingEnable = null;
            if (!isVendingMachine && !isLiteDevice) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.FTP_PORT") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.FTP_PORT").equals("")) {
                  Long playerFtpPort = rs.getLong(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.FTP_PORT");
                  Long serverFtpPort = Long.parseLong(CommonConfig.get("download.server.ftp.port") == null ? "21" : CommonConfig.get("download.server.ftp.port"));
                  if (serverFtpPort != playerFtpPort) {
                     waitingTime = 3000L;
                     params = new HashMap();
                     systemSetup = new DeviceSystemSetupConf();
                     systemSetup.setFtp_port(serverFtpPort);
                     params.put("device_system_setup_conf", systemSetup);
                     reservationManager = ReservationManagerImpl.getInstance();
                     reservation = new ScheduleReservation();
                     reservation.setDevice_id(device.getDevice_id());
                     reservation.setDevice_model_name(this.ctxt.getDevice().getDevice_model_name());
                     reservation.setService_id("SET_DEVICE_SYSTEM_SETUP_CONF");
                     reservation.setService_params(params);
                     reservation.setService_start_date(new Date(System.currentTimeMillis() + waitingTime));
                     reservationManager.insert(reservation);
                  }
               }

               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.AUTO_IP_SET") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.AUTO_IP_SET").equals("")) {
                  if ("true".equalsIgnoreCase(rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.AUTO_IP_SET"))) {
                     autoIpSet = true;
                  } else {
                     autoIpSet = false;
                  }
               }

               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.AUTO_COMPUTERNAME_SET") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.AUTO_COMPUTERNAME_SET").equals("")) {
                  if ("true".equalsIgnoreCase(rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.AUTO_COMPUTERNAME_SET"))) {
                     autoComputerNameSet = true;
                  } else {
                     autoComputerNameSet = false;
                  }
               }

               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.COMPUTERNAME") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.COMPUTERNAME").equals("") && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.COMPUTERNAME").equals("FAIL")) {
                  computerName = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.COMPUTERNAME");
               }

               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.VNC_PASSWORD") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.VNC_PASSWORD").equals("") && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.VNC_PASSWORD").equals("FAIL")) {
                  dayLightSavingEnable = rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.VNC_PASSWORD");
               }

               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.USE_MPPLAYER") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.USE_MPPLAYER").equals("") && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.USE_MPPLAYER").equals("FAIL")) {
                  if ("true".equalsIgnoreCase(rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.USE_MPPLAYER"))) {
                     useMpplayer = true;
                  } else {
                     useMpplayer = false;
                  }
               }

               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.RM_RULE_VERSION") != null) {
                  if ("".equals(rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.RM_RULE_VERSION"))) {
                     device.setRm_rule_version("-");
                  } else {
                     device.setRm_rule_version(rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.RM_RULE_VERSION"));
                  }

                  DeviceRMRuleInfo rmruleInfo = DeviceRMRuleInfoImpl.getInstance();
                  DeviceRMRule rmrule = rmruleInfo.getRMRuleByDeviceId(deviceId);
                  if (rmrule != null && !device.getRm_rule_version().equalsIgnoreCase(rmrule.getVersion())) {
                     rmruleInfo.deployToDevice(device, rmrule);
                  }
               }

               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PROTOCOL_PRIORITY") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PROTOCOL_PRIORITY").equals("")) {
                  device.setProtocol_priority(rs.getLong(".MO.DEVICE_CONF.SYSTEM_SETUP.PROTOCOL_PRIORITY"));
               }

               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.RM_DATA_SETTING") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.RM_DATA_SETTING").equals("")) {
                  device.setRm_data_setting(rs.getLong(".MO.DEVICE_CONF.SYSTEM_SETUP.RM_DATA_SETTING"));
               }

               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.SMART_DOWNLOAD") != null && !rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.SMART_DOWNLOAD").equals("")) {
                  device.setSmart_download(rs.getLong(".MO.DEVICE_CONF.SYSTEM_SETUP.SMART_DOWNLOAD"));
               }

               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.THIRD_APPLICATION_UPDATE_DOMAIN") != null) {
                  device.setThird_application_update_domain(rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.THIRD_APPLICATION_UPDATE_DOMAIN"));
               }

               if ((autoIpSet || autoComputerNameSet) && oldDevice != null && oldDevice.getIs_approved()) {
                  boolean sendSetIp = false;
                  sendSetComputerName = false;
                  if (autoIpSet && oldDevice.getIp_address() != null && !oldDevice.getIp_address().equals("") && !oldDevice.getIp_address().equals(device.getIp_address())) {
                     if (device.getIp_setting_type().equals("DHCP") && oldDevice.getIp_setting_type().equals("DHCP")) {
                        sendSetIp = false;
                     } else {
                        sendSetIp = true;
                     }

                     logger.info("IP address is different between old and current " + device.getDevice_id());
                  }

                  if (autoComputerNameSet && oldDevice.getComputer_name() != null && !oldDevice.getComputer_name().equals("") && !oldDevice.getComputer_name().equals(computerName)) {
                     sendSetComputerName = true;
                  }

                  if (sendSetIp) {
                     waitingTime = 6000L;
                     params = new HashMap();
                     DeviceNetworkConf network = new DeviceNetworkConf();
                     network.setIp_address(oldDevice.getIp_address());
                     network.setIp_setting_type(oldDevice.getIp_setting_type());
                     network.setSubnet_mask(oldDevice.getSubnet_mask());
                     network.setGateway(oldDevice.getGateway());
                     network.setDns_server_main(oldDevice.getDns_server_main());
                     network.setDns_server_sub(oldDevice.getDns_server_sub());
                     params.put("device_network_conf", network);
                     reservationManager = ReservationManagerImpl.getInstance();
                     reservation = new ScheduleReservation();
                     reservation.setDevice_id(device.getDevice_id());
                     reservation.setDevice_model_name(this.ctxt.getDevice().getDevice_model_name());
                     reservation.setService_id("SET_DEVICE_NETWORK_CONF");
                     reservation.setService_params(params);
                     reservation.setService_start_date(new Date(System.currentTimeMillis() + waitingTime));
                     reservationManager.insert(reservation);
                     device.setIp_address("");
                  }

                  if (sendSetComputerName) {
                     waitingTime = 9000L;
                     params = new HashMap();
                     systemSetup = new DeviceSystemSetupConf();
                     systemSetup.setComputer_name(oldDevice.getComputer_name());
                     params.put("device_system_setup_conf", systemSetup);
                     reservationManager = ReservationManagerImpl.getInstance();
                     reservation = new ScheduleReservation();
                     reservation.setDevice_id(device.getDevice_id());
                     reservation.setDevice_model_name(this.ctxt.getDevice().getDevice_model_name());
                     reservation.setService_id("SET_DEVICE_SYSTEM_SETUP_CONF");
                     reservation.setService_params(params);
                     reservation.setService_start_date(new Date(System.currentTimeMillis() + waitingTime));
                     reservationManager.insert(reservation);
                     computerName = "";
                  }
               }
            }

            device.setAuto_ip_set(autoIpSet);
            device.setAuto_computer_name_set(autoComputerNameSet);
            device.setUse_mpplayer(useMpplayer);
            device.setComputer_name(computerName);
            device.setVnc_password(dayLightSavingEnable);
            deviceDao.setDevicePostBootstrap(device);
            this.addBootingTimeAndReason(rs, deviceId);
            logger.error("[MagicInfo_PostBootstrap][" + deviceId + "] recv Post/send sysInfo. sendSysInfo=" + sendSysInfoCnt++);
            DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
            confMgr.reqGetSystemInfoFromDevice(this.ctxt.getDevice().getDevice_id(), "");
            MonitoringManager mgr = MonitoringManagerImpl.getInstance();
            mgr.setConnectionInfo(this.ctxt.getDevice().getDevice_id(), monitoring_interval);

            try {
               DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
               DeviceInfo devicedao = DeviceInfoImpl.getInstance();
               DeviceControl control;
               if (oldDevice.getDevice_type().equalsIgnoreCase("FLIP") && (double)oldDevice.getDevice_type_version() >= 2.0D) {
                  control = confManager.parsingGettingResultSetToEntity(deviceId, rs);
                  if (control != null && control.getSetup() != null && !StringUtils.isEmpty(control.getSetup().getPin_code())) {
                     control.getSetup().setPin_code(device.getPin_code());
                  }

                  devicedao.setDeviceControl(control);
                  logger.info("[MagicInfo_PostBootstrap][" + deviceId + "][FLIP2.0] success to set Device Info.");
               } else {
                  control = confManager.parsingGettingResultSetToEntity(deviceId, rs, false, false, false, true);
                  devicedao.setDeviceControl(control);
                  logger.info("[MagicInfo_PostBootstrap][" + deviceId + "][DISPLAY MO] success to set Device Info.");
               }
            } catch (Exception var111) {
               logger.error("[MagicInfo_PostBootstrap][" + deviceId + "] Set Device Info Exception " + var111.toString());
            }

            deviceQrtz.deleteDeviceQrtzCheckInfo(deviceId, "POST_BOOTSTRAP_SERVICE");
            logger.error("[MagicInfo_PostBootstrap][" + deviceId + "] Delete device info from qrtz check table");

            try {
               if (DeviceUtils.isSupportNOC()) {
                  DeviceNocInfo nocDao = DeviceNocInfoImpl.getInstance();
                  moVariableTagString = deviceDao.getDeviceGroupIdByDeviceId(deviceId);
                  boolean nocGroup = nocDao.isNocSupportGroup(Long.valueOf(moVariableTagString));
                  if (nocGroup) {
                     DeviceNocManager nocService = DeviceNocManagerImpl.getInstance();
                     tagIds = new ArrayList();
                     tagIds.add(deviceId);
                     nocService.thingworxUpdateGeneralAndSetup(tagIds);
                  }
               }
            } catch (Exception var104) {
               logger.error("[MagicInfo_PostBootstrap][thingworx] failed to call api for update postboot info :" + deviceId);
            }

            return appVer;
         }
      } catch (Exception var115) {
         logger.error("[MagicInfo_PostBootstrap][" + deviceId + "] " + var115.getMessage(), var115);
         logger.error("[MagicInfo_PostBootstrap][" + deviceId + "] Get Product : MO Exception !!!");
         throw new ServiceException(var115);
      }
   }

   private void addBootingTimeAndReason(ResultSet rs, String deviceId) {
      try {
         String bootReason = rs.getString(".MO.DISPLAY_CONF.GENERAL.BOOT_REASON");
         String bootTimeStr = rs.getString(".MO.DISPLAY_CONF.GENERAL.BOOT_TIME");
         Date bootTime = null;
         if (bootTimeStr != null && !bootTimeStr.equals("")) {
            bootTime = DateUtils.string2Date(bootTimeStr, "yyyy-MM-dd'T'HH:mm:ss");
         }

         DeviceConnHistoryInfo deviceConnHistoryInfo = DeviceConnHistoryInfoImpl.getInstance();
         Timestamp bootTimeTimestamp = bootTime == null ? null : new Timestamp(bootTime.getTime());
         deviceConnHistoryInfo.addConnectedTime(deviceId, new Timestamp(System.currentTimeMillis()), bootReason, bootTimeTimestamp);
      } catch (Exception var8) {
         logger.error(var8);
      }

   }

   protected RMQL setRMQL(Device device, Long service_seq, HashMap params) throws Exception {
      RMQL rmql = RMQLInstanceCreator.getInstance(device, "GET", service_seq);
      if (device.getDevice_type().equals("FLIP") && (double)device.getDevice_type_version() >= 2.0D) {
         rmql.addMO(".MO.DEVICE_CONF", "");
         rmql.addMO(".MO.DISPLAY_CONF", "");
      } else {
         rmql.addMO(".MO.DEVICE_CONF.GENERAL", "");
         rmql.addMO(".MO.DEVICE_CONF.NETWORK", "");
         rmql.addMO(".MO.DEVICE_CONF.SYSTEM_SETUP", "");
         rmql.addMO(".MO.MONITORING_INFO.CURRENT_SCHEDULE", "");
         if (device.getDevice_model_code().equals("8000")) {
            rmql.addMO(".MO.VM_INFO.STOCK_STATUS", "");
            rmql.addMO(".MO.VM_INFO.MACHINE_STATUS", "");
            rmql.addMO(".MO.VM_INFO.PLAN_O_GRAM", "");
            rmql.addMO(".MO.VM_INFO.DISPLAY_O_GRAM", "");
            rmql.addMO(".MO.VM_INFO.VERSION", "");
         }

         if (!device.getDevice_type().equalsIgnoreCase("LPLAYER") && !device.getDevice_model_code().equals("8000") && !device.getDevice_model_code().equals(String.valueOf(9998)) && !device.getDevice_model_code().equals(String.valueOf(9999))) {
            DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
            Device oldDevice = deviceDao.getDevice(device.getDevice_id());
            boolean isSupport;
            if (!oldDevice.getDevice_type().equals("SPLAYER") && !oldDevice.getDevice_type().equals("SIGNAGE") && !device.getDevice_type().equals("LPLAYER")) {
               try {
                  isSupport = true;
                  if (isSupport) {
                     rmql.addMO(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT", "");
                  }
               } catch (Exception var9) {
                  rmql.addMO(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT", "");
               }
            }

            if (!oldDevice.getDevice_type_version().equals(CommonDataConstants.TYPE_VERSION_1_0) || !oldDevice.getDevice_type().equals("SPLAYER")) {
               try {
                  isSupport = false;
                  if (device.getDevice_type().equals("iPLAYER")) {
                     isSupport = true;
                  } else {
                     isSupport = true;
                  }

                  if (isSupport) {
                     rmql.addMO(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK", "");
                  }
               } catch (Exception var8) {
                  rmql.addMO(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK", "");
               }
            }

            rmql.addMO(".MO.DISPLAY_CONF", "");
         }
      }

      return rmql;
   }

   protected void doPreprocess(HashMap params) {
   }

   public String getEdgeServerMOStringFromInfo(String downloadServerString) {
      String edgeMOString = "";
      if (!downloadServerString.isEmpty() && downloadServerString.indexOf(33) != 0) {
         try {
            V2EdgeService v2EdgeService = (V2EdgeServiceImpl)BeanUtils.getBean("V2EdgeServiceImpl");
            edgeMOString = "";
            String edgeServerOnly = downloadServerString.split("!")[1];
            downloadServerString.replace("!" + edgeServerOnly, "");
            String[] edgeServerList = downloadServerString.split("@");

            for(int i = 0; i < edgeServerList.length; ++i) {
               EdgeServer edgeServer = null;
               String edgeServerString = edgeServerList[i];
               String[] edgeServerSpecifics = edgeServerString.split(";");
               String hostName = edgeServerSpecifics[0];

               try {
                  edgeServer = v2EdgeService.selectEdgeServerByHostName(hostName);
               } catch (Exception var12) {
                  logger.error("", var12);
               }

               if (edgeServer != null) {
                  edgeMOString = edgeMOString + edgeServer.getIpAddress() + ";21";
               }

               if (i != edgeServerList.length - 1) {
                  edgeMOString = edgeMOString + "@";
               }
            }

            edgeMOString = edgeMOString + "!" + edgeServerOnly;
         } catch (Exception var13) {
            logger.error(var13.getMessage());
         }

         return edgeMOString;
      } else {
         return "";
      }
   }

   public String getEdgeServerMOString() {
      StringBuilder rtn = new StringBuilder();
      DeviceSystemSetupConfManagerImpl systemSetupDao = DeviceSystemSetupConfManagerImpl.getInstance();

      try {
         List edgeServersList = systemSetupDao.getEdgeServerMappingList(this.ctxt.getDevice().getDevice_id());
         if (edgeServersList == null) {
            return rtn.toString();
         }

         for(int i = 0; i < edgeServersList.size(); ++i) {
            if (i > 0) {
               rtn.append("@");
            }

            rtn.append(((EdgeServer)edgeServersList.get(i)).getIpAddress() == null ? "" : ((EdgeServer)edgeServersList.get(i)).getHostName() + ";" + ((EdgeServer)edgeServersList.get(i)).getIpAddress()).append(";");
            rtn.append("21");
         }
      } catch (Exception var5) {
         return rtn.toString();
      }

      return rtn.toString();
   }

   private boolean isNeedSetVarTags(List varTagsDb, List varTags) {
      if (varTagsDb == null && varTags == null) {
         return false;
      } else if (varTagsDb == null && varTags != null || varTagsDb != null && varTags == null) {
         return true;
      } else if (varTagsDb.size() == 0 && varTags.size() == 0) {
         return false;
      } else if (varTagsDb.size() != varTags.size()) {
         return true;
      } else {
         boolean ret = false;

         try {
            Iterator var4 = varTags.iterator();

            while(var4.hasNext()) {
               String varTag = (String)var4.next();
               String[] devTagInfo = varTag.split(":");
               if (devTagInfo.length < 3) {
                  ret = true;
                  break;
               }

               for(int k = 0; k < varTagsDb.size(); ++k) {
                  DeviceTag dbTag = (DeviceTag)varTagsDb.get(k);
                  if (dbTag.getTag_name().equals(devTagInfo[0]) && dbTag.getTag_type().equals(Long.parseLong(devTagInfo[1])) && dbTag.getTag_condition().equals(devTagInfo[2])) {
                     break;
                  }

                  if (k == varTagsDb.size() - 1) {
                     ret = true;
                     break;
                  }
               }

               if (ret) {
                  break;
               }
            }
         } catch (Exception var9) {
            logger.error("", var9);
            ret = true;
         }

         return ret;
      }
   }
}
