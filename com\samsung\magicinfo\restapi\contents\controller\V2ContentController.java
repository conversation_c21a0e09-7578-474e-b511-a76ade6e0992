package com.samsung.magicinfo.restapi.contents.controller;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.kpi.annotation.KPI;
import com.samsung.magicinfo.framework.kpi.annotation.LogProperty;
import com.samsung.magicinfo.mvc.handler.ApiVersion;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.common.model.V2CommonResultResource;
import com.samsung.magicinfo.restapi.contents.model.V2AdsContentSettingResource;
import com.samsung.magicinfo.restapi.contents.model.V2AdsContentSuggestionResource;
import com.samsung.magicinfo.restapi.contents.model.V2CifsContentSettingResource;
import com.samsung.magicinfo.restapi.contents.model.V2ContentAdvertisementEditResource;
import com.samsung.magicinfo.restapi.contents.model.V2ContentAdvertisementMultiEditResource;
import com.samsung.magicinfo.restapi.contents.model.V2ContentAdvertisementResource;
import com.samsung.magicinfo.restapi.contents.model.V2ContentApproval;
import com.samsung.magicinfo.restapi.contents.model.V2ContentApprovals;
import com.samsung.magicinfo.restapi.contents.model.V2ContentCategoryResourceRequestWrapper;
import com.samsung.magicinfo.restapi.contents.model.V2ContentCheckResource;
import com.samsung.magicinfo.restapi.contents.model.V2ContentConvertibility;
import com.samsung.magicinfo.restapi.contents.model.V2ContentConvertibilityList;
import com.samsung.magicinfo.restapi.contents.model.V2ContentDeleteParam;
import com.samsung.magicinfo.restapi.contents.model.V2ContentEdit;
import com.samsung.magicinfo.restapi.contents.model.V2ContentExpireDate;
import com.samsung.magicinfo.restapi.contents.model.V2ContentFileResource;
import com.samsung.magicinfo.restapi.contents.model.V2ContentIds;
import com.samsung.magicinfo.restapi.contents.model.V2ContentListFilter;
import com.samsung.magicinfo.restapi.contents.model.V2ContentResource;
import com.samsung.magicinfo.restapi.contents.model.V2ContentShare;
import com.samsung.magicinfo.restapi.contents.model.V2ContentTagAssignment;
import com.samsung.magicinfo.restapi.contents.model.V2ContentThumbnailResource;
import com.samsung.magicinfo.restapi.contents.model.V2ContentVersion;
import com.samsung.magicinfo.restapi.contents.model.V2ContentWebAuthorResource;
import com.samsung.magicinfo.restapi.contents.model.V2FtpContentSettingResource;
import com.samsung.magicinfo.restapi.contents.model.V2PlaylistAddData;
import com.samsung.magicinfo.restapi.contents.model.V2PlaylistAddResource;
import com.samsung.magicinfo.restapi.contents.model.V2UrlContentSettingResource;
import com.samsung.magicinfo.restapi.contents.service.V2ContentService;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.utils.RestAPIUtil;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.Authorization;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

@Api(
   value = "Content Management System Rest api",
   description = "Operations pertaining to content in Content Management System",
   tags = {"Content API Group"}
)
@RestController
@RequestMapping({"/restapi/v2.0/cms/contents"})
@Validated
@ApiVersion({2.0D})
public class V2ContentController {
   @Autowired
   private V2ContentService V2ContentService;
   private final Logger logger = LoggingManagerV2.getLogger(this.getClass());

   public V2ContentController() {
      super();
   }

   @ApiOperation(
      value = "Get all content list",
      notes = "View a list of all the contents and information\r\nWhen calling API, query contents from startIndex and return as many as pageSize.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "startIndex",
   value = "Content query starting point.",
   required = true,
   dataType = "int",
   allowableValues = "range[1, infinity]"
), @ApiImplicitParam(
   name = "pageSize",
   value = "The number of content lists to show.",
   required = true,
   dataType = "int",
   allowableValues = "range[1, infinity]"
)})
   @ApiResponses({@ApiResponse(
   code = 400,
   message = "Bad Request \n errorCode : 400002, groupId is invalid value.\n errorCode : 400002, sizeFilter is invalid value."
)})
   @GetMapping(
      value = {""},
      produces = {"application/json"}
   )
   public ResponseEntity getContents(@RequestParam("startIndex") @Min(value = 1L,message = "Minimum value of startIndex is 1") int startIndex, @RequestParam("pageSize") @Min(value = 1L,message = "Minimum value of pageSize is 1") int pageSize) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][getContents] get all content list");
      V2ContentListFilter filter = new V2ContentListFilter();
      filter.setStartIndex(startIndex);
      filter.setPageSize(pageSize);
      V2PageResource resources = this.V2ContentService.getContents(filter);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resources.getList(), resources.getStartIndex(), resources.getPageSize(), resources.getRecordsTotal());
      this.logger.info("[REST_v2.0][CONTENTS][getContents] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Search for content based on filters",
      notes = "This feature is based on GET /restapi/v2.0/cms/contents\r\nIt is designed so that you can search by using one or several filter-items. Clearing all values returns all lists.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "filter",
   value = "Search filter for list of contents condition.",
   dataType = "V2ContentListFilter"
)})
   @PostMapping(
      value = {"/filter"},
      produces = {"application/json"}
   )
   public ResponseEntity getContentsWithFilter(@Valid @RequestBody V2ContentListFilter filter, BindingResult result) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][getContentsWithFilter] get content list with filter");
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         V2PageResource pageResource = this.V2ContentService.getContents(filter);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(pageResource.getList(), pageResource.getStartIndex(), pageResource.getPageSize(), pageResource.getRecordsTotal());
         this.logger.info("[REST_v2.0][CONTENTS][getContentsWithFilter] finish successfully.");
         return new ResponseEntity(responseBody, HttpStatus.OK);
      }
   }

   @ApiOperation(
      value = "Get specific content information",
      notes = "Get information about specific content based on contentId.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "Enter a content ID.",
   required = true,
   dataType = "string",
   example = "94656F46-D6D1-454A-A08C-2250D367F615"
)})
   @ApiResponses({@ApiResponse(
   code = 404,
   message = "Data not found \n errorCode : 404001, The contentId is not found."
)})
   @GetMapping(
      value = {"/{contentId}"},
      produces = {"application/json"}
   )
   public ResponseEntity getContent(@PathVariable("contentId") @Pattern(regexp = "^[0-9A-Fa-f]{8}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{12}$",message = "Not UUID pattern.") @NotEmpty @NotNull String contentId) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][getContent] get content detail");
      V2ContentResource resource = this.V2ContentService.getContent(contentId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      this.logger.info("[REST_v2.0][CONTENTS][getContent] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Approve or disapprove for a content",
      notes = "Approve or disapprove for a unauthorized content",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "Id of specific content",
   required = true,
   dataType = "String"
), @ApiImplicitParam(
   name = "approval",
   value = "Choose whether to approve content",
   required = true,
   dataType = "V2ContentApproval"
)})
   @ApiResponses({@ApiResponse(
   code = 404,
   message = "Data not found \n errorCode : 404001, The contentId information is not found."
)})
   @PutMapping(
      value = {"/unapproved-contents/{contentId}"},
      produces = {"application/json"}
   )
   public ResponseEntity approveContent(@PathVariable String contentId, @Valid @RequestBody V2ContentApproval approval, BindingResult result) throws Exception {
      this.logger.info("[REST_v2.0][CONTENT][approveContent][" + contentId + "] approve/reject by contentId");
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         V2ContentResource resource = this.V2ContentService.approveContent(contentId, approval);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
         this.logger.info("[REST_v2.0][CONTENTS][approveContent] finish successfully.");
         return new ResponseEntity(responseBody, HttpStatus.OK);
      }
   }

   @ApiOperation(
      value = "Approve or disapprove for multiple contents",
      notes = "Approve or disapprove for multiple unauthorized contents",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "approvals",
   value = "Resource for approved contents list.",
   required = true,
   dataType = "V2ContentApprovals"
)})
   @PutMapping(
      value = {"/unapproved-contents"},
      produces = {"application/json"}
   )
   public ResponseEntity approveContents(@Valid @RequestBody V2ContentApprovals approvals, BindingResult result) throws Exception {
      this.logger.info("[REST_v2.0][CONTENT][approveContents] approve/reject with V2ContentApproval model list");
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         List resource = this.V2ContentService.approveContents(approvals);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
         responseBody.setTotalCount(resource.size());
         this.logger.info("[REST_v2.0][CONTENTS][approveContents] finish successfully.");
         return new ResponseEntity(responseBody, HttpStatus.OK);
      }
   }

   @ApiOperation(
      value = "Create CIFS content",
      notes = "Create CIFS content",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "resource",
   value = "Parameter group for creating CIFS type content. The values of the sub-parameters are all required.",
   required = true,
   dataType = "V2CifsContentSettingResource"
)})
   @ApiResponses({@ApiResponse(
   code = 400,
   message = "Bad Request \n errorCode : 400308, Failed to connect to the Remote Server.\n errorCode : 400309, The available files do not exist in the server.\n errorCode : 400310, One or more parameter(s) are invalid in Remote Content.\n errorCode : 400311, Failed to login to the remote server.\n errorCode : 400312, The same content already exists in the Content List.\n errorCode : 400313, Invalid directory path of remote server."
)})
   @PostMapping(
      value = {"/cifs-settings"},
      produces = {"application/json"}
   )
   public ResponseEntity createCifsContent(@Valid @RequestBody V2CifsContentSettingResource resource, BindingResult result) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][createCifsContent][" + resource.getCifsContentName() + "]");
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         V2CifsContentSettingResource newResource = this.V2ContentService.createCifsContent(resource);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(newResource);
         this.logger.info("[REST_v2.0][CONTENTS][createCifsContent] finish successfully.");
         return new ResponseEntity(responseBody, HttpStatus.OK);
      }
   }

   @ApiOperation(
      value = "Get specific CIFS content information",
      notes = "Information query function for CIFS type contents.\r\nResults are displayed based on CIFS information.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "A specific content ID.",
   required = true,
   dataType = "String"
)})
   @GetMapping(
      value = {"/cifs-settings/{contentId}"},
      produces = {"application/json"}
   )
   public ResponseEntity getCifsContent(@NotEmpty @NotNull @PathVariable("contentId") String contentId) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][getCifsContent][" + contentId + "]");
      V2CifsContentSettingResource resource = this.V2ContentService.getCifsContentSetting(contentId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      this.logger.info("[REST_v2.0][CONTENTS][getCifsContent] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Edit the specific CIFS content information",
      notes = "Edit the specific CIFS content information",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "A specific content ID.",
   required = true,
   dataType = "String"
), @ApiImplicitParam(
   name = "resource",
   value = "Parameter group for creating CIFS type content. Enter only the values of sub- parameters you want to edit.",
   required = true,
   dataType = "V2CifsContentSettingResource"
)})
   @ApiResponses({@ApiResponse(
   code = 400,
   message = "Bad Request \n errorCode : 400010, No changes have been made.\n errorCode : 400301, One or more parameter(s) are invalid in Remote Content.\n errorCode : 400302, You cannot edit while download is in progress.\n errorCode : 400308, Failed to connect to the Remote Server.\n errorCode : 400311, Failed to login to the remote server.\n errorCode : 400312, The same content already exists in the Content List.\n errorCode : 400313, Invalid directory path of remote server."
), @ApiResponse(
   code = 404,
   message = "Data not found \n errorCode : 404001, The content is not found."
)})
   @PutMapping(
      value = {"/cifs-settings/{contentId}"},
      produces = {"application/json"}
   )
   public ResponseEntity updateCifsContent(@NotEmpty @NotNull @PathVariable("contentId") String contentId, @Valid @RequestBody V2CifsContentSettingResource resource, BindingResult result) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][updateCifsContent][" + contentId + "]");
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         V2CifsContentSettingResource updatedResource = this.V2ContentService.updateCifsContentSetting(contentId, resource);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(updatedResource);
         this.logger.info("[REST_v2.0][CONTENTS][updateCifsContent] finish successfully.");
         return new ResponseEntity(responseBody, HttpStatus.OK);
      }
   }

   @ApiOperation(
      value = "Create FTP content",
      notes = "Create FTP content.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiResponses({@ApiResponse(
   code = 400,
   message = "Bad Request \n errorCode : 400308, Failed to connect to the Remote Server.\n errorCode : 400309, The available files do not exist in the server.\n errorCode : 400310, One or more parameter(s) are invalid in Remote Content.\n errorCode : 400311, Failed to login to the remote server.\n errorCode : 400312, The same content already exists in the Content List.\n errorCode : 400313, Invalid directory path of remote server."
)})
   @ApiImplicitParams({@ApiImplicitParam(
   name = "resource",
   required = true,
   value = "Parameter group for creating FTP type content. The values of the sub-parameters are all required.",
   dataType = "V2FtpContentSettingResource"
)})
   @PostMapping(
      value = {"/ftp-settings"},
      produces = {"application/json"}
   )
   public ResponseEntity createFtpContent(@Valid @RequestBody V2FtpContentSettingResource resource, BindingResult result) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][createFtpContent][" + resource + "]");
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         V2FtpContentSettingResource newResource = this.V2ContentService.createFtpContent(resource);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(newResource);
         this.logger.info("[REST_v2.0][CONTENTS][createFtpContent] finish successfully.");
         return new ResponseEntity(responseBody, HttpStatus.OK);
      }
   }

   @ApiOperation(
      value = "Get a specific FTP content information",
      notes = "Information query function for FTP type contents.\r\nResults are displayed based on FTP information.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "ID of specific content.",
   dataType = "String",
   required = true
)})
   @GetMapping(
      value = {"/ftp-settings/{contentId}"},
      produces = {"application/json"}
   )
   public ResponseEntity getFtpContent(@Valid @PathVariable String contentId) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][getFtpContent][" + contentId + "]");
      V2FtpContentSettingResource resource = this.V2ContentService.getFtpContent(contentId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      this.logger.info("[REST_v2.0][CONTENTS][getFtpContent] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Edit specific FTP content information",
      notes = "Edit specific FTP content information",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "ID of specific content.",
   dataType = "String",
   required = true
), @ApiImplicitParam(
   name = "resource",
   value = "Resource for FTP content information edit.",
   dataType = "V2FtpContentSettingResource"
)})
   @ApiResponses({@ApiResponse(
   code = 400,
   message = "Bad Request \n errorCode : 400002, Remote Content is invalid value.\n errorCode : 400002, Remote directory is invalid value.\n errorCode : 400009, The same Content List exists.\n errorCode : 400010, No changes have been made.\n errorCode : 400011, The FTP connection is failed.\n errorCode : 400011, The time out in ftp server connection is failed.\n errorCode : 400300, You failed to login to the FTP server. Please check your login information.\n errorCode : 400302, You cannot edit while download is in progress."
), @ApiResponse(
   code = 500,
   message = "Internal Server Error \n errorCode : 500302, An unexpected error has occurred while updating the CIFS content."
)})
   @PutMapping(
      value = {"/ftp-settings/{contentId}"},
      produces = {"application/json"}
   )
   public ResponseEntity updateFtpContent(@NotEmpty @NotNull @PathVariable("contentId") String contentId, @Valid @RequestBody V2FtpContentSettingResource resource, BindingResult result) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][updateFtpContent][" + contentId + "]");
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         V2FtpContentSettingResource updatedResource = this.V2ContentService.updateFtpContent(contentId, resource);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(updatedResource);
         this.logger.info("[REST_v2.0][CONTENTS][updateFtpContent] finish successfully.");
         return new ResponseEntity(responseBody, HttpStatus.OK);
      }
   }

   @ApiOperation(
      value = "Create URL type content",
      notes = "Create URL type content.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "resource",
   required = true,
   value = "Resource for URL type content setting. This model is used in common with streaming type.",
   dataType = "V2UrlContentSettingResource"
)})
   @PostMapping(
      value = {"/url-settings"},
      produces = {"application/json"}
   )
   public ResponseEntity createUrlContent(@Valid @RequestBody V2UrlContentSettingResource resource, BindingResult result) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][createUrlContent][" + resource.getUrlContentName() + "]");
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         V2UrlContentSettingResource newResource = this.V2ContentService.createUrlContent(resource);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(newResource);
         this.logger.info("[REST_v2.0][CONTENTS][createUrlContent] finish successfully.");
         return new ResponseEntity(responseBody, HttpStatus.OK);
      }
   }

   @ApiOperation(
      value = "Get specific URL type content information",
      notes = "This API shows only URL related information among specific content information.\r\nMore information can be found in GET .../cms/contents  & GET .../cms/contents/{contentId}.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "ID of specific content.",
   required = true,
   dataType = "String"
)})
   @GetMapping(
      value = {"/url-settings/{contentId}"},
      produces = {"application/json"}
   )
   public ResponseEntity getUrlContent(@Valid @PathVariable String contentId) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][getUrlContent][" + contentId + "]");
      V2UrlContentSettingResource resource = this.V2ContentService.getUrlContent(contentId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      this.logger.info("[REST_v2.0][CONTENTS][getUrlContent] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Edit specific URL type content information",
      notes = "Edit specific URL type content information",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "ID of specific content.",
   required = true,
   dataType = "String"
), @ApiImplicitParam(
   name = "resource",
   value = "Resource for URL type content setting edit. This model is used in common with streaming type.",
   dataType = "V2UrlContentSettingResource"
)})
   @ApiResponses({@ApiResponse(
   code = 400,
   message = "Bad Request \n errorCode : 400002, contentId is invalid value."
)})
   @PutMapping(
      value = {"/url-settings/{contentId}"},
      produces = {"application/json"}
   )
   public ResponseEntity updateUrlContent(@NotEmpty @NotNull @PathVariable("contentId") String contentId, @Valid @RequestBody V2UrlContentSettingResource resource, BindingResult result) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][updateUrlContent][" + contentId + "]");
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         V2UrlContentSettingResource updatedResource = this.V2ContentService.updateUrlContent(contentId, resource);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(updatedResource);
         this.logger.info("[REST_v2.0][CONTENTS][updateUrlContent] finish successfully.");
         return new ResponseEntity(responseBody, HttpStatus.OK);
      }
   }

   @ApiOperation(
      value = "Get ADS content suggestion information",
      notes = "Get suggestions for pulisher info and ad unit id",
      authorizations = {@Authorization("api_key")}
   )
   @GetMapping(
      value = {"/ads-content-suggestion-info"},
      produces = {"application/json"}
   )
   public ResponseEntity getAdsContentSuggestionInfo() throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][getAdsContentSuggestionInfo]");
      V2AdsContentSuggestionResource resource = this.V2ContentService.getAdsContentSuggestionInfo();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      this.logger.info("[REST_v2.0][CONTENTS][getAdsContentSuggestionInfo] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Delete ADS content suggestion information",
      notes = "Delete suggestions for publisher info and ad unit id",
      authorizations = {@Authorization("api_key")}
   )
   @DeleteMapping(
      value = {"/ads-content-delete-suggestion-info/{suggestionType}/{suggestionId}"},
      produces = {"application/json"}
   )
   public ResponseEntity deleteAdsContentSuggestionInfo(@Pattern(regexp = "AD_UNIT_ID_LIST|PUBLISHER_INFO_LIST",message = "Not supported suggestion type.") @NotNull @NotEmpty @PathVariable String suggestionType, @NotNull @NotEmpty @PathVariable String suggestionId) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][deleteAdsContentSuggestionInfo]");
      V2CommonResultResource resource = this.V2ContentService.deleteAdsContentSuggestionInfo(suggestionType, suggestionId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      this.logger.info("[REST_v2.0][CONTENTS][deleteAdsContentSuggestionInfo] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Create Ads content type",
      notes = "Create Ads content type",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "resource",
   required = true,
   value = "Resource for ads type content setting.",
   dataType = "V2AdsContentSettingResource"
)})
   @PostMapping(
      value = {"/ads-settings"},
      consumes = {"application/json"},
      produces = {"application/json"}
   )
   public ResponseEntity createAdsContent(@RequestBody V2AdsContentSettingResource resource, BindingResult result) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][createAdsContent][]");
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         V2AdsContentSettingResource newResource = this.V2ContentService.createAdsContent(resource);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(newResource);
         return new ResponseEntity(responseBody, HttpStatus.OK);
      }
   }

   @ApiOperation(
      value = "Edit specific ADS content",
      notes = "Edit specific ADS content",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "ID of specific content.",
   dataType = "String",
   required = true
), @ApiImplicitParam(
   name = "resource",
   value = "Resource for ADS content edit.",
   dataType = "V2AdsContentSettingResource"
)})
   @PutMapping(
      value = {"/ads-settings/{contentId}"},
      produces = {"application/json"}
   )
   public ResponseEntity updateAdsContent(@NotEmpty @NotNull @PathVariable("contentId") String contentId, @Valid @RequestBody V2AdsContentSettingResource resource, BindingResult result) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][updateAdsContent][" + contentId + "]");
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         V2AdsContentSettingResource updatedResource = this.V2ContentService.updateAdsContent(contentId, resource);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(updatedResource);
         this.logger.info("[REST_v2.0][CONTENTS][updateAdsContent] finish successfully.");
         return new ResponseEntity(responseBody, HttpStatus.OK);
      }
   }

   @ApiOperation(
      value = "Get a specific ADS content information",
      notes = "Information query function for ADS type contents.\r\nResults are displayed based on ADS information.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "ID of specific content.",
   dataType = "String",
   required = true
)})
   @GetMapping(
      value = {"/ads-settings/{contentId}"},
      produces = {"application/json"}
   )
   public ResponseEntity getAdsContent(@Valid @PathVariable String contentId) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][getAdsContent][" + contentId + "]");
      V2AdsContentSettingResource resource = this.V2ContentService.getAdsContent(contentId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      this.logger.info("[REST_v2.0][CONTENTS][getAdsContent] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Create STRM (Streaming) content type",
      notes = "Create STRM (Streaming) content type.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "resource",
   required = true,
   value = "Resource for streaming type content setting. The values of the sub-parameters are all required.",
   dataType = "V2UrlContentSettingResource"
)})
   @PostMapping(
      value = {"/strm-settings"},
      produces = {"application/json"}
   )
   public ResponseEntity createStrmContent(@Valid @RequestBody V2UrlContentSettingResource resource, BindingResult result) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][createStrmContent][" + resource.getUrlContentName() + "]");
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         V2UrlContentSettingResource newResource = this.V2ContentService.createStrmContent(resource);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(newResource);
         this.logger.info("[REST_v2.0][CONTENTS][createStrmContent] finish successfully.");
         return new ResponseEntity(responseBody, HttpStatus.OK);
      }
   }

   @ApiOperation(
      value = "Get specific STRM (Streaming) type content information",
      notes = "This API shows only streaming related information among specific content information.\r\nMore information can be found in GET /restapi/v2.0/cms/contents/{contentId}.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "ID of specific content.",
   required = true,
   dataType = "String"
)})
   @GetMapping(
      value = {"/strm-settings/{contentId}"},
      produces = {"application/json"}
   )
   public ResponseEntity getStrmContent(@Valid @PathVariable String contentId) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][getStrmContent][" + contentId + "]");
      V2UrlContentSettingResource resource = this.V2ContentService.getStrmContent(contentId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      this.logger.info("[REST_v2.0][CONTENTS][getStrmContent] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Edit specific STRM (Streaming) type content information",
      notes = "Edit specific STRM (Streaming) type content information.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "ID of specific content.",
   required = true,
   dataType = "String"
), @ApiImplicitParam(
   name = "resource",
   value = "Resource for streaming content setting edit.",
   dataType = "V2UrlContentSettingResource"
)})
   @ApiResponses({@ApiResponse(
   code = 400,
   message = "Bad Request \n errorCode : 400002, contentId and expirationDate is invalid value."
)})
   @PutMapping(
      value = {"/strm-settings/{contentId}"},
      produces = {"application/json"}
   )
   public ResponseEntity updateStrmContent(@NotEmpty @NotNull @PathVariable("contentId") String contentId, @Valid @RequestBody V2UrlContentSettingResource resource, BindingResult result) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][updateStrmContent][" + contentId + "]");
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         V2UrlContentSettingResource updatedResource = this.V2ContentService.updateStrmContent(contentId, resource);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(updatedResource);
         this.logger.info("[REST_v2.0][CONTENTS][updateStrmContent] finish successfully.");
         return new ResponseEntity(responseBody, HttpStatus.OK);
      }
   }

   @ApiOperation(
      value = "Change group of contents",
      notes = "Move multiple contents to one specified group.\r\nThe contents can be specified in an array-list.\r\nSee 'Models' at the bottom of Swagger\r\n'successList' of the response data is List&#60;V2ContentResource&#62; type and this value consists of resources for the contentId that was processed successfully.\r\n'failList' of the response data is List&#60;V2ContentResource&#62; type and this value consists of resources for the contentId that was not processed.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "groupId",
   value = "Id of specific group.",
   required = true,
   dataType = "string"
), @ApiImplicitParam(
   name = "body",
   value = "specific content IDs.",
   required = true,
   dataType = "V2ContentIds"
)})
   @PostMapping(
      value = {"/{groupId}/moved-contents"},
      produces = {"application/json"}
   )
   public ResponseEntity changeGroup(@Valid @RequestBody V2ContentIds body, @PathVariable("groupId") String groupId) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][changeGroup] change group");
      V2CommonResultResource resource = this.V2ContentService.changeGroup(body, groupId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      HttpStatus status = HttpStatus.OK;
      if (resource.getFailList() != null && resource.getFailList().size() > 0) {
         status = HttpStatus.BAD_REQUEST;
         responseBody.setStatus("Fail");
      }

      responseBody.setTotalCount(body.getContentIds().size());
      this.logger.info("[REST_v2.0][CONTENTS][changeGroup][" + body.getContentIds() + "] finish successfully.");
      return new ResponseEntity(responseBody, status);
   }

   @LogProperty(
      eventType = "Restore content"
   )
   @ApiOperation(
      value = "Restore from recycle-bin",
      notes = "Restore deleted contents from recycle bin to default group.\r\n'successList' of the response data is List&#60;String&#62; type and this value consists of contentIds that was processed successfully.\r\n'failList' of the response data is List&#60;String&#62; type and this value consists of contentIds that were not processed.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "body",
   value = "IDs of specific contents.",
   dataType = "V2ContentIds"
)})
   @PostMapping(
      value = {"/restored-contents"},
      produces = {"application/json"}
   )
   public ResponseEntity contentsRestore(@Valid @RequestBody V2ContentIds body) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][contentsRestore] contents restore");
      V2CommonResultResource resource = this.V2ContentService.contentRestore(body);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      HttpStatus status = HttpStatus.OK;
      if (resource.getFailList() != null && resource.getFailList().size() > 0) {
         status = HttpStatus.BAD_REQUEST;
         responseBody.setStatus("Fail");
      }

      responseBody.setTotalCount(body.getContentIds().size());
      this.logger.info("[REST_v2.0][CONTENTS][contentsRestore] finish successfully.");
      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Check LFD file can be converted to template",
      notes = "Check LFD file can be converted to template.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "Content ID. Refer to POST .../contents/filter and \"contentType\", \"contentTypes\".",
   required = true,
   dataType = "string"
)})
   @GetMapping(
      value = {"/{contentId}/template-convertibility"},
      produces = {"application/json"}
   )
   public ResponseEntity checkConvertibility(@PathVariable("contentId") String contentId) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][checkConvertibility] Check can convert");
      V2ContentConvertibility convertibility = this.V2ContentService.checkConvertibility(contentId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(convertibility);
      this.logger.info("[REST_v2.0][CONTENTS][checkConvertibility] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Check multiple LFD files can be converted to template",
      notes = "Check multiple LFD files can be converted to template.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "list",
   value = "V2ContentConvertibility list including contentId to check convertibility.",
   required = true,
   dataType = "V2ContentConvertibilityList"
)})
   @PostMapping(
      value = {"/template-convertibility"},
      produces = {"application/json"}
   )
   public ResponseEntity checkConvertibility(@Valid @RequestBody V2ContentConvertibilityList list, BindingResult result) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][checkConvertibility] Check can convert");
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         V2ContentConvertibilityList convertibilityList = this.V2ContentService.checkConvertibility(list);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(convertibilityList.getConvertibilityList(), 0, 0, convertibilityList.getConvertibilityList().size());
         this.logger.info("[REST_v2.0][CONTENTS][checkConvertibility] finish successfully.");
         return new ResponseEntity(responseBody, HttpStatus.OK);
      }
   }

   @ApiOperation(
      value = "Change multiple categories by specified multiple contents",
      notes = "Specify categories of multiple contents.\r\nThe categories and contents can be specified in an array-list.\r\nSee 'Models' at the bottom of Swagger\r\n'successList' of the response data is List&#60;V2ContentResource&#62; type and this value consists of resources for the contentId that was processed successfully.\r\n'failList' of the response data is List&#60;String&#62; type and this value consists of contentIds that were not processed.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "categories",
   value = "Specific a single or multiple content and category IDs.",
   required = true,
   dataType = "V2ContentCategoryResourceRequestWrapper"
)})
   @PostMapping(
      value = {"/categories"},
      produces = {"application/json"}
   )
   public ResponseEntity updateCategory(@Valid @RequestBody V2ContentCategoryResourceRequestWrapper categories) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][updateCategory] update category");
      HttpStatus status = HttpStatus.OK;
      V2CommonResultResource resource = this.V2ContentService.updateCategory(categories);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      if (resource.getFailList() != null && resource.getFailList().size() > 0) {
         status = HttpStatus.INTERNAL_SERVER_ERROR;
         responseBody.setStatus("Fail");
      }

      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Change category of multiple contents",
      notes = "Change category of multiple contents with contentId and target categoryId to move.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "categoryId",
   value = "A specific category ID.",
   required = true,
   dataType = "string"
), @ApiImplicitParam(
   name = "body",
   value = "Specific content IDs.",
   required = true,
   dataType = "V2ContentIds"
)})
   @PutMapping(
      value = {"/categories/{categoryId}"},
      produces = {"application/json"}
   )
   public ResponseEntity updateCategory(@NotEmpty @NotNull @PathVariable("categoryId") String categoryId, @Valid @RequestBody V2ContentIds body, BindingResult result) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][updateCategory] content restore");
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         V2PageResource pageResource = this.V2ContentService.updateCategory(body, categoryId);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(pageResource.getList(), 0, 0, pageResource.getRecordsTotal());
         this.logger.info("[REST_v2.0][CONTENTS][updateCategory] finish successfully.");
         return new ResponseEntity(responseBody, HttpStatus.OK);
      }
   }

   @ApiOperation(
      value = "Get content version list",
      notes = "Get all version list about  related content version information.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "ID of specific content. Refer to GET .../contents.",
   required = true,
   dataType = "string"
)})
   @GetMapping(
      value = {"/{contentId}/versions"},
      produces = {"application/json"}
   )
   public ResponseEntity getVersionList(@NotNull @NotEmpty @PathVariable String contentId) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][getVersionList] Check Moveable");
      List contentVersionResources = this.V2ContentService.getVersionList(contentId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(contentVersionResources, 0, 0, contentVersionResources.size());
      this.logger.info("[REST_v2.0][CONTENTS][getVersionList] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get list of Playlist and Schedule having this content",
      notes = "Get all Playlists and Schedules using the content",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "ID of specific content. Refer to GET .../contents.",
   required = true,
   dataType = "string"
)})
   @GetMapping(
      value = {"/content-use/{contentId}"},
      produces = {"application/json"}
   )
   public ResponseEntity getContentUseList(@NotNull @NotEmpty @PathVariable String contentId) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][getContentUseList] Content Use in Playlist and Schedule");
      List contentUseResources = this.V2ContentService.getContentUseList(contentId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(contentUseResources);
      this.logger.info("[REST_v2.0][CONTENTS][getContentUseList] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Update content version",
      notes = "Update version of specified content.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "Id value of content",
   required = true,
   dataType = "string"
), @ApiImplicitParam(
   name = "versionId",
   value = "Id value of version to be changed",
   required = true,
   dataType = "string"
)})
   @ApiResponses({@ApiResponse(
   code = 404,
   message = "Data not found \n errorCode : 404001, contentId is not found."
), @ApiResponse(
   code = 500,
   message = "Internal Server Error"
)})
   @PutMapping(
      value = {"/{contentId}/versions/{versionId}"},
      produces = {"application/json"}
   )
   public ResponseEntity updateContentVersion(@NotNull @NotEmpty @PathVariable String contentId, @NotNull @NotEmpty @PathVariable String versionId) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][updateContentVersion] Update version of content");
      V2ContentResource contentResource = this.V2ContentService.updateContentVersion(contentId, versionId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(contentResource);
      this.logger.info("[REST_v2.0][CONTENTS][updateContentVersion] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @KPI
   @LogProperty(
      eventType = "Edit content"
   )
   @ApiOperation(
      value = "Update the content details",
      notes = "Update details of specified content. \r\ncontentName, shareFlag, groupID, versionID, content_type, strmAddress, expirationDate are able to be updated.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "Id value of content",
   required = true,
   dataType = "string"
), @ApiImplicitParam(
   name = "resource",
   value = "Model object which include fields could be changed",
   required = true,
   dataType = "V2ContentEdit"
)})
   @ApiResponses({@ApiResponse(
   code = 404,
   message = "Data not found \n errorCode : 404001, contentId is not found."
)})
   @PutMapping(
      value = {"/{contentId}"},
      produces = {"application/json"}
   )
   public ResponseEntity updateContentDetail(@Pattern(regexp = "^[0-9A-Fa-f]{8}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{12}$",message = "Not UUID pattern.") @NotNull @NotEmpty @PathVariable("contentId") String contentId, @Valid @RequestBody V2ContentEdit resource, BindingResult result) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][updateContentDetail] Update details of content");
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         V2ContentResource contentResource = this.V2ContentService.updateContentDetail(contentId, resource);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(contentResource);
         this.logger.info("[REST_v2.0][CONTENTS][updateContentDetail] finish successfully.");
         return new ResponseEntity(responseBody, HttpStatus.OK);
      }
   }

   @KPI
   @LogProperty(
      eventType = "Add content"
   )
   @ApiOperation(
      value = "Upload file for content creation",
      notes = "Upload the content file to MagicINFO server.\r\nSupports all file types required for creation, including images, videos, and web content package files.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "files",
   value = "Target file to upload",
   paramType = "form",
   dataType = "__file",
   required = true
), @ApiImplicitParam(
   name = "groupId",
   value = "Group id to be uploaded",
   dataType = "string",
   defaultValue = "0",
   example = "0",
   required = true
), @ApiImplicitParam(
   name = "contentType",
   value = "Type of file to upload (example : IMAGE,MOVIE,OFFICE,PDF,FLASH,SOUND,HTML)",
   dataType = "string",
   allowableValues = "IMAGE,MOVIE,OFFICE,PDF,FLASH,SOUND,HTML",
   example = "HTML",
   required = true
), @ApiImplicitParam(
   name = "updatedContentId",
   value = "Content id to be updated",
   dataType = "string",
   example = "3B55D68D-AB22-4EB8-AE1A-5976057F912A",
   allowEmptyValue = true
), @ApiImplicitParam(
   name = "webContentName",
   value = "Name of web content(web content only)",
   dataType = "string",
   example = "webContentFile.zip",
   allowEmptyValue = true
), @ApiImplicitParam(
   name = "startPage",
   value = "Start page name of web content such as \"index.html\"(web content only)",
   dataType = "string",
   example = "index.html",
   allowEmptyValue = true
), @ApiImplicitParam(
   name = "refreshInterval",
   value = "Refresh interval of web content. Format is \"hh:mm:ss\"(web content only, SSSP Web App only)",
   dataType = "string",
   defaultValue = "00:01:00",
   example = "00:01:00",
   allowEmptyValue = true
), @ApiImplicitParam(
   name = "mode",
   value = "If you want to change web content package or SSSP Web App, input \"UPDATE\"(web content, SSSP Web App only)",
   dataType = "string",
   allowableValues = ",UPDATE",
   example = "UPDATE",
   allowEmptyValue = true
), @ApiImplicitParam(
   name = "categoryIds",
   value = "Set categories for files"
)})
   @ApiResponses({@ApiResponse(
   code = 400,
   message = "Bad Request \n errorCode : 400002, multipart content is invalid value.\n errorCode : 400305, The media file type does not support content upload."
), @ApiResponse(
   code = 404,
   message = "Data not found \n errorCode : 404001, CONTENTS_HOME or THUMBNAIL_HOME is invalid value.\n errorCode : 404001, server setup info is invalid value.\n errorCode : 404001, file is invalid value."
), @ApiResponse(
   code = 500,
   message = "Internal Server Error \n errorCode : 500000, Internal Server Error\n errorCode : 500013, It is failed to sssp content file upload.\n errorCode : 500013, It is failed to content file upload.\n errorCode : 500300, The SSSP Web App content creation is failed.\n errorCode : 500300, The WEB content creation is failed.\n errorCode : 500301, An error occurred while creating the thumbnail file.\n errorCode : 500305, Thumbnail file exists but cannot be load."
)})
   @PostMapping(
      value = {"/files"},
      consumes = {"multipart/form-data"},
      produces = {"application/json"}
   )
   public ResponseEntity uploadContentFile(@NotNull @RequestPart("files") MultipartFile[] files, @NotNull @NotEmpty @RequestParam("groupId") String groupId, @NotNull @NotEmpty @RequestParam("contentType") @KPI("mediaType") String contentType, @RequestParam(value = "updatedContentId",required = false) String updatedContentId, @RequestParam(value = "webContentName",required = false) String webContentName, @RequestParam(value = "startPage",required = false) String startPage, @RequestParam(value = "refreshInterval",required = false) String refreshInterval, @RequestParam(value = "mode",required = false) String mode, @RequestParam(value = "categoryIds",required = false) List categoryIds, HttpServletRequest request) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][uploadContentFile] upload file to create content");
      V2ContentFileResource resource = this.V2ContentService.uploadContentFile(files, StrUtils.nvl(groupId), StrUtils.nvl(updatedContentId), StrUtils.nvl(contentType), StrUtils.nvl(webContentName), StrUtils.nvl(startPage), StrUtils.nvl(refreshInterval), StrUtils.nvl(mode), categoryIds, request);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      this.logger.info("[REST_v2.0][CONTENTS][uploadContentFile] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Create new template file with LFD content",
      notes = "Create new template file using already existing LFD content",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "Content ID. Refer to POST .../contents/filter and \"contentType\", \"contentTypes\".",
   dataType = "string",
   required = true
), @ApiImplicitParam(
   name = "groupId",
   value = "Id of target group that created template would be located.",
   dataType = "string",
   defaultValue = "0",
   example = "0",
   required = true
), @ApiImplicitParam(
   name = "templateName",
   value = "Name of template which is newly created.",
   dataType = "string",
   example = "webContentFile.zip"
)})
   @ApiResponses({@ApiResponse(
   code = 400,
   message = "Bad Request \n errorCode : 400316, Can not find matched group with groupId: groupId."
), @ApiResponse(
   code = 404,
   message = "Data not found \n errorCode : 404001, The contentId is not found."
)})
   @PostMapping(
      value = {"/{contentId}/templates"},
      produces = {"application/json"}
   )
   public ResponseEntity convertTemplate(@PathVariable String contentId, @RequestParam(value = "groupId",required = false) String groupId, @RequestParam(value = "templateName",required = false) String templateName) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][convertTemplate][" + contentId + "]");
      V2ContentResource resource = this.V2ContentService.convertTemplate(contentId, groupId, templateName);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      this.logger.info("[REST][DASHBOARD][uploadContentFile] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @LogProperty(
      eventType = "Delete content"
   )
   @ApiOperation(
      value = "Delete contents in several ways",
      notes = "Integrate multiple delete functions.\r\nYou can delete one or multiple contents.\r\nAvailable deleteMethod values are 'GO_TO_RECYCLEBIN', 'GO_TO_RECYCLEBIN_FORCE', 'DELETE_FORCE'.\r\nSee 'Models' at the bottom of Swagger\r\n'successList' of the response data is List&#60;String&#62; type and this value consists of contentIds that was processed successfully.\r\n'failList' of the response data is List&#60;V2ContentDeleteFail&#62; type and this value consists of fail information for the contentId that was not processed.\r\n",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "resource",
   value = "Resource for contents deletion.",
   dataType = "V2ContentDeleteParam"
)})
   @ApiResponses({@ApiResponse(
   code = 500,
   message = "Internal Server Error \n errorCode : 500001, Unknown error occurred.\n errorCode : 500304, This content cannot be managed and cannot be deleted."
)})
   @PostMapping(
      value = {"/deleted-contents"},
      produces = {"application/json"}
   )
   public ResponseEntity deletionContents(@Valid @RequestBody V2ContentDeleteParam resource) throws Exception {
      V2CommonResultResource result = new V2CommonResultResource();
      ResponseBody responseBody = new ResponseBody();
      HttpStatus status = HttpStatus.OK;
      responseBody.setStatus("Success");
      if ("GO_TO_RECYCLEBIN".equalsIgnoreCase(resource.getDeleteMethod())) {
         result = this.V2ContentService.gotoRecyclebin(resource);
      } else if ("GO_TO_RECYCLEBIN_FORCE".equalsIgnoreCase(resource.getDeleteMethod())) {
         result = this.V2ContentService.forceGotoRecyclebin(resource);
      } else if ("DELETE_FORCE".equalsIgnoreCase(resource.getDeleteMethod())) {
         result = this.V2ContentService.permanentlyDeletePlaylist(resource);
      }

      if (result.getFailList() != null && result.getFailList().size() > 0) {
         responseBody.setStatus("Fail");
         status = HttpStatus.BAD_REQUEST;
      }

      responseBody.setItems(result);
      responseBody.setTotalCount(resource.getContentIds().size());
      responseBody.setApiVersion("2.0");
      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Assign tags to content",
      notes = "Assign tags and tag conditions to specified content.\r\nSee 'Models' at the bottom of Swagger\r\n'successList' of the response data is List&#60;V2ContentResource&#62; type and this value consists of resources for the contentId that was processed successfully.\r\n'failList' of the response data is List&#60;String&#62; type and this value consists of contentIds that were not processed.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "body",
   value = "Resource for assigned tags edit.",
   dataType = "V2ContentTagAssignment"
)})
   @PutMapping(
      value = {"/tags"},
      produces = {"application/json"}
   )
   public ResponseEntity assignTagToContent(@Valid @RequestBody V2ContentTagAssignment body, BindingResult result) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][assignTagToContent] Assign tag and tag conditions to specified content");
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         HttpStatus status = HttpStatus.OK;
         V2CommonResultResource resource = this.V2ContentService.assignTagToContent(body);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
         if (resource.getFailList() != null && resource.getFailList().size() > 0) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            responseBody.setStatus("Fail");
         }

         return new ResponseEntity(responseBody, status);
      }
   }

   @ApiOperation(
      value = "Share content in a shared folder",
      notes = "Shares multiple contents in MY CONTENTS groups to the specified SHARED CONTENTS group.\r\nSee 'Models' at the bottom of Swagger\r\n'successList' of the response data is List&#60;V2ContentResource&#62; type and this value consists of resources for the contentId that was processed successfully.\r\n'failList' of the response data is List&#60;String&#62; type and this value consists of contentIds that were not processed.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "body",
   value = "Model object which include contentId list to be shared",
   required = true,
   dataType = "V2ContentShare"
)})
   @PostMapping(
      value = {"/groups/shared-contents"},
      produces = {"application/json"}
   )
   public ResponseEntity shareContents(@Valid @RequestBody V2ContentShare body, BindingResult result) throws Exception {
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         this.logger.info("[REST_v2.0][CONTENTS][shareContents]");
         V2CommonResultResource sharing = this.V2ContentService.shareContents(body);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(sharing);
         HttpStatus status = HttpStatus.OK;
         if (sharing.getFailList() != null && sharing.getFailList().size() > 0) {
            status = HttpStatus.BAD_REQUEST;
            responseBody.setStatus("Fail");
         }

         responseBody.setTotalCount(body.getContentIds().size());
         this.logger.info("[REST_v2.0][CONTENTS][shareContents] finish successfully.");
         return new ResponseEntity(responseBody, status);
      }
   }

   @ApiOperation(
      value = "Release sharing of shared contents",
      notes = "Release sharing of shared contents.\r\nSee 'Models' at the bottom of Swagger\r\n'successList' of the response data is List&#60;V2ContentResource&#62; type and this value consists of resources for the contentId that was processed successfully.\r\n'failList' of the response data is List&#60;String&#62; type and this value consists of contentIds that were not processed.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "body",
   value = "Model object which include contentId list to be released shared status.",
   required = true,
   dataType = "V2ContentShare"
)})
   @PostMapping(
      value = {"/unshared-contents"},
      produces = {"application/json"}
   )
   public ResponseEntity releaseSharing(@Valid @RequestBody V2ContentShare body, BindingResult result) throws Exception {
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         this.logger.info("[REST_v2.0][CONTENTS][releaseSharing]");
         V2CommonResultResource sharing = this.V2ContentService.releaseSharing(body);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(sharing);
         HttpStatus status = HttpStatus.OK;
         if (sharing.getFailList() != null && sharing.getFailList().size() > 0) {
            status = HttpStatus.BAD_REQUEST;
            responseBody.setStatus("Fail");
         }

         responseBody.setTotalCount(body.getContentIds().size());
         this.logger.info("[REST_v2.0][CONTENTS][releaseSharing] finish successfully.");
         return new ResponseEntity(responseBody, status);
      }
   }

   @ApiOperation(
      value = "Check authority for contents to move another group",
      notes = "Check authority for contents to move another group.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "body",
   value = "V2ContentIds including contentId list to move another group.",
   required = true,
   dataType = "V2ContentIds"
)})
   @PostMapping(
      value = {"/groups/authority-check"},
      produces = {"application/json"}
   )
   public ResponseEntity checkAuthorityToMoveContent(@Valid @RequestBody V2ContentIds body, BindingResult result) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][checkAuthorityToMoveContent] authority-check");
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         List list = this.V2ContentService.checkAuthorityToMoveContent(body);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(list);
         responseBody.setTotalCount(list.size());
         this.logger.info("[REST_v2.0][CONTENTS][checkAuthorityToMoveContent] finish successfully.");
         return new ResponseEntity(responseBody, HttpStatus.OK);
      }
   }

   @ApiOperation(
      value = "Download thumbnail image of specified content",
      notes = "Download thumbnail image of specified content.\r\nRefer to detail information of content to fill input parameters.\r\nIf can't find thumbnail file for thumbnailId, thumbnail of no image will be returned.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "thumbnailId",
   value = "Id of thumbnail or MOVIE_THUMBNAIL, OFFICE_THUMBNAIL, FLASH_THUMBNAIL, PDF_THUMBNAIL, SOUND_THUMBNAIL, ETC_THUMBNAIL, NOIMAGE_THUMBNAIL, STRM_THUMBNAIL, URL_THUMBNAIL, HTML_THUMBNAIL, SAPP_THUMBNAIL, FTP_THUMBNAIL, CIFS_THUMBNAIL, DLK_THUMBNAIL, MEDIASLIDE_THUMBNAIL are allowable. To download custom logo image, fill CUSTOM_LOGO as a thumbnailId and logoFilePath. Fill CAPTURE as a thumbnailId to get current captured image of device.",
   required = true,
   dataType = "string"
), @ApiImplicitParam(
   name = "width",
   value = "Width of thumbnail image.",
   dataType = "int",
   defaultValue = "100"
), @ApiImplicitParam(
   name = "height",
   value = "Height of thumbnail image.",
   dataType = "int",
   defaultValue = "56"
), @ApiImplicitParam(
   name = "resolution",
   value = "Resolution of thumbnail image.",
   dataType = "string",
   defaultValue = "MEDIUM",
   allowableValues = "SMALL,MEDIUM,HD,ORIGIN"
), @ApiImplicitParam(
   name = "logoFilePath",
   value = "filePath including file name of custom logo image.",
   dataType = "string"
), @ApiImplicitParam(
   name = "capturedFileName",
   value = "fileName composed with mac address of device to get captured image.",
   dataType = "string"
), @ApiImplicitParam(
   name = "lastLoginDate",
   value = "Needed for set response' cache Header(Expires), If null, will not caching on browser side.",
   dataType = "string"
)})
   @ApiResponses({@ApiResponse(
   code = 400,
   message = "Bad Request \n errorCode : 400020, The logoFilePath value cannot be null or empty."
), @ApiResponse(
   code = 403,
   message = "Forbidden \n errorCode : 403001, You are not allowed permission.\n errorCode : 403003, You do not have content read permission."
), @ApiResponse(
   code = 404,
   message = "Data not found \n errorCode : 404001, The thumbnailId is not found.\n errorCode : 404001, The CUSTOM_LOGO_HOME is not found.\n errorCode : 404001, The THUMBNAIL_HOME is not found.\n errorCode : 404001, The file path is not found."
), @ApiResponse(
   code = 500,
   message = "Internal Server Error \n errorCode : 500001, Unknown error occurred.\n errorCode : 500304, This content cannot be managed and cannot be deleted."
)})
   @GetMapping(
      value = {"/thumbnails/{thumbnailId}"},
      produces = {"application/json"}
   )
   public ResponseEntity downloadThumbnail(@PathVariable("thumbnailId") String thumbnailId, @RequestParam(value = "width",required = false) Integer width, @RequestParam(value = "height",required = false) Integer height, @RequestParam(value = "resolution",required = false,defaultValue = "MEDIUM") String resolution, @RequestParam(value = "logoFilePath",required = false) String logoFilePath, @RequestParam(value = "capturedFileName",required = false) String capturedFileName, @RequestParam(value = "lastLoginDate",required = false) String lastLoginDate) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][downloadThumbnail] download thumbnail image");
      V2ContentThumbnailResource resource = this.V2ContentService.downloadThumbnail(thumbnailId, width, height, resolution, logoFilePath, capturedFileName);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @LogProperty(
      eventType = "Delete content permanently"
   )
   @ApiOperation(
      value = "Empty the recycle-bin",
      notes = "Delete all contents in recycle-bin.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({})
   @ApiResponses({@ApiResponse(
   code = 400,
   message = "Bad Request \n errorCode : 400012, The recycle bin is already empty."
)})
   @DeleteMapping(
      value = {"/recycle-bin"},
      produces = {"application/json"}
   )
   public ResponseEntity deleteAll() throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][deleteMethod] move recycle content");
      V2CommonResultResource result = this.V2ContentService.deleteAll();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Get each version of content",
      notes = "Get version information including thumbnail information for each content version.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "Id value of content",
   required = true,
   dataType = "string"
), @ApiImplicitParam(
   name = "versionId",
   value = "Version information to check.",
   required = true,
   dataType = "Long"
)})
   @ApiResponses({@ApiResponse(
   code = 400,
   message = "Bad Request \n errorCode : 400002, version id is invalid value."
), @ApiResponse(
   code = 404,
   message = "Data not found \n errorCode : 404001, The content id and version id is not found."
)})
   @GetMapping(
      value = {"/{contentId}/versions/{versionId}"},
      produces = {"application/json"}
   )
   public ResponseEntity getContentVersion(@PathVariable @NotNull @NotEmpty @Pattern(regexp = "^[0-9A-Fa-f]{8}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{12}$",message = "Not UUID pattern.") String contentId, @PathVariable @NotNull @Min(value = 1L,message = "Minimum value of versionId is 1") Long versionId) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][getContentVersion] Update version of content");
      V2ContentVersion contentResource = this.V2ContentService.getContentVersion(contentId, versionId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(contentResource);
      this.logger.info("[REST_v2.0][CONTENTS][getContentVersion] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Check the availability of various contents",
      notes = "Check the authority and return the availability result before applying the content to the playlist.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "body",
   value = "Specific contents IDs.",
   dataType = "V2ContentIds"
)})
   @ApiResponses({@ApiResponse(
   code = 400,
   message = "Bad Request \n errorCode : 400002, contentIds is invalid value."
)})
   @PostMapping(
      value = {"/availability"},
      produces = {"application/json"}
   )
   public ResponseEntity contentIdCheck(@Valid @NotNull @RequestBody V2ContentIds body) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][contentIdCheck] Check authority of content");
      V2ContentCheckResource contentResource = this.V2ContentService.contentIdCheck(body);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(contentResource);
      this.logger.info("[REST_v2.0][CONTENTS][contentIdCheck] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Download content resources",
      notes = "Download resources of the specified content.\r\nDownload in the format of each content type.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentIds",
   value = "Specific content IDs.",
   dataType = "V2ContentIds"
)})
   @ApiResponses({@ApiResponse(
   code = 400,
   message = "Bad Request \n errorCode : 400002, contentIds is invalid value."
)})
   @PostMapping(
      value = {"/download"},
      produces = {"application/json"}
   )
   public ResponseEntity contentDownload(@Valid @NotNull @RequestBody V2ContentIds contentIds, HttpServletRequest request, HttpServletResponse response) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][contentDownload] This function file downloads of content");
      ResponseBody responseBody = new ResponseBody();
      this.V2ContentService.contentDownload(contentIds, request, response);
      responseBody.setApiVersion("2.0");
      responseBody.setStatus("Success");
      this.logger.info("[REST_v2.0][CONTENTS][contentDownload] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Transfer user information to Web Author",
      notes = "transfer user information by external link. \r\n(web-author, content(*exclude image file) edit).\r\nSend User ID and token authentication information.",
      authorizations = {@Authorization("api_key")}
   )
   @GetMapping(
      value = {"/web-author/authorized-user"},
      produces = {"application/json"}
   )
   public ResponseEntity contentWebAuthor() throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][contentWebAuthor] Web Author user infomation");
      V2ContentWebAuthorResource resource = this.V2ContentService.contentWebAuthor();
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      this.logger.info("[REST_v2.0][CONTENTS][contentWebAuthor] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Export content list using filters",
      notes = "Download a list of content as an Excel or PDF file based on a filter. \r\nTo include only the values you want, set only the tables you need as a filter.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "exportType",
   value = "Export file type",
   required = true,
   dataType = "string",
   allowableValues = "EXCEL,PDF"
), @ApiImplicitParam(
   name = "filter",
   value = "Export filter condition",
   required = true,
   dataType = "V2ContentListFilter"
), @ApiImplicitParam(
   name = "locale",
   value = "Locale information",
   required = false,
   dataType = "string",
   allowableValues = "de,en,es,fr,it,ja,ko,pt,ru,sv,tr,ar,fa,pl,vi,zh_cn,zh_tw"
)})
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Download completed successfully.\nThe model below is not related to response.\n(When download is complete, 'Code: 200' is transmitted without a response model)"
), @ApiResponse(
   code = 400,
   message = "Bad Request \n errorCode : 400002, groupId is required value.\n errorCode : 400002, sizeFilter is required value."
)})
   @PostMapping(
      value = {"/filter/export"},
      produces = {"application/json"}
   )
   public ModelAndView contentExport(@Valid @RequestBody V2ContentListFilter filter, @RequestParam(value = "exportType",required = true,defaultValue = "EXCEL") String exportType, @RequestParam(value = "locale",required = false) String locale, HttpServletResponse response) throws Exception {
      this.logger.info("[REST_v2.0][Device][contentExport] Get export list of content by filter");
      ModelAndView result = this.V2ContentService.contentExport(filter, exportType, response, locale);
      return result;
   }

   @ApiOperation(
      value = "Get contents information by file ID",
      notes = "Of the uploaded content, the original file get the same contents",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "fileId",
   value = "ID of specific file.",
   dataType = "String",
   example = "7153036D-6612-4956-B2CD-42D483C36307"
)})
   @GetMapping(
      value = {"/files/{fileId}/contents"},
      produces = {"application/json"}
   )
   public ResponseEntity getContentsByFileId(@PathVariable("fileId") @Pattern(regexp = "^[0-9A-Fa-f]{8}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{12}$",message = "Not UUID pattern.") @NotEmpty @NotNull String fileId) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][getContentsByFileId] get content list by file id.");
      V2PageResource pageResource = this.V2ContentService.getContentsByFileId(fileId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(pageResource.getList(), pageResource.getStartIndex(), pageResource.getPageSize(), pageResource.getRecordsTotal());
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Edit expire date to contents",
      notes = "Edit expire date one or multiple contents.\r\nSee 'Models' at the bottom of Swagger\r\n'successList' of the response data is List&#60;V2ContentResource&#62; type and this value consists of resources for the contentId that was processed successfully.\r\n'failList' of the response data is List&#60;String&#62; type and this value consists of contentIds that were not processed.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "body",
   value = "Resource for expired-date of contents edit",
   dataType = "V2ContentExpireDate"
)})
   @PutMapping(
      value = {"/expiration-date"},
      produces = {"application/json"}
   )
   public ResponseEntity expireDateToContent(@Valid @RequestBody V2ContentExpireDate body) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][expireDateToContent] expire date to specified content");
      HttpStatus status = HttpStatus.OK;
      V2CommonResultResource resource = this.V2ContentService.expireDateToContent(body);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(resource);
      if (resource.getFailList() != null && resource.getFailList().size() > 0) {
         status = HttpStatus.INTERNAL_SERVER_ERROR;
         responseBody.setStatus("Fail");
      }

      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Get a list of available playlists",
      notes = "Get a list of playlists to which you can add the specified contents.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "body",
   value = "Resource for check contents list.",
   dataType = "V2PlaylistAddData"
)})
   @ApiResponses({@ApiResponse(
   code = 400,
   message = "Bad Request \n errorCode : 400002, contentIds is invalid value."
)})
   @PostMapping(
      value = {"/playlists/available-playlists"},
      produces = {"application/json"}
   )
   public ResponseEntity playListAddContent(@Valid @RequestBody V2PlaylistAddData body) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][playListAddContent] add content to playlist");
      V2PlaylistAddResource contentResource = this.V2ContentService.playListAddContent(body);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(contentResource);
      this.logger.info("[REST_v2.0][CONTENTS][playListAddContent] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Release sharing of shared contents",
      notes = "Release sharing of shared contents.\r\nSee 'Models' at the bottom of Swagger\r\n'successList' of the response data is List&#60;V2ContentResource&#62; type and this value consists of resources for the contentId that was processed successfully.\r\n'failList' of the response data is List&#60;String&#62; type and this value consists of contentIds that were not processed.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "body",
   value = "Model object which include contentId list to be released shared status.",
   required = true,
   dataType = "V2ContentShare"
)})
   @PostMapping(
      value = {"/groups/unshared-contents"},
      produces = {"application/json"}
   )
   public ResponseEntity releaseSharingContents(@Valid @RequestBody V2ContentShare body, BindingResult result) throws Exception {
      if (result.hasErrors()) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_REQUESTBODY_INVALID);
      } else {
         this.logger.info("[REST_v2.0][CONTENTS][releaseSharing]");
         V2CommonResultResource sharing = this.V2ContentService.releaseSharing(body);
         ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(sharing);
         HttpStatus status = HttpStatus.OK;
         if (sharing.getFailList() != null && sharing.getFailList().size() > 0) {
            status = HttpStatus.BAD_REQUEST;
            responseBody.setStatus("Fail");
         }

         responseBody.setTotalCount(body.getContentIds().size());
         this.logger.info("[REST_v2.0][CONTENTS][releaseSharing] finish successfully.");
         return new ResponseEntity(responseBody, status);
      }
   }

   @ApiOperation(
      value = "Cancel content upload",
      notes = "If you want to cancel the content upload, the content id that is not currently available will not be uploaded.\r\nMulti id can be set, and the appropriate contents id can be added. If the upload is successful, it will be included in the success list and if it is cancelled, it will be included in the cancellation list.\r\n'successList' of the response data is List&#60;String&#62; type and this value consists of contentIds that was processed successfully.\r\n'failList' of the response data is List&#60;String&#62; type and this value consists of contentIds that were not processed.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "resource",
   value = "Specific contents IDs.",
   dataType = "V2CommonIds",
   required = true
)})
   @PostMapping(
      value = {"/cancel-upload-contents"},
      produces = {"application/json"}
   )
   public ResponseEntity completeUploadContents(@NotNull @Valid @RequestBody V2CommonIds resource) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][completeUploadContents]");
      V2CommonResultResource result = this.V2ContentService.completeUploadContents(resource);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      HttpStatus status = HttpStatus.OK;
      if (result.getFailList() != null && result.getFailList().size() > 0) {
         status = HttpStatus.BAD_REQUEST;
         responseBody.setStatus("Fail");
      }

      responseBody.setTotalCount(resource.getIds().size());
      this.logger.info("[REST_v2.0][CONTENTS][releaseSharing] finish successfully.");
      return new ResponseEntity(responseBody, status);
   }

   @ApiOperation(
      value = "Get Advertisement",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "Enter a Content ID.",
   required = true,
   dataType = "string"
)})
   @GetMapping(
      value = {"/{contentId}/advertisement"},
      produces = {"application/json"}
   )
   public ResponseEntity getAdvertisement(@PathVariable("contentId") @NotEmpty @NotNull String contentId) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][getAdvertisement] Get Advertisement");
      V2ContentAdvertisementResource result = this.V2ContentService.getAdvertisement(contentId);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      this.logger.info("[REST_v2.0][CONTENTS][getAdvertisement] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Set Advertisement",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "contentId",
   value = "Enter a Content ID.",
   required = true,
   dataType = "string"
)})
   @PutMapping(
      value = {"/{contentId}/advertisement"},
      produces = {"application/json"}
   )
   public ResponseEntity setAdvertisement(@PathVariable("contentId") @NotEmpty @NotNull String contentId, @RequestBody V2ContentAdvertisementEditResource resource) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][setAdvertisement] Set Advertisement");
      resource.setContentId(contentId);
      V2CommonResultResource result = this.V2ContentService.setAdvertisement(resource);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      this.logger.info("[REST_v2.0][CONTENTS][setAdvertisement] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }

   @ApiOperation(
      value = "Set Advertisement Multi",
      authorizations = {@Authorization("api_key")}
   )
   @PostMapping(
      value = {"/advertisements"},
      produces = {"application/json"}
   )
   public ResponseEntity setAdvertisementMulti(@RequestBody V2ContentAdvertisementMultiEditResource resource) throws Exception {
      this.logger.info("[REST_v2.0][CONTENTS][setAdvertisementMulti] Set Advertisement");
      V2CommonResultResource result = this.V2ContentService.setAdvertisementMulti(resource);
      ResponseBody responseBody = RestAPIUtil.successResponseBodyBuilder(result);
      this.logger.info("[REST_v2.0][CONTENTS][setAdvertisementMulti] finish successfully.");
      return new ResponseEntity(responseBody, HttpStatus.OK);
   }
}
