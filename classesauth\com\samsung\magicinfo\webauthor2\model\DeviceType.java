package com.samsung.magicinfo.webauthor2.model;

public enum DeviceType {
  iPLAYER, S10PLAYER, S9<PERSON><PERSON>YER, S8PLAYER, S7PLAYER, S6PLAYER, S5<PERSON>AYER, S4<PERSON>AYER, S3<PERSON>AYER, S2<PERSON>AYER, SP<PERSON><PERSON><PERSON>, <PERSON><PERSON>A<PERSON><PERSON>, ALL;
  
  public String getPlayerType() {
    switch (this) {
      case iPLAYER:
        return iPLAYER.name();
      case WPLAYER:
        return WPLAYER.name();
    } 
    return SPLAYER.name();
  }
  
  public static String getCompatiblePlayerType(String playerType) {
    if (playerType == null)
      return ""; 
    switch (playerType.toLowerCase()) {
      case "iplayer":
        return iPLAYER.name();
      case "splayer":
        return SPLAYER.name();
      case "ledbox":
        return SPLAYER.name();
      case "signage":
        return SPLAYER.name();
      case "rsplayer":
        return SPLAYER.name();
      case "riplayer":
        return iPLAYER.name();
      case "sig_child":
        return SPLAYER.name();
    } 
    return playerType;
  }
  
  public String getPlayerVersion() {
    switch (this) {
      case iPLAYER:
        return "1.0";
      case S10PLAYER:
        return "10.0";
      case S9PLAYER:
        return "9.0";
      case S8PLAYER:
        return "8.0";
      case S7PLAYER:
        return "7.0";
      case S6PLAYER:
        return "6.0";
      case S5PLAYER:
        return "5.0";
      case S4PLAYER:
        return "4.0";
      case S3PLAYER:
        return "3.0";
      case S2PLAYER:
        return "2.0";
      case SPLAYER:
        return "1.0";
      case WPLAYER:
        return "1.0";
    } 
    return "1.0";
  }
}
