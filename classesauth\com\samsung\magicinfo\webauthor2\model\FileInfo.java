package com.samsung.magicinfo.webauthor2.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.samsung.magicinfo.webauthor2.util.FileHashUtil;
import java.io.File;

public class FileInfo {
  @JsonProperty
  private final String fileId;
  
  private final String fileLocalPath;
  
  private final String fileName;
  
  private final String fileHash;
  
  private final long size;
  
  public FileInfo(String fileId, String fileLocalPath, String fileName, String fileHash, long size) {
    this.fileLocalPath = fileLocalPath;
    this.fileName = fileName;
    this.fileHash = fileHash;
    this.size = size;
    this.fileId = fileId;
  }
  
  public static FileInfo fromFile(String fileId, File file) {
    String fileHash = FileHashUtil.getHash(file);
    return new FileInfo(fileId, file.getPath(), file.getName(), fileHash, file.length());
  }
  
  public String getFileName() {
    return this.fileName;
  }
  
  public String getFileHash() {
    return this.fileHash;
  }
  
  public long getSize() {
    return this.size;
  }
  
  @JsonIgnore
  public String getFileId() {
    return this.fileId;
  }
  
  public String getFileLocalPath() {
    return this.fileLocalPath;
  }
}
