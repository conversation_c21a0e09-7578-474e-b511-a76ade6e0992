package com.samsung.magicinfo.webauthor2.model.datalink;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public class Element {
  private final String name;
  
  private final ElementType elementType;
  
  private final long changeDuration;
  
  private final String positionX;
  
  private final String positionY;
  
  private final String width;
  
  private final String height;
  
  private final boolean keepLastValue;
  
  private final List<DLKData> dataList;
  
  @JsonCreator
  public Element(@JsonProperty("name") String name, @JsonProperty("elementType") ElementType elementType, @JsonProperty("changeDuration") long changeDuration, @JsonProperty("positionX") String positionX, @JsonProperty("positionY") String positionY, @JsonProperty("width") String width, @JsonProperty("height") String height, @JsonProperty("keepLastValue") boolean keepLastValue, @JsonProperty("dataList") List<DLKData> dataList) {
    this.name = name;
    this.elementType = elementType;
    this.changeDuration = changeDuration;
    this.positionX = positionX;
    this.positionY = positionY;
    this.width = width;
    this.height = height;
    this.keepLastValue = keepLastValue;
    this.dataList = dataList;
  }
  
  public String getName() {
    return this.name;
  }
  
  public ElementType getElementType() {
    return this.elementType;
  }
  
  public long getChangeDuration() {
    return this.changeDuration;
  }
  
  public String getPositionX() {
    return this.positionX;
  }
  
  public String getPositionY() {
    return this.positionY;
  }
  
  public String getWidth() {
    return this.width;
  }
  
  public String getHeight() {
    return this.height;
  }
  
  public boolean isKeepLastValue() {
    return this.keepLastValue;
  }
  
  public List<DLKData> getDataList() {
    return this.dataList;
  }
}
