package com.samsung.magicinfo.framework.scheduler.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SequenceDB;
import com.samsung.magicinfo.framework.scheduler.entity.MessageGroup;
import com.samsung.magicinfo.protocol.exception.ActionNotSupportedException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;
import org.springframework.util.StringUtils;

public class MessageGroupDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(MessageGroupDao.class);

   public MessageGroupDao() {
      super();
   }

   public MessageGroupDao(SqlSession session) {
      super(session);
   }

   public int addGroup(MessageGroup mesgGroup) throws SQLException, ActionNotSupportedException {
      if (mesgGroup.getP_group_id() >= -1L && mesgGroup.getP_group_id() != 999999L) {
         SqlSession session = this.openNewSession(false);

         int var5;
         try {
            int groupId = this.createNewGroupId();
            if (groupId == -1) {
               session.rollback();
               byte var13 = -1;
               return var13;
            }

            int updates = ((MessageGroupDaoMapper)this.getMapper(session)).addGroup(mesgGroup, groupId);
            if (updates <= 0) {
               session.rollback();
               byte var14 = -1;
               return var14;
            }

            session.commit();
            var5 = groupId;
         } catch (SQLException var11) {
            try {
               session.rollback();
            } catch (Exception var10) {
            }

            throw var11;
         } finally {
            session.close();
         }

         return var5;
      } else {
         throw new ActionNotSupportedException("MESSAGE_SCHEDULE_CANT_ADD_GROUP_SEL_LOCATION_P");
      }
   }

   public boolean delGroup(int group_id) throws SQLException {
      if (group_id <= 0) {
         this.logger.error("Block to delete schedule group of root_group_id");
         return false;
      } else {
         List groupIds = this.getChildGroupIdList(group_id, true);
         SqlSession session = this.openNewSession(false);

         boolean var16;
         try {
            MessageGroupDaoMapper mapper = (MessageGroupDaoMapper)this.getMapper(session);

            for(int i = groupIds.size() - 1; i > 0; --i) {
               Long groupId = (Long)groupIds.get(i);
               mapper.deleteMessage(groupId);
               mapper.deleteGroupMap(groupId);
               int groupsDeleted = mapper.deleteGroup(groupId);
               if (groupsDeleted == 0) {
                  session.rollback();
                  boolean var8 = false;
                  return var8;
               }
            }

            mapper.deleteMessage((long)group_id);
            mapper.deleteGroupMap((long)group_id);
            mapper.deleteGroup((long)group_id);
            session.commit();
            var16 = true;
         } catch (SQLException var14) {
            try {
               session.rollback();
            } catch (Exception var13) {
            }

            throw var14;
         } finally {
            session.close();
         }

         return var16;
      }
   }

   public List getChildMessageList(int group_id, boolean recursive) throws SQLException {
      List mesgList = ((MessageGroupDaoMapper)this.getMapper()).getChildMessageList(group_id);
      if (recursive) {
         List groupIdList = this.getChildGroupIdList(group_id, true);
         Iterator iter = groupIdList.iterator();

         while(iter.hasNext()) {
            Long group = (Long)iter.next();
            List subGroupList = this.getChildMessageList(group.intValue(), false);
            if (subGroupList != null && subGroupList.size() != 0) {
               mesgList.addAll(subGroupList);
            }
         }
      }

      return mesgList;
   }

   public List getChildMessageCount(int group_id, boolean recursive) throws SQLException {
      List retValue = ((MessageGroupDaoMapper)this.getMapper()).getChildMessageCount(group_id);
      if (recursive) {
         List groupIdList = this.getChildGroupIdList(group_id, true);
         Iterator iter = groupIdList.iterator();

         while(iter.hasNext()) {
            Long group = (Long)iter.next();
            List subGroupList = this.getChildMessageCount(group.intValue(), false);
            if (subGroupList != null && subGroupList.size() != 0) {
               retValue.addAll(subGroupList);
            }
         }
      }

      return retValue;
   }

   public List getChildGroupIdList(int group_id, boolean recursive) throws SQLException {
      List groupIdList = ((MessageGroupDaoMapper)this.getMapper()).getChildGroupIdList(999999, group_id);
      List rtList = new ArrayList();
      if (groupIdList != null) {
         for(int i = 0; i < groupIdList.size(); ++i) {
            if (recursive) {
               Long group = (Long)((Map)groupIdList.get(i)).get("group_id");
               rtList.add(group);
               List temp = this.getChildGroupIdList(group.intValue(), recursive);
               if (temp != null && temp.size() != 0) {
                  rtList.addAll(temp);
               }
            } else {
               rtList.add((Long)((Map)groupIdList.get(i)).get("group_id"));
            }
         }
      }

      return rtList;
   }

   public List getChildIdList(int group_id, boolean recursive) throws SQLException {
      List retList = ((MessageGroupDaoMapper)this.getMapper()).getChildIdList(group_id);
      if (retList != null && recursive) {
         List groupIdList = this.getChildGroupIdList(group_id, true);
         Iterator iter = groupIdList.iterator();

         while(iter.hasNext()) {
            Long group = (Long)iter.next();
            List subGroupIdList = this.getChildIdList(group.intValue(), false);
            if (subGroupIdList != null && subGroupIdList.size() != 0) {
               retList.addAll(subGroupIdList);
            }
         }
      }

      return (List)(retList != null ? retList : new ArrayList());
   }

   public List getChildGroupList(int group_id, boolean recursive) throws SQLException {
      List groupList = ((MessageGroupDaoMapper)this.getMapper()).getChildGroupList(999999, group_id);
      if (recursive && groupList != null) {
         List tmp = new ArrayList();
         Iterator iter = groupList.iterator();

         while(iter.hasNext()) {
            MessageGroup mesgGroup = (MessageGroup)iter.next();
            tmp.add(mesgGroup);
            tmp.addAll(this.getChildGroupList(mesgGroup.getGroup_id().intValue(), recursive));
         }

         return tmp;
      } else {
         return groupList;
      }
   }

   public List getChildGroupList(int group_id, boolean recursive, String deviceType) throws SQLException {
      if (!StringUtils.hasLength(deviceType)) {
         deviceType = "iPLAYER";
      }

      List groupList = ((MessageGroupDaoMapper)this.getMapper()).getChildGroupListByDevice(999999, group_id, deviceType);
      List tmp = new ArrayList();
      if (!recursive) {
         return groupList;
      } else {
         Iterator iter = groupList.iterator();

         while(iter.hasNext()) {
            MessageGroup mesgGroup = (MessageGroup)iter.next();
            tmp.add(mesgGroup);
            tmp.addAll(this.getChildGroupList(mesgGroup.getGroup_id().intValue(), recursive, deviceType));
         }

         return tmp;
      }
   }

   public MessageGroup getGroup(int group_id) throws SQLException {
      return ((MessageGroupDaoMapper)this.getMapper()).getGroup(group_id);
   }

   public List getGroupList() throws SQLException {
      return ((MessageGroupDaoMapper)this.getMapper()).getGroupList();
   }

   public int getParentGroupId(int group_id) throws SQLException {
      return ((MessageGroupDaoMapper)this.getMapper()).getParentGroupId(group_id);
   }

   public boolean moveGroup(int group_id, int new_parent_group_id) throws SQLException {
      return false;
   }

   public boolean moveGroup(MessageGroup mesgGroup) throws SQLException {
      if (mesgGroup.getGroup_id() <= 0L) {
         this.logger.error("Block to move schedule group of root_group_id");
         return false;
      } else {
         int updates = ((MessageGroupDaoMapper)this.getMapper()).moveGroup(mesgGroup);
         return updates > 0;
      }
   }

   public boolean setGroup(MessageGroup mesgGroup) throws SQLException {
      if (mesgGroup.getGroup_id() <= 0L) {
         this.logger.error("Block to set schedule group of root_group_id");
         return false;
      } else {
         int updates = ((MessageGroupDaoMapper)this.getMapper()).setGroup(mesgGroup);
         return updates > 0;
      }
   }

   public boolean updateGroupName(MessageGroup mesgGroup) throws SQLException {
      int updates = ((MessageGroupDaoMapper)this.getMapper()).updateGroupName(mesgGroup);
      return updates > 0;
   }

   private int createNewGroupId() throws SQLException {
      int id = false;
      int id = SequenceDB.getNextValue("MI_CDS_INFO_MESSAGE_GROUP");
      return id;
   }

   public int getMessageOrgGroupId(int groupId) throws SQLException {
      Map result = ((MessageGroupDaoMapper)this.getMapper()).getMessageOrgGroupId(groupId);
      long newParentGroupId = (Long)result.get("P_GROUP_ID");
      return newParentGroupId != 0L ? this.getMessageOrgGroupId((int)newParentGroupId) : ((Long)result.get("GROUP_ID")).intValue();
   }

   public int getMessageGroupForUser(String strOrg) throws SQLException {
      Long result = ((MessageGroupDaoMapper)this.getMapper()).getMessageGroupForUser(strOrg);
      return result != null ? result.intValue() : -1;
   }

   public int getMessageGroupForOrg(String strOrg) throws SQLException {
      Long result = ((MessageGroupDaoMapper)this.getMapper()).getMessageGroupForOrg(strOrg);
      return result != null ? result.intValue() : -1;
   }

   public boolean addGroupForOrg(String strOrgName) throws SQLException {
      int groupId = this.createNewGroupId();
      int groupIdBase = this.createNewGroupId();
      if (groupId != -1 && groupIdBase != -1) {
         SqlSession session = this.openNewSession(false);

         boolean var8;
         try {
            MessageGroupDaoMapper mapper = (MessageGroupDaoMapper)this.getMapper(session);
            int groupUpdates = mapper.addGroupForOrg(groupId, strOrgName);
            int groupBaseUpdates = mapper.addGroupForOrgBase(groupId, groupIdBase, "default");
            if (groupUpdates > 0 && groupBaseUpdates > 0) {
               session.commit();
               var8 = true;
               return var8;
            }

            session.rollback();
            var8 = false;
         } catch (SQLException var14) {
            try {
               session.rollback();
            } catch (Exception var13) {
            }

            throw var14;
         } finally {
            session.close();
         }

         return var8;
      } else {
         return false;
      }
   }

   public boolean canDeleteOrgGroups(String strOrg) throws SQLException {
      int iOrgRootGroup = this.getMessageGroupForUser(strOrg);
      boolean result = false;
      List children = this.getChildMessageList(iOrgRootGroup, true);
      if (children != null && children.size() != 0) {
         if (children.size() > 0) {
            result = false;
         }
      } else {
         result = true;
      }

      return result;
   }

   public boolean deleteOrgGroups(String strOrg) throws SQLException {
      int orgGroupId = this.getMessageGroupForOrg(strOrg);
      if (orgGroupId <= 0) {
         return false;
      } else {
         List groupList = this.getChildGroupIdList(orgGroupId, true);
         SqlSession session = this.openNewSession(false);

         boolean var17;
         try {
            MessageGroupDaoMapper mapper = (MessageGroupDaoMapper)this.getMapper(session);

            int updates;
            for(int i = groupList.size() - 1; i > 0; --i) {
               Long groupId = (Long)groupList.get(i);
               mapper.deleteMessage(groupId);
               mapper.deleteGroupMap(groupId);
               updates = mapper.deleteGroup(groupId);
               if (updates <= 0) {
                  session.rollback();
                  boolean var9 = false;
                  return var9;
               }
            }

            updates = mapper.deleteGroup((long)orgGroupId);
            if (updates <= 0) {
               session.rollback();
               var17 = false;
               return var17;
            }

            session.commit();
            var17 = true;
         } catch (SQLException var15) {
            try {
               session.rollback();
            } catch (Exception var14) {
            }

            throw var15;
         } finally {
            session.close();
         }

         return var17;
      }
   }

   public boolean setOrgName(String originName, String newName) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var5;
      try {
         int updates = ((MessageGroupDaoMapper)this.getMapper(session)).setOrgName(originName, newName);
         if (updates == 1) {
            session.commit();
            var5 = true;
            return var5;
         }

         session.rollback();
         var5 = false;
      } catch (Exception var9) {
         session.rollback();
         var5 = false;
         return var5;
      } finally {
         session.close();
      }

      return var5;
   }

   public List getDeviceGroupMappedInMessageByGroupId(long g_id) throws SQLException {
      return ((MessageGroupDaoMapper)this.getMapper()).getDeviceGroupMappedInMessageByGroupId(g_id);
   }

   public boolean mapDeviceGroupWithDefault(String defaultProgramId, String pid) throws SQLException {
      int updates = ((MessageGroupDaoMapper)this.getMapper()).mapDeviceGroupWithDefault(defaultProgramId, pid);
      return updates > 0;
   }

   public boolean updateGroupTypeByMessageId(String deviceType, String messageId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var5;
      try {
         int updates = ((MessageGroupDaoMapper)this.getMapper(session)).updateGroupTypeByMessageId(deviceType, messageId);
         if (updates != 1) {
            session.rollback();
            var5 = false;
            return var5;
         }

         session.commit();
         var5 = true;
         return var5;
      } catch (Exception var9) {
         session.rollback();
         var5 = false;
      } finally {
         session.close();
      }

      return var5;
   }

   public String getMessageGroupRoot(int g_id) throws SQLException {
      Map info = ((MessageGroupDaoMapper)this.getMapper()).getMessageGroupRoot(g_id);
      int newGroupParentId = ((Long)info.get("P_GROUP_ID")).intValue();
      return newGroupParentId > 0 ? this.getMessageGroupRoot(newGroupParentId) : (String)info.get("GROUP_NAME");
   }

   public String getProgramOrgNameByGroupId(long groupId) throws SQLException {
      Map groupMap = ((MessageGroupDaoMapper)this.getMapper()).getProgramOrgNameByGroupId(groupId);
      if (groupMap == null) {
         return null;
      } else {
         return (Long)groupMap.get("group_depth") <= 1L ? (String)groupMap.get("group_name") : this.getProgramOrgNameByGroupId((Long)groupMap.get("p_group_id"));
      }
   }

   public List getRootGroupById(String organization) throws SQLException {
      return ((MessageGroupDaoMapper)this.getMapper()).getRootGroupById(organization);
   }

   public List getMessageGroupIdByOrganization(int groupId) throws SQLException {
      Object rtn = new ArrayList();

      try {
         rtn = ((MessageGroupDaoMapper)this.getMapper()).getGroupIdByOrganizationGroupIdWithRecursive(groupId);
      } catch (Exception var7) {
         this.logger.info("[MagicInfo_ProgramGroup] not support with query");
         List list = this.getChildGroupList(groupId, true);
         if (list != null && list.size() > 0) {
            rtn = new ArrayList();
            Iterator var5 = list.iterator();

            while(var5.hasNext()) {
               MessageGroup group = (MessageGroup)var5.next();
               ((List)rtn).add(group.getGroup_id());
            }
         }
      }

      return (List)rtn;
   }

   public Integer getMessageOrganizationIdByName(String groupName) throws SQLException {
      return ((MessageGroupDaoMapper)this.getMapper()).getOrganizationIdByName(groupName);
   }

   public int getMessageScheduleGroupTotalCount() throws SQLException {
      return ((MessageGroupDaoMapper)this.getMapper()).getMessageScheduleGroupTotalCount();
   }
}
