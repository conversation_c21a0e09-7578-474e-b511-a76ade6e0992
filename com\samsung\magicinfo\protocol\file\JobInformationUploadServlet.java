package com.samsung.magicinfo.protocol.file;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.exception.ExceptionCode;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.device.job.manager.JobManager;
import com.samsung.magicinfo.framework.device.job.manager.JobManagerImpl;
import java.io.File;
import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.Logger;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

public class JobInformationUploadServlet extends HttpServlet {
   private static final long serialVersionUID = -1331313960283169412L;
   private Logger logger = LoggingManagerV2.getLogger(JobInformationUploadServlet.class);

   public JobInformationUploadServlet() {
      super();
   }

   public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");
      this.doPost(request, response);
   }

   public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");

      try {
         String jobId = request.getHeader("JobID");
         String JOBS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "jobs_home";
         File homeDir = SecurityUtils.getSafeFile(JOBS_HOME);
         if (!homeDir.exists()) {
            boolean fSuccess = homeDir.mkdirs();
            if (!fSuccess) {
               this.logger.error("mkdir Fail");
            }
         }

         String userId = request.getParameter("id");
         CommonsMultipartResolver multiPartResolver = new CommonsMultipartResolver();
         MultipartHttpServletRequest multiRequest = multiPartResolver.resolveMultipart(request);
         String deviceList = multiRequest.getParameter("DeviceList");
         String jobName = multiRequest.getParameter("JobName");
         String jobXml = multiRequest.getParameter("JobXml");
         String isNew = multiRequest.getParameter("IsNEW");
         String deviceType = multiRequest.getParameter("DeviceType");
         multiPartResolver.cleanupMultipart(multiRequest);
         JobManager jobMgr = JobManagerImpl.getInstance();
         boolean rt = false;
         if (deviceType != null && deviceType.equals("DEVICE")) {
            if (isNew != null && isNew.equals("N")) {
               rt = jobMgr.editJob(jobId, userId, jobName, deviceList, jobXml);
            } else {
               rt = jobMgr.addJob(jobId, userId, jobName, deviceList, jobXml);
            }
         }

         if (!rt) {
            response.sendError(500, ExceptionCode.HTTP500[2]);
         }

         jobId = jobId.replaceAll("\n", "");
         jobId = jobId.replaceAll("\r", "");
         response.addHeader("JobID", jobId);
         response.addHeader("JobID", jobId);
      } catch (Exception var16) {
         response.sendError(600, var16.toString());
         this.logger.error(var16);
      }

   }
}
