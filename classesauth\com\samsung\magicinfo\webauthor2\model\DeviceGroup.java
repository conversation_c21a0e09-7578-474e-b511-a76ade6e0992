package com.samsung.magicinfo.webauthor2.model;

import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceGroupData;
import java.util.ArrayList;
import java.util.List;

public class DeviceGroup {
  private String id;
  
  private String parentId;
  
  private Integer depth;
  
  private String name;
  
  private String description;
  
  private String playerType;
  
  private Integer deviceCount;
  
  private Integer totalDeviceCount;
  
  private Boolean hasVideowallLayout;
  
  public String getId() {
    return this.id;
  }
  
  public void setId(String groupId) {
    this.id = groupId;
  }
  
  public String getParentId() {
    return this.parentId;
  }
  
  public void setParentId(String parentId) {
    this.parentId = parentId;
  }
  
  public Integer getDepth() {
    return this.depth;
  }
  
  public void setDepthId(Integer depth) {
    this.depth = depth;
  }
  
  public String getName() {
    return this.name;
  }
  
  public void setName(String name) {
    this.name = name;
  }
  
  public String getDescription() {
    return this.description;
  }
  
  public void setDescription(String description) {
    this.description = description;
  }
  
  public String getPlayerType() {
    return this.playerType;
  }
  
  public void setPlayerType(String playerType) {
    this.playerType = playerType;
  }
  
  public Integer getDeviceCount() {
    return this.deviceCount;
  }
  
  public void setDeviceCount(Integer deviceCount) {
    this.deviceCount = deviceCount;
  }
  
  public Integer getTotalDeviceCount() {
    return this.totalDeviceCount;
  }
  
  public void setTotalDeviceCount(Integer totalDeviceCount) {
    this.totalDeviceCount = totalDeviceCount;
  }
  
  public Boolean getHasVideowallLayout() {
    return this.hasVideowallLayout;
  }
  
  public void setHasVideowallLayout(Boolean hasVideowallLayout) {
    this.hasVideowallLayout = hasVideowallLayout;
  }
  
  public DeviceGroup(String Id, String parentId, Integer depth, String name, String description, String playerType, Integer deviceCount, Integer totalDeviceCount, String videowallLayoutId) {
    this.id = Id;
    this.parentId = parentId;
    this.depth = depth;
    this.name = name;
    this.description = description;
    this.playerType = playerType;
    this.deviceCount = deviceCount;
    this.totalDeviceCount = totalDeviceCount;
    if (videowallLayoutId.isEmpty()) {
      this.hasVideowallLayout = Boolean.valueOf(false);
    } else {
      this.hasVideowallLayout = Boolean.valueOf(true);
    } 
  }
  
  public static DeviceGroup fromData(DeviceGroupData data) {
    return new DeviceGroup(data.getId(), data.getParentId(), data.getDepth(), data.getName(), data.getDescription(), data.getPlayerType(), data.getDeviceCount(), data.getTotalDeviceCount(), data.getVideowallLayoutId());
  }
  
  public static List<DeviceGroup> fromData(List<DeviceGroupData> dataList) {
    List<DeviceGroup> result = new ArrayList<>();
    for (DeviceGroupData data : dataList)
      result.add(fromData(data)); 
    return result;
  }
}
