package com.samsung.magicinfo.webauthor2.service.textImage;

import com.samsung.magicinfo.webauthor2.util.OperatingSystem;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Paths;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import javax.imageio.ImageIO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class PNGgenerationServiceImpl implements PNGgenerationService {
  private static final Logger LOGGER = LoggerFactory.getLogger(PNGgenerationServiceImpl.class);
  
  private static final String LIB_WIN_EXECUTABLE = "wkhtmltoimage.exe";
  
  private static final String LIB_LINUX_EXECUTABLE = "wkhtmltoimage";
  
  private static final String LIB_LOCATION = "textImage/";
  
  public int generatePNG(String htmlPath, String pngPath, int width, int height) {
    int commandWidth = (width < 400) ? 400 : width;
    int commandHeight = (height < 400) ? 400 : height;
    Process process = null;
    int result = -1;
    try {
      List<String> params = getWkhtmlToImageParameters(htmlPath, pngPath, commandWidth, commandHeight);
      process = (new ProcessBuilder(params)).start();
      result = process.waitFor();
      LOGGER.debug(MessageFormat.format("Text Png generation result:{0}", new Object[] { Integer.valueOf(result) }));
      if (commandWidth != width || commandHeight != height)
        cropPngAtPath(pngPath, width, height); 
    } catch (IOException|URISyntaxException|InterruptedException ex) {
      LOGGER.error(ex.getMessage(), ex);
    } finally {
      if (process != null)
        process.destroy(); 
    } 
    return result;
  }
  
  private List<String> getWkhtmlToImageParameters(String htmlPath, String pngPath, int commandWidth, int commandHeight) throws URISyntaxException {
    List<String> params = new ArrayList<>();
    String pathToLib = getLibraryFilePath();
    params.add(pathToLib);
    params.add("-q");
    params.add("--format");
    params.add("png");
    params.add("--transparent");
    params.add("--quality");
    params.add("100");
    params.add("--width");
    params.add(Integer.toString(commandWidth));
    params.add("--height");
    params.add(Integer.toString(commandHeight));
    params.add("--crop-w");
    params.add(Integer.toString(commandWidth));
    params.add("--crop-h");
    params.add(Integer.toString(commandHeight));
    params.add("--crop-x");
    params.add("0");
    params.add("--crop-y");
    params.add("0");
    params.add(htmlPath);
    params.add(pngPath);
    return params;
  }
  
  private String getLibraryFilePath() throws URISyntaxException {
    ClassLoader cl = getClass().getClassLoader();
    if (OperatingSystem.isWindows())
      return Paths.get(cl.getResource("textImage/wkhtmltoimage.exe").toURI()).toString(); 
    if (OperatingSystem.isLinux())
      return Paths.get(cl.getResource("textImage/wkhtmltoimage").toURI()).toString(); 
    throw new UnsupportedOperationException("Image generation from text is only supported on Windows and Linux operating systems");
  }
  
  private void cropPngAtPath(String pngPath, int width, int height) {
    File pngFile = new File(pngPath);
    if (pngFile.exists())
      try {
        BufferedImage image = ImageIO.read(pngFile);
        if (image != null) {
          BufferedImage croppedImage = image.getSubimage(0, 0, width, height);
          ImageIO.write(croppedImage, "png", pngFile);
        } 
      } catch (IOException e) {
        LOGGER.error("Unable to crop image at:  " + pngPath);
      }  
  }
}
