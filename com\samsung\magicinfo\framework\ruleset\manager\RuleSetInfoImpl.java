package com.samsung.magicinfo.framework.ruleset.manager;

import com.samsung.common.db.PagedListInfo;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.SequenceDB;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.ruleset.dao.RuleSetDao;
import com.samsung.magicinfo.framework.ruleset.entity.Condition;
import com.samsung.magicinfo.framework.ruleset.entity.Result;
import com.samsung.magicinfo.framework.ruleset.entity.ResultKeyword;
import com.samsung.magicinfo.framework.ruleset.entity.RuleSet;
import com.samsung.magicinfo.framework.ruleset.entity.RulesetGroup;
import com.samsung.magicinfo.framework.ruleset.entity.SelectConditionRuleset;
import com.samsung.magicinfo.restapi.ruleset.model.V2ResultResource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

@Service
public class RuleSetInfoImpl implements RuleSetInfo {
   Logger logger = LoggingManagerV2.getLogger(RuleSetInfoImpl.class);
   private static RuleSetInfoImpl instance = null;
   private RuleSetDao dao = null;

   public static synchronized RuleSetInfo getInstance() {
      if (instance == null) {
         instance = new RuleSetInfoImpl((SqlSession)null);
      }

      return instance;
   }

   public static RuleSetInfo getInstance(SqlSession sqlSession) {
      return new RuleSetInfoImpl(sqlSession);
   }

   private RuleSetInfoImpl(SqlSession sqlSession) {
      super();
      if (this.dao == null) {
         this.dao = new RuleSetDao(sqlSession);
      }

   }

   public PagedListInfo getPagedList(int startPos, int pageSize, Map condition, String section) throws Exception {
      if (section != null) {
         byte var6 = -1;
         switch(section.hashCode()) {
         case 125758149:
            if (section.equals("public_condition")) {
               var6 = 0;
            }
            break;
         case 1374632627:
            if (section.equals("public_result")) {
               var6 = 1;
            }
         }

         switch(var6) {
         case 0:
            return this.dao.getConditionList(condition, true);
         case 1:
            return this.dao.getPublicResultList(condition);
         }
      }

      return this.dao.getRuleSetList(condition);
   }

   public Boolean addRuleset(RuleSet ruleset) throws SQLException {
      return this.dao.addRuleset(ruleset);
   }

   public RuleSet getRuleset(String rulesetId) throws SQLException {
      return this.dao.getRulesetBasicInfo(rulesetId);
   }

   public List getConditionsInRuleset(String rulesetId) throws SQLException {
      return this.dao.getConditionsInRuleset(rulesetId);
   }

   public List getContentsInRuleset(String rulesetId) throws SQLException {
      return this.dao.getContentsInRuleset(rulesetId);
   }

   public List getContentIdsInResult(String resultId) throws SQLException {
      return this.dao.getContentIdsInResult(resultId);
   }

   public List getPublicConditions(Long organizationId) throws SQLException {
      return this.dao.getPublicConditions(organizationId);
   }

   public List getPublicResults(Long organizationId) throws SQLException {
      List results = this.dao.getPublicResults(organizationId);
      Iterator var3 = results.iterator();

      while(var3.hasNext()) {
         Result result = (Result)var3.next();
         List contentsIdList = this.dao.getContentIdsInResult(result.getResult_id());
         result.setContentsIDList(contentsIdList);
      }

      return results;
   }

   public Boolean editRuleset(RuleSet ruleset) throws SQLException {
      return this.dao.editRuleset(ruleset);
   }

   public Boolean updateRulesetMetaFile(String rulesetId, String fileId) throws SQLException {
      RuleSet ruleset = new RuleSet();
      ruleset.setRuleset_id(rulesetId);
      ruleset.setFile_id(fileId);
      return this.dao.updateRuleset(ruleset, (SqlSession)null);
   }

   public List getChildGroupList(Long groupId, boolean recursive) throws SQLException {
      return this.dao.getChildGroupList(groupId, recursive);
   }

   public Long getOrgGroupIdByName(String groupName) throws SQLException {
      return groupName.equals("ROOT") ? 0L : this.dao.getOrgGroupIdByName(groupName);
   }

   public RulesetGroup getGroupById(long groupId) throws SQLException {
      return this.dao.getGroupById(groupId);
   }

   public Long getCountGroupedRuleset(Map map) throws SQLException {
      Long groupId = (Long)map.get("group_id");
      RulesetGroup group = this.dao.getGroupById(groupId);
      return group.getP_group_id() == 0L ? this.dao.getCountRulesetByOrganization(groupId) : (long)this.dao.getCountGroupedRuleset(map);
   }

   public Condition getCondition(String conditionId) throws SQLException {
      return this.dao.getCondition(conditionId);
   }

   public Result getResult(String resultId) throws SQLException {
      Result result = this.dao.getResult(resultId);
      List contentsIdList = this.dao.getContentIdsInResult(resultId);
      result.setContentsIDList(contentsIdList);
      ArrayList contentList;
      Iterator var6;
      String contentsId;
      V2ResultResource.Content content;
      if (result.getContents_type().equalsIgnoreCase("content")) {
         ContentInfo contentDao = ContentInfoImpl.getInstance();
         contentList = new ArrayList();
         var6 = contentsIdList.iterator();

         while(var6.hasNext()) {
            contentsId = (String)var6.next();
            if (contentDao.getContentAndFileActiveVerInfo(contentsId) != null && !contentDao.isDeletedContentByContentId(contentsId)) {
               content = new V2ResultResource.Content();
               content.setContentId(contentsId);
               content.setContentName(contentDao.getContentName(contentsId));
               contentList.add(content);
            }
         }

         result.setContentList(contentList);
      } else if (result.getContents_type().equalsIgnoreCase("playlist")) {
         PlaylistInfo playlistDao = PlaylistInfoImpl.getInstance();
         contentList = new ArrayList();
         var6 = contentsIdList.iterator();

         while(var6.hasNext()) {
            contentsId = (String)var6.next();
            if (playlistDao.getPlaylistActiveVerInfo(contentsId) != null && playlistDao.isDeleted(contentsId).equalsIgnoreCase("N")) {
               content = new V2ResultResource.Content();
               content.setContentId(contentsId);
               content.setContentName(playlistDao.getPlaylistName(contentsId));
               contentList.add(content);
            }
         }

         result.setContentList(contentList);
      }

      return result;
   }

   public Integer addCondition(Condition condition) throws SQLException {
      return this.dao.addCondition(condition, (SqlSession)null);
   }

   public Integer updateCondition(Condition condition) throws SQLException {
      return this.dao.updateCondition(condition, (SqlSession)null);
   }

   public PagedListInfo getDeviceListByConditions(List conditions, Integer start, Integer length) throws SQLException {
      return this.dao.getDeviceListByCondition(conditions, start, length);
   }

   public Boolean setDeleteStatus(Boolean isDeleted, String[] rulesetIds) throws SQLException {
      return this.dao.setDeleteStatus(isDeleted, rulesetIds, (SqlSession)null);
   }

   public List getAllRulesetIdInRecycleBin() throws Exception {
      Long loginUserOrgId = this.getOrgGroupIdByName(SecurityUtils.getLoginUserOrganization());
      if (loginUserOrgId == null) {
         this.logger.error("Cannot get Organization ID of Login User.");
         throw new Exception("Cannot get Organization ID of Login User.");
      } else {
         List deletedList = this.dao.getAllDeletedRulesetIds(loginUserOrgId);
         return deletedList;
      }
   }

   public List deleteRecycleBin(List deletedRulesetIdList) throws Exception {
      if (deletedRulesetIdList != null && deletedRulesetIdList.size() > 0) {
         String[] deletedArr = (String[])deletedRulesetIdList.toArray(new String[deletedRulesetIdList.size()]);
         this.deleteRulesetAndDetails(deletedArr);
      }

      return deletedRulesetIdList;
   }

   public Boolean deleteRulesetAndDetails(String[] rulesetIds) throws SQLException {
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      SqlSession session = this.dao.openNewSession(true);

      try {
         Boolean var5;
         try {
            List fileIdList = new ArrayList();
            String[] var18 = rulesetIds;
            int var6 = rulesetIds.length;

            for(int var7 = 0; var7 < var6; ++var7) {
               String rulesetId = var18[var7];
               RuleSet ruleset = this.getRuleset(rulesetId);
               Boolean var10;
               if (!this.dao.deleteRulesetDetails(rulesetId, session)) {
                  session.rollback();
                  var10 = false;
                  return var10;
               }

               if (!this.dao.deleteRulesetGroupMapping(rulesetId, session)) {
                  session.rollback();
                  var10 = false;
                  return var10;
               }

               fileIdList.add(ruleset.getFile_id());
            }

            if (!this.dao.deleteRuleset(rulesetIds, session)) {
               session.rollback();
               var5 = false;
               return var5;
            } else {
               session.commit();
               Iterator var19 = fileIdList.iterator();

               while(var19.hasNext()) {
                  String fileId = (String)var19.next();

                  try {
                     contentInfo.deleteFile(fileId);
                  } catch (Exception var15) {
                     this.logger.error(var15);
                  }
               }

               return true;
            }
         } catch (SQLException var16) {
            this.logger.error(var16);
            session.rollback();
            var5 = false;
            return var5;
         }
      } finally {
         session.close();
      }
   }

   public Boolean deleteConditions(List conditionIds) throws SQLException {
      return this.dao.deleteConditions(conditionIds, (SqlSession)null);
   }

   public Boolean isUsedContentsForRuleset(String contentsId) throws SQLException {
      List result = this.dao.getRulesetUsingContents(contentsId);
      return result.size() > 0 ? true : false;
   }

   public List getRulesetUsingContents(String contentsId) throws SQLException {
      return this.dao.getRulesetUsingContents(contentsId);
   }

   public List getRulesetUsingSubPlaylist(String playlistId) throws SQLException {
      return this.dao.getRulesetUsingSubPlaylist(playlistId);
   }

   public boolean addOrganAndDefaultGroup(String organName) throws SQLException {
      SqlSession session = this.dao.openNewSession(false);

      boolean var6;
      try {
         Integer organId = this.createNewGroupId();
         if (organId == -1) {
            this.logger.error("Cannot get a new group id.");
            session.rollback();
            boolean var15 = false;
            return var15;
         }

         int organResult = this.dao.addRulesetOrganization(organId, organName, session);
         Integer defaultGroupId = this.createNewGroupId();
         if (defaultGroupId != -1) {
            int defaultGroupResult = this.dao.addRulesetDefaultGroup(defaultGroupId, organId, "default", session);
            boolean var7;
            if (organResult > 0 && defaultGroupResult > 0) {
               session.commit();
               var7 = true;
               return var7;
            }

            session.rollback();
            var7 = false;
            return var7;
         }

         this.logger.error("Cannot get a new group id.");
         session.rollback();
         var6 = false;
      } catch (SQLException var13) {
         try {
            session.rollback();
         } catch (Exception var12) {
         }

         this.logger.error(var13);
         throw var13;
      } finally {
         session.close();
      }

      return var6;
   }

   private int createNewGroupId() throws SQLException {
      int id = false;
      int id = SequenceDB.getNextValue("MI_RULE_INFO_RULESET_GROUP");
      return id;
   }

   public List getResultsInRuleset(String rulesetId) throws SQLException {
      List resultIds = this.dao.getResultIdsInRuleset(rulesetId, (SqlSession)null);
      List resultList = new ArrayList();
      Iterator var4 = resultIds.iterator();

      while(var4.hasNext()) {
         String id = (String)var4.next();
         Result result = this.getResult(id);
         resultList.add(result);
      }

      return resultList;
   }

   public Boolean addResultAndResultContentMap(List results) throws SQLException {
      SqlSession session = this.dao.openNewSession(false);

      try {
         Iterator var3 = results.iterator();

         while(var3.hasNext()) {
            Result result = (Result)var3.next();
            Boolean var5;
            if (!this.dao.addResult(result, session)) {
               session.rollback();
               var5 = false;
               return var5;
            }

            if (result.getContentsIDList() != null && !this.dao.addResultContentMapping(result.getResult_id(), result.getContentsIDList(), result.getContents_type(), session)) {
               session.rollback();
               var5 = false;
               return var5;
            }
         }

         session.commit();
      } catch (SQLException var9) {
         this.logger.error(var9);
         session.rollback();
         Boolean var4 = false;
         return var4;
      } finally {
         session.close();
      }

      return true;
   }

   public Boolean deleteResultAndResultContentMap(List resultIds) throws SQLException {
      SqlSession session = this.dao.openNewSession(false);

      Boolean var4;
      try {
         Iterator var3 = resultIds.iterator();

         while(var3.hasNext()) {
            String resultId = (String)var3.next();
            Boolean var5;
            if (!this.dao.deleteResultContentMapping(resultId, session)) {
               session.rollback();
               var5 = false;
               return var5;
            }

            if (!this.dao.deleteResult(resultId, session)) {
               session.rollback();
               var5 = false;
               return var5;
            }
         }

         session.commit();
         return true;
      } catch (SQLException var9) {
         this.logger.error(var9);
         session.rollback();
         var4 = false;
      } finally {
         session.close();
      }

      return var4;
   }

   public Boolean updateResultAndResultContentMap(List results) throws SQLException {
      SqlSession session = this.dao.openNewSession(false);

      Boolean var4;
      try {
         List resultIds = new ArrayList();
         Iterator var11 = results.iterator();

         while(var11.hasNext()) {
            Result result = (Result)var11.next();
            resultIds.add(result.getResult_id());
         }

         if (!this.deleteResultAndResultContentMap(resultIds)) {
            session.rollback();
            var4 = false;
            return var4;
         }

         if (this.addResultAndResultContentMap(results)) {
            session.commit();
            return true;
         }

         session.rollback();
         var4 = false;
         return var4;
      } catch (SQLException var9) {
         this.logger.error(var9);
         session.rollback();
         var4 = false;
      } finally {
         session.close();
      }

      return var4;
   }

   public RulesetGroup getOrganizationByGroupId(Long groupId) throws SQLException {
      RulesetGroup group = this.dao.getGroupById(groupId);
      if (group == null) {
         return null;
      } else {
         return group.getGroup_depth() <= 1L ? group : this.getOrganizationByGroupId(group.getP_group_id());
      }
   }

   public List getRuleSetList(SelectConditionRuleset condition) throws SQLException {
      return this.dao.getRuleSetList(condition);
   }

   public Integer getRuleSetListTotalCount(SelectConditionRuleset condition) throws SQLException {
      return this.dao.getRuleSetListTotalCount(condition);
   }

   public List getConditionList(SelectConditionRuleset condition, Boolean isPublic) throws SQLException {
      return this.dao.getConditionList(condition, isPublic);
   }

   public Integer getConditionListTotalCount(SelectConditionRuleset condition, Boolean isPublic) throws SQLException {
      return this.dao.getConditionListTotalCount(condition, isPublic);
   }

   public List getResultList(SelectConditionRuleset condition, Boolean isPublic) throws SQLException {
      return this.dao.getResultList(condition, isPublic);
   }

   public Integer getResultListTotalCount(SelectConditionRuleset condition, Boolean isPublic) throws SQLException {
      return this.dao.getResultListTotalCount(condition, isPublic);
   }

   public Integer addGroup(RulesetGroup group) throws SQLException {
      long groupId = (long)this.createNewGroupId();
      RulesetGroup parentGroup = this.getGroupById(group.getP_group_id());
      group.setGroup_depth(parentGroup.getGroup_depth() + 1L);
      group.setGroup_id(groupId);
      group.setDescription("created");
      Integer result = this.dao.addRulesetGroup(group, (SqlSession)null);
      return result;
   }

   public Boolean updateGroup(RulesetGroup group) throws SQLException {
      return this.dao.updateRulesetGroup(group);
   }

   public Boolean deleteGroup(Long groupId) {
      SqlSession session = this.dao.openNewSession(false);

      try {
         RulesetGroup g = this.getGroupById(groupId);
         List groups = this.dao.getChildGroupList(groupId, true);
         groups.add(g);
         List rulesetIdList = new ArrayList();
         Iterator var6 = groups.iterator();

         while(var6.hasNext()) {
            RulesetGroup group = (RulesetGroup)var6.next();
            List list = this.dao.getRulesetListByGroupId(group.getGroup_id());
            Iterator var9 = list.iterator();

            while(var9.hasNext()) {
               RuleSet ruleset = (RuleSet)var9.next();
               if (!this.dao.updateRulesetGroupMapping(ruleset.getRuleset_id(), ruleset.getOrganization_id(), session)) {
                  session.rollback();
                  Boolean var11 = false;
                  return var11;
               }

               rulesetIdList.add(ruleset.getRuleset_id());
            }
         }

         if (rulesetIdList.size() > 0) {
            String[] rulesetIds = (String[])rulesetIdList.toArray(new String[rulesetIdList.size()]);
            if (!this.dao.setDeleteStatus(true, rulesetIds, session)) {
               session.rollback();
               Boolean var20 = false;
               return var20;
            }
         }

         if (!this.dao.deleteRulesetGroup(groups, session)) {
            session.rollback();
            Boolean var19 = false;
            return var19;
         }

         session.commit();
      } catch (Exception var15) {
         this.logger.error(var15);
         session.rollback();
         Boolean var4 = false;
         return var4;
      } finally {
         session.close();
      }

      return true;
   }

   public Boolean updateRulesetGroupMapping(List rulesetIds, Long groupId) {
      SqlSession session = this.dao.openNewSession(false);

      Boolean var5;
      try {
         RulesetGroup org = this.getOrganizationByGroupId(groupId);
         Iterator var13 = rulesetIds.iterator();

         while(var13.hasNext()) {
            String rulesetId = (String)var13.next();
            RuleSet ruleset = new RuleSet();
            ruleset.setRuleset_id(rulesetId);
            ruleset.setOrganization_id(org.getGroup_id());
            this.dao.updateRulesetGroupMapping(rulesetId, groupId, session);
            this.dao.updateRuleset(ruleset, session);
         }

         session.commit();
         return true;
      } catch (Exception var11) {
         this.logger.error(var11);
         session.rollback();
         var5 = false;
      } finally {
         session.close();
      }

      return var5;
   }

   public List getKeywords(Long organizationId) throws SQLException {
      return this.dao.getKeywords(organizationId);
   }

   private int createNewKeywordId() throws SQLException {
      int id = false;
      int id = SequenceDB.getNextValue("MI_RULE_INFO_KEYWORD");
      return id;
   }

   public Integer addKeyword(ResultKeyword keyword) throws SQLException {
      long keywordId = (long)this.createNewKeywordId();
      keyword.setKeyword_id(keywordId);
      return this.dao.addKeyword(keyword);
   }

   public Integer updateKeyword(ResultKeyword keyword) throws SQLException {
      return this.dao.updateKeyword(keyword);
   }

   public Boolean deleteKeywords(List keywordIds) throws SQLException {
      return this.dao.deleteKeywords(keywordIds);
   }

   public List getRulesetListByGroupId(Long groupId) throws SQLException {
      return this.dao.getRulesetListByGroupId(groupId);
   }

   public Boolean restoreRuleset(String[] rulesetIds, Long groupId) {
      SqlSession session = this.dao.openNewSession(false);

      try {
         Boolean var5;
         try {
            RulesetGroup org = this.getOrganizationByGroupId(groupId);
            if (!this.dao.setDeleteStatus(false, rulesetIds, session)) {
               session.rollback();
               var5 = false;
               return var5;
            } else {
               String[] var16 = rulesetIds;
               int var6 = rulesetIds.length;

               for(int var7 = 0; var7 < var6; ++var7) {
                  String rulesetId = var16[var7];
                  RuleSet ruleset = new RuleSet();
                  ruleset.setRuleset_id(rulesetId);
                  ruleset.setOrganization_id(org.getGroup_id());
                  Boolean var10;
                  if (!this.dao.updateRulesetGroupMapping(rulesetId, groupId, session)) {
                     session.rollback();
                     var10 = false;
                     return var10;
                  }

                  if (!this.dao.updateRuleset(ruleset, session)) {
                     session.rollback();
                     var10 = false;
                     return var10;
                  }
               }

               session.commit();
               return true;
            }
         } catch (Exception var14) {
            session.rollback();
            var5 = false;
            return var5;
         }
      } finally {
         session.close();
      }
   }

   public Boolean hasContentInRuleset(String rulesetId, String contentId) throws SQLException {
      return this.dao.hasContentInRuleset(rulesetId, contentId);
   }

   public List getRulesetContentDownloadStatus(String rulesetId, String deviceId) throws SQLException {
      return this.dao.getRulesetContentDownloadStatus(rulesetId, deviceId);
   }

   public List getRulesetGroupBySearch(String organizationName, String searchText) throws SQLException {
      return this.dao.getRulesetGroupBySearch(organizationName, searchText);
   }

   public Integer getRulesetGroupTotalCount() throws SQLException {
      return this.dao.getRulesetGroupTotalCount();
   }

   public List getParentsGroupList(long pGroupId) throws SQLException {
      return this.dao.getParentsGroupList(pGroupId);
   }
}
