<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.samsung.magicinfo.framework.device.deviceInfo.dao.LedCabinetConfDaoMapper">
    
    <sql id="limitResult">LIMIT #{pageSize} OFFSET #{startPos}</sql>
    
    <select id="getLedCabinet" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.LedCabinet">
        SELECT *
            FROM MI_DMS_INFO_LED_CABINET
        WHERE PARENT_DEVICE_ID = #{parentDeviceId}
            AND CABINET_GROUP_ID = #{cabinetGroupId}
            AND CABINET_ID = #{cabinetId}
    </select>
    
    <select id="getLedCabinetListCount" resultType="int">
        SELECT COUNT(*)
            FROM MI_DMS_INFO_LED_CABINET
        WHERE PARENT_DEVICE_ID = #{parentDeviceId}
            <if test="cabinetGroupId != null and cabinetGroupId != '' and cabinetGroupId > 0">
                AND CABINET_GROUP_ID = #{cabinetGroupId}
            </if>        
            AND CABINET_ID != 0 
    </select>


	<select id="getLedCabinetListByIdList" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.LedCabinet">
	    SELECT *
        FROM MI_DMS_INFO_LED_CABINET 
        WHERE 
            PARENT_DEVICE_ID = #{parentDeviceId}
            <if test="childIds != null and childIds.size > 0">
                AND CABINET_GROUP_ID || '-' || CABINET_ID IN
                <foreach item="childId" collection="childIds" open="(" separator="," close=")">
                    #{childId}
                </foreach>
	        </if>
	</select>
	
    <select id="getLedCabinetListByIdList" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.LedCabinet" databaseId="mssql">
        SELECT *
        FROM MI_DMS_INFO_LED_CABINET 
        WHERE 
            PARENT_DEVICE_ID = #{parentDeviceId}
            <if test="childIds != null and childIds.size > 0">
                AND CAST(CABINET_GROUP_ID AS VARCHAR) + '-' + CAST(CABINET_ID AS VARCHAR) IN
                <foreach item="childId" collection="childIds" open="(" separator="," close=")">
                    #{childId}
                </foreach>
            </if>
    </select>
	
    <select id="getLedCabinetList" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.LedCabinet">
        <bind name="dirUpper" value="_parameter.dir.toUpperCase()"/>
        <bind name="srcUpper" value="_parameter.src.toUpperCase()"/>
        
        SELECT *
            FROM MI_DMS_INFO_LED_CABINET 
        WHERE PARENT_DEVICE_ID = #{parentDeviceId}
        <if test="cabinetGroupId != null and cabinetGroupId != '' and cabinetGroupId > 0">
            AND CABINET_GROUP_ID = #{cabinetGroupId}
        </if>
        AND CABINET_ID != 0
        <if test="sort != null and sort != '' and dir != null and dir != ''">
            <bind name="safe_sort" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(sort)" />
            <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(dirUpper)" />
            ORDER BY
                <choose>
                        <when test="safe_sort == 'DEVICE_ID'">
                            CABINET_GROUP_ID ASC, CABINET_ID ${safe_sortOrder} 
                        </when>
                        <otherwise>
                            CABINET_GROUP_ID ASC, ${safe_sort} ${safe_sortOrder}
                        </otherwise> 
                </choose>   
        </if>
        <if test="sort == null or sort == ''">
            ORDER BY CABINET_GROUP_ID ASC, CABINET_ID ASC
        </if>
        <if test="startPos >= 0 and pageSize > 0">
            <include refid="limitResult"/>
        </if>
    </select>
    
    <select id="getLedCabinetList" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.LedCabinet" databaseId="mssql">
        <bind name="dirUpper" value="_parameter.dir.toUpperCase()"/>
        <bind name="srcUpper" value="_parameter.src.toUpperCase()"/>
        SELECT *
            FROM MI_DMS_INFO_LED_CABINET 
        WHERE PARENT_DEVICE_ID = #{parentDeviceId}
        <if test="cabinetGroupId != null and cabinetGroupId != '' and cabinetGroupId > 0">
            AND CABINET_GROUP_ID = #{cabinetGroupId}
        </if>      
        AND CABINET_ID != 0  
        <if test="sort != null and sort != '' and dir != null and dir != ''">
            <bind name="safe_sort" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(sort)" />
            <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(dirUpper)" />
            ORDER BY
                <choose>
                        <when test="safe_sort == 'DEVICE_ID'">
                            CABINET_GROUP_ID ASC, CABINET_ID ${safe_sortOrder} 
                        </when>
                        <otherwise>
                            CABINET_GROUP_ID ASC, ${safe_sort} ${safe_sortOrder} 
                        </otherwise> 
                </choose>   
        </if>
        <if test="sort == null or sort == ''">
            ORDER BY CABINET_GROUP_ID ASC, CABINET_ID ASC 
        </if>
        <if test="startPos >= 0 and pageSize > 0">
            <include refid="limitResult"/>      
        </if>
    </select>
        
    <insert id="addLedCabinet">
        INSERT INTO MI_DMS_INFO_LED_CABINET (
            PARENT_DEVICE_ID, CABINET_ID, CABINET_GROUP_ID, CABINET_TYPE, POWER, OPEN_DETECTION, HDBT, VOLTAGE_STATUS, RESOLUTION, PHY_SIZE, ASPECT_RATIO, MODULES,
            PITCH, LUMINANCE, ABL, AUTO_SOURCE, IC, CURRENT_TEMPERATURE, FAULT, FW_VERSION, FPGA_VERSION, 
            LAST_SCANNED_TIME, ON_SCREEN_DISPLAY, POSITION_X, POSITION_Y, MODULE_PRODUCT_NUMBER, MODULE_DATE
        ) VALUES (
            #{ledCabinet.parent_device_id}, #{ledCabinet.cabinet_id}, #{ledCabinet.cabinet_group_id}, #{ledCabinet.cabinet_type}, #{ledCabinet.power}, #{ledCabinet.open_detection}, #{ledCabinet.hdbt}, #{ledCabinet.voltage_status},
            #{ledCabinet.resolution}, #{ledCabinet.phy_size}, #{ledCabinet.aspect_ratio}, #{ledCabinet.modules},
            #{ledCabinet.pitch}, #{ledCabinet.luminance}, #{ledCabinet.abl}, #{ledCabinet.auto_source},
            #{ledCabinet.ic}, #{ledCabinet.current_temperature}, #{ledCabinet.fault}, #{ledCabinet.fw_version}, 
            #{ledCabinet.fpga_version}, #{ledCabinet.last_scanned_time}, #{ledCabinet.on_screen_display}, 
            #{ledCabinet.position_X}, #{ledCabinet.position_Y}, #{ledCabinet.module_product_number}, #{ledCabinet.module_date}
        )
    </insert>

    <update id="setLedCabinetPowerOff">
        UPDATE MI_DMS_INFO_LED_CABINET SET
            POWER = '0'
        WHERE PARENT_DEVICE_ID = #{parentDeviceId} 
            <if test="cabinetId != null">
                AND CABINET_ID = #{cabinetId}
            </if>
            <if test="cabinetGroupId != null">
                AND CABINET_GROUP_ID = #{cabinetGroupId}
            </if>
    </update>
    
    <update id="setLedCabinet">
        UPDATE MI_DMS_INFO_LED_CABINET SET
            <if test="info.power != null">
                POWER = #{info.power},
            </if>
            <if test="info.open_detection != null">
                OPEN_DETECTION = #{info.open_detection},
            </if>
            <if test="info.hdbt != null">
                HDBT = #{info.hdbt},
            </if>
            <if test="info.voltage_status != null">
                VOLTAGE_STATUS = #{info.voltage_status},
            </if>
            <if test="info.resolution != null">
                RESOLUTION = #{info.resolution},
            </if>
            <if test="info.phy_size != null">
                PHY_SIZE = #{info.phy_size},
            </if>
            <if test="info.aspect_ratio != null">
                ASPECT_RATIO = #{info.aspect_ratio},
            </if>
            <if test="info.modules != null">
                MODULES = #{info.modules},
            </if>            
            <if test="info.pitch != null">
                PITCH = #{info.pitch},
            </if>
            <if test="info.luminance != null">
                LUMINANCE = #{info.luminance},
            </if>
            <if test="info.abl != null">
                ABL = #{info.abl},
            </if>
            <if test="info.auto_source != null">
                AUTO_SOURCE = #{info.auto_source},
            </if>
            <if test="info.ic != null">
                IC = #{info.ic},
            </if>
            <if test="info.current_temperature != null">
                CURRENT_TEMPERATURE = #{info.current_temperature},
            </if>
            <if test="info.fault != null">
                FAULT = #{info.fault},
            </if>               
            <if test="info.input_source != null">
                INPUT_SOURCE = #{info.input_source},
            </if>
            <if test="info.gamut != null">
                GAMUT = #{info.gamut},
            </if>
            <if test="info.backlight != null">
                BACKLIGHT = #{info.backlight},
            </if>
            <if test="info.max_backlight != null">
                MAX_BACKLIGHT = #{info.max_backlight},
            </if>
            <if test="info.pixel_rgb_cc != null">
                PIXEL_RGB_CC = #{info.pixel_rgb_cc},
            </if>
            <if test="info.module_rgb_cc != null">
                MODULE_RGB_CC = #{info.module_rgb_cc},
            </if>
            <if test="info.edge_correction != null">
                EDGE_CORRECTION = #{info.edge_correction},
            </if>
            <if test="info.fw_version != null">
                FW_VERSION = #{info.fw_version},
            </if>
            <if test="info.fpga_version != null">
                FPGA_VERSION = #{info.fpga_version},
            </if>
            <if test="info.last_scanned_time != null">
                LAST_SCANNED_TIME = #{info.last_scanned_time},
            </if>
            <if test="info.on_screen_display != null">
                ON_SCREEN_DISPLAY = #{info.on_screen_display},
            </if>
            <if test="info.isLODError != null">
	            ISLODERROR = #{info.isLODError},
            </if>
            <if test="info.isTemperatureError != null">
	            ISTEMPERATUREERROR = #{info.isTemperatureError},
            </if>
            <if test="info.isVoltageError != null">
	            ISVOLTAGEERROR = #{info.isVoltageError},
            </if>
            <if test="info.isICError != null">
	            ISICERROR = #{info.isICError},
            </if>     
            <if test="info.position_X != null">
	            position_X = #{info.position_X},
            </if> 
            <if test="info.position_Y != null">
	            position_Y = #{info.position_Y},
            </if>                  
            <if test="info.module_product_number != null">
	            MODULE_PRODUCT_NUMBER = #{info.module_product_number},
            </if>
            <if test="info.module_date != null">
	            MODULE_DATE = #{info.module_date},
            </if>
            PARENT_DEVICE_ID = #{info.parent_device_id}
        WHERE PARENT_DEVICE_ID = #{info.parent_device_id} 
            <if test="info.cabinet_id != null">
	            AND CABINET_ID = #{info.cabinet_id}
	        </if>
	        <if test="info.cabinet_group_id != null">
	            AND CABINET_GROUP_ID = #{info.cabinet_group_id}
            </if>
    </update>

    <delete id="deleteLedCabinets">
        DELETE FROM MI_DMS_INFO_LED_CABINET
        WHERE PARENT_DEVICE_ID = #{parentDeviceId}
    </delete>
    
    <delete id="deleteLedCabinet">
        DELETE FROM MI_DMS_INFO_LED_CABINET
        WHERE PARENT_DEVICE_ID = #{parentDeviceId} AND CABINET_GROUP_ID = #{cabinetGroupId} AND CABINET_ID = #{cabinetId}
    </delete>
    
    <insert id="addLedCabinetGroupInfo">
        INSERT INTO MI_DMS_INFO_LED_CABINET ( 
        PARENT_DEVICE_ID, CABINET_GROUP_ID, CABINET_ID, RESOLUTION, POSITION_X, POSITION_Y  
        ) VALUES(
        #{parentDeviceId}, #{cabinetGroupId}, 0 , #{groupResolution}, #{positionX}, #{positionY})
    </insert>
    
    <update id="setLedCabinetGroupInfo">
        UPDATE MI_DMS_INFO_LED_CABINET 
        SET
			POSITION_X = #{positionX},
            POSITION_Y = #{positionY},
            RESOLUTION = #{groupResolution}
        WHERE PARENT_DEVICE_ID = #{parentDeviceId} 
			AND CABINET_ID = 0
	        AND CABINET_GROUP_ID = #{cabinetGroupId}
    </update>
    
     <select id="getLedCabinetGroupIds" resultType="java.lang.Long">
     	SELECT  DISTINCT CABINET_GROUP_ID FROM MI_DMS_INFO_LED_CABINET WHERE PARENT_DEVICE_ID = #{parentDeviceId} ORDER BY CABINET_GROUP_ID ASC
     </select>
     
     <select id="getLedCabinetCountByGroup" resultType="int">
     	SELECT COUNT(*) FROM MI_DMS_INFO_LED_CABINET WHERE PARENT_DEVICE_ID = #{parentDeviceId} AND CABINET_GROUP_ID = #{groupId} AND CABINET_ID != 0  
     </select>
     
     <select id="getLedCabinetGroupLayoutInfo" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.LedCabinet">
     	SELECT * FROM MI_DMS_INFO_LED_CABINET WHERE PARENT_DEVICE_ID = #{parentDeviceId} AND CABINET_ID = 0
     </select>
     
     
     <select id="getErrorLedCabinetCntByGroupId" resultType="int">
     	SELECT COUNT(*) FROM MI_DMS_INFO_LED_CABINET WHERE PARENT_DEVICE_ID = #{parentDeviceId} AND CABINET_GROUP_ID = #{groupId} AND (ISLODERROR = 'ERROR' OR ISTEMPERATUREERROR = 'ERROR' OR ISVOLTAGEERROR = 'ERROR' OR ISICERROR = 'ERROR') AND CABINET_ID != 0  
     </select>
</mapper>