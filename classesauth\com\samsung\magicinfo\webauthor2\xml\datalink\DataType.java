package com.samsung.magicinfo.webauthor2.xml.datalink;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DataType", propOrder = {"serverAddress", "valueLocation", "values", "tagList", "convertTable"})
public class DataType {
  @XmlElement(name = "ServerAddress", required = true)
  protected String serverAddress = null;
  
  @XmlElement(name = "ValueLocation", required = true)
  protected ValueLocationType valueLocation;
  
  @XmlElement(name = "Values", required = true)
  protected ValuesType values;
  
  @XmlElement(name = "TagList")
  protected TagListType tagList;
  
  @XmlElement(name = "ConvertTable")
  protected ConvertTableType convertTable;
  
  @XmlAttribute(name = "type")
  protected String type;
  
  public String getServerAddress() {
    return this.serverAddress;
  }
  
  public void setServerAddress(String value) {
    this.serverAddress = value;
  }
  
  public ValueLocationType getValueLocation() {
    return this.valueLocation;
  }
  
  public void setValueLocation(ValueLocationType value) {
    this.valueLocation = value;
  }
  
  public ValuesType getValues() {
    return this.values;
  }
  
  public void setValues(ValuesType value) {
    this.values = value;
  }
  
  public String getType() {
    return this.type;
  }
  
  public void setType(String value) {
    this.type = value;
  }
  
  public TagListType getTagList() {
    return this.tagList;
  }
  
  public void setTagList(TagListType tagList) {
    this.tagList = tagList;
  }
  
  public ConvertTableType getConvertTable() {
    return this.convertTable;
  }
  
  public void setConvertTable(ConvertTableType convertTable) {
    this.convertTable = convertTable;
  }
}
