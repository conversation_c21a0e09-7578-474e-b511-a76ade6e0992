package com.samsung.magicinfo.restapi.user.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.AbilityUtils;
import com.samsung.common.utils.RoleUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.page.ListManager;
import com.samsung.common.utils.page.PageManager;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.role.entity.Role;
import com.samsung.magicinfo.framework.role.manager.AbilityInfo;
import com.samsung.magicinfo.framework.role.manager.AbilityInfoImpl;
import com.samsung.magicinfo.framework.role.manager.RoleInfo;
import com.samsung.magicinfo.framework.role.manager.RoleInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserLogManager;
import com.samsung.magicinfo.framework.user.manager.UserLogManagerImpl;
import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonDeleteFail;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.user.model.V2CheckRoleNameResource;
import com.samsung.magicinfo.restapi.user.model.V2RoleAbilityData;
import com.samsung.magicinfo.restapi.user.model.V2RoleAbilityResource;
import com.samsung.magicinfo.restapi.user.model.V2RoleCreation;
import com.samsung.magicinfo.restapi.user.model.V2RoleEditResource;
import com.samsung.magicinfo.restapi.user.model.V2RoleListQueryDataResource;
import com.samsung.magicinfo.restapi.user.model.V2RoleListQueryFilter;
import com.samsung.magicinfo.restapi.user.model.V2RoleSaveResource;
import com.samsung.magicinfo.restapi.utils.ConvertUtil;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.apache.logging.log4j.Logger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2RoleService")
@Transactional
public class V2RoleServiceImpl implements V2RoleService {
   protected Logger logger = LoggingManagerV2.getLogger(V2RoleServiceImpl.class);

   public V2RoleServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2PageResource roleListQuery(V2RoleListQueryFilter filter) throws Exception {
      this.logger.debug("..........roleListQuery() Started.............." + new Date());
      String sortOrder = StrUtils.nvl(filter.getSortOrder()).equals("") ? "asc" : filter.getSortOrder();
      String sortColumn = StrUtils.nvl(filter.getSortColumn()).equals("") ? "role_name" : filter.getSortColumn();
      if (sortColumn.equalsIgnoreCase("organization_name")) {
         sortColumn = "GROUP_NAME";
      }

      String searchText = StrUtils.nvl(filter.getSearchText()).equals("") ? "" : filter.getSearchText();
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      int startIndex = filter.getStartIndex();
      int results = filter.getPageSize();
      boolean isSearch = filter.isSearch();
      UserInfo userInfo = UserInfoImpl.getInstance();
      RoleInfo roleInfo = RoleInfoImpl.getInstance();
      User user = SecurityUtils.getLoginUser();
      String organization = user.getOrganization();
      Long rootGroupId = userInfo.getRootGroupIdByUserId(user.getUser_id());
      ListManager listMgr = new ListManager(roleInfo, "list");
      listMgr.addSearchInfo("sortColumn", sortColumn);
      listMgr.addSearchInfo("sortOrder", sortOrder);
      listMgr.addSearchInfo("searchText", searchText);
      listMgr.addSearchInfo("organization", organization);
      listMgr.addSearchInfo("root_group_id", rootGroupId);
      listMgr.addSearchInfo("scope", RoleUtils.SCOPE_GROUP);
      listMgr.addSearchInfo("isSearch", isSearch);
      listMgr.setLstSize(Integer.valueOf(results));
      PageManager pageMgr = null;
      List roleList = listMgr.V2dbexecute(startIndex, results);
      pageMgr = listMgr.getPageManager();
      ArrayList dataList = new ArrayList();

      for(int i = 0; i < roleList.size(); ++i) {
         V2RoleListQueryDataResource data = new V2RoleListQueryDataResource();
         Role role = (Role)roleList.get(i);
         data.setRoleName(role.getRole_name().replaceAll("<", "&lt"));
         data.setScope(role.getScope());
         data.setRoleId(role.getRole_id());
         data.setUserCount(role.getUser_count());
         data.setIsDefault(role.getIs_default());
         data.setOrganizationId(role.getRoot_group_id());
         String organizationName = userInfo.getOrganNameByRootGroupId(role.getRoot_group_id());
         data.setOrganizationName(organizationName);
         data.setServerMode(role.getServer_mode());
         dataList.add(data);
      }

      V2PageResource resource = V2PageResource.createPageResource(dataList, pageMgr);
      this.logger.debug("roleListQuery() Ended.............." + new Date());
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2RoleAbilityResource abilityList() throws Exception {
      UserInfo userInfo = UserInfoImpl.getInstance();
      String pUserId = SecurityUtils.getLoginUserId();
      User pUser = userInfo.getAllByUserId(pUserId);
      Long rootGroupId = pUser.getRoot_group_id();
      String scope = RoleUtils.SCOPE_GROUP;
      RoleInfo roleInfo = RoleInfoImpl.getInstance();
      AbilityInfo abilityInfo = AbilityInfoImpl.getInstance();
      AbilityUtils ability = new AbilityUtils();
      boolean canWriteUser = ability.checkAuthority("User Write");
      boolean ruleManagerEnable = false;
      if (CommonConfig.get("rulemanager.enable") != null && CommonConfig.get("rulemanager.enable").equals("true") && ability.checkAuthority("RuleManager HQ")) {
         ruleManagerEnable = true;
      }

      boolean bRegLicLfd = false;
      boolean bool_reg_lic_soc = false;
      boolean bool_reg_lic_android = false;
      boolean bool_reg_lic_sinage = false;
      boolean bool_reg_lic_lite = false;
      boolean bool_reg_lic_rms = false;
      boolean rmMode = false;
      if (CommonConfig.get("BOOL_REG_LIC_LFD") != null) {
         bRegLicLfd = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LFD"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SOC") != null) {
         bool_reg_lic_soc = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SOC"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_ANDROID") != null) {
         bool_reg_lic_android = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_ANDROID"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SIGNAGE") != null) {
         bool_reg_lic_sinage = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SIGNAGE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_LITE") != null) {
         bool_reg_lic_lite = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LITE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_RMS") != null) {
         bool_reg_lic_rms = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_RMS"));
      }

      if (bool_reg_lic_rms && !bRegLicLfd && !bool_reg_lic_soc && !bool_reg_lic_android && !bool_reg_lic_sinage && !bool_reg_lic_lite) {
         rmMode = true;
      }

      Map map = new HashMap();
      map.put("rootGroupId", rootGroupId);
      map.put("scope", scope);
      map.put("rmMode", rmMode);
      roleInfo.getChildRoleListByRootGroupId(map);
      List abilityList = null;
      abilityList = abilityInfo.getDefaultAbilityList();
      V2RoleAbilityResource resource = new V2RoleAbilityResource();
      List dataList = new ArrayList();

      for(int i = 0; i < abilityList.size(); ++i) {
         V2RoleAbilityData data = new V2RoleAbilityData();
         String abilityGroup = (String)((Map)abilityList.get(i)).get("ability_group");
         String abilityName = (String)((Map)abilityList.get(i)).get("ability_name");
         Long abilityId = (Long)((Map)abilityList.get(i)).get("ability_id");
         data.setAbilityGroupName(abilityGroup);
         data.setAbilityName(abilityName);
         data.setAbilityId(abilityId);
         dataList.add(data);
      }

      resource.setAbilityList(dataList);
      resource.setCanWriteUser(String.valueOf(canWriteUser));
      resource.setRmMode(rmMode);
      resource.setRuleManagerEnable(String.valueOf(ruleManagerEnable));
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2RoleEditResource roleEdit(String roleId) throws Exception {
      V2RoleEditResource resource = new V2RoleEditResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      roleId = StrUtils.nvl(roleId);
      RoleInfo roleInfo = RoleInfoImpl.getInstance();
      AbilityInfo abilityInfo = AbilityInfoImpl.getInstance();
      AbilityUtils ability = new AbilityUtils();
      boolean canWriteUser = ability.checkAuthority("User Write");
      boolean ruleManagerEnable = false;
      if (CommonConfig.get("rulemanager.enable") != null && CommonConfig.get("rulemanager.enable").equals("true") && ability.checkAuthority("RuleManager HQ")) {
         ruleManagerEnable = true;
      }

      List abilityList = null;
      String roleName = roleInfo.getNameByRoleId(Long.valueOf(roleId));
      abilityList = abilityInfo.getAllAbilityList(Long.valueOf(roleId));
      Role role = roleInfo.getAllByRoleId(Long.valueOf(roleId));
      Long roleRootGroupId = role.getRoot_group_id();
      if (roleName.equals("Administrator") || roleName.equals("Content Manager") || roleName.equals("Schedule Manager") || roleName.equals("Device Manager") || roleName.equals("User Manager") || roleName.equals("Content Uploader") || roleName.equals("Schedule Editor") || roleName.equals("Content Schedule Manager")) {
         canWriteUser = false;
      }

      if (roleRootGroupId == 0L && !userContainer.getUser().getOrganization().equals("ROOT")) {
         canWriteUser = false;
      }

      List list = new ArrayList();
      Iterator var14 = abilityList.iterator();

      while(var14.hasNext()) {
         Map map = (Map)var14.next();
         V2RoleAbilityData abilityData = new V2RoleAbilityData();
         abilityData.setActive((Boolean)map.get("is_check"));
         abilityData.setAbilityGroupName((String)map.get("ability_group"));
         abilityData.setAbilityName((String)map.get("ability_name"));
         abilityData.setAbilityId((Long)map.get("ability_id"));
         list.add(abilityData);
      }

      resource.setAbilityList(list);
      resource.setCanWriteUser(String.valueOf(String.valueOf(canWriteUser)));
      resource.setRuleManagerEnable(String.valueOf(ruleManagerEnable));
      resource.setRoleRootGroupId(roleRootGroupId);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2RoleSaveResource roleSave(V2RoleCreation body, HttpServletRequest request) throws Exception {
      V2RoleSaveResource resource = new V2RoleSaveResource();
      String pUserId = SecurityUtils.getLoginUserId();
      UserInfo userInfo = UserInfoImpl.getInstance();
      User pUser = userInfo.getAllByUserId(pUserId);
      Long rootGroupId = pUser.getRoot_group_id();
      if (body.getOrganizationId() != null) {
         rootGroupId = body.getOrganizationId();
      }

      RoleInfo roleInfo = RoleInfoImpl.getInstance();
      UserLogManager logMgr = UserLogManagerImpl.getInstance();
      String roleName = StrUtils.nvl(body.getRoleName());
      ArrayList abilityIds = body.getAbilityIds();
      if (roleInfo.getCountRoleByRoleName(roleName) != 0) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_SAME_ITEM_EXIST, new String[]{"role name"});
      } else {
         Role role = new Role();
         role.setIs_default("N");
         role.setRole_name(roleName);
         role.setRoot_group_id(rootGroupId);
         role.setScope("GROUP");
         List abilityMapList = new ArrayList();

         for(int i = 0; i < abilityIds.size(); ++i) {
            Map abilityMap = new HashMap();
            abilityMap.put("ability_id", Long.parseLong((String)abilityIds.get(i)));
            abilityMapList.add(i, abilityMap);
         }

         Long roleId = roleInfo.addRole(role);
         if (roleId > 0L) {
            roleInfo.addAbilityListByRoleId(roleId, abilityMapList);
            resource.setRoleName(roleName);
            resource.setRoleId(roleId);
            resource.setAbilityIds(abilityIds);
            return resource;
         } else {
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2RoleSaveResource roleEditSave(V2CommonIds body, String roleIdStr, HttpServletRequest request) throws Exception {
      V2RoleSaveResource resource = new V2RoleSaveResource();
      String pUserId = SecurityUtils.getLoginUserId();
      UserLogManager logMgr = UserLogManagerImpl.getInstance();
      RoleInfo roleInfo = RoleInfoImpl.getInstance();
      List abilityIds = body.getIds();
      Long roleId = 0L;
      if (roleIdStr != null && !roleIdStr.equals("")) {
         roleId = Long.valueOf(roleIdStr);
         Role role = roleInfo.getAllByRoleId(roleId);
         if (role.getIs_default().equals("Y")) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_CANNOT_EDIT_DEFAULT_ROLE);
         } else {
            List abilityMapList = new ArrayList();

            for(int i = 0; i < abilityIds.size(); ++i) {
               Map abilityMap = new HashMap();
               abilityMap.put("ability_id", Long.parseLong((String)abilityIds.get(i)));
               abilityMapList.add(abilityMap);
            }

            roleInfo.addAbilityListByRoleId(roleId, abilityMapList);
            String roleName = role.getRole_name();
            resource.setRoleName(roleName);
            resource.setRoleId(roleId);
            resource.setAbilityIds(abilityIds);
            return resource;
         }
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_EMPTY, new String[]{"roleId"});
      }
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2CommonBulkResultResource deleteRole(V2CommonIds body, HttpServletRequest request) throws Exception {
      V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
      RoleInfo roleInfo = RoleInfoImpl.getInstance();
      String pUserId = SecurityUtils.getLoginUserId();
      UserLogManager logMgr = UserLogManagerImpl.getInstance();
      List roleIds = body.getIds();
      List deletedSuccessList = new ArrayList();
      List deletedFailList = new ArrayList();
      Iterator var10 = roleIds.iterator();

      while(var10.hasNext()) {
         String roleId = (String)var10.next();

         try {
            roleInfo.getNameByRoleId(Long.valueOf(roleId));
            Role role = roleInfo.getAllByRoleId(Long.valueOf(roleId));
            List mappedUserList = roleInfo.getMappedUseListrByRoleId(role.getRole_id());
            boolean isDefaultRole = role.getIs_default().equalsIgnoreCase("Y");
            V2CommonDeleteFail deletedFail;
            if (mappedUserList.size() != 0) {
               deletedFail = new V2CommonDeleteFail();
               deletedFail.setId(roleId);
               deletedFail.setReason(RestExceptionCode.BAD_REQUEST_USED_ROLE_NOT_DELETE.getMessage());
               deletedFail.setReasonCode(RestExceptionCode.BAD_REQUEST_USED_ROLE_NOT_DELETE.getCode());
               deletedFailList.add(deletedFail);
            } else if (isDefaultRole) {
               deletedFail = new V2CommonDeleteFail();
               deletedFail.setId(roleId);
               deletedFail.setReason(RestExceptionCode.BAD_REQUEST_DEFAULT_ROLE_NOT_DELETE.getMessage());
               deletedFail.setReasonCode(RestExceptionCode.BAD_REQUEST_DEFAULT_ROLE_NOT_DELETE.getCode());
               deletedFailList.add(deletedFail);
            } else {
               roleInfo.deleteRoleByRoleId(role.getRole_id());
               roleInfo.deleteAllAbilityByRoleId(role.getRole_id());
               deletedSuccessList.add(roleId);
            }
         } catch (Exception var17) {
            V2CommonDeleteFail item = new V2CommonDeleteFail();
            item.setId(roleId);
            item.setReason(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN.getMessage());
            item.setReasonCode(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN.getCode());
            deletedFailList.add(item);
         }
      }

      resource.setSuccessList(deletedSuccessList);
      resource.setFailList(deletedFailList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('User Read Authority', 'User Write Authority', 'User Approval Authority')")
   public V2CheckRoleNameResource checkRoleName(String roleName) throws Exception {
      V2CheckRoleNameResource resource = new V2CheckRoleNameResource();
      RoleInfo roleInfo = RoleInfoImpl.getInstance();
      roleName = StrUtils.nvl(roleName);
      if (roleInfo.getCountRoleByRoleName(roleName) == 0) {
         resource.setResult("available");
      } else {
         resource.setResult("unavailable");
      }

      return resource;
   }

   public V2CommonIds roleUsers(String roleId) throws Exception {
      RoleInfo roleInfo = RoleInfoImpl.getInstance();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      V2CommonIds Ids = new V2CommonIds();
      List userIds = new ArrayList();
      List userRoleList = roleInfo.getMappedUseListrByRoleId(Long.parseLong(roleId));
      int size = userRoleList.size();
      String orgName = userContainer.getUser().getOrganization();
      if (size > 0) {
         new HashMap();

         for(int i = 0; i < userRoleList.size(); ++i) {
            if (orgName.equals("ROOT")) {
               userIds.add((String)((Map)userRoleList.get(i)).get("user_id"));
            } else if (((Map)userRoleList.get(i)).get("organization").toString().equals(orgName)) {
               userIds.add((String)((Map)userRoleList.get(i)).get("user_id"));
            }
         }

         Ids.setIds(userIds);
      }

      return Ids;
   }

   public List assignableRoles() throws Exception {
      List resources = new ArrayList();
      User user = SecurityUtils.getLoginUser();
      Long organizationId = user.getRoot_group_id();
      String scope = RoleUtils.getUserScope(user);
      UserInfo userInfo = UserInfoImpl.getInstance();
      RoleInfo roleInfo = RoleInfoImpl.getInstance();
      boolean bRegLicLfd = false;
      boolean bool_reg_lic_soc = false;
      boolean bool_reg_lic_android = false;
      boolean bool_reg_lic_sinage = false;
      boolean bool_reg_lic_lite = false;
      boolean bool_reg_lic_rms = false;
      boolean rmMode = false;
      if (CommonConfig.get("BOOL_REG_LIC_LFD") != null) {
         bRegLicLfd = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LFD"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SOC") != null) {
         bool_reg_lic_soc = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SOC"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_ANDROID") != null) {
         bool_reg_lic_android = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_ANDROID"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_SIGNAGE") != null) {
         bool_reg_lic_sinage = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_SIGNAGE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_LITE") != null) {
         bool_reg_lic_lite = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_LITE"));
      }

      if (CommonConfig.get("BOOL_REG_LIC_RMS") != null) {
         bool_reg_lic_rms = Boolean.parseBoolean(CommonConfig.get("BOOL_REG_LIC_RMS"));
      }

      if (bool_reg_lic_rms && !bRegLicLfd && !bool_reg_lic_soc && !bool_reg_lic_android && !bool_reg_lic_sinage && !bool_reg_lic_lite) {
         rmMode = true;
      }

      Map map = new HashMap();
      map.put("root_group_id", organizationId);
      map.put("scope", scope);
      map.put("rmMode", rmMode);
      List roleList = roleInfo.getChildRoleListByRootGroupId(map);
      Role loginUserRole = roleInfo.getRoleByUserId(user.getUser_id());
      int i;
      if (RoleUtils.isServerAdminRole(loginUserRole.getRole_name())) {
         Role serverAdminRole = roleInfo.getAllByRoleName("Server Administrator");
         ((List)roleList).add(0, serverAdminRole);
      } else {
         if (user.isMu()) {
            organizationId = userInfo.getCurMngOrgId(user.getUser_id());
         }

         List tempRoleList = new ArrayList();

         for(i = 0; i < ((List)roleList).size(); ++i) {
            new V2RoleListQueryDataResource();
            Long root_group_id = (Long)((Map)((List)roleList).get(i)).get("root_group_id");
            if (root_group_id == 0L || root_group_id == organizationId) {
               String is_default = (String)((Map)((List)roleList).get(i)).get("is_default");
               String organization = (String)((Map)((List)roleList).get(i)).get("organization");
               Long role_id = (Long)((Map)((List)roleList).get(i)).get("role_id");
               String role_name = (String)((Map)((List)roleList).get(i)).get("role_name");
               String role_scope = (String)((Map)((List)roleList).get(i)).get("scope");
               String server_mode = (String)((Map)((List)roleList).get(i)).get("server_mode");
               Long user_count = (Long)((Map)((List)roleList).get(i)).get("user_count");
               Role role = new Role();
               role.setIs_default(is_default);
               role.setOrganization(organization);
               role.setRole_id(role_id);
               role.setRole_name(role_name);
               role.setRoot_group_id(root_group_id);
               role.setScope(role_scope);
               role.setServer_mode(server_mode);
               role.setUser_count(user_count);
               tempRoleList.add(role);
            }
         }

         roleList = tempRoleList;
      }

      if (!loginUserRole.getRole_name().equals("Administrator") && !loginUserRole.getRole_name().equals("Server Administrator")) {
         int roleListSize = ((List)roleList).size();

         for(i = 0; i < roleListSize; ++i) {
            if (((Role)((List)roleList).get(i)).getRole_name().equals("Administrator")) {
               ((List)roleList).remove(i);
               break;
            }
         }
      }

      ObjectMapper mapper = new ObjectMapper();
      mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
      mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
      mapper.configure(DeserializationFeature.FAIL_ON_MISSING_CREATOR_PROPERTIES, false);

      for(i = 0; i < ((List)roleList).size(); ++i) {
         V2RoleListQueryDataResource data = new V2RoleListQueryDataResource();
         if (RoleUtils.isServerAdminRole(loginUserRole.getRole_name()) && i == 0) {
            data.setRoleId(((Role)((List)roleList).get(i)).getRole_id());
            data.setRoleName(((Role)((List)roleList).get(i)).getRole_name());
            data.setIsDefault(((Role)((List)roleList).get(i)).getIs_default());
            data.setScope(((Role)((List)roleList).get(i)).getScope());
            data.setOrganizationId(((Role)((List)roleList).get(i)).getRoot_group_id());
         } else {
            String jsonString = mapper.writeValueAsString(((List)roleList).get(i));
            Map tempMap = (Map)mapper.readValue(jsonString, new TypeReference() {
            });
            Map tempMap = ConvertUtil.convertMap(tempMap);
            data = (V2RoleListQueryDataResource)mapper.convertValue(tempMap, V2RoleListQueryDataResource.class);
            data.setOrganizationId((long)(Integer)tempMap.get("rootGroupId"));
         }

         resources.add(data);
      }

      return resources;
   }
}
