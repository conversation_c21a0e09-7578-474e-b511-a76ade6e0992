package com.samsung.magicinfo.webauthor2.service.upload;

import com.samsung.magicinfo.webauthor2.exception.service.FileItemValidationException;
import com.samsung.magicinfo.webauthor2.model.FileMetaInfo;
import com.samsung.magicinfo.webauthor2.model.ImageDimension;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import com.samsung.magicinfo.webauthor2.service.FileMetaService;
import com.samsung.magicinfo.webauthor2.service.thumbnail.ThumbnailService;
import com.samsung.magicinfo.webauthor2.util.FileHashUtil;
import com.samsung.magicinfo.webauthor2.util.ImageDimensionUtil;
import com.samsung.magicinfo.webauthor2.util.PlayTimeUtil;
import com.samsung.magicinfo.webauthor2.util.SupportedFormatUtils;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public final class UploadHelperServiceImpl implements UploadHelperService {
  private static final Logger logger = LoggerFactory.getLogger(UploadHelperServiceImpl.class);
  
  private ThumbnailService thumbnailService;
  
  private FileMetaService fileMetaService;
  
  @Autowired
  public UploadHelperServiceImpl(ThumbnailService thumbnailService, FileMetaService fileMetaService) {
    this.thumbnailService = thumbnailService;
    this.fileMetaService = fileMetaService;
  }
  
  public final List<MediaSource> prepareFilesToUpload(MultipartFile fileItem, Path workingDirectory) throws IOException, FileItemValidationException {
    List<MediaSource> mediaSources = new ArrayList<>();
    Path filePath = Paths.get(workingDirectory.toString(), new String[] { fileItem.getOriginalFilename() });
    FileUtils.copyInputStreamToFile(fileItem.getInputStream(), filePath.toFile());
    MediaSource mainFile = prepareMainMediaSource(filePath);
    mediaSources.add(mainFile);
    if (isMovieOrImage(mainFile)) {
      MediaSource thumbnail = prepareThumbnailMediaSource(workingDirectory, mainFile);
      mediaSources.add(thumbnail);
    } 
    return mediaSources;
  }
  
  public final List<MediaSource> prepareFilesToUpload(Path filePath, Path workingDirectory) throws IOException, FileItemValidationException {
    List<MediaSource> mediaSources = new ArrayList<>();
    MediaSource mainFile = prepareMainMediaSource(filePath);
    mediaSources.add(mainFile);
    if (isMovieOrImage(mainFile)) {
      MediaSource thumbnail = prepareThumbnailMediaSource(workingDirectory, mainFile);
      mediaSources.add(thumbnail);
    } 
    return mediaSources;
  }
  
  public MediaSource getDetailsFromFile(Path path) throws IOException {
    return prepareMainMediaSource(path);
  }
  
  private String getShortNameForTitle(String originalFileName) {
    String shortenFileName;
    int magicinfo_server_file_name_max_length = 55;
    int fileNameLength = originalFileName.length();
    String fileExt = FilenameUtils.getExtension(originalFileName);
    if (fileNameLength - fileExt.length() - 1 > 55) {
      shortenFileName = originalFileName.substring(0, 55);
    } else {
      shortenFileName = FilenameUtils.getName(originalFileName);
    } 
    return shortenFileName;
  }
  
  private MediaSource prepareMainMediaSource(Path filePath) throws IOException {
    MediaSource mainFile = new MediaSource();
    mainFile.setTitle(getShortNameForTitle(FilenameUtils.getName(filePath.toString())));
    mainFile.setFileName(FilenameUtils.getName(filePath.toString()));
    mainFile.setFileType(FilenameUtils.getExtension(filePath.toString()));
    MediaType mediaType = SupportedFormatUtils.getMediaTypeForExtension(FilenameUtils.getExtension(filePath.getFileName().toString()));
    if (mediaType != null)
      mainFile.setMediaType(mediaType); 
    mainFile.setMediaSize(Files.size(filePath));
    mainFile.setFileId(UUID.randomUUID().toString().toUpperCase());
    mainFile.setFileHash(FileHashUtil.getHash(filePath.toFile()));
    mainFile.setPath(filePath.toString());
    fillDimensions(mainFile, filePath);
    return mainFile;
  }
  
  private MediaSource prepareThumbnailMediaSource(Path workingDirectory, MediaSource mainFile) throws IOException {
    MediaSource thumbnail = new MediaSource();
    Path thumbnailPath = this.thumbnailService.createContentThumbnail(mainFile, workingDirectory);
    thumbnail.setPath(thumbnailPath.toString());
    thumbnail.setTitle(getShortNameForTitle(FilenameUtils.getName(thumbnailPath.toString())));
    thumbnail.setFileName(FilenameUtils.getName(thumbnailPath.toString()));
    thumbnail.setFileType("thumbnail");
    thumbnail.setMediaType(MediaType.IMAGE);
    thumbnail.setMediaSize(Files.size(thumbnailPath));
    thumbnail.setFileId(UUID.randomUUID().toString().toUpperCase());
    thumbnail.setFileHash(FileHashUtil.getHash(thumbnailPath.toFile()));
    fillDimensions(thumbnail, thumbnailPath);
    return thumbnail;
  }
  
  private boolean isMovieOrImage(MediaSource mainFile) {
    return (mainFile.getMediaType() == MediaType.MOVIE || mainFile.getMediaType() == MediaType.IMAGE);
  }
  
  private void fillDimensions(MediaSource mediaSource, Path path) {
    MediaType mediaType = mediaSource.getMediaType();
    if (mediaType == null)
      return; 
    if (mediaType == MediaType.IMAGE) {
      try {
        ImageDimension imageDimensions = ImageDimensionUtil.getImageDimensions(path);
        mediaSource.setMediaWidth(imageDimensions.getWidth());
        mediaSource.setMediaHeight(imageDimensions.getHeight());
      } catch (IOException ex) {
        logger.error(ex.getMessage(), ex);
      } 
    } else if (mediaType == MediaType.MOVIE) {
      FileMetaInfo fileMeta = this.fileMetaService.getFileMeta(path);
      mediaSource.setMediaDuration(PlayTimeUtil.convertPlayTime((String)fileMeta.getLength().or("00:00:00")).doubleValue());
      mediaSource.setMediaWidth(Integer.parseInt((String)fileMeta.getWidth().or("0")));
      mediaSource.setMediaHeight(Integer.parseInt((String)fileMeta.getHeight().or("0")));
    } else if (mediaType == MediaType.SOUND) {
      FileMetaInfo fileMeta = this.fileMetaService.getFileMeta(path);
      mediaSource.setMediaDuration(PlayTimeUtil.convertPlayTime((String)fileMeta.getLength().or("00:00:00")).doubleValue());
    } 
  }
}
