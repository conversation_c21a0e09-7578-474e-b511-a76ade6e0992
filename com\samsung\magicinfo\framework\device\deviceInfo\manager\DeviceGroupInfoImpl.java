package com.samsung.magicinfo.framework.device.deviceInfo.manager;

import com.samsung.common.cache.CacheFactory;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceGroupDao;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.rms.model.DeviceGroupFilter;
import java.sql.SQLException;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Deque;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class DeviceGroupInfoImpl implements DeviceGroupInfo {
   private static DeviceGroupInfoImpl instance;
   private static final String CACHE_DEVICE_KEY = "MEMORY_DEVICE_GROUP_INFO";
   static Logger logger;
   private int daoType = 0;
   private DeviceGroupDao dao = null;
   private DeviceGroupDao liteDao = null;
   private static DeviceGroupDao groupDao;

   public static DeviceGroupInfo getInstance(SqlSession session) {
      return new DeviceGroupInfoImpl(session);
   }

   public static DeviceGroupInfo getInstance() {
      if (instance == null) {
         Class var0 = DeviceGroupInfo.class;
         synchronized(DeviceGroupInfo.class) {
            instance = new DeviceGroupInfoImpl((SqlSession)null);
         }
      }

      return instance;
   }

   private DeviceGroupInfoImpl(SqlSession session) {
      super();
      if (this.dao == null) {
         this.dao = new DeviceGroupDao(session);
      }

   }

   private static void initCacheDeviceGroup() {
      if (groupDao == null) {
         groupDao = new DeviceGroupDao();
      }

      HashMap mapTmp = new HashMap();

      try {
         List groupList = groupDao.getAllDeviceGroups();
         Iterator var2 = groupList.iterator();

         DeviceGroup current;
         while(var2.hasNext()) {
            current = (DeviceGroup)var2.next();
            mapTmp.put(current.getGroup_id(), current);
         }

         var2 = groupList.iterator();

         while(var2.hasNext()) {
            current = (DeviceGroup)var2.next();
            Long parentGroupId = current.getP_group_id();
            if (parentGroupId != null) {
               DeviceGroup parentGroup = (DeviceGroup)mapTmp.get(parentGroupId);
               if (parentGroup != null) {
                  current.setParentGroup(parentGroup);
                  parentGroup.addChildGroup(current);
                  mapTmp.put(parentGroupId, parentGroup);
                  mapTmp.put(current.getGroup_id(), current);
               }
            }
         }
      } catch (Exception var6) {
         mapTmp = null;
      }

      updateCache(mapTmp);
   }

   public void updateCacheDeviceGroup() {
      initCacheDeviceGroup();
   }

   public void updateCacheDeviceGroup(Long pGroupId, DeviceGroup deviceGroup) {
      logger.info("[MagicInfo_DeviceGroup] update cache for device groups");
      Map groupList = this.getCacheDeviceGroup();
      if (groupList != null) {
         DeviceGroup pGroup = (DeviceGroup)groupList.get(pGroupId);
         if (pGroup != null) {
            pGroup.addChildGroup(deviceGroup);
            deviceGroup.setTotal_count(0L);
            groupList.put(pGroup.getGroup_id(), pGroup);
            groupList.put(deviceGroup.getGroup_id(), deviceGroup);
            updateCache(groupList);
         }
      }

   }

   private static void updateCache(Map groupList) {
      try {
         CacheFactory.getCache().set("MEMORY_DEVICE_GROUP_INFO", groupList);
      } catch (Exception var2) {
         logger.error("", var2);
      }

   }

   public Map getCacheDeviceGroup() {
      Map deviceGroup = null;

      try {
         Object obj = CacheFactory.getCache().get("MEMORY_DEVICE_GROUP_INFO");
         deviceGroup = (Map)obj;
      } catch (Exception var3) {
         logger.error("[MagicInfo_DeviceGroup] get cache groups error : " + var3.getMessage());
         deviceGroup = null;
      }

      return deviceGroup;
   }

   public int addGroup(DeviceGroup deviceGroup) throws SQLException, ConfigException {
      return this.daoType == 0 ? this.dao.addGroup(deviceGroup) : this.liteDao.addGroup(deviceGroup);
   }

   public boolean addGroupForOrg(String strOrgName, String userId) throws SQLException, ConfigException {
      return this.daoType == 0 ? this.dao.addGroupForOrg(strOrgName, userId) : this.liteDao.addGroupForOrg(strOrgName, userId);
   }

   public boolean canDeleteOrgGroups(String strOrg) throws SQLException {
      return this.daoType == 0 ? this.dao.canDeleteOrgGroups(strOrg) : this.liteDao.canDeleteOrgGroups(strOrg);
   }

   public boolean canDeleteDeviceGroup(int groupId) throws SQLException {
      return this.dao.canDeleteDeviceGroup(groupId);
   }

   public boolean deleteChildGroupAndDevice(Long groupId, String userId, HttpServletRequest request) throws SQLException, ConfigException {
      return this.daoType == 0 ? this.dao.deleteChildGroupAndDevice(groupId, userId) : this.liteDao.deleteChildGroupAndDevice(groupId, userId);
   }

   public boolean deleteOrgGroups(String strOrg, String userId, HttpServletRequest request) throws SQLException, ConfigException {
      long orgGroupId = this.getOrganGroupIdByName(strOrg);
      return this.deleteChildGroupAndDevice(orgGroupId, userId, request);
   }

   public boolean delGroup(int group_id) throws SQLException {
      return this.daoType == 0 ? this.dao.delGroup(group_id) : this.liteDao.delGroup(group_id);
   }

   public List getChildDeviceIdList(int group_id, boolean recursive) throws SQLException {
      return this.daoType == 0 ? this.dao.getChildDeviceIdList(group_id, recursive) : this.liteDao.getChildDeviceIdList(group_id, recursive);
   }

   public List getChildDeviceIdList(int group_id) throws SQLException {
      return this.daoType == 0 ? this.dao.getChildDeviceIdList(group_id) : this.liteDao.getChildDeviceIdList(group_id);
   }

   public List getChildDeviceIdListByOrganName(String organName) throws SQLException {
      return this.dao.getChildDeviceIdListByOrganName(organName);
   }

   public List getChildDeviceList(int group_id, boolean recursive) throws SQLException {
      return this.daoType == 0 ? this.dao.getChildDeviceList(group_id, recursive) : this.liteDao.getChildDeviceList(group_id, recursive);
   }

   public List getChildDeviceList(int group_id) throws SQLException {
      return this.daoType == 0 ? this.dao.getChildDeviceList(group_id) : this.liteDao.getChildDeviceList(group_id);
   }

   public List getChildGroupIdList(int group_id, boolean recursive) throws SQLException {
      return this.daoType == 0 ? this.dao.getChildGroupIdList(group_id, recursive) : this.liteDao.getChildGroupIdList(group_id, recursive);
   }

   public List getChildGroupList(int group_id, boolean recursive) throws SQLException {
      return this.daoType == 0 ? this.dao.getChildGroupList(group_id, recursive) : this.liteDao.getChildGroupList(group_id, recursive);
   }

   public List getChildGroupListWithPermission(int group_id, String user_id, boolean recursive) throws SQLException {
      return this.daoType == 0 ? this.dao.getChildGroupListWithPermission(group_id, user_id, recursive) : this.liteDao.getChildGroupListWithPermission(group_id, user_id, recursive);
   }

   public List getChildGroupListByGroupType(int group_id, boolean recursive, String groupType) throws SQLException {
      return this.daoType == 0 ? this.dao.getChildGroupListByGroupType(group_id, recursive, groupType) : null;
   }

   public List getChildGroupListByGroupTypeForSchedule(int group_id, boolean recursive, String groupType, ArrayList auth_tree_list, String userId, Long minPriority) throws SQLException {
      return this.daoType == 0 ? this.dao.getChildGroupListByGroupTypeForSchedule(group_id, recursive, groupType, auth_tree_list, userId, minPriority) : null;
   }

   public String getDefaultProgramId(long groupId) throws SQLException {
      return this.daoType == 0 ? this.dao.getDefaultProgramId(groupId) : this.liteDao.getDefaultProgramId(groupId);
   }

   public int getDeviceGroupForUser(String strOrg) throws SQLException {
      return this.daoType == 0 ? this.dao.getDeviceGroupForUser(strOrg) : this.liteDao.getDeviceGroupForUser(strOrg);
   }

   public String getDeviceGroupRoot(int groupId) throws SQLException {
      return this.daoType == 0 ? this.dao.getDeviceGroupRoot(groupId) : this.liteDao.getDeviceGroupRoot(groupId);
   }

   public int getDeviceOrgGroupId(int groupId) throws SQLException {
      return this.daoType == 0 ? this.dao.getDeviceOrgGroupId(groupId) : this.liteDao.getDeviceOrgGroupId(groupId);
   }

   public DeviceGroup getGroup(int group_id) throws SQLException {
      DeviceGroup group = null;
      Map groupList = this.getCacheDeviceGroup();
      if (groupList != null) {
         group = (DeviceGroup)groupList.get((long)group_id);
      }

      if (group == null) {
         if (this.daoType == 0) {
            group = this.dao.getGroup(group_id);
         } else {
            group = this.liteDao.getGroup(group_id);
         }
      }

      return group;
   }

   public List getGroupType(int group_id) throws SQLException {
      return this.dao.getGroupType(group_id);
   }

   public DeviceGroup getGroupByDeviceId(String device_id) throws SQLException {
      return this.daoType == 0 ? this.dao.getGroupByDeviceId(device_id) : this.liteDao.getGroupByDeviceId(device_id);
   }

   public long getGroupIdByOrgBasic(String organGroupName, String childGroupName) throws SQLException {
      return this.daoType == 0 ? this.dao.getGroupIdByOrgBasic(organGroupName, childGroupName) : this.liteDao.getGroupIdByOrgBasic(organGroupName, childGroupName);
   }

   public List getGroupList() throws SQLException {
      return this.daoType == 0 ? this.dao.getGroupList() : this.liteDao.getGroupList();
   }

   public List getGroupList(DeviceGroupFilter params) throws SQLException {
      return this.dao.getGroupList(params);
   }

   public int getGroupListCnt(DeviceGroupFilter params) throws SQLException {
      return this.dao.getGroupListCnt(params);
   }

   public Map getGroupNameByDeviceId(String deviceId) throws SQLException {
      return this.daoType == 0 ? this.dao.getGroupNameByDeviceId(deviceId) : this.liteDao.getGroupNameByDeviceId(deviceId);
   }

   public List getGroupsForOrg(String strOrg) throws SQLException {
      return this.daoType == 0 ? this.dao.getGroupsForOrg(strOrg) : this.liteDao.getGroupsForOrg(strOrg);
   }

   public String getMessageGroupRoot(int groupId) throws SQLException {
      return this.daoType == 0 ? this.dao.getMessageGroupRoot(groupId) : this.liteDao.getMessageGroupRoot(groupId);
   }

   public long getOrganGroupIdByName(String orgGroupName) throws SQLException {
      return this.daoType == 0 ? this.dao.getOrganGroupIdByName(orgGroupName) : this.liteDao.getOrganGroupIdByName(orgGroupName);
   }

   public Map getOrgGroupId(String groupName) throws SQLException {
      return this.daoType == 0 ? this.dao.getOrgGroupId(groupName) : this.liteDao.getOrgGroupId(groupName);
   }

   public String getOrgNameByGroupId(long groupId) throws SQLException {
      return this.daoType == 0 ? this.dao.getOrgNameByGroupId(groupId) : this.liteDao.getOrgNameByGroupId(groupId);
   }

   public Long getOrgIdByGroupId(long groupId) throws SQLException {
      return this.daoType == 0 ? this.dao.getOrgIdByGroupId(groupId) : this.liteDao.getOrgIdByGroupId(groupId);
   }

   public int getParentGroupId(int group_id) throws SQLException {
      return this.daoType == 0 ? this.dao.getParentGroupId(group_id) : this.liteDao.getParentGroupId(group_id);
   }

   public String getProgramGroupRoot(int groupId) throws SQLException {
      return this.daoType == 0 ? this.dao.getProgramGroupRoot(groupId) : this.liteDao.getProgramGroupRoot(groupId);
   }

   public boolean moveGroup(int group_id, int new_parent_group_id, Long change_in_group_depth) throws SQLException {
      return this.daoType == 0 ? this.dao.moveGroup(group_id, new_parent_group_id, change_in_group_depth) : this.liteDao.moveGroup(group_id, new_parent_group_id, change_in_group_depth);
   }

   public boolean setDefaultProgramId(long groupId, String programId) throws SQLException {
      return this.daoType == 0 ? this.dao.setDefaultProgramId(groupId, programId) : this.liteDao.setDefaultProgramId(groupId, programId);
   }

   public boolean setGroup(DeviceGroup deviceGroup) throws SQLException, ConfigException {
      return this.daoType == 0 ? this.dao.setGroup(deviceGroup) : this.liteDao.setGroup(deviceGroup);
   }

   public boolean setOrgName(String originName, String newName) throws SQLException {
      return this.daoType == 0 ? this.dao.setOrgName(originName, newName) : this.liteDao.setOrgName(originName, newName);
   }

   public List getGroupIdByName(String groupName) throws SQLException {
      return this.daoType == 0 ? this.dao.getGroupIdByName(groupName) : this.liteDao.getGroupIdByName(groupName);
   }

   public DeviceGroup getDeviceTopGroup(int groupId) throws SQLException {
      return this.daoType == 0 ? this.dao.getDeviceTopGroup(groupId) : null;
   }

   public boolean setDeviceGroupType(int groupId, String groupType) throws SQLException {
      return this.setDeviceGroupType(groupId, groupType, (SqlSession)null);
   }

   public boolean setDeviceGroupType(int groupId, String groupType, SqlSession session) throws SQLException {
      if (this.daoType != 0) {
         return false;
      } else {
         boolean rtn = this.dao.setDeviceGroupType(groupId, groupType);
         ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
         if (groupType != null && groupType.equals("iPLAYER")) {
            scheduleInfo.updateDefaultProgramDeviceType((long)groupId, "SPLAYER", session);
         } else {
            scheduleInfo.updateDefaultProgramDeviceType((long)groupId, "SPLAYER", session);
         }

         return rtn;
      }
   }

   public boolean setAllDeviceGroupType(int groupId, String groupType) throws SQLException {
      if (this.daoType != 0) {
         return false;
      } else {
         DeviceGroup deviceGroup = this.dao.getGroup(groupId);
         int topGroupId = false;
         int currGroupId = false;
         if (deviceGroup != null && (deviceGroup.getGroup_type() == null || deviceGroup.getGroup_type().equals(""))) {
            DeviceGroup topDeviceGroup = this.dao.getDeviceTopGroup(groupId);
            if (topDeviceGroup == null) {
               this.setDeviceGroupType(groupId, groupType);
               logger.info("TopDeviceGroup none!!! set type. groupId=" + groupId + ",type=" + groupType);
               return true;
            }

            int topGroupId = topDeviceGroup.getGroup_id().intValue();
            if (topDeviceGroup.getGroup_type() != null && !topDeviceGroup.getGroup_type().equals("") && topDeviceGroup.getGroup_type().equals(groupType)) {
               this.setDeviceGroupType(groupId, groupType);
               logger.info("set CurrentOnly, groupId=" + groupId + ",groupType=" + groupType);
            } else {
               this.setDeviceGroupType(topGroupId, groupType);
               List groupIdList = this.getChildGroupIdList(topGroupId, true);

               for(int i = 0; i < groupIdList.size(); ++i) {
                  int currGroupId = ((Long)groupIdList.get(i)).intValue();
                  this.setDeviceGroupType(currGroupId, groupType);
                  logger.info("set All, groupId=" + currGroupId + ",groupType=" + groupType);
               }
            }
         }

         return true;
      }
   }

   public int getCntDeviceInDeviceGroup(int groupId) throws SQLException {
      return this.daoType == 0 ? this.dao.getCntDeviceInDeviceGroup(groupId) : this.liteDao.getCntDeviceInDeviceGroup(groupId);
   }

   public int getCntDeviceInDeviceGroupExceptFor(int groupId, List deviceTypeList) throws SQLException {
      return this.dao.getCntDeviceInDeviceGroupExceptFor(groupId, deviceTypeList);
   }

   public int getCntDeviceInLiteDeviceGroup(int groupId) throws SQLException {
      return this.daoType == 0 ? this.dao.getCntDeviceInLiteDeviceGroup(groupId) : this.liteDao.getCntDeviceInDeviceGroup(groupId);
   }

   public int getCntDeviceInVwlConsoleDevice(String consoleId) throws SQLException {
      return this.dao.getCntDeviceInVwlConsoleDevice(consoleId);
   }

   public String getParentGroupType(int group_id) throws SQLException {
      DeviceGroup pGroup = this.getGroup(group_id);
      Long p_group_depth = pGroup.getGroup_depth();
      if (p_group_depth.intValue() <= 1) {
         return "";
      } else {
         String p_group_type = pGroup.getGroup_type();
         if (p_group_type.equals("")) {
            int p_group_id = this.getParentGroupId(group_id);
            return this.getParentGroupType(p_group_id);
         } else {
            return p_group_type;
         }
      }
   }

   public List getRedundancyGroups() throws SQLException {
      return this.dao.getRedundancyGroups();
   }

   public boolean isRedundancyGroup(int GroupId) throws SQLException {
      return this.dao.isRedundancyGroup(GroupId);
   }

   public List getRedundantDeviceIdbyGroupId(int GroupId) throws SQLException {
      return this.dao.getRedundantDeviceIdbyGroupId(GroupId);
   }

   public boolean setIsRedundancy(long groupId, boolean isRedundancy) throws SQLException {
      return this.dao.setIsRedundancy(groupId, isRedundancy);
   }

   public String getVwlLayoutIdByGroupId(String groupId) throws SQLException {
      return this.dao.getVwlLayoutIdByGroupId(groupId);
   }

   public List getVwlLayoutGroupId() throws SQLException {
      return this.dao.getVwlLayoutGroupId();
   }

   public List getAllVWLLayoutGroupList() throws SQLException {
      return this.dao.getAllVWLLayoutGroupList();
   }

   public boolean setVwtId(String deviceId, String vwtId) throws SQLException {
      return this.dao.setVwtId(deviceId, vwtId);
   }

   public boolean isVwlGroup(String groupId) throws SQLException {
      return this.dao.isVwlGroup(groupId);
   }

   public String getGroupNameByVwtId(String vwtId) throws SQLException {
      return this.dao.getGroupNameByVwtId(vwtId);
   }

   public boolean isVWLLayoutGroup(String deviceGroupIds) throws SQLException {
      return this.dao.isVWLLayoutGroup(deviceGroupIds);
   }

   public Long getMinimumPriority(String deviceGroupIds) throws SQLException {
      return this.dao.getMinimumPriority(deviceGroupIds);
   }

   public Long getPriority(String deviceType, Float deviceTypeVersion) throws SQLException {
      return this.dao.getPriority(deviceType, deviceTypeVersion);
   }

   public String getDeviceTypeByMinimumPriority(Long minimumPriority) throws SQLException {
      return this.dao.getDeviceTypeByMinimumPriority(minimumPriority);
   }

   public Float getDeviceTypeVersionByMinimumPriority(Long minimumPriority) throws SQLException {
      return this.dao.getDeviceTypeVersionByMinimumPriority(minimumPriority);
   }

   public boolean cancelVwlGroup(String groupId) throws SQLException {
      return this.dao.cancelVwlGroup(groupId);
   }

   public boolean updateDeviceGroupPriority(Long groupPriority, long groupId) throws SQLException {
      return this.dao.updateDeviceGroupPriority(groupPriority, groupId);
   }

   public boolean updateDeviceGroupPriority(Long groupPriority, long groupId, SqlSession session) throws SQLException {
      DeviceGroupDao deviceDao = new DeviceGroupDao(session);
      return deviceDao.updateDeviceGroupPriority(groupPriority, groupId);
   }

   public String getGroupNameByGroupId(long groupId) throws SQLException {
      return this.daoType == 0 ? this.dao.getGroupNameByGroupId(groupId) : this.liteDao.getGroupNameByGroupId(groupId);
   }

   public List getScheduleMappingDeviceGroupAuth(String programId, String userId, boolean include) {
      return this.daoType == 0 ? this.dao.getScheduleMappingDeviceGroupAuth(programId, userId, include) : null;
   }

   public List getMessageMappingDeviceGroupAuth(String programId, String userId, boolean include) {
      return this.daoType == 0 ? this.dao.getMessageMappingDeviceGroupAuth(programId, userId, include) : null;
   }

   public List getEventMappingDeviceGroupAuth(String programId, String userId, boolean include) {
      return this.daoType == 0 ? this.dao.getEventMappingDeviceGroupAuth(programId, userId, include) : null;
   }

   public List getAuthDeviceGroupList(String userId) throws SQLException {
      return this.dao.getAuthDeviceGroupList(userId);
   }

   public String getPermissionsDeviceGroup(String userId) throws SQLException {
      List deviceIdList = this.dao.getPermissionsDeviceGroup(userId);
      StringBuffer groupIdStr = new StringBuffer();

      for(int i = 0; i < deviceIdList.size(); ++i) {
         groupIdStr.append(((DeviceGroup)deviceIdList.get(i)).getGroup_id());
         if (i != deviceIdList.size() - 1) {
            groupIdStr.append(",");
         }
      }

      return groupIdStr.toString();
   }

   public List getPermissionsDeviceGroupList(String userId) throws SQLException {
      return this.dao.getPermissionsDeviceGroup(userId);
   }

   public String getOrgId(String orgName) throws SQLException {
      List getOrgIdList = this.dao.getOrgId(orgName);
      StringBuffer groupIdStr = new StringBuffer();

      for(int i = 0; i < getOrgIdList.size(); ++i) {
         groupIdStr.append(((DeviceGroup)getOrgIdList.get(i)).getGroup_id());
         if (i != getOrgIdList.size() - 1) {
            groupIdStr.append(",");
         }
      }

      return groupIdStr.toString();
   }

   public List getOrganizationGroup() throws SQLException {
      return this.dao.getOrganizationGroup();
   }

   public List getDeviceTypesMapGroup(Long groupId) throws SQLException {
      return this.dao.getDeviceTypesMapGroup(groupId);
   }

   public boolean setGroupTypeDefault(Long groupId) throws SQLException {
      return this.dao.setGroupTypeDefault(groupId);
   }

   public List getAllGroupName() throws SQLException {
      return this.dao.getAllGroupName();
   }

   public List getGroupById(String type, String cmd, long id, boolean deviceAuth) throws SQLException {
      if (type.equals("PREMIUM")) {
         if (cmd.equals("DEVICE")) {
            if (deviceAuth) {
               return this.dao.getAuthGroupById(cmd, id);
            }

            return this.dao.getGroupById(cmd, id);
         }

         if (!cmd.equals("CONTENT") && cmd.equals("SCHEDULE")) {
         }
      }

      return null;
   }

   public List getGroupByIdWithPermission(String type, String cmd, long id, String userId, boolean deviceAuth) throws SQLException {
      if (type.equals("PREMIUM")) {
         if (cmd.equals("DEVICE")) {
            if (deviceAuth) {
               return this.dao.getAuthGroupById(cmd, id);
            }

            return this.dao.getGroupByIdWithPermission(cmd, id, userId);
         }

         if (!cmd.equals("CONTENT") && cmd.equals("SCHEDULE")) {
         }
      }

      return null;
   }

   public List getGroupById(String cmd, List userGroups) throws SQLException {
      return cmd.equals("DEVICE") ? this.dao.getGroupById(userGroups) : null;
   }

   public List getRootGroupById(String type, String cmd, String organization) throws SQLException {
      if (type.equals("PREMIUM")) {
         if (cmd.equals("DEVICE")) {
            return this.dao.getRootGroupById(cmd, organization);
         }

         if (!cmd.equals("CONTENT") && cmd.equals("SCHEDULE")) {
         }
      }

      return null;
   }

   public List getVwlGroupById(String type, String cmd, long id) throws SQLException {
      if (type.equals("VWL")) {
         if (cmd.equals("DEVICE")) {
            return this.dao.getVwlGroupById(cmd, id);
         }

         if (!cmd.equals("CONTENT") && cmd.equals("SCHEDULE")) {
         }
      }

      return null;
   }

   public List getVwlRootGroupById(String type, String cmd, String organization) throws SQLException {
      if (type.equals("VWL")) {
         if (cmd.equals("DEVICE")) {
            return this.dao.getVwlRootGroupById(cmd, organization);
         }

         if (!cmd.equals("CONTENT") && cmd.equals("SCHEDULE")) {
         }
      }

      return null;
   }

   public List getAdminVwlRootGroupById(String type, String cmd, long id) throws SQLException {
      if (type.equals("VWL")) {
         if (cmd.equals("DEVICE")) {
            return this.dao.getAdminVwlRootGroupById(cmd, id);
         }

         if (!cmd.equals("CONTENT") && cmd.equals("SCHEDULE")) {
         }
      }

      return null;
   }

   public boolean getDeviceAuthor(long groupId, String userId) throws SQLException {
      return this.dao.getDeviceAuthor(groupId, userId);
   }

   public boolean isSyncPlayGroup(int devGroupId) throws SQLException {
      return this.dao.isSyncPlayGroup(devGroupId);
   }

   public int checkChildPermissions2(String userId, Long groupId) throws SQLException {
      return this.dao.checkChildPermissions2(userId, groupId);
   }

   public List getDeviceModelName(long groupId) throws SQLException {
      return this.dao.getDeviceModelName(groupId);
   }

   public String getModelCountInfo(long groupId) throws SQLException {
      return this.dao.getModelCountInfo(groupId);
   }

   public long getDiskSpaceRepository(String groupIds) throws SQLException {
      return this.dao.getDiskSpaceRepository(groupIds);
   }

   public List getAllDeviceGroupsByGroupName(long groupId, String groupName) throws SQLException {
      Map groupList = this.getCacheDeviceGroup();
      if (groupList == null) {
         return this.dao.getAllDeviceGroups(groupId, groupName);
      } else {
         DeviceGroup rootGroup = (DeviceGroup)groupList.get(groupId);
         List list = new ArrayList();
         Deque q = new ArrayDeque();
         q.addLast(rootGroup);

         while(true) {
            List children;
            do {
               if (q.isEmpty()) {
                  if (list.size() > 0 && ((DeviceGroup)list.get(0)).getGroup_id() == 0L) {
                     list.remove(0);
                  }

                  return list;
               }

               DeviceGroup group = (DeviceGroup)q.removeLast();
               if (StringUtils.isEmpty(groupName)) {
                  list.add(group);
               } else if (group.getGroup_depth() > 1L && group.getGroup_name().toUpperCase().indexOf(groupName.toUpperCase()) > -1) {
                  list.add(group);
               }

               children = group.getChildrenGroup();
            } while(children == null);

            Iterator var10 = children.iterator();

            while(var10.hasNext()) {
               DeviceGroup child = (DeviceGroup)var10.next();
               q.addLast(child);
            }
         }
      }
   }

   public List getAllDeviceGroupsByGroupName(List groupIds, String groupName) throws SQLException {
      Map groupList = this.getCacheDeviceGroup();
      if (groupList == null) {
         return this.dao.getAllDeviceGroups(groupIds, groupName);
      } else {
         List list = new ArrayList();
         Iterator var5 = groupIds.iterator();

         while(var5.hasNext()) {
            Long groupId = (Long)var5.next();
            list.addAll(this.getAllDeviceGroupsByGroupName(groupId, groupName));
         }

         return list;
      }
   }

   public List getAllDeviceGroups(long groupId) throws SQLException {
      Map groupList = this.getCacheDeviceGroup();
      if (groupList == null) {
         return this.dao.getAllDeviceGroups(groupId, (String)null);
      } else {
         DeviceGroup rootGroup = (DeviceGroup)groupList.get(groupId);
         List list = new ArrayList();
         Deque q = new ArrayDeque();
         q.addLast(rootGroup);

         while(true) {
            List children;
            do {
               if (q.isEmpty()) {
                  return list;
               }

               DeviceGroup group = (DeviceGroup)q.removeLast();
               list.add(group);
               children = group.getChildrenGroup();
            } while(children == null);

            Iterator var9 = children.iterator();

            while(var9.hasNext()) {
               DeviceGroup child = (DeviceGroup)var9.next();
               q.addLast(child);
            }
         }
      }
   }

   public List getAllDeviceGroups(List groupIds) throws SQLException {
      Map groupList = this.getCacheDeviceGroup();
      if (groupList == null) {
         return this.dao.getAllDeviceGroups(groupIds, (String)null);
      } else {
         List list = new ArrayList();
         Iterator var4 = groupIds.iterator();

         while(var4.hasNext()) {
            Long groupId = (Long)var4.next();
            list.addAll(this.getAllDeviceGroups(groupId));
         }

         return list;
      }
   }

   public List getAllAuthorityDeviceGroups(String userId) throws SQLException {
      return this.dao.getAllAuthorityDeviceGroups(userId);
   }

   public int getUnapprovedDeviceCountByUser(List groupIds) throws SQLException {
      return this.dao.getUnapprovedDeviceCountByUser(groupIds);
   }

   public long getTotalOrganizationDeviceCountByGroupId(long groupId) throws SQLException {
      return this.dao.getTotalOrganizationDeviceCountByGroupId(groupId);
   }

   public long getTotalApprovalDeviceCount() throws SQLException {
      return this.dao.getTotalApprovalDeviceCount();
   }

   public List getDeviceCountByOrganization() throws SQLException {
      return this.dao.getDeviceCountByOrganization();
   }

   public List getParentGroupNamePathByGroupId(Long groupId) throws SQLException {
      return this.getParentGroupNamePathByGroupId((Map)null, groupId);
   }

   public List getParentGroupNamePathByGroupId(Map groupList, Long groupId) throws SQLException {
      if (groupList == null) {
         groupList = this.getCacheDeviceGroup();
      }

      if (groupList == null) {
         return this.dao.getParentGroupNamePathByGroupId(groupId);
      } else {
         List list = new ArrayList();
         if (groupList.get(groupId) != null) {
            list.add(groupList.get(groupId));

            while(((DeviceGroup)groupList.get(groupId)).getP_group_id() > 0L) {
               if (groupList.get(groupId) != null) {
                  groupId = ((DeviceGroup)groupList.get(groupId)).getP_group_id();
                  list.add(0, groupList.get(groupId));
               }
            }
         }

         return list;
      }
   }

   public String getParentOrgNameByGroupId(Long groupId) throws SQLException {
      return this.dao.getParentOrgNameByGroupId(groupId);
   }

   public boolean changeDeviceOrgName(String name, String oldName) throws SQLException {
      return this.dao.changeDeviceOrgName(name, oldName);
   }

   public int getCntAnalysisDeviceGroup() throws SQLException {
      return this.dao.getCntAnalysisDeviceGroup();
   }

   public boolean setAnalysisDeviceGroup(long groupId, boolean value) throws SQLException {
      return this.dao.setAnalysisDeviceGroup(groupId, value);
   }

   public Map getDeviceOrganizationByGroupId(int groupId) throws SQLException {
      return this.dao.getDeviceOrganizationByGroupId(groupId);
   }

   public String getDeviceTypeByGroupId(Long groupId) throws SQLException {
      return this.dao.getDeviceTypeByGroupId(groupId);
   }

   public void addGroupTotalCount(Long groupId, Long count) throws SQLException {
      this.dao.addGroupTotalCount(groupId, count);
   }

   public void updateOrgNameByGroupId(String beforeOrgName, long groupId, long targetGroupId) throws SQLException {
      String targetOrganization = this.dao.getOrgNameByGroupId(targetGroupId);
      if (beforeOrgName != null && !beforeOrgName.equals(targetOrganization)) {
         this.dao.updateOrganizationByGroupId(groupId, targetOrganization);
      }

   }

   public boolean addAlarmDeviceGroup(long organizationId, String orgName, String groupIds) throws SQLException {
      return this.dao.addAlarmDeviceGroup(organizationId, orgName, groupIds);
   }

   public List getAlarmDeviceGroupList(long organizationId) throws SQLException {
      return this.dao.getAlarmDeviceGroup(organizationId);
   }

   public List getAlarmDeviceGroupListByName(String orgName) throws SQLException {
      return this.dao.getAlarmDeviceGroupByName(orgName);
   }

   public DeviceGroup getDeviceOrgGroupByUserOrgId(Long userOrgId) throws SQLException {
      return this.dao.getDeviceOrgGroupByUserOrgId(userOrgId);
   }

   public List getGroupDeviceListByOrganName(String organName) throws SQLException {
      return this.daoType == 0 ? this.dao.getGroupDeviceListByOrganName(organName) : this.liteDao.getGroupDeviceListByOrganName(organName);
   }

   public Integer getCountByUserIdAndGroupId(String userId, Long groupId) throws SQLException {
      return this.dao.getCountByUserIdAndGroupId(userId, groupId);
   }

   public List V2GetChildDeviceIdList(int group_id, boolean recursive) throws SQLException {
      return this.daoType == 0 ? this.dao.V2GetChildDeviceIdList(group_id, recursive) : this.liteDao.getChildDeviceIdList(group_id, recursive);
   }

   public int getDeviceGroupTotalCount() throws SQLException {
      return this.dao.getDeviceGroupTotalCount();
   }

   static {
      initCacheDeviceGroup();
      logger = LoggingManagerV2.getLogger(DeviceGroupInfoImpl.class);
   }
}
