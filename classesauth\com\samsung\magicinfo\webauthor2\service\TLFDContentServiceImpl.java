package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import java.util.Arrays;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
public class TLFDContentServiceImpl implements TLFDContentService {
  private final ContentService contentService;
  
  @Autowired
  public TLFDContentServiceImpl(ContentService contentService) {
    this.contentService = contentService;
  }
  
  public Page<Content> getTLFDContentList(Pageable pageable, DeviceType deviceType) {
    return this.contentService.getContentResources(pageable, deviceType, Arrays.asList(new MediaType[] { MediaType.TLFD }));
  }
}
