package com.samsung.magicinfo.webauthor2.model;

import com.samsung.magicinfo.webauthor2.repository.model.ContentData;

public class Document {
  private Long organizationId;
  
  public Document(Long organizationId) {
    this.organizationId = organizationId;
  }
  
  public static Document fromData(ContentData contentData) {
    return new Document(Long.valueOf(contentData.getOrganizationId()));
  }
  
  public Long getOrganizationId() {
    return this.organizationId;
  }
  
  public void setOrganizationId(Long organizationId) {
    this.organizationId = organizationId;
  }
}
