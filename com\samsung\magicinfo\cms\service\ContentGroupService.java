package com.samsung.magicinfo.cms.service;

import com.samsung.common.model.ResponseBody;
import com.samsung.magicinfo.framework.content.entity.Group;
import java.util.Map;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("ContentGroupService")
@Transactional
public interface ContentGroupService {
   ResponseBody listDefaultGroup() throws Exception;

   ResponseBody getGroup(String var1) throws Exception;

   ResponseBody listChildGroup(String var1) throws Exception;

   Map createGroup(Group var1) throws Exception;
}
