package com.samsung.magicinfo.webauthor2.service.upload;

import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.UnknownHttpStatusCodeException;
import org.springframework.web.util.UriComponentsBuilder;

@Service
public class HttpContentUploadServiceImpl implements HttpContentUploadService {
  private static final Logger logger = LoggerFactory.getLogger(HttpContentUploadServiceImpl.class);
  
  private RestTemplate restTemplate;
  
  private UserData userData;
  
  @Autowired
  public HttpContentUploadServiceImpl(RestTemplate restTemplate, UserData userData) {
    this.restTemplate = restTemplate;
    this.userData = userData;
  }
  
  public void uploadListOfMediaSources(List<MediaSource> mediaSources, String contentId) {
    for (MediaSource ms : mediaSources) {
      if (shouldMediaSourceBeUploaded(ms))
        uploadSingleMediaSource(ms, contentId); 
    } 
  }
  
  private boolean shouldMediaSourceBeUploaded(MediaSource ms) {
    return (ms.isIsNew() && ms.getPath() != null && Files.exists(Paths.get(ms.getPath(), new String[0]), new java.nio.file.LinkOption[0]));
  }
  
  public void uploadSingleMediaSource(MediaSource mediaSource, String contentId) {
    Boolean isUrlAuthNotAllowed = this.userData.isUrlAuthNotAllowedToThisMisServletSession();
    UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.newInstance().path("/servlet/FileUploader");
    if (false == isUrlAuthNotAllowed.booleanValue())
      uriComponentsBuilder.queryParam("id", new Object[] { this.userData.getUserId() }).queryParam("passwd", new Object[] { this.userData.getToken() }); 
    URI uri = uriComponentsBuilder.build().encode().toUri();
    HttpHeaders headers = new HttpHeaders();
    headers.add("userId", this.userData.getUserId());
    headers.add("token", this.userData.getToken());
    headers.setContentType(MediaType.MULTIPART_FORM_DATA);
    headers.setCacheControl("no-cache");
    headers.setConnection("keep-alive");
    headers.set("category", "0");
    headers.set("CID", contentId);
    headers.set("FILE_ID", mediaSource.getFileId());
    HttpEntity<FileSystemResource> request = new HttpEntity(new FileSystemResource(mediaSource.getPath().toString()), (MultiValueMap)headers);
    logger.debug("MediaSource : " + mediaSource.toString());
    logger.debug("Upload : " + mediaSource.getPath().toString() + ", Header : " + headers.toString());
    try {
      this.restTemplate.exchange(uri, HttpMethod.POST, request, String.class);
    } catch (UnknownHttpStatusCodeException ex) {
      logger.error("Failed to uploadSingleMediaSource : " + ex.getMessage());
      throw new UploaderException(500, "Unknown http status code response from MagicINFO Server " + ex
          .getMessage());
    } 
  }
}
