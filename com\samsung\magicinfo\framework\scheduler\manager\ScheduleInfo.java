package com.samsung.magicinfo.framework.scheduler.manager;

import com.samsung.common.db.DBListExecuter;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.DetailDownloadContentEntity;
import com.samsung.magicinfo.framework.scheduler.entity.DynamicTagEntity;
import com.samsung.magicinfo.framework.scheduler.entity.FrameEntity;
import com.samsung.magicinfo.framework.scheduler.entity.FrameTemplateEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.SyncSchedule;
import com.samsung.magicinfo.openapi.impl.OpenApiServiceException;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.SqlSession;

public interface ScheduleInfo extends DBListExecuter {
   boolean addProgram(ProgramEntity var1, String var2) throws SQLException, ConfigException;

   boolean addDeviceGroupMappedInProgram(String var1, String var2) throws SQLException;

   boolean addDeviceGroupMappedInProgramTemp(String var1, String var2, String var3, String var4, String var5) throws SQLException;

   boolean deleteDeviceGroupMappedInProgramTempByProgramId(String var1) throws SQLException;

   boolean deleteDeviceGroupMappedInProgramByProgramId(String var1) throws SQLException;

   boolean setDefaultProgramId(long var1, String var3) throws SQLException;

   List getDeviceGroupMappedInProgramTemp(String var1) throws SQLException;

   List getDeviceGroupMappedInProgram(String var1) throws SQLException;

   boolean updateProgram(ProgramEntity var1, String var2) throws SQLException, ConfigException;

   ProgramEntity getProgram(String var1) throws SQLException;

   String getProgramName(String var1) throws SQLException;

   boolean deleteProgram(String var1) throws SQLException;

   boolean setProgram(String var1, ProgramEntity var2) throws ConfigException, SQLException;

   boolean addFrame(FrameEntity var1) throws SQLException;

   List getFrames(String var1, int var2, int var3) throws SQLException;

   List getFrames(String var1, int var2) throws SQLException;

   List getFramesInfo(String var1, int var2) throws SQLException;

   List getTempFrames(String var1, int var2, String var3) throws SQLException;

   boolean deleteFrame(String var1, String var2, int var3, int var4) throws SQLException;

   boolean deleteTempFrame(String var1, String var2) throws SQLException, ConfigException;

   boolean deleteTempFrameByChannelNo(String var1, String var2, int var3) throws SQLException, ConfigException;

   boolean deleteTempScheduleForFrameIndex(String var1, String var2, int var3, int var4) throws SQLException;

   boolean addContentSchedule(ContentsScheduleEntity var1) throws ConfigException, SQLException;

   boolean addSyncMatchInfoTemp(SyncSchedule var1) throws SQLException;

   boolean addSyncMatchInfo(String var1) throws SQLException;

   boolean updateDefaultProgramDeviceType(Long var1, String var2, SqlSession var3) throws SQLException;

   boolean updateContentSchedule(ContentsScheduleEntity var1) throws SQLException, ConfigException;

   List getContentSchedules(String var1, int var2) throws SQLException;

   List getContentListFromProgramidandChannel(String var1, int var2, int var3, int var4) throws SQLException;

   List getContentSchedules(String var1, int var2, int var3, int var4) throws SQLException;

   int countSyncScheduleMatchInfoTemp(String var1) throws SQLException;

   int countSyncScheduleMatchInfo(String var1) throws SQLException;

   boolean deleteSyncScheduleMatchInfoTemp(String var1) throws SQLException;

   boolean deleteSyncScheduleMatchInfo(String var1) throws SQLException;

   List getSyncScheduleMatchInfoTemp(String var1) throws SQLException;

   List getSyncScheduleMatchInfo(String var1) throws SQLException;

   List getContentSchedules(String var1) throws SQLException;

   boolean addPanelOrZeroFrameSchedule(ScheduleEntity var1) throws ConfigException, SQLException;

   boolean updatePanelOrZeroFrameSchedule(ScheduleEntity var1) throws ConfigException, SQLException;

   boolean deleteTempSchedule(String var1, String var2) throws ConfigException, SQLException;

   boolean deleteTempScheduleByChannelNo(String var1, String var2, int var3) throws ConfigException, SQLException;

   boolean deleteProgramTempData(String var1, String var2) throws SQLException;

   boolean deleteProgramData(String var1) throws SQLException;

   boolean deleteProgramTempDataWithSession(String var1) throws SQLException;

   boolean deleteProgramTempDataWithId(String var1) throws SQLException;

   boolean deleteAllProgramTempData() throws SQLException;

   List getPanelOrZeroFrameSchedules(String var1, String var2) throws SQLException;

   List getPanelOrZeroFrameTempSchedules(String var1, String var2) throws SQLException;

   long getTempFrameCount(String var1, String var2) throws SQLException;

   long getTempFrameCount(String var1, int var2, String var3) throws SQLException;

   long getTempFrameCount(String var1) throws SQLException;

   long getTempFrameCountForSession(String var1) throws SQLException;

   FrameEntity getFrameData(String var1, int var2) throws SQLException;

   List getFrameCount(String var1) throws SQLException;

   FrameEntity getFrameData(String var1, int var2, int var3) throws SQLException;

   String getBGMContentName(String var1) throws SQLException;

   Map getDeviceGroupIdsAndName(String var1) throws SQLException;

   List getProgramGroupIdAndName(String var1) throws SQLException;

   List selAllScheduleByMonth(Map var1) throws SQLException;

   List selAllScheduleByWeek(Map var1) throws SQLException;

   List selAllScheduleByDay(Map var1) throws SQLException;

   ContentsScheduleEntity getScheduleData(Map var1) throws SQLException, ConfigException;

   boolean deleteSchedule(String var1, String var2) throws SQLException;

   boolean transferProgramDataToMain(String var1, String var2, String var3) throws SQLException, ConfigException;

   boolean transferProgramDataToTemp(String var1, String var2) throws SQLException, ConfigException;

   boolean transferSyncScheduleMatchInfoToTemp(String var1, String var2) throws SQLException;

   boolean transferScheduleDataToTempWithNewId(String var1, String var2, String var3) throws SQLException, ConfigException;

   boolean transferScheduleDataToTempWithNewIdForImport(String var1, String var2, String var3, String var4, int var5) throws SQLException, ConfigException;

   boolean reserveSchedule(ProgramEntity var1) throws Exception;

   boolean reserveSchedule(ProgramEntity var1, SqlSession var2) throws Exception;

   boolean checkDeviceMapping(String var1, String var2) throws Exception;

   boolean changeDefaultProgramName(String var1, String var2) throws SQLException;

   String createDefaultProgram(String var1, String var2, String var3) throws SQLException, ConfigException, Exception;

   List getProgramByContentId(String var1) throws SQLException;

   List getProgramByPlaylistId(String var1) throws SQLException;

   void setContentTrigger(String var1) throws Exception;

   void setPlaylistTrigger(String var1) throws Exception;

   void setPlaylistTrigger(String var1, boolean var2) throws Exception;

   void deploySchedule(String var1, String var2, long var3) throws Exception;

   void deploySchedule(String var1, String var2, long var3, String var5) throws Exception;

   boolean isProgramNameUnique(String var1, String var2, int var3) throws SQLException;

   List getDownloadContentList(String var1, String var2) throws SQLException;

   List getEventList(String var1) throws SQLException;

   long getProgramVersion(String var1) throws SQLException;

   long getContentScheduleCntToday() throws SQLException;

   long getContentScheduleCntToday(String var1) throws SQLException;

   long getContentScheduleCntThisWeek() throws SQLException;

   long getAllScheduleCount() throws SQLException;

   long getAllScheduleCount(String var1) throws SQLException;

   long getMapedScheduleCount() throws SQLException;

   long getMapedScheduleCount(String var1) throws SQLException;

   long getNotMapedScheduleCount() throws SQLException;

   long getNotMapedScheduleCount(String var1) throws SQLException;

   boolean onProgramLayoutChange(String var1, String var2, int var3, String var4, double var5, double var7) throws SQLException;

   List getFrameTemplates(String var1, String var2, String var3) throws SQLException;

   boolean saveFrameTemplate(FrameTemplateEntity var1) throws SQLException;

   long createFrameTemplate(FrameTemplateEntity var1) throws SQLException;

   boolean deleteFrameTemplate(Long var1) throws SQLException;

   long checkAvailableDiskSpace(String var1, String var2, String var3, boolean var4) throws SQLException;

   boolean updateDeviceGroupMappedInProgramAsDefault(String var1) throws SQLException;

   boolean updateDeviceGroupMappedInProgramAsDefaultByPid(String var1) throws SQLException;

   List getDeviceProgramMapList(String var1) throws SQLException;

   boolean mapDeviceGroupWithDefault(Long var1, String var2) throws SQLException;

   String getProgramIdByProgramName(String var1) throws SQLException;

   boolean deleteFrameByFrameId(String var1) throws SQLException;

   boolean deleteContentScheduleByScheduleId(String var1) throws SQLException;

   boolean deleteContentScheduleByScheduleId(String var1, boolean var2, boolean var3) throws SQLException;

   List getContentScheduleIdByFrameId(String var1) throws SQLException;

   boolean addProgramWithFrameAndHWControlAndContent(ProgramEntity var1, FrameEntity var2, ContentsScheduleEntity var3, String var4) throws SQLException, ConfigException;

   boolean modifyProgramWithFrameAndHWControlAndContent(ProgramEntity var1, FrameEntity var2, ContentsScheduleEntity var3, String var4) throws SQLException, ConfigException;

   boolean addProgramWithBasicInformation(ProgramEntity var1, String var2) throws SQLException, ConfigException;

   boolean modifyProgramWithBasicInformation(ProgramEntity var1, String var2) throws SQLException, ConfigException;

   boolean addContentScheduleWithoutTemp(ContentsScheduleEntity var1) throws SQLException, ConfigException, OpenApiServiceException;

   boolean addContentScheduleWithoutTemp(ContentsScheduleEntity var1, boolean var2, boolean var3) throws SQLException, ConfigException, OpenApiServiceException;

   boolean modifyContentScheduleWithoutTemp(ContentsScheduleEntity var1) throws SQLException, ConfigException, OpenApiServiceException;

   boolean modifyContentScheduleWithoutTemp(ContentsScheduleEntity var1, boolean var2, boolean var3) throws SQLException, ConfigException, OpenApiServiceException;

   boolean addHWConstraint(ContentsScheduleEntity var1) throws SQLException, ConfigException;

   boolean modifyHWConstraint(ContentsScheduleEntity var1) throws SQLException, ConfigException;

   boolean addFrameWithoutTemp(FrameEntity var1) throws SQLException;

   String getDeviceTypeByProgramId(String var1) throws SQLException;

   Float getDeviceTypeVersionByProgramId(String var1) throws SQLException;

   int getFrameIndexByFrameId(String var1) throws SQLException;

   int setLinedataByProgramId(String var1, int var2, String var3) throws SQLException;

   int setLinedataToZeroFrame(String var1, int var2) throws SQLException;

   String getProgramIdByFrameId(String var1) throws SQLException;

   boolean deleteFrameByChannelNo(String var1, int var2) throws SQLException, ConfigException;

   boolean deleteScheduleByChannelNo(String var1, int var2) throws SQLException;

   boolean updateDefaultProgramDeviceType(Long var1, String var2) throws SQLException;

   String isDeleted(String var1) throws SQLException;

   String getCreatorIdByProgramId(String var1) throws SQLException;

   List getProgramListBySchOrgId(int var1) throws SQLException;

   List getChannelListByProgramId(String var1) throws SQLException;

   boolean modifyProgramDeviceTypeAndVersion(String var1, String var2, float var3) throws SQLException;

   List getSyncGroupListPerSchedule(String var1) throws SQLException;

   List getSyncDeviceIdListPerSchedule(String var1, String var2, String var3) throws SQLException;

   List getScheduleDetailPublishStatusList(String var1, long var2);

   List getContentListInSchedule(String var1);

   void addContentPublishData(@Param("entity") DetailDownloadContentEntity var1) throws SQLException;

   void deleteContentPublishData(@Param("programId") String var1) throws SQLException;

   int addDynaminTagInfoTemp(DynamicTagEntity var1) throws SQLException;

   boolean deleteDynaminTagInfoTemp(String var1) throws SQLException;

   List getDynaminTagInfo(String var1) throws SQLException;

   List getDynaminTagInfoTemp(String var1, String var2) throws SQLException;

   boolean deleteAllProgramData(String var1) throws ConfigException, SQLException;

   boolean programVersionUp(String var1) throws Exception;

   boolean addNewVersionProgram(ProgramEntity var1, String var2, String var3) throws ConfigException, SQLException;

   boolean updateNewVersionProgram(ProgramEntity var1, String var2, String var3) throws SQLException;

   boolean deleteDynaminTagInfo(String var1) throws SQLException;

   boolean addDynaminTagInfo(List var1) throws SQLException;

   List getDeletedProgramIdList(String var1) throws Exception;

   boolean updateProgramView(ProgramEntity var1) throws SQLException;

   List getScheduleMappedContentTotalSize(String var1) throws Exception;

   FrameTemplateEntity getTemplateEntity(long var1) throws Exception;

   boolean addNewADProgram(ProgramEntity var1, String var2, String var3) throws ConfigException, SQLException;

   List getSlotListFromProgramIdWithFrameId(String var1, String var2) throws Exception;

   List getAdScheduleListFromProgramIdWithSlotId(String var1, String var2) throws Exception;

   boolean updateADNewVersionProgram(ProgramEntity var1, String var2, String var3) throws SQLException;

   List getSlotListFromProgramId(String var1) throws Exception;

   boolean updateTemplate(FrameTemplateEntity var1) throws Exception;

   List getAuthorityFromFrame(String var1, String var2);

   List getReserveSchedule() throws Exception;

   int getCountProgramIdByGroupId(long var1) throws Exception;

   List getContentListInScheduleWithStopDate(String var1, String var2) throws Exception;

   List getContentListInADScheduleWithStopDate(String var1, String var2) throws Exception;

   List getTagPlaylistIdVersion(String var1) throws Exception;

   boolean chkOrganizationByProgramId(String var1, String var2) throws Exception;

   List getDynamicTagByScheduleIdIdAndPlaylistId(String var1, String var2) throws Exception;

   boolean existsProgramId(String var1) throws Exception;

   ProgramEntity getProgramWithGroupIdAndNameByProgramId(String var1) throws SQLException;

   int getCountScheduleToExpire(String var1, Long var2, String var3, SelectCondition var4) throws SQLException;

   int getDeviceCountByScheduleToExpire(String var1, Long var2, String var3, SelectCondition var4) throws SQLException;

   List getListScheduleToExpire(int var1, int var2, String var3, Long var4, String var5, SelectCondition var6) throws SQLException;

   List getDeviceListByScheduleToExpire(int var1, int var2, String var3, Long var4, String var5, SelectCondition var6) throws SQLException;

   Long getMaxPriorityByProgramId(String var1) throws SQLException;

   Long getMinPriorityByProgramId(String var1) throws SQLException;

   Long getPriorityByScheduleId(String var1, String var2) throws SQLException;

   List getScheduleIdAndPriorityByProgramId(String var1) throws SQLException;

   boolean updateSchedulePriorityByProgramId(String var1, List var2) throws SQLException;

   void setExpiredContentTrigger(String var1, String var2) throws Exception;

   void setPlaylistWithExpiredContentTrigger(String var1, String var2) throws Exception;

   List getContentListByProgramId(String var1) throws SQLException;

   List getFrameContentList(String var1) throws Exception;

   List getScheduleGroupBySearchText(String var1, String var2, String var3) throws SQLException;

   List getParentsGroupList(int var1, String var2) throws SQLException;

   List getProgramCountByProgramType() throws SQLException;
}
