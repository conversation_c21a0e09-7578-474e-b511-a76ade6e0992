package com.samsung.magicinfo.webauthor2.xml.datalink;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;

@XmlRegistry
public class ObjectFactory {
  private static final QName _DataLinkContentMeta_QNAME = new QName("", "DataLinkContentMeta");
  
  public DataLinkContentMetaType createDataLinkContentMetaType() {
    return new DataLinkContentMetaType();
  }
  
  public SettingType createSettingType() {
    return new SettingType();
  }
  
  public ValueLocationType createValueLocationType() {
    return new ValueLocationType();
  }
  
  public ErrorType createErrorType() {
    return new ErrorType();
  }
  
  public DataType createDataType() {
    return new DataType();
  }
  
  public LFDContentType createLFDContentType() {
    return new LFDContentType();
  }
  
  public SyncGroupType createSyncGroupType() {
    return new SyncGroupType();
  }
  
  public ElementType createElementType() {
    return new ElementType();
  }
  
  public SplitGroupType createSplitGroupType() {
    return new SplitGroupType();
  }
  
  public PageType createPageType() {
    return new PageType();
  }
  
  @XmlElementDecl(namespace = "", name = "DataLinkContentMeta")
  public JAXBElement<DataLinkContentMetaType> createDataLinkContentMeta(DataLinkContentMetaType value) {
    return new JAXBElement(_DataLinkContentMeta_QNAME, DataLinkContentMetaType.class, null, value);
  }
}
