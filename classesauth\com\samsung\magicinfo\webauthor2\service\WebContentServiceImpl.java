package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.exception.repository.ContentNotFoundException;
import com.samsung.magicinfo.webauthor2.exception.service.DownloaderException;
import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.FileItemWrapper;
import com.samsung.magicinfo.webauthor2.model.HtmlPreviewResponse;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.model.PreviewUsageResponse;
import com.samsung.magicinfo.webauthor2.model.SupportFileItemWrapper;
import com.samsung.magicinfo.webauthor2.repository.inmemory.model.PreviewUsage;
import com.samsung.magicinfo.webauthor2.service.download.LFDFileXmlFactory;
import com.samsung.magicinfo.webauthor2.util.UserData;
import com.samsung.magicinfo.webauthor2.util.ZipFileUtil;
import com.samsung.magicinfo.webauthor2.xml.lfd.FileItem;
import com.samsung.magicinfo.webauthor2.xml.lfd.SupportFileItem;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.ArrayList;
import java.util.List;
import javax.inject.Inject;
import javax.servlet.ServletContext;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.joda.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.context.annotation.SessionScope;

@Service
@SessionScope
public class WebContentServiceImpl implements WebContentService {
  private static final Logger logger = LoggerFactory.getLogger(WebContentServiceImpl.class);
  
  private ServletContext servletContext;
  
  private UserData userData;
  
  private ContentService contentService;
  
  private RemoteContentService remoteContentService;
  
  private LFDFileXmlFactory lfdFileXmlFactory;
  
  private WebContentPreviewUsageService usageService;
  
  private ZipFileUtil zipFileUtil;
  
  public static final String DEFAULT_WORKING_DIRECTORY_ROOT = "insertContents";
  
  public static final String PREVIEW_DIRECTORY_ROOT = "preview";
  
  public static final String TEMPORARY_WORKING_DIRECTORY = "WebAuthor";
  
  @Inject
  public WebContentServiceImpl(ServletContext servletContext, UserData userData, ContentService contentService, RemoteContentService remoteContentService, LFDFileXmlFactory lfdFileXmlFactory, WebContentPreviewUsageService usageService, ZipFileUtil zipFileUtil) {
    this.servletContext = servletContext;
    this.userData = userData;
    this.contentService = contentService;
    this.remoteContentService = remoteContentService;
    this.lfdFileXmlFactory = lfdFileXmlFactory;
    this.usageService = usageService;
    this.zipFileUtil = zipFileUtil;
  }
  
  public HtmlPreviewResponse getPreviewInfo(String contentId) {
    PreviewUsage usage = this.usageService.findByContentId(contentId);
    Content webContentLFDContent = this.contentService.getContent(contentId);
    int webContentVersionId = webContentLFDContent.getVersionId().intValue();
    if (usage != null) {
      if (usage.getProgress() == 100 && usage.getVersionId() >= webContentVersionId) {
        usage.setUserId(this.userData.getUserId());
        usage.setLastused(LocalDate.now());
        this.usageService.update(usage);
      } else {
        downloadToRepository(contentId, usage);
        usage.setVersionId(webContentVersionId);
        this.usageService.update(usage);
      } 
    } else {
      usage = new PreviewUsage(contentId, webContentVersionId, LocalDate.now(), this.userData.getUserId(), "", 0);
      downloadToRepository(contentId, usage);
      this.usageService.insert(usage);
    } 
    return new HtmlPreviewResponse(usage.getProgress(), true, getStartFileName(usage.getStartPage()));
  }
  
  public List<PreviewUsageResponse> getPreviewUsages() {
    return PreviewUsageResponse.fromUsageData(this.usageService.findAll());
  }
  
  public void downloadToRepository(String contentId, PreviewUsage usage) {
    try {
      Path webContentRepoDir = Paths.get(this.servletContext.getRealPath("preview"), new String[] { contentId });
      if (Files.exists(webContentRepoDir, new java.nio.file.LinkOption[0])) {
        cleanup(webContentRepoDir);
      } else {
        Files.createDirectories(webContentRepoDir, (FileAttribute<?>[])new FileAttribute[0]);
      } 
      logger.debug("Downloading WebContent with id {} to path: {}", contentId, webContentRepoDir.toString());
      Content webContentLFDContent = this.contentService.getContent(contentId);
      if (webContentLFDContent.getType() != MediaType.LFD && webContentLFDContent.getType() != MediaType.HTML)
        throw new ContentNotFoundException("404", "Content not found id: " + contentId + " with MediaType: LFD or HTML."); 
      String lfdXml = this.remoteContentService.getXmlFileContents(webContentLFDContent.getFileId(), webContentLFDContent.getFileName());
      List<SupportFileItem> supportFileItems = this.lfdFileXmlFactory.unmarshalSupportFileItems(lfdXml);
      String startPage = this.lfdFileXmlFactory.unmarshalStartPage(lfdXml);
      usage.setStartPage(startPage);
      this.usageService.update(usage);
      List<SupportFileItemWrapper> downloadedSupportItems = downloadSupportFileItems(contentId, webContentRepoDir.getParent(), supportFileItems, usage);
      List<FileItem> fileItems = this.lfdFileXmlFactory.unmarshalFileItems(lfdXml);
      List<FileItemWrapper> downloadedFileItems = downloadFileItems(fileItems, contentId, webContentRepoDir);
      for (FileItemWrapper item : downloadedFileItems) {
        if (FilenameUtils.getExtension(item.getFilename()).equalsIgnoreCase("zip")) {
          logger.debug("Procesing .zip file");
          this.zipFileUtil.extract(item.getPath(), webContentRepoDir);
          usage.setProgress(100);
          this.usageService.update(usage);
        } 
      } 
    } catch (IOException ex) {
      logger.error("Error downloading WebCotent with id " + contentId, ex.getMessage());
      throw new DownloaderException("Error downloading WebCotent with id " + contentId);
    } 
  }
  
  @Async
  private List<SupportFileItemWrapper> downloadSupportFileItems(String contentId, Path workingDirectory, List<SupportFileItem> items, PreviewUsage usage) {
    List<SupportFileItemWrapper> result = new ArrayList<>();
    long totalBytes = 0L;
    for (SupportFileItem item : items)
      totalBytes += item.getFileSize(); 
    logger.debug("Downloading SupportFileItems in content:" + contentId + " total size:" + totalBytes);
    long remainingBytes = totalBytes;
    int currentProgress = 0;
    for (SupportFileItem item : items) {
      SupportFileItemWrapper wrapper = new SupportFileItemWrapper(item);
      String relativePath = item.getKeyPathLocation();
      Path relativefull = Paths.get(relativePath.replace("\\", "/"), new String[0]);
      Path relative = relativefull.subpath(1, relativefull.getNameCount() - 1);
      wrapper.setPathToFile(this.remoteContentService.getContentFileFromMagicInfoServer(workingDirectory, relative.toString(), item.getFileId(), item.getPureFileItem()));
      wrapper.setRelativePath(relativefull.subpath(2, relativefull.getNameCount()));
      result.add(wrapper);
      remainingBytes -= item.getFileSize();
      wrapper.getPathToFile().toFile().setExecutable(false);
      wrapper.getPathToFile().toFile().setWritable(true);
      usage.setProgress(calculateProgress(totalBytes, remainingBytes));
      logger.debug("Downloading SupportFileItems in content:" + contentId + " Progress: " + currentProgress);
      this.usageService.update(usage);
    } 
    usage.setProgress(100);
    this.usageService.update(usage);
    return result;
  }
  
  private List<FileItemWrapper> downloadFileItems(List<FileItem> fileItems, String contentId, Path workingDirectory) {
    logger.debug("Downloading " + fileItems.size() + " FileItems in content:" + contentId);
    List<FileItemWrapper> wrappers = new ArrayList<>();
    for (FileItem item : fileItems) {
      FileItemWrapper wrapper = new FileItemWrapper(item);
      wrapper.setFilename(getStartFileName(item.getValue()));
      Path pathToFile = this.remoteContentService.getContentFileFromMagicInfoServer(workingDirectory, item.getFileId(), wrapper.getFilename());
      wrapper.setPath(Paths.get(pathToFile.toString(), new String[0]));
      wrappers.add(wrapper);
    } 
    return wrappers;
  }
  
  public void cleanup(Path dir) {
    if (dir != null && Files.exists(dir, new java.nio.file.LinkOption[0]))
      FileUtils.deleteQuietly(dir.toFile()); 
  }
  
  private int calculateProgress(long total, long remaing) {
    double quotient = remaing / total;
    return (int)Math.round(100.0D * (1.0D - quotient));
  }
  
  public String getStartFileName(String startPage) {
    return startPage.substring(startPage.lastIndexOf('\\') + 1);
  }
}
