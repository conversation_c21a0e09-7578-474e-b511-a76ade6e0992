package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.util.SupportedFormatUtils;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SupportedFormatServiceImpl implements SupportedFormatService {
  private FileService fileService;
  
  @Autowired
  public SupportedFormatServiceImpl(FileService fileService) {
    this.fileService = fileService;
  }
  
  public void copySupportedFormatsFromMagicInfoServer() {
    for (MediaType type : MediaType.values()) {
      List<String> listOfSupportedFormats = this.fileService.getFileTypeList(type, DeviceType.iPLAYER);
      SupportedFormatUtils.setFileFormats(type, listOfSupportedFormats);
    } 
  }
}
