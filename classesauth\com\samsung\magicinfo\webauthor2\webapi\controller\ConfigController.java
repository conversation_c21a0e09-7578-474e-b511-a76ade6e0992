package com.samsung.magicinfo.webauthor2.webapi.controller;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.properties.MagicInfoProperties;
import com.samsung.magicinfo.webauthor2.service.ServerInfoService;
import com.samsung.magicinfo.webauthor2.service.UserService;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Scope("session")
public class ConfigController {
  private UserService userService;
  
  private UserData userData;
  
  private MagicInfoProperties magicInfoProperties;
  
  private ServerInfoService serverInfoService;
  
  private static final Logger logger = LoggerFactory.getLogger(ConfigController.class);
  
  @Autowired
  public ConfigController(UserService userService, UserData userData, MagicInfoProperties magicInfoProperties, ServerInfoService serverInfoService) {
    this.userService = userService;
    this.userData = userData;
    this.magicInfoProperties = magicInfoProperties;
    this.serverInfoService = serverInfoService;
  }
  
  @GetMapping({"/ConfigParameters"})
  public HttpEntity<Map<String, Object>> getParam(Locale locale) {
    logger.info("UserData:" + this.userData.toString());
    String language = this.userData.getLanguage();
    String localeStr = Strings.isNullOrEmpty(language) ? locale.toString() : language;
    String startContentId = this.userData.getStartContentId();
    String openApiVersion = this.serverInfoService.getOpenApiVersion();
    if (Strings.isNullOrEmpty(openApiVersion))
      openApiVersion = this.magicInfoProperties.getOpenApiVersion(); 
    this.userData.setSessionMisOpenApiVersion(openApiVersion);
    this.userService.setUserAuthority(this.userData.getUserId());
    Map<String, Object> outMap = new HashMap<>();
    outMap.put("context", this.magicInfoProperties.getWebauthorContext());
    outMap.put("version", this.magicInfoProperties.getWebauthorVersion());
    outMap.put("development", this.magicInfoProperties.getWebauthorDevelopment());
    outMap.put("locale", localeStr);
    outMap.put("contentId", startContentId);
    outMap.put("openApiVersion", openApiVersion);
    outMap.put("hasContentLockAuthority", Boolean.valueOf(this.userService.hasContentLockAuthority()));
    outMap.put("hasContentUploadAuthority", Boolean.valueOf(this.userService.hasContentUploadAuthority()));
    outMap.put("hasContentAddElementAuthority", Boolean.valueOf(this.userService.hasContentAddElementAuthority()));
    return (HttpEntity<Map<String, Object>>)ResponseEntity.ok(outMap);
  }
}
