package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.exception.repository.MagicInfoRemoteContentException;
import com.samsung.magicinfo.webauthor2.repository.RemoteContentRepository;
import java.io.IOException;
import java.nio.file.Path;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class RemoteContentServiceImpl implements RemoteContentService {
  private RemoteContentRepository remoteContentRepository;
  
  @Autowired
  public RemoteContentServiceImpl(RemoteContentRepository remoteContentRepository) {
    this.remoteContentRepository = remoteContentRepository;
  }
  
  public byte[] getContentFileFromMagicInfoServer(String fileId, String fileName) {
    return this.remoteContentRepository.getContentFileFromMagicInfoServer(fileId, fileName);
  }
  
  public String getVwlFileFromMagicInfoServer(String fileId, String fileName) {
    return this.remoteContentRepository.getVwlContent(fileId, fileName);
  }
  
  public String getXmlFileContents(String fileId, String fileName) {
    return this.remoteContentRepository.getXmlFileContents(fileId, fileName);
  }
  
  public Path getContentFileFromMagicInfoServer(Path workspaceFolder, String fileId, String fileName) {
    try {
      return this.remoteContentRepository.getContentFileFromMagicInfoServer(workspaceFolder, fileId, fileName);
    } catch (IOException ex) {
      throw new MagicInfoRemoteContentException(ex.getMessage());
    } 
  }
  
  public Path getContentFileFromMagicInfoServer(Path workspaceFolder, String relativePath, String fileId, String fileName) {
    try {
      return this.remoteContentRepository.getContentFileFromMagicInfoServer(workspaceFolder, relativePath, fileId, fileName);
    } catch (IOException ex) {
      throw new MagicInfoRemoteContentException(ex.getMessage());
    } 
  }
  
  public Path getFontFileFromMagicInfoServer(String fileId, String fileName) {
    try {
      return this.remoteContentRepository.getFontFileFromMagicInfoServer(fileId, fileName);
    } catch (IOException ex) {
      throw new MagicInfoRemoteContentException(ex.getMessage());
    } 
  }
}
