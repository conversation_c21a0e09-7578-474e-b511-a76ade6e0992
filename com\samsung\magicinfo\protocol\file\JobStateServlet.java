package com.samsung.magicinfo.protocol.file;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.exception.CMSExceptionCode;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.ContentUtils;
import com.samsung.common.utils.FileUtils;
import com.samsung.common.utils.RequestUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.XPathQueryBuilder;
import com.samsung.magicinfo.framework.content.constants.ContentConstants;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.NotificationData;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.ContentXmlManager;
import com.samsung.magicinfo.framework.content.manager.UploadTemplateThread;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.protocol.util.MailUtil;
import com.samsung.magicinfo.protocol.util.VWLParser;
import java.awt.AlphaComposite;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.awt.image.ImageObserver;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import javax.imageio.ImageIO;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpression;
import javax.xml.xpath.XPathFactory;
import org.apache.logging.log4j.Logger;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

public class JobStateServlet extends HttpServlet {
   private static final long serialVersionUID = -1937920273302329385L;
   private Logger logger = LoggingManagerV2.getLogger(JobStateServlet.class);

   public JobStateServlet() {
      super();
   }

   public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");
      this.doPost(request, response);
   }

   public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
      request.setCharacterEncoding("UTF-8");
      response.setContentType("text/html; charset=UTF-8");

      try {
         String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
         String THUMBNAIL_HOME = CommonConfig.get("THUMBNAIL_HOME").replace('/', File.separatorChar);
         String userId = SecurityUtils.getUserIdFromRequest(request);
         String token = SecurityUtils.getTokenFromRequest(request);
         String jobState = StrUtils.nvl(request.getHeader("JOB_STATE"));
         String contentID = request.getHeader("CID");
         String PROMThumbnailType = request.getHeader("PROMThumbnailType");
         String isEdit = request.getHeader("isEdit");
         String template = StrUtils.nvl(request.getParameter("template")).equals("") ? "N" : request.getParameter("template");
         if (contentID == null) {
            this.logger.error("[MagicInfo_JobStateServelet] " + CMSExceptionCode.APP602[2]);
            response.setHeader("code", CMSExceptionCode.APP602[0]);
            response.setHeader("message", CMSExceptionCode.APP602[2]);
            response.sendError(602, CMSExceptionCode.APP602[2]);
            return;
         }

         contentID = SecurityUtils.directoryTraversalChecker(contentID, request.getRemoteAddr());
         ContentInfo cmsDao = ContentInfoImpl.getInstance();
         Content content = null;
         Long version = 0L;
         boolean isTemplate = false;
         String validationResult;
         if (!jobState.equals("") && jobState.equals("COMPLETE")) {
            content = cmsDao.getContentVerInfo(contentID, 0L);
            Long maxVersion = cmsDao.getContentNextVer(contentID);
            List contentFileList = cmsDao.getFileListByContentIdAndVersion(contentID, version);
            version = 0L;
            this.logger.error("JobStateServlet contentId:" + contentID + ", added version:" + maxVersion);
            validationResult = cmsDao.checkContentValidation(contentID, version);
            boolean allFileExist = true;
            if (!validationResult.equals(ContentConstants.CONT_VALID[0])) {
               allFileExist = false;
            }

            String meta_file;
            String lfdFilePath;
            if (allFileExist && content.getMedia_type().equals("HTML")) {
               try {
                  meta_file = ContentUtils.parseForLFD(content, "//Content/Page/Element[@type='Web']/StartupPage/KeyFrame");
                  if (meta_file != null && !meta_file.equals("")) {
                     content.setHtml_start_page(meta_file);
                  }

                  lfdFilePath = ContentUtils.parseForLFD(content, "//Content/Page/Element[@type='Web']/WebRefreshNew/KeyFrame");
                  if (lfdFilePath != null && !lfdFilePath.equals("")) {
                     content.setRefresh_interval(lfdFilePath);
                  }

                  cmsDao.updateContentForStartPageRefreshInterval(content);
               } catch (Exception var69) {
                  this.logger.error("", var69);
                  allFileExist = false;
               }
            }

            File cfgFile;
            boolean ret;
            String cmsMetaPath;
            if (!allFileExist) {
               if (maxVersion == 1L) {
                  cmsDao.deleteContentCompletely(contentID);
                  meta_file = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separator + "contents_home" + File.separator + "contents_meta" + File.separator + contentID + File.separator + "ContentsMetadata.CSD";
                  lfdFilePath = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separator + "contents_home" + File.separator + "contents_meta" + File.separator + contentID + File.separator;
                  File metaFile = SecurityUtils.getSafeFile(meta_file);
                  cfgFile = SecurityUtils.getSafeFile(lfdFilePath);
                  ret = metaFile.delete();
                  if (!ret) {
                     this.logger.error("returned fail");
                  }

                  ret = cfgFile.delete();
                  if (!ret) {
                     this.logger.error("returned fail");
                  }
               }

               cmsDao.deleteVersionContent(contentID, version);

               for(int j = 0; j < contentFileList.size(); ++j) {
                  Map map1 = (Map)contentFileList.get(j);
                  cmsMetaPath = (String)map1.get("FILE_ID");
                  if (cmsDao.isDeletableFileByVersion(cmsMetaPath, contentID, version)) {
                     cmsDao.deleteFile(cmsMetaPath);
                  }
               }

               if (!validationResult.equals(ContentConstants.CONT_HTML_VAILD[0])) {
                  this.logger.error("[checkContentValidation] INVALID! all files are deleted. contentId:" + contentID + ", version:" + maxVersion + "(" + validationResult + ")");
                  response.sendError(606, CMSExceptionCode.APP606[2]);
                  return;
               }
            } else if (isEdit != null && isEdit.equalsIgnoreCase("TRUE")) {
               version = cmsDao.setActiveVersion(contentID, false);
            } else {
               version = cmsDao.setActiveVersion(contentID, true);
            }

            if (version <= 0L) {
               cmsDao.deleteVersionContent(contentID, 0L);
            } else {
               File thumbFile;
               int orgWidth;
               int smallWidth;
               File thumb_smallFile;
               File thumb_mediumFile;
               String thumb_url2;
               String cmsMetaPath;
               Map modelCountMap;
               String type;
               if (content.getMedia_type().equalsIgnoreCase("IMAGE") || content.getMedia_type().equalsIgnoreCase("MOVIE") || content.getMedia_type().equalsIgnoreCase("LFD") || content.getMedia_type().equalsIgnoreCase("LFT") || content.getMedia_type().equalsIgnoreCase("VWL") || content.getMedia_type().equalsIgnoreCase("PROM") || content.getMedia_type().equalsIgnoreCase("HTML") && !PROMThumbnailType.equalsIgnoreCase("OFFICE") && !PROMThumbnailType.equalsIgnoreCase("FLASH") && !PROMThumbnailType.equalsIgnoreCase("PDF") && !PROMThumbnailType.equalsIgnoreCase("SOUND") && !PROMThumbnailType.equalsIgnoreCase("ETC")) {
                  if (template.equalsIgnoreCase("Y")) {
                     isTemplate = true;
                  }

                  File fileCmsHome = SecurityUtils.getSafeFile(THUMBNAIL_HOME);
                  if (!fileCmsHome.exists()) {
                     boolean fSuccess = fileCmsHome.mkdir();
                     if (!fSuccess) {
                        this.logger.error("mkdir Fail");
                     }
                  }

                  lfdFilePath = StrUtils.nvl(request.getHeader("STORE_PATH"));
                  if (StrUtils.nvl(lfdFilePath).equals(".\\")) {
                     lfdFilePath = "";
                  }

                  cmsMetaPath = THUMBNAIL_HOME + File.separator + content.getThumb_file_id() + StrUtils.nvl(lfdFilePath);
                  cfgFile = SecurityUtils.getSafeFile(cmsMetaPath);
                  if (!cfgFile.exists()) {
                     ret = cfgFile.mkdir();
                     if (!ret) {
                        this.logger.error("mkdir Fail");
                     }
                  }

                  cmsMetaPath = CONTENTS_HOME + "/" + content.getThumb_file_id() + "/" + content.getThumb_file_name();
                  String thumb_url = THUMBNAIL_HOME + "/" + content.getThumb_file_id() + "/" + content.getThumb_file_name();
                  File checkFile = SecurityUtils.getSafeFile(thumb_url);
                  BufferedImage bufferedImage;
                  int orgHeight;
                  if (!checkFile.exists()) {
                     thumbFile = SecurityUtils.getSafeFile(cmsMetaPath);
                     if (thumbFile.exists()) {
                        bufferedImage = ImageIO.read(thumbFile);
                        if (bufferedImage == null) {
                           this.logger.error("bufferImage is null " + thumbFile);
                           throw new NullPointerException();
                        }

                        orgWidth = bufferedImage.getWidth();
                        orgHeight = bufferedImage.getHeight();
                        smallWidth = 50;
                        int smallHeight = 38;
                        int mediumWidth = 165;
                        int mediumHeight = 109;
                        if ((long)(orgWidth / mediumWidth) > (long)(orgHeight / mediumHeight)) {
                           mediumHeight = orgHeight * mediumWidth / orgWidth;
                           if (mediumHeight % 2 != 0) {
                              ++mediumHeight;
                           }
                        } else {
                           mediumWidth = orgWidth * mediumHeight / orgHeight;
                           if (mediumWidth % 2 != 0) {
                              ++mediumWidth;
                           }
                        }

                        if ((long)(orgWidth / smallWidth) > (long)(orgHeight / smallHeight)) {
                           smallHeight = orgHeight * smallWidth / orgWidth;
                           if (smallHeight % 2 != 0) {
                              ++smallHeight;
                           }
                        } else {
                           smallWidth = orgWidth * smallHeight / orgHeight;
                           if (smallWidth % 2 != 0) {
                              ++smallWidth;
                           }
                        }

                        if (mediumWidth < 1) {
                           mediumWidth = 1;
                        }

                        if (mediumHeight < 1) {
                           mediumHeight = 1;
                        }

                        if (smallWidth < 1) {
                           smallWidth = 1;
                        }

                        if (smallHeight < 1) {
                           smallHeight = 1;
                        }

                        File thumb_File = SecurityUtils.getSafeFile(thumb_url);
                        thumb_smallFile = SecurityUtils.getSafeFile(thumb_url + "_SMALL.PNG");
                        thumb_mediumFile = SecurityUtils.getSafeFile(thumb_url + "_MEDIUM.PNG");
                        ImageIO.write(this.createResizedCopy(bufferedImage, orgWidth, orgHeight), "PNG", thumb_File);
                        ImageIO.write(this.createResizedCopy(bufferedImage, smallWidth, smallHeight), "PNG", thumb_smallFile);
                        ImageIO.write(this.createResizedCopy(bufferedImage, mediumWidth, mediumHeight), "PNG", thumb_mediumFile);
                        if (content.getMedia_type().equalsIgnoreCase("VWL")) {
                           thumb_url2 = CONTENTS_HOME + "/" + content.getMain_file_id() + "/" + content.getThumb_file_name();
                           File thumb_File2 = SecurityUtils.getSafeFile(thumb_url2);
                           ImageIO.write(this.createResizedCopy(bufferedImage, orgWidth, orgHeight), "PNG", thumb_File2);
                        }
                     }
                  } else if (content.getMedia_type().equalsIgnoreCase("VWL")) {
                     thumbFile = SecurityUtils.getSafeFile(cmsMetaPath);
                     bufferedImage = ImageIO.read(thumbFile);
                     if (bufferedImage != null) {
                        orgWidth = bufferedImage.getWidth();
                        orgHeight = bufferedImage.getHeight();
                        type = CONTENTS_HOME + "/" + content.getMain_file_id() + "/" + content.getThumb_file_name();
                        File thumb_File2 = SecurityUtils.getSafeFile(type);
                        ImageIO.write(this.createResizedCopy(bufferedImage, orgWidth, orgHeight), "PNG", thumb_File2);
                     }
                  }

                  if (content.getMedia_type().equalsIgnoreCase("IMAGE") || content.getMedia_type().equalsIgnoreCase("MOVIE")) {
                     modelCountMap = cmsDao.getHDThumbnailInfo(content.getContent_id());
                     if (((String)modelCountMap.get("result")).equals("success")) {
                        this.logger.info("Success Create HD Thumbnail.");
                     } else {
                        this.logger.error("Failed Create HD Thumbnail");
                     }
                  }
               }

               if (version > 1L && content.getMedia_type().equalsIgnoreCase("DLK")) {
                  cmsDao.deleteOldVersionContent(content.getContent_id(), version);
               }

               String cmsLFDPath;
               try {
                  UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
                  List contentNotiDataList = new ArrayList();
                  NotificationData notiData = new NotificationData();
                  notiData.setName(content.getContent_name());
                  notiData.setOrgId(content.getOrganization_id());
                  notiData.setOrgName(userGroupInfo.getGroupNameByGroupId(content.getOrganization_id()));
                  notiData.setUserName(userId);
                  contentNotiDataList.add(notiData);
                  if (version == 1L) {
                     cmsLFDPath = "Add content";
                  } else {
                     cmsLFDPath = "Edit content";
                  }

                  MailUtil.sendContentEventMail(contentNotiDataList, cmsLFDPath);
               } catch (Exception var68) {
                  this.logger.error(var68);
               }

               ContentFile file = cmsDao.getMainFileInfo(contentID);
               if (file.getIs_streaming().equalsIgnoreCase("Y")) {
                  lfdFilePath = System.getenv("MAGICINFO_PREMIUM_HOME");
                  cmsMetaPath = lfdFilePath + "/conf/streams.cfg";
                  cfgFile = SecurityUtils.getSafeFile(cmsMetaPath);
                  if (!cfgFile.exists()) {
                     ret = cfgFile.createNewFile();
                     if (!ret) {
                        this.logger.error("returned false");
                     }
                  }

                  FileOutputStream fos = new FileOutputStream(cfgFile, true);
                  OutputStreamWriter osw = new OutputStreamWriter(fos);
                  BufferedWriter bw = new BufferedWriter(osw);

                  try {
                     bw.newLine();
                     bw.write("[" + file.getFile_id() + "]");
                     bw.newLine();
                     bw.write("file=" + CONTENTS_HOME + File.separatorChar + file.getFile_id() + File.separatorChar + file.getFile_name());
                     bw.newLine();
                  } finally {
                     bw.close();
                     osw.close();
                     fos.close();
                  }
               }

               String dbFileID;
               UploadTemplateThread nodePlayerType;
               File type;
               String strFileName;
               String strFileSize;
               try {
                  if (content.getMedia_type().equalsIgnoreCase("LFT")) {
                     if (version > 1L) {
                        isTemplate = true;
                     }

                     cmsDao.updateUsedTemplateByContentId(contentID);
                     lfdFilePath = content.getMain_file_id();
                     cmsMetaPath = content.getMain_file_name();
                     cmsLFDPath = CONTENTS_HOME + File.separator + lfdFilePath + File.separator + cmsMetaPath;
                     cmsMetaPath = CONTENTS_HOME + File.separator + "contents_meta" + File.separator + contentID + File.separator + "ContentsMetadata.CSD";
                     DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
                     DocumentBuilder parser = dbf.newDocumentBuilder();
                     cmsMetaPath = SecurityUtils.directoryTraversalChecker(cmsMetaPath, request.getRemoteAddr());
                     Document doc = parser.parse(new FileInputStream(SecurityUtils.getSafeFile(cmsMetaPath)), "UTF-8");
                     Element contentElement = doc.getDocumentElement();
                     if (contentElement == null) {
                        this.logger.error("[JSServlet] DLK CSD file Invalid. Path=" + cmsMetaPath);
                     }

                     nodePlayerType = new UploadTemplateThread(cmsLFDPath, contentID, version);
                     nodePlayerType.start();
                     type = SecurityUtils.getSafeFile(cmsLFDPath);
                     Document lftFileDoc = parser.parse(type);
                     long pageCount = 0L;
                     XPathFactory factory = XPathFactory.newInstance();
                     XPath xpath = factory.newXPath();
                     thumb_smallFile = null;
                     thumb_mediumFile = null;
                     XPathExpression expr = xpath.compile((new XPathQueryBuilder("//DataLinkContentMeta/Page")).build());
                     Object result = expr.evaluate(lftFileDoc, XPathConstants.NODESET);
                     NodeList dataLinkPageNodeList = (NodeList)result;
                     pageCount = (long)dataLinkPageNodeList.getLength();
                     cmsDao.updateDatalinkLFDToContentInfo(contentID, version, pageCount);
                     NodeList fileList = contentElement.getElementsByTagName("TransferFile");
                     if (fileList == null || fileList.getLength() == 0) {
                        this.logger.error("\n DLK_LFD CSD file not found TransferFile Tag. Path=" + cmsMetaPath);
                     }

                     for(int i = 0; i < fileList.getLength(); ++i) {
                        Element fnameElement = (Element)fileList.item(i);
                        dbFileID = "";
                        String reqIndex = "";
                        if (fnameElement.hasAttribute("reqIndex")) {
                           reqIndex = fnameElement.getAttribute("reqIndex");
                        }

                        if (fnameElement.hasAttribute("type")) {
                           dbFileID = fnameElement.getAttribute("type");
                        }

                        if (dbFileID.equals("thumbnail") && Integer.parseInt(reqIndex) >= 1) {
                           if (fnameElement.hasChildNodes()) {
                              NodeList nodeFileName = fnameElement.getElementsByTagName("FileName");
                              NodeList nodeFileSize = fnameElement.getElementsByTagName("FileSize");
                              NodeList nodeFileHash = fnameElement.getElementsByTagName("FileHashValue");
                              Element elFileName = (Element)nodeFileName.item(0);
                              Element elFileSize = (Element)nodeFileSize.item(0);
                              Element elFileHash = (Element)nodeFileHash.item(0);
                              String strFileName = elFileName.getChildNodes().item(0).getNodeValue();
                              String strFileSize = elFileSize.getChildNodes().item(0).getNodeValue();
                              String strFileHash = elFileHash.getChildNodes().item(0).getNodeValue();
                              String dbFileID = cmsDao.getFileIDByHash(strFileName, Long.parseLong(strFileSize), strFileHash);
                              if (strFileName != null && dbFileID != null) {
                                 strFileName = SecurityUtils.directoryTraversalChecker(strFileName, request.getRemoteAddr());
                                 this.copyFile(CONTENTS_HOME + File.separator + dbFileID + File.separator + strFileName, CONTENTS_HOME + File.separator + content.getMain_file_id() + File.separator + strFileName);
                              } else {
                                 this.logger.error("\n ThumbNail fileName(CSD) or fileID(DB) is null. Can't copy.CSD fileName=" + strFileName + ",DB fileID=" + dbFileID + "\n path=" + cmsMetaPath);
                              }
                           } else {
                              this.logger.error("\n TransFile(thumbnail) do not have childNode. Path=" + cmsMetaPath);
                           }
                        }
                     }

                     if (isTemplate) {
                        ContentInfo cInfo = ContentInfoImpl.getInstance();
                        type = null;
                        List dlkContentList = cInfo.getDlkContentIdByTemplateId(contentID);
                        if (dlkContentList != null && dlkContentList.size() > 0) {
                           for(smallWidth = 0; smallWidth < dlkContentList.size(); ++smallWidth) {
                              ContentXmlManager contentXmlManager = new ContentXmlManager();
                              Map dlkContent = (Map)dlkContentList.get(smallWidth);
                              String dlkContentId = dlkContent.get("DLK_CONTENT_ID").toString();
                              String strVersionID = cInfo.getActiveVersionByContentId(contentID);
                              long nextVersion = cInfo.getContentNextVer(dlkContentId);
                              thumb_url2 = cInfo.getMainFileInfo(dlkContentId).getFile_id();
                              strFileName = UUID.randomUUID().toString().toUpperCase();
                              strFileSize = cInfo.getFileInfoByContentIdVersionId(contentID, strVersionID);
                              ContentFile dlkFile = cInfo.getFileInfo(thumb_url2);
                              dbFileID = dlkFile.getFile_path() + File.separator + dlkFile.getFile_name();
                              ContentFile templateFile = cInfo.getFileInfo(strFileSize);
                              ContentFile contentDlkFile = new ContentFile();
                              String newDlkFilePath = CONTENTS_HOME + File.separator + strFileName;
                              String newDlkFilePathFile = CONTENTS_HOME + File.separator + strFileName + File.separator + dlkFile.getFile_name();
                              File fileCmsFile = SecurityUtils.getSafeFile(newDlkFilePath);
                              if (!fileCmsFile.exists()) {
                                 boolean fSuccess = fileCmsFile.mkdir();
                                 if (!fSuccess) {
                                    this.logger.error("mkdir Fail");
                                 }
                              }

                              contentXmlManager.modifyTemplateInfo(templateFile, dbFileID, newDlkFilePathFile, nextVersion, contentID, dlkContentId);
                              contentDlkFile.setFile_id(strFileName);
                              contentDlkFile.setFile_path(newDlkFilePath);
                              contentDlkFile.setFile_name(dlkFile.getFile_name());
                              contentDlkFile.setCreator_id(userId);
                              contentDlkFile.setFile_type("DLK");
                              contentDlkFile.setFile_size(dlkFile.getFile_size());
                              String hashCode = "";
                              long fileSize = 0L;
                              hashCode = FileUtils.getHash(SecurityUtils.getSafeFile(newDlkFilePathFile));
                              fileSize = SecurityUtils.getSafeFile(newDlkFilePathFile).length();
                              cInfo.addFile(contentDlkFile);
                              cInfo.updateThumbnailIdOfDlkByContentId(contentID, strVersionID, dlkContentId);
                              cInfo.updateHashCodeByMainFileId(strFileName, fileSize, hashCode);
                              cInfo.updateVersionAndMainFileIdInContentVersionInfo(nextVersion, strFileName.toUpperCase(), dlkContentId.toUpperCase());
                              ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
                              schInfo.setContentTrigger(dlkContentId);
                              List pList = cInfo.getPlaylistListUsingContent(dlkContentId);

                              for(int k = 0; k < pList.size(); ++k) {
                                 Map map = (Map)pList.get(k);
                                 String playlistId = (String)map.get("playlist_id");
                                 schInfo.setPlaylistTrigger(playlistId);
                              }

                              EventInfo eInfo = EventInfoImpl.getInstance();
                              eInfo.setContentTrigger(dlkContentId);

                              for(int k = 0; k < pList.size(); ++k) {
                                 Map map = (Map)pList.get(k);
                                 String playlistId = (String)map.get("playlist_id");
                                 eInfo.setPlaylistTrigger(playlistId);
                              }
                           }
                        }
                     }
                  }
               } catch (Exception var73) {
                  this.logger.error("\n [JSServlet] DLK CSD or LFD file error !!! ContentID=" + contentID, var73);
               }

               Node parent;
               try {
                  if (content.getMedia_type().equalsIgnoreCase("VWL")) {
                     int vwlVersion = 1;
                     this.logger.info("[JobStateServlet] VWL thumbnail move Logic Start !!!");
                     cmsMetaPath = CONTENTS_HOME + File.separator + "contents_meta" + File.separator + contentID + File.separator + "ContentsMetadata.CSD";
                     DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
                     DocumentBuilder parser = dbf.newDocumentBuilder();
                     Document doc = parser.parse(SecurityUtils.getSafeFile(cmsMetaPath));
                     Element contentElement = doc.getDocumentElement();
                     if (contentElement == null) {
                        this.logger.error("\n VWL CSD file Invalid. Path=" + cmsMetaPath);
                     }

                     thumbFile = null;
                     if (contentElement.getElementsByTagName("VwlVersion") != null) {
                        Node VwlVersion = contentElement.getElementsByTagName("VwlVersion").item(0);
                        if (VwlVersion != null) {
                           if (VwlVersion.hasChildNodes()) {
                              vwlVersion = Integer.parseInt(VwlVersion.getChildNodes().item(0).getNodeValue().toString());
                           } else {
                              vwlVersion = 1;
                           }
                        }
                     }

                     NodeList fileList = contentElement.getElementsByTagName("TransferFile");
                     if (fileList == null || fileList.getLength() == 0) {
                        this.logger.error("\n VWL CSD file not found TransferFile Tag. Path=" + cmsMetaPath);
                     }

                     for(orgWidth = 0; orgWidth < fileList.getLength(); ++orgWidth) {
                        Element fnameElement = (Element)fileList.item(orgWidth);
                        type = "";
                        String reqIndex = "";
                        if (fnameElement.hasAttribute("reqIndex")) {
                           reqIndex = fnameElement.getAttribute("reqIndex");
                        }

                        if (fnameElement.hasAttribute("type")) {
                           type = fnameElement.getAttribute("type");
                        }

                        if (type.equals("thumbnail") && Integer.parseInt(reqIndex) > 1) {
                           if (fnameElement.hasChildNodes()) {
                              NodeList nodeFileName = fnameElement.getElementsByTagName("FileName");
                              NodeList nodeFileSize = fnameElement.getElementsByTagName("FileSize");
                              NodeList nodeFileHash = fnameElement.getElementsByTagName("FileHashValue");
                              Element elFileName = (Element)nodeFileName.item(0);
                              Element elFileSize = (Element)nodeFileSize.item(0);
                              Element elFileHash = (Element)nodeFileHash.item(0);
                              strFileName = elFileName.getChildNodes().item(0).getNodeValue();
                              strFileSize = elFileSize.getChildNodes().item(0).getNodeValue();
                              String strFileHash = elFileHash.getChildNodes().item(0).getNodeValue();
                              dbFileID = cmsDao.getFileIDByHash(strFileName, Long.parseLong(strFileSize), strFileHash);
                              if (strFileName != null && dbFileID != null) {
                                 vwlVersion = 2;
                                 this.moveFile(CONTENTS_HOME + File.separator + dbFileID + File.separator, strFileName, CONTENTS_HOME + File.separator + content.getMain_file_id() + File.separator, strFileName);
                                 cmsDao.setFilePath(dbFileID, CONTENTS_HOME + File.separator + content.getMain_file_id());
                              } else {
                                 this.logger.error("\n ThumbNail fileName(CSD) or fileID(DB) is null. Can't copy.CSD fileName=" + strFileName + ",DB fileID=" + dbFileID + "\n path=" + cmsMetaPath);
                              }
                           } else {
                              this.logger.error("\n TransFile(thumbnail) do not have childNode. Path=" + cmsMetaPath);
                           }
                        }
                     }

                     nodePlayerType = null;
                     if (contentElement.getElementsByTagName("PlayerType") != null) {
                        parent = contentElement.getElementsByTagName("PlayerType").item(0);
                        if (parent != null) {
                           type = null;
                           if (parent.hasChildNodes()) {
                              String type = parent.getChildNodes().item(0).getNodeValue().toString();
                              if (type.equals("SPLAYER")) {
                                 vwlVersion = 11;
                              }
                           }
                        }
                     }

                     cmsDao.updateVwlVersion(contentID, vwlVersion);
                     this.logger.info("[JobStateServlet] VWL thumbnail move Logic End !!!");
                  }
               } catch (Exception var72) {
                  this.logger.error("\n VWL thumbnail file move failed. check CSD file !!! ContentID=" + contentID, var72);
               }

               if (content != null & content.getMedia_type() != null) {
                  try {
                     DocumentBuilderFactory dbf2;
                     DocumentBuilder parser2;
                     Element contentElement2;
                     Document doc2;
                     NodeList fileItems;
                     if (content.getMedia_type().equalsIgnoreCase("VWL")) {
                        this.logger.info("[JobStateServlet] VWL model_count_info add to DB Logic Start !!!");
                        lfdFilePath = CONTENTS_HOME + File.separator + content.getMain_file_id() + File.separator + content.getMain_file_name();
                        dbf2 = DocumentBuilderFactory.newInstance();
                        parser2 = dbf2.newDocumentBuilder();
                        doc2 = parser2.parse(SecurityUtils.getSafeFile(lfdFilePath));
                        contentElement2 = doc2.getDocumentElement();
                        fileItems = contentElement2.getElementsByTagName("Model");
                        modelCountMap = VWLParser.getModelCountMap(fileItems);
                        cmsDao.updateVwlModelCountInfo(content.getContent_id(), VWLParser.getModelCountInfo(modelCountMap));
                        this.logger.info("[JobStateServlet] VWL model_count_info add to DB Logic End !!!");
                     } else if (content.getMedia_type().equalsIgnoreCase("LFD")) {
                        this.logger.info("[JobStateServlet] Set delete_lock to Y which file is used in the LFD !!!");
                        lfdFilePath = CONTENTS_HOME + File.separator + content.getMain_file_id() + File.separator + content.getMain_file_name();
                        dbf2 = DocumentBuilderFactory.newInstance();
                        parser2 = dbf2.newDocumentBuilder();
                        doc2 = parser2.parse(SecurityUtils.getSafeFile(lfdFilePath));
                        contentElement2 = doc2.getDocumentElement();
                        fileItems = contentElement2.getElementsByTagName("FileItems");
                        if (fileItems != null) {
                           ContentInfo cInfo = ContentInfoImpl.getInstance();

                           for(int i = 0; i < fileItems.getLength(); ++i) {
                              try {
                                 parent = fileItems.item(i);
                                 if (parent != null) {
                                    for(Node child = parent.getFirstChild(); child != null; child = child.getNextSibling()) {
                                       try {
                                          if (child instanceof Element && "FileItem".equals(child.getNodeName())) {
                                             NamedNodeMap nodeMap = child.getAttributes();
                                             if (nodeMap != null) {
                                                Node fileIDNode = nodeMap.getNamedItem("FileID");
                                                if (fileIDNode != null) {
                                                   String fileID = fileIDNode.getTextContent();
                                                   if (fileID != null) {
                                                      ContentFile cFile = cInfo.getFileInfo(fileID);
                                                      if (cFile != null && cFile.getFile_id() != null) {
                                                         this.logger.error("[deleteLock] Set deleteLock to Y for fileID :" + fileID);
                                                         cInfo.setDeleteLock(fileID, "Y");
                                                      }
                                                   }
                                                }
                                             }
                                          }
                                       } catch (Exception var66) {
                                          this.logger.error("Error in second for loop to set deleteLock about LFD file");
                                       }
                                    }
                                 }
                              } catch (Exception var70) {
                                 this.logger.error("Error in first for loop to set deleteLock about LFD file");
                              }
                           }
                        }

                        this.logger.info("[JobStateServlet] End of Setting delete_lock to Y which file is used in the LFD !!!");
                     }
                  } catch (Exception var71) {
                     this.logger.error("[JobStateServlet] Error in Setting delete_lock to Y which file is used in the LFD !!! ContentID=" + contentID);
                     this.logger.error("[JobStateServlet] Error in Setting delete_lock to Y which file is used in the LFD !!! getMedia_type()=" + content.getMedia_type());
                     this.logger.error("", var71);
                  }
               }
            }
         } else if (!jobState.equalsIgnoreCase("EXIST") && !jobState.equalsIgnoreCase("CANCEL")) {
            cmsDao.deleteVersionContent(contentID, 0L);
         }

         String strHost = request.getServerName();
         String strPort = String.valueOf(request.getServerPort());
         if ("80".equals(strPort)) {
            strPort = "";
         } else {
            strPort = ":" + strPort;
         }

         validationResult = request.getContextPath();
         String url = RequestUtils.getProtocolScheme(request) + strHost + strPort + validationResult;
         response.setHeader("REDIRECT_URL", url + "/login.htm?cmd=NotNormal&user_id=" + userId + "&password=" + token);
         this.logger.info("[JSServlet] Success ");
         MonitoringManager monitoringMgr = MonitoringManagerImpl.getInstance();
         monitoringMgr.setUploadContentId(contentID);
      } catch (Exception var74) {
         response.sendError(600, var74.toString());
         response.setHeader("code", "600");
         response.setHeader("message", "error : " + var74.getMessage());
         this.logger.error(var74.getStackTrace());
      }

   }

   private BufferedImage createResizedCopy(Image originalImage, int scaledWidth, int scaledHeight) {
      BufferedImage scaledBI = new BufferedImage(scaledWidth, scaledHeight, 3);
      Graphics2D g = scaledBI.createGraphics();
      g.setComposite(AlphaComposite.Src);
      g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
      g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
      g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
      g.drawImage(originalImage, 0, 0, scaledWidth, scaledHeight, (ImageObserver)null);
      g.dispose();
      return scaledBI;
   }

   public void copyFile(String srcPath, String dstPath) {
      FileInputStream fis = null;
      FileOutputStream fos = null;
      boolean var5 = false;

      try {
         fis = new FileInputStream(SecurityUtils.directoryTraversalChecker(srcPath, (String)null));
         fos = new FileOutputStream(dstPath);

         int data;
         try {
            while((data = fis.read()) != -1) {
               fos.write(data);
            }
         } catch (Exception var16) {
            this.logger.error(var16);
         }
      } catch (Exception var17) {
         this.logger.error("[JobStateServlet]:move file fail !!!");
      } finally {
         try {
            if (fis != null) {
               fis.close();
            }

            if (fos != null) {
               fos.close();
            }
         } catch (IOException var15) {
            this.logger.error("", var15);
         }

      }

   }

   public void moveFile(String srcPath, String srcFileName, String dstPath, String dstFileName) {
      try {
         File in = SecurityUtils.getSafeFile(srcPath, srcFileName);
         File out = SecurityUtils.getSafeFile(dstPath, dstFileName);
         boolean fSuccess = in.renameTo(out);
         if (!fSuccess) {
            this.logger.error("rename Fail");
         }
      } catch (Exception var8) {
         this.logger.error("[JobStateServlet]:VWL move file fail !!! FileName=" + srcFileName);
      }

   }
}
