package com.samsung.magicinfo.openapi.impl;

public class OpenApiExceptionCode {
   public static final String[] E001 = new String[]{"E001", "요청 하실 때 service나 xml 변수를 넣어 주십시오. (스펙 문서를 참고해 주십시오.)"};
   public static final String[] E002 = new String[]{"E002", "Service Called is not Open Method... "};
   public static final String[] E004 = new String[]{"E004", "Does not match the number of parameters."};
   public static final String[] E005 = new String[]{"E005", "User information is required."};
   public static final String[] E006 = new String[]{"E006", "Token is missing."};
   public static final String[] E007 = new String[]{"E007", "Tokens are not valid."};
   public static final String[] E010 = new String[]{"E010", "Unknown Class List"};
   public static final String[] E100 = new String[]{"E100", "The confirmation does not match the ID or Password"};
   public static final String[] E109 = new String[]{"E109", "inspiration fail or invalid token"};
   public static final String[] E110 = new String[]{"E110", "fail"};
   public static final String[] E201 = new String[]{"E201", "Response XML을 작성하는 도중 에러가 발생했습니다. : "};
   public static final String[] E202 = new String[]{"E202", "응답 객체가 XmlTransferable 을 지원하지 않습니다 : "};
   public static final String[] E301 = new String[]{"E301", "Service not authorized"};
   public static final String[] E401 = new String[]{"E401", "Saas is not enable"};
   public static final String[] E991 = new String[]{"E991", "Internal Error (IllegalAccessException)"};
   public static final String[] E992 = new String[]{"E992", "Internal Error (NoSuchMethodException)"};
   public static final String[] E993 = new String[]{"E993", "Internal Error (InvocationTargetException)"};
   public static final String[] E999 = new String[]{"E999", "Unkown Error!"};
   public static final String[] E900 = new String[]{"E900", "License Error"};
   public static final String[] D001 = new String[]{"D001", "Service not authorized(Device Read Authority)"};
   public static final String[] D002 = new String[]{"D002", "Service not authorized(Device Write Authority)"};
   public static final String[] D003 = new String[]{"D003", "Service not authorized(Remote Job Read Authority)"};
   public static final String[] D004 = new String[]{"D004", "Service not authorized(Device Approval Authority)"};
   public static final String[] D005 = new String[]{"D005", "DeviceId parameter is missing."};
   public static final String[] D006 = new String[]{"D006", "Data not found."};
   public static final String[] D007 = new String[]{"D007", "Send Setting Request Failed."};
   public static final String[] D008 = new String[]{"D008", "Received Setting Result Failed."};
   public static final String[] D009 = new String[]{"D009", "Time parameter invalid."};
   public static final String[] D010 = new String[]{"D010", "Send Getting Request Failed."};
   public static final String[] D011 = new String[]{"D011", "Received Getting Result Failed."};
   public static final String[] D012 = new String[]{"D012", "Player Request Failed."};
   public static final String[] D013 = new String[]{"D013", "Device not found."};
   public static final String[] D014 = new String[]{"D014", "Device Approval Failed."};
   public static final String[] D015 = new String[]{"D015", "Device Not Connected.(Power off)"};
   public static final String[] D016 = new String[]{"D016", "Request Timeout.(Device No Response)"};
   public static final String[] D017 = new String[]{"D017", "Type of ALL is not supported to the parameter of deviceType in this API."};
   public static final String[] D018 = new String[]{"D018", "You cannot use this API. (only iPLAYER)"};
   public static final String[] D019 = new String[]{"D019", "Service not authorized(Device Log Authority)"};
   public static final String[] D020 = new String[]{"D020", "SPLAYER needs deviceModelName value."};
   public static final String[] D021 = new String[]{"D021", "Not supported Predefined Cmd."};
   public static final String[] D022 = new String[]{"D022", "This group is not layout group"};
   public static final String[] D023 = new String[]{"D023", "Device type is not supported"};
   public static final String[] D101 = new String[]{"D101", "Service not authorized(Lite Device Read Authority)"};
   public static final String[] D102 = new String[]{"D102", "Service not authorized(Lite Device Write Authority)"};
   public static final String[] D103 = new String[]{"D103", "Service not authorized(Lite Remote Job Read Authority)"};
   public static final String[] D104 = new String[]{"D104", "Service not authorized(Device Approval Authority)"};
   public static final String[] D201 = new String[]{"D201", "Service not authorized(Videowall Read Authority)"};
   public static final String[] D202 = new String[]{"D202", "Service not authorized(Videowall Write Authority)"};
   public static final String[] D203 = new String[]{"D203", "Service not authorized(Lite Remote Job Read Authority)"};
   public static final String[] D204 = new String[]{"D204", "Service not authorized(Videowall Approval Authority)"};
   public static final String[] D300 = new String[]{"D300", "DeviceSystemSetupConf Entity Exception."};
   public static final String[] D301 = new String[]{"D301", "DeviceSystemSetupConf Parameter none."};
   public static final String[] D302 = new String[]{"D302", "Not Changed Parameter : trigger_interval."};
   public static final String[] D303 = new String[]{"D303", "Check Parameter : trigger_interval [ 1 ~ 60(sec) ]"};
   public static final String[] D304 = new String[]{"D304", "Not Changed Parameter : ftp_connect_mode."};
   public static final String[] D305 = new String[]{"D305", "Check Parameter : ftp_connect_mode [ ACTIVE / PASSIVE ]"};
   public static final String[] D306 = new String[]{"D306", "Not Changed Parameter : monitoring_interval."};
   public static final String[] D307 = new String[]{"D307", "Check Parameter : monitoring_interval [ 1 ~ 60(min) ]"};
   public static final String[] D308 = new String[]{"D308", "Not Changed Parameter : connection_limit_time."};
   public static final String[] D309 = new String[]{"D309", "Check Parameter : connection_limit_time [ 1 ~ 300(sec) ]"};
   public static final String[] D310 = new String[]{"D310", "Not Changed Parameter : time_zone_index."};
   public static final String[] D311 = new String[]{"D311", "Check Parameter : time_zone_index [ Not Exist ]"};
   public static final String[] D312 = new String[]{"D312", "Not Changed Parameter : tunneling_server"};
   public static final String[] D313 = new String[]{"D313", "Check Parameter : tunneling_server [ max_length is 80]"};
   public static final String[] D314 = new String[]{"D314", "Not Changed Parameter : repository_path"};
   public static final String[] D315 = new String[]{"D315", "Check Parameter : repository_path [ max_length is 200]"};
   public static final String[] D316 = new String[]{"D316", "Not Changed Parameter : screen_capture_interval"};
   public static final String[] D317 = new String[]{"D317", "Check Parameter : screen_capture_interval [ 1 ~ 180(min) ]"};
   public static final String[] D318 = new String[]{"D318", "Not Changed Parameter : proxy_setting"};
   public static final String[] D319 = new String[]{"D319", "Check Parameter : proxy_setting. format[ 0/1/2;*;*;*;* ]"};
   public static final String[] D320 = new String[]{"D320", "Not Changed Parameter : mnt_folder_path"};
   public static final String[] D321 = new String[]{"D321", "Check Parameter : mnt_folder_path[ max_length is 200 ]"};
   public static final String[] D322 = new String[]{"D322", "Not Changed Parameter : system_restart_interval"};
   public static final String[] D323 = new String[]{"D323", "Check Parameter : system_restart_interval. format[ Sun;Mon;Tue;Wed;Thu;Fri;Sat hh:mm ]"};
   public static final String[] D324 = new String[]{"D324", "Check Parameter : system_restart_interval's day format. [ Sun;Mon;Tue;Wed;Thu;Fri;Sat ]"};
   public static final String[] D325 = new String[]{"D325", "Check Parameter : system_restart_interval's time format. [ hh(0~23):mm(0~59) ]"};
   public static final String[] D326 = new String[]{"D326", "Not Changed Parameter : log_mnt"};
   public static final String[] D327 = new String[]{"D327", "Check Parameter : log_mnt. format[ 0/1/2;1~365(day);1~300(MB) ]"};
   public static final String[] D328 = new String[]{"D328", "Not Changed Parameter : proof_of_play_mnt"};
   public static final String[] D329 = new String[]{"D329", "Check Parameter : proof_of_play_mnt. format[ 1~365(day);1~300(MB) ]"};
   public static final String[] D330 = new String[]{"D330", "Not Changed Parameter : content_mnt"};
   public static final String[] D331 = new String[]{"D331", "Check Parameter : content_mnt. format[ 1~365(day);1~300(MB) ]"};
   public static final String[] D332 = new String[]{"D332", "Not Changed Parameter : screen_rotation"};
   public static final String[] D333 = new String[]{"D333", "Check Parameter : screen_rotation. format[ 0 / 270 ]"};
   public static final String[] D334 = new String[]{"D334", "Not Changed Parameter : reset_password"};
   public static final String[] D335 = new String[]{"D335", "Check Parameter : reset_password. format[ 0 / 1 ]"};
   public static final String[] D336 = new String[]{"D336", "Not Changed Parameter : switch_time"};
   public static final String[] D337 = new String[]{"D337", "Not Changed Parameter : Day Light Saving"};
   public static final String[] D338 = new String[]{"D338", "Not Changed Parameter : Day Light Saving Manual"};
   public static final String[] D400 = new String[]{"D400", "DeviceDisplayConf Entity Exception."};
   public static final String[] D401 = new String[]{"D401", "DeviceDisplayConf Parameter none."};
   public static final String[] D402 = new String[]{"D402", "Not Changed Parameter : basic_power(Already PowerOff)"};
   public static final String[] D403 = new String[]{"D403", "Check Parameter : basic_power [ 0(Power Off) ]"};
   public static final String[] D404 = new String[]{"D404", "Not Changed Parameter : basic_volume"};
   public static final String[] D405 = new String[]{"D405", "Check Parameter : basic_volume[ 0 ~ 100 ]"};
   public static final String[] D406 = new String[]{"D406", "Not Changed Parameter : basic_mute"};
   public static final String[] D407 = new String[]{"D407", "Check Parameter : basic_mute [ 0(Mute Off) ~ 1(Mute On) ]"};
   public static final String[] D408 = new String[]{"D408", "Not Changed Parameter : basic_source"};
   public static final String[] D409 = new String[]{"D409", "Check Parameter : basic_source [ 4,8,12,20,24,30,32,33,35,37,48,64,96 ]"};
   public static final String[] D410 = new String[]{"D410", "Not Changed Parameter : basic_direct_channel"};
   public static final String[] D411 = new String[]{"D411", "Can't changed basic_direct_channel [Basic Source is not ATV/DTV]"};
   public static final String[] D412 = new String[]{"D412", "Check Parameter : basic_direct_channel [ *;*;*;*;*;* ]"};
   public static final String[] D413 = new String[]{"D413", "Not Changed Parameter : basic_panel_status"};
   public static final String[] D414 = new String[]{"D414", "Check Parameter : basic_panel_status [ 0(Panel On) / 1(Panel Off) ]"};
   public static final String[] D415 = new String[]{"D415", "Not Changed Parameter : mnt_auto"};
   public static final String[] D416 = new String[]{"D416", "Check Parameter : mnt_auto [ *;*;*;*;*;*;*;* ]"};
   public static final String[] D417 = new String[]{"D417", "Not Changed Parameter : mnt_manual"};
   public static final String[] D418 = new String[]{"D418", "Check Parameter : mnt_manual[ 0 ~ 100 ]"};
   public static final String[] D419 = new String[]{"D419", "Not Changed Parameter : mnt_safety_screen_timer"};
   public static final String[] D420 = new String[]{"D420", "Check Parameter : mnt_safety_screen_timer [ *;*;* ]"};
   public static final String[] D421 = new String[]{"D421", "Not Changed Parameter : mnt_safety_screen_run"};
   public static final String[] D422 = new String[]{"D422", "Check Parameter : mnt_safety_screen_run [ 0/1/2/3/4/6/7 ]"};
   public static final String[] D423 = new String[]{"D423", "Not Changed Parameter : mnt_pixel_shift"};
   public static final String[] D424 = new String[]{"D424", "Check Parameter : mnt_pixel_shift [ *;*;*;* ]"};
   public static final String[] D425 = new String[]{"D425", "Not Changed Parameter : mnt_safety_lock"};
   public static final String[] D426 = new String[]{"D426", "Check Parameter : mnt_safety_lock [ 0(Off)/1(On) ]"};
   public static final String[] D427 = new String[]{"D427", "Not Changed Parameter : advanced_osd_display"};
   public static final String[] D428 = new String[]{"D428", "Check Parameter : advanced_osd_display [ *;* ]"};
   public static final String[] D429 = new String[]{"D429", "Not Changed Parameter : advandced_fan_control"};
   public static final String[] D430 = new String[]{"D430", "Check Parameter : advanced_fan_control [ 0(manual)/1(auto) ]"};
   public static final String[] D431 = new String[]{"D431", "Not Changed Parameter : advandced_auto_power"};
   public static final String[] D432 = new String[]{"D432", "Check Parameter : advanced_auto_power[ 0(off)/1(on) ]"};
   public static final String[] D433 = new String[]{"D433", "Not Changed Parameter : advandced_stand_by"};
   public static final String[] D434 = new String[]{"D434", "Check Parameter : advanced_stand_by [ 0(off)/1(on)/2(auto) ]"};
   public static final String[] D435 = new String[]{"D435", "Not Changed Parameter : misc_remocon"};
   public static final String[] D436 = new String[]{"D436", "Check Parameter : misc_remocon [ 0(Disable)/1(Enable) ]"};
   public static final String[] D437 = new String[]{"D437", "Not Changed Parameter : misc_panel_lock"};
   public static final String[] D438 = new String[]{"D438", "Check Parameter : misc_panel_lock [ 0(Unlock)/1(Lock) ]"};
   public static final String[] D439 = new String[]{"D439", "Not Changed Parameter : misc_osd"};
   public static final String[] D440 = new String[]{"D440", "Check Parameter : misc_osd[ 0(Off)/1(On) ]"};
   public static final String[] D441 = new String[]{"D441", "Not Changed Parameter : diagnosis_alarm_temperature"};
   public static final String[] D442 = new String[]{"D442", "Check Parameter : diagnosis_alarm_temperature [ 75 ~ 125 ]"};
   public static final String[] D443 = new String[]{"D443", "Not Changed Parameter : misc_all_lock"};
   public static final String[] D444 = new String[]{"D444", "Check Parameter : misc_all_lock [ 0(off) / 1(on) ]"};
   public static final String[] D500 = new String[]{"D500", "DeviceTimeConf Entity Exception."};
   public static final String[] D501 = new String[]{"D501", "DeviceTimeConf Parameter none."};
   public static final String[] D502 = new String[]{"D502", "Check Timer Range format [ Date/Hour/Min/AmPm ... ]"};
   public static final String[] D503 = new String[]{"D503", "Not Changed Parameter : timer_clock"};
   public static final String[] D504 = new String[]{"D504", "Check Parameter : timer_clock [ *;*;*;*;*;*;* ]"};
   public static final String[] D505 = new String[]{"D505", "Not Changed Parameter : timer_timer1"};
   public static final String[] D506 = new String[]{"D506", "Check Parameter : timer_timer1[ *;*;*;*;*;*;*;*;*;*;*;*;*;*;* ] or [ *;*;*;*;*;*;*;*;*;*;*;*;* ]"};
   public static final String[] D507 = new String[]{"D507", "Check Parameter : timer_timer1 format [ repeat/weekly/volume/inputSource/holiday_apply ]"};
   public static final String[] D508 = new String[]{"D508", "Not Changed Parameter : timer_timer2"};
   public static final String[] D509 = new String[]{"D509", "Check Parameter : timer_timer2[ *;*;*;*;*;*;*;*;*;*;*;*;*;*;* ] or [ *;*;*;*;*;*;*;*;*;*;*;*;* ]"};
   public static final String[] D510 = new String[]{"D510", "Check Parameter : timer_timer2 format [ repeat/weekly/volume/inputSource/holiday_apply ]"};
   public static final String[] D511 = new String[]{"D511", "Not Changed Parameter : timer_timer3"};
   public static final String[] D512 = new String[]{"D512", "Check Parameter : timer_timer3[ *;*;*;*;*;*;*;*;*;*;*;*;*;*;* ] or [ *;*;*;*;*;*;*;*;*;*;*;*;* ]"};
   public static final String[] D513 = new String[]{"D513", "Check Parameter : timer_timer3 format [ repeat/weekly/volume/inputSource/holiday_apply ]"};
   public static final String[] D514 = new String[]{"D514", "Not Changed Parameter : timer_holiday"};
   public static final String[] D515 = new String[]{"D515", "Check Parameter : timer_holiday[ *;*;*;*;* ]"};
   public static final String[] D516 = new String[]{"D516", "Check Parameter : timer_holiday format [ 0~2, management_command ]"};
   public static final String[] D517 = new String[]{"D517", "Not Changed Parameter : time_current_time"};
   public static final String[] D518 = new String[]{"D518", "Check Parameter : time_current_time [ *;*;* ]"};
   public static final String[] D519 = new String[]{"D519", "Not Changed Parameter : time_on_time"};
   public static final String[] D520 = new String[]{"D520", "Check Parameter : time_on_time [ *;*;*;*;*;* ]"};
   public static final String[] D521 = new String[]{"D521", "Check Parameter : time_on_time format [ volume/Act,Inact/BasicInputSource ]"};
   public static final String[] D522 = new String[]{"D522", "Not Changed Parameter : time_off_time"};
   public static final String[] D523 = new String[]{"D523", "Check Parameter : time_off_time [ *;*;*;* ]"};
   public static final String[] D524 = new String[]{"D524", "Check Parameter : time_off_time format [ Act,Inact ]"};
   public static final String[] D525 = new String[]{"D525", "Device Support New Time format Only."};
   public static final String[] D526 = new String[]{"D526", "Device Support Old Time format Only."};
   public static final String[] D600 = new String[]{"D600", "Parent group does not exist."};
   public static final String[] D601 = new String[]{"D601", "Invalid depth of parent group."};
   public static final String[] D602 = new String[]{"D602", "You cannot use 0 to the p_group_id"};
   public static final String[] D603 = new String[]{"D603", "You cannot delete Organization."};
   public static final String[] D604 = new String[]{"D604", "You cannot rename Organization."};
   public static final String[] D605 = new String[]{"D605", "Cannot move device from VWL LayoutGroup."};
   public static final String[] D606 = new String[]{"D606", "Cannot move device to VWL LayoutGroup."};
   public static final String[] D607 = new String[]{"D607", "Cannot move device from Backup Play set group."};
   public static final String[] D608 = new String[]{"D608", "Cannot move device to Backup Play set group."};
   public static final String[] D609 = new String[]{"D609", "You cannot move to Organization."};
   public static final String[] D610 = new String[]{"D610", "Device Move Fail."};
   public static final String[] D611 = new String[]{"D611", "Device Move Success but deply schedule fail."};
   public static final String[] D700 = new String[]{"D700", "File size exceed."};
   public static final String[] D701 = new String[]{"D701", "Not supported file type. Custom Logo : movie up to 150MB/picture up to 50MB, Default Content: only video."};
   public static final String[] D702 = new String[]{"D702", "Wrong customize file type. Should be 1 for Default Content, 2 for Custom Logo"};
   public static final String[] D703 = new String[]{"D703", "Faled to upload customize file"};
   public static final String[] D704 = new String[]{"D704", "Customize File publish fail"};
   public static final String[] D999 = new String[]{"D999", "Device Service Internal Error."};
   public static final String[] V001 = new String[]{"V001", "The required parameters must be present."};
   public static final String[] V002 = new String[]{"V002", "Invalid value for parameter."};
   public static final String[] V003 = new String[]{"V003", "The value already exists."};
   public static final String[] V101 = new String[]{"V101", "The range of values you entered is invalid. Please check again."};
   public static final String[] V102 = new String[]{"V102", "The Start Date must be earlier than the End Date."};
   public static final String[] V103 = new String[]{"V103", "The search period cannot be later than today."};
   public static final String[] V104 = new String[]{"V104", "fromDate parameter is missing."};
   public static final String[] V105 = new String[]{"V105", "ToDate parameter is missing."};
   public static final String[] V201 = new String[]{"V201", "You cannot use this API. (only Premium)"};
   public static final String[] G101 = new String[]{"G101", "You cannot enter invalid special characters."};
   public static final String[] G102 = new String[]{"G102", "The same group name already exists."};
   public static final String[] G103 = new String[]{"G103", "This group id does not exist."};
   public static final String[] G104 = new String[]{"G104", "This group id is wrong."};
   public static final String[] G105 = new String[]{"G105", "This Content Group can not delete."};
   public static final String[] G106 = new String[]{"G106", "Fail to add the organization."};
   public static final String[] G107 = new String[]{"G107", "Fail to add the group."};
   public static final String[] G108 = new String[]{"G108", "This is not the organization."};
   public static final String[] G109 = new String[]{"G109", "This group id does not exist in the same organization."};
   public static final String[] G110 = new String[]{"G110", "This group id is not the organization."};
   public static final String[] G111 = new String[]{"G111", "This group id and the organization id of this user are same."};
   public static final String[] G112 = new String[]{"G112", "You cannot change the user organization of the administrator."};
   public static final String[] G113 = new String[]{"G113", "You cannot change the Administrator organization."};
   public static final String[] G114 = new String[]{"G114", "The organization is not exist."};
   public static final String[] G115 = new String[]{"G115", "This group id is the organization id."};
   public static final String[] G116 = new String[]{"G116", "The default group cannot be deleted or renamed."};
   public static final String[] G117 = new String[]{"G117", "Cannot delete this group."};
   public static final String[] G118 = new String[]{"G118", "Cannot delete the group as it contains the content being used in a playlist or schedule."};
   public static final String[] G119 = new String[]{"G119", "Enter the organization name."};
   public static final String[] G120 = new String[]{"G120", "The group name must be 'default'."};
   public static final String[] G121 = new String[]{"G121", "The user's organization and new organization name must be same."};
   public static final String[] G122 = new String[]{"G122", "The same organization name already exists."};
   public static final String[] G123 = new String[]{"G123", "The group name's max length is 20."};
   public static final String[] G124 = new String[]{"G124", "The organization name's max length is 20."};
   public static final String[] C101 = new String[]{"C101", "This content id does not exist."};
   public static final String[] C102 = new String[]{"C102", "This contentType is wrong."};
   public static final String[] C103 = new String[]{"C103", "Fail - delete Content"};
   public static final String[] C104 = new String[]{"C104", "It is wrong locale value. (de_DE, en_US, es_ES, fr_FR, it_IT, ja_JP, ko_KR, pt_PT, ru_RU, sv_SE, tr_TR, zh_TW)"};
   public static final String[] C105 = new String[]{"C105", "Fail - Modify Content"};
   public static final String[] C106 = new String[]{"C106", "ContentId Already exist."};
   public static final String[] C107 = new String[]{"C107", "Fail - Insert Template Info to DB."};
   public static final String[] C108 = new String[]{"C108", "Device have no Content download progress."};
   public static final String[] C109 = new String[]{"C109", "Expired content cannot be added."};
   public static final String[] P101 = new String[]{"P101", "The playlist id does not exist."};
   public static final String[] P102 = new String[]{"P102", "Fail - add Playlist"};
   public static final String[] P103 = new String[]{"P103", "Fail - delete Playlist"};
   public static final String[] P104 = new String[]{"P104", "The content can use only IMAGE, MOVIE, OFFICE, FLASH Type."};
   public static final String[] P105 = new String[]{"P105", "This effect does not exist."};
   public static final String[] P106 = new String[]{"P106", "Enter the effect duration."};
   public static final String[] P107 = new String[]{"P107", "This effect direction does not exist."};
   public static final String[] P108 = new String[]{"P108", "You can input only effect_in_name value among effect values in this Lite Version."};
   public static final String[] P109 = new String[]{"P109", "You can setup content effect only IMAGE type content in this Lite Version."};
   public static final String[] P110 = new String[]{"P110", "The content can use only IMAGE, MOVIE, LFD, OFFICE, FLASH, PDF, FTP, CIFS Type in this playlist of iPlayer type."};
   public static final String[] P112 = new String[]{"P112", "The content can use only IMAGE, MOVIE, LFD, OFFICE, FLASH, PDF Type in this playlist of SPlayer type."};
   public static final String[] P111 = new String[]{"P111", "You cannot delete the active playlist version"};
   public static final String[] P113 = new String[]{"P113", "Service not authorized(Playlist Log Authority)"};
   public static final String[] P114 = new String[]{"P112", "The content can use only IMAGE, MOVIE Type in this VideoWall playlist type."};
   public static final String[] P115 = new String[]{"P115", "The playlist version id does not exist."};
   public static final String[] P116 = new String[]{"P116", "The content is invalid"};
   public static final String[] U001 = new String[]{"U001", "This user id does not exist."};
   public static final String[] U002 = new String[]{"U002", "Error - delete MappingInfo by UserID"};
   public static final String[] U003 = new String[]{"U003", "Error - Approve User by UserID"};
   public static final String[] U004 = new String[]{"U004", "Fail - add User"};
   public static final String[] U005 = new String[]{"U005", "Fail - delete User"};
   public static final String[] U101 = new String[]{"U101", "The role id does not exist."};
   public static final String[] U102 = new String[]{"U102", "You can use this method only ALL scope."};
   public static final String[] U103 = new String[]{"U103", "You can use this method only GROUP scope."};
   public static final String[] U104 = new String[]{"U104", "The role name does not exist."};
   public static final String[] U105 = new String[]{"U105", "changeRoleScope didn't match - parameter roleScope"};
   public static final String[] U106 = new String[]{"U106", "before userRoleScope didn't match - parameter roleScope"};
   public static final String[] U107 = new String[]{"U107", "The root_group_id is wrong."};
   public static final String[] U108 = new String[]{"U108", "The user's role must be Administrator."};
   public static final String[] U109 = new String[]{"U109", "The role name does exist."};
   public static final String[] U110 = new String[]{"U110", "Service not authorized."};
   public static final String[] U111 = new String[]{"U111", "You can use only isAll false type becuase of your Ability."};
   public static final String[] U112 = new String[]{"U112", "You can't see other organization's group user."};
   public static final String[] U113 = new String[]{"U113", "Unable to delete the role as it is being used by a user(s)."};
   public static final String[] U114 = new String[]{"U114", "You cannot add any organization in Lite Version."};
   public static final String[] U115 = new String[]{"U115", "You cannot delete any organization in Lite Version."};
   public static final String[] U116 = new String[]{"U116", "You cannot modify the default role."};
   public static final String[] U117 = new String[]{"U117", "You cannot delete the default role."};
   public static final String[] U118 = new String[]{"U118", "This abilityId does not exist."};
   public static final String[] U119 = new String[]{"U119", "You can make only All manager in ROOT organization."};
   public static final String[] U120 = new String[]{"U120", "You can make only one Administrator role user in each organization."};
   public static final String[] U121 = new String[]{"U121", "You cannot modify role_name using this API. please input this user_id's role_name."};
   public static final String[] U122 = new String[]{"U122", "You cannot modify organization using this API. please input this user_id's organization."};
   public static final String[] U123 = new String[]{"U123", "You can remove deleted content only"};
   public static final String[] U124 = new String[]{"U124", "You can restore deleted content only"};
   public static final String[] U125 = new String[]{"U125", "You can remove deleted playlist only"};
   public static final String[] U126 = new String[]{"U126", "You can restore deleted playlist only"};
   public static final String[] U127 = new String[]{"U127", "You cannot control content or playlist of others because you don't have Manage ability"};
   public static final String[] U128 = new String[]{"U128", "You cannot add only LDAP user. This user is not LDAP user."};
   public static final String[] U129 = new String[]{"U129", "addProgramGroup fail while addUserForMega"};
   public static final String[] U130 = new String[]{"U130", "addMessageGroup fail while addUserForMega"};
   public static final String[] U131 = new String[]{"U131", "addDeviceGroup fail while addUserForMega"};
   public static final String[] U132 = new String[]{"U132", "add default Content Group fail while addUserForMega"};
   public static final String[] U133 = new String[]{"U133", "add default Playlist Group fail while addUserForMega"};
   public static final String[] N001 = new String[]{"N001", "The notice id does not exist."};
   public static final String[] N002 = new String[]{"N002", "start_date and end_date must have date value when has_limited value is true."};
   public static final String[] N003 = new String[]{"N003", "end_date must be larger then start_date"};
   public static final String[] N004 = new String[]{"N004", "The token ID and user ID of target notice is not same."};
   public static final String[] S001 = new String[]{"S001", "Failed to apply : Unknown error"};
   public static final String[] S002 = new String[]{"S002", "Failed to apply : Operation error"};
   public static final String[] S003 = new String[]{"S003", "Failed to apply : One or more elements are missed or invalid. Check the condition."};
   public static final String[] S004 = new String[]{"S004", "Failed to apply : A element with the specified name already exists. Change the value."};
   public static final String[] S005 = new String[]{"S005", "Failed to apply : No result exists"};
   public static final String[] S006 = new String[]{"S006", "Failed to apply : No permission. Check your authority"};
   public static final String[] S007 = new String[]{"S007", "Default content only support MOVIE or FLASH"};
   public static final String[] S008 = new String[]{"S008", "You must enter content ID"};
   public static final String[] S009 = new String[]{"S009", "The program is not exist"};
   public static final String[] S010 = new String[]{"S010", "You cannot delete a frame which frame_index of frame is zero"};
   public static final String[] S011 = new String[]{"S011", "Service not authorized(Schedule Log Authority)"};
   public static final String[] S012 = new String[]{"S012", "Old Type schedule does not support."};
   public static final String[] S013 = new String[]{"S013", "Range of channel NO must be from 1 to 999"};
   public static final String[] S014 = new String[]{"S014", "Already exist channel"};
   public static final String[] S015 = new String[]{"S015", "Program Group Id does not exist."};
   public static final String[] S101 = new String[]{"S101", "Weekday values are not exist"};
   public static final String[] S201 = new String[]{"S201", "Error while program is generated"};
   public static final String[] S202 = new String[]{"S202", "Error while channel is generated. channel_no :"};
   public static final String[] S203 = new String[]{"S203", "Error while frame is generated. frame_index :"};
   public static final String[] S204 = new String[]{"S204", "Error while content schedule is generated"};
   public static final String[] S205 = new String[]{"S205", "Fail to deploy schedule to device. program_id :"};
   public static final String[] S206 = new String[]{"S206", "Fail to version up program. program_id :"};
   public static final String[] M001 = new String[]{"M001", "Failed to apply : Unknown error"};
   public static final String[] M002 = new String[]{"M002", "Failed to apply : Operation error"};
   public static final String[] M003 = new String[]{"M003", "Failed to apply : One or more elements are missed or invalid. Check the condition."};
   public static final String[] M004 = new String[]{"M004", "Failed to apply : A element with the specified name already exists. Change the value."};
   public static final String[] M005 = new String[]{"M005", "Failed to apply : No result exists"};
   public static final String[] M006 = new String[]{"M006", "Failed to apply : No permission. Check your authority"};
   public static final String[] M007 = new String[]{"M007", "Failed to apply : The message and the device group organization are different."};
   public static final String[] M008 = new String[]{"M008", "This contrect id does not exist."};
   public static final String[] CL001 = new String[]{"CL001", "Not found mandatory parameter."};
   public static final String[] CL002 = new String[]{"CL002", "Open API Internal Exception."};
   public static final String[] CL003 = new String[]{"CL003", "Bootstrapping internal error."};
   public static final String[] CL004 = new String[]{"CL004", "Not found client information. Request bootstrapping first."};
   public static final String[] CL005 = new String[]{"CL005", "Not approvaled."};
   public static final String[] CL006 = new String[]{"CL006", "Ftp user not found."};
   public static final String[] RM001 = new String[]{"RM001", "RM Server is not registered."};
   public static final String[] RM002 = new String[]{"RM002", "fail connection to rm server"};
   public static final String[] RM003 = new String[]{"RM003", "Device is not connected"};
   public static final String[] RM004 = new String[]{"RM004", "RM Server has been already run"};
   public static final String[] RM005 = new String[]{"RM005", "Do not support paramter"};
   public static final String[] RM006 = new String[]{"RM006", "RM Server return error code :"};
   public static final String[] ST001 = new String[]{"ST001", "Do not find tag id"};
   public static final String[] ST002 = new String[]{"ST002", "Please check condition"};
   public static final String[] ST003 = new String[]{"ST003", "Failed to delete tag condition"};
   public static final String[] ST004 = new String[]{"ST004", "Failed to delete tag"};
   public static final String[] RM999 = new String[]{"RM999", "Start RM Server error."};
   public static final String[] X001 = new String[]{"X001", "You are not allowed to aceess here."};
   public static final String[] SH001 = new String[]{"SH001", "Shop does not exist"};

   public OpenApiExceptionCode() {
      super();
   }
}
