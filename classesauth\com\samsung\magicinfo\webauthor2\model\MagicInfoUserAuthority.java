package com.samsung.magicinfo.webauthor2.model;

public enum MagicInfoUserAuthority {
  CONTENT_READ_AUTHORITY("Content Read Authority"),
  CONTENT_WRITE_AUTHORITY("Content Write Authority"),
  PLAYLIST_READ_AUTHORITY("Playlist Read Authority"),
  PLAYLIST_WRITE_AUTHORITY("Playlist Write Authority"),
  CONTENT_LOCK_AUTHORITY("Content Lock Authority"),
  CONTENT_UPLOAD_AUTHORITY("Content Upload Authority"),
  CONTENT_ADD_ELEMENT_AUTHORITY("Content Add Element Authority");
  
  private final String fieldDescription;
  
  MagicInfoUserAuthority(String value) {
    this.fieldDescription = value;
  }
  
  public static MagicInfoUserAuthority getAuthorityFromString(String authority) {
    for (MagicInfoUserAuthority authorities : values()) {
      if (authority.equalsIgnoreCase(authorities.toString()))
        return authorities; 
    } 
    throw new IllegalArgumentException();
  }
  
  public String toString() {
    return this.fieldDescription;
  }
}
