package com.samsung.magicinfo.framework.setup.manager;

import com.ice.jni.registry.RegStringValue;
import com.ice.jni.registry.Registry;
import com.ice.jni.registry.RegistryKey;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.ExternalSystemUtils;
import com.samsung.common.utils.HWUniqueKey;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManager;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManagerImpl;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.setup.dao.SlmLicenseDao;
import com.samsung.magicinfo.framework.setup.entity.CompanyInfoEntity;
import com.samsung.magicinfo.framework.setup.entity.ServerManagementEntity;
import com.samsung.magicinfo.framework.setup.entity.SlmActivationKeyInfo;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseEntity;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseHistoryEntity;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseOrgEntity;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseStatusEntity;
import com.samsung.magicinfo.openapi.custom.openEntity.etc.MobileLicenseEntity;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.interfaces.WSCall;
import com.samsung.magicinfo.protocol.security.SlmLicenseHWCheck;
import com.sec.gsbn.lms.ag.activation.attribute.Product;
import com.sec.gsbn.lms.ag.common.site.SiteInformation;
import edu.emory.mathcs.backport.java.util.Arrays;
import java.io.UnsupportedEncodingException;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.security.GeneralSecurityException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.context.support.ResourceBundleMessageSource;

public class SlmLicenseManagerImpl implements SlmLicenseManager {
   private static Logger logger = LoggingManagerV2.getLogger(SlmLicenseManagerImpl.class);
   public static String PRODUCT_NAME_LITE = "Lite Player";
   public static String PRODUCT_NAME_RMS = "RM Player";
   public static String PRODUCT_NAME_NEW_UNIVERSAL = "Unified Player";
   private static String iv;
   private static Key keySpec;
   private static SlmLicenseManagerImpl slmLicenseManagerImpl;
   private static ResourceBundleMessageSource rms;
   private static HashMap supportedProductCodeMap;
   private static HashMap supportedDeviceTypeMap;
   private static DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
   SlmLicenseDao licenseDao = null;

   private SlmLicenseManagerImpl() {
      super();
      supportedProductCodeMap = new HashMap();
      String[] PRODUCT_CODE_UNIVERSAL = new String[]{"SPLAYER", "SIGNAGE", "LEDBOX", "iPLAYER", "WPLAYER"};
      supportedProductCodeMap.put("01014A", new ArrayList(Arrays.asList(PRODUCT_CODE_UNIVERSAL)));
      String[] PRODUCT_CODE_NEW_UNIVERSAL = new String[]{"SPLAYER", "SIGNAGE", "LEDBOX", "iPLAYER", "WPLAYER"};
      supportedProductCodeMap.put("01015A", new ArrayList(Arrays.asList(PRODUCT_CODE_NEW_UNIVERSAL)));
      String[] PRODUCT_CODE_PREMIUM_S = new String[]{"SPLAYER", "SIGNAGE", "LEDBOX", "iPLAYER", "WPLAYER"};
      supportedProductCodeMap.put("010121", new ArrayList(Arrays.asList(PRODUCT_CODE_PREMIUM_S)));
      String[] PRODUCT_CODE_PREMIUM_I = new String[]{"SPLAYER", "SIGNAGE", "LEDBOX", "iPLAYER", "WPLAYER"};
      supportedProductCodeMap.put("010120", new ArrayList(Arrays.asList(PRODUCT_CODE_PREMIUM_I)));
      String[] PRODUCT_CODE_SIGNAGE = new String[]{"SPLAYER", "SIGNAGE", "LEDBOX", "iPLAYER", "WPLAYER"};
      supportedProductCodeMap.put("010V31", new ArrayList(Arrays.asList(PRODUCT_CODE_SIGNAGE)));
      String[] PRODUCT_CODE_E2E = new String[]{"SPLAYER", "SIGNAGE", "LEDBOX", "iPLAYER"};
      supportedProductCodeMap.put("0101CS", new ArrayList(Arrays.asList(PRODUCT_CODE_E2E)));
      String[] PRODUCT_CODE_ANALYTICS_STORE = new String[0];
      supportedProductCodeMap.put("010D0E", new ArrayList(Arrays.asList(PRODUCT_CODE_ANALYTICS_STORE)));
      String[] PRODUCT_CODE_ANALYTICS_DISPLAY = new String[0];
      supportedProductCodeMap.put("010D0F", new ArrayList(Arrays.asList(PRODUCT_CODE_ANALYTICS_DISPLAY)));
      String[] PRODUCT_CODE_RMS = new String[]{"RIPLAYER", "RSPLAYER", "FLIP", "RLEDBOX", "RSIGNAGE", "RKIOSK"};
      supportedProductCodeMap.put("01064A", new ArrayList(Arrays.asList(PRODUCT_CODE_RMS)));
      String[] PRODUCT_CODE_LITE = new String[]{"LPLAYER"};
      supportedProductCodeMap.put("010311", new ArrayList(Arrays.asList(PRODUCT_CODE_LITE)));
      String[] PRODUCT_CODE_ANDROID = new String[]{"APLAYER"};
      supportedProductCodeMap.put("01011N", new ArrayList(Arrays.asList(PRODUCT_CODE_ANDROID)));
      supportedDeviceTypeMap = new HashMap();
      String[] TYPE_SOC = new String[]{"01014A", "01015A", "010121", "010V31", "010120"};
      supportedDeviceTypeMap.put("SPLAYER", new ArrayList(Arrays.asList(TYPE_SOC)));
      String[] TYPE_PREMIUM = new String[]{"01014A", "01015A", "010121", "010V31", "010120"};
      supportedDeviceTypeMap.put("iPLAYER", new ArrayList(Arrays.asList(TYPE_PREMIUM)));
      String[] TYPE_SIGNAGE = new String[]{"01014A", "01015A", "010121", "010V31", "010120"};
      supportedDeviceTypeMap.put("SIGNAGE", new ArrayList(Arrays.asList(TYPE_SIGNAGE)));
      String[] TYPE_LEDBOX = new String[]{"01014A", "01015A", "010121", "010V31", "010120"};
      supportedDeviceTypeMap.put("LEDBOX", new ArrayList(Arrays.asList(TYPE_LEDBOX)));
      String[] TYPE_FLIP = new String[]{"01064A"};
      supportedDeviceTypeMap.put("FLIP", new ArrayList(Arrays.asList(TYPE_FLIP)));
      String[] TYPE_RMS_I = new String[]{"01064A"};
      supportedDeviceTypeMap.put("RIPLAYER", new ArrayList(Arrays.asList(TYPE_RMS_I)));
      String[] TYPE_RMS_S = new String[]{"01064A"};
      supportedDeviceTypeMap.put("RSPLAYER", new ArrayList(Arrays.asList(TYPE_RMS_S)));
      String[] TYPE_RMS_LEDBOX = new String[]{"01064A"};
      supportedDeviceTypeMap.put("RLEDBOX", new ArrayList(Arrays.asList(TYPE_RMS_LEDBOX)));
      String[] TYPE_RMS_SIGNAGE = new String[]{"01064A"};
      supportedDeviceTypeMap.put("RSIGNAGE", new ArrayList(Arrays.asList(TYPE_RMS_SIGNAGE)));
      String[] TYPE_RMS_KIOSK = new String[]{"01064A"};
      supportedDeviceTypeMap.put("RKIOSK", new ArrayList(Arrays.asList(TYPE_RMS_KIOSK)));
      String[] TYPE_LITE = new String[]{"010311"};
      supportedDeviceTypeMap.put("LPLAYER", new ArrayList(Arrays.asList(TYPE_LITE)));
      String[] TYPE_APLAYER = new String[]{"01011N"};
      supportedDeviceTypeMap.put("APLAYER", new ArrayList(Arrays.asList(TYPE_APLAYER)));
      String[] TYPE_WPLAYER = new String[]{"01014A", "01015A", "010121", "010V31", "010120"};
      supportedDeviceTypeMap.put("WPLAYER", new ArrayList(Arrays.asList(TYPE_WPLAYER)));
      this.licenseDao = new SlmLicenseDao();
   }

   public void initSlmLicenseOrgInfo() {
      try {
         this.licenseDao.updateDeviceTypeListByProductCode("01015A", this.getDeviceTypeList((ArrayList)supportedProductCodeMap.get("01015A")));
         this.licenseDao.updateDeviceTypeListByProductCode("01064A", this.getDeviceTypeList((ArrayList)supportedProductCodeMap.get("01064A")));
         this.licenseDao.addDeviceTypeListByProductCode("01015A", PRODUCT_NAME_NEW_UNIVERSAL, this.getDeviceTypeList((ArrayList)supportedProductCodeMap.get("01015A")));
         this.licenseDao.addDeviceTypeListByProductCode("01064A", PRODUCT_NAME_RMS, this.getDeviceTypeList((ArrayList)supportedProductCodeMap.get("01064A")));
      } catch (SQLException var2) {
         var2.printStackTrace();
      }

   }

   private String getDeviceTypeList(ArrayList deviceTypeList) {
      StringBuilder result = new StringBuilder();

      String deviceType;
      for(Iterator var3 = deviceTypeList.iterator(); var3.hasNext(); result.append(deviceType)) {
         deviceType = (String)var3.next();
         if (result.length() > 0) {
            result.append(",");
         }
      }

      return result.toString();
   }

   public static SlmLicenseManagerImpl getInstance() {
      if (slmLicenseManagerImpl == null) {
         Class var0 = SlmLicenseManagerImpl.class;
         synchronized(SlmLicenseManagerImpl.class) {
            try {
               AES256Util("MagicInfoPremiumSlmLicense");
            } catch (UnsupportedEncodingException var3) {
               logger.error("", var3);
            }

            slmLicenseManagerImpl = new SlmLicenseManagerImpl();
            rms = new ResourceBundleMessageSource();
            rms.setBasename("resource/messages");
         }
      }

      return slmLicenseManagerImpl;
   }

   public int getLicenseCountByDeviceType(String deviceType) throws SQLException {
      List productCodeList = this.getProductCodeByDeviceType(deviceType);
      int count = 0;
      String productCode;
      if (productCodeList != null && productCodeList.size() > 0) {
         for(Iterator var4 = productCodeList.iterator(); var4.hasNext(); count += this.getCntLicenseMaxClient(productCode)) {
            productCode = (String)var4.next();
         }
      }

      return count;
   }

   public int getLicenseCountByProductCode(String productCode) throws SQLException {
      int count = 0;
      if (!productCode.equalsIgnoreCase("010D0F") && !productCode.equalsIgnoreCase("010D0E")) {
         List deviceTypeList = this.getDeviceTypeByProduct(productCode);
         if (deviceTypeList.size() > 0) {
            count += this.getLicenseCountByDeviceType((String)deviceTypeList.get(0));
         }
      } else {
         count += this.getCntLicenseMaxClient(productCode);
      }

      return count;
   }

   public int getRemainLicenseCountByDeviceType(String deviceType) throws SQLException {
      int allLicenseCount = this.getLicenseCountByDeviceType(deviceType);
      int allUsedLicenseCount = 9999;
      List productCodeList = this.getProductCodeByDeviceType(deviceType);
      if (productCodeList != null && productCodeList.size() > 0) {
         List findDeviceTypeList = new ArrayList();
         Iterator var6 = productCodeList.iterator();

         while(true) {
            List deviceTypeList;
            do {
               do {
                  if (!var6.hasNext()) {
                     allUsedLicenseCount = deviceMgr.getAllDeviceCountByDeviceTypeList(findDeviceTypeList);
                     return allLicenseCount - allUsedLicenseCount;
                  }

                  String productCode = (String)var6.next();
                  deviceTypeList = this.getDeviceTypeByProduct(productCode);
               } while(deviceTypeList == null);
            } while(deviceTypeList.size() <= 0);

            Iterator var9 = deviceTypeList.iterator();

            while(var9.hasNext()) {
               String device_type = (String)var9.next();
               if (!findDeviceTypeList.contains(device_type)) {
                  findDeviceTypeList.add(device_type);
               }
            }
         }
      } else {
         return allLicenseCount - allUsedLicenseCount;
      }
   }

   public int getRemainLicenseCountByProductCode(String productCode) throws SQLException {
      List dievceTypeList = this.getDeviceTypeByProduct(productCode);
      int allLicenseCount = this.getLicenseCountByProductCode(productCode);
      int allUsedLicenseCount = deviceMgr.getAllDeviceCountByDeviceTypeList(dievceTypeList);
      return allLicenseCount - allUsedLicenseCount;
   }

   public int getDeviceCountByProductCode(String productCode) throws SQLException {
      List dievceTypeList = this.getDeviceTypeByProduct(productCode);
      return deviceMgr.getAllDeviceCountByDeviceTypeList(dievceTypeList);
   }

   public List getDeviceListByProductCode(String productCode) throws SQLException {
      List findDeviceTypeList = this.getDeviceTypeByProduct(productCode);
      return deviceMgr.getApprovalDeviceIdByDeviceTypeListAsc(findDeviceTypeList);
   }

   public Set getSupportProductCode() {
      return supportedProductCodeMap.keySet();
   }

   private List getDeviceTypeByProduct(String productCode) {
      List findDeviceTypeList = new ArrayList();
      List dievceTypeList = (List)supportedProductCodeMap.get(productCode);
      if (dievceTypeList != null && dievceTypeList.size() > 0) {
         Iterator var4 = dievceTypeList.iterator();

         while(var4.hasNext()) {
            String device_type = (String)var4.next();
            if (!findDeviceTypeList.contains(device_type)) {
               findDeviceTypeList.add(device_type);
            }
         }
      }

      return findDeviceTypeList;
   }

   private List getProductCodeByDeviceType(String deviceType) {
      return (List)supportedDeviceTypeMap.get(deviceType);
   }

   public void insertSlmLicenseInfo(String activationKey) throws Exception {
      SlmLicenseEntity LicenseEntity = null;
      LicenseEntity = this.getSlmLicenseEntity(activationKey);
      this.licenseDao.addSlmLicenseInfo(LicenseEntity);
      if (CommonConfig.get("e2e.enable") == null || CommonConfig.get("e2e.enable").equalsIgnoreCase("false")) {
         this.chkSlmLicenseStatus(LicenseEntity.getProduct_code());
      }

   }

   public boolean insertSlmLicenseInfoForE2E(String activationKey, String deviceId, String issued_no) throws Exception {
      SlmLicenseEntity LicenseEntity = null;
      LicenseEntity = this.getSlmLicenseEntity(activationKey);
      LicenseEntity.setIssued_no(issued_no);
      return this.licenseDao.addSlmLicenseInfoForE2E(LicenseEntity, deviceId);
   }

   public boolean updateSlmLicenseInfo(String activationKey) throws Exception {
      SlmLicenseEntity LicenseEntity = null;
      LicenseEntity = this.getSlmLicenseEntity(activationKey);
      this.licenseDao.updateSlmLicenseInfo(LicenseEntity);
      this.chkSlmLicenseStatus(LicenseEntity.getProduct_code());
      return true;
   }

   public boolean updateSlmLicenseInfoForE2E(String activationKey) throws Exception {
      SlmLicenseEntity LicenseEntity = null;
      LicenseEntity = this.getSlmLicenseEntity(activationKey);
      this.licenseDao.updateSlmLicenseInfoForE2E(LicenseEntity);
      return true;
   }

   public boolean extendSlmLicenseInfoForE2E(String activationKey) throws Exception {
      SlmLicenseEntity LicenseEntity = null;
      LicenseEntity = this.getSlmLicenseEntity(activationKey);
      this.licenseDao.extendSlmLicenseInfoForE2E(LicenseEntity);
      return true;
   }

   public void chkSlmLicenseStatus(String productCode) throws Exception {
      if (!this.licenseChk()) {
         List invaildList = this.getInvaildLicenseList();
         if (invaildList != null && invaildList.size() > 0) {
            Iterator var3 = invaildList.iterator();

            while(var3.hasNext()) {
               Map licenses = (Map)var3.next();
               String getProductCode = (String)licenses.get("productCode");
               SlmLicenseStatusEntity license = this.getLicenseStaus(getProductCode);
               if (license == null) {
                  this.addLicenseStatus(productCode);
               }
            }
         }
      }

      SlmLicenseStatusEntity licenseStatus = this.getLicenseStaus(productCode);
      if (licenseStatus != null && this.getRemainLicenseCountByProductCode(productCode) >= 0) {
         logger.error("[MagicInfo_SlmLicenseStatus] delete slm status product_code : " + productCode);
         this.delLicenseStatus(productCode);
      }

   }

   public String getLicenseKeyfromActivationKey(String activationKey) {
      String licenseKey = null;
      SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
      Object[] rtn = slmMgr.sdkGetActivationKeyParseInfo(activationKey);
      if ((Integer)rtn[0] == 0) {
         SlmActivationKeyInfo actKeyInfo = (SlmActivationKeyInfo)rtn[1];
         licenseKey = actKeyInfo.getLicense_key();
      } else {
         licenseKey = String.valueOf(rtn[0]);
         logger.error("getActivationKeyInfo parse failed. error=" + (Integer)rtn[0]);
      }

      return licenseKey;
   }

   public SlmLicenseEntity getSlmLicenseEntity(String activationKey) throws Exception {
      if (activationKey != null && !activationKey.equals("")) {
         SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
         Object[] rtn = slmMgr.sdkGetActivationKeyParseInfo(activationKey);
         if ((Integer)rtn[0] != 0) {
            logger.error("Can't convert slmLicenseEntity. GetActivationKeyParseInfo is fail. eCode=" + (Integer)rtn[0]);
            return null;
         } else {
            SlmActivationKeyInfo slmActKeyInfo = (SlmActivationKeyInfo)rtn[1];
            SlmLicenseEntity licenseEntity = new SlmLicenseEntity();
            licenseEntity.setActivation_key(slmActKeyInfo.getActivation_key());
            licenseEntity.setLicense_key(slmActKeyInfo.getLicense_key());
            ArrayList product = slmActKeyInfo.getProduct_list();
            if (product != null && product.size() > 0) {
               licenseEntity.setProduct_code(((Product)product.get(0)).getProductCode());
               licenseEntity.setProduct_name(((Product)product.get(0)).getProductName());
               licenseEntity.setLicense_type(((Product)product.get(0)).getLicenseType());
               licenseEntity.setMax_clients(((Product)product.get(0)).getMaxClients() == null ? 0L : Long.parseLong(((Product)product.get(0)).getMaxClients()));
               licenseEntity.setStart_date(DateUtils.string2Timestamp(((Product)product.get(0)).getStartDate(), "yyyyMMdd"));
               licenseEntity.setEnd_date(DateUtils.string2Timestamp(((Product)product.get(0)).getEndDate(), "yyyyMMdd"));
               String maEndDttm = ((Product)product.get(0)).getAttribute("maEndDttm");

               try {
                  if (maEndDttm != null) {
                     licenseEntity.setMaint_end_date(DateUtils.string2Timestamp(maEndDttm, "yyyyMMddhhmmss"));
                  }
               } catch (Exception var9) {
                  logger.error("", var9);
               }

               return licenseEntity;
            } else {
               logger.error("Can't convert slmLicenseEntity. Invalid product info");
               return null;
            }
         }
      } else {
         logger.error("Can't convert slmLicenseEntity.[Invalid slmActKeyInfo]");
         return null;
      }
   }

   public String getHWUniqueKeyFromActivationKey(String activationKey) throws Exception {
      if (activationKey != null && !activationKey.equals("")) {
         SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
         Object[] rtn = slmMgr.sdkGetActivationKeyParseInfo(activationKey);
         if ((Integer)rtn[0] != 0) {
            logger.error("Can't convert slmLicenseEntity. GetActivationKeyParseInfo is fail. eCode=" + (Integer)rtn[0]);
            return null;
         } else {
            SlmActivationKeyInfo slmActKeyInfo = (SlmActivationKeyInfo)rtn[1];
            return slmActKeyInfo.getHwUnique_key();
         }
      } else {
         logger.error("Can't convert slmLicenseEntity.[Invalid slmActKeyInfo]");
         return null;
      }
   }

   public int licenseKeyVerify(String licenseKey) throws ConfigException, SQLException {
      SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
      int rtn = slmMgr.sdkLicenseKeyVerify(licenseKey);
      if (rtn != 0) {
         this.setSlmLicenseHistory("01", licenseKey, "licenseKey verify fail! code : " + rtn, true);
      }

      if (this.licenseKeyDuplicate(licenseKey) != 0) {
         rtn = 900;
         this.setSlmLicenseHistory("01", licenseKey, "licenseKey duplicate! code : " + rtn, true);
      }

      return rtn;
   }

   public int licenseKeyDuplicate(String licenseKey) {
      short rtn = 0;

      try {
         SlmLicenseEntity slmEntity = this.licenseDao.getSlmLicense(licenseKey);
         if (slmEntity != null) {
            rtn = 900;
         }
      } catch (Exception var4) {
         logger.error("", var4);
      }

      return rtn;
   }

   public int licenseKeyDuplicateForE2E(String licenseKey) {
      short rtn = 0;

      try {
         SlmLicenseEntity slmEntity = this.licenseDao.getSlmLicenseForE2E(licenseKey);
         if (slmEntity != null) {
            rtn = 900;
         }
      } catch (Exception var4) {
         logger.error("", var4);
      }

      return rtn;
   }

   public int hwUniqueKeyVerify(String activationKey) {
      int rtn = false;
      String server_hwUniqueKey = null;
      String hwUniqueKeyFromActivationKey = null;

      try {
         server_hwUniqueKey = this.getHWUniqueKey();
      } catch (SQLException var7) {
         rtn = true;
         logger.error("", var7);
      }

      try {
         hwUniqueKeyFromActivationKey = this.getHWUniqueKeyFromActivationKey(activationKey);
      } catch (Exception var6) {
         rtn = true;
         logger.error("", var6);
      }

      short rtn;
      if (hwUniqueKeyFromActivationKey == null) {
         rtn = 600;
      } else if (server_hwUniqueKey.equals(hwUniqueKeyFromActivationKey)) {
         rtn = 0;
      } else {
         rtn = 700;
      }

      return rtn;
   }

   public int activationRequest(String licenseKey, String hwUniqueKey, String companyName, String division, String address, String phone, String email) {
      SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
      Object[] rtn = slmMgr.sdkRequestGetActivation(licenseKey, hwUniqueKey);
      SlmLicenseEntity licenseEntity;
      SlmLicenseEntity freeOfCharged;
      if (rtn != null && rtn.length == 2 && (Integer)rtn[0] == 0) {
         if ((Integer)rtn[0] == 0) {
            try {
               this.setSlmLicenseHistory("01", licenseKey, "Activation Re-Request success", true);
               licenseEntity = null;

               try {
                  licenseEntity = this.getSlmLicenseEntity((String)rtn[1]);
               } catch (Exception var15) {
                  logger.error("Failed to get license entity.", var15);
               }

               if (!this.checkDuplicationLicenseCharged((String)rtn[1])) {
                  rtn[0] = 900;
               } else if (this.isExpired(licenseEntity)) {
                  rtn[0] = 901;
                  logger.error("It's expired license : " + licenseKey + ", " + licenseEntity.getEnd_date());
               } else {
                  SlmLicenseEntity freeOfCharged = this.findFreeOfChargedKeyFromLicenseList(licenseEntity.getProduct_code());
                  freeOfCharged = this.findTrialKeyFromLicenseList(licenseEntity.getProduct_code());
                  this.insertSlmLicenseInfo((String)rtn[1]);
                  this.deleteLicenseKey(freeOfCharged);
                  this.deleteLicenseKey(freeOfCharged);
               }
            } catch (Exception var16) {
               rtn[0] = 500;
               logger.error("", var16);
            }
         }
      } else if ((Integer)rtn[0] == 19002) {
         rtn = slmMgr.sdkNewActivationRequest(licenseKey, hwUniqueKey, slmMgr.createSiteInfomation(companyName, division, address, phone, email));
         if ((Integer)rtn[0] == 0) {
            try {
               licenseEntity = null;

               try {
                  licenseEntity = this.getSlmLicenseEntity((String)rtn[1]);
               } catch (Exception var14) {
                  logger.error("Failed to get license entity.", var14);
               }

               boolean expired = this.isExpired(licenseEntity);
               if (this.checkDuplicationLicenseCharged((String)rtn[1]) && !expired) {
                  freeOfCharged = this.findFreeOfChargedKeyFromLicenseList(licenseEntity.getProduct_code());
                  SlmLicenseEntity trial = this.findTrialKeyFromLicenseList(licenseEntity.getProduct_code());
                  this.insertSlmLicenseInfo((String)rtn[1]);
                  this.deleteLicenseKey(freeOfCharged);
                  this.deleteLicenseKey(trial);
               } else {
                  if (expired) {
                     rtn[0] = 901;
                     logger.error("It's expired license : " + licenseKey + ", " + licenseEntity.getEnd_date());
                  } else {
                     rtn[0] = 900;
                     logger.error("It's duplicated license : " + licenseKey);
                  }

                  int returnErrCode = slmMgr.sdkRequestDeActivationProcess((String)rtn[1]);
                  if (returnErrCode == 0) {
                     logger.info("Successfully de-activate license: " + licenseKey);
                  } else {
                     logger.error("Error: de-activate license failed | return code : " + returnErrCode);
                  }
               }
            } catch (Exception var17) {
               rtn[0] = 500;
               logger.error("", var17);
            }
         }
      } else {
         logger.error("license error from SLM Server!! Error Code : [" + rtn[0] + "]");
      }

      return (Integer)rtn[0];
   }

   private boolean isExpired(SlmLicenseEntity licenseEntity) {
      boolean ret = false;

      try {
         if (licenseEntity != null && licenseEntity.getEnd_date() != null) {
            if (licenseEntity.getEnd_date().before(new Date())) {
               ret = true;
            }
         } else {
            ret = false;
         }
      } catch (Exception var4) {
         ret = false;
      }

      return ret;
   }

   public boolean checkDuplicationLicenseCharged(String activation_key) throws Exception {
      boolean rtn = false;
      SlmLicenseEntity licenseEntity = this.getSlmLicenseEntity(activation_key);
      if (licenseEntity != null) {
         if (this.licenseDao.getLicenseFromProductAndType(licenseEntity.getProduct_code(), licenseEntity.getLicense_type()) > 0) {
            logger.info("license duplication!");
            rtn = false;
         } else {
            rtn = true;
         }
      } else {
         logger.error("SlmLicense Entity is null");
      }

      return rtn;
   }

   public String getActivationKey(String licenseKey) {
      try {
         return this.licenseDao.getActivationKey(licenseKey);
      } catch (SQLException var3) {
         logger.error("", var3);
         return null;
      }
   }

   public int changeActivationRequest(String activationKey) {
      SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
      logger.info("old licenseKey : " + this.getLicenseKeyfromActivationKey(activationKey));
      Object[] rtn = slmMgr.sdkChangeActivationRequest(activationKey);
      if ((Integer)rtn[0] == 0) {
         try {
            this.updateSlmLicenseInfo((String)rtn[1]);
         } catch (Exception var5) {
            rtn[0] = 500;
            logger.error("", var5);
         }

         logger.info("new licenseKey : " + this.getLicenseKeyfromActivationKey((String)rtn[1]));
      }

      return (Integer)rtn[0];
   }

   public int changeActivationActive(String oldActivationKey, String newActivationKey) {
      SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
      logger.info("old licenseKey : " + this.getLicenseKeyfromActivationKey(oldActivationKey));
      int rtn = false;
      String old_license = this.getLicenseKeyfromActivationKey(oldActivationKey);
      String new_license = this.getLicenseKeyfromActivationKey(newActivationKey);
      int rtn;
      if (!old_license.equalsIgnoreCase(new_license)) {
         rtn = 800;
      } else {
         rtn = slmMgr.sdkChangeActivationActive(oldActivationKey, newActivationKey);
         if (rtn == 0) {
            try {
               this.updateSlmLicenseInfo(newActivationKey);
            } catch (Exception var8) {
               rtn = 500;
               logger.error("", var8);
            }

            logger.info("new licenseKey : " + this.getLicenseKeyfromActivationKey(newActivationKey));
         }
      }

      return rtn;
   }

   public int activationActive(String licenseKey, String activationKey) {
      SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
      int rtn = slmMgr.sdkNewActivationActive(licenseKey, activationKey);
      if (rtn == 0) {
         SlmLicenseEntity licenseEntity = null;

         try {
            licenseEntity = this.getSlmLicenseEntity(activationKey);
         } catch (Exception var9) {
            logger.error("", var9);
         }

         try {
            if (!this.checkDuplicationLicenseCharged(activationKey)) {
               rtn = 900;
            } else if (this.isExpired(licenseEntity)) {
               rtn = 901;
               logger.error("It's expired license : " + licenseKey + ", " + licenseEntity.getEnd_date());
            } else {
               SlmLicenseEntity freeOfCharged = this.findFreeOfChargedKeyFromLicenseList(licenseEntity.getProduct_code());
               SlmLicenseEntity trial = this.findTrialKeyFromLicenseList(licenseEntity.getProduct_code());
               this.insertSlmLicenseInfo(activationKey);
               this.deleteLicenseKey(freeOfCharged);
               this.deleteLicenseKey(trial);
            }
         } catch (Exception var8) {
            rtn = 500;
            logger.error("", var8);
         }
      }

      return rtn;
   }

   public int activationActiveForE2E(String licenseKey, String activationKey, String deviceId, String issudNo) {
      SlmSdkManagerImpl var5 = SlmSdkManagerImpl.getInstance();

      try {
         boolean dbResult = false;
         dbResult = this.insertSlmLicenseInfoForE2E(activationKey, deviceId, issudNo);
         if (!dbResult) {
            throw new Exception();
         }
      } catch (Exception var7) {
         logger.error("", var7);
      }

      return 0;
   }

   public int reActivationActiveForE2E(String licenseKey, String activationKey, String deviceId, String issudNo) {
      SlmSdkManagerImpl var5 = SlmSdkManagerImpl.getInstance();

      try {
         boolean dbResult = false;
         dbResult = this.updateSlmLicenseInfoForE2E(activationKey);
         if (!dbResult) {
            throw new Exception();
         }
      } catch (Exception var7) {
         logger.error("", var7);
      }

      return 0;
   }

   public int deActivationRequest(String licenseKey) throws Exception {
      SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
      String activationKey = this.getActivationKey(licenseKey);
      int rtn = slmMgr.sdkRequestDeActivationProcess(activationKey);
      if (rtn == 0) {
         try {
            SlmLicenseEntity licenseEntity = this.getSlmLicenseEntity(activationKey);
            this.checkSlmLicenseDeleteDevice(licenseEntity);
         } catch (SQLException var6) {
            rtn = 500;
            logger.error("", var6);
         }
      }

      return rtn;
   }

   public String deActivationActive(String licenseKey) throws Exception {
      SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
      String activationKey = null;

      try {
         activationKey = this.licenseDao.getActivationKey(licenseKey);
      } catch (SQLException var8) {
         logger.error("", var8);
      }

      Object[] rtn = slmMgr.sdkGenerateDeactivationKey(activationKey);
      String deKey = null;
      if ((Integer)rtn[0] == 0) {
         try {
            SlmLicenseEntity licenseEntity = this.getSlmLicenseEntity(activationKey);
            this.checkSlmLicenseDeleteDevice(licenseEntity);
         } catch (SQLException var7) {
            deKey = String.valueOf(500);
            logger.error("", var7);
         }

         deKey = (String)rtn[1];
         this.setSlmLicenseHistory("03", licenseKey, deKey, true);
      }

      return deKey;
   }

   public String getHWUniqueKey() throws SQLException {
      String key = new String();

      try {
         int i;
         if (CommonConfig.get("multiple.server.enable") != null) {
            if (CommonConfig.get("multiple.server.enable").equalsIgnoreCase("true")) {
               key = this.licenseDao.getHwUniqueKey();
               if (key == null) {
                  HWUniqueKey hwKey = new HWUniqueKey();
                  key = HWUniqueKey.makeHWUniqueKey();
                  this.licenseDao.setHwUniqueKey(key);
               }
            } else {
               for(i = 0; i < SlmLicenseHWCheck.getInstance().size(); ++i) {
                  key = SlmLicenseHWCheck.getInstance().getKey(i);
               }
            }
         } else {
            for(i = 0; i < SlmLicenseHWCheck.getInstance().size(); ++i) {
               key = SlmLicenseHWCheck.getInstance().getKey(i);
            }
         }
      } catch (ConfigException var3) {
         logger.error("", var3);
      }

      if (key == null) {
         key = HWUniqueKey.makeHWUniqueKey();
         this.licenseDao.setHwUniqueKey(key);
      }

      return key;
   }

   public void printActivationKeyInfo(String activationKey) {
      SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
      Object[] rtn = slmMgr.sdkGetActivationKeyParseInfo(activationKey);
      if ((Integer)rtn[0] == 0) {
         SlmActivationKeyInfo actKeyInfo = (SlmActivationKeyInfo)rtn[1];
         System.out.println("ActKey                :" + actKeyInfo.getActivation_key());
         System.out.println("LicenseKey            :" + actKeyInfo.getLicense_key());
         System.out.println("HwUniqueKey           :" + actKeyInfo.getHwUnique_key());
         ArrayList product = actKeyInfo.getProduct_list();

         for(int i = 0; i < product.size(); ++i) {
            System.out.println("Product EndDate     :" + ((Product)product.get(i)).getEndDate());
            System.out.println("Product StartDate   :" + ((Product)product.get(i)).getStartDate());
            System.out.println("Product Code        :" + ((Product)product.get(i)).getProductCode());
            System.out.println("Product Name        :" + ((Product)product.get(i)).getProductName());
            System.out.println("Product LicenseType :" + ((Product)product.get(i)).getLicenseType());
            System.out.println("Product MaxClients  :" + ((Product)product.get(i)).getMaxClients());
            HashMap list = ((Product)product.get(i)).getAttributes();
            Iterator iter = list.keySet().iterator();

            while(iter.hasNext()) {
               String key = (String)iter.next();
               System.out.println("Product attribute " + key + ":" + (String)list.get(key));
            }
         }
      } else {
         logger.error("getActivationKeyInfo parse failed. error=" + (Integer)rtn[0]);
      }

   }

   public void SlmlicenseLoadingConfig() {
      try {
         boolean BOOL_REG_LIC_LFD = false;
         boolean BOOL_REG_LIC_LITE = false;
         boolean BOOL_REG_LIC_MOBILE = false;
         boolean BOOL_REG_LIC_SOC = false;
         boolean BOOL_REG_LIC_ANDROID = false;
         boolean BOOL_REG_LIC_SIGNAGE = false;
         boolean BOOL_REG_LIC_RMS = false;
         boolean BOOL_REG_LIC_DATALINK = true;
         boolean BOOL_REG_LIC_WPLAYER = false;
         Iterator var10 = supportedProductCodeMap.keySet().iterator();

         while(true) {
            String productCode;
            do {
               if (!var10.hasNext()) {
                  if (CommonConfig.get("e2e.enable") != null && CommonConfig.get("e2e.enable").equalsIgnoreCase("true")) {
                     List miList = null;

                     try {
                        miList = this.getAllSlmLicense();
                     } catch (Exception var17) {
                        logger.error("Log_License, license is invalid.");
                     }

                     if (miList != null && miList.size() != 0) {
                        BOOL_REG_LIC_LFD = true;
                        BOOL_REG_LIC_LITE = true;
                        BOOL_REG_LIC_MOBILE = true;
                        BOOL_REG_LIC_SOC = true;
                        BOOL_REG_LIC_ANDROID = true;
                        BOOL_REG_LIC_SIGNAGE = true;
                        BOOL_REG_LIC_RMS = true;
                        BOOL_REG_LIC_DATALINK = true;
                     } else {
                        BOOL_REG_LIC_LFD = false;
                        BOOL_REG_LIC_LITE = false;
                        BOOL_REG_LIC_MOBILE = false;
                        BOOL_REG_LIC_SOC = false;
                        BOOL_REG_LIC_ANDROID = false;
                        BOOL_REG_LIC_SIGNAGE = false;
                        BOOL_REG_LIC_RMS = false;
                        BOOL_REG_LIC_DATALINK = true;
                     }
                  }

                  if (this.hasMigrationLicense() >= 0L) {
                     BOOL_REG_LIC_LFD = true;
                     BOOL_REG_LIC_LITE = true;
                     BOOL_REG_LIC_MOBILE = true;
                     BOOL_REG_LIC_SOC = true;
                     BOOL_REG_LIC_ANDROID = true;
                     BOOL_REG_LIC_SIGNAGE = true;
                     BOOL_REG_LIC_RMS = true;
                     BOOL_REG_LIC_DATALINK = true;
                  }

                  CommonConfig.set("BOOL_REG_LIC_LFD", String.valueOf(BOOL_REG_LIC_LFD));
                  CommonConfig.set("BOOL_REG_LIC_VIDEOWALL", String.valueOf(false));
                  CommonConfig.set("BOOL_REG_LIC_LITE", String.valueOf(BOOL_REG_LIC_LITE));
                  CommonConfig.set("BOOL_REG_LIC_DATALINK", String.valueOf(BOOL_REG_LIC_DATALINK));
                  CommonConfig.set("BOOL_REG_LIC_MOBILE", String.valueOf(BOOL_REG_LIC_MOBILE));
                  CommonConfig.set("BOOL_REG_LIC_SOC", String.valueOf(BOOL_REG_LIC_SOC));
                  CommonConfig.set("BOOL_REG_LIC_ANDROID", String.valueOf(BOOL_REG_LIC_ANDROID));
                  CommonConfig.set("BOOL_REG_LIC_WPLAYER", String.valueOf(BOOL_REG_LIC_WPLAYER));
                  CommonConfig.set("BOOL_REG_LIC_SIGNAGE", String.valueOf(BOOL_REG_LIC_SIGNAGE));
                  CommonConfig.set("BOOL_REG_LIC_RMS", String.valueOf(BOOL_REG_LIC_RMS));
                  if (CommonConfig.get("menu.vw.enable") != null && CommonConfig.get("menu.vw.enable").equals("true") && (CommonConfig.get("BOOL_REG_LIC_LFD").equals("true") || CommonConfig.get("BOOL_REG_LIC_SOC").equals("true"))) {
                     CommonConfig.set("BOOL_REG_LIC_VIDEOWALL", String.valueOf(true));
                  }

                  if (CommonConfig.get("BOOL_REG_LIC_RMS").equals("true") && !CommonConfig.get("BOOL_REG_LIC_LFD").equals("true") && !CommonConfig.get("BOOL_REG_LIC_LITE").equals("true") && !CommonConfig.get("BOOL_REG_LIC_SOC").equals("true") && !CommonConfig.get("BOOL_REG_LIC_ANDROID").equals("true") && !CommonConfig.get("BOOL_REG_LIC_SIGNAGE").equals("true")) {
                     CommonConfig.set("RMS_MODE", String.valueOf(true));
                  } else {
                     CommonConfig.set("RMS_MODE", String.valueOf(false));
                  }

                  StrUtils.printCommonConfigLicenseInfo("LoadingLicenseConfig");
                  return;
               }

               productCode = (String)var10.next();
            } while(this.getLicenseCountByProductCode(productCode) <= 0);

            List deviceTypeList = (List)supportedProductCodeMap.get(productCode);
            Iterator var13 = supportedDeviceTypeMap.keySet().iterator();

            while(var13.hasNext()) {
               String deviceType = (String)var13.next();
               if (deviceTypeList.contains(deviceType)) {
                  byte var16 = -1;
                  switch(deviceType.hashCode()) {
                  case -**********:
                     if (deviceType.equals("LEDBOX")) {
                        var16 = 3;
                     }
                     break;
                  case -**********:
                     if (deviceType.equals("WPLAYER")) {
                        var16 = 4;
                     }
                     break;
                  case -**********:
                     if (deviceType.equals("SIGNAGE")) {
                        var16 = 2;
                     }
                     break;
                  case -**********:
                     if (deviceType.equals("SPLAYER")) {
                        var16 = 0;
                     }
                     break;
                  case -392637480:
                     if (deviceType.equals("RIPLAYER")) {
                        var16 = 10;
                     }
                     break;
                  case -312192656:
                     if (deviceType.equals("RSIGNAGE")) {
                        var16 = 13;
                     }
                     break;
                  case -107535262:
                     if (deviceType.equals("RSPLAYER")) {
                        var16 = 11;
                     }
                     break;
                  case -79259038:
                     if (deviceType.equals("APLAYER")) {
                        var16 = 5;
                     }
                     break;
                  case 81272:
                     if (deviceType.equals("RMS")) {
                        var16 = 8;
                     }
                     break;
                  case 2160749:
                     if (deviceType.equals("FLIP")) {
                        var16 = 9;
                     }
                     break;
                  case **********:
                     if (deviceType.equals("iPLAYER")) {
                        var16 = 1;
                     }
                     break;
                  case 1093346861:
                     if (deviceType.equals("LPLAYER")) {
                        var16 = 6;
                     }
                     break;
                  case 1980850542:
                     if (deviceType.equals("MPLAYER")) {
                        var16 = 7;
                     }
                     break;
                  case 2002487986:
                     if (deviceType.equals("RLEDBOX")) {
                        var16 = 12;
                     }
                  }

                  switch(var16) {
                  case 0:
                  case 1:
                  case 2:
                  case 3:
                     BOOL_REG_LIC_LFD = true;
                     BOOL_REG_LIC_SIGNAGE = true;
                     BOOL_REG_LIC_SOC = true;
                     break;
                  case 4:
                     BOOL_REG_LIC_WPLAYER = true;
                     break;
                  case 5:
                     BOOL_REG_LIC_ANDROID = true;
                     break;
                  case 6:
                     BOOL_REG_LIC_LITE = true;
                     break;
                  case 7:
                     BOOL_REG_LIC_MOBILE = true;
                     break;
                  case 8:
                  case 9:
                  case 10:
                  case 11:
                  case 12:
                  case 13:
                     BOOL_REG_LIC_RMS = true;
                  }
               }
            }
         }
      } catch (ConfigException var18) {
         logger.error(var18);
      } catch (Exception var19) {
         logger.error("", var19);
      }

   }

   public boolean checkFreeSlmLicense(String licenseKey) throws SQLException {
      boolean rtn = false;
      SlmLicenseHistoryEntity slmLicenseInfo = null;
      slmLicenseInfo = this.licenseDao.getSlmLicenseHistory(licenseKey);
      if (slmLicenseInfo == null) {
         rtn = false;
      } else {
         rtn = true;
      }

      return rtn;
   }

   public void checkDBSlmLicense() throws Exception {
      List miList;
      try {
         miList = this.getAllSlmLicense();
      } catch (Exception var5) {
         logger.error("Log_License, license is invalid.");
         return;
      }

      SlmLicenseEntity slmLicenseEntity = null;
      SlmLicenseEntity CheckSlmLicenseEntity = null;

      for(int i = 0; i < miList.size(); ++i) {
         slmLicenseEntity = (SlmLicenseEntity)miList.get(i);
         CheckSlmLicenseEntity = this.getSlmLicenseEntity(slmLicenseEntity.getActivation_key());
         if (CheckSlmLicenseEntity.getLicense_type() != "12" && this.hwUniqueKeyVerify(slmLicenseEntity.getActivation_key()) != 0) {
            logger.error("****************************************************************************************");
            logger.error("license : " + slmLicenseEntity.getLicense_key());
            logger.error("check HWunique key! invalid!");
            logger.error("****************************************************************************************");
         }

         if (!slmLicenseEntity.getLicense_key().equals(CheckSlmLicenseEntity.getLicense_key()) || !slmLicenseEntity.getProduct_code().equals(CheckSlmLicenseEntity.getProduct_code()) || !slmLicenseEntity.getProduct_name().equals(CheckSlmLicenseEntity.getProduct_name()) || !slmLicenseEntity.getLicense_type().equals(CheckSlmLicenseEntity.getLicense_type()) || !slmLicenseEntity.getMax_clients().equals(CheckSlmLicenseEntity.getMax_clients()) || !slmLicenseEntity.getStart_date().equals(CheckSlmLicenseEntity.getStart_date()) || !slmLicenseEntity.getEnd_date().equals(CheckSlmLicenseEntity.getEnd_date())) {
            logger.error("****************************************************************************************");
            logger.error("Log_License : License decoding Fail !!!!!!");
            logger.error("****************************************************************************************");
            this.setSlmLicenseHistory("02", "LICENSE", "Invalid Activation key!", true);
            if (!this.updateSlmLicenseInfo(CheckSlmLicenseEntity.getActivation_key())) {
               logger.error("Log_License : Update error");
            }
         }
      }

   }

   public void checkDBSlmLicenseForE2E() throws Exception {
      List miList;
      try {
         miList = this.getAllSlmLicenseForE2E();
      } catch (Exception var8) {
         logger.error("Log_License, license is invalid.");
         return;
      }

      SlmLicenseEntity slmLicenseEntity = null;
      SlmLicenseEntity CheckSlmLicenseEntity = null;

      int i;
      for(i = 0; i < miList.size(); ++i) {
         slmLicenseEntity = (SlmLicenseEntity)miList.get(i);
         CheckSlmLicenseEntity = this.getSlmLicenseEntity(slmLicenseEntity.getActivation_key());
         if (!slmLicenseEntity.getLicense_key().equals(CheckSlmLicenseEntity.getLicense_key()) || !slmLicenseEntity.getStart_date().equals(CheckSlmLicenseEntity.getStart_date()) || !slmLicenseEntity.getEnd_date().equals(CheckSlmLicenseEntity.getEnd_date())) {
            logger.error("****************************************************************************************");
            logger.error("Log_License : License decoding Fail !!!!!!");
            logger.error("****************************************************************************************");
            this.setSlmLicenseHistory("02", "LICENSE", "Invalid Activation key!", true);
            if (!this.updateSlmLicenseInfoForE2E(CheckSlmLicenseEntity.getActivation_key())) {
               logger.error("Log_License : Update error");
            }
         }
      }

      miList = null;

      try {
         miList = this.getAllSlmLicenseForE2E();
      } catch (Exception var7) {
         logger.error("Log_License, license is invalid.");
         return;
      }

      slmLicenseEntity = null;

      for(i = 0; i < miList.size(); ++i) {
         slmLicenseEntity = (SlmLicenseEntity)miList.get(i);
         boolean extendResult = false;
         if (this.chkLicenseExtension(slmLicenseEntity.getEnd_date())) {
            try {
               if (CommonConfig.get("e2e.license.system") != null && !CommonConfig.get("e2e.license.system").toUpperCase().equals(ExternalSystemUtils.SYSTEM_PBP)) {
                  extendResult = this.reActivationProcessForE2E_SLMDirect(slmLicenseEntity.getDevice_id());
               } else {
                  extendResult = this.reActivationProcessForE2E(slmLicenseEntity.getDevice_id());
               }
            } catch (Exception var9) {
               logger.error(var9.toString());
            }
         }

         if (extendResult) {
            slmLicenseEntity = this.licenseDao.getSlmLicenseForE2EByDeviceId(slmLicenseEntity.getDevice_id());
         }

         if (this.chkExpiredForE2E(slmLicenseEntity.getEnd_date())) {
            this.deleteProcessDeviceForE2E(slmLicenseEntity);
         }
      }

   }

   public List getAllSlmLicense() throws SQLException {
      return this.licenseDao.getAllSlmlLicense();
   }

   public List getAllSlmLicenseForE2E() throws SQLException {
      return this.licenseDao.getAllSlmlLicenseForE2E();
   }

   public void checkSlmLicenseExpired() throws SQLException {
      List miList;
      try {
         miList = this.getAllSlmLicense();
      } catch (Exception var4) {
         logger.error("Log_License, license is invalid.");
         return;
      }

      SlmLicenseEntity slmLicenseEntity = null;

      for(int i = 0; i < miList.size(); ++i) {
         slmLicenseEntity = (SlmLicenseEntity)miList.get(i);
         if (!this.chkExpired(slmLicenseEntity.getEnd_date())) {
            this.checkSlmLicenseDeleteDevice(slmLicenseEntity);
         }
      }

   }

   private boolean deleteMappingInfoOfSlmLicenseOrgIfNeed(SlmLicenseEntity slmLicenseEntity) throws SQLException {
      boolean delete = slmLicenseEntity.getLicense_type().equals("11");
      if (!delete) {
         delete = slmLicenseEntity.getLicense_type().equals("13") && slmLicenseEntity.getProduct_code().equals("010311");
      }

      if (delete) {
         this.deleteMappingInfoOfSlmLicenseOrg(slmLicenseEntity.getProduct_code());
      }

      return delete;
   }

   private int getNumAssignedLicenseToOrg(String productCode) throws SQLException {
      List slmLicenseOrgEntities = this.licenseDao.getLicenseInfoAssignedToOrganization();
      int totalNumOfAssignedLicense = 0;
      Iterator var4 = slmLicenseOrgEntities.iterator();

      while(var4.hasNext()) {
         SlmLicenseOrgEntity slmLicenseOrgEntity = (SlmLicenseOrgEntity)var4.next();
         long maxLicenseCount = slmLicenseOrgEntity.getMax_license_count();
         if (productCode.equals(slmLicenseOrgEntity.getProduct_code())) {
            totalNumOfAssignedLicense = (int)((long)totalNumOfAssignedLicense + (maxLicenseCount != -1L ? maxLicenseCount : 0L));
         }
      }

      return totalNumOfAssignedLicense;
   }

   public void checkSlmLicenseDeleteDevice(SlmLicenseEntity slmLicenseEntity) throws SQLException {
      if (this.licenseDao.deleteLicenseInfo(slmLicenseEntity.getLicense_key())) {
         try {
            if (this.hasMigrationLicense() >= 0L) {
               this.deleteMappingInfoOfSlmLicenseOrgIfNeed(slmLicenseEntity);
               logger.error("Migration License is activated. No devices has been deleted.");
               return;
            }
         } catch (Exception var17) {
            logger.error("", var17);
         }

         List deviceTypeList = this.getDeviceTypeByProduct(slmLicenseEntity.getProduct_code());
         if (deviceTypeList.isEmpty()) {
            logger.error("No device type exists. Do not delete devices.");
            return;
         }

         int slmLicenseTotalMaxClient = this.getLicenseCountByProductCode(slmLicenseEntity.getProduct_code());
         List deviceIdList = deviceMgr.getApprovalDeviceIdByDeviceTypeListAsc(deviceTypeList);
         List deviceIdListInOrgAssignedLicense = deviceMgr.getApprovalDeviceIdByDeviceTypeListInOrgAssignedLicenseAsc(deviceTypeList);
         if (!this.deleteMappingInfoOfSlmLicenseOrgIfNeed(slmLicenseEntity)) {
            deviceIdList.removeAll(deviceIdListInOrgAssignedLicense);
            slmLicenseTotalMaxClient -= this.getNumAssignedLicenseToOrg(slmLicenseEntity.getProduct_code());
         }

         if (deviceIdList == null || deviceIdList.isEmpty()) {
            logger.error("License is alive. do not delete device.");
            return;
         }

         if (deviceIdList.size() > slmLicenseTotalMaxClient) {
            logger.error("delete deivce. device List=" + deviceIdList.size() + ", del Start Idx=" + slmLicenseTotalMaxClient);
            if (slmLicenseEntity.getLicense_type().equals("13")) {
               try {
                  this.setSlmLicenseHistory("04", slmLicenseEntity.getLicense_key(), "Trial license key is expried!! delete device : " + (deviceIdList.size() - slmLicenseTotalMaxClient), true);
               } catch (ConfigException var16) {
                  logger.error("", var16);
               }
            } else {
               try {
                  this.setSlmLicenseHistory("04", slmLicenseEntity.getLicense_key(), "license key was returned!! delete device : " + (deviceIdList.size() - slmLicenseTotalMaxClient), true);
               } catch (ConfigException var15) {
                  logger.error("", var15);
               }
            }

            int numRemovingDevice = deviceIdList.size() - slmLicenseTotalMaxClient;
            MonitoringManager monMgr = MonitoringManagerImpl.getInstance();

            for(int j = 0; j < numRemovingDevice; ++j) {
               try {
                  Map deviceId = (Map)deviceIdList.get(j);
                  String dId = (String)deviceId.get("DEVICE_ID");
                  if (dId != null) {
                     boolean result = deviceMgr.deleteDevice(dId);
                     logger.error("Log_License : delete device. id=" + dId + ",idx=" + j);
                     if (result) {
                        WSCall.setPlayerRequest(dId, "agent restart");
                        monMgr.connectionReload(dId, 0);
                        monMgr.scheduleReload(dId, 0);
                     }
                  }
               } catch (Exception var14) {
                  logger.error("", var14);
               }
            }

            DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
            deviceGroupInfo.updateCacheDeviceGroup();
         } else {
            logger.error("need not delete deivce. deviceList=" + deviceIdList.size() + ", del StartIdx=" + slmLicenseTotalMaxClient);
            if (slmLicenseEntity.getLicense_type().equals("13")) {
               try {
                  this.setSlmLicenseHistory("04", slmLicenseEntity.getLicense_key(), "Trial license key is expried!!", true);
               } catch (ConfigException var13) {
                  logger.error("", var13);
               }
            } else {
               try {
                  this.setSlmLicenseHistory("04", slmLicenseEntity.getLicense_key(), "license key was returned!!", true);
               } catch (ConfigException var12) {
                  logger.error("", var12);
               }
            }
         }
      }

   }

   private void deleteMappingInfoOfSlmLicenseOrg(String productCode) throws SQLException {
      this.licenseDao.deleteMappingInfoOfSlmLicenseOrg(productCode);
   }

   public boolean chkHasMaintenance() {
      boolean ret = false;
      boolean isNewUnifiedLicense = false;
      Calendar cal = Calendar.getInstance();
      cal.set(2021, 4, 30, 0, 0, 0);
      cal.set(14, 0);
      Date buildTime = cal.getTime();

      try {
         List licenseList = this.licenseDao.getAllSlmlLicense();
         if (licenseList == null || licenseList.isEmpty()) {
            return true;
         }

         Iterator var6 = licenseList.iterator();

         while(var6.hasNext()) {
            SlmLicenseEntity license = (SlmLicenseEntity)var6.next();
            SlmLicenseEntity temp = this.getSlmLicenseEntity(license.getActivation_key());
            String code = temp.getProduct_code();
            if ("01015A".equals(code) && temp.getLicense_type().equals("11")) {
               isNewUnifiedLicense = true;
               if (temp.getMaint_end_date() != null && temp.getMaint_end_date().after(buildTime)) {
                  ret = true;
               }
            }
         }
      } catch (Exception var10) {
         logger.error("", var10);
      }

      if (!isNewUnifiedLicense) {
         ret = true;
      }

      return ret;
   }

   public boolean hasChargedLicense(String licenseKey) throws Exception {
      List licenses = this.getAllSlmLicense();
      if (null != licenses) {
         String[] key = licenseKey.split("-");
         Iterator var4 = licenses.iterator();

         while(var4.hasNext()) {
            SlmLicenseEntity entity = (SlmLicenseEntity)var4.next();
            if (entity.getLicense_type().equals("11") && entity.getProduct_code().equals(key[0])) {
               return true;
            }
         }
      }

      return false;
   }

   public long chkValidationOfServerVersion() {
      ServerSetupInfo serverSetup = ServerSetupInfoImpl.getInstance();
      int limitDaysForShowingWarning = true;
      long ret = 0L;

      try {
         ServerManagementEntity mng = serverSetup.getRecentServerManagementInfo();
         if (null == mng) {
            return ret;
         }

         Calendar cal = Calendar.getInstance();
         cal.setTime(mng.getChecked());
         cal.add(5, 60);
         if (!mng.getConfirmed()) {
            ret = cal.getTimeInMillis();
         }
      } catch (Exception var7) {
         logger.error("", var7);
      }

      return ret;
   }

   public void deleteProcessDeviceForE2E(SlmLicenseEntity slmLicenseEntity) throws Exception {
      String deviceId = this.getHWUniqueKeyFromActivationKey(slmLicenseEntity.getActivation_key());
      boolean deActivationResult;
      if (CommonConfig.get("e2e.license.system") != null && !CommonConfig.get("e2e.license.system").toUpperCase().equals(ExternalSystemUtils.SYSTEM_PBP)) {
         try {
            deActivationResult = this.deActivationProcessForE2E_SLMDirect(deviceId);
            if (!deActivationResult) {
               logger.error("deActivation fail!" + deviceId);
            }
         } catch (Exception var7) {
            logger.error("deActivation exception!" + deviceId, var7);
         }
      } else {
         try {
            deActivationResult = this.deActivationProcessForE2E(deviceId);
            if (!deActivationResult) {
               logger.error("deActivation fail!" + deviceId);
            }
         } catch (Exception var8) {
            logger.error("deActivation exception!" + deviceId, var8);
         }
      }

      if (this.licenseDao.deleteLicenseInfoForE2EByDeviceId(deviceId)) {
         try {
            this.setSlmLicenseHistoryForE2E("E03", deviceId, slmLicenseEntity.getLicense_key(), slmLicenseEntity.getIssued_no(), "license key was returned!! delete device : " + deviceId, true);
         } catch (ConfigException var6) {
            logger.error("", var6);
         }

         MonitoringManager monMgr = MonitoringManagerImpl.getInstance();

         try {
            if (deviceId != null) {
               boolean result = deviceMgr.deleteDevice(deviceId);
               logger.error("Log_License : delete device. id=" + deviceId);
               if (result) {
                  WSCall.setPlayerRequest(deviceId, "agent restart");
                  monMgr.connectionReload(deviceId, 0);
                  monMgr.scheduleReload(deviceId, 0);
               }
            }

            DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
            deviceGroupInfo.updateCacheDeviceGroup();
         } catch (Exception var5) {
            logger.error("", var5);
         }
      }

   }

   public boolean chkExpired(Timestamp Endtime) {
      boolean rtn = false;
      Calendar calendar = Calendar.getInstance();
      Date now = calendar.getTime();
      Timestamp currentTimestamp = new Timestamp(now.getTime());

      try {
         if (currentTimestamp.after(Endtime)) {
            rtn = false;
         } else {
            rtn = true;
         }
      } catch (Exception var7) {
         logger.error("", var7);
         rtn = false;
      }

      return rtn;
   }

   public boolean chkExpiredForE2E(Timestamp Endtime) {
      boolean rtn = false;
      Calendar calendar = Calendar.getInstance();
      calendar.add(7, -14);
      Date minus14Date = calendar.getTime();
      Timestamp minus14daysTimestamp = new Timestamp(minus14Date.getTime());

      try {
         if (minus14daysTimestamp.after(Endtime)) {
            rtn = true;
         } else {
            rtn = false;
         }
      } catch (Exception var7) {
         logger.error("", var7);
         rtn = false;
      }

      return rtn;
   }

   public boolean chkLicenseExtension(Timestamp Endtime) {
      boolean rtn = false;
      Calendar calendar = Calendar.getInstance();
      calendar.add(7, 5);
      Date now = calendar.getTime();
      Timestamp plus5daysTimestamp = new Timestamp(now.getTime());

      try {
         if (plus5daysTimestamp.after(Endtime)) {
            rtn = true;
         } else {
            rtn = false;
         }
      } catch (Exception var7) {
         logger.error("", var7);
         rtn = false;
      }

      return rtn;
   }

   public boolean chkTrialLicenseKey(SlmLicenseEntity licenseKeyinfo) {
      boolean rtn = false;
      if (licenseKeyinfo.getLicense_type().equals("13") && this.chkFreeTrial(licenseKeyinfo.getStart_date(), licenseKeyinfo.getEnd_date())) {
         rtn = true;
      } else {
         rtn = false;
      }

      return rtn;
   }

   public boolean chkFreeTrial(Timestamp start_date, Timestamp end_date) {
      boolean rtn = false;
      int result = end_date.getYear() - start_date.getYear();
      if (result > 100) {
         rtn = false;
      } else {
         rtn = true;
      }

      return rtn;
   }

   public int getCntLicenseMaxClient(String productCode) throws SQLException {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String orgName = userContainer == null ? "ROOT" : userContainer.getUser().getOrganization();
      return orgName.equals("ROOT") ? this.licenseDao.getCntSlmLicenseByProductCode(productCode) : this.getAssignedLicenseNumByOrg(productCode, orgName);
   }

   private int getAssignedLicenseNumByOrg(String productCode, String orgName) throws SQLException {
      long sumOfAssignedLicenseNumByOrg = 0L;
      List slmLicenseOrgEntities = this.licenseDao.getLicenseInfoAssignedToOrganization();
      Iterator var6 = slmLicenseOrgEntities.iterator();

      while(var6.hasNext()) {
         SlmLicenseOrgEntity slmLicenseOrgEntity = (SlmLicenseOrgEntity)var6.next();
         if (productCode.equals(slmLicenseOrgEntity.getProduct_code())) {
            long maxLicenseCount = slmLicenseOrgEntity.getMax_license_count();
            if (orgName.equals(slmLicenseOrgEntity.getOrganization_name()) && maxLicenseCount > 0L) {
               return (int)maxLicenseCount;
            }

            sumOfAssignedLicenseNumByOrg += maxLicenseCount != -1L ? maxLicenseCount : 0L;
         }
      }

      return this.licenseDao.getCntSlmLicenseByProductCode(productCode) - (int)sumOfAssignedLicenseNumByOrg;
   }

   public boolean isOrgSetLicenseNum(String deviceType, long orgGroupId) throws SQLException {
      List productCodeList = this.getProductCodeByDeviceType(deviceType);
      List slmLicenseOrgEntities = this.licenseDao.getLicenseInfoAssignedToOrganization();
      Iterator var6 = slmLicenseOrgEntities.iterator();

      SlmLicenseOrgEntity slmLicenseOrgEntity;
      long maxLicenseCount;
      do {
         if (!var6.hasNext()) {
            return false;
         }

         slmLicenseOrgEntity = (SlmLicenseOrgEntity)var6.next();
         maxLicenseCount = slmLicenseOrgEntity.getMax_license_count();
      } while(maxLicenseCount == -1L || !productCodeList.contains(slmLicenseOrgEntity.getProduct_code()) || orgGroupId != slmLicenseOrgEntity.getOrganization_id());

      return true;
   }

   public long getMaximumNumMovable(String deviceType, long orgGroupId) throws SQLException {
      List productCodeList = this.getProductCodeByDeviceType(deviceType);
      List slmLicenseOrgEntities = this.licenseDao.getLicenseInfoAssignedToOrganization();
      int totalNumOfAssignedLicense = 0;
      int totalNumOfDevicesUsingCommonLicense = 0;
      Iterator var8 = slmLicenseOrgEntities.iterator();

      while(var8.hasNext()) {
         SlmLicenseOrgEntity slmLicenseOrgEntity = (SlmLicenseOrgEntity)var8.next();
         long maxLicenseCount = slmLicenseOrgEntity.getMax_license_count();
         long usedLicenseCount = slmLicenseOrgEntity.getUsed_license_count();
         if (productCodeList.contains(slmLicenseOrgEntity.getProduct_code())) {
            totalNumOfAssignedLicense = (int)((long)totalNumOfAssignedLicense + (maxLicenseCount != -1L ? maxLicenseCount : 0L));
            if (maxLicenseCount == -1L) {
               totalNumOfDevicesUsingCommonLicense = (int)((long)totalNumOfDevicesUsingCommonLicense + usedLicenseCount);
            }

            if (orgGroupId == slmLicenseOrgEntity.getOrganization_id() && maxLicenseCount != -1L) {
               return maxLicenseCount - usedLicenseCount;
            }
         }
      }

      int cntSlmLicenseByProductCodeList = this.getCntSlmLicenseByProductCodeList(productCodeList);
      return (long)(cntSlmLicenseByProductCodeList - (totalNumOfAssignedLicense + totalNumOfDevicesUsingCommonLicense));
   }

   public int getCntSlmLicenseByProductCodeList(List productCode) throws SQLException {
      int rtn = 0;
      List slmLicenseList = this.licenseDao.getCntSlmLicensebyProductCodeList(productCode);

      for(int i = 0; i < slmLicenseList.size(); ++i) {
         rtn = (int)((long)rtn + ((SlmLicenseEntity)slmLicenseList.get(i)).getMax_clients());
      }

      return rtn;
   }

   public int getCntSlmLicenseMaxClients(String productCode, String licenseType) throws SQLException {
      int rtn = 0;
      List slmLicenseList = this.licenseDao.getCntSlmLicenseByProductCode(productCode, licenseType);

      for(int i = 0; i < slmLicenseList.size(); ++i) {
         rtn = (int)((long)rtn + ((SlmLicenseEntity)slmLicenseList.get(i)).getMax_clients());
      }

      return rtn;
   }

   public SlmLicenseEntity findTrialKeyFromLicenseList(String productCode) {
      try {
         List slmLicenseList = this.licenseDao.getProductCodeSlmLicense(productCode);
         Iterator var3 = slmLicenseList.iterator();

         while(var3.hasNext()) {
            SlmLicenseEntity slmLicenseEntity = (SlmLicenseEntity)var3.next();
            if (this.chkTrialLicenseKey(slmLicenseEntity)) {
               return slmLicenseEntity;
            }
         }
      } catch (SQLException var5) {
         logger.error("", var5);
      }

      return null;
   }

   public SlmLicenseEntity findFreeOfChargedKeyFromLicenseList(String productCode) {
      try {
         List slmLicenseList = this.licenseDao.getProductCodeSlmLicense(productCode);
         Iterator var3 = slmLicenseList.iterator();

         while(var3.hasNext()) {
            SlmLicenseEntity slmLicenseEntity = (SlmLicenseEntity)var3.next();
            if (slmLicenseEntity.getLicense_type().equalsIgnoreCase("12")) {
               return slmLicenseEntity;
            }
         }
      } catch (SQLException var5) {
         logger.error("", var5);
      }

      return null;
   }

   public static List getMac() {
      ArrayList macAddresses = new ArrayList();

      try {
         Enumeration nis = NetworkInterface.getNetworkInterfaces();
         InetAddress ip = InetAddress.getLocalHost();
         NetworkInterface network = NetworkInterface.getByInetAddress(ip);
         byte[] current_mac = network.getHardwareAddress();
         StringBuilder sb = new StringBuilder();

         for(int i = 0; i < current_mac.length; ++i) {
            sb.append(String.format("%02X%s", current_mac[i], i < current_mac.length - 1 ? "-" : ""));
         }

         String current_mac_address = sb.toString();

         label60:
         while(true) {
            NetworkInterface ni;
            do {
               do {
                  if (!nis.hasMoreElements()) {
                     break label60;
                  }

                  ni = (NetworkInterface)nis.nextElement();
               } while(ni.getHardwareAddress() == null);
            } while(ni.getHardwareAddress().toString().equals(""));

            byte[] mac = ni.getHardwareAddress();
            sb = new StringBuilder();

            for(int i = 0; i < mac.length; ++i) {
               sb.append(String.format("%02X%s", mac[i], i < mac.length - 1 ? "-" : ""));
            }

            if (current_mac_address.equals(sb.toString())) {
               System.out.println("****************************************************************************************");
               System.out.println("current Mac address : " + sb.toString());
               System.out.println("****************************************************************************************");
               macAddresses.add(0, sb.toString().replaceAll("-", ""));
            } else if (mac.length < 7 && !sb.toString().trim().equals("")) {
               System.out.println("****************************************************************************************");
               System.out.println("Mac address : " + sb.toString());
               System.out.println("****************************************************************************************");
               macAddresses.add(sb.toString().replaceAll("-", ""));
            }
         }
      } catch (UnknownHostException var10) {
         logger.error("", var10);
      } catch (SocketException var11) {
         logger.error("", var11);
      } catch (Exception var12) {
         logger.error("", var12);
      }

      macAddresses.add("01-01-01-01-01-01".replaceAll("-", ""));
      return macAddresses;
   }

   private void deleteLicenseKey(SlmLicenseEntity slmLicenseEntity) throws Exception {
      if (null != slmLicenseEntity) {
         this.checkSlmLicenseDeleteDevice(slmLicenseEntity);
         this.licenseDao.deleteLicenseInfo(slmLicenseEntity.getLicense_key());
      }
   }

   public boolean checkProductCode(String productCode) {
      boolean rtn = false;
      if (productCode.equals("010120")) {
         rtn = false;
      } else if (productCode.equals("010121")) {
         rtn = false;
      } else if (productCode.equals("010V31")) {
         rtn = false;
      } else if (productCode.equals("010311")) {
         rtn = true;
      } else if (productCode.equals("01011N")) {
         rtn = true;
      } else if (productCode.equals("01064A")) {
         rtn = true;
      } else if (productCode.equals("01014A")) {
         rtn = false;
      } else if (productCode.equals("01015A")) {
         rtn = true;
      } else if (productCode.equals("0101CS")) {
         rtn = true;
      } else if (productCode.equals("010D0E")) {
         rtn = true;
      } else if (productCode.equals("010D0F")) {
         rtn = true;
      } else if (productCode.equals("01010M")) {
         rtn = true;
      }

      return rtn;
   }

   public void setSlmLicenseHistory(String type, String licenseKey, String desc, Boolean confirm) throws ConfigException, SQLException {
      SlmLicenseHistoryEntity LicenseHistory = new SlmLicenseHistoryEntity();
      LicenseHistory.setType(type);
      LicenseHistory.setLicenseKey(licenseKey);
      LicenseHistory.setDescription(desc);
      LicenseHistory.setConfirm(confirm);
      this.licenseDao.setSlmLicenseHistory(LicenseHistory);
   }

   public void setSlmLicenseHistoryForE2E(String type, String deviceId, String licenseKey, String issudNo, String desc, Boolean confirm) throws ConfigException, SQLException {
      SlmLicenseHistoryEntity LicenseHistory = new SlmLicenseHistoryEntity();
      LicenseHistory.setType(type);
      LicenseHistory.setDevice_id(deviceId);
      LicenseHistory.setLicenseKey(licenseKey);
      LicenseHistory.setIssued_no(issudNo);
      LicenseHistory.setDescription(desc);
      this.licenseDao.setSlmLicenseHistoryForE2E(LicenseHistory);
   }

   public SlmLicenseHistoryEntity getSlmLicenseHistory(String licenseKey) throws SQLException {
      SlmLicenseHistoryEntity LicenseHistory = null;
      LicenseHistory = this.licenseDao.getSlmLicenseHistory(licenseKey);
      return LicenseHistory;
   }

   public int getCntAllSlmLicenseHistoryForE2E() throws SQLException {
      return this.licenseDao.getCntAllSlmLicenseHistoryForE2E();
   }

   public List getAllSlmLicenseHistoryForE2E(int pageNumber) throws SQLException {
      List licenseHistoryList = null;
      licenseHistoryList = this.licenseDao.getAllSlmLicenseHistoryForE2E(pageNumber);
      return licenseHistoryList;
   }

   public int getCntSlmLicenseHistoryForE2EByDeviceId(String deviceId) throws SQLException {
      return this.licenseDao.getCntSlmLicenseHistoryForE2EByDeviceId(deviceId);
   }

   public List getSlmLicenseHistoryForE2EByDeviceId(String deviceId, int pageNumber) throws SQLException {
      List licenseHistoryList = null;
      licenseHistoryList = this.licenseDao.getSlmLicenseHistoryForE2EByDeviceId(deviceId, pageNumber);
      return licenseHistoryList;
   }

   public boolean deleteAllLicense() throws SQLException {
      return this.licenseDao.deleteAllLicense();
   }

   public boolean addDeviceSamples(String mac, int num) throws SQLException {
      return this.licenseDao.addDeviceSamples(mac, num);
   }

   public boolean addSocDeviceSamples(String mac, int num) throws SQLException {
      return this.licenseDao.addSocDeviceSamples(mac, num);
   }

   public boolean addLiteDeviceSamples(String mac, int num) throws SQLException {
      return this.licenseDao.addLiteDeviceSamples(mac, num);
   }

   public boolean TestAddLicenseKey(String slmLicenseKey, String ActivationKey) throws ConfigException, SQLException {
      return this.licenseDao.TestAddLicenseKey(slmLicenseKey, ActivationKey);
   }

   public String getProductNameByDeviceType(String deviceType) {
      System.out.println("deviceType :" + deviceType);
      String rtn = "";
      List productCodeList = (List)supportedDeviceTypeMap.get(deviceType);
      if (productCodeList != null && productCodeList.size() > 0) {
         rtn = this.getProductCodeDes((String)productCodeList.get(0));
      }

      return rtn;
   }

   public String getProductCodeDes(String productCode) {
      String rtn = null;
      byte var4 = -1;
      switch(productCode.hashCode()) {
      case **********:
         if (productCode.equals("010120")) {
            var4 = 0;
         }
         break;
      case **********:
         if (productCode.equals("010121")) {
            var4 = 1;
         }
         break;
      case **********:
         if (productCode.equals("010V31")) {
            var4 = 2;
         }
      }

      switch(var4) {
      case 0:
      case 1:
      case 2:
         rtn = "Unified Player License (I, S, Signage)";
         break;
      default:
         rtn = this.getProductCode(productCode);
      }

      return rtn;
   }

   public String getProductCode(String productCode) {
      String rtn = null;
      if (productCode.equals("010120")) {
         rtn = "I Player";
      } else if (productCode.equals("010121")) {
         rtn = "S Player";
      } else if (productCode.equals("010311")) {
         rtn = "Lite Player";
      } else if (productCode.equals("01011N")) {
         rtn = "Android Player";
      } else if (productCode.equals("010V31")) {
         rtn = "Signage Player";
      } else if (productCode.equals("01064A")) {
         rtn = "RM Player";
      } else if (productCode.equals("01014A")) {
         rtn = "Unified Player License (I, S, Signage)";
      } else if (productCode.equals("01015A")) {
         rtn = "Unified Player License 2 (I, S, Signage)";
      } else if (productCode.equals("0101CS")) {
         rtn = "Cloud Server License";
      } else if (productCode.equals("010D0F")) {
         rtn = "Analytics Display License";
      } else if (productCode.equals("010D0E")) {
         rtn = "Analytics Store License";
      } else if (productCode.equals("01010M")) {
         rtn = "Migration License";
      }

      return rtn;
   }

   public List getAllSlmLicenseHistory(int pageNumber) {
      List historyEntity = null;

      try {
         historyEntity = this.licenseDao.getAllSlmLicenseHistory(pageNumber * 25);
      } catch (SQLException var4) {
         logger.error("", var4);
      }

      return historyEntity;
   }

   public List getAllSlmLicenseHistory() {
      List historyEntity = null;

      try {
         historyEntity = this.licenseDao.getAllSlmLicenseHistory();
      } catch (SQLException var3) {
         logger.error("", var3);
      }

      return historyEntity;
   }

   public int getCntSlmLicenseHistory() {
      return this.licenseDao.getCntSlmLicenseHistory();
   }

   public String getErrMsg(int i) {
      String rtn;
      switch(i) {
      case 0:
         rtn = "success ";
         break;
      case 500:
         rtn = "DB error ";
         break;
      case 600:
         rtn = "unvalid Activation Key ";
         break;
      case 700:
         rtn = "HWuniqueKey error ";
         break;
      case 800:
         rtn = "license error";
         break;
      case 1000:
         rtn = "internal error ";
         break;
      default:
         rtn = "SLM Server Error code : ";
      }

      return rtn + i;
   }

   public static int safeLongToInt(long l) {
      if (l >= -2147483648L && l <= 2147483647L) {
         return (int)l;
      } else {
         throw new IllegalArgumentException(l + " cannot be cast to int without changing its value.");
      }
   }

   public MobileLicenseEntity getMobileLicenseEntity() {
      PagedListInfo pagedListInfo = null;
      String section = "getLicenseHistoryList";
      int startPos = 1;
      int pageSize = 10;
      SelectCondition condObj = new SelectCondition();
      condObj.setSort_name("");
      condObj.setOrder_dir("");
      Map condition = new HashMap();
      condition.put("condition", condObj);

      try {
         pagedListInfo = this.getPagedList(startPos, pageSize, condition, section);
         pagedListInfo = this.getPagedList(startPos, pagedListInfo.getTotalRowCount(), condition, section);
      } catch (Exception var13) {
         logger.error("", var13);
      }

      List slmLicenseList = pagedListInfo.getPagedResultList();
      int cnt = pagedListInfo.getTotalRowCount();
      new SlmLicenseEntity();
      boolean mobileLicense = false;
      Long totalConnection = 0L;

      for(int i = 0; i < cnt; ++i) {
         SlmLicenseEntity slmLicenseEntity = (SlmLicenseEntity)slmLicenseList.get(i);
         if (slmLicenseEntity.getProduct_code().equals("010115")) {
            mobileLicense = true;
            totalConnection = totalConnection + slmLicenseEntity.getMax_clients();
         }
      }

      return new MobileLicenseEntity(mobileLicense, totalConnection);
   }

   public PagedListInfo getPagedList(int startPos, int pageSize, Map condition, String section) throws SQLException, ConfigException {
      PagedListInfo pagedListInfo = null;
      if (section.equals("getLicenseHistoryList")) {
         pagedListInfo = this.licenseDao.getLicenseList(startPos, pageSize, condition);
      }

      return pagedListInfo;
   }

   public String getRegistryValue(String key) throws Exception {
      RegistryKey r = Registry.HKEY_LOCAL_MACHINE.openSubKey("Software\\Samsung\\MagicInfo-i\\Server\\Premium");
      String value = "";

      try {
         RegStringValue strValue = (RegStringValue)r.getValue(key);
         value = strValue.getData();
      } catch (Exception var5) {
         logger.error(var5);
      }

      return value;
   }

   public int getCntSlmLicense() throws SQLException {
      return this.licenseDao.getCntSlmlicense();
   }

   public boolean setValid(String licenseKey, boolean state) throws SQLException {
      return this.licenseDao.setValid(licenseKey, state);
   }

   public boolean licenseChk(String productCode) throws SQLException {
      boolean rtn = true;
      if (this.getRemainLicenseCountByProductCode(productCode) < 0) {
         logger.error("[MagicInfo_License] fail license slmLicenseTotalMaxClient productCode : " + productCode);
         rtn = false;
      }

      return rtn;
   }

   public boolean licenseChk() {
      boolean rtn = true;

      try {
         List licenses = this.getAllSlmLicense();
         if (licenses != null && licenses.size() > 0) {
            Iterator var3 = licenses.iterator();

            while(var3.hasNext()) {
               SlmLicenseEntity license = (SlmLicenseEntity)var3.next();
               if (!license.getProduct_code().equals("010D0F") && !license.getProduct_code().equals("010D0E") && this.getRemainLicenseCountByProductCode(license.getProduct_code()) < 0) {
                  logger.error("[MagicInfo_License] fail license slmLicenseTotalMaxClient productCode : " + license.getProduct_code());
                  return false;
               }
            }
         }
      } catch (Exception var5) {
         logger.error("", var5);
      }

      return rtn;
   }

   public List getInvaildLicenseList(String productCode) throws SQLException {
      List invalidList = new ArrayList();
      boolean integrateLicense = false;
      int deviceCount = false;
      int slmLicenseTotalMaxClient = false;
      List deviceTypeList = (List)supportedProductCodeMap.get(productCode);
      Iterator var7 = supportedDeviceTypeMap.keySet().iterator();

      while(var7.hasNext()) {
         String deviceType = (String)var7.next();
         if (deviceTypeList.contains(deviceType)) {
            byte var10 = -1;
            switch(deviceType.hashCode()) {
            case -**********:
               if (deviceType.equals("LEDBOX")) {
                  var10 = 3;
               }
               break;
            case -**********:
               if (deviceType.equals("SIGNAGE")) {
                  var10 = 2;
               }
               break;
            case -**********:
               if (deviceType.equals("SPLAYER")) {
                  var10 = 0;
               }
               break;
            case **********:
               if (deviceType.equals("iPLAYER")) {
                  var10 = 1;
               }
            }

            HashMap data;
            int deviceCount;
            int slmLicenseTotalMaxClient;
            switch(var10) {
            case 0:
            case 1:
            case 2:
            case 3:
               if (!integrateLicense) {
                  deviceCount = this.getDeviceCountByProductCode(productCode);
                  slmLicenseTotalMaxClient = this.getLicenseCountByProductCode(productCode);
                  integrateLicense = true;
                  if (slmLicenseTotalMaxClient < deviceCount) {
                     data = new HashMap();
                     data.put("name", this.getProductCodeDes(productCode));
                     data.put("productCode", productCode);
                     data.put("deviceCount", deviceCount);
                     data.put("licenseCount", slmLicenseTotalMaxClient);
                     invalidList.add(data);
                  }
               }
               break;
            default:
               deviceCount = this.getDeviceCountByProductCode(productCode);
               slmLicenseTotalMaxClient = this.getLicenseCountByProductCode(productCode);
               if (slmLicenseTotalMaxClient < deviceCount) {
                  data = new HashMap();
                  data.put("name", this.getProductCodeDes(productCode));
                  data.put("productCode", productCode);
                  data.put("deviceCount", deviceCount);
                  data.put("licenseCount", slmLicenseTotalMaxClient);
                  invalidList.add(data);
               }
            }
         }
      }

      return invalidList;
   }

   public List getInvaildLicenseList() throws SQLException {
      List invalidList = new ArrayList();
      List licenseList = this.licenseDao.getAllSlmlLicense();
      boolean integrateLicense = false;
      if (licenseList != null && licenseList.size() > 0) {
         Iterator var4 = licenseList.iterator();

         while(true) {
            SlmLicenseEntity license;
            List deviceTypeList;
            do {
               if (!var4.hasNext()) {
                  return invalidList;
               }

               license = (SlmLicenseEntity)var4.next();
               int deviceCount = false;
               int slmLicenseTotalMaxClient = false;
               deviceTypeList = (List)supportedProductCodeMap.get(license.getProduct_code());
            } while(deviceTypeList == null);

            Iterator var9 = supportedDeviceTypeMap.keySet().iterator();

            while(var9.hasNext()) {
               String deviceType = (String)var9.next();
               if (deviceTypeList.contains(deviceType)) {
                  byte var12 = -1;
                  switch(deviceType.hashCode()) {
                  case -**********:
                     if (deviceType.equals("LEDBOX")) {
                        var12 = 3;
                     }
                     break;
                  case -**********:
                     if (deviceType.equals("SIGNAGE")) {
                        var12 = 2;
                     }
                     break;
                  case -**********:
                     if (deviceType.equals("SPLAYER")) {
                        var12 = 0;
                     }
                     break;
                  case **********:
                     if (deviceType.equals("iPLAYER")) {
                        var12 = 1;
                     }
                  }

                  HashMap data;
                  int deviceCount;
                  int slmLicenseTotalMaxClient;
                  switch(var12) {
                  case 0:
                  case 1:
                  case 2:
                  case 3:
                     if (!integrateLicense) {
                        deviceCount = this.getDeviceCountByProductCode(license.getProduct_code());
                        slmLicenseTotalMaxClient = this.getLicenseCountByProductCode(license.getProduct_code());
                        integrateLicense = true;
                        if (slmLicenseTotalMaxClient < deviceCount) {
                           data = new HashMap();
                           data.put("name", this.getProductCodeDes(license.getProduct_code()));
                           data.put("productCode", license.getProduct_code());
                           data.put("deviceCount", deviceCount);
                           data.put("licenseCount", slmLicenseTotalMaxClient);
                           invalidList.add(data);
                        }
                     }
                     break;
                  default:
                     deviceCount = this.getDeviceCountByProductCode(license.getProduct_code());
                     slmLicenseTotalMaxClient = this.getLicenseCountByProductCode(license.getProduct_code());
                     if (slmLicenseTotalMaxClient < deviceCount) {
                        data = new HashMap();
                        data.put("name", this.getProductCodeDes(license.getProduct_code()));
                        data.put("productCode", license.getProduct_code());
                        data.put("deviceCount", deviceCount);
                        data.put("licenseCount", slmLicenseTotalMaxClient);
                        invalidList.add(data);
                     }
                  }
               }
            }
         }
      } else {
         return invalidList;
      }
   }

   public boolean addLicenseStatus(String productCode) throws SQLException {
      boolean addStatus = true;
      Calendar cal = Calendar.getInstance();
      cal.set(10, 0);
      cal.set(12, 0);
      cal.set(13, 0);
      long time = cal.getTimeInMillis();
      cal.add(2, 1);
      long expiredDate = cal.getTimeInMillis();
      SlmLicenseStatusEntity licenseStatus = this.licenseDao.getLicenseStaus(productCode);
      String key;
      if (licenseStatus != null) {
         try {
            key = this.decrypt(licenseStatus.getExpired_key());
            String[] parseKey = key.split("\\|");
            if (parseKey.length == 3 && expiredDate > Long.valueOf(parseKey[2])) {
               addStatus = false;
            }
         } catch (NoSuchAlgorithmException var16) {
            logger.error("", var16);
         } catch (UnsupportedEncodingException var17) {
            logger.error("", var17);
         } catch (GeneralSecurityException var18) {
            logger.error("", var18);
         }
      }

      if (addStatus) {
         if (this.licenseDao.chkLicenseStatus(productCode) > 0) {
            this.licenseDao.delLicenseStatus(productCode);
         }

         key = null;

         try {
            key = this.encrypt(productCode + "|" + time + "|" + expiredDate);
            logger.error("[MagicInfo_SlmLicenseStatus] input expired key : " + key);
         } catch (NoSuchAlgorithmException var13) {
         } catch (UnsupportedEncodingException var14) {
         } catch (GeneralSecurityException var15) {
         }

         if (key != null) {
            SlmLicenseStatusEntity status = new SlmLicenseStatusEntity();
            status.setProduct_code(productCode);
            status.setExpired_key(key);
            status.setReg_date(new Timestamp(time));

            try {
               this.setSlmLicenseHistory("01", productCode, "Add the invalid License product Code", true);
            } catch (ConfigException var12) {
            }

            return this.licenseDao.addLicenseStatus(status);
         }
      }

      return true;
   }

   public int chkLicenseStatus(String productCode) throws SQLException {
      return this.licenseDao.chkLicenseStatus(productCode);
   }

   public List getListLicenseStatus() throws SQLException {
      return this.licenseDao.getListLicenseStatus();
   }

   private static void AES256Util(String key) throws UnsupportedEncodingException {
      iv = key.substring(0, 16);
      byte[] keyBytes = new byte[16];
      byte[] b = key.getBytes("UTF-8");
      int len = b.length;
      if (len > keyBytes.length) {
         len = keyBytes.length;
      }

      System.arraycopy(b, 0, keyBytes, 0, len);
      keySpec = new SecretKeySpec(keyBytes, "AES");
   }

   public String encrypt(String str) throws NoSuchAlgorithmException, GeneralSecurityException, UnsupportedEncodingException {
      Cipher c = Cipher.getInstance("AES/CTR/NoPadding");
      c.init(1, keySpec, new IvParameterSpec(iv.getBytes()));
      byte[] encrypted = c.doFinal(str.getBytes());
      String enStr = new String(Base64.encodeBase64(encrypted));
      return enStr;
   }

   public String decrypt(String str) throws NoSuchAlgorithmException, GeneralSecurityException, UnsupportedEncodingException {
      Cipher c = Cipher.getInstance("AES/CTR/NoPadding");
      c.init(2, keySpec, new IvParameterSpec(iv.getBytes()));
      byte[] byteStr = Base64.decodeBase64(str.getBytes());
      byte[] result = c.doFinal(byteStr);
      return new String(result);
   }

   public SlmLicenseStatusEntity getLicenseStaus(String productCode) throws SQLException {
      return this.licenseDao.getLicenseStaus(productCode);
   }

   public void chkSlmLicenseStatus(String productCode, Timestamp now, Timestamp end) throws SQLException {
      try {
         if (now.after(end)) {
            DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
            int slmLicenseTotalMaxClient = this.getLicenseCountByProductCode(productCode);
            List deviceIdList = this.getDeviceListWithProductCode(productCode);
            if (deviceIdList == null) {
               logger.error("License is alive. do not delete device.");
               return;
            }

            if (slmLicenseTotalMaxClient < deviceIdList.size()) {
               Locale locale = Locale.getDefault();
               DeviceLogManager logMgr = DeviceLogManagerImpl.getInstance();
               ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
               rms.setBasename("resource/messages");
               MonitoringManager monMgr = MonitoringManagerImpl.getInstance();
               String eventType = "TEXT_TITLE_DELETE_DEVICE_P";
               String strMenu = rms.getMessage("COM_SID_MENU", (Object[])null, Locale.ENGLISH);
               String strMenuName = rms.getMessage("COM_TEXT_MONITORING_P", (Object[])null, Locale.ENGLISH);
               String strCommand = rms.getMessage("TEXT_TITLE_DELETE_DEVICE_P", (Object[])null, Locale.ENGLISH);
               int finishNumber = deviceIdList.size() - slmLicenseTotalMaxClient;

               for(int j = 0; j < finishNumber; ++j) {
                  try {
                     Map deviceId = (Map)deviceIdList.get(j);
                     String dId = (String)deviceId.get("DEVICE_ID");
                     if (dId != null) {
                        boolean result = deviceDao.deleteDevice(dId);
                        logger.error("[MagicInfo_SlmLicenseStatus][" + dId + "] delete device. id=" + dId + ",idx=" + j);
                        if (result) {
                           WSCall.setPlayerRequest(dId, "agent restart");
                           monMgr.connectionReload(dId, 0);
                           monMgr.scheduleReload(dId, 0);
                        }
                     }
                  } catch (Exception var20) {
                  }
               }

               this.setSlmLicenseHistory("01", productCode, "Delete the invalid devices number : " + finishNumber, true);
               this.delLicenseStatus(productCode);
            }
         }
      } catch (Exception var21) {
      }

   }

   public boolean delLicenseStatus(String product_code) throws SQLException {
      return this.licenseDao.delLicenseStatus(product_code);
   }

   public List getDeviceListWithProductCode(String productCode) throws SQLException {
      return this.getDeviceListByProductCode(productCode);
   }

   public String errorCodeToMessage(int errorCode, Locale locale) {
      String rtn = "";
      switch(errorCode) {
      case 8:
         rtn = rms.getMessage("message.license_error_8", (Object[])null, locale);
         break;
      case 900:
         rtn = rms.getMessage("message.license_error_900", (Object[])null, locale);
         break;
      case 4315:
         rtn = rms.getMessage("message.license_error_4315", (Object[])null, locale);
         break;
      case 12000:
         rtn = rms.getMessage("message.license_error_12000", (Object[])null, locale);
         break;
      case 13000:
         rtn = rms.getMessage("message.license_error_13000", (Object[])null, locale);
         break;
      case 19002:
         rtn = rms.getMessage("message.license_error_19002", (Object[])null, locale);
         break;
      default:
         rtn = rms.getMessage("COM_MIS_TEXT_ERROR_SLM_LICENSE_SERVER_P", (Object[])null, locale) + " : " + errorCode;
      }

      return rtn;
   }

   public void activationProcessForE2E(String selDeviceId, String accountCode, String brandCode, String modelCd, String location, String deviceName) throws Exception {
      SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
      JSONObject newLicenseJSON = null;

      try {
         newLicenseJSON = slmMgr.requestNewLicenseKey("MI-CLOUD", accountCode, brandCode, "domainInfo", "01", location, modelCd, 1);
      } catch (Exception var18) {
         throw var18;
      }

      if (newLicenseJSON.getString("resultCode").equalsIgnoreCase("00")) {
         String licenseKey = newLicenseJSON.getString("licenseKey");
         String issudNo = newLicenseJSON.getString("issudNo");
         this.setSlmLicenseHistoryForE2E("E00", selDeviceId, licenseKey, issudNo, "requestNewLicenseKey success", true);
         int rtn = slmMgr.sdkLicenseKeyVerify(licenseKey);
         if (rtn != 0) {
            this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "licenseKey verify fail! code : " + rtn + " Device Id = " + selDeviceId, true);
            throw new Exception("FAIL - licenseKey verify fail!" + licenseKey);
         } else if (this.licenseKeyDuplicateForE2E(licenseKey) != 0) {
            int rtn = 900;
            this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "licenseKey duplicate! code : " + rtn + " Device ID = " + selDeviceId, true);
            throw new Exception("FAIL - licenseKey duplicate!" + licenseKey);
         } else {
            JSONObject activationJSON = null;

            try {
               activationJSON = slmMgr.requestNewActivation("MI-CLOUD", issudNo, licenseKey, "01", selDeviceId, location, deviceName, accountCode, brandCode);
            } catch (Exception var17) {
               throw var17;
            }

            if (activationJSON.getString("resultCode").equalsIgnoreCase("00")) {
               String activationKey = activationJSON.getString("activaKey");
               SlmLicenseEntity licenseEntity = this.getSlmLicenseEntity(activationKey);
               this.setSlmLicenseHistoryForE2E("E00", selDeviceId, licenseKey, issudNo, "requestNewActivation success", true);
               if (!licenseEntity.getLicense_key().equals(licenseKey)) {
                  this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "licenseKey is different from Activation Key. Device ID = " + selDeviceId, true);
                  throw new Exception("FAIL - LicenseKey is different");
               } else if (licenseEntity.getMax_clients() != 1L) {
                  this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "Client Number is not 1. Device ID = " + selDeviceId, true);
                  throw new Exception("FAIL - Client number is not 1");
               } else {
                  String deviceIdFromAK = this.getHWUniqueKeyFromActivationKey(activationKey);
                  if (!deviceIdFromAK.equalsIgnoreCase(selDeviceId)) {
                     this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "Device ID is different from Activation key. Device ID = " + selDeviceId, true);
                     throw new Exception("FAIL - Device id is differnet");
                  } else {
                     int result = this.activationActiveForE2E(licenseKey, activationKey, selDeviceId, issudNo);
                     if (result != 0) {
                        this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "Active activation fail code = " + result + " Device ID = " + selDeviceId, true);
                        throw new Exception("FAIL - Active activation fail code = " + result);
                     }
                  }
               }
            } else {
               this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "Get Activation Key Fail ErrCode" + activationJSON.getString("resultCode") + "  Msg : " + activationJSON.getString("resultMessage") + " Device ID = " + selDeviceId, true);
               throw new Exception("FAIL - Get Activation Key Fail ErrCode" + activationJSON.getString("resultCode") + "Msg : " + activationJSON.getString("resultMessage"));
            }
         }
      } else {
         this.setSlmLicenseHistoryForE2E("E02", selDeviceId, "", "", "Get LicenseKey Fail ErrCode" + newLicenseJSON.getString("resultCode") + "  Msg : " + newLicenseJSON.getString("resultMessage") + " Device ID = " + selDeviceId, true);
         throw new Exception("FAIL - Get LicenseKey Fail ErrCode" + newLicenseJSON.getString("resultCode") + "Msg : " + newLicenseJSON.getString("resultMessage"));
      }
   }

   public boolean reActivationProcessForE2E(String selDeviceId) throws Exception {
      SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
      SlmLicenseEntity slmLicenseEntity = this.licenseDao.getSlmLicenseForE2EByDeviceId(selDeviceId);
      if (slmLicenseEntity == null) {
         this.setSlmLicenseHistoryForE2E("E02", selDeviceId, "", "", "No license Information. Cannot extend license period.", true);
         return false;
      } else {
         String licenseKey = slmLicenseEntity.getLicense_key();
         String issudNo = slmLicenseEntity.getIssued_no();
         JSONObject activationJSON = null;

         try {
            activationJSON = slmMgr.requestReActivation("MI-CLOUD", issudNo, licenseKey, "01", selDeviceId);
         } catch (Exception var11) {
            throw var11;
         }

         if (activationJSON.getString("resultCode").equalsIgnoreCase("00")) {
            String activationKey = activationJSON.getString("activaKey");
            SlmLicenseEntity licenseEntity = this.getSlmLicenseEntity(activationKey);
            this.setSlmLicenseHistoryForE2E("E00", selDeviceId, licenseKey, issudNo, "requestReActivation success", true);
            if (!licenseEntity.getLicense_key().equals(licenseKey)) {
               this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "licenseKey is different from Activation Key. Device ID = " + selDeviceId, true);
               throw new Exception("FAIL - LicenseKey is different");
            } else if (licenseEntity.getMax_clients() != 1L) {
               this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "Client Number is not 1. Device ID = " + selDeviceId, true);
               throw new Exception("FAIL - Client number is not 1");
            } else {
               String deviceIdFromAK = this.getHWUniqueKeyFromActivationKey(activationKey);
               if (!deviceIdFromAK.equalsIgnoreCase(selDeviceId)) {
                  this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "Device ID is different from Activation key. Device ID = " + selDeviceId, true);
                  throw new Exception("FAIL - Device id is differnet");
               } else {
                  int result = this.reActivationActiveForE2E(licenseKey, activationKey, selDeviceId, issudNo);
                  if (result != 0) {
                     this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "Active reactivation fail code = " + result + " Device ID = " + selDeviceId, true);
                     throw new Exception("FAIL - Active activation fail code = " + result);
                  } else {
                     return true;
                  }
               }
            }
         } else {
            this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "Get reActivation Key Fail ErrCode" + activationJSON.getString("resultCode") + "  Msg : " + activationJSON.getString("resultMessage") + " Device ID = " + selDeviceId, true);
            throw new Exception("FAIL - Get Activation Key Fail ErrCode" + activationJSON.getString("resultCode") + "Msg : " + activationJSON.getString("resultMessage"));
         }
      }
   }

   public boolean deActivationProcessForE2E(String deviceId) throws Exception {
      SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
      SlmLicenseEntity slmLicenseEntity = this.licenseDao.getSlmLicenseForE2EByDeviceId(deviceId);
      JSONObject deActivationJSON = null;

      try {
         deActivationJSON = slmMgr.requestDeActivation("MI-CLOUD", slmLicenseEntity.getIssued_no(), slmLicenseEntity.getLicense_key(), "01", deviceId);
      } catch (Exception var6) {
         throw var6;
      }

      if (deActivationJSON.getString("resultCode").equalsIgnoreCase("00")) {
         this.setSlmLicenseHistoryForE2E("E00", deviceId, slmLicenseEntity.getLicense_key(), slmLicenseEntity.getIssued_no(), "requestDeActivation success", true);
         return true;
      } else {
         this.setSlmLicenseHistoryForE2E("E02", deviceId, slmLicenseEntity.getLicense_key(), slmLicenseEntity.getIssued_no(), "DeActivae Fail ErrCode" + deActivationJSON.getString("resultCode") + "  Msg : " + deActivationJSON.getString("resultMessage") + " Device ID = " + deviceId, true);
         return false;
      }
   }

   public boolean swapProcessForE2E(SlmLicenseEntity oldslmLicenseEntity, String newDeviceId, String location, String deviceName) throws Exception {
      SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
      String issudNo = oldslmLicenseEntity.getIssued_no();
      String licenseKey = oldslmLicenseEntity.getLicense_key();
      String oldDeviceId = oldslmLicenseEntity.getDevice_id();
      JSONObject activationJSON = null;

      try {
         activationJSON = slmMgr.requestChangeDevice("MI-CLOUD", issudNo, licenseKey, "01", oldDeviceId, newDeviceId, location, deviceName);
      } catch (Exception var14) {
         throw var14;
      }

      if (activationJSON.getString("resultCode").equalsIgnoreCase("00")) {
         String activationKey = activationJSON.getString("activaKey");
         SlmLicenseEntity licenseEntity = this.getSlmLicenseEntity(activationKey);
         this.setSlmLicenseHistoryForE2E("E00", newDeviceId, licenseKey, issudNo, "requestNewActivation success", true);
         if (!licenseEntity.getLicense_key().equals(licenseKey)) {
            this.setSlmLicenseHistoryForE2E("E02", newDeviceId, licenseKey, issudNo, "licenseKey is different from Activation Key. Device ID = " + newDeviceId, true);
            throw new Exception("FAIL - LicenseKey is different");
         } else if (licenseEntity.getMax_clients() != 1L) {
            this.setSlmLicenseHistoryForE2E("E02", newDeviceId, licenseKey, issudNo, "Client Number is not 1. Device ID = " + newDeviceId, true);
            throw new Exception("FAIL - Client number is not 1");
         } else {
            String deviceIdFromAK = this.getHWUniqueKeyFromActivationKey(activationKey);
            if (!deviceIdFromAK.equalsIgnoreCase(newDeviceId)) {
               this.setSlmLicenseHistoryForE2E("E02", newDeviceId, licenseKey, issudNo, "Device ID is different from Activation key. Device ID = " + newDeviceId, true);
               throw new Exception("FAIL - Device id is differnet");
            } else {
               int result = this.activationActiveForE2E(licenseKey, activationKey, newDeviceId, issudNo);
               if (result != 0) {
                  this.setSlmLicenseHistoryForE2E("E02", newDeviceId, licenseKey, issudNo, "Active activation fail code = " + result + " Device ID = " + newDeviceId, true);
                  throw new Exception("FAIL - Active activation fail code = " + result);
               } else {
                  return true;
               }
            }
         }
      } else {
         this.setSlmLicenseHistoryForE2E("E02", newDeviceId, licenseKey, issudNo, "Get Activation Key Fail ErrCode" + activationJSON.getString("resultCode") + "  Msg : " + activationJSON.getString("resultMessage") + " Device ID = " + newDeviceId, true);
         throw new Exception("FAIL - Get Activation Key Fail ErrCode" + activationJSON.getString("resultCode") + "Msg : " + activationJSON.getString("resultMessage"));
      }
   }

   public CompanyInfoEntity getCompanyInfo() throws Exception {
      return this.licenseDao.getCompanyInfo();
   }

   public boolean insertCompanyInfo(CompanyInfoEntity companyInfoEntity) throws Exception {
      return this.licenseDao.insertCompanyInfo(companyInfoEntity);
   }

   public boolean modifyDeviceLocationProcessForE2E(String deviceId, String location) throws Exception {
      SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
      SlmLicenseEntity slmLicenseEntity = this.licenseDao.getSlmLicenseForE2EByDeviceId(deviceId);
      String licenseKey = slmLicenseEntity.getLicense_key();
      if (slmLicenseEntity == null) {
         this.setSlmLicenseHistoryForE2E("E02", deviceId, "", "", "No license Information. Cannot modify device location.", true);
         return false;
      } else {
         JSONObject activationJSON = slmMgr.requestModifyLocation("MI-CLOUD", licenseKey, deviceId, location);
         if (activationJSON.getString("resultCode").equalsIgnoreCase("00")) {
            this.setSlmLicenseHistoryForE2E("E00", deviceId, licenseKey, "", "requestModifyLocation success", true);
            return true;
         } else {
            this.setSlmLicenseHistoryForE2E("E02", deviceId, licenseKey, "", "Modify device location Fail ErrCode" + activationJSON.getString("resultCode") + "  Msg : " + activationJSON.getString("resultMessage") + " Device ID = " + deviceId, true);
            return false;
         }
      }
   }

   public boolean modifyDeviceLocationProcessForE2E_SLMDirect(String deviceId, String location) throws Exception {
      SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
      SlmLicenseEntity slmLicenseEntity = this.licenseDao.getSlmLicenseForE2EByDeviceId(deviceId);
      String licenseKey = slmLicenseEntity.getLicense_key();
      if (slmLicenseEntity == null) {
         this.setSlmLicenseHistoryForE2E("E02", deviceId, "", "", "No license Information. Cannot modify device location.", true);
         return false;
      } else {
         CompanyInfoEntity company = null;
         company = this.licenseDao.getCompanyInfo();
         SiteInformation siteInformation = new SiteInformation();
         siteInformation.setCompanyName(company.getCompany_name());
         siteInformation.setCompanyaddress(company.getAddress());
         siteInformation.setDeptName(company.getDivision());
         siteInformation.setEmailAddress(company.getEmail());
         siteInformation.setTelephone(company.getPhone());
         JSONObject activationJSON = slmMgr.requestModifyLocation_SLMDirect("MI-CLOUD", licenseKey, siteInformation, location);
         if (activationJSON.getString("resultCode").equalsIgnoreCase("00")) {
            this.setSlmLicenseHistoryForE2E("E00", deviceId, licenseKey, "", "requestModifyLocation success", true);
            return true;
         } else {
            this.setSlmLicenseHistoryForE2E("E02", deviceId, licenseKey, "", "Modify device location Fail ErrCode" + activationJSON.getString("resultCode") + "  Msg : " + activationJSON.getString("resultMessage") + " Device ID = " + deviceId, true);
            return false;
         }
      }
   }

   public boolean hasOldLicense() throws Exception {
      String[] oldLicenses = new String[]{"010120", "010121", "010V31", "01014A"};
      List licenses = this.getAllSlmLicense();
      if (licenses != null) {
         Iterator var3 = licenses.iterator();

         while(var3.hasNext()) {
            SlmLicenseEntity entity = (SlmLicenseEntity)var3.next();
            if (Arrays.asList(oldLicenses).contains(entity.getProduct_code())) {
               return true;
            }
         }
      }

      return false;
   }

   public long hasMigrationLicense() throws Exception {
      List licenses = this.getAllSlmLicense();
      if (licenses != null) {
         Iterator var2 = licenses.iterator();

         while(var2.hasNext()) {
            SlmLicenseEntity entity = (SlmLicenseEntity)var2.next();
            if ("01010M".equals(entity.getProduct_code())) {
               if (entity.getEnd_date() == null) {
                  return 0L;
               }

               return entity.getEnd_date().getTime();
            }
         }
      }

      return -1L;
   }

   public void activationProcessForE2E_SLMDirect(String selDeviceId, String soldToCode, String modelCd, String secorgId, String location, String deviceName) throws Exception {
      SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
      CompanyInfoEntity company = null;
      company = this.licenseDao.getCompanyInfo();
      if (company == null) {
         this.setSlmLicenseHistoryForE2E("E02", selDeviceId, "", "", "No Company Information.", true);
         throw new Exception("No Company Information. Insert Company Information");
      } else {
         SiteInformation siteInformation = new SiteInformation();
         siteInformation.setCompanyName(company.getCompany_name());
         siteInformation.setCompanyaddress(company.getAddress());
         siteInformation.setDeptName(company.getDivision());
         siteInformation.setEmailAddress(company.getEmail());
         siteInformation.setTelephone(company.getPhone());
         JSONObject newLicenseJSON = null;

         try {
            newLicenseJSON = slmMgr.requestNewLicenseKey_SLMDirect("MI-CLOUD", soldToCode, company.getDomain_name(), secorgId, "01", location, modelCd, 1);
         } catch (Exception var20) {
            throw var20;
         }

         if (newLicenseJSON.getString("resultCode").equalsIgnoreCase("00")) {
            String licenseKey = newLicenseJSON.getString("licenseKey");
            String issudNo = newLicenseJSON.getString("issudNo");
            this.setSlmLicenseHistoryForE2E("E00", selDeviceId, licenseKey, issudNo, "requestNewLicenseKey success", true);
            int rtn = slmMgr.sdkLicenseKeyVerify(licenseKey);
            if (rtn != 0) {
               this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "licenseKey verify fail! code : " + rtn + " Device Id = " + selDeviceId, true);
               throw new Exception("FAIL - licenseKey verify fail!" + licenseKey);
            } else if (this.licenseKeyDuplicateForE2E(licenseKey) != 0) {
               int rtn = 900;
               this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "licenseKey duplicate! code : " + rtn + " Device ID = " + selDeviceId, true);
               throw new Exception("FAIL - licenseKey duplicate!" + licenseKey);
            } else {
               JSONObject activationJSON = null;

               try {
                  activationJSON = slmMgr.requestNewActivation_SLMDirect("MI-CLOUD", issudNo, licenseKey, "01", selDeviceId, siteInformation, location, deviceName);
               } catch (Exception var19) {
                  throw var19;
               }

               if (activationJSON.getString("resultCode").equalsIgnoreCase("00")) {
                  String activationKey = activationJSON.getString("activaKey");
                  SlmLicenseEntity licenseEntity = this.getSlmLicenseEntity(activationKey);
                  this.setSlmLicenseHistoryForE2E("E00", selDeviceId, licenseKey, issudNo, "requestNewActivation success", true);
                  if (!licenseEntity.getLicense_key().equals(licenseKey)) {
                     this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "licenseKey is different from Activation Key. Device ID = " + selDeviceId, true);
                     throw new Exception("FAIL - LicenseKey is different");
                  } else if (licenseEntity.getMax_clients() != 1L) {
                     this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "Client Number is not 1. Device ID = " + selDeviceId, true);
                     throw new Exception("FAIL - Client number is not 1");
                  } else {
                     String deviceIdFromAK = this.getHWUniqueKeyFromActivationKey(activationKey);
                     if (!deviceIdFromAK.equalsIgnoreCase(selDeviceId)) {
                        this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "Device ID is different from Activation key. Device ID = " + selDeviceId, true);
                        throw new Exception("FAIL - Device id is differnet");
                     } else {
                        int result = this.activationActiveForE2E(licenseKey, activationKey, selDeviceId, issudNo);
                        if (result != 0) {
                           this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "Active activation fail code = " + result + " Device ID = " + selDeviceId, true);
                           throw new Exception("FAIL - Active activation fail code = " + result);
                        }
                     }
                  }
               } else {
                  this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "Get Activation Key Fail ErrCode" + activationJSON.getString("resultCode") + "  Msg : " + activationJSON.getString("resultMessage") + " Device ID = " + selDeviceId, true);
                  throw new Exception("FAIL - Get Activation Key Fail ErrCode" + activationJSON.getString("resultCode") + "Msg : " + activationJSON.getString("resultMessage"));
               }
            }
         } else {
            this.setSlmLicenseHistoryForE2E("E02", selDeviceId, "", "", "Get LicenseKey Fail ErrCode" + newLicenseJSON.getString("resultCode") + "  Msg : " + newLicenseJSON.getString("resultMessage") + " Device ID = " + selDeviceId, true);
            throw new Exception("FAIL - Get LicenseKey Fail ErrCode" + newLicenseJSON.getString("resultCode") + "Msg : " + newLicenseJSON.getString("resultMessage"));
         }
      }
   }

   public boolean reActivationProcessForE2E_SLMDirect(String selDeviceId) throws Exception {
      SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
      SlmLicenseEntity slmLicenseEntity = this.licenseDao.getSlmLicenseForE2EByDeviceId(selDeviceId);
      if (slmLicenseEntity == null) {
         this.setSlmLicenseHistoryForE2E("E02", selDeviceId, "", "", "No license Information. Cannot extend license period.", true);
         return false;
      } else {
         String licenseKey = slmLicenseEntity.getLicense_key();
         String issudNo = slmLicenseEntity.getIssued_no();
         JSONObject activationJSON = null;

         try {
            activationJSON = slmMgr.requestReActivation_SLMDirect("MI-CLOUD", issudNo, licenseKey, "01", selDeviceId);
         } catch (Exception var11) {
            throw var11;
         }

         if (activationJSON.getString("resultCode").equalsIgnoreCase("00")) {
            String activationKey = activationJSON.getString("activaKey");
            SlmLicenseEntity licenseEntity = this.getSlmLicenseEntity(activationKey);
            this.setSlmLicenseHistoryForE2E("E00", selDeviceId, licenseKey, issudNo, "requestReActivation success", true);
            if (!licenseEntity.getLicense_key().equals(licenseKey)) {
               this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "licenseKey is different from Activation Key. Device ID = " + selDeviceId, true);
               throw new Exception("FAIL - LicenseKey is different");
            } else if (licenseEntity.getMax_clients() != 1L) {
               this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "Client Number is not 1. Device ID = " + selDeviceId, true);
               throw new Exception("FAIL - Client number is not 1");
            } else {
               String deviceIdFromAK = this.getHWUniqueKeyFromActivationKey(activationKey);
               if (!deviceIdFromAK.equalsIgnoreCase(selDeviceId)) {
                  this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "Device ID is different from Activation key. Device ID = " + selDeviceId, true);
                  throw new Exception("FAIL - Device id is differnet");
               } else {
                  int result = this.reActivationActiveForE2E(licenseKey, activationKey, selDeviceId, issudNo);
                  if (result != 0) {
                     this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "Active reactivation fail code = " + result + " Device ID = " + selDeviceId, true);
                     throw new Exception("FAIL - Active activation fail code = " + result);
                  } else {
                     return true;
                  }
               }
            }
         } else {
            this.setSlmLicenseHistoryForE2E("E02", selDeviceId, licenseKey, issudNo, "Get reActivation Key Fail ErrCode" + activationJSON.getString("resultCode") + "  Msg : " + activationJSON.getString("resultMessage") + " Device ID = " + selDeviceId, true);
            throw new Exception("FAIL - Get Activation Key Fail ErrCode" + activationJSON.getString("resultCode") + "Msg : " + activationJSON.getString("resultMessage"));
         }
      }
   }

   public boolean deActivationProcessForE2E_SLMDirect(String deviceId) throws Exception {
      SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
      SlmLicenseEntity slmLicenseEntity = this.licenseDao.getSlmLicenseForE2EByDeviceId(deviceId);
      JSONObject deActivationJSON = null;

      try {
         deActivationJSON = slmMgr.requestDeActivation_SLMDirect("MI-CLOUD", slmLicenseEntity.getIssued_no(), slmLicenseEntity.getLicense_key(), "01", deviceId);
      } catch (Exception var6) {
         throw var6;
      }

      if (deActivationJSON.getString("resultCode").equalsIgnoreCase("00")) {
         this.setSlmLicenseHistoryForE2E("E00", deviceId, slmLicenseEntity.getLicense_key(), slmLicenseEntity.getIssued_no(), "requestDeActivation success", true);
         return true;
      } else {
         this.setSlmLicenseHistoryForE2E("E02", deviceId, slmLicenseEntity.getLicense_key(), slmLicenseEntity.getIssued_no(), "DeActivae Fail ErrCode" + deActivationJSON.getString("resultCode") + "  Msg : " + deActivationJSON.getString("resultMessage") + " Device ID = " + deviceId, true);
         return false;
      }
   }

   public boolean swapProcessForE2E_SLMDirect(SlmLicenseEntity oldslmLicenseEntity, String newDeviceId, String location, String deviceName) throws Exception {
      SlmSdkManager slmMgr = SlmSdkManagerImpl.getInstance();
      String issudNo = oldslmLicenseEntity.getIssued_no();
      String licenseKey = oldslmLicenseEntity.getLicense_key();
      CompanyInfoEntity company = null;
      company = this.licenseDao.getCompanyInfo();
      if (company == null) {
         this.setSlmLicenseHistoryForE2E("E02", newDeviceId, "", "", "No Company Information.", true);
         throw new Exception("No Company Information. Insert Company Information");
      } else {
         SiteInformation siteInformation = new SiteInformation();
         siteInformation.setCompanyName(company.getCompany_name());
         siteInformation.setCompanyaddress(company.getAddress());
         siteInformation.setDeptName(company.getDivision());
         siteInformation.setEmailAddress(company.getEmail());
         siteInformation.setTelephone(company.getPhone());
         JSONObject deActivationJSON = null;

         try {
            deActivationJSON = slmMgr.requestDeActivation_SLMDirect("MI-CLOUD", oldslmLicenseEntity.getIssued_no(), oldslmLicenseEntity.getLicense_key(), "01", oldslmLicenseEntity.getDevice_id());
         } catch (Exception var17) {
            throw var17;
         }

         if (deActivationJSON.getString("resultCode").equalsIgnoreCase("00")) {
            this.setSlmLicenseHistoryForE2E("E00", oldslmLicenseEntity.getDevice_id(), oldslmLicenseEntity.getLicense_key(), oldslmLicenseEntity.getIssued_no(), "requestDeActivation success", true);
            JSONObject activationJSON = null;

            try {
               activationJSON = slmMgr.requestNewActivation_SLMDirect("MI-CLOUD", issudNo, licenseKey, "01", newDeviceId, siteInformation, location, deviceName);
            } catch (Exception var16) {
               throw var16;
            }

            if (activationJSON.getString("resultCode").equalsIgnoreCase("00")) {
               String activationKey = activationJSON.getString("activaKey");
               SlmLicenseEntity licenseEntity = this.getSlmLicenseEntity(activationKey);
               this.setSlmLicenseHistoryForE2E("E00", newDeviceId, licenseKey, issudNo, "requestNewActivation success", true);
               if (licenseEntity.getMax_clients() != 1L) {
                  this.setSlmLicenseHistoryForE2E("E02", newDeviceId, licenseKey, issudNo, "Client Number is not 1. Device ID = " + newDeviceId, true);
                  throw new Exception("FAIL - Client number is not 1");
               } else {
                  String deviceIdFromAK = this.getHWUniqueKeyFromActivationKey(activationKey);
                  if (!deviceIdFromAK.equalsIgnoreCase(newDeviceId)) {
                     this.setSlmLicenseHistoryForE2E("E02", newDeviceId, licenseKey, issudNo, "Device ID is different from Activation key. Device ID = " + newDeviceId, true);
                     throw new Exception("FAIL - Device id is differnet");
                  } else {
                     int result = this.activationActiveForE2E(licenseKey, activationKey, newDeviceId, issudNo);
                     if (result != 0) {
                        this.setSlmLicenseHistoryForE2E("E02", newDeviceId, licenseKey, issudNo, "Active activation fail code = " + result + " Device ID = " + newDeviceId, true);
                        throw new Exception("FAIL - Active activation fail code = " + result);
                     } else {
                        return true;
                     }
                  }
               }
            } else {
               this.setSlmLicenseHistoryForE2E("E02", newDeviceId, licenseKey, issudNo, "Get Activation Key Fail ErrCode" + activationJSON.getString("resultCode") + "  Msg : " + activationJSON.getString("resultMessage") + " Device ID = " + newDeviceId, true);
               throw new Exception("FAIL - Get Activation Key Fail ErrCode" + activationJSON.getString("resultCode") + "Msg : " + activationJSON.getString("resultMessage"));
            }
         } else {
            this.setSlmLicenseHistoryForE2E("E02", oldslmLicenseEntity.getDevice_id(), oldslmLicenseEntity.getLicense_key(), oldslmLicenseEntity.getIssued_no(), "DeActivae Fail ErrCode" + deActivationJSON.getString("resultCode") + "  Msg : " + deActivationJSON.getString("resultMessage") + " Device ID = " + oldslmLicenseEntity.getDevice_id(), true);
            throw new Exception("Get DeActivation Fail ErrCode" + deActivationJSON.getString("resultCode") + "Msg : " + deActivationJSON.getString("resultMessage"));
         }
      }
   }
}
