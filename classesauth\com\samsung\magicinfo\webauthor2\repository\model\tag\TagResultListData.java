package com.samsung.magicinfo.webauthor2.repository.model.tag;

import java.io.Serializable;
import java.util.List;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;

public class TagResultListData implements Serializable {
  @XmlElement
  private Integer totalCount;
  
  @XmlElementWrapper(name = "resultList")
  @XmlElement(name = "TagEntity")
  private List<TagData> resultList;
  
  public Integer getTotalCount() {
    return this.totalCount;
  }
  
  public List<TagData> getResultList() {
    return this.resultList;
  }
}
