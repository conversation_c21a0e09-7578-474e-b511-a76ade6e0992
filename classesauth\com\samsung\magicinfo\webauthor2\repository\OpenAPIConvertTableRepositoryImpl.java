package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.exception.repository.CannotAddConvertTableException;
import com.samsung.magicinfo.webauthor2.exception.repository.CannotUpdateConvertTableException;
import com.samsung.magicinfo.webauthor2.repository.model.datalink.ConvertTableData;
import com.samsung.magicinfo.webauthor2.util.JaxbUtil;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.util.List;
import javax.inject.Inject;
import javax.xml.bind.JAXBException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.web.client.RestTemplate;

@Repository
public class OpenAPIConvertTableRepositoryImpl implements OpenAPIConvertTableRepository {
  private static final Logger logger = LoggerFactory.getLogger(OpenAPIConvertTableRepository.class);
  
  private RestTemplate restTemplate;
  
  private UserData userData;
  
  private JaxbUtil jaxbUtil;
  
  @Inject
  public OpenAPIConvertTableRepositoryImpl(RestTemplate restTemplate, UserData userData, JaxbUtil jaxbUtil) {
    this.restTemplate = restTemplate;
    this.userData = userData;
    this.jaxbUtil = jaxbUtil;
  }
  
  public List<ConvertTableData> getConvertTableDataList() {
    GetConvertTableListOpenApiMethod openApiMethod = new GetConvertTableListOpenApiMethod(this.restTemplate, this.userData.getUserId(), this.userData.getToken());
    return openApiMethod.callMethod();
  }
  
  public String addConvertTableData(ConvertTableData convertTableData) {
    AddConvertTableOpenApiMethod openApiMethod = new AddConvertTableOpenApiMethod(this.restTemplate, this.userData.getUserId(), this.userData.getToken(), convertTableData, this.jaxbUtil);
    try {
      return openApiMethod.callPostMethod();
    } catch (JAXBException e) {
      logger.error(e.getMessage());
      throw new CannotAddConvertTableException("999", e.getMessage());
    } 
  }
  
  public String deleteConvertTable(String tableName) {
    DeleteConvertTableOpenApiMethod openApiMethod = new DeleteConvertTableOpenApiMethod(this.restTemplate, this.userData.getToken(), tableName);
    return openApiMethod.callMethod();
  }
  
  public String modifyConvertTableData(ConvertTableData oldConvertTableData, ConvertTableData newConvertTableData) {
    ModifyConvertTableOpenApiMethod openApiMethod = new ModifyConvertTableOpenApiMethod(this.restTemplate, this.userData.getUserId(), this.userData.getToken(), oldConvertTableData, newConvertTableData, this.jaxbUtil);
    try {
      return openApiMethod.callPostMethod();
    } catch (JAXBException e) {
      logger.error(e.getMessage());
      throw new CannotUpdateConvertTableException("999", e.getMessage());
    } 
  }
}
