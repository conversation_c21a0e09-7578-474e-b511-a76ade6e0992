package com.samsung.magicinfo.framework.setup.manager;

import com.samsung.magicinfo.framework.setup.entity.CategoryEntity;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

public interface CategoryInfo {
   List getCategoryWithPgroupId(long var1) throws SQLException;

   List getCategoryWithPgroupId(long var1, String var3) throws SQLException;

   CategoryEntity getCategory(long var1) throws SQLException;

   long addCategory(CategoryEntity var1) throws SQLException;

   boolean deleteCategory(long var1) throws SQLException;

   Map getCategoryTag(String var1) throws SQLException;

   List getCategoryWithContentId(String var1) throws SQLException;

   List getCategoryWithPlaylistId(String var1) throws SQLException;

   void setCategoryFromContentId(String var1, String var2) throws SQLException;

   boolean deleteCategoryFromContentId(String var1) throws SQLException;

   boolean moveCategory(long var1, long var3) throws SQLException;

   void setCategoryFromPlaylistId(String var1, String var2) throws SQLException;

   boolean deleteCategoryFromPlaylistId(String var1) throws SQLException;

   boolean updateCategory(CategoryEntity var1) throws SQLException;

   List getCategoryByMultipleOrg(List var1) throws SQLException;

   void setCategoryFromPlaylistId(List var1, String var2) throws SQLException;
}
