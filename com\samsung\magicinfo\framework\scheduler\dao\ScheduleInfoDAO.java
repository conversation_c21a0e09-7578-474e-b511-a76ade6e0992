package com.samsung.magicinfo.framework.scheduler.dao;

import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DaoTools;
import com.samsung.common.utils.SequenceDB;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceGroupDao;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.scheduler.constants.ScheduleConstants;
import com.samsung.magicinfo.framework.scheduler.entity.AdScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.AdSlotEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ChannelEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.DetailDownloadContentEntity;
import com.samsung.magicinfo.framework.scheduler.entity.DynamicTagEntity;
import com.samsung.magicinfo.framework.scheduler.entity.FrameEntity;
import com.samsung.magicinfo.framework.scheduler.entity.FrameTemplateEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.SyncSchedule;
import com.samsung.magicinfo.framework.scheduler.manager.ProgramGroupInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ProgramGroupInfoImpl;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class ScheduleInfoDAO extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(ScheduleInfoDAO.class);

   public ScheduleInfoDAO() {
      super();
   }

   public ScheduleInfoDAO(SqlSession session) {
      super(session);
   }

   public boolean addFrame(FrameEntity frame) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var3;
      try {
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert_addFrame(frame) == 1) {
            String userGroupIds = frame.getUser_group_ids();
            if (StringUtils.isNotBlank(userGroupIds)) {
               String[] var4 = userGroupIds.split(",");
               int var5 = var4.length;

               for(int var6 = 0; var6 < var5; ++var6) {
                  String userGroupId = var4[var6];
                  if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert2_addFrame(frame, Long.parseLong(userGroupId)) != 1) {
                     session.rollback();
                     boolean var8 = false;
                     return var8;
                  }
               }
            }

            session.commit();
            boolean var15 = true;
            return var15;
         }

         session.rollback();
         var3 = false;
      } catch (SQLException var12) {
         session.rollback();
         throw var12;
      } finally {
         session.close();
      }

      return var3;
   }

   public boolean addDeviceGroupMappedInProgramTemp(String program_id, String device_group_id, String device_groups, String default_content, String bgm_content_id) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var8;
      try {
         String[] dGroupIdsArray = device_group_id.split(",");
         String[] dGroupNamesArray = device_groups.split(",");

         for(int i = 0; i < dGroupIdsArray.length; ++i) {
            long groupId = Long.parseLong(dGroupIdsArray[i].trim());
            String groupName = dGroupNamesArray[i].trim();
            ((ScheduleInfoDAOMapper)this.getMapper(session)).addDeviceGroupMappedInProgramTemp(program_id, groupId, groupName, default_content, bgm_content_id);
         }

         session.commit();
         boolean var21 = true;
         return var21;
      } catch (SQLException var17) {
         session.rollback();
         throw var17;
      } catch (Exception var18) {
         session.rollback();
         var8 = false;
      } finally {
         session.close();
      }

      return var8;
   }

   public boolean addDeviceGroupMappedInProgram(String program_id, String device_group_id) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var5;
      try {
         long deviceGroupId = Long.parseLong(device_group_id);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_addProgram(deviceGroupId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).addDeviceGroupMappedInProgram(program_id, deviceGroupId);
         session.commit();
         boolean var6 = true;
         return var6;
      } catch (SQLException var11) {
         session.rollback();
         throw var11;
      } catch (Exception var12) {
         session.rollback();
         var5 = false;
      } finally {
         session.close();
      }

      return var5;
   }

   public boolean deleteDeviceGroupMappedInProgramTempByProgramId(String program_id) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var3;
      try {
         ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteDeviceGroupMappedInProgramTempByProgramId(program_id);
         session.commit();
         var3 = true;
      } catch (SQLException var7) {
         session.rollback();
         throw var7;
      } finally {
         session.close();
      }

      return var3;
   }

   public boolean deleteDeviceGroupMappedInProgramByProgramId(String program_id) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var3;
      try {
         ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteDeviceGroupMappedInProgramByProgramId(program_id);
         session.commit();
         var3 = true;
      } catch (SQLException var7) {
         session.rollback();
         throw var7;
      } finally {
         session.close();
      }

      return var3;
   }

   public boolean deleteDeviceGroupMappingByDeviceGroupId(Long groupId) throws SQLException {
      return this.deleteDeviceGroupMappingByDeviceGroupId((SqlSession)null, groupId);
   }

   public boolean deleteDeviceGroupMappingByDeviceGroupId(SqlSession sqlSession, Long groupId) throws SQLException {
      if (sqlSession == null) {
         sqlSession = this.openNewSession(false);
      }

      ((ScheduleInfoDAOMapper)this.getMapper(sqlSession)).delete2_addProgramWithBasicInformation(groupId);
      return true;
   }

   public List getDeviceGroupMappedInProgramTemp(String program_id) throws SQLException {
      try {
         return ((ScheduleInfoDAOMapper)this.getMapper()).getDeviceGroupMappedInProgramTemp(program_id);
      } catch (SQLException var3) {
         throw var3;
      }
   }

   public List getDeviceGroupMappedInProgram(String program_id) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getDeviceGroupMappedInProgram(program_id);
   }

   public boolean updateDeviceGroupMappedInProgramAsDefault(String group_id) throws SQLException {
      ((ScheduleInfoDAOMapper)this.getMapper()).updateDeviceGroupMappedInProgramAsDefault("dd328c1a-19ab-4bd8-a604-5073dadd1383", Long.valueOf(group_id));
      return true;
   }

   public boolean updateDeviceGroupMappedInProgramAsDefaultByPid(String program_id) throws SQLException {
      ((ScheduleInfoDAOMapper)this.getMapper()).updateDeviceGroupMappedInProgramAsDefaultByPid(Long.valueOf(program_id), "dd328c1a-19ab-4bd8-a604-5073dadd1383");
      return true;
   }

   public boolean addNewVersionProgram(ProgramEntity program, String sessionId, String ipAddress) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var5;
      try {
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert_addProgram(program) == 1) {
            if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert2_addProgram(program) != 1) {
               session.rollback();
               var5 = false;
               return var5;
            }

            String deviceGroupIds = program.getDevice_group_ids();
            int i;
            boolean var38;
            if (StringUtils.isNotBlank(deviceGroupIds)) {
               String[] var26 = deviceGroupIds.split(",");
               i = var26.length;

               for(int var8 = 0; var8 < i; ++var8) {
                  String deviceId = var26[var8];
                  Long deviceIdL = Long.parseLong(deviceId);
                  Map map = ((ScheduleInfoDAOMapper)this.getMapper(session)).select_addProgram(deviceIdL);
                  if (map != null) {
                     String defaultProgramId = (String)map.get("default_program_id");
                     String programId = (String)map.get("program_id");
                     if (defaultProgramId != null && programId != null && defaultProgramId.equals(programId)) {
                        ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_addProgram(defaultProgramId);
                        if (((ScheduleInfoDAOMapper)this.getMapper(session)).update_addProgram(defaultProgramId) != 1) {
                           session.rollback();
                           boolean var14 = false;
                           return var14;
                        }
                     }
                  }

                  ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_addProgram(deviceIdL);
                  if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert3_addProgram(program.getProgram_id(), deviceIdL) != 1) {
                     session.rollback();
                     var38 = false;
                     return var38;
                  }
               }
            }

            if (program != null) {
               List channelList = program.getChannelList();
               if (channelList != null) {
                  for(i = 0; i < channelList.size(); ++i) {
                     ChannelEntity channel = (ChannelEntity)channelList.get(i);
                     if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert7_addProgramWithBasicInformation(channel) != 1) {
                        session.rollback();
                        session.close();
                        boolean var33 = false;
                        return var33;
                     }

                     List frameList = channel.getFrameList();
                     if (frameList != null) {
                        for(int j = 0; j < frameList.size(); ++j) {
                           FrameEntity frame = (FrameEntity)frameList.get(j);
                           if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert4_addProgramWithBasicInformation(frame) != 1) {
                              session.rollback();
                              session.close();
                              var38 = false;
                              return var38;
                           }

                           String[] frameAuthority = frame.getAuthority();
                           int k;
                           if (frameAuthority != null && !frameAuthority.equals("")) {
                              String[] var40 = frameAuthority;
                              k = frameAuthority.length;

                              for(int var15 = 0; var15 < k; ++var15) {
                                 String groupIdStr = var40[var15];
                                 if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert5_addProgramWithBasicInformation(frame, Long.parseLong(groupIdStr)) != 1) {
                                    session.rollback();
                                    session.close();
                                    boolean var17 = false;
                                    return var17;
                                 }
                              }
                           }

                           List scheduleList = frame.getScheduleList();
                           if (scheduleList != null) {
                              for(k = 0; k < scheduleList.size(); ++k) {
                                 ContentsScheduleEntity schedule = (ContentsScheduleEntity)scheduleList.get(k);
                                 if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert6_addProgramWithFrameAndHWControlAndContent(schedule, schedule.getPriority()) != 1) {
                                    session.rollback();
                                    boolean var44 = false;
                                    return var44;
                                 }
                              }
                           }
                        }
                     }
                  }
               }
            }

            ((ScheduleInfoDAOMapper)this.getMapper(session)).insert6_addProgram(program.getProgram_id(), sessionId);
            long logId = (long)SequenceDB.getNextValue("MI_CDS_LOG_PROGRAM");
            boolean var31;
            if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert7_addProgram(logId, program, "00", ipAddress) != 1) {
               session.rollback();
               var31 = false;
               return var31;
            }

            if (program.getUse_sync_play() != null && program.getUse_sync_play().equals("Y")) {
               List scheduleIdList = ((ScheduleInfoDAOMapper)this.getMapper(session)).getScheduleIdListByProgramId(program.getProgram_id());

               for(int idx = 0; idx < scheduleIdList.size(); ++idx) {
                  String scheduleId = (String)scheduleIdList.get(idx);
                  ((ScheduleInfoDAOMapper)this.getMapper(session)).addDynaminTagInfo(scheduleId, sessionId);
               }
            }

            session.commit();
            var31 = true;
            return var31;
         }

         session.rollback();
         var5 = false;
      } catch (SQLException var22) {
         session.rollback();
         throw var22;
      } catch (Exception var23) {
         session.rollback();
         boolean var6 = false;
         return var6;
      } finally {
         session.close();
      }

      return var5;
   }

   public boolean addNewADProgram(ProgramEntity program, String sessionId, String ipAddress) throws SQLException {
      SqlSession session = this.openNewSession(false);

      try {
         boolean var26;
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert_addProgram(program) != 1) {
            session.rollback();
            var26 = false;
            return var26;
         } else if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert2_addProgram(program) != 1) {
            session.rollback();
            var26 = false;
            return var26;
         } else {
            String deviceGroupIds = program.getDevice_group_ids();
            int i;
            boolean var36;
            if (StringUtils.isNotBlank(deviceGroupIds)) {
               String[] var27 = deviceGroupIds.split(",");
               i = var27.length;

               for(int var8 = 0; var8 < i; ++var8) {
                  String deviceId = var27[var8];
                  Long deviceIdL = Long.parseLong(deviceId);
                  Map map = ((ScheduleInfoDAOMapper)this.getMapper(session)).select_addProgram(deviceIdL);
                  if (map != null) {
                     String defaultProgramId = (String)map.get("default_program_id");
                     String programId = (String)map.get("program_id");
                     if (defaultProgramId != null && programId != null && defaultProgramId.equals(programId)) {
                        ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_addProgram(defaultProgramId);
                        if (((ScheduleInfoDAOMapper)this.getMapper(session)).update_addProgram(defaultProgramId) != 1) {
                           session.rollback();
                           boolean var14 = false;
                           return var14;
                        }
                     }
                  }

                  ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_addProgram(deviceIdL);
                  if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert3_addProgram(program.getProgram_id(), deviceIdL) != 1) {
                     session.rollback();
                     var36 = false;
                     return var36;
                  }
               }
            }

            if (program != null) {
               List channelList = program.getChannelList();
               if (channelList != null) {
                  for(i = 0; i < channelList.size(); ++i) {
                     ChannelEntity channel = (ChannelEntity)channelList.get(i);
                     if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert7_addProgramWithBasicInformation(channel) != 1) {
                        session.rollback();
                        session.close();
                        boolean var33 = false;
                        return var33;
                     }

                     List frameList = channel.getFrameList();
                     if (frameList != null) {
                        label323:
                        for(int j = 0; j < frameList.size(); ++j) {
                           FrameEntity frame = (FrameEntity)frameList.get(j);
                           if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert4_addProgramWithBasicInformation(frame) != 1) {
                              session.rollback();
                              session.close();
                              var36 = false;
                              return var36;
                           }

                           List slotList = frame.getSlotList();
                           if (slotList != null) {
                              Iterator var38 = slotList.iterator();

                              while(true) {
                                 List scheduleList;
                                 do {
                                    if (!var38.hasNext()) {
                                       continue label323;
                                    }

                                    AdSlotEntity slot = (AdSlotEntity)var38.next();
                                    if (!((ScheduleInfoDAOMapper)this.getMapper(session)).insertSlot(slot)) {
                                       session.rollback();
                                       session.close();
                                       boolean var40 = false;
                                       return var40;
                                    }

                                    scheduleList = slot.getScheduleList();
                                 } while(scheduleList == null);

                                 Iterator var16 = scheduleList.iterator();

                                 while(var16.hasNext()) {
                                    AdScheduleEntity scheudle = (AdScheduleEntity)var16.next();
                                    if (!((ScheduleInfoDAOMapper)this.getMapper(session)).insertAdSchedule(scheudle)) {
                                       session.rollback();
                                       session.close();
                                       boolean var18 = false;
                                       return var18;
                                    }
                                 }
                              }
                           }
                        }
                     }
                  }
               }
            }

            long logId = (long)SequenceDB.getNextValue("MI_CDS_LOG_PROGRAM");
            boolean var31;
            if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert7_addProgram(logId, program, "00", ipAddress) != 1) {
               session.rollback();
               var31 = false;
               return var31;
            } else {
               session.commit();
               var31 = true;
               return var31;
            }
         }
      } catch (SQLException var23) {
         this.logger.error("", var23);
         session.rollback();
         throw var23;
      } catch (Exception var24) {
         this.logger.error("", var24);
         session.rollback();
         boolean var6 = false;
         return var6;
      } finally {
         session.close();
      }
   }

   public boolean addProgram(ProgramEntity program, String sessionId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var25;
      try {
         boolean var21;
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert_addProgram(program) != 1) {
            session.rollback();
            var21 = false;
            return var21;
         }

         if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert2_addProgram(program) != 1) {
            session.rollback();
            var21 = false;
            return var21;
         }

         String deviceGroupIds = program.getDevice_group_ids();
         if (StringUtils.isNotBlank(deviceGroupIds)) {
            String[] var22 = deviceGroupIds.split(",");
            int var6 = var22.length;

            for(int var7 = 0; var7 < var6; ++var7) {
               String deviceId = var22[var7];
               Long deviceIdL = Long.parseLong(deviceId);
               Map map = ((ScheduleInfoDAOMapper)this.getMapper(session)).select_addProgram(deviceIdL);
               if (map != null) {
                  String defaultProgramId = (String)map.get("default_program_id");
                  String programId = (String)map.get("program_id");
                  if (defaultProgramId != null && programId != null && defaultProgramId.equals(programId)) {
                     ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_addProgram(defaultProgramId);
                     if (((ScheduleInfoDAOMapper)this.getMapper(session)).update_addProgram(defaultProgramId) != 1) {
                        session.rollback();
                        boolean var13 = false;
                        return var13;
                     }
                  }
               }

               ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_addProgram(deviceIdL);
               if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert3_addProgram(program.getProgram_id(), deviceIdL) != 1) {
                  session.rollback();
                  boolean var28 = false;
                  return var28;
               }
            }
         }

         ((ScheduleInfoDAOMapper)this.getMapper(session)).insert4_addProgram(program.getProgram_id(), sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).insert5_addProgram(program.getProgram_id(), sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).insert6_addProgram(program.getProgram_id(), sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).insertIntoMiCdsInfoChannel_addProgram(program.getProgram_id(), sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete3_addProgram(program.getProgram_id(), sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete4_addProgram(program.getProgram_id(), sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete5_addProgram(program.getProgram_id(), sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteFromMiCdsInfoChannelTmp(program.getProgram_id(), sessionId);
         long logId = (long)SequenceDB.getNextValue("MI_CDS_LOG_PROGRAM");
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert7_addProgram(logId, program, "00", "undefined") != 1) {
            session.rollback();
            var25 = false;
            return var25;
         }

         if (program.getUse_sync_play() != null && program.getUse_sync_play().equals("Y")) {
            List scheduleIdList = ((ScheduleInfoDAOMapper)this.getMapper(session)).getScheduleIdListByProgramId(program.getProgram_id());

            for(int idx = 0; idx < scheduleIdList.size(); ++idx) {
               String scheduleId = (String)scheduleIdList.get(idx);
               ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteDynaminTagInfo(scheduleId);
               ((ScheduleInfoDAOMapper)this.getMapper(session)).addDynaminTagInfo(scheduleId, sessionId);
               ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteDynaminTagInfoTemp(scheduleId);
            }
         }

         session.commit();
         var25 = true;
      } catch (SQLException var18) {
         session.rollback();
         throw var18;
      } catch (Exception var19) {
         session.rollback();
         boolean var5 = false;
         return var5;
      } finally {
         session.close();
      }

      return var25;
   }

   public List getSyncGroupListPerSchedule(String scheduleId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getSyncGroupListPerSchedule(scheduleId);
   }

   public List getSyncDeviceIdListPerSchedule(String scheduleId, String playlistId, String syncPlayId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getSyncDeviceIdListPerSchedule(scheduleId, playlistId, syncPlayId);
   }

   public boolean addDefaultProgram(ProgramEntity program, ChannelEntity channel, FrameEntity frame, String ipAddress) throws SQLException {
      SqlSession session = this.openNewSession(false);

      try {
         boolean var21;
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert_addDefaultProgram(program) != 1) {
            session.rollback();
            var21 = false;
            return var21;
         } else if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert_miCdsInfoChannelTemp_addDefaultProgram(channel) != 1) {
            session.rollback();
            var21 = false;
            return var21;
         } else {
            String orgName = "";
            String deviceGroupIds = program.getDevice_group_ids();
            if (StringUtils.isNotBlank(deviceGroupIds)) {
               DeviceGroupDao devGrpDao = new DeviceGroupDao(session);
               String[] var9 = deviceGroupIds.split(",");
               int var10 = var9.length;

               for(int var11 = 0; var11 < var10; ++var11) {
                  String deviceGroupId = var9[var11];
                  deviceGroupId = deviceGroupId.trim();
                  orgName = devGrpDao.getOrgNameByGroupId(Long.parseLong(deviceGroupId), session);
                  ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_addDefaultProgram(Long.parseLong(deviceGroupId));
                  devGrpDao.setDefaultProgramId(Long.parseLong(deviceGroupId), program.getProgram_id(), session);
                  if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert2_addDefaultProgram(program.getProgram_id(), Long.parseLong(deviceGroupId)) != 1) {
                     session.rollback();
                     boolean var13 = false;
                     return var13;
                  }
               }
            }

            if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert3_addDefaultProgram(frame) != 1) {
               session.rollback();
               boolean var24 = false;
               return var24;
            } else {
               Long orgGroupId = ((ScheduleInfoDAOMapper)this.getMapper(session)).select_addDefaultProgram(orgName);
               if (orgGroupId != null) {
                  ((ScheduleInfoDAOMapper)this.getMapper(session)).insert4_addDefaultProgram(program.getProgram_id(), orgGroupId);
               }

               long logId = (long)SequenceDB.getNextValue("MI_CDS_LOG_PROGRAM");
               boolean var26;
               if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert5_addDefaultProgram(logId, program, "00", ipAddress) != 1) {
                  session.rollback();
                  var26 = false;
                  return var26;
               } else {
                  session.commit();
                  var26 = true;
                  return var26;
               }
            }
         }
      } catch (SQLException var18) {
         session.rollback();
         throw var18;
      } catch (Exception var19) {
         session.rollback();
         boolean var7 = false;
         return var7;
      } finally {
         session.close();
      }
   }

   public boolean updateProgram(ProgramEntity program, String sessionId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var26;
      try {
         boolean var21;
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).update_updateProgram(program) != 1) {
            session.rollback();
            var21 = false;
            return var21;
         }

         if (((ScheduleInfoDAOMapper)this.getMapper(session)).update2_updateProgram(program) != 1) {
            session.rollback();
            var21 = false;
            return var21;
         }

         String deviceGroupIds = program.getDevice_group_ids();
         int idx;
         if (StringUtils.isNotBlank(deviceGroupIds)) {
            String[] var22 = deviceGroupIds.split(",");
            idx = var22.length;

            for(int var7 = 0; var7 < idx; ++var7) {
               String deviceId = var22[var7];
               Long deviceIdL = Long.parseLong(deviceId);
               Map map = ((ScheduleInfoDAOMapper)this.getMapper(session)).select_updateProgram(deviceIdL);
               if (map != null) {
                  String defaultProgramId = (String)map.get("default_program_id");
                  String programId = (String)map.get("program_id");
                  if (defaultProgramId == null || programId == null || programId.equals("")) {
                     this.logger.error("[Content Schedule] default_program_id or programId is null of dev_group id is " + deviceId.trim());
                     this.logger.error("defaultProgramId : " + defaultProgramId);
                     this.logger.error("programId : " + programId);
                     throw new Exception();
                  }

                  if (defaultProgramId != null && programId != null && defaultProgramId.equals(programId)) {
                     ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_updateProgram(defaultProgramId);
                     if (((ScheduleInfoDAOMapper)this.getMapper(session)).update3_updateProgram(defaultProgramId) != 1) {
                        session.rollback();
                        boolean var13 = false;
                        return var13;
                     }
                  }
               }

               ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_updateProgram(deviceIdL);
               if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert_updateProgram(program.getProgram_id(), deviceIdL) != 1) {
                  session.rollback();
                  boolean var27 = false;
                  return var27;
               }
            }
         }

         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete3_updateProgram(program.getProgram_id());
         ((ScheduleInfoDAOMapper)this.getMapper(session)).insert2_updateProgram(program.getProgram_id(), sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteChannel(program.getProgram_id());
         ((ScheduleInfoDAOMapper)this.getMapper(session)).copyChannelFromTmpToOriginTable(program.getProgram_id(), sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete4_updateProgram(program.getProgram_id());
         ((ScheduleInfoDAOMapper)this.getMapper(session)).insert3_updateProgram(program.getProgram_id(), sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete5_updateProgram(program.getProgram_id());
         ((ScheduleInfoDAOMapper)this.getMapper(session)).insert4_updateProgram(program.getProgram_id(), sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteChannelTmp(program.getProgram_id(), sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete6_updateProgram(program.getProgram_id(), sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete7_updateProgram(program.getProgram_id(), sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete8_updateProgram(program.getProgram_id(), sessionId);
         if (program.getUse_sync_play() != null && program.getUse_sync_play().equals("Y")) {
            List scheduleIdList = ((ScheduleInfoDAOMapper)this.getMapper(session)).getScheduleIdListByProgramId(program.getProgram_id());

            for(idx = 0; idx < scheduleIdList.size(); ++idx) {
               String scheduleId = (String)scheduleIdList.get(idx);
               ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteDynaminTagInfo(scheduleId);
               ((ScheduleInfoDAOMapper)this.getMapper(session)).addDynaminTagInfo(scheduleId, sessionId);
               ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteDynaminTagInfoTemp(scheduleId);
            }
         }

         long logId = (long)SequenceDB.getNextValue("MI_CDS_LOG_PROGRAM");
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert5_updateProgram(logId, program, "01", "") == 1) {
            session.commit();
            var26 = true;
            return var26;
         }

         session.rollback();
         var26 = false;
      } catch (SQLException var18) {
         session.rollback();
         throw var18;
      } catch (Exception var19) {
         session.rollback();
         boolean var5 = false;
         return var5;
      } finally {
         session.close();
      }

      return var26;
   }

   public boolean updateNewVersionProgram(ProgramEntity program, String sessionId, String ipAddress) throws SQLException {
      SqlSession session = this.openNewSession(false);

      try {
         boolean var25;
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).update_updateProgram(program) != 1) {
            session.rollback();
            var25 = false;
            return var25;
         } else if (((ScheduleInfoDAOMapper)this.getMapper(session)).update2_updateProgram(program) != 1) {
            session.rollback();
            var25 = false;
            return var25;
         } else {
            String deviceGroupIds = program.getDevice_group_ids();
            ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteDeviceGroupMappedInProgramByProgramId(program.getProgram_id());
            int i;
            boolean var35;
            if (StringUtils.isNotBlank(deviceGroupIds)) {
               String[] var26 = deviceGroupIds.split(",");
               i = var26.length;

               for(int var8 = 0; var8 < i; ++var8) {
                  String deviceId = var26[var8];
                  Long deviceIdL = Long.parseLong(deviceId);
                  Map map = ((ScheduleInfoDAOMapper)this.getMapper(session)).select_updateProgram(deviceIdL);
                  if (map != null) {
                     String defaultProgramId = (String)map.get("default_program_id");
                     String programId = (String)map.get("program_id");
                     if (defaultProgramId == null || programId == null || programId.equals("")) {
                        this.logger.error("[Content Schedule] default_program_id or programId is null of dev_group id is " + deviceId.trim());
                        this.logger.error("defaultProgramId : " + defaultProgramId);
                        this.logger.error("programId : " + programId);
                        throw new Exception();
                     }

                     if (defaultProgramId != null && programId != null && defaultProgramId.equals(programId)) {
                        ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_updateProgram(defaultProgramId);
                        if (((ScheduleInfoDAOMapper)this.getMapper(session)).update3_updateProgram(defaultProgramId) != 1) {
                           session.rollback();
                           boolean var14 = false;
                           return var14;
                        }
                     }
                  }

                  ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_updateProgram(deviceIdL);
                  if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert_updateProgram(program.getProgram_id(), deviceIdL) != 1) {
                     session.rollback();
                     var35 = false;
                     return var35;
                  }
               }
            }

            ((ScheduleInfoDAOMapper)this.getMapper(session)).delete3_updateProgram(program.getProgram_id());
            ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteChannel(program.getProgram_id());
            ((ScheduleInfoDAOMapper)this.getMapper(session)).delete4_updateProgram(program.getProgram_id());
            ((ScheduleInfoDAOMapper)this.getMapper(session)).delete5_updateProgram(program.getProgram_id());
            if (program != null) {
               List channelList = program.getChannelList();
               if (channelList != null) {
                  for(i = 0; i < channelList.size(); ++i) {
                     ChannelEntity channel = (ChannelEntity)channelList.get(i);
                     if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert7_addProgramWithBasicInformation(channel) != 1) {
                        session.rollback();
                        session.close();
                        boolean var32 = false;
                        return var32;
                     }

                     List frameList = channel.getFrameList();
                     if (frameList != null) {
                        for(int j = 0; j < frameList.size(); ++j) {
                           FrameEntity frame = (FrameEntity)frameList.get(j);
                           if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert4_addProgramWithBasicInformation(frame) != 1) {
                              session.rollback();
                              session.close();
                              var35 = false;
                              return var35;
                           }

                           String[] frameAuthority = frame.getAuthority();
                           int k;
                           if (frameAuthority != null && !frameAuthority.equals("")) {
                              String[] var37 = frameAuthority;
                              k = frameAuthority.length;

                              for(int var15 = 0; var15 < k; ++var15) {
                                 String groupIdStr = var37[var15];
                                 if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert5_addProgramWithBasicInformation(frame, Long.parseLong(groupIdStr)) != 1) {
                                    session.rollback();
                                    session.close();
                                    boolean var17 = false;
                                    return var17;
                                 }
                              }
                           }

                           List scheduleList = frame.getScheduleList();
                           if (scheduleList != null) {
                              for(k = 0; k < scheduleList.size(); ++k) {
                                 ContentsScheduleEntity schedule = (ContentsScheduleEntity)scheduleList.get(k);
                                 if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert6_addProgramWithFrameAndHWControlAndContent(schedule, schedule.getPriority()) != 1) {
                                    session.rollback();
                                    boolean var41 = false;
                                    return var41;
                                 }
                              }
                           }
                        }
                     }
                  }
               }
            }

            long logId = (long)SequenceDB.getNextValue("MI_CDS_LOG_PROGRAM");
            boolean var30;
            if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert5_updateProgram(logId, program, "01", ipAddress) != 1) {
               session.rollback();
               var30 = false;
               return var30;
            } else {
               session.commit();
               var30 = true;
               return var30;
            }
         }
      } catch (SQLException var22) {
         session.rollback();
         throw var22;
      } catch (Exception var23) {
         session.rollback();
         boolean var6 = false;
         return var6;
      } finally {
         session.close();
      }
   }

   public boolean updateADNewVersionProgram(ProgramEntity program, String sessionId, String ipAddress) throws SQLException {
      SqlSession session = this.openNewSession(false);

      try {
         boolean var26;
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).update_updateProgram(program) != 1) {
            session.rollback();
            var26 = false;
            return var26;
         } else if (((ScheduleInfoDAOMapper)this.getMapper(session)).update2_updateProgram(program) != 1) {
            session.rollback();
            var26 = false;
            return var26;
         } else {
            String deviceGroupIds = program.getDevice_group_ids();
            ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteDeviceGroupMappedInProgramByProgramId(program.getProgram_id());
            int i;
            boolean var36;
            if (StringUtils.isNotBlank(deviceGroupIds)) {
               String[] var27 = deviceGroupIds.split(",");
               i = var27.length;

               for(int var8 = 0; var8 < i; ++var8) {
                  String deviceId = var27[var8];
                  Long deviceIdL = Long.parseLong(deviceId);
                  Map map = ((ScheduleInfoDAOMapper)this.getMapper(session)).select_updateProgram(deviceIdL);
                  if (map != null) {
                     String defaultProgramId = (String)map.get("default_program_id");
                     String programId = (String)map.get("program_id");
                     if (defaultProgramId == null || programId == null || programId.equals("")) {
                        this.logger.error("[Content Schedule] default_program_id or programId is null of dev_group id is " + deviceId.trim());
                        this.logger.error("defaultProgramId : " + defaultProgramId);
                        this.logger.error("programId : " + programId);
                        throw new Exception();
                     }

                     if (defaultProgramId != null && programId != null && defaultProgramId.equals(programId)) {
                        ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_updateProgram(defaultProgramId);
                        if (((ScheduleInfoDAOMapper)this.getMapper(session)).update3_updateProgram(defaultProgramId) != 1) {
                           session.rollback();
                           boolean var14 = false;
                           return var14;
                        }
                     }
                  }

                  ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_updateProgram(deviceIdL);
                  if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert_updateProgram(program.getProgram_id(), deviceIdL) != 1) {
                     session.rollback();
                     var36 = false;
                     return var36;
                  }
               }
            }

            ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteAdSlotList(program.getProgram_id());
            ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteAdScheduleList(program.getProgram_id());
            ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteChannel(program.getProgram_id());
            ((ScheduleInfoDAOMapper)this.getMapper(session)).delete4_updateProgram(program.getProgram_id());
            if (program != null) {
               List channelList = program.getChannelList();
               if (channelList != null) {
                  for(i = 0; i < channelList.size(); ++i) {
                     ChannelEntity channel = (ChannelEntity)channelList.get(i);
                     if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert7_addProgramWithBasicInformation(channel) != 1) {
                        session.rollback();
                        session.close();
                        boolean var33 = false;
                        return var33;
                     }

                     List frameList = channel.getFrameList();
                     if (frameList != null) {
                        label354:
                        for(int j = 0; j < frameList.size(); ++j) {
                           FrameEntity frame = (FrameEntity)frameList.get(j);
                           if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert4_addProgramWithBasicInformation(frame) != 1) {
                              session.rollback();
                              session.close();
                              var36 = false;
                              return var36;
                           }

                           List slotList = frame.getSlotList();
                           if (slotList != null) {
                              Iterator var38 = slotList.iterator();

                              while(true) {
                                 List scheduleList;
                                 do {
                                    if (!var38.hasNext()) {
                                       continue label354;
                                    }

                                    AdSlotEntity slot = (AdSlotEntity)var38.next();
                                    if (!((ScheduleInfoDAOMapper)this.getMapper(session)).insertSlot(slot)) {
                                       session.rollback();
                                       session.close();
                                       boolean var40 = false;
                                       return var40;
                                    }

                                    scheduleList = slot.getScheduleList();
                                 } while(scheduleList == null);

                                 Iterator var16 = scheduleList.iterator();

                                 while(var16.hasNext()) {
                                    AdScheduleEntity scheudle = (AdScheduleEntity)var16.next();
                                    if (!((ScheduleInfoDAOMapper)this.getMapper(session)).insertAdSchedule(scheudle)) {
                                       session.rollback();
                                       session.close();
                                       boolean var18 = false;
                                       return var18;
                                    }
                                 }
                              }
                           }
                        }
                     }
                  }
               }
            }

            long logId = (long)SequenceDB.getNextValue("MI_CDS_LOG_PROGRAM");
            boolean var31;
            if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert5_updateProgram(logId, program, "01", ipAddress) != 1) {
               session.rollback();
               var31 = false;
               return var31;
            } else {
               session.commit();
               var31 = true;
               return var31;
            }
         }
      } catch (SQLException var23) {
         session.rollback();
         throw var23;
      } catch (Exception var24) {
         session.rollback();
         boolean var6 = false;
         return var6;
      } finally {
         session.close();
      }
   }

   public boolean updateProgramName(String program_name, String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).updateProgramName(program_name, programId) == 1;
   }

   public boolean addContentSchedule(ContentsScheduleEntity schedule) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var3;
      try {
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).update_addContentSchedule(schedule) == 1) {
            long priority = 0L;
            Map map = ((ScheduleInfoDAOMapper)this.getMapper(session)).select_addContentSchedule(schedule);
            if (map != null && map.size() > 0) {
               priority = (Long)map.get("PRIORITY");
            }

            int cnt = ((ScheduleInfoDAOMapper)this.getMapper(session)).insert2_addContentSchedule(schedule, priority + 1L);
            boolean var7;
            if (cnt != 1) {
               session.rollback();
               var7 = false;
               return var7;
            }

            session.commit();
            var7 = cnt > 0;
            return var7;
         }

         session.rollback();
         var3 = false;
      } catch (SQLException var11) {
         session.rollback();
         throw var11;
      } finally {
         session.close();
      }

      return var3;
   }

   public boolean addSyncMatchInfoTemp(SyncSchedule syncschedule) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var3;
      try {
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).insertSyncScheduleMatchInfoTemp(syncschedule) == 1) {
            session.commit();
            var3 = true;
            return var3;
         }

         session.rollback();
         var3 = false;
      } catch (SQLException var7) {
         session.rollback();
         throw var7;
      } finally {
         session.close();
      }

      return var3;
   }

   public boolean addSyncMatchInfo(String scheduleId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var3;
      try {
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).insertSyncScheduleMatchInfo(scheduleId) == 1) {
            session.commit();
            var3 = true;
            return var3;
         }

         session.rollback();
         var3 = false;
      } catch (SQLException var7) {
         session.rollback();
         throw var7;
      } finally {
         session.close();
      }

      return var3;
   }

   public int countSyncScheduleMatchInfoTemp(String scheduleId) throws SQLException {
      int cnt = false;
      int cnt = ((ScheduleInfoDAOMapper)this.getMapper()).countSyncScheduleMatchInfoTemp(scheduleId);
      return cnt;
   }

   public int countSyncScheduleMatchInfo(String scheduleId) throws SQLException {
      int cnt = false;
      int cnt = ((ScheduleInfoDAOMapper)this.getMapper()).countSyncScheduleMatchInfo(scheduleId);
      return cnt;
   }

   public boolean deleteSyncScheduleMatchInfoTemp(String scheduleId) throws SQLException {
      ((ScheduleInfoDAOMapper)this.getMapper()).deleteSyncScheduleMatchInfoTemp(scheduleId);
      return true;
   }

   public boolean deleteSyncScheduleMatchInfo(String scheduleId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var3;
      try {
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).deleteSyncScheduleMatchInfo(scheduleId)) {
            var3 = true;
            return var3;
         }

         session.rollback();
         var3 = false;
      } catch (SQLException var7) {
         session.rollback();
         throw var7;
      } finally {
         session.close();
      }

      return var3;
   }

   public List getSyncScheduleMatchInfoTemp(String scheduleId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getSyncScheduleMatchInfoTemp(scheduleId);
   }

   public List getSyncScheduleMatchInfo(String scheduleId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getSyncScheduleMatchInfo(scheduleId);
   }

   public boolean transferProgramDataToMain(String programId, String sessionId, String userId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var5;
      try {
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_transferProgramDataToMain(programId);
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).update_transferProgramDataToMain(programId) == 1) {
            ((ScheduleInfoDAOMapper)this.getMapper(session)).insert_transferProgramDataToMain(programId, sessionId);
            ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_transferProgramDataToMain(programId);
            ((ScheduleInfoDAOMapper)this.getMapper(session)).insert2_transferProgramDataToMain(programId, sessionId);
            ((ScheduleInfoDAOMapper)this.getMapper(session)).delete3_transferProgramDataToMain(programId);
            ((ScheduleInfoDAOMapper)this.getMapper(session)).insert3_transferProgramDataToMain(programId, sessionId);
            ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteChannel(programId);
            ((ScheduleInfoDAOMapper)this.getMapper(session)).copyChannelFromTmpToOriginTable(programId, sessionId);
            ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteChannelTmp(programId, sessionId);
            ((ScheduleInfoDAOMapper)this.getMapper(session)).delete4_transferProgramDataToMain(programId, sessionId);
            ((ScheduleInfoDAOMapper)this.getMapper(session)).delete5_transferProgramDataToMain(programId, sessionId);
            ((ScheduleInfoDAOMapper)this.getMapper(session)).delete6_transferProgramDataToMain(programId, sessionId);
            long logId = (long)SequenceDB.getNextValue("MI_CDS_LOG_PROGRAM");
            boolean var7;
            if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert4_transferProgramDataToMain(logId, programId, "03", userId) != 1) {
               session.rollback();
               var7 = false;
               return var7;
            }

            session.commit();
            var7 = true;
            return var7;
         }

         session.rollback();
         var5 = false;
      } catch (SQLException var11) {
         session.rollback();
         throw var11;
      } finally {
         session.close();
      }

      return var5;
   }

   public boolean transferSyncTagDataToMain(String programId, String sessionId) throws Exception {
      SqlSession session = this.openNewSession(false);

      try {
         List scheduleIdList = ((ScheduleInfoDAOMapper)this.getMapper(session)).getScheduleIdListByProgramId(programId);

         for(int idx = 0; idx < scheduleIdList.size(); ++idx) {
            String scheduleId = (String)scheduleIdList.get(idx);
            ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteDynaminTagInfo(scheduleId);
            ((ScheduleInfoDAOMapper)this.getMapper(session)).addDynaminTagInfo(scheduleId, sessionId);
            ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteDynaminTagInfoTemp(scheduleId);
         }

         session.commit();
         boolean var12 = true;
         return var12;
      } catch (SQLException var10) {
         session.rollback();
         throw var10;
      } finally {
         session.close();
      }
   }

   public boolean transferProgramDataToTemp(String programId, String sessionId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var4;
      try {
         ((ScheduleInfoDAOMapper)this.getMapper(session)).insert_transferProgramDataToTemp(programId, sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).insert2_transferProgramDataToTemp(programId, sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).insert3_transferProgramDataToTemp(programId, sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).copyChannelFromOriginToTmpTable(programId, sessionId);
         session.commit();
         var4 = true;
      } catch (SQLException var8) {
         session.rollback();
         throw var8;
      } finally {
         session.close();
      }

      return var4;
   }

   public boolean transferSyncScheduleMatchInfoToTemp(String scheduleId, String sessionId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var4;
      try {
         ((ScheduleInfoDAOMapper)this.getMapper(session)).transferToDynaminTagInfoTemp(scheduleId, sessionId);
         session.commit();
         var4 = true;
      } catch (SQLException var8) {
         session.rollback();
         throw var8;
      } finally {
         session.close();
      }

      return var4;
   }

   public boolean transferScheduleDataToTempWithNewId(String newProgramId, String programId, String sessionId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      try {
         Iterator var5 = ((ScheduleInfoDAOMapper)this.getMapper(session)).select_transferScheduleDataToTempWithNewId(programId).iterator();

         boolean var7;
         while(var5.hasNext()) {
            ContentsScheduleEntity schedule = (ContentsScheduleEntity)var5.next();
            if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert_transferScheduleDataToTempWithNewId(newProgramId, sessionId, schedule, UUID.randomUUID().toString()) != 1) {
               session.rollback();
               var7 = false;
               return var7;
            }
         }

         var5 = ((ScheduleInfoDAOMapper)this.getMapper(session)).select2_transferScheduleDataToTempWithNewId(programId).iterator();

         while(var5.hasNext()) {
            FrameEntity frame = (FrameEntity)var5.next();
            if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert2_transferScheduleDataToTempWithNewId(newProgramId, sessionId, frame, UUID.randomUUID().toString()) != 1) {
               session.rollback();
               var7 = false;
               return var7;
            }
         }

         var5 = ((ScheduleInfoDAOMapper)this.getMapper(session)).selectChannelsWithProgramId(programId).iterator();

         while(var5.hasNext()) {
            ChannelEntity channel = (ChannelEntity)var5.next();
            if (((ScheduleInfoDAOMapper)this.getMapper(session)).insertIntoChannelTmpTable(newProgramId, sessionId, channel) != 1) {
               session.rollback();
               var7 = false;
               return var7;
            }
         }

         session.commit();
         boolean var13 = true;
         return var13;
      } catch (SQLException var11) {
         session.rollback();
         throw var11;
      } finally {
         session.close();
      }
   }

   public List getFrameCount(String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).select2_transferScheduleDataToTempWithNewId(programId);
   }

   public boolean transferScheduleDataToTempWithNewIdForImport(String programId, String schedule_id, String sessionId, String frame_index, int channel_no) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var16;
      try {
         List rt = ((ScheduleInfoDAOMapper)this.getMapper(session)).selectSchedulesWithSheduleId(schedule_id);

         for(int count = 0; count < rt.size(); ++count) {
            ContentsScheduleEntity schedule = (ContentsScheduleEntity)rt.get(count);
            if (((ScheduleInfoDAOMapper)this.getMapper(session)).insertScheduleTmp(programId, sessionId, UUID.randomUUID().toString(), channel_no, schedule) != 1) {
               session.rollback();
               boolean var10 = false;
               return var10;
            }
         }

         session.commit();
         var16 = true;
      } catch (SQLException var14) {
         session.rollback();
         throw var14;
      } finally {
         session.close();
      }

      return var16;
   }

   public boolean updateContentSchedule(ContentsScheduleEntity schedule) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var3;
      try {
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).update_updateContentSchedule(schedule) == 1) {
            long priority = 0L;
            Map map = ((ScheduleInfoDAOMapper)this.getMapper(session)).select_updateContentSchedule(schedule);
            if (map != null && map.size() > 0) {
               priority = (Long)map.get("PRIORITY") + 1L;
            }

            int cnt = ((ScheduleInfoDAOMapper)this.getMapper(session)).update2_updateContentSchedule(schedule, priority);
            session.commit();
            boolean var7 = cnt > 0;
            return var7;
         }

         session.rollback();
         var3 = false;
      } catch (SQLException var11) {
         session.rollback();
         throw var11;
      } finally {
         session.close();
      }

      return var3;
   }

   public boolean addPanelOrZeroFrameSchedule(ScheduleEntity schedule) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).addPanelOrZeroFrameSchedule(schedule) > 0;
   }

   public boolean updatePanelOrZeroFrameSchedule(ScheduleEntity schedule) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).updatePanelOrZeroFrameSchedule(schedule) > 0;
   }

   public boolean deleteTempSchedule(String programId, String scheduleId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).deleteTempSchedule(programId, scheduleId) > 0;
   }

   public boolean deleteTempScheduleByChannelNo(String programId, String sessionId, int channelNo) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).deleteTempScheduleByChannelNo(programId, sessionId, channelNo);
   }

   public boolean deleteTempFrame(String programId, String session_id) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var4;
      try {
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_deleteTempFrame(programId, session_id);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_deleteTempFrame(programId, session_id);
         session.commit();
         var4 = true;
      } catch (SQLException var8) {
         session.rollback();
         throw var8;
      } finally {
         session.close();
      }

      return var4;
   }

   public boolean deleteTempFrameByChannelNo(String programId, String session_id, int channelNo) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).deleteTempFrameByChannelNo(programId, session_id, channelNo);
   }

   public boolean deleteProgram(String programId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var3;
      try {
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_deleteProgram(programId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_deleteProgram(programId);
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).delete3_deleteProgram(programId) >= 1) {
            session.commit();
            var3 = true;
            return var3;
         }

         session.rollback();
         var3 = false;
      } catch (SQLException var7) {
         session.rollback();
         throw var7;
      } finally {
         session.close();
      }

      return var3;
   }

   public boolean deleteSchedule(String sessionId, String scheduleId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).insert_deleteSchedule(sessionId, scheduleId, Boolean.TRUE) > 0;
   }

   public boolean deleteTempScheduleForFrameIndex(String programId, String sessionId, int channel_no, int frameIndex) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).deleteTempScheduleForFrameIndex(programId, sessionId, channel_no, frameIndex) > 0;
   }

   public List getChannels(String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getChannels(programId);
   }

   public List getFrames(String programId, int channelNo, int screenIndex) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getFrames(programId, channelNo, screenIndex);
   }

   public List getAuthorityFromFrame(String programId, String frameId) {
      ArrayList groupIds = new ArrayList();

      try {
         List mapList = ((ScheduleInfoDAOMapper)this.getMapper()).select2_getFrameData(programId, frameId);
         if (mapList != null) {
            Iterator var5 = mapList.iterator();

            while(var5.hasNext()) {
               Map userGroup = (Map)var5.next();
               groupIds.add((Long)userGroup.get("user_group_id"));
            }
         }
      } catch (SQLException var7) {
         groupIds = null;
      }

      return groupIds;
   }

   public List getFrames(String programId, int channelNo) throws SQLException {
      List frameList = ((ScheduleInfoDAOMapper)this.getMapper()).getFrame(programId, channelNo);

      for(int index = 0; index < frameList.size(); ++index) {
         FrameEntity frame = (FrameEntity)frameList.get(index);
         if (frame != null) {
            List mapList = ((ScheduleInfoDAOMapper)this.getMapper()).select2_getFrameData(programId, frame.getFrame_id());
            if (mapList != null) {
               List authorityList = new ArrayList();
               List authorityNameList = new ArrayList();
               Iterator var9 = mapList.iterator();

               while(var9.hasNext()) {
                  Map userGroup = (Map)var9.next();
                  authorityList.add((Long)userGroup.get("user_group_id"));
                  authorityNameList.add((String)userGroup.get("group_name"));
               }

               if (authorityList.size() > 0) {
                  String[] userGroupIds = new String[authorityList.size()];
                  String[] userGroupNames = new String[authorityList.size()];

                  for(int i = 0; i < authorityList.size(); ++i) {
                     userGroupIds[i] = String.valueOf(authorityList.get(i));
                     userGroupNames[i] = (String)authorityNameList.get(i);
                  }

                  frame.setAuthority(userGroupIds);
                  frame.setAuthorityNames(userGroupNames);
               } else {
                  frame.setAuthority((String[])null);
                  frame.setAuthorityNames((String[])null);
               }
            }

            if (frame.getDefault_content_id() != null && !frame.getDefault_content_id().equals("")) {
               Map map = ((ScheduleInfoDAOMapper)this.getMapper()).getContentName(frame.getDefault_content_id());
               if (map != null) {
                  String contntName = (String)map.get("content_name");
                  frame.setDefault_content_names(contntName);
               }
            }
         }
      }

      return frameList;
   }

   public List getFramesInfo(String programId, int screenIndex) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getFramesInfo(programId, screenIndex);
   }

   public List getTempFrames(String programId, int channelNo, String session_id) throws SQLException {
      List frameEntities = ((ScheduleInfoDAOMapper)this.getMapper()).select_getTempFrames(programId, channelNo, session_id);
      Iterator var5 = frameEntities.iterator();

      while(var5.hasNext()) {
         FrameEntity frameEntity = (FrameEntity)var5.next();
         StringBuilder userGroupIds = new StringBuilder();
         StringBuilder userGroupNames = new StringBuilder();
         Iterator var9 = ((ScheduleInfoDAOMapper)this.getMapper()).select2_getTempFrames(session_id, programId, channelNo, frameEntity.getFrame_id()).iterator();

         while(var9.hasNext()) {
            Map userGroup = (Map)var9.next();
            if (StringUtils.isNotBlank(userGroupIds.toString()) && StringUtils.isNotBlank(userGroupNames.toString())) {
               userGroupIds.append(",");
               userGroupNames.append(",");
            }

            userGroupIds.append(userGroup.get("user_group_id"));
            userGroupNames.append(userGroup.get("group_name"));
         }

         frameEntity.setUser_group_ids(userGroupIds.toString());
         frameEntity.setUser_groups(userGroupNames.toString());
      }

      return frameEntities;
   }

   public FrameEntity getFrameData(String programId, int frame_index) throws SQLException {
      FrameEntity frameEntity = ((ScheduleInfoDAOMapper)this.getMapper()).select_getFrameDate(programId, frame_index);
      if (frameEntity != null) {
         StringBuilder userGroupIds = new StringBuilder();
         StringBuilder userGroupNames = new StringBuilder();
         Iterator var6 = ((ScheduleInfoDAOMapper)this.getMapper()).select2_getFrameData(programId, frameEntity.getFrame_id()).iterator();

         while(var6.hasNext()) {
            Map userGroup = (Map)var6.next();
            if (StringUtils.isNotBlank(userGroupIds.toString()) && StringUtils.isNotBlank(userGroupNames.toString())) {
               userGroupIds.append(",");
               userGroupNames.append(",");
            }

            userGroupIds.append(userGroup.get("user_group_id"));
            userGroupNames.append(userGroup.get("group_name"));
         }

         frameEntity.setUser_group_ids(userGroupIds.toString());
         frameEntity.setUser_groups(userGroupNames.toString());
      }

      return frameEntity;
   }

   public FrameEntity getFrameData(String programId, int frame_index, int channelNo) throws SQLException {
      FrameEntity frameEntity = ((ScheduleInfoDAOMapper)this.getMapper()).select_getFrameDataWithChannelNo(programId, frame_index, channelNo);
      if (frameEntity != null) {
         StringBuilder userGroupIds = new StringBuilder();
         StringBuilder userGroupNames = new StringBuilder();
         Iterator var7 = ((ScheduleInfoDAOMapper)this.getMapper()).select2_getFrameData(programId, frameEntity.getFrame_id()).iterator();

         while(var7.hasNext()) {
            Map userGroup = (Map)var7.next();
            if (StringUtils.isNotBlank(userGroupIds.toString()) && StringUtils.isNotBlank(userGroupNames.toString())) {
               userGroupIds.append(",");
               userGroupNames.append(",");
            }

            userGroupIds.append(userGroup.get("user_group_id"));
            userGroupNames.append(userGroup.get("group_name"));
         }

         frameEntity.setUser_group_ids(userGroupIds.toString());
         frameEntity.setUser_groups(userGroupNames.toString());
      }

      return frameEntity;
   }

   public long getTempFrameCount(String programId, String session_id) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getTempFrameCount(programId, session_id);
   }

   public long getTempFrameCount(String programId, int channelNo, String session_id) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getTempFrameCountWithChannelNoCondition(programId, channelNo, session_id);
   }

   public long getTempFrameCount(String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getTempFrameCountForProgram(programId);
   }

   public long getTempFrameCountForSession(String session_id) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getTempFrameCountForSession(session_id);
   }

   public boolean deleteProgramTempData(String programId, String sessionId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var4;
      try {
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_deleteProgramTempData(programId, sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteFromMiCdsInfoChannelTmp(programId, sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_deleteProgramTempData(programId, sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete3_deleteProgramTempData(programId, sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete4_deleteProgramTempData(programId);
         session.commit();
         var4 = true;
      } catch (SQLException var8) {
         session.rollback();
         throw var8;
      } finally {
         session.close();
      }

      return var4;
   }

   public boolean deleteProgramData(String programId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var3;
      try {
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_deleteProgramData(programId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteFromMiCdsInfoChannel(programId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_deleteProgramData(programId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete3_deleteProgramData(programId);
         session.commit();
         var3 = true;
      } catch (SQLException var7) {
         session.rollback();
         throw var7;
      } finally {
         session.close();
      }

      return var3;
   }

   public boolean deleteProgramTempDataWithSession(String sessionId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var3;
      try {
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_deleteProgramTempDataWithSession(sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteFromMiCdsInfoChannelTmpWithSessionId(sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_deleteProgramTempDataWithSession(sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete3_deleteProgramTempDataWithSession(sessionId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteDynaminTagInfoTempWithSession(sessionId);
         session.commit();
         var3 = true;
      } catch (SQLException var7) {
         session.rollback();
         throw var7;
      } finally {
         session.close();
      }

      return var3;
   }

   public boolean deleteProgramTempDataWithId(String programId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var3;
      try {
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_deleteProgramTempDataWithId(programId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteFromMiCdsInfoChannelTmpWithProgramId(programId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_deleteProgramTempDataWithId(programId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete3_deleteProgramTempDataWithId(programId);
         session.commit();
         var3 = true;
      } catch (SQLException var7) {
         session.rollback();
         throw var7;
      } finally {
         session.close();
      }

      return var3;
   }

   public boolean deleteAllProgramTempData() {
      SqlSession session = this.openNewSession(false);

      try {
         ((ScheduleInfoDAOMapper)this.getMapper(session)).deletet_deleteAllProgramTempData();
         ((ScheduleInfoDAOMapper)this.getMapper(session)).deletet2_deleteAllProgramTempData();
         ((ScheduleInfoDAOMapper)this.getMapper(session)).deletet3_deleteAllProgramTempData();
         ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteAllChannelsTmp();
         session.commit();
      } catch (SQLException var6) {
         session.rollback();
         this.logger.error(var6);
      } finally {
         session.close();
      }

      return true;
   }

   public ProgramEntity getProgram(String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getProgram(programId);
   }

   public ProgramEntity getProgram(String programId, SqlSession session) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper(session)).getProgram(programId);
   }

   public String getProgramName(String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getProgramName(programId);
   }

   public PagedListInfo getProgramList(Map map, int startPos, int pageSize) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.put("limit", pageSize);
      paramsMap.put("offset", startPos > 0 ? startPos - 1 : 0);
      if (MapUtils.isNotEmpty(map)) {
         if (map.get("screen_count") != null) {
            paramsMap.put("screen_count", map.get("screen_count"));
         }

         if (map.get("device_group_id") != null) {
            paramsMap.put("device_group_id", map.get("device_group_id"));
         }

         if (StringUtils.isNotBlank((String)map.get("program_id"))) {
            paramsMap.put("program_id", ((String)map.get("program_id")).toUpperCase());
         }
      }

      return new PagedListInfo(((ScheduleInfoDAOMapper)this.getMapper()).getProgramList(paramsMap), ((ScheduleInfoDAOMapper)this.getMapper()).getProgramListCount(paramsMap));
   }

   public String getBGMContentName(String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getBGMContentName(programId);
   }

   public List getContentSchedules(String programId, int channelNo, int screenIndex, int frameIndex) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getContentSchedules(programId, channelNo, screenIndex, frameIndex, "00");
   }

   public List getContentListFromProgramidandChannel(String programId, int channelNo, int screenIndex, int frameIndex) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getContentListFromProgramidandChannel(programId, channelNo, screenIndex, frameIndex, "00");
   }

   public List getContentSchedules(String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getContentSchedulesForProgramId(programId, "00");
   }

   public List getContentSchedules(String programId, int channelNo) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getContentSchedulesForProgramIdAndChannelId(programId, channelNo, "00");
   }

   public List getPanelOrZeroFrameSchedules(String programId, String scheduleType) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getPanelOrZeroFrameSchedules(programId, scheduleType);
   }

   public List getPanelOrZeroFrameTempSchedules(String programId, String scheduleType) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getPanelOrZeroFrameTempSchedules(programId, scheduleType);
   }

   public List selAllSchedule(String programId, String sessionId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).selAllSchedule(programId, sessionId);
   }

   public List selAllScheduleByMonth(Map map) throws SQLException {
      int frame_index = Integer.parseInt((String)map.get("frame_index"));
      int channelNo = 1;
      if (map.get("channel_no") != null) {
         channelNo = Integer.parseInt((String)map.get("channel_no"));
      }

      return ((ScheduleInfoDAOMapper)this.getMapper()).selAllScheduleByMonth(map, frame_index, channelNo, frame_index == 0);
   }

   public List selAllScheduleByWeek(Map map) throws SQLException {
      String[] weekDates = ((String)map.get("weekDates")).split(",");
      int channelNo = 1;
      if (map.get("channel_no") != null) {
         channelNo = Integer.parseInt((String)map.get("channel_no"));
      }

      int frameIndex = Integer.parseInt((String)map.get("frame_index"));
      return ((ScheduleInfoDAOMapper)this.getMapper()).selAllScheduleByWeek(map, weekDates, frameIndex, channelNo, frameIndex == 0, this.getConstants());
   }

   public List selAllScheduleByDay(Map map) throws SQLException {
      String datestr = (String)map.get("spdate");
      int monthday = Integer.parseInt(datestr.substring(8, 10));
      String date = "" + monthday;
      if (monthday < 10) {
         date = "0" + monthday;
      }

      int frameIndex = Integer.parseInt((String)map.get("frame_index"));
      int channelNo = 1;
      if (map.get("channel_no") != null && !map.get("channel_no").equals("undefined")) {
         channelNo = NumberUtils.toInt((String)map.get("channel_no"), 1);
      }

      return ((ScheduleInfoDAOMapper)this.getMapper()).selAllScheduleByDay(map, frameIndex, channelNo, frameIndex == 0, date, this.getConstants());
   }

   public ContentsScheduleEntity getScheduleData(Map map) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getScheduleData(map);
   }

   public List getScheduleListPage(Map map, int startPos, int pageSize) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.put("limit", pageSize);
      paramsMap.put("offset", startPos > 0 ? startPos - 1 : 0);
      paramsMap.put("frame_index", Integer.parseInt((String)map.get("frame_index")));
      paramsMap.put("program_id", map.get("program_id"));
      paramsMap.put("session_id", map.get("session_id"));
      int channelNo = 1;
      if (map.get("channel_no") != null) {
         channelNo = Integer.parseInt((String)map.get("channel_no"));
      }

      paramsMap.put("channel_no", channelNo);
      String epdate = (String)map.get("epdate");
      String spdate = (String)map.get("spdate");
      if (StringUtils.isNotBlank(spdate) && StringUtils.isNotBlank(epdate)) {
         paramsMap.put("spdate", spdate);
         paramsMap.put("epdate", epdate);
      }

      String sptime = (String)map.get("sptime");
      String eptime = (String)map.get("eptime");
      if (StringUtils.isNotBlank(sptime) && StringUtils.isNotBlank(eptime)) {
         paramsMap.put("sptime", sptime);
         paramsMap.put("eptime", eptime);
      }

      String sortColumn = (String)map.get("sortColumn");
      if (StringUtils.isNotBlank(sortColumn)) {
         if (sortColumn.toUpperCase().equals("PERIOD")) {
            paramsMap.put("sortColumn", "S.START_DATE");
         } else if (sortColumn.toUpperCase().equals("TIME")) {
            paramsMap.put("sortColumn", "S.START_TIME");
         } else if (sortColumn.toUpperCase().equals("CREATE_DATE")) {
            paramsMap.put("sortColumn", "S.CREATE_DATE");
         } else if (sortColumn.toUpperCase().equals("SCH_INFO")) {
            paramsMap.put("sortColumn", "C.CONTENT_NAME");
         }

         String sortOrder = (String)map.get("sortOrder");
         if (StringUtils.isNotBlank(sortOrder)) {
            paramsMap.put("sortOrder", sortOrder.toUpperCase());
         }
      } else {
         paramsMap.put("sortColumn", "S.START_DATE");
         paramsMap.put("sortOrder", "DESC");
      }

      return ((ScheduleInfoDAOMapper)this.getMapper()).getScheduleListPage(paramsMap);
   }

   public int getScheduleListCnt(Map map) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.put("frame_index", Integer.parseInt((String)map.get("frame_index")));
      paramsMap.put("program_id", map.get("program_id"));
      paramsMap.put("session_id", map.get("session_id"));
      int channelNo = 1;
      if (map.get("channel_no") != null) {
         channelNo = Integer.parseInt((String)map.get("channel_no"));
      }

      paramsMap.put("channel_no", channelNo);
      String epdate = (String)map.get("epdate");
      String spdate = (String)map.get("spdate");
      if (StringUtils.isNotBlank(spdate) && StringUtils.isNotBlank(epdate)) {
         paramsMap.put("spdate", spdate);
         paramsMap.put("epdate", epdate);
      }

      String sptime = (String)map.get("sptime");
      String eptime = (String)map.get("eptime");
      if (StringUtils.isNotBlank(sptime) && StringUtils.isNotBlank(eptime)) {
         paramsMap.put("sptime", sptime);
         paramsMap.put("eptime", eptime);
      }

      return ((ScheduleInfoDAOMapper)this.getMapper()).getScheduleListCnt(paramsMap);
   }

   public PagedListInfo getScheduleList(String programId, int channelNo, int frameIndex, Map map) throws SQLException {
      Map paramsMap = new HashMap();
      int pageSize = (Integer)map.get("pageSize");
      paramsMap.put("limit", pageSize);
      int startPos = (Integer)map.get("startPos");
      paramsMap.put("offset", startPos > 0 ? startPos - 1 : 0);
      paramsMap.put("frame_index", frameIndex);
      paramsMap.put("program_id", programId);
      paramsMap.put("channel_no", channelNo);
      String searchText = (String)map.get("searchText");
      if (StringUtils.isNotBlank(searchText)) {
         paramsMap.put("searchText", searchText.toUpperCase().replaceAll("_", "^_"));
      }

      String epdate = (String)map.get("epdate");
      String spdate = (String)map.get("spdate");
      if (StringUtils.isNotBlank(spdate) && StringUtils.isNotBlank(epdate)) {
         paramsMap.put("spdate", spdate);
         paramsMap.put("epdate", epdate);
      }

      String sptime = (String)map.get("sptime");
      String eptime = (String)map.get("eptime");
      if (StringUtils.isNotBlank(sptime) && StringUtils.isNotBlank(eptime)) {
         paramsMap.put("sptime", sptime);
         paramsMap.put("eptime", eptime);
      }

      String sortColumn = (String)map.get("sortColumn");
      if (StringUtils.isNotBlank(sortColumn)) {
         if (sortColumn.toUpperCase().equals("START_DATE")) {
            paramsMap.put("sortColumn", "S.START_DATE");
         } else if (sortColumn.toUpperCase().equals("START_TIME")) {
            paramsMap.put("sortColumn", "S.START_TIME");
         } else if (sortColumn.toUpperCase().equals("CREATE_DATE")) {
            paramsMap.put("sortColumn", "S.CREATE_DATE");
         } else if (sortColumn.toUpperCase().equals("CONTENT_NAME")) {
            paramsMap.put("sortColumn", "C.CONTENT_NAME");
         }

         String sortOrder = (String)map.get("sortOrder");
         if (StringUtils.isNotBlank(sortOrder)) {
            paramsMap.put("sortOrder", sortOrder.toUpperCase());
         }
      } else {
         paramsMap.put("sortColumn", "S.START_DATE");
         paramsMap.put("sortOrder", "DESC");
      }

      return new PagedListInfo(((ScheduleInfoDAOMapper)this.getMapper()).getScheduleList(paramsMap), ((ScheduleInfoDAOMapper)this.getMapper()).getScheduleListCount(paramsMap));
   }

   public PagedListInfo getHWConstraintList(String programId, Map map) throws SQLException {
      Map paramsMap = new HashMap();
      int pageSize = (Integer)map.get("pageSize");
      paramsMap.put("limit", pageSize);
      int startPos = (Integer)map.get("startPos");
      paramsMap.put("offset", startPos > 0 ? startPos - 1 : 0);
      paramsMap.put("program_id", programId);
      String searchText = (String)map.get("searchText");
      if (StringUtils.isNotBlank(searchText)) {
         paramsMap.put("searchText", searchText.toUpperCase().replaceAll("_", "^_"));
      }

      String epdate = (String)map.get("epdate");
      String spdate = (String)map.get("spdate");
      if (StringUtils.isNotBlank(spdate) && StringUtils.isNotBlank(epdate)) {
         paramsMap.put("spdate", spdate);
         paramsMap.put("epdate", epdate);
      }

      String sptime = (String)map.get("sptime");
      String eptime = (String)map.get("eptime");
      if (StringUtils.isNotBlank(sptime) && StringUtils.isNotBlank(eptime)) {
         paramsMap.put("sptime", sptime);
         paramsMap.put("eptime", eptime);
      }

      String sortColumn = (String)map.get("sortName");
      if (StringUtils.isNotBlank(sortColumn)) {
         if (sortColumn.toUpperCase().equals("START_DATE")) {
            paramsMap.put("sortColumn", "S.START_DATE");
         } else if (sortColumn.toUpperCase().equals("START_TIME")) {
            paramsMap.put("sortColumn", "S.START_TIME");
         } else if (sortColumn.toUpperCase().equals("CREATE_DATE")) {
            paramsMap.put("sortColumn", "S.CREATE_DATE");
         }

         String sortOrder = (String)map.get("sortOrder");
         if (StringUtils.isNotBlank(sortOrder)) {
            paramsMap.put("sortOrder", sortOrder.toUpperCase());
         }
      } else {
         paramsMap.put("sortColumn", "S.START_DATE");
         paramsMap.put("sortOrder", "DESC");
      }

      return new PagedListInfo(((ScheduleInfoDAOMapper)this.getMapper()).getHWConstraintList(paramsMap), ((ScheduleInfoDAOMapper)this.getMapper()).getHWConstraintListCount(paramsMap));
   }

   public boolean setProgram(String sessionId, ProgramEntity program) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var4;
      try {
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_setProgram(program.getProgram_id());
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_setProgram(program.getProgram_id());
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).update_setProgram(program) == 1) {
            if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert_setProgram(program) != 1) {
               session.rollback();
               var4 = false;
               return var4;
            }

            String deviceGroupIds = program.getDevice_group_ids();
            if (StringUtils.isNotBlank(deviceGroupIds)) {
               String[] var5 = deviceGroupIds.split(",");
               int var6 = var5.length;

               for(int var7 = 0; var7 < var6; ++var7) {
                  String deviceId = var5[var7];
                  long deviceIdParsed = Long.parseLong(deviceId);
                  ((ScheduleInfoDAOMapper)this.getMapper(session)).delete3_setProgram(deviceIdParsed);
                  if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert2_setProgram(program.getProgram_id(), deviceIdParsed) != 1) {
                     session.rollback();
                     boolean var11 = false;
                     return var11;
                  }
               }
            }

            session.commit();
            boolean var18 = true;
            return var18;
         }

         session.rollback();
         var4 = false;
      } catch (SQLException var15) {
         session.rollback();
         throw var15;
      } finally {
         session.close();
      }

      return var4;
   }

   public boolean programVersionUp(String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).programVersionUp(programId) > 0;
   }

   public List getDeviceGroupIds(String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getDeviceGroupIds(programId);
   }

   public List getDeviceProgramMapList(String programId) throws SQLException {
      return this.getDeviceProgramMapList(programId, (SqlSession)null);
   }

   public List getDeviceProgramMapList(String programId, SqlSession session) throws SQLException {
      ScheduleInfoDAOMapper mapper = session != null ? (ScheduleInfoDAOMapper)this.getMapper(session) : (ScheduleInfoDAOMapper)this.getMapper();
      return mapper.getDeviceProgramMapList(programId);
   }

   public boolean mapDeviceGroupWithDefault(Long devGrp, String defaultProgramId) throws SQLException {
      return this.mapDeviceGroupWithDefault(devGrp, defaultProgramId, (SqlSession)null);
   }

   public boolean mapDeviceGroupWithDefault(Long devGrp, String defaultProgramId, SqlSession session) throws SQLException {
      ScheduleInfoDAOMapper mapper = session != null ? (ScheduleInfoDAOMapper)this.getMapper(session) : (ScheduleInfoDAOMapper)this.getMapper();
      return mapper.mapDeviceGroupWithDefault(devGrp, defaultProgramId);
   }

   public Map getDeviceGroupIdsAndName(String programId, SqlSession session) throws SQLException {
      ScheduleInfoDAOMapper mapper = session == null ? (ScheduleInfoDAOMapper)this.getMapper() : (ScheduleInfoDAOMapper)this.getMapper(session);
      List list = mapper.getDeviceGroupIdsAndName(programId);
      StringBuilder group_ids = new StringBuilder();
      StringBuilder group_names = new StringBuilder();
      Iterator var7 = list.iterator();

      while(var7.hasNext()) {
         Map m = (Map)var7.next();
         if (group_ids.length() > 0 || group_names.length() > 0) {
            group_ids.append(",");
            group_names.append(",");
         }

         group_ids.append(m.get("device_group_id"));
         group_names.append((String)((String)m.get("group_name")));
      }

      Map rt = new HashMap();
      rt.put("device_group_ids", group_ids.toString());
      rt.put("group_names", group_names.toString());
      return rt;
   }

   public String getGroupNameByGroupId(long groupId) throws SQLException {
      ScheduleInfoDAOMapper mapper = (ScheduleInfoDAOMapper)this.getMapper();
      return mapper.getGroupNameByGroupId(groupId);
   }

   public Map getDeviceGroupIdsAndName(String programId) throws SQLException {
      return this.getDeviceGroupIdsAndName(programId, (SqlSession)null);
   }

   public List getProgramGroupIdAndName(String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getProgramGroupIdAndName(programId);
   }

   public boolean setActiveProgramVersion(String programId, long version) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).update_setActiveProgramVersion(version, programId) == 1 || ((ScheduleInfoDAOMapper)this.getMapper()).insert_setActiveProgramVersion(programId, version) == 1;
   }

   public long getActiveProgramVersion(String programId) throws SQLException {
      Long ver = ((ScheduleInfoDAOMapper)this.getMapper()).getActiveProgramVersion(programId);
      return ver != null ? ver : -1L;
   }

   public long getProgramVersion(String programId) throws SQLException {
      Long ver = ((ScheduleInfoDAOMapper)this.getMapper()).getProgramVersion(programId);
      if (ver != null) {
         return ver;
      } else {
         this.logger.fatal("Critical error in logic - programId: " + programId);
         return -1L;
      }
   }

   public List getProgramByContentId(String contentId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getProgramByContentId(contentId);
   }

   public List getProgramByPlaylistId(String playlistId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getProgramByPlaylistId(playlistId);
   }

   public boolean isProgramNameUnique(String program_name, String programId, int prog_group_id) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.put("program_id", programId);
      paramsMap.put("program_name", program_name);
      long root_group_id = this.getProgramGroupRoot(prog_group_id);
      if (root_group_id != -1L) {
         List childGroupIdList = this.getChildGroupIdList((int)root_group_id, true);
         childGroupIdList.add(root_group_id);
         paramsMap.put("childGroupIds", childGroupIdList);
      }

      return !((ScheduleInfoDAOMapper)this.getMapper()).isProgramNameUnique(paramsMap);
   }

   public List getChildGroupIdList(int group_id, boolean recursive) throws SQLException {
      List rtList = new ArrayList();
      List groupIdList = ((ScheduleInfoDAOMapper)this.getMapper()).getChildGroupIdList(group_id, 999999);
      if (groupIdList != null) {
         Iterator var5 = groupIdList.iterator();

         while(var5.hasNext()) {
            Map groupId = (Map)var5.next();
            Long group = (Long)groupId.get("group_id");
            if (recursive) {
               rtList.add(group);
               List temp = this.getChildGroupIdList(group.intValue(), recursive);
               if (temp != null && temp.size() != 0) {
                  rtList.addAll(temp);
               }
            } else {
               rtList.add(group);
            }
         }
      }

      return rtList;
   }

   public long getProgramGroupRoot(int groupId) throws SQLException {
      Map info = ((ScheduleInfoDAOMapper)this.getMapper()).getProgramGroupRoot(groupId);
      int newGroupParentId = ((Long)info.get("P_GROUP_ID")).intValue();
      return newGroupParentId != 0 ? this.getProgramGroupRoot(newGroupParentId) : (Long)info.get("GROUP_ID");
   }

   public long getProgramGroupParentId(int groupId) throws SQLException {
      Map info = ((ScheduleInfoDAOMapper)this.getMapper()).getProgramGroupRoot(groupId);
      return (long)((Long)info.get("P_GROUP_ID")).intValue();
   }

   public boolean setProgramDeployTime(String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).setProgramDeployTime(programId);
   }

   public List getDownloadContentList(String programId, String device_id) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getDownloadContentList(programId, device_id);
   }

   public List getEventList(String scheduleId) throws SQLException {
      List rt = ((ScheduleInfoDAOMapper)this.getMapper()).getEventList(scheduleId);
      return rt;
   }

   public PagedListInfo getDownloadContentList(int startPos, int pageSize, Map condition) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.put("device_id", condition.get("device_id"));
      paramsMap.put("limit", pageSize);
      paramsMap.put("offset", startPos > 0 ? startPos - 1 : 0);
      String sort = (String)condition.get("sort");
      String dir = (String)condition.get("dir");
      if (StringUtils.isNotBlank(sort) && StringUtils.isNotBlank(dir)) {
         paramsMap.put("sortColumn", sort.toUpperCase());
         paramsMap.put("sortOrder", dir.toUpperCase());
      } else {
         paramsMap.put("sortColumn", "A.CONTENT_NAME");
         paramsMap.put("sortOrder", "ASC");
      }

      return new PagedListInfo(((ScheduleInfoDAOMapper)this.getMapper()).getDownloadStatusContentList(paramsMap), ((ScheduleInfoDAOMapper)this.getMapper()).getDownloadStatusContenCount(paramsMap));
   }

   public PagedListInfo getDownloadStatus(int startPos, int pageSize, Map condition) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.put("program_id", condition.get("program_id"));
      paramsMap.put("device_id", condition.get("device_id"));
      paramsMap.put("limit", pageSize);
      paramsMap.put("offset", startPos > 0 ? startPos - 1 : 0);
      String sort = (String)condition.get("sort");
      String dir = (String)condition.get("dir");
      if (StringUtils.isNotBlank(sort) && StringUtils.isNotBlank(dir)) {
         paramsMap.put("sortColumn", sort.toUpperCase());
         paramsMap.put("sortOrder", dir.toUpperCase());
      } else {
         paramsMap.put("sortColumn", "A.CONTENT_NAME");
         paramsMap.put("sortOrder", "ASC");
      }

      PagedListInfo pageInfo = null;

      try {
         pageInfo = new PagedListInfo(((ScheduleInfoDAOMapper)this.getMapper()).getDownloadStatusPagedList(paramsMap), ((ScheduleInfoDAOMapper)this.getMapper()).getDownloadStatusListCount(paramsMap));
      } catch (Exception var9) {
         this.logger.error("", var9);
      }

      return pageInfo;
   }

   public PagedListInfo getDownloadContentPagedListForContentSchedule(int startPos, int pageSize, Map condition) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.put("program_id", condition.get("program_id"));
      paramsMap.put("device_id", condition.get("device_id"));
      paramsMap.put("stop_date", condition.get("stop_date"));
      paramsMap.put("limit", pageSize);
      paramsMap.put("offset", startPos > 0 ? startPos - 1 : 0);
      long time = System.currentTimeMillis();
      SimpleDateFormat dayTime = new SimpleDateFormat("yyyy-MM-dd");
      String str = dayTime.format(new Date(time));
      paramsMap.put("expired_date", str);
      String sort = (String)condition.get("sort");
      String dir = (String)condition.get("dir");
      if (StringUtils.isNotBlank(sort) && StringUtils.isNotBlank(dir)) {
         paramsMap.put("sortColumn", sort.toUpperCase());
         paramsMap.put("sortOrder", dir.toUpperCase());
      } else {
         paramsMap.put("sortColumn", "A.CONTENT_NAME");
         paramsMap.put("sortOrder", "ASC");
      }

      return new PagedListInfo(((ScheduleInfoDAOMapper)this.getMapper()).getDownloadContentPagedList(paramsMap), ((ScheduleInfoDAOMapper)this.getMapper()).getDownloadContentPagedListCount(paramsMap));
   }

   public PagedListInfo getDownloadContentPagedListForEventSchedule(int startPos, int pageSize, Map condition) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.put("program_id", condition.get("program_id"));
      paramsMap.put("device_id", condition.get("device_id"));
      paramsMap.put("stop_date", condition.get("stop_date"));
      paramsMap.put("limit", pageSize);
      paramsMap.put("offset", startPos > 0 ? startPos - 1 : 0);
      String sort = (String)condition.get("sort");
      String dir = (String)condition.get("dir");
      if (StringUtils.isNotBlank(sort) && StringUtils.isNotBlank(dir)) {
         paramsMap.put("sortColumn", sort.toUpperCase());
         paramsMap.put("sortOrder", dir.toUpperCase());
      } else {
         paramsMap.put("sortColumn", "A.CONTENT_NAME");
         paramsMap.put("sortOrder", "ASC");
      }

      if (condition.get("event_schedule_id") != null) {
         String event_schedule_id = condition.get("event_schedule_id").toString();
         List content_list = ((ScheduleInfoDAOMapper)this.getMapper()).getContentIdByEventScheduleId(event_schedule_id);
         String content_id = null;
         if (content_list != null && content_list.size() > 0 && content_list.get(0) != null) {
            content_id = ((Map)content_list.get(0)).get("content_id").toString();
            paramsMap.put("content_id", content_id);
         }
      }

      return new PagedListInfo(((ScheduleInfoDAOMapper)this.getMapper()).getDownloadContentPagedListForEventSchedule(paramsMap), ((ScheduleInfoDAOMapper)this.getMapper()).getDownloadContentPagedListCountForEventSchedule(paramsMap));
   }

   public List getDownloadStatusForEventSchedule(int startPos, int pageSize, Map condition) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.put("program_id", condition.get("program_id"));
      paramsMap.put("device_id", condition.get("device_id"));
      paramsMap.put("stop_date", condition.get("stop_date"));
      paramsMap.put("limit", pageSize);
      paramsMap.put("offset", startPos > 0 ? startPos - 1 : 0);
      String sort = (String)condition.get("sort");
      String dir = (String)condition.get("dir");
      if (StringUtils.isNotBlank(sort) && StringUtils.isNotBlank(dir)) {
         paramsMap.put("sortColumn", sort.toUpperCase());
         paramsMap.put("sortOrder", dir.toUpperCase());
      } else {
         paramsMap.put("sortColumn", "A.CONTENT_NAME");
         paramsMap.put("sortOrder", "ASC");
      }

      return ((ScheduleInfoDAOMapper)this.getMapper()).getDownloadContentPagedListForEventSchedule(paramsMap);
   }

   public List getDownloadStatusForContentSchedule(int startPos, int pageSize, Map condition) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.put("program_id", condition.get("program_id"));
      paramsMap.put("device_id", condition.get("device_id"));
      paramsMap.put("stop_date", condition.get("stop_date"));
      paramsMap.put("limit", pageSize);
      paramsMap.put("offset", startPos > 0 ? startPos - 1 : 0);
      String sort = (String)condition.get("sort");
      String dir = (String)condition.get("dir");
      if (StringUtils.isNotBlank(sort) && StringUtils.isNotBlank(dir)) {
         paramsMap.put("sortColumn", sort.toUpperCase());
         paramsMap.put("sortOrder", dir.toUpperCase());
      } else {
         paramsMap.put("sortColumn", "A.CONTENT_NAME");
         paramsMap.put("sortOrder", "ASC");
      }

      return ((ScheduleInfoDAOMapper)this.getMapper()).getDownloadStatusPagedList(paramsMap);
   }

   public int getCntDownloadStatusForContentSchedule(int startPos, int pageSize, Map condition) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.put("program_id", condition.get("program_id"));
      paramsMap.put("device_id", condition.get("device_id"));
      paramsMap.put("stop_date", condition.get("stop_date"));
      paramsMap.put("limit", pageSize);
      paramsMap.put("offset", startPos > 0 ? startPos - 1 : 0);
      String sort = (String)condition.get("sort");
      String dir = (String)condition.get("dir");
      if (StringUtils.isNotBlank(sort) && StringUtils.isNotBlank(dir)) {
         paramsMap.put("sortColumn", sort.toUpperCase());
         paramsMap.put("sortOrder", dir.toUpperCase());
      } else {
         paramsMap.put("sortColumn", "A.CONTENT_NAME");
         paramsMap.put("sortOrder", "ASC");
      }

      return ((ScheduleInfoDAOMapper)this.getMapper()).getDownloadContentPagedListCount(paramsMap);
   }

   public int getCntDownloadStatusForEventSchedule(int startPos, int pageSize, Map condition) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.put("program_id", condition.get("program_id"));
      paramsMap.put("device_id", condition.get("device_id"));
      paramsMap.put("stop_date", condition.get("stop_date"));
      paramsMap.put("limit", pageSize);
      paramsMap.put("offset", startPos > 0 ? startPos - 1 : 0);
      String sort = (String)condition.get("sort");
      String dir = (String)condition.get("dir");
      if (StringUtils.isNotBlank(sort) && StringUtils.isNotBlank(dir)) {
         paramsMap.put("sortColumn", sort.toUpperCase());
         paramsMap.put("sortOrder", dir.toUpperCase());
      } else {
         paramsMap.put("sortColumn", "A.CONTENT_NAME");
         paramsMap.put("sortOrder", "ASC");
      }

      return ((ScheduleInfoDAOMapper)this.getMapper()).getDownloadContentPagedListCountForEventSchedule(paramsMap);
   }

   public PagedListInfo getEventList(int startPos, int pageSize, Map condition) throws SQLException {
      String scheduleId = (String)condition.get("scheduleId");
      String sort = (String)condition.get("sort");
      String dir = (String)condition.get("dir");
      String currDateStr = (String)condition.get("stop_date");
      if (currDateStr == null || currDateStr.equals("")) {
         Date currDate = new Date(System.currentTimeMillis());
         SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
         sf.format(currDate);
      }

      List result = ((ScheduleInfoDAOMapper)this.getMapper()).getEventListPaged(scheduleId, sort.toUpperCase(), dir.toUpperCase(), DaoTools.offsetStartPost(startPos), pageSize);
      Integer totCnt = ((ScheduleInfoDAOMapper)this.getMapper()).getEventCountByScheduleId(scheduleId);
      return new PagedListInfo(result, totCnt);
   }

   public long getContentScheduleCntToday() throws SQLException {
      Calendar curr_date = Calendar.getInstance();
      Calendar start_date = Calendar.getInstance();
      start_date.clear();
      start_date.set(1, curr_date.get(1));
      start_date.set(2, curr_date.get(2));
      start_date.set(5, curr_date.get(5));
      start_date.set(11, 0);
      start_date.set(12, 0);
      start_date.set(13, 0);
      Calendar end_date = Calendar.getInstance();
      end_date.clear();
      end_date.set(1, curr_date.get(1));
      end_date.set(2, curr_date.get(2));
      end_date.set(5, curr_date.get(5));
      end_date.set(11, 23);
      end_date.set(12, 59);
      end_date.set(13, 59);
      Timestamp start_time = new Timestamp(start_date.getTimeInMillis());
      Timestamp end_time = new Timestamp(end_date.getTimeInMillis());
      return ((ScheduleInfoDAOMapper)this.getMapper()).getContentScheduleCntToday(start_time, end_time);
   }

   public List getContentScheduleTodayGroupId() throws SQLException {
      Calendar curr_date = Calendar.getInstance();
      Calendar start_date = Calendar.getInstance();
      start_date.clear();
      start_date.set(1, curr_date.get(1));
      start_date.set(2, curr_date.get(2));
      start_date.set(5, curr_date.get(5));
      start_date.set(11, 0);
      start_date.set(12, 0);
      start_date.set(13, 0);
      Calendar end_date = Calendar.getInstance();
      end_date.clear();
      end_date.set(1, curr_date.get(1));
      end_date.set(2, curr_date.get(2));
      end_date.set(5, curr_date.get(5));
      end_date.set(11, 23);
      end_date.set(12, 59);
      end_date.set(13, 59);
      Timestamp start_time = new Timestamp(start_date.getTimeInMillis());
      Timestamp end_time = new Timestamp(end_date.getTimeInMillis());
      return ((ScheduleInfoDAOMapper)this.getMapper()).getContentScheduleTodayGroupId(start_time, end_time);
   }

   public long getContentScheduleCntThisWeek() throws SQLException {
      Calendar curr_date = Calendar.getInstance();
      Calendar start_date = Calendar.getInstance();
      start_date.clear();
      start_date.set(1, curr_date.get(1));
      start_date.set(2, curr_date.get(2));
      start_date.set(5, curr_date.get(5));
      start_date.set(11, 0);
      start_date.set(12, 0);
      start_date.set(13, 0);
      start_date.add(5, -6);
      Calendar end_date = Calendar.getInstance();
      end_date.clear();
      end_date.set(1, curr_date.get(1));
      end_date.set(2, curr_date.get(2));
      end_date.set(5, curr_date.get(5));
      end_date.set(11, 23);
      end_date.set(12, 59);
      end_date.set(13, 59);
      Timestamp start_time = new Timestamp(start_date.getTimeInMillis());
      Timestamp end_time = new Timestamp(end_date.getTimeInMillis());
      return ((ScheduleInfoDAOMapper)this.getMapper()).getContentScheduleCntThisWeek(start_time, end_time);
   }

   public long getAllScheduleCount() throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getAllScheduleCount();
   }

   public List getAllScheduleGroupId() throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getAllScheduleGroupId();
   }

   public String isDelete(String programId) throws SQLException {
      Map map = ((ScheduleInfoDAOMapper)this.getMapper()).isDelete(programId);
      return (String)map.get("deleted");
   }

   public long getMapedScheduleCount() throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getMappedScheduleCount();
   }

   public List getMapedScheduleGroupId() throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getMapedScheduleGroupId();
   }

   public long getNotMapedScheduleCount() throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getNotMapedScheduleCount();
   }

   public List getNotMapedScheduleGroupId() throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getNotMapedScheduleGroupId();
   }

   public boolean onProgramLayoutChange(String programId, String session_id, int channelNo, String layout_type, double resolution_x, double resolution_y) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var11;
      try {
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_onProgramLayoutChange(programId, session_id, channelNo);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_onProgramLayoutChange(programId, session_id, channelNo);
         String line_data;
         if (layout_type.equalsIgnoreCase("custom")) {
            line_data = "CustomLayout";
         } else {
            line_data = "ZeroFrameOnly";
         }

         if (((ScheduleInfoDAOMapper)this.getMapper(session)).update_onProgramLayoutChange(programId, session_id, channelNo, resolution_x, resolution_y, line_data) == 1) {
            session.commit();
            var11 = true;
            return var11;
         }

         session.rollback();
         var11 = false;
      } catch (SQLException var15) {
         session.rollback();
         throw var15;
      } finally {
         session.close();
      }

      return var11;
   }

   public List getFrameTemplates(String template_type, String resolution, String organization) throws SQLException {
      if (template_type.equalsIgnoreCase("FIXED") && !organization.equalsIgnoreCase("ROOT")) {
         return ((ScheduleInfoDAOMapper)this.getMapper()).select_getFrameTemplates(template_type, organization);
      } else {
         return template_type.equalsIgnoreCase("FIXED") && organization.equalsIgnoreCase("ROOT") ? ((ScheduleInfoDAOMapper)this.getMapper()).select2_getFrameTemplates(template_type) : ((ScheduleInfoDAOMapper)this.getMapper()).select3_getFrameTemplates(template_type, organization, resolution);
      }
   }

   public boolean saveFrameTemplate(FrameTemplateEntity fte) throws SQLException {
      long templateId = (long)SequenceDB.getNextValue("MI_CDS_INFO_FRAME_TEMPLATE");
      return ((ScheduleInfoDAOMapper)this.getMapper()).saveFrameTemplate(templateId, fte) == 1;
   }

   public long createFrameTemplate(FrameTemplateEntity fte) throws SQLException {
      long templateId = (long)SequenceDB.getNextValue("MI_CDS_INFO_FRAME_TEMPLATE");
      return ((ScheduleInfoDAOMapper)this.getMapper()).saveFrameTemplate(templateId, fte) == 1 ? templateId : -1L;
   }

   public boolean deleteFrameTemplate(Long template_id) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).deleteFrameTemplate(template_id) == 1;
   }

   public long checkAvailableDiskSpace(String programId, String session_id, String curr_date, boolean temp) throws SQLException {
      long device_space;
      if (temp) {
         device_space = ((ScheduleInfoDAOMapper)this.getMapper()).select_checkAvailableDiskSpace(programId);
      } else {
         device_space = ((ScheduleInfoDAOMapper)this.getMapper()).select2_checkAvailableDiskSpace(programId);
      }

      long content_size = ((ScheduleInfoDAOMapper)this.getMapper()).select3_checkAvailableDiskSpace(programId, session_id, curr_date);

      long default_content_size;
      try {
         if (temp) {
            default_content_size = ((ScheduleInfoDAOMapper)this.getMapper()).select4_checkAvailableDiskSpace(programId);
         } else {
            default_content_size = ((ScheduleInfoDAOMapper)this.getMapper()).select5_checkAvailableDiskSpace(programId);
         }
      } catch (Exception var15) {
         default_content_size = 0L;
      }

      long bgm_content_size;
      try {
         if (temp) {
            bgm_content_size = ((ScheduleInfoDAOMapper)this.getMapper()).select6_checkAvailableDiskSpace(programId);
         } else {
            bgm_content_size = ((ScheduleInfoDAOMapper)this.getMapper()).select7_checkAvailableDiskSpace(programId);
         }
      } catch (Exception var14) {
         bgm_content_size = 0L;
      }

      return device_space - content_size - default_content_size - bgm_content_size;
   }

   public String getProgramIdByProgramName(String programName) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getProgramIdByProgramName(programName);
   }

   public boolean deleteFrameByFrameId(String frameId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).deleteFrameByFrameId(frameId) == 1;
   }

   public boolean deleteContentScheduleByScheduleId(String scheduleId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).deleteContentScheduleByScheduleId(scheduleId) == 1;
   }

   public boolean addProgramWithFrameAndHWControlAndContent(ProgramEntity program, FrameEntity frame, ContentsScheduleEntity schedule, String sessionId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var31;
      try {
         boolean var7;
         try {
            boolean var23;
            if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert_addProgramWithFrameAndHWControlAndContent(program) != 1) {
               session.rollback();
               var23 = false;
               return var23;
            }

            if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert2_addProgramWithFrameAndHWControlAndContent(program) != 1) {
               session.rollback();
               var23 = false;
               return var23;
            }

            String deviceGroupIds = program.getDevice_group_ids();
            int var9;
            boolean var34;
            if (StringUtils.isNotBlank(deviceGroupIds)) {
               String[] var24 = deviceGroupIds.split(",");
               int var8 = var24.length;

               for(var9 = 0; var9 < var8; ++var9) {
                  String deviceGroupIdStr = var24[var9];
                  Long deviceGroupId = Long.parseLong(deviceGroupIdStr);
                  Map map = ((ScheduleInfoDAOMapper)this.getMapper(session)).select_addProgramWithFrameAndHWControlAndContent(deviceGroupId);
                  if (MapUtils.isNotEmpty(map) && map.get("default_program_id") != null && map.get("program_id") != null) {
                     String defaultProgramId = (String)map.get("default_program_id");
                     String programId = (String)map.get("program_id");
                     if (defaultProgramId.equals(programId)) {
                        ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_addProgramWithFrameAndHWControlAndContent(defaultProgramId);
                        if (((ScheduleInfoDAOMapper)this.getMapper(session)).update_addProgramWithFrameAndHWControlAndContent(defaultProgramId) != 1) {
                           session.rollback();
                           boolean var15 = false;
                           return var15;
                        }
                     }
                  }

                  ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_addProgramWithFrameAndHWControlAndContent(deviceGroupId);
                  if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert3_addProgramWithFrameAndHWControlAndContent(program.getProgram_id(), deviceGroupId) != 1) {
                     session.rollback();
                     var34 = false;
                     return var34;
                  }
               }
            }

            if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert4_addProgramWithFrameAndHWControlAndContent(frame) != 1) {
               session.rollback();
               var7 = false;
               return var7;
            }

            String userGroupIds = frame.getUser_group_ids();
            if (userGroupIds != null && !userGroupIds.equals("")) {
               String[] var26 = userGroupIds.split(",");
               var9 = var26.length;

               for(int var28 = 0; var28 < var9; ++var28) {
                  String userGroupIdStr = var26[var28];
                  Long userGroupId = Long.valueOf(userGroupIdStr);
                  if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert5_addProgramWithFrameAndHWControlAndContent(frame, userGroupId) != 1) {
                     session.rollback();
                     var34 = false;
                     return var34;
                  }
               }
            }

            long priority = 0L;
            Map map = ((ScheduleInfoDAOMapper)this.getMapper(session)).select2_addProgramWithFrameAndHWControlAndContent(schedule);
            if (MapUtils.isNotEmpty(map)) {
               priority = (Long)map.get("PRIORITY");
            }

            if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert6_addProgramWithFrameAndHWControlAndContent(schedule, priority + 1L) == 1) {
               long logId = (long)SequenceDB.getNextValue("MI_CDS_LOG_PROGRAM");
               if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert7_addProgramWithFrameAndHWControlAndContent(logId, program, "00") != 1) {
                  session.rollback();
                  var34 = false;
                  return var34;
               }

               session.commit();
               var34 = true;
               return var34;
            }

            session.rollback();
            var31 = false;
         } catch (SQLException var20) {
            session.rollback();
            throw var20;
         } catch (Exception var21) {
            session.rollback();
            var7 = false;
            return var7;
         }
      } finally {
         session.close();
      }

      return var31;
   }

   public boolean addProgramWithBasicInformation(ProgramEntity program, String sessionId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      try {
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert_addProgramWithBasicInformation(program) != 1) {
            session.rollback();
            session.close();
            return false;
         } else if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert2_addProgramWithBasicInformation(program) != 1) {
            session.rollback();
            session.close();
            return false;
         } else {
            if (program.getDevice_group_ids() != null) {
               String deviceGroupIds = program.getDevice_group_ids();
               if (!deviceGroupIds.equals("")) {
                  String[] var5 = deviceGroupIds.split(",");
                  int var6 = var5.length;

                  for(int var7 = 0; var7 < var6; ++var7) {
                     String deviceIdStr = var5[var7];
                     Long deviceId = Long.parseLong(deviceIdStr);
                     Map map = ((ScheduleInfoDAOMapper)this.getMapper(session)).select_addProgramWithBasicInformation(deviceId);
                     if (map != null && map.get("default_program_id") != null && map.get("program_id") != null) {
                        String defaultProgramId = (String)map.get("default_program_id");
                        String programId = (String)map.get("program_id");
                        if (defaultProgramId.equals(programId)) {
                           ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_addProgramWithBasicInformation(defaultProgramId);
                           if (((ScheduleInfoDAOMapper)this.getMapper(session)).update_addProgramWithBasicInformation(defaultProgramId) != 1) {
                              session.rollback();
                              session.close();
                              return false;
                           }
                        }
                     }

                     ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_addProgramWithBasicInformation(deviceId);
                     if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert3_addProgramWithBasicInformation(program.getProgram_id(), deviceId) != 1) {
                        session.rollback();
                        session.close();
                        return false;
                     }
                  }
               }
            }

            ChannelEntity channel = new ChannelEntity();
            channel.setProgram_id(program.getProgram_id());
            channel.setChannel_no(1);
            channel.setChannel_name("Channel");
            channel.setChannel_description("");
            if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert7_addProgramWithBasicInformation(channel) != 1) {
               session.rollback();
               session.close();
               return false;
            } else {
               FrameEntity frame = new FrameEntity();
               frame.setProgram_id(program.getProgram_id());
               frame.setScreen_index(0);
               frame.setFrame_id(UUID.randomUUID().toString());
               frame.setFrame_index(0);
               frame.setIs_main_frame("Y");
               frame.setX(0.0D);
               frame.setY(0.0D);
               frame.setWidth(100.0D);
               frame.setHeight(100.0D);
               frame.setLine_data("ZeroFrameOnly");
               frame.setVersion(1L);
               frame.setFrame_name("Frame 0");
               frame.setChannel_no(1);
               if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert4_addProgramWithBasicInformation(frame) != 1) {
                  session.rollback();
                  session.close();
                  return false;
               } else {
                  String userGroupIds = frame.getUser_group_ids();
                  if (StringUtils.isNotBlank(userGroupIds)) {
                     String[] var18 = userGroupIds.split(",");
                     int var20 = var18.length;

                     for(int var21 = 0; var21 < var20; ++var21) {
                        String userGroupId = var18[var21];
                        if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert5_addProgramWithBasicInformation(frame, Long.parseLong(userGroupId)) != 1) {
                           session.rollback();
                           session.close();
                           return false;
                        }
                     }
                  }

                  long logId = (long)SequenceDB.getNextValue("MI_CDS_LOG_PROGRAM");
                  if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert6_addProgramWithBasicInformation(logId, program, "00") != 1) {
                     session.rollback();
                     session.close();
                     return false;
                  } else {
                     session.commit();
                     session.close();
                     return true;
                  }
               }
            }
         }
      } catch (SQLException var13) {
         session.rollback();
         session.close();
         throw var13;
      } catch (Exception var14) {
         session.rollback();
         session.close();
         return false;
      }
   }

   public boolean modifyProgramWithFrameAndHWControlAndContent(ProgramEntity program, FrameEntity frame, ContentsScheduleEntity schedule, String sessionId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      try {
         boolean var23;
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).update_modifyProgramWithFrameAndHWControlAndContent(program) != 1) {
            session.rollback();
            var23 = false;
            return var23;
         } else if (((ScheduleInfoDAOMapper)this.getMapper(session)).update2_modifyProgramWithFrameAndHWControlAndContent(program) != 1) {
            session.rollback();
            var23 = false;
            return var23;
         } else {
            String userGroupIds;
            int var9;
            String[] userGroupIdsArr;
            if (program.getDevice_group_ids() != null) {
               userGroupIds = program.getDevice_group_ids();
               if (!userGroupIds.equals("")) {
                  userGroupIdsArr = userGroupIds.split(",");
                  int var8 = userGroupIdsArr.length;

                  for(var9 = 0; var9 < var8; ++var9) {
                     String groupIdStr = userGroupIdsArr[var9];
                     Long groupId = Long.valueOf(groupIdStr);
                     Map map = ((ScheduleInfoDAOMapper)this.getMapper(session)).select_modifyProgramWithFrameAndHWControlAndContent(groupId);
                     if (map != null && map.get("default_program_id") != null && map.get("program_id") != null) {
                        String defaultProgramId = (String)map.get("default_program_id");
                        String programId = (String)map.get("program_id");
                        if (defaultProgramId.equals(programId)) {
                           ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_modifyProgramWithFrameAndHWControlAndContent(defaultProgramId);
                           if (((ScheduleInfoDAOMapper)this.getMapper(session)).update3_modifyProgramWithFrameAndHWControlAndContent(defaultProgramId) != 1) {
                              session.rollback();
                              boolean var15 = false;
                              return var15;
                           }
                        }
                     }

                     ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_modifyProgramWithFrameAndHWControlAndContent(groupId);
                     if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert_modifyProgramWithFrameAndHWControlAndContent(program.getProgram_id(), groupId) != 1) {
                        session.rollback();
                        boolean var34 = false;
                        return var34;
                     }
                  }
               }
            }

            if (((ScheduleInfoDAOMapper)this.getMapper(session)).update4_modifyProgramWithFrameAndHWControlAndContent(frame) != 1) {
               session.rollback();
               var23 = false;
               return var23;
            } else {
               userGroupIds = frame.getUser_group_ids();
               boolean var33;
               if (StringUtils.isNotBlank(userGroupIds)) {
                  userGroupIdsArr = userGroupIds.split(",");
                  String[] var26 = userGroupIdsArr;
                  var9 = userGroupIdsArr.length;

                  for(int var29 = 0; var29 < var9; ++var29) {
                     String userGroupIdStr = var26[var29];
                     if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert2_modifyProgramWithFrameAndHWControlAndContent(frame, Long.valueOf(userGroupIdStr)) != 1) {
                        session.rollback();
                        var33 = false;
                        return var33;
                     }
                  }
               }

               long priority = 0L;
               if (((ScheduleInfoDAOMapper)this.getMapper(session)).update5_modifyProgramWithFrameAndHWControlAndContent(schedule) != 1) {
                  session.rollback();
                  boolean var28 = false;
                  return var28;
               } else {
                  Map map = ((ScheduleInfoDAOMapper)this.getMapper(session)).select2_modifyProgramWithFrameAndHWControlAndContent(schedule);
                  if (map != null && map.size() > 0) {
                     priority = (Long)map.get("PRIORITY") + 1L;
                  }

                  if (((ScheduleInfoDAOMapper)this.getMapper(session)).update6_modifyProgramWithFrameAndHWControlAndContent(schedule, priority) != 1) {
                     session.rollback();
                     boolean var31 = false;
                     return var31;
                  } else {
                     long logId = (long)SequenceDB.getNextValue("MI_CDS_LOG_PROGRAM");
                     if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert3_modifyProgramWithFrameAndHWControlAndContent(logId, program, "01") != 1) {
                        session.rollback();
                        var33 = false;
                        return var33;
                     } else {
                        session.commit();
                        var33 = true;
                        return var33;
                     }
                  }
               }
            }
         }
      } catch (SQLException var20) {
         session.rollback();
         throw var20;
      } catch (Exception var21) {
         session.rollback();
         boolean var7 = false;
         return var7;
      } finally {
         session.close();
      }
   }

   public boolean modifyProgramWithBasicInformation(ProgramEntity program, String sessionId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var24;
      try {
         boolean var22;
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).update_modifyProgramWithBasicInformation(program) != 1) {
            session.rollback();
            var22 = false;
            return var22;
         }

         if (((ScheduleInfoDAOMapper)this.getMapper(session)).update2_modifyProgramWithBasicInformation(program) != 1) {
            session.rollback();
            var22 = false;
            return var22;
         }

         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_modifyProgramWithBasicInformation(program.getProgram_id());
         if (program.getDevice_group_ids() != null) {
            String deviceGroupIds = program.getDevice_group_ids();
            if (!deviceGroupIds.equals("")) {
               String[] var23 = deviceGroupIds.split(",");
               int var6 = var23.length;

               for(int var7 = 0; var7 < var6; ++var7) {
                  String deviceGroupIdStr = var23[var7];
                  Long deviceGroupId = Long.valueOf(deviceGroupIdStr);
                  Map map = ((ScheduleInfoDAOMapper)this.getMapper(session)).select_modifyProgramWithBasicInformation(deviceGroupId);
                  if (map != null && map.get("default_program_id") != null && map.get("program_id") != null) {
                     String defaultProgramId = (String)map.get("default_program_id");
                     String programId = (String)map.get("program_id");
                     if (defaultProgramId.equals(programId)) {
                        ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_modifyProgramWithBasicInformation(defaultProgramId);
                        if (((ScheduleInfoDAOMapper)this.getMapper(session)).update3_modifyProgramWithBasicInformation(defaultProgramId) != 1) {
                           session.rollback();
                           boolean var13 = false;
                           return var13;
                        }
                     }
                  }

                  ((ScheduleInfoDAOMapper)this.getMapper(session)).delete3_modifyProgramWithBasicInformation(deviceGroupId);
                  if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert_modifyProgramWithBasicInformation(program.getProgram_id(), deviceGroupId) != 1) {
                     session.rollback();
                     boolean var25 = false;
                     return var25;
                  }
               }
            }
         }

         long logId = (long)SequenceDB.getNextValue("MI_CDS_LOG_PROGRAM");
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert2_modifyProgramWithBasicInformation(logId, program, "01") == 1) {
            session.commit();
            var24 = true;
            return var24;
         }

         session.rollback();
         var24 = false;
      } catch (SQLException var18) {
         session.rollback();
         throw var18;
      } catch (Exception var19) {
         session.rollback();
         boolean var5 = false;
         return var5;
      } finally {
         session.close();
      }

      return var24;
   }

   public boolean addContentScheduleWithoutTemp(ContentsScheduleEntity schedule) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var7;
      try {
         long priority = 0L;
         Map map = ((ScheduleInfoDAOMapper)this.getMapper(session)).select_addContentScheduleWithoutTemp(schedule);
         if (map != null && map.size() > 0) {
            priority = (Long)map.get("PRIORITY");
         }

         int cntForCS;
         if (schedule.getPlayer_mode() != null) {
            cntForCS = ((ScheduleInfoDAOMapper)this.getMapper(session)).insert_addContentScheduleWithoutTemp(schedule, priority + 1L, schedule.getPlayer_mode());
         } else {
            cntForCS = ((ScheduleInfoDAOMapper)this.getMapper(session)).insert_addContentScheduleWithoutTemp(schedule, priority + 1L, "single");
         }

         if (cntForCS == 1) {
            session.commit();
            var7 = cntForCS > 0;
            return var7;
         }

         session.rollback();
         var7 = false;
      } catch (SQLException var11) {
         session.rollback();
         throw var11;
      } finally {
         session.close();
      }

      return var7;
   }

   public boolean addHWConstraint(ContentsScheduleEntity schedule) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var4;
      try {
         int cntForCS = ((ScheduleInfoDAOMapper)this.getMapper(session)).insert_addHWConstraint(schedule, 1);
         if (cntForCS != 1) {
            session.rollback();
            var4 = false;
            return var4;
         }

         session.commit();
         var4 = cntForCS > 0;
      } catch (SQLException var8) {
         session.rollback();
         throw var8;
      } finally {
         session.close();
      }

      return var4;
   }

   public boolean modifyContentScheduleWithoutTemp(ContentsScheduleEntity schedule) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var7;
      try {
         long priority = 0L;
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).update_modifyContentScheduleWithoutTemp(schedule) != 1) {
            session.rollback();
            boolean var13 = false;
            return var13;
         }

         Map map = ((ScheduleInfoDAOMapper)this.getMapper(session)).select_modifyContentScheduleWithoutTemp(schedule);
         if (map != null && map.size() > 0) {
            priority = (Long)map.get("PRIORITY") + 1L;
         }

         int cntForCS = ((ScheduleInfoDAOMapper)this.getMapper(session)).update2_modifyContentScheduleWithoutTemp(schedule, priority);
         if (cntForCS == 1) {
            session.commit();
            var7 = cntForCS > 0;
            return var7;
         }

         session.rollback();
         var7 = false;
      } catch (SQLException var11) {
         session.rollback();
         throw var11;
      } finally {
         session.close();
      }

      return var7;
   }

   public boolean modifyHWConstraint(ContentsScheduleEntity schedule) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var4;
      try {
         int cntForCS = ((ScheduleInfoDAOMapper)this.getMapper(session)).update_modifyHWConstraint(schedule);
         if (cntForCS != 1) {
            session.rollback();
            var4 = false;
            return var4;
         }

         session.commit();
         var4 = cntForCS > 0;
      } catch (SQLException var8) {
         session.rollback();
         throw var8;
      } finally {
         session.close();
      }

      return var4;
   }

   public List getContentScheduleIdByFrameId(String frameId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getContentScheduleIdByFrameId(frameId);
   }

   public boolean addFrameWithoutTemp(FrameEntity frame) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var15;
      try {
         if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert_addFrameWithoutTemp(frame) != 1) {
            session.rollback();
            boolean var14 = false;
            return var14;
         }

         String userGroupIds = frame.getUser_group_ids();
         if (StringUtils.isNotBlank(userGroupIds)) {
            String[] var4 = userGroupIds.split(",");
            int var5 = var4.length;

            for(int var6 = 0; var6 < var5; ++var6) {
               String userGroupId = var4[var6];
               if (((ScheduleInfoDAOMapper)this.getMapper(session)).insert2_addFrameWithoutTemp(frame, Long.parseLong(userGroupId)) != 1) {
                  session.rollback();
                  boolean var8 = false;
                  return var8;
               }
            }
         }

         session.commit();
         var15 = true;
      } catch (SQLException var12) {
         session.rollback();
         throw var12;
      } finally {
         session.close();
      }

      return var15;
   }

   public int getFrameIndexByFrameId(String frameId) throws SQLException {
      Long result = 0L;

      try {
         result = ((ScheduleInfoDAOMapper)this.getMapper()).getFrameIndexByFrameId(frameId);
      } catch (Exception var4) {
         this.logger.error("", var4);
      }

      return result.intValue();
   }

   public String getDeviceTypeByProgramId(String programId) throws SQLException {
      String result = "";

      try {
         result = ((ScheduleInfoDAOMapper)this.getMapper()).getDeviceTypeByProgramId(programId);
      } catch (Exception var4) {
         this.logger.error("", var4);
      }

      return StringUtils.isNotBlank(result) ? result : "SPLAYER";
   }

   public Float getDeviceTypeVersionByProgramId(String programId) {
      Float returnValue = CommonDataConstants.TYPE_VERSION_1_0;
      Float result = 0.0F;

      try {
         result = ((ScheduleInfoDAOMapper)this.getMapper()).getDeviceTypeVersionByProgramId(programId);
      } catch (Exception var5) {
         this.logger.error("", var5);
      }

      if (result != null && result > 0.0F) {
         returnValue = result;
      }

      return returnValue;
   }

   public int setLinedataByProgramId(String programId, int channelNo, String lineData) throws SQLException {
      try {
         return ((ScheduleInfoDAOMapper)this.getMapper()).setLinedataByProgramId(lineData, programId, channelNo);
      } catch (Exception var5) {
         this.logger.error("", var5);
         return 0;
      }
   }

   public int setLinedataToZeroFrame(String programId, int channelNo) throws SQLException {
      try {
         return ((ScheduleInfoDAOMapper)this.getMapper()).setLinedataToZeroFrame(programId, channelNo);
      } catch (Exception var4) {
         this.logger.error("", var4);
         return 0;
      }
   }

   public String getProgramIdByFrameId(String frameId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getProgramIdByFrameId(frameId);
   }

   public String getProgramIdByByScheduleId(String scheduleId) throws SQLException {
      List programId = ((ScheduleInfoDAOMapper)this.getMapper()).getProgramIdByByScheduleId(scheduleId);
      return CollectionUtils.isNotEmpty(programId) ? (String)((Map)programId.get(0)).get("program_id") : null;
   }

   public boolean updateDefaultProgramDeviceType(Long device_group_id, String device_type) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).updateDefaultProgramDeviceType(device_group_id, device_type) == 1;
   }

   public boolean updateDefaultProgramDeviceType(Long device_group_id, String device_type, SqlSession session) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper(session)).updateDefaultProgramDeviceType(device_group_id, device_type) == 1;
   }

   public String getCreatorIdByProgramId(String programId) throws SQLException {
      String retVal = "";
      String result = ((ScheduleInfoDAOMapper)this.getMapper()).getCreatorIdByProgramId(programId);
      if (result != null) {
         retVal = result;
      }

      return retVal;
   }

   public List getProgramListBySchOrgId(int schOrgId) throws SQLException {
      new ArrayList();
      ProgramGroupInfo programGroupInfo = ProgramGroupInfoImpl.getInstance();
      List tempGroupList = programGroupInfo.getChildGroupList(schOrgId, true);
      return tempGroupList.size() < 1 ? null : ((ScheduleInfoDAOMapper)this.getMapper()).getProgramListBySchOrgId(tempGroupList);
   }

   public List getChannelListByProgramId(String program_id) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getChannels(program_id);
   }

   public boolean deleteFrameByChannelNo(String programId, int channelNo) throws SQLException {
      ((ScheduleInfoDAOMapper)this.getMapper()).deleteFrameByChannelNo(programId, channelNo);
      return true;
   }

   public boolean deleteScheduleByChannelNo(String programId, int channel_no) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).deleteScheduleByChannelNo(programId, channel_no);
   }

   public boolean setDefaultProgramId(long groupId, String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).update_setDefaultProgramId(programId, groupId) == 1;
   }

   public boolean modifyProgramDeviceTypeAndVersion(String programId, String deviceType, float deviceTypeVersion) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).modifyProgramDeviceTypeAndVersion(programId, deviceType, deviceTypeVersion) == 1;
   }

   public Map getConstants() {
      return ScheduleConstants.getConstantsMap();
   }

   public List getScheduleDetailPublishStatusList(String programId, long device_group_id) {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getScheduleDetailPublishStatusList(programId, device_group_id);
   }

   public List getContentListInSchedule(String programId) {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getContentListInSchedule(programId);
   }

   public void addContentPublishData(DetailDownloadContentEntity detailDownloadContentEntity) throws SQLException {
      int cnt = ((ScheduleInfoDAOMapper)this.getMapper()).getContentPublishDataCount(detailDownloadContentEntity);
      if (cnt > 0) {
         ((ScheduleInfoDAOMapper)this.getMapper()).updateContentPublishDataProgress(detailDownloadContentEntity);
      } else {
         ((ScheduleInfoDAOMapper)this.getMapper()).addContentPublishData(detailDownloadContentEntity);
      }

   }

   public int getScheduleDetailProgress(String programId) throws Exception {
      int progress = 0;
      List lists = ((ScheduleInfoDAOMapper)this.getMapper()).getScheduleDetailProgress(programId);

      for(int i = 0; i < lists.size(); ++i) {
         if (lists.get(i) != null && !((String)lists.get(i)).isEmpty()) {
            String[] temp = ((String)lists.get(i)).split(" ");
            progress += Integer.valueOf(temp[0]);
         }
      }

      if (lists.size() > 0) {
         progress /= lists.size();
      }

      return progress;
   }

   public void deleteContentPublishData(String programId) throws SQLException {
      ((ScheduleInfoDAOMapper)this.getMapper()).deleteContentPublishData(programId);
   }

   public int getContentPublishDataCount(DetailDownloadContentEntity detailDownloadContentEntity) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getContentPublishDataCount(detailDownloadContentEntity);
   }

   public List getSchedulePublishStatusList(String programId, long device_group_id) {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getSchedulePublishStatusList(programId, device_group_id);
   }

   public int getCheckContentPublishCount(String programId) {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getCheckContentPublishCount(programId);
   }

   public int addDynaminTagInfoTemp(DynamicTagEntity entity) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).addDynaminTagInfoTemp(entity);
   }

   public int addDynaminTagInfo(String scheduleId, String sessionId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).addDynaminTagInfo(scheduleId, sessionId);
   }

   public boolean deleteDynaminTagInfoTemp(String scheduleId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).deleteDynaminTagInfoTemp(scheduleId);
   }

   public boolean deleteDynaminTagInfo(String scheduleId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).deleteDynaminTagInfo(scheduleId);
   }

   public List getDynaminTagInfo(String scheduleId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getDynaminTagInfo(scheduleId);
   }

   public List getDynaminTagInfoTemp(String sessionId, String scheduleId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getDynaminTagInfoTemp(sessionId, scheduleId);
   }

   public List getTagListWithIsSync(String scheduleId, String playlistId, long versionId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getTagListWithIsSync(scheduleId, playlistId, versionId);
   }

   public List getTagListForContent(String scheduleId, String playlistId, String syncPlayId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getTagListForContent(scheduleId, playlistId, syncPlayId);
   }

   public boolean deleteAllProgramData(String programId) {
      SqlSession session = this.openNewSession(false);

      try {
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete_deleteProgramDataWithId(programId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete1_deleteProgramDataWithId(programId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete2_deleteProgramDataWithId(programId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).delete3_deleteProgramDataWithId(programId);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).deleteAllChannelsWithId(programId);
         session.commit();
      } catch (SQLException var7) {
         session.rollback();
         this.logger.error(var7);
      } finally {
         session.close();
      }

      return true;
   }

   public boolean addDynaminTagInfoList(List tagList) throws SQLException {
      SqlSession session = this.openNewSession(false);

      try {
         Iterator var3 = tagList.iterator();

         while(var3.hasNext()) {
            DynamicTagEntity tagEntity = (DynamicTagEntity)var3.next();
            ((ScheduleInfoDAOMapper)this.getMapper(session)).addDynaminTagInfoList(tagEntity);
         }

         session.commit();
      } catch (SQLException var8) {
         session.rollback();
         this.logger.error(var8);
      } finally {
         session.close();
      }

      return true;
   }

   public List getDeletedProgramIdList(String organization) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getDeletedProgramIdList(organization);
   }

   public boolean updateProgramView(ProgramEntity program) throws SQLException {
      SqlSession session = this.openNewSession(false);

      try {
         ((ScheduleInfoDAOMapper)this.getMapper(session)).updateProgramView(program);
         ((ScheduleInfoDAOMapper)this.getMapper(session)).updateProgramGroupId(program.getProgram_id(), program.getProgram_group_id());
         session.commit();
      } catch (SQLException var7) {
         session.rollback();
      } finally {
         session.close();
      }

      return true;
   }

   public boolean updateProgramGroupId(String programId, long groupId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).updateProgramGroupId(programId, groupId);
   }

   public List getScheduleMappedContentTotalSize(String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getScheduleMappedContentTotalSize(programId, "00");
   }

   public FrameTemplateEntity getTemplateEntity(long templateId) throws Exception {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getTemplateEntity(templateId);
   }

   public List getAdSlotList(String programId, String frameId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getAdSlotList(programId, frameId);
   }

   public List getAdSlotList(String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getAdSlotListFromProgramId(programId);
   }

   public List getAdScheduleList(String programId, String slotId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getAdScheduleList(programId, slotId);
   }

   public boolean updateTemplate(FrameTemplateEntity template) throws Exception {
      return ((ScheduleInfoDAOMapper)this.getMapper()).updateTemplate(template);
   }

   public List getReserveScheduleList() throws Exception {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getReserveScheduleList();
   }

   public List getProgramIdListForVWL() throws Exception {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getProgramIdListForVWL();
   }

   public boolean updateProgramType(String programId, String programType) throws Exception {
      return ((ScheduleInfoDAOMapper)this.getMapper()).updateProgramTypeFromProgramId(programId, programType);
   }

   public boolean updateProgramTypeFromLFD(String fromProgramType, String toProgramType) throws Exception {
      return ((ScheduleInfoDAOMapper)this.getMapper()).updateProgramTypeFromProgramType(fromProgramType, toProgramType);
   }

   public boolean updateProgramTypeFromSyncPlay(String programType) throws Exception {
      return ((ScheduleInfoDAOMapper)this.getMapper()).updateProgramTypeFromSyncPlay(programType);
   }

   public boolean updateProgramTypeFromAdvertisement(String programType) throws Exception {
      return ((ScheduleInfoDAOMapper)this.getMapper()).updateProgramTypeFromAdvertisement(programType);
   }

   public int getCountProgram(String programType) throws Exception {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getCountProgram(programType);
   }

   public List getTagPlaylistIdVersion(String programid) throws Exception {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getTagPlaylistIdVersion(programid, "PLAYLIST");
   }

   public int getCountProgramIdByGroupId(long groupId) throws Exception {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getCountProgramIdByGroupId(groupId);
   }

   public List getContentListInScheduleWithStopDate(String programId, String stopDate) throws Exception {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getContentListInScheduleWithStopDate(programId, stopDate);
   }

   public List getContentListInADScheduleWithStopDate(String programId, String stopDate) throws Exception {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getContentListInADScheduleWithStopDate(programId, stopDate);
   }

   public List getDynamicTagByScheduleIdIdAndPlaylistId(String programId, String playlistId) throws Exception {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getDynamicTagByScheduleIdIdAndPlaylistId(programId, playlistId);
   }

   public String getOrganiationByProgramId(String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getOrganiationByProgramId(programId);
   }

   public long existsProgramId(String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).existsProgramId(programId);
   }

   public ProgramEntity getProgramWithGroupIdAndNameByProgramId(String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getProgramWithGroupIdAndNameByProgramId(programId);
   }

   public int getCountScheduleToExpire(List orgGroupIds, String userId, String stopDate, SelectCondition condition) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getCountScheduleToExpire(orgGroupIds, userId, stopDate, condition);
   }

   public int getDeviceCountByScheduleToExpire(List orgGroupIds, String userId, String stopDate, SelectCondition condition) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getDeviceCountByScheduleToExpire(orgGroupIds, userId, stopDate, condition);
   }

   public List getListScheduleToExpire(int startPos, int pageSize, List orgGroupIds, String userId, String stopDate, SelectCondition condition) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getListScheduleToExpire(startPos, pageSize, orgGroupIds, stopDate, condition);
   }

   public List getDeviceListByScheduleToExpire(int startPos, int pageSize, List orgGroupIds, String userId, String stopDate, SelectCondition condition) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getDeviceListByScheduleToExpire(startPos, pageSize, orgGroupIds, stopDate, condition);
   }

   public Long getMaxPriorityByProgramId(String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getMaxPriorityByProgramId(programId);
   }

   public Long getMinPriorityByProgramId(String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getMinPriorityByProgramId(programId);
   }

   public Long getPriorityByScheduleId(String programId, String scheduleId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getPriorityByScheduleId(programId, scheduleId);
   }

   public List getScheduleIdAndPriorityByProgramId(String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getScheduleIdAndPriorityByProgramId(programId);
   }

   public boolean updateSchedulePriorityByProgramId(String programId, List scheduleMapList) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var14;
      try {
         Iterator var4 = scheduleMapList.iterator();

         while(var4.hasNext()) {
            Map scheduleMapItem = (Map)var4.next();
            String scheduleId = scheduleMapItem.get("schedule_id").toString();
            Long priority = Long.parseLong(scheduleMapItem.get("priority").toString());
            if (((ScheduleInfoDAOMapper)this.getMapper(session)).updateSchedulePriorityByProgramId(programId, scheduleId, priority) != 1) {
               session.rollback();
               boolean var8 = false;
               return var8;
            }
         }

         session.commit();
         var14 = true;
      } catch (SQLException var12) {
         session.rollback();
         throw var12;
      } finally {
         session.close();
      }

      return var14;
   }

   public List getProgramByExpiredContentId(String contentId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getProgramByExpiredContentId(contentId);
   }

   public boolean deleteExpiredContentInProgram(String programId, String contentId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).deleteExpiredContentInProgram(programId, contentId);
   }

   public boolean deleteExpiredContentInFrame(String programId, int channelNo, int screenIndex, String frameId, String contentId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).deleteExpiredContentInFrame(programId, channelNo, screenIndex, frameId, contentId);
   }

   public boolean deleteExpiredContentInSchedule(String scheduleId, String contentId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).deleteExpiredContentInSchedule(scheduleId, contentId);
   }

   public boolean deletePlaylistInSchedule(String programType, String scheduleId, String playlistId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).deletePlaylistInSchedule(programType, scheduleId, playlistId);
   }

   public List getPlaylistByExpiredContentId(String contentId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getPlaylistByExpiredContentId(contentId);
   }

   public List getContentListByProgramId(String programId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getContentListByProgramId(programId);
   }

   public List getScheduleByPlaylistId(String playlistId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getScheduleByPlaylistId(playlistId);
   }

   public List getFrameContentsByProgramId(String programId) throws Exception {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getFrameContentsByProgramId(programId);
   }

   public List getScheduleGroupBySearchText(String searchText, String organizationName, String table) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getScheduleGroupBySearchText(searchText, organizationName, table);
   }

   public List getParentsGroupList(int pGroupId, String table) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getParentsGroupList(pGroupId, table);
   }

   public List getProgramCountByProgramType() throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).getProgramCountByProgramType();
   }

   public int deleteBgmContentInProgram(String contentId) throws SQLException {
      return ((ScheduleInfoDAOMapper)this.getMapper()).deleteBgmContentInProgram(contentId);
   }
}
