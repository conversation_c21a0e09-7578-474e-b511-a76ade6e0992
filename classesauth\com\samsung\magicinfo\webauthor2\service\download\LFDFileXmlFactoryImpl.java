package com.samsung.magicinfo.webauthor2.service.download;

import com.samsung.magicinfo.webauthor2.xml.lfd.FileItem;
import com.samsung.magicinfo.webauthor2.xml.lfd.FileItems;
import com.samsung.magicinfo.webauthor2.xml.lfd.SupportFileItem;
import com.samsung.magicinfo.webauthor2.xml.lfd.SupportFileItems;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;
import javax.xml.stream.XMLEventReader;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.transform.Source;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.xml.transform.StringSource;

@Service
public class LFDFileXmlFactoryImpl implements LFDFileXmlFactory {
  private static final Logger logger = LoggerFactory.getLogger(LFDFileXmlFactoryImpl.class);
  
  public List<SupportFileItem> unmarshalSupportFileItems(String lfdXml) {
    List<SupportFileItem> result = new ArrayList<>();
    SupportFileItems sfis = new SupportFileItems();
    sfis.setFileItems(result);
    try {
      JAXBContext jc = JAXBContext.newInstance(new Class[] { SupportFileItems.class });
      Unmarshaller unmarshaller = jc.createUnmarshaller();
      StringSource stringSource = new StringSource(lfdXml);
      XMLInputFactory inputFactory = XMLInputFactory.newFactory();
      XMLEventReader xer = inputFactory.createXMLEventReader((Source)stringSource);
      while (xer.hasNext()) {
        if (xer.peek().isStartElement() && xer.peek().asStartElement().getName().getLocalPart().equals("SupportFileItems"))
          sfis = (SupportFileItems)unmarshaller.unmarshal(xer); 
        xer.nextEvent();
      } 
      xer.close();
    } catch (XMLStreamException|javax.xml.bind.JAXBException ex) {
      logger.error("Error parsing LFD XML:", ex.getMessage());
    } 
    return sfis.getFileItems();
  }
  
  public List<FileItem> unmarshalFileItems(String lfdXml) {
    List<FileItem> result = new ArrayList<>();
    FileItems items = new FileItems();
    items.setFileItems(result);
    try {
      JAXBContext jc = JAXBContext.newInstance(new Class[] { FileItems.class });
      Unmarshaller unmarshaller = jc.createUnmarshaller();
      StringSource stringSource = new StringSource(lfdXml);
      XMLInputFactory inputFactory = XMLInputFactory.newFactory();
      XMLEventReader xer = inputFactory.createXMLEventReader((Source)stringSource);
      while (xer.hasNext()) {
        if (xer.peek().isStartElement() && xer.peek().asStartElement().getName().getLocalPart().equals("FileItems"))
          items = (FileItems)unmarshaller.unmarshal(xer); 
        xer.nextEvent();
      } 
      xer.close();
    } catch (XMLStreamException|javax.xml.bind.JAXBException ex) {
      logger.error("Error parsing LFD XML:", ex.getMessage());
    } 
    return items.getFileItems();
  }
  
  public String unmarshalStartPage(String lfdXml) {
    String result = null;
    try {
      StringSource stringSource = new StringSource(lfdXml);
      XMLInputFactory inputFactory = XMLInputFactory.newFactory();
      XMLEventReader xer = inputFactory.createXMLEventReader((Source)stringSource);
      while (xer.hasNext()) {
        if (xer.peek().isStartElement() && xer.peek().asStartElement().getName().getLocalPart().equals("StartupPage")) {
          xer.nextTag();
          xer.nextTag();
          result = xer.peek().toString();
          break;
        } 
        xer.nextEvent();
      } 
      xer.close();
    } catch (XMLStreamException ex) {
      logger.error("Error parsing LFD XML:", ex.getMessage());
    } 
    return result;
  }
}
