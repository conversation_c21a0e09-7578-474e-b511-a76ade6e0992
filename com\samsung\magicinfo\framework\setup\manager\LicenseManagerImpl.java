package com.samsung.magicinfo.framework.setup.manager;

import com.ice.jni.registry.RegStringValue;
import com.ice.jni.registry.Registry;
import com.ice.jni.registry.RegistryKey;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.license.Mklicense;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.setup.dao.LicenseDao;
import com.samsung.magicinfo.framework.setup.entity.LicenseEntity;
import com.samsung.magicinfo.openapi.custom.openEntity.etc.MobileLicenseEntity;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.io.BufferedReader;
import java.io.DataInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStreamReader;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.logging.log4j.Logger;

public class LicenseManagerImpl implements LicenseManager {
   private Logger logger = LoggingManagerV2.getLogger(LicenseManagerImpl.class);
   LicenseDao dao = null;

   public static String deVal(int val) {
      String rtnStr = "";
      String[] arAlpha = new String[]{"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};
      if (val > 9) {
         val -= 10;
         rtnStr = arAlpha[val];
      } else {
         rtnStr = Integer.toString(val);
      }

      return rtnStr;
   }

   public static int enInt(String val) throws Exception {
      int rtn = 0;
      String[] arAlpha = new String[]{"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};

      for(int i = 0; i < arAlpha.length; ++i) {
         if (arAlpha[i].equals(val.toUpperCase())) {
            rtn = i + 10;
         }
      }

      if (rtn == 0) {
         rtn = Integer.parseInt(val);
      }

      return rtn;
   }

   public static LicenseManagerImpl getInstance() {
      return new LicenseManagerImpl();
   }

   private LicenseManagerImpl() {
      super();
      this.dao = new LicenseDao();
   }

   public boolean addLicenseInfoHistory(LicenseEntity license) throws SQLException, ConfigException {
      List license_key = null;
      boolean returnValue = true;
      String lfdStr = Mklicense.getEncodeLic("UFB1FB49V7BPNQHSI69Q", "01-01-01-01-01-01");
      String liteKey = Mklicense.getEncodeLic("ZLX4NT6YUZS37PC6KBQT", "01-01-01-01-01-01");
      if (!license.getProduct_key().equalsIgnoreCase(lfdStr) && !license.getProduct_key().equalsIgnoreCase(liteKey)) {
         returnValue = this.dao.addLicenseInfoHistory(license);
      } else {
         if (license.getSw_grade().equalsIgnoreCase("P")) {
            license_key = this.dao.getLicenseInfoHistoryByProductKey(lfdStr);
         } else {
            license_key = this.dao.getLicenseInfoHistoryByProductKey(liteKey);
         }

         if (license_key.size() > 0) {
            System.out.println("already exists in History ");
            return false;
         }

         returnValue = this.dao.addLicenseInfoHistory(license);
      }

      return returnValue;
   }

   public boolean chkLicenseValue(String license_type, String license_key) throws SQLException, ConfigException {
      return this.dao.chkLicenseValue(license_type, license_key);
   }

   public String decodeKey(String val) {
      String[] arList = new String[]{"18", "42", "23", "74", "85", "16", "37", "68", "29", "91", "14", "22", "44", "27", "51", "76", "67", "89", "25", "32", "35", "77", "04", "97", "53", "75", "63", "00", "09", "01", "54", "45", "88", "17", "62", "94"};
      String rtnStr = "";
      if (val != null && val.indexOf("-") > 0) {
         int kpos = 0;
         long ksum = 0L;
         String[] val1 = val.split("-");
         String knum1 = "";
         String knum2 = "";
         String knum3 = "";
         long dval1 = 0L;
         long dval2 = 0L;
         knum1 = val1[0].substring(0, 1);
         knum2 = val1[1].substring(0, 1);
         knum3 = val1[2].substring(0, 1);
         dval1 = Long.parseLong(val1[0].substring(1, val1[0].length())) / (long)Integer.parseInt(knum1);
         dval2 = Long.parseLong(val1[1].substring(1, val1[1].length())) / (long)Integer.parseInt(knum2);
         String keyval = Long.toString(dval1) + Long.toString(dval2);

         for(int i = 0; i < 12; ++i) {
            int k = i * 2;
            String tval1 = keyval.substring(k, k + 2);

            for(int j = 0; j < arList.length; ++j) {
               if (arList[j].equals(tval1)) {
                  ksum += (long)Integer.parseInt(arList[j]);
                  kpos = j;
                  break;
               }
            }

            if (i > 0 && i % 2 == 0) {
               rtnStr = rtnStr + "-";
            }

            rtnStr = rtnStr + deVal(kpos);
         }

         ksum = Long.parseLong(val1[2].substring(1, val1[2].length())) / (long)Integer.parseInt(knum3) - ksum;
         rtnStr = rtnStr + "-" + Long.toString(ksum);
      }

      return rtnStr;
   }

   public String encodeKey(String macaddr, String cnt) throws Exception {
      String[] arList = new String[]{"18", "42", "23", "74", "85", "16", "37", "68", "29", "91", "14", "22", "44", "27", "51", "76", "67", "89", "25", "32", "35", "77", "04", "97", "53", "75", "63", "00", "09", "01", "54", "45", "88", "17", "62", "94"};
      Random rand = new Random(System.currentTimeMillis());
      long chkVal1 = (long)Math.abs(rand.nextInt(8) + 1);
      long chkVal2 = (long)Math.abs(rand.nextInt(8) + 1);
      long chkVal3 = (long)Math.abs(rand.nextInt(8) + 1);
      String[] macStr = macaddr.split("-");
      long ksum = 0L;
      String keyVal = "";
      String keyval1 = "";

      for(int i = 0; i < macStr.length; ++i) {
         String v0 = macStr[i].toString();
         String v1 = v0.substring(0, 1);
         String v2 = v0.substring(1, 2);
         if (i == 3) {
            if (Long.toString(Long.parseLong(keyval1) * chkVal1).length() < 13) {
               keyVal = keyVal + chkVal1 + "0" + Long.toString(Long.parseLong(keyval1) * chkVal1) + "-";
            } else {
               keyVal = keyVal + chkVal1 + Long.toString(Long.parseLong(keyval1) * chkVal1) + "-";
            }

            keyval1 = "";
         }

         keyval1 = keyval1 + arList[enInt(v1)] + arList[enInt(v2)];
         ksum += Long.parseLong(arList[enInt(v1)]) + Long.parseLong(arList[enInt(v2)]);
      }

      if (Long.toString(Long.parseLong(keyval1) * chkVal2).length() < 13) {
         keyVal = keyVal + chkVal2 + "0" + Long.toString(Long.parseLong(keyval1) * chkVal2) + "-";
      } else {
         keyVal = keyVal + chkVal2 + Long.toString(Long.parseLong(keyval1) * chkVal2) + "-";
      }

      keyVal = keyVal + chkVal3 + Long.toString((Long.parseLong(cnt) + ksum) * chkVal3);
      return keyVal;
   }

   public String encodeTrialKey(String indate, String cnt) throws Exception {
      String[] arList = new String[]{"18", "42", "23", "74", "85", "16", "37", "68", "29", "91", "14", "22", "44", "27", "51", "76", "67", "89", "25", "32", "35", "77", "04", "97", "53", "75", "63", "00", "09", "01", "54", "45", "88", "17", "62", "94"};
      Random rand = new Random(System.currentTimeMillis());
      long chkVal1 = (long)Math.abs(rand.nextInt(8) + 1);
      long chkVal2 = (long)Math.abs(rand.nextInt(8) + 1);
      long chkVal3 = (long)Math.abs(rand.nextInt(8) + 1);
      String indateStr = indate.replaceAll("-", "");
      indateStr = indateStr + StrUtils.strZeroPlus(Integer.parseInt(cnt)) + arList[rand.nextInt(8) + 1];
      String pVal = "";

      for(int r = 0; r < indateStr.length(); ++r) {
         if (r % 2 == 0) {
            if (r > 1) {
               pVal = pVal + "-" + indateStr.substring(r, r + 2);
            } else {
               pVal = pVal + indateStr.substring(r, r + 2);
            }
         }
      }

      String[] indateList = pVal.split("-");
      long ksum = 0L;
      String keyVal = "";
      String keyval1 = "";

      for(int i = 0; i < indateList.length; ++i) {
         String v0 = indateList[i].toString();
         String v1 = v0.substring(0, 1);
         String v2 = v0.substring(1, 2);
         if (i == 3) {
            if (Long.toString(Long.parseLong(keyval1) * chkVal1).length() < 13) {
               keyVal = keyVal + chkVal1 + "0" + Long.toString(Long.parseLong(keyval1) * chkVal1) + "-";
            } else {
               keyVal = keyVal + chkVal1 + Long.toString(Long.parseLong(keyval1) * chkVal1) + "-";
            }

            keyval1 = "";
         }

         keyval1 = keyval1 + arList[enInt(v1)] + arList[enInt(v2)];
         ksum += Long.parseLong(arList[enInt(v1)]) + Long.parseLong(arList[enInt(v2)]);
      }

      if (Long.toString(Long.parseLong(keyval1) * chkVal2).length() < 13) {
         keyVal = keyVal + chkVal2 + "0" + Long.toString(Long.parseLong(keyval1) * chkVal2) + "-";
      } else {
         keyVal = keyVal + chkVal2 + Long.toString(Long.parseLong(keyval1) * chkVal2) + "-";
      }

      keyVal = keyVal + chkVal3 + Long.toString((Long.parseLong("05") + ksum) * chkVal3);
      return keyVal;
   }

   public List getLicense(String license_type) throws SQLException {
      return this.dao.getLicenseInfo(license_type);
   }

   public Map getLicenseInfo(String license_type) throws SQLException {
      new HashMap();
      HashMap rmap = new HashMap();

      try {
         rmap.put("KVAL", "");
         rmap.put("KCNT", "");
         List arlst = this.dao.getLicenseInfo(license_type);
         if (arlst != null && arlst.size() > 0) {
            Map hmap = (Map)arlst.get(0);
            if (license_type.equals("02")) {
               if (this.new_lstrChk(StrUtils.nvl((String)hmap.get("license_key"))).equals("LIC_SUCCESS")) {
                  String val1 = this.decodeKey(StrUtils.nvl((String)hmap.get("license_key")));
                  if (!val1.equals("")) {
                     rmap.put("KVAL", StrUtils.nvl(val1.substring(0, val1.lastIndexOf("-"))));
                     rmap.put("KCNT", StrUtils.nvl(val1.substring(val1.lastIndexOf("-") + 1, val1.length())));
                  }
               }
            } else if (this.new_lstrChk(StrUtils.nvl((String)hmap.get("license_key"))).equals("LIC_SUCCESS")) {
               Map infoMap = Mklicense.getLicInfo(StrUtils.nvl((String)hmap.get("license_key")));
               if (infoMap != null) {
                  rmap.put("KVAL", StrUtils.nvl((String)infoMap.get("MAC")));
                  rmap.put("KCNT", StrUtils.nvl((String)infoMap.get("CONNECTION")));
               }
            }
         }
      } catch (Exception var6) {
         this.logger.error(var6);
      }

      return rmap;
   }

   public String getPaddr() throws Exception {
      String rtnStr = "01-01-01-01-01-01";
      String cmd = "ipconfig /all";
      Process pid = Runtime.getRuntime().exec(cmd);
      BufferedReader in = new BufferedReader(new InputStreamReader(pid.getInputStream()));

      while(true) {
         String line = in.readLine();
         if (line == null) {
            break;
         }

         String patternStr = "(([0-9,a-z,A-Z]+-){5})[0-9,a-z,A-Z]+";
         Pattern pattern = Pattern.compile(patternStr);
         Matcher matcher = pattern.matcher(line);
         boolean matchFound = matcher.find();
         if (matchFound) {
            rtnStr = matcher.group();
            break;
         }
      }

      return rtnStr;
   }

   public String getRegistryValue(String key) throws Exception {
      RegistryKey r = Registry.HKEY_LOCAL_MACHINE.openSubKey("Software\\Samsung\\MagicInfo-i\\Server\\Premium");
      String value = "";

      try {
         RegStringValue strValue = (RegStringValue)r.getValue(key);
         value = strValue.getData();
      } catch (Exception var5) {
         this.logger.error(var5);
      }

      return value;
   }

   public void setRegistryValue(String key, String value) throws Exception {
      RegistryKey r = Registry.HKEY_LOCAL_MACHINE.openSubKey("Software\\Samsung\\MagicInfo-i\\Server\\Premium");

      try {
         System.out.println("setReg" + key + value);
         RegStringValue strValue = (RegStringValue)r.getValue(key);
         strValue.setData(value);
         r.setValue(key, strValue);
      } catch (Exception var5) {
         this.logger.error(var5);
      }

   }

   public String getTrialFile() throws Exception {
      String brtn = "";
      File file = null;
      DataInputStream in = null;
      String istr = "";
      String lstr = "";
      FileInputStream fstream = null;
      FileOutputStream fos = null;

      try {
         String confFilePath = System.getProperty("user.home") + File.separator + "mips.tlf";
         file = SecurityUtils.getSafeFile(confFilePath);
         Date date = new Date(System.currentTimeMillis());
         SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
         String targetDate;
         if (file.exists()) {
            fstream = new FileInputStream(SecurityUtils.directoryTraversalChecker(confFilePath, (String)null));
            in = new DataInputStream(fstream);
            StringBuffer sbuf = new StringBuffer();

            while(in.available() != 0) {
               sbuf.append(in.readLine().toString());
            }

            lstr = StrUtils.nvl(this.decodeKey(sbuf.toString()));
            String[] trgDateList = lstr.split("-");
            targetDate = trgDateList[0] + trgDateList[1] + "-" + trgDateList[2] + "-" + trgDateList[3];
            Map kmap = this.getLicenseInfo("02");
            if (this.new_lstrChk(sbuf.toString()).equals("LIC_SUCCESS")) {
               String decodeVal;
               if (((String)kmap.get("KVAL")).equals("")) {
                  decodeVal = formatter.format(date);
                  String diffDay = DateUtils.getDayDiff(formatter.format(date), targetDate, "yyyy-MM-dd");
                  istr = this.encodeTrialKey(decodeVal, diffDay);
                  this.dao.chkLicenseValue("02", istr);
               }

               kmap = this.getLicenseInfo("02");
               decodeVal = (String)kmap.get("KVAL") + "-" + (String)kmap.get("KCNT");
               String[] srcDateList = decodeVal.split("-");
               String sourceDate = srcDateList[0] + srcDateList[1] + "-" + srcDateList[2] + "-" + srcDateList[3];
               String currDate = formatter.format(date);
               String checkDiffDay = DateUtils.getDayDiff(sourceDate, targetDate, "yyyy-MM-dd");
               String curr_DiffDay = DateUtils.getDayDiff(currDate, targetDate, "yyyy-MM-dd");
               long iCheckDiffDay = Long.parseLong(checkDiffDay);
               long iCurr_DiffDay = Long.parseLong(curr_DiffDay);
               if (iCheckDiffDay >= iCurr_DiffDay && iCheckDiffDay > 0L) {
                  istr = this.encodeTrialKey(currDate, curr_DiffDay);
                  this.dao.chkLicenseValue("02", istr);
                  brtn = curr_DiffDay;
               } else {
                  brtn = "alert.expired";
               }
            } else {
               brtn = "alert.expired";
            }
         } else {
            Map kmap = this.getLicenseInfo("02");
            if (((String)kmap.get("KVAL")).equals("")) {
               String initDate = formatter.format(date);
               targetDate = DateUtils.getAddDate(Integer.parseInt("60"), formatter.format(date), "yyyy-MM-dd");
               String diffDay = DateUtils.getDayDiff(formatter.format(date), targetDate, "yyyy-MM-dd");
               istr = this.encodeTrialKey(initDate, diffDay);
               lstr = this.encodeTrialKey(targetDate, diffDay);
               fos = new FileOutputStream(confFilePath);
               fos.write(lstr.getBytes("UTF-8"));
               this.dao.chkLicenseValue("02", istr);
               brtn = diffDay;
            } else {
               brtn = "alert.expired";
            }
         }
      } catch (Exception var33) {
         this.logger.error(var33);
      } finally {
         try {
            if (fstream != null) {
               fstream.close();
            }

            if (in != null) {
               in.close();
            }

            if (fos != null) {
               fos.close();
            }

            file = null;
         } catch (Exception var32) {
            this.logger.error(var32);
         }

      }

      return brtn;
   }

   public synchronized String lfilecheck() throws Exception {
      return "VALID-LICENSE";
   }

   public String new_lstrChk(String lstr) throws Exception {
      String brtn = "LIC_SUCCESS";
      this.logger.info("....lstrChk Start......lstr: " + lstr);
      if (!lstr.equals("") && lstr.indexOf("-") > -1) {
         String[] arrLstr = lstr.split("-");
         if (arrLstr.length == 4) {
            for(int i = 0; i < arrLstr.length; ++i) {
               if (arrLstr[i].length() != 8) {
                  this.logger.error("Log_License : License Str Key Length invalid.");
                  brtn = "INVALID_KEY_LENGTH";
                  return brtn;
               }
            }
         }
      }

      try {
         HashMap infoMap = Mklicense.getLicInfo(lstr);
         if (infoMap == null) {
            brtn = "INVALID_KEY_FORMAT";
            return brtn;
         } else if (!this.checkLicenseKeyCode(infoMap)) {
            this.logger.error("Log_License : checkLicenseCode fail.");
            brtn = "INCLUDE_NOT_SUPPORT_CODE";
            return brtn;
         } else {
            if (infoMap.get("SW_COST").equals("D")) {
               String sw_date = StrUtils.nvl((String)infoMap.get("SW_DATE"));
               String sw_seq = StrUtils.nvl((String)infoMap.get("SW_SEQ"));
               if (!this.chkDemoExpired(sw_date, sw_seq)) {
                  this.logger.error("Log_License : License Demo Expired!!");
                  brtn = "KEY_DATE_EXPIRED";
                  return brtn;
               }
            }

            return brtn;
         }
      } catch (Exception var6) {
         this.logger.error(var6);
         brtn = "KEY_FORMAT_ERROR";
         return brtn;
      }
   }

   public String new_lstrChkWithProductKey(String productKey, String lstr) throws Exception {
      String brtn = "LIC_SUCCESS";
      brtn = this.new_lstrChk(lstr);
      if (!brtn.equals("LIC_SUCCESS")) {
         return brtn;
      } else {
         try {
            HashMap productKeyLstr = Mklicense.getDecodeLic(lstr);
            String productKeySrc = Mklicense.getDecodeSn(productKey);
            if (!productKeyLstr.get("SN").equals(productKeySrc)) {
               this.logger.error("Log_License : after decoding, not matchecd(SerialKey != LicenseKey)");
               brtn = "NOT_MATCH_SERIAL_KEY";
            }
         } catch (Exception var6) {
            this.logger.error(var6);
            brtn = "KEY_FORMAT_ERROR";
         }

         return brtn;
      }
   }

   public boolean chkDemoExpired(String sw_date, String sw_seq) {
      boolean rtn = false;
      if (sw_seq != null && !sw_seq.equals("") && sw_date != null && !sw_date.equals("")) {
         try {
            int currentYear = false;
            int currentMonth = false;
            int startYear = false;
            int startMonth = false;
            int exYear = false;
            int exMonth = false;
            Calendar cal = Calendar.getInstance();
            int currentYear = cal.get(1);
            int currentMonth = cal.get(2) + 1;
            String[] exDate = sw_seq.split("-");
            int exYear = Integer.parseInt(exDate[0]);
            int exMonth = Integer.parseInt(exDate[1]);
            String[] startDate = sw_date.split("-");
            int startYear = Integer.parseInt(startDate[0]);
            int startMonth = Integer.parseInt(startDate[1]);
            if (currentYear <= exYear && (currentYear != exYear || currentMonth <= exMonth) && currentYear >= startYear && (currentYear != startYear || currentMonth >= startMonth) && exYear >= startYear && (exYear != startYear || exMonth >= startMonth) && (currentYear != exYear || exMonth < 6 + currentMonth) && (currentYear == exYear || exMonth + 12 < 6 + currentMonth)) {
               rtn = true;
            } else {
               rtn = false;
            }
         } catch (Exception var13) {
            this.logger.error("", var13);
            rtn = false;
         }

         return rtn;
      } else {
         return rtn;
      }
   }

   public String[] searchPaddr() throws Exception {
      List rtnStr = new ArrayList();
      rtnStr.add("01-01-01-01-01-01");
      String macAddress = null;
      String command = "ifconfig";
      String osName = System.getProperty("os.name");
      if (osName.startsWith("Windows")) {
         command = "ipconfig /all";
      } else if (!osName.startsWith("Linux") && !osName.startsWith("Mac") && !osName.startsWith("HP-UX") && !osName.startsWith("NeXTStep") && !osName.startsWith("Solaris") && !osName.startsWith("SunOS") && !osName.startsWith("FreeBSD") && !osName.startsWith("NetBSD")) {
         if (osName.startsWith("OpenBSD")) {
            command = "netstat -in";
         } else if (!osName.startsWith("IRIX") && !osName.startsWith("AIX") && !osName.startsWith("Tru64")) {
            if (!osName.startsWith("Caldera") && !osName.startsWith("UnixWare") && !osName.startsWith("OpenUNIX")) {
               throw new Exception("The current operating system '" + osName + "' is not supported.");
            }

            command = "ndstat";
         } else {
            command = "netstat -ia";
         }
      } else {
         command = "ifconfig -a";
      }

      Process pid = Runtime.getRuntime().exec(command);
      BufferedReader in = new BufferedReader(new InputStreamReader(pid.getInputStream()));
      Pattern p = Pattern.compile("([\\w]{1,2}(-|:)){5}[\\w]{1,2}");

      String line;
      while((line = in.readLine()) != null) {
         Matcher m = p.matcher(line);
         if (m.find()) {
            macAddress = m.group().replace(":", "-").toUpperCase();
            if (!macAddress.equalsIgnoreCase("01-01-01-01-01-01") && !macAddress.equalsIgnoreCase("00-00-00-00-00-00")) {
               rtnStr.add(macAddress);
            }
         }
      }

      in.close();
      return (String[])rtnStr.toArray(new String[rtnStr.size()]);
   }

   public List getLicenseInfoHistoryByProductKey(String product_key) throws SQLException {
      return this.dao.getLicenseInfoHistoryByProductKey(product_key);
   }

   public boolean deleteLicenseInfoHistory(String sw_cost, String sw_change, String sw_grade, String product_kind) throws SQLException {
      return this.dao.deleteLicenseInfoHistory(sw_cost, sw_change, sw_grade, product_kind);
   }

   public boolean deleteLicenseInfoHistoryForPremium() throws SQLException {
      return this.dao.deleteLicenseInfoHistoryForPremium();
   }

   public boolean deleteLicenseInfoByLicenseType(String type) throws SQLException {
      return this.dao.deleteLicenseInfoByLicenseType(type);
   }

   public boolean deleteLicenseInfoHistoryByKey(String key) throws SQLException {
      return this.dao.deleteLicenseInfoHistoryByKey(key);
   }

   public boolean deleteDemoLicenseInfoHistory(String product_kind, String sw_grade) throws SQLException {
      return this.dao.deleteDemoLicenseInfoHistory(product_kind, sw_grade);
   }

   public boolean deleteAllLicenseInfoHistory() throws SQLException {
      return this.dao.deleteAllLicenseInfoHistory();
   }

   public boolean insertPLicense() throws SQLException {
      return this.dao.insertPLicense();
   }

   public PagedListInfo getPagedList(int startPos, int pageSize, Map condition, String section) throws SQLException, ConfigException {
      PagedListInfo pagedListInfo = null;
      if (section.equals("getLicenseHistoryList")) {
         pagedListInfo = this.dao.getLicenseHistoryList(startPos, pageSize, condition);
      }

      return pagedListInfo;
   }

   public int getCntLicUVending() throws SQLException {
      return this.dao.getCntLicUVending();
   }

   public int getCntLicUVendingMenuOn() throws SQLException {
      return this.dao.getCntLicUVendingMenuOn();
   }

   public int getCntLicVideoWall() throws SQLException {
      return this.dao.getCntLicVideoWall();
   }

   public int getCntLicVideoWallMenuOn() throws SQLException {
      return this.dao.getCntLicVideoWallMenuOn();
   }

   public int getCntBasicLic(String sw_grade, String pKey) throws SQLException {
      return this.dao.getCntBasicLic(sw_grade, pKey);
   }

   public void deleteDeviceByLicenseExpired(String deviceType) {
      this.logger.error("Log_License : Start delete deivce for License Expired.......deviceType=" + deviceType);
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      List deviceIdList = null;
      int startingNumber = 0;
      int licCnt = 0;

      try {
         if (deviceType.equals("iPLAYER")) {
            licCnt = this.getConnCntLicenseByProduct("ST", "P", "MI");
            deviceIdList = deviceDao.getDeviceIdByAsc("iPLAYER");
            startingNumber = Integer.parseInt("5");
         } else if (deviceType.equals("SPLAYER")) {
            licCnt = this.getConnCntLicenseByProduct("ST", "P", "MS");
            deviceIdList = deviceDao.getDeviceIdByAsc("SPLAYER");
            startingNumber = 0;
         } else if (deviceType.equals("LPLAYER")) {
            licCnt = this.getConnCntLicenseByProduct("ST", "L", "MI");
            deviceIdList = deviceDao.getDeviceIdByAsc("LPLAYER");
            startingNumber = Integer.parseInt("25");
         }
      } catch (Exception var10) {
         this.logger.error("", var10);
         return;
      }

      if (deviceIdList != null && licCnt <= 0) {
         if (deviceIdList.size() > startingNumber) {
            this.logger.error("delete deivce. device List=" + deviceIdList.size() + ", del Start Idx=" + startingNumber);

            for(int i = startingNumber; i < deviceIdList.size(); ++i) {
               try {
                  Map deviceId = (Map)deviceIdList.get(i);
                  String dId = (String)deviceId.get("DEVICE_ID");
                  if (dId != null) {
                     deviceDao.deleteDevice(dId);
                     this.logger.error("Log_License : delete device. id=" + dId + ",idx=" + i);
                  }
               } catch (Exception var9) {
                  this.logger.error("", var9);
               }
            }
         } else {
            this.logger.error("need not delete deivce. deviceList=" + deviceIdList.size() + ", del StartIdx=" + startingNumber);
         }

      } else {
         this.logger.error("License is alive. do not delete device. deviceType=" + deviceType);
      }
   }

   public int getConnCntLicenseByProduct(String sw_change, String sw_grade, String product_kind) {
      return 0;
   }

   public int getConnCntLicense(String sw_cost, String sw_change, String sw_grade, String product_kind) {
      return 0;
   }

   public List getCostLicenseHistory(String sw_cost, String sw_change, String sw_grade, String product_kind) throws SQLException {
      return null;
   }

   public List getAllLicenseHistory(String sw_cost, String sw_change, String sw_grade, String product_kind) throws SQLException {
      return null;
   }

   public List getAllLicenseHistory() throws SQLException {
      return null;
   }

   public int getCntLicenseHistory(String sw_cost, String sw_change, String sw_grade, String product_kind) throws SQLException {
      return 0;
   }

   public int getCntProductKey(String product_key) throws SQLException {
      return 0;
   }

   public boolean setLicenseMenuOnByProductKind(String product_kind, String menu_on) throws SQLException, ConfigException {
      return false;
   }

   public String getLicenseKeyByLicenseType(String license_type) throws SQLException {
      return null;
   }

   public boolean updateLicenseInfo(String pKey) throws SQLException {
      return false;
   }

   public boolean addDeviceSamples(String mac, int num) throws SQLException {
      return false;
   }

   public boolean addSocDeviceSamples(String mac, int num) throws SQLException {
      return false;
   }

   public boolean addLiteDeviceSamples(String mac, int num) throws SQLException {
      return false;
   }

   public void checkDemoLicense() {
   }

   public void checkDBLicenseHistory() {
   }

   public boolean checkLicenseKeyCode(HashMap infoMap) {
      return false;
   }

   public boolean addBasicLicense(String swGrade) {
      return false;
   }

   public boolean addLicenseByHashMap(String lstr, HashMap lstrMap) {
      return false;
   }

   public void licenseLoadingConfig() {
   }

   public MobileLicenseEntity getMobileLicenseEntity() {
      return null;
   }

   public void checkTrialLicense() {
   }

   public boolean isUseTrialLicense(String trialType) {
      return false;
   }

   public boolean insertTrialLicense(String productKind, String swEdition, String swChg, int trialDay, String macAddr) {
      return false;
   }

   public String makeLicenseKey(String productKind, String swEdition, String swKind, String swChg, String swOption, String swCost, String connection, String swDate, String swSeq, String macAddr) {
      return null;
   }

   public String getTrialExpiredDate(HashMap info) {
      return null;
   }
}
