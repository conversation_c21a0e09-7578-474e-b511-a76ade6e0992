package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.model.ResponseContentGroupData;
import com.samsung.magicinfo.webauthor2.repository.model.ResultContentGroupListData;
import java.util.HashMap;
import java.util.Map;
import org.springframework.web.client.RestTemplate;

public class GetContentGroupListOpenApiMethod extends OpenApiMethod<ResultContentGroupListData, ResponseContentGroupData> {
  private final String userId;
  
  private final String token;
  
  public GetContentGroupListOpenApiMethod(RestTemplate restTemplate, String userId, String token) {
    super(restTemplate);
    this.userId = userId;
    this.token = token;
  }
  
  protected String getOpenApiClassName() {
    return "CommonContentService";
  }
  
  protected String getOpenApiMethodName() {
    return "getContentGroupList";
  }
  
  protected Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("userId", this.userId);
    vars.put("token", this.token);
    return vars;
  }
  
  Class<ResponseContentGroupData> getResponseClass() {
    return ResponseContentGroupData.class;
  }
  
  ResultContentGroupListData convertResponseData(ResponseContentGroupData responseData) {
    return responseData.getResponseClass();
  }
}
