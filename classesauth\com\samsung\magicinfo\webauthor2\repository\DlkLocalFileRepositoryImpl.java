package com.samsung.magicinfo.webauthor2.repository;

import com.google.common.base.Joiner;
import com.google.common.base.Optional;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import javax.inject.Inject;
import javax.servlet.ServletContext;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

@Repository
public class DlkLocalFileRepositoryImpl implements DlkLocalFileRepository {
  private static final Logger logger = LoggerFactory.getLogger(DlkLocalFileRepositoryImpl.class);
  
  private final ServletContext servletContext;
  
  @Inject
  public DlkLocalFileRepositoryImpl(ServletContext servletContext) {
    this.servletContext = servletContext;
  }
  
  public File getDLKFile(String userWorkspaceFolder) {
    Path dirPath = Paths.get(createPathToDLKFolder(userWorkspaceFolder), new String[0]);
    String[] extensions = { "DLK", "dlk" };
    List<File> files = (List<File>)FileUtils.listFiles(dirPath.toFile(), extensions, true);
    if (files.size() > 0)
      return files.get(0); 
    return null;
  }
  
  public String getDLKFileContent(String userWorkspaceFolder) {
    File dlkFile = getDLKFile(userWorkspaceFolder);
    try {
      return new String(Files.readAllBytes(dlkFile.toPath()), StandardCharsets.UTF_8);
    } catch (IOException e) {
      logger.error(e.getMessage());
      return null;
    } 
  }
  
  public void saveDLKFile(String xmlDLK, String fileName, String userWorkspaceFolderName) {
    Path filePath = Paths.get(createPathToDLKFile(userWorkspaceFolderName, fileName), new String[0]);
    try {
      Path parent = filePath.getParent();
      if (parent != null)
        deleteDLKFile(parent.toString()); 
      logger.debug("Storing XML in: " + filePath);
      FileUtils.writeStringToFile(filePath.toFile(), xmlDLK, StandardCharsets.UTF_8);
    } catch (IOException ex) {
      logger.error(ex.getMessage());
    } 
  }
  
  private void deleteDLKFile(String userDirectory) {
    Path dirPath = Paths.get(userDirectory, new String[0]);
    if (Files.notExists(dirPath, new java.nio.file.LinkOption[0]))
      return; 
    String[] extensions = { "DLK", "dlk" };
    Iterable<File> dlkFiles = FileUtils.listFiles(dirPath.toFile(), extensions, true);
    for (File dlkFile : dlkFiles)
      FileUtils.deleteQuietly(dlkFile); 
  }
  
  private String createPathToDLKFile(String userWorkspaceFolder, String fileName) {
    String fileWithExtenstion, dlkFileExt = ".DLK";
    String serverDirectoryPath = this.servletContext.getRealPath("insertContents");
    Optional<String> fileExtension = Optional.fromNullable(FilenameUtils.getExtension(fileName));
    if (fileExtension.isPresent()) {
      fileWithExtenstion = FilenameUtils.getBaseName(fileName) + ".DLK";
    } else {
      fileWithExtenstion = fileName + ".DLK";
    } 
    return Joiner.on(File.separator).join(serverDirectoryPath, userWorkspaceFolder, new Object[] { fileWithExtenstion });
  }
  
  private String createPathToDLKFolder(String userWorkspaceFolder) {
    String serverDirectoryPath = this.servletContext.getRealPath("insertContents");
    return Joiner.on(File.separator).join(serverDirectoryPath, userWorkspaceFolder, new Object[0]);
  }
}
