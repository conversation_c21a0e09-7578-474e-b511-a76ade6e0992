package com.samsung.magicinfo.rms.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.samsung.magicinfo.openapi.annotation.CdataField;
import com.thoughtworks.xstream.annotations.XStreamOmitField;
import io.swagger.annotations.ApiModelProperty;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@JsonInclude(Include.NON_NULL)
public class DeviceDisplayConfResource {
   @ApiModelProperty(
      hidden = true
   )
   private String deviceId;
   private List deviceIds;
   @CdataField
   @ApiModelProperty(
      hidden = true
   )
   private String deviceName;
   @ApiModelProperty(
      hidden = true
   )
   private String deviceModelCode;
   @ApiModelProperty(
      hidden = true
   )
   private String deviceModelName;
   @ApiModelProperty(
      hidden = true
   )
   private String deviceType;
   @ApiModelProperty(
      hidden = true
   )
   private Float deviceTypeVersion;
   @ApiModelProperty(
      example = "0",
      value = "0 : power off"
   )
   private String basicPower;
   @ApiModelProperty(
      example = "0",
      value = "volume value"
   )
   private Long basicVolume;
   @ApiModelProperty(
      example = "0",
      value = "0 : mute off"
   )
   private Long basicMute;
   @ApiModelProperty(
      example = "96",
      value = "MagicInfo-Lite/S"
   )
   private Long basicSource;
   @ApiModelProperty(
      example = "2;1;0;65535;0",
      value = "1;chkAtvDtv(0:false, 1:true);chkAirCable(0:false, 1:true);tv_channel_major_channel;tv_channel_minor_channel[Channel] button on display list tab. (Modification is only allowed with TV or DTV inpusource"
   )
   private String basicDirectChannel;
   @ApiModelProperty(
      example = "0",
      value = "0 : panel off"
   )
   private Long basicPanelStatus;
   @ApiModelProperty(
      example = "N",
      value = "N: not supported, 0: off, 1: on"
   )
   private String networkStandbyMode;
   @ApiModelProperty(
      example = "2",
      value = "0: Off, 1: DICOM Simulation Mode, 2: Director Mode, 3: Adaptive Picture"
   )
   private Long pvSplPictureMode;
   @ApiModelProperty(
      example = "32",
      value = "0: dynamic, 1: mdc_pv_standard, 2: mdc_pv_movie, 3: custom, 4: mdc_pv_natural, 5: calibration, 32: shopAndMall_video, 33: shopAndMall_text, 34: officeAndSchool_video, 35: officeAndSchool_text, 36:terminalAndStation_video, 37: terminalAndStation_text, 38: videowall_video, 39: videowall_text"
   )
   private Long pvMode;
   @ApiModelProperty(
      example = "99",
      value = "1~100"
   )
   private Long pvContrast;
   @ApiModelProperty(
      example = "20",
      value = "1~100"
   )
   private Long pvBrightness;
   @ApiModelProperty(
      example = "59",
      value = "1~100"
   )
   private Long pvSharpness;
   @ApiModelProperty(
      example = "55",
      value = "1~100"
   )
   private Long pvColor;
   @ApiModelProperty(
      example = "50",
      value = "1~100"
   )
   private Long pvTint;
   @ApiModelProperty(
      example = "80",
      value = "80:off, 1: cool, 2:normal"
   )
   private Long pvColortone;
   private Long pvColorTemperature;
   @ApiModelProperty(
      example = "1",
      value = "1: 16:9, 4: zoom, 5: zoom1, 6: zoom2, 9: screen_fit_just_scan1, 11: 4:3,12: wide_fit, 49: wide_zoom, 13: custom, 14: mdc_smart_view1, 15: mdc_smart_view2, 32: psize_original_ratio"
   )
   private Long pvSize;
   @ApiModelProperty(
      example = "2",
      value = "1: on, 0: off, 2: mdc_medium, 3: mdc_high, 4: autho, 5: mdc_auto_visual"
   )
   private Long pvDigitalnr;
   @ApiModelProperty(
      example = "0",
      value = "1: on, 0: off, 2: auto2, 3: mdc_cinema_smooth"
   )
   private Long pvFilmmode;
   private String pvVideoPicturePositionSize;
   @ApiModelProperty(
      example = "",
      value = "if pv_hdmi_black_level value is null : not supported - 0: low, 1: normalelse 0: normal, 1: low, 2: auto"
   )
   private Long pvHdmiBlackLevel;
   private Long pvMpegNoiseFilter;
   @ApiModelProperty(
      example = "0",
      value = "0: natural, 1: gammaMode1, 2: gammaMode2, 3: gammaMode3, 4: gammaMode4, 5: gammaMode5, 17: -1, 18: -2, 19: -3, 20: -4, 21: -5, 32: custom"
   )
   private Long ppcGamma;
   @ApiModelProperty(
      example = "0",
      value = "if Pv_hdmi_black_level value is null - 0: low, 1: normalelse 1: normal, 0: low"
   )
   private Long ppcHdmiBlackLevel;
   @ApiModelProperty(
      example = "35",
      value = "16: entertainment, 17: internet, 18: text, 19: custom, 20: advertisement, 21: information, 22: calibration, 32: shopAndMall_video, 33: shopAndMall_text, 34: officeAndSchool_video, 35: officeAndSchool_text, 36: terminalAndStation_video, 37: terminalAndStation_text, 38: videowall_video, 39: videowall_text"
   )
   private Long ppcMagicBright;
   @ApiModelProperty(
      example = "99",
      value = "0~100"
   )
   private Long ppcContrast;
   @ApiModelProperty(
      example = "20",
      value = "0~100"
   )
   private Long ppcBrightness;
   @ApiModelProperty(
      example = "0",
      value = "if ppc_colortone not null - 1: cool1, 0: cool2, 2: normal, 3: warm1, 4: warm2, 80: off"
   )
   private Long ppcColortone;
   @ApiModelProperty(
      example = "160",
      value = "if ppc_colortone not null - 253:254:35:255:45:0:55:1:65:2:75:3:85:4:95:5:105:6:115:7:125:8:135:9:145:16:155:160 - 2,800K:3,000K:3,500K:4,000K:4,500K:5,000K:5,500K:6,000K:6,500K:7,000K:7,500K:8,000K:8,500K:9,000K:9,500K:10,000K:10,500K:11,000K:11,500K:12,000K:12,500K:13,000K:13,500K:14,000K:14,500K:15,000K:15,500K:16,000K"
   )
   private Long ppcColorTemperature;
   @ApiModelProperty(
      example = "0",
      value = ""
   )
   private Long ppcRed;
   @ApiModelProperty(
      example = "0",
      value = ""
   )
   private Long ppcGreen;
   @ApiModelProperty(
      example = "0",
      value = ""
   )
   private Long ppcBlue;
   @ApiModelProperty(
      example = "0",
      value = "16: 16:9, 24: 4:3, 32: psize_original_ratio"
   )
   private Long ppcSize;
   @ApiModelProperty(
      hidden = true
   )
   private String timeCurrentTime;
   @ApiModelProperty(
      hidden = true
   )
   private String timeOnTime;
   @ApiModelProperty(
      hidden = true
   )
   private String timeOffTime;
   private Long pipSource;
   private Long pipSize;
   private Long pipPosition;
   private Long pipSwap;
   private Long pipControl;
   @ApiModelProperty(
      example = "0",
      value = "0: mdc_sound_standard, 1: mdc_sound_music, 2: mdc_sound_movie, 3: mdc_sound_speech, 4: custom, 5: amplify"
   )
   private Long soundMode;
   @ApiModelProperty(
      example = "0",
      value = ""
   )
   private Long soundBass;
   @ApiModelProperty(
      example = "0",
      value = ""
   )
   private Long soundTreble;
   @ApiModelProperty(
      example = "0",
      value = ""
   )
   private Long soundBalance;
   @ApiModelProperty(
      example = "0",
      value = "0: off, 1: on"
   )
   private Long soundSrs;
   @ApiModelProperty(
      example = "0",
      value = ""
   )
   private Long soundEffect;
   private Long imageCoarse;
   private Long imageFine;
   private Long imageHpos;
   private Long imageVpos;
   private Long imageAuto;
   @ApiModelProperty(
      example = "0",
      value = ""
   )
   private Long sbStatus;
   @ApiModelProperty(
      example = "0",
      value = "0~255"
   )
   private Long sbRgain;
   @ApiModelProperty(
      example = "0",
      value = "0~255"
   )
   private Long sbGgain;
   @ApiModelProperty(
      example = "0",
      value = "0~255"
   )
   private Long sbBgain;
   @ApiModelProperty(
      example = "0",
      value = "0~255"
   )
   private Long sbRoffset;
   @ApiModelProperty(
      example = "0",
      value = "0~255"
   )
   private Long sbGoffset;
   @ApiModelProperty(
      example = "0",
      value = "0~255"
   )
   private Long sbBoffset;
   @ApiModelProperty(
      example = "0",
      value = "0~255"
   )
   private Long sbGain;
   @ApiModelProperty(
      example = "0",
      value = "0~255"
   )
   private Long sbSharp;
   @ApiModelProperty(
      example = "12;0;AM;100;12;0;PM;255"
   )
   private DeviceDisplayConfSubResource mntAuto;
   @ApiModelProperty(
      example = "70",
      value = "0~100"
   )
   private Long mntManual;
   @ApiModelProperty(
      example = "null"
   )
   private Long mntVideoWall;
   @ApiModelProperty(
      example = "null"
   )
   private Long mntFormat;
   @ApiModelProperty(
      example = "1;10;AM"
   )
   private DeviceDisplayConfSubResource mntSafetyScreenTimer;
   @ApiModelProperty(
      example = "0"
   )
   private Long mntSafetyScreenRun;
   @ApiModelProperty(
      example = "0;4;4;4"
   )
   private DeviceDisplayConfSubResource mntPixelShift;
   @ApiModelProperty(
      example = "0"
   )
   private Long mntSafetyLock;
   @ApiModelProperty(
      example = "0"
   )
   private Long advancedRj45SettingRefresh;
   @ApiModelProperty(
      example = "1;1;1;1"
   )
   private DeviceDisplayConfSubResource advancedOsdDisplayType;
   @ApiModelProperty(
      example = "0"
   )
   private Long advancedFanControl;
   @ApiModelProperty(
      example = "40"
   )
   private Long advancedFanSpeed;
   @ApiModelProperty(
      example = "0"
   )
   private Long advancedReset;
   @ApiModelProperty(
      example = "0"
   )
   private Long advancedAutoPower;
   @ApiModelProperty(
      example = "0"
   )
   private Long advancedUserAutoColor;
   @ApiModelProperty(
      example = "2"
   )
   private Long advancedStandBy;
   @ApiModelProperty(
      example = "1;1;20;30"
   )
   private DeviceDisplayConfSubResource autoSourceSwitching;
   @ApiModelProperty(
      example = "1"
   )
   private Long miscRemocon;
   @ApiModelProperty(
      example = "0"
   )
   private Long miscPanelLock;
   @ApiModelProperty(
      example = "1"
   )
   private Long miscOsd;
   @ApiModelProperty(
      example = "0"
   )
   private Long miscAllLock;
   @ApiModelProperty(
      example = "null"
   )
   private String diagnosisDisplayStatus;
   @ApiModelProperty(
      example = "31"
   )
   private Long diagnosisMonitorTemperature;
   @ApiModelProperty(
      example = "77"
   )
   private Long diagnosisAlarmTemperature;
   @ApiModelProperty(
      example = "16;68"
   )
   private String diagnosisPanelOnTime;
   @ApiModelProperty(
      example = "null"
   )
   private Long chkSchChannel;
   @ApiModelProperty(
      example = "false"
   )
   private Boolean webcam;
   @ApiModelProperty(
      hidden = true
   )
   private String vwtId;
   @XStreamOmitField
   @ApiModelProperty(
      hidden = true
   )
   private Long childCnt;
   @XStreamOmitField
   @ApiModelProperty(
      hidden = true
   )
   private Long connChildCnt;
   @XStreamOmitField
   @ApiModelProperty(
      hidden = true
   )
   private Boolean isChild;
   @XStreamOmitField
   @ApiModelProperty(
      hidden = true
   )
   private Boolean hasChild;
   @ApiModelProperty(
      hidden = true
   )
   private String vwlMode;
   @ApiModelProperty(
      hidden = true
   )
   private String vwlPosition;
   @ApiModelProperty(
      hidden = true
   )
   private String vwlFormat;
   @ApiModelProperty(
      hidden = true
   )
   private String vwlLayout;
   private List inputSourceList = new ArrayList();
   private Timestamp mdcUpdateTime;
   @XStreamOmitField
   private boolean pcMode;
   private Long maxPowerSaving;
   private Long brightnessLimit;
   private Long touchControlLock;
   private DeviceDisplayConfSubResource webBrowserUrl;
   private DeviceDisplayCustomLogoResource customLogo;
   private Long screenFreeze;
   private Long screenMute;
   private Long blackTone;
   private Long fleshTone;
   private Long rgbOnlyMode;
   private Long osdMenuSize;
   private String ledPictureSize;
   private String ledHdr;
   private Long ledHdrDre;
   private Long autoMotionPlus;
   private Long autoMotionPlusJudderReduction;
   private Long ecoSensor;
   private String colorSpace;
   private Long pictureEnhancer;
   private Float sensorInternalTemperature;
   private Float sensorInternalHumidity;
   private Float sensorEnvironmentTemperature;
   private Float sensorFrontglassTemperature;
   private Float sensorFrontglassHumidity;
   private String errorFlag;
   private String advancedOsdDisplayTypeValue;
   private String autoBrightness;
   private Long childAlarmTemperature;
   private Long minBrightness;
   private Long liveMode;
   private Long displayOutputMode;
   private Boolean cleanupUserData;
   private Long cleanupUserDataInterval;
   private Long autoSave;
   private Long autoPowerOff;
   private DeviceSMTPResource smtp;
   private DevicePrintServerResource printServer;
   private String print_server;
   private Long web_command;
   private Long web_option;
   private String web_url;
   private Timestamp web_end_time;
   private Long web_duration;
   private Map settingResultSummary;
   private Long installEnvironment;
   @ApiModelProperty("dehumidify")
   private Dehumidify dehumidify;
   private Long dimmingOption;
   private Long dimmingNightTimeOverride;
   private DimmingEcoSensor dimmingEcoSensor;
   private DimmingSunriseSunset dimmingSunriseSunset;
   private List dimmingSunriseSunsetTimes;
   @ApiModelProperty("led dimming brightness output")
   private DimmingBrightnessOutput dimmingBrightnessOutput;

   public DeviceDisplayConfResource() {
      super();
   }

   public Long getBlackTone() {
      return this.blackTone;
   }

   public void setBlackTone(Long blackTone) {
      this.blackTone = blackTone;
   }

   public Long getFleshTone() {
      return this.fleshTone;
   }

   public void setFleshTone(Long fleshTone) {
      this.fleshTone = fleshTone;
   }

   public Long getRgbOnlyMode() {
      return this.rgbOnlyMode;
   }

   public void setRgbOnlyMode(Long rgbOnlyMode) {
      this.rgbOnlyMode = rgbOnlyMode;
   }

   public Long getScreenFreeze() {
      return this.screenFreeze;
   }

   public void setScreenFreeze(Long screenFreeze) {
      this.screenFreeze = screenFreeze;
   }

   public Long getScreenMute() {
      return this.screenMute;
   }

   public void setScreenMute(Long screenMute) {
      this.screenMute = screenMute;
   }

   public Long getMaxPowerSaving() {
      return this.maxPowerSaving;
   }

   public void setMaxPowerSaving(Long maxPowerSaving) {
      this.maxPowerSaving = maxPowerSaving;
   }

   public Long getBrightnessLimit() {
      return this.brightnessLimit;
   }

   public void setBrightnessLimit(Long brightnessLimit) {
      this.brightnessLimit = brightnessLimit;
   }

   public Long getTouchControlLock() {
      return this.touchControlLock;
   }

   public void setTouchControlLock(Long touchControlLock) {
      this.touchControlLock = touchControlLock;
   }

   public DeviceDisplayConfSubResource getWebBrowserUrl() {
      return this.webBrowserUrl;
   }

   public void setWebBrowserUrl(DeviceDisplayConfSubResource webBrowserUrl) {
      this.webBrowserUrl = webBrowserUrl;
   }

   public DeviceDisplayCustomLogoResource getCustomLogo() {
      return this.customLogo;
   }

   public void setCustomLogo(DeviceDisplayCustomLogoResource customLogo) {
      this.customLogo = customLogo;
   }

   public Long getPvMpegNoiseFilter() {
      return this.pvMpegNoiseFilter;
   }

   public void setPvMpegNoiseFilter(Long pvMpegNoiseFilter) {
      this.pvMpegNoiseFilter = pvMpegNoiseFilter;
   }

   public DeviceDisplayConfSubResource getAutoSourceSwitching() {
      return this.autoSourceSwitching;
   }

   public void setAutoSourceSwitching(DeviceDisplayConfSubResource autoSourceSwitching) {
      this.autoSourceSwitching = autoSourceSwitching;
   }

   public boolean isPcMode() {
      return this.pcMode;
   }

   public void setPcMode(boolean pcMode) {
      this.pcMode = pcMode;
   }

   public Timestamp getMdcUpdateTime() {
      return this.mdcUpdateTime;
   }

   public void setMdcUpdateTime(Timestamp mdcUpdateTime) {
      this.mdcUpdateTime = mdcUpdateTime;
   }

   public List getInputSourceList() {
      return this.inputSourceList;
   }

   public void setInputSourceList(List inputSourceList) {
      this.inputSourceList = inputSourceList;
   }

   public String getDeviceId() {
      return this.deviceId;
   }

   public void setDeviceId(String deviceId) {
      this.deviceId = deviceId;
   }

   public String getDeviceName() {
      return this.deviceName;
   }

   public void setDeviceName(String deviceName) {
      this.deviceName = deviceName;
   }

   public String getDeviceModelCode() {
      return this.deviceModelCode;
   }

   public void setDeviceModelCode(String deviceModelCode) {
      this.deviceModelCode = deviceModelCode;
   }

   public String getDeviceModelName() {
      return this.deviceModelName;
   }

   public void setDeviceModelName(String deviceModelName) {
      this.deviceModelName = deviceModelName;
   }

   public String getDeviceType() {
      return this.deviceType;
   }

   public void setDeviceType(String deviceType) {
      this.deviceType = deviceType;
   }

   public Float getDeviceTypeVersion() {
      return this.deviceTypeVersion;
   }

   public void setDeviceTypeVersion(Float deviceTypeVersion) {
      this.deviceTypeVersion = deviceTypeVersion;
   }

   public String getBasicPower() {
      return this.basicPower;
   }

   public void setBasicPower(String basicPower) {
      this.basicPower = basicPower;
   }

   public Long getBasicVolume() {
      return this.basicVolume;
   }

   public void setBasicVolume(Long basicVolume) {
      this.basicVolume = basicVolume;
   }

   public Long getBasicMute() {
      return this.basicMute;
   }

   public void setBasicMute(Long basicMute) {
      this.basicMute = basicMute;
   }

   public Long getBasicSource() {
      return this.basicSource;
   }

   public void setBasicSource(Long basicSource) {
      this.basicSource = basicSource;
   }

   public String getBasicDirectChannel() {
      return this.basicDirectChannel;
   }

   public void setBasicDirectChannel(String basicDirectChannel) {
      this.basicDirectChannel = basicDirectChannel;
   }

   public Long getBasicPanelStatus() {
      return this.basicPanelStatus;
   }

   public void setBasicPanelStatus(Long basicPanelStatus) {
      this.basicPanelStatus = basicPanelStatus;
   }

   public String getNetworkStandbyMode() {
      return this.networkStandbyMode;
   }

   public void setNetworkStandbyMode(String networkStandbyMode) {
      this.networkStandbyMode = networkStandbyMode;
   }

   public Long getPvSplPictureMode() {
      return this.pvSplPictureMode;
   }

   public void setPvSplPictureMode(Long pvSplPictureMode) {
      this.pvSplPictureMode = pvSplPictureMode;
   }

   public Long getPvMode() {
      return this.pvMode;
   }

   public void setPvMode(Long pvMode) {
      this.pvMode = pvMode;
   }

   public Long getPvContrast() {
      return this.pvContrast;
   }

   public void setPvContrast(Long pvContrast) {
      this.pvContrast = pvContrast;
   }

   public Long getPvBrightness() {
      return this.pvBrightness;
   }

   public void setPvBrightness(Long pvBrightness) {
      this.pvBrightness = pvBrightness;
   }

   public Long getPvSharpness() {
      return this.pvSharpness;
   }

   public void setPvSharpness(Long pvSharpness) {
      this.pvSharpness = pvSharpness;
   }

   public Long getPvColor() {
      return this.pvColor;
   }

   public void setPvColor(Long pvColor) {
      this.pvColor = pvColor;
   }

   public Long getPvTint() {
      return this.pvTint;
   }

   public void setPvTint(Long pvTint) {
      this.pvTint = pvTint;
   }

   public Long getPvColortone() {
      return this.pvColortone;
   }

   public void setPvColortone(Long pvColortone) {
      this.pvColortone = pvColortone;
   }

   public Long getPvColorTemperature() {
      return this.pvColorTemperature;
   }

   public void setPvColorTemperature(Long pvColorTemperature) {
      this.pvColorTemperature = pvColorTemperature;
   }

   public Long getPvSize() {
      return this.pvSize;
   }

   public void setPvSize(Long pvSize) {
      this.pvSize = pvSize;
   }

   public Long getPvDigitalnr() {
      return this.pvDigitalnr;
   }

   public void setPvDigitalnr(Long pvDigitalnr) {
      this.pvDigitalnr = pvDigitalnr;
   }

   public Long getPvFilmmode() {
      return this.pvFilmmode;
   }

   public void setPvFilmmode(Long pvFilmmode) {
      this.pvFilmmode = pvFilmmode;
   }

   public String getPvVideoPicturePositionSize() {
      return this.pvVideoPicturePositionSize;
   }

   public void setPvVideoPicturePositionSize(String pvVideoPicturePositionSize) {
      this.pvVideoPicturePositionSize = pvVideoPicturePositionSize;
   }

   public Long getPvHdmiBlackLevel() {
      return this.pvHdmiBlackLevel;
   }

   public void setPvHdmiBlackLevel(Long pvHdmiBlackLevel) {
      this.pvHdmiBlackLevel = pvHdmiBlackLevel;
   }

   public Long getPpcGamma() {
      return this.ppcGamma;
   }

   public void setPpcGamma(Long ppcGamma) {
      this.ppcGamma = ppcGamma;
   }

   public Long getPpcHdmiBlackLevel() {
      return this.ppcHdmiBlackLevel;
   }

   public void setPpcHdmiBlackLevel(Long ppcHdmiBlackLevel) {
      this.ppcHdmiBlackLevel = ppcHdmiBlackLevel;
   }

   public Long getPpcMagicBright() {
      return this.ppcMagicBright;
   }

   public void setPpcMagicBright(Long ppcMagicBright) {
      this.ppcMagicBright = ppcMagicBright;
   }

   public Long getPpcContrast() {
      return this.ppcContrast;
   }

   public void setPpcContrast(Long ppcContrast) {
      this.ppcContrast = ppcContrast;
   }

   public Long getPpcBrightness() {
      return this.ppcBrightness;
   }

   public void setPpcBrightness(Long ppcBrightness) {
      this.ppcBrightness = ppcBrightness;
   }

   public Long getPpcColortone() {
      return this.ppcColortone;
   }

   public void setPpcColortone(Long ppcColortone) {
      this.ppcColortone = ppcColortone;
   }

   public Long getPpcColorTemperature() {
      return this.ppcColorTemperature;
   }

   public void setPpcColorTemperature(Long ppcColorTemperature) {
      this.ppcColorTemperature = ppcColorTemperature;
   }

   public Long getPpcRed() {
      return this.ppcRed;
   }

   public void setPpcRed(Long ppcRed) {
      this.ppcRed = ppcRed;
   }

   public Long getPpcGreen() {
      return this.ppcGreen;
   }

   public void setPpcGreen(Long ppcGreen) {
      this.ppcGreen = ppcGreen;
   }

   public Long getPpcBlue() {
      return this.ppcBlue;
   }

   public void setPpcBlue(Long ppcBlue) {
      this.ppcBlue = ppcBlue;
   }

   public Long getPpcSize() {
      return this.ppcSize;
   }

   public void setPpcSize(Long ppcSize) {
      this.ppcSize = ppcSize;
   }

   public String getTimeCurrentTime() {
      return this.timeCurrentTime;
   }

   public void setTimeCurrentTime(String timeCurrentTime) {
      this.timeCurrentTime = timeCurrentTime;
   }

   public String getTimeOnTime() {
      return this.timeOnTime;
   }

   public void setTimeOnTime(String timeOnTime) {
      this.timeOnTime = timeOnTime;
   }

   public String getTimeOffTime() {
      return this.timeOffTime;
   }

   public void setTimeOffTime(String timeOffTime) {
      this.timeOffTime = timeOffTime;
   }

   public Long getPipSource() {
      return this.pipSource;
   }

   public void setPipSource(Long pipSource) {
      this.pipSource = pipSource;
   }

   public Long getPipSize() {
      return this.pipSize;
   }

   public void setPipSize(Long pipSize) {
      this.pipSize = pipSize;
   }

   public Long getPipPosition() {
      return this.pipPosition;
   }

   public void setPipPosition(Long pipPosition) {
      this.pipPosition = pipPosition;
   }

   public Long getPipSwap() {
      return this.pipSwap;
   }

   public void setPipSwap(Long pipSwap) {
      this.pipSwap = pipSwap;
   }

   public Long getPipControl() {
      return this.pipControl;
   }

   public void setPipControl(Long pipControl) {
      this.pipControl = pipControl;
   }

   public Long getSoundMode() {
      return this.soundMode;
   }

   public void setSoundMode(Long soundMode) {
      this.soundMode = soundMode;
   }

   public Long getSoundBass() {
      return this.soundBass;
   }

   public void setSoundBass(Long soundBass) {
      this.soundBass = soundBass;
   }

   public Long getSoundTreble() {
      return this.soundTreble;
   }

   public void setSoundTreble(Long soundTreble) {
      this.soundTreble = soundTreble;
   }

   public Long getSoundBalance() {
      return this.soundBalance;
   }

   public void setSoundBalance(Long soundBalance) {
      this.soundBalance = soundBalance;
   }

   public Long getSoundSrs() {
      return this.soundSrs;
   }

   public void setSoundSrs(Long soundSrs) {
      this.soundSrs = soundSrs;
   }

   public Long getSoundEffect() {
      return this.soundEffect;
   }

   public void setSoundEffect(Long soundEffect) {
      this.soundEffect = soundEffect;
   }

   public Long getImageCoarse() {
      return this.imageCoarse;
   }

   public void setImageCoarse(Long imageCoarse) {
      this.imageCoarse = imageCoarse;
   }

   public Long getImageFine() {
      return this.imageFine;
   }

   public void setImageFine(Long imageFine) {
      this.imageFine = imageFine;
   }

   public Long getImageHpos() {
      return this.imageHpos;
   }

   public void setImageHpos(Long imageHpos) {
      this.imageHpos = imageHpos;
   }

   public Long getImageVpos() {
      return this.imageVpos;
   }

   public void setImageVpos(Long imageVpos) {
      this.imageVpos = imageVpos;
   }

   public Long getImageAuto() {
      return this.imageAuto;
   }

   public void setImageAuto(Long imageAuto) {
      this.imageAuto = imageAuto;
   }

   public Long getSbStatus() {
      return this.sbStatus;
   }

   public void setSbStatus(Long sbStatus) {
      this.sbStatus = sbStatus;
   }

   public Long getSbRgain() {
      return this.sbRgain;
   }

   public void setSbRgain(Long sbRgain) {
      this.sbRgain = sbRgain;
   }

   public Long getSbGgain() {
      return this.sbGgain;
   }

   public void setSbGgain(Long sbGgain) {
      this.sbGgain = sbGgain;
   }

   public Long getSbBgain() {
      return this.sbBgain;
   }

   public void setSbBgain(Long sbBgain) {
      this.sbBgain = sbBgain;
   }

   public Long getSbRoffset() {
      return this.sbRoffset;
   }

   public void setSbRoffset(Long sbRoffset) {
      this.sbRoffset = sbRoffset;
   }

   public Long getSbGoffset() {
      return this.sbGoffset;
   }

   public void setSbGoffset(Long sbGoffset) {
      this.sbGoffset = sbGoffset;
   }

   public Long getSbBoffset() {
      return this.sbBoffset;
   }

   public void setSbBoffset(Long sbBoffset) {
      this.sbBoffset = sbBoffset;
   }

   public Long getSbGain() {
      return this.sbGain;
   }

   public void setSbGain(Long sbGain) {
      this.sbGain = sbGain;
   }

   public Long getSbSharp() {
      return this.sbSharp;
   }

   public void setSbSharp(Long sbSharp) {
      this.sbSharp = sbSharp;
   }

   public DeviceDisplayConfSubResource getMntAuto() {
      return this.mntAuto;
   }

   public void setMntAuto(DeviceDisplayConfSubResource mntAuto) {
      this.mntAuto = mntAuto;
   }

   public Long getMntManual() {
      return this.mntManual;
   }

   public void setMntManual(Long mntManual) {
      this.mntManual = mntManual;
   }

   public Long getMntVideoWall() {
      return this.mntVideoWall;
   }

   public void setMntVideoWall(Long mntVideoWall) {
      this.mntVideoWall = mntVideoWall;
   }

   public Long getMntFormat() {
      return this.mntFormat;
   }

   public void setMntFormat(Long mntFormat) {
      this.mntFormat = mntFormat;
   }

   public DeviceDisplayConfSubResource getMntSafetyScreenTimer() {
      return this.mntSafetyScreenTimer;
   }

   public void setMntSafetyScreenTimer(DeviceDisplayConfSubResource mntSafetyScreenTimer) {
      this.mntSafetyScreenTimer = mntSafetyScreenTimer;
   }

   public Long getMntSafetyScreenRun() {
      return this.mntSafetyScreenRun;
   }

   public void setMntSafetyScreenRun(Long mntSafetyScreenRun) {
      this.mntSafetyScreenRun = mntSafetyScreenRun;
   }

   public DeviceDisplayConfSubResource getMntPixelShift() {
      return this.mntPixelShift;
   }

   public void setMntPixelShift(DeviceDisplayConfSubResource mntPixelShift) {
      this.mntPixelShift = mntPixelShift;
   }

   public Long getMntSafetyLock() {
      return this.mntSafetyLock;
   }

   public void setMntSafetyLock(Long mntSafetyLock) {
      this.mntSafetyLock = mntSafetyLock;
   }

   public Long getAdvancedRj45SettingRefresh() {
      return this.advancedRj45SettingRefresh;
   }

   public void setAdvancedRj45SettingRefresh(Long advancedRj45SettingRefresh) {
      this.advancedRj45SettingRefresh = advancedRj45SettingRefresh;
   }

   public DeviceDisplayConfSubResource getAdvancedOsdDisplayType() {
      return this.advancedOsdDisplayType;
   }

   public void setAdvancedOsdDisplayType(DeviceDisplayConfSubResource advancedOsdDisplayType) {
      this.advancedOsdDisplayType = advancedOsdDisplayType;
   }

   public Long getAdvancedFanControl() {
      return this.advancedFanControl;
   }

   public void setAdvancedFanControl(Long advancedFanControl) {
      this.advancedFanControl = advancedFanControl;
   }

   public Long getAdvancedFanSpeed() {
      return this.advancedFanSpeed;
   }

   public void setAdvancedFanSpeed(Long advancedFanSpeed) {
      this.advancedFanSpeed = advancedFanSpeed;
   }

   public Long getAdvancedReset() {
      return this.advancedReset;
   }

   public void setAdvancedReset(Long advancedReset) {
      this.advancedReset = advancedReset;
   }

   public Long getAdvancedAutoPower() {
      return this.advancedAutoPower;
   }

   public void setAdvancedAutoPower(Long advancedAutoPower) {
      this.advancedAutoPower = advancedAutoPower;
   }

   public Long getAdvancedUserAutoColor() {
      return this.advancedUserAutoColor;
   }

   public void setAdvancedUserAutoColor(Long advancedUserAutoColor) {
      this.advancedUserAutoColor = advancedUserAutoColor;
   }

   public Long getAdvancedStandBy() {
      return this.advancedStandBy;
   }

   public void setAdvancedStandBy(Long advancedStandBy) {
      this.advancedStandBy = advancedStandBy;
   }

   public Long getMiscRemocon() {
      return this.miscRemocon;
   }

   public void setMiscRemocon(Long miscRemocon) {
      this.miscRemocon = miscRemocon;
   }

   public Long getMiscPanelLock() {
      return this.miscPanelLock;
   }

   public void setMiscPanelLock(Long miscPanelLock) {
      this.miscPanelLock = miscPanelLock;
   }

   public Long getMiscOsd() {
      return this.miscOsd;
   }

   public void setMiscOsd(Long miscOsd) {
      this.miscOsd = miscOsd;
   }

   public Long getMiscAllLock() {
      return this.miscAllLock;
   }

   public void setMiscAllLock(Long miscAllLock) {
      this.miscAllLock = miscAllLock;
   }

   public String getDiagnosisDisplayStatus() {
      return this.diagnosisDisplayStatus;
   }

   public void setDiagnosisDisplayStatus(String diagnosisDisplayStatus) {
      this.diagnosisDisplayStatus = diagnosisDisplayStatus;
   }

   public Long getDiagnosisMonitorTemperature() {
      return this.diagnosisMonitorTemperature;
   }

   public void setDiagnosisMonitorTemperature(Long diagnosisMonitorTemperature) {
      this.diagnosisMonitorTemperature = diagnosisMonitorTemperature;
   }

   public Long getDiagnosisAlarmTemperature() {
      return this.diagnosisAlarmTemperature;
   }

   public void setDiagnosisAlarmTemperature(Long diagnosisAlarmTemperature) {
      this.diagnosisAlarmTemperature = diagnosisAlarmTemperature;
   }

   public String getDiagnosisPanelOnTime() {
      return this.diagnosisPanelOnTime;
   }

   public void setDiagnosisPanelOnTime(String diagnosisPanelOnTime) {
      this.diagnosisPanelOnTime = diagnosisPanelOnTime;
   }

   public Long getChkSchChannel() {
      return this.chkSchChannel;
   }

   public void setChkSchChannel(Long chkSchChannel) {
      this.chkSchChannel = chkSchChannel;
   }

   public Boolean getWebcam() {
      return this.webcam;
   }

   public void setWebcam(Boolean webcam) {
      this.webcam = webcam;
   }

   public String getVwtId() {
      return this.vwtId;
   }

   public void setVwtId(String vwtId) {
      this.vwtId = vwtId;
   }

   public Long getChildCnt() {
      return this.childCnt;
   }

   public void setChildCnt(Long childCnt) {
      this.childCnt = childCnt;
   }

   public Long getConnChildCnt() {
      return this.connChildCnt;
   }

   public void setConnChildCnt(Long connChildCnt) {
      this.connChildCnt = connChildCnt;
   }

   public Boolean getIsChild() {
      return this.isChild;
   }

   public void setIsChild(Boolean isChild) {
      this.isChild = isChild;
   }

   public Boolean getHasChild() {
      return this.hasChild;
   }

   public void setHasChild(Boolean hasChild) {
      this.hasChild = hasChild;
   }

   public String getVwlMode() {
      return this.vwlMode;
   }

   public void setVwlMode(String vwlMode) {
      this.vwlMode = vwlMode;
   }

   public String getVwlPosition() {
      return this.vwlPosition;
   }

   public void setVwlPosition(String vwlPosition) {
      this.vwlPosition = vwlPosition;
   }

   public String getVwlFormat() {
      return this.vwlFormat;
   }

   public void setVwlFormat(String vwlFormat) {
      this.vwlFormat = vwlFormat;
   }

   public String getVwlLayout() {
      return this.vwlLayout;
   }

   public void setVwlLayout(String vwlLayout) {
      this.vwlLayout = vwlLayout;
   }

   public List getDeviceIds() {
      return this.deviceIds;
   }

   public void setDeviceIds(List deviceIds) {
      this.deviceIds = deviceIds;
   }

   public Object clone() throws CloneNotSupportedException {
      return super.clone();
   }

   public Long getOsdMenuSize() {
      return this.osdMenuSize;
   }

   public void setOsdMenuSize(Long osdMenuSize) {
      this.osdMenuSize = osdMenuSize;
   }

   public String getLedPictureSize() {
      return this.ledPictureSize;
   }

   public void setLedPictureSize(String ledPictureSize) {
      this.ledPictureSize = ledPictureSize;
   }

   public String getLedHdr() {
      return this.ledHdr;
   }

   public void setLedHdr(String ledHdr) {
      this.ledHdr = ledHdr;
   }

   public Long getLedHdrDre() {
      return this.ledHdrDre;
   }

   public void setLedHdrDre(Long ledHdrDre) {
      this.ledHdrDre = ledHdrDre;
   }

   public Long getAutoMotionPlus() {
      return this.autoMotionPlus;
   }

   public void setAutoMotionPlus(Long autoMotionPlus) {
      this.autoMotionPlus = autoMotionPlus;
   }

   public Long getAutoMotionPlusJudderReduction() {
      return this.autoMotionPlusJudderReduction;
   }

   public void setAutoMotionPlusJudderReduction(Long autoMotionPlusJudderReduction) {
      this.autoMotionPlusJudderReduction = autoMotionPlusJudderReduction;
   }

   public Long getEcoSensor() {
      return this.ecoSensor;
   }

   public void setEcoSensor(Long ecoSensor) {
      this.ecoSensor = ecoSensor;
   }

   public String getColorSpace() {
      return this.colorSpace;
   }

   public void setColorSpace(String colorSpace) {
      this.colorSpace = colorSpace;
   }

   public Long getPictureEnhancer() {
      return this.pictureEnhancer;
   }

   public void setPictureEnhancer(Long pictureEnhancer) {
      this.pictureEnhancer = pictureEnhancer;
   }

   public Float getSensorInternalTemperature() {
      return this.sensorInternalTemperature;
   }

   public void setSensorInternalTemperature(Float sensorInternalTemperature) {
      this.sensorInternalTemperature = sensorInternalTemperature;
   }

   public Float getSensorInternalHumidity() {
      return this.sensorInternalHumidity;
   }

   public void setSensorInternalHumidity(Float sensorInternalHumidity) {
      this.sensorInternalHumidity = sensorInternalHumidity;
   }

   public Float getSensorEnvironmentTemperature() {
      return this.sensorEnvironmentTemperature;
   }

   public void setSensorEnvironmentTemperature(Float sensorEnvironmentTemperature) {
      this.sensorEnvironmentTemperature = sensorEnvironmentTemperature;
   }

   public Float getSensorFrontglassTemperature() {
      return this.sensorFrontglassTemperature;
   }

   public void setSensorFrontglassTemperature(Float sensorFrontglassTemperature) {
      this.sensorFrontglassTemperature = sensorFrontglassTemperature;
   }

   public Float getSensorFrontglassHumidity() {
      return this.sensorFrontglassHumidity;
   }

   public void setSensorFrontglassHumidity(Float sensorFrontglassHumidity) {
      this.sensorFrontglassHumidity = sensorFrontglassHumidity;
   }

   public String getErrorFlag() {
      return this.errorFlag;
   }

   public void setErrorFlag(String errorFlag) {
      this.errorFlag = errorFlag;
   }

   public String getAdvancedOsdDisplayTypeValue() {
      return this.advancedOsdDisplayTypeValue;
   }

   public void setAdvancedOsdDisplayTypeValue(String advancedOsdDisplayTypeValue) {
      this.advancedOsdDisplayTypeValue = advancedOsdDisplayTypeValue;
   }

   public String getAutoBrightness() {
      return this.autoBrightness;
   }

   public void setAutoBrightness(String autoBrightness) {
      this.autoBrightness = autoBrightness;
   }

   public Long getChildAlarmTemperature() {
      return this.childAlarmTemperature;
   }

   public void setChildAlarmTemperature(Long childAlarmTemperature) {
      this.childAlarmTemperature = childAlarmTemperature;
   }

   public Long getMinBrightness() {
      return this.minBrightness;
   }

   public void setMinBrightness(Long minBrightness) {
      this.minBrightness = minBrightness;
   }

   public Long getLiveMode() {
      return this.liveMode;
   }

   public void setLiveMode(Long liveMode) {
      this.liveMode = liveMode;
   }

   public Long getDisplayOutputMode() {
      return this.displayOutputMode;
   }

   public void setDisplayOutputMode(Long displayOutputMode) {
      this.displayOutputMode = displayOutputMode;
   }

   public Boolean getCleanupUserData() {
      return this.cleanupUserData;
   }

   public void setCleanupUserData(Boolean cleanupUserData) {
      this.cleanupUserData = cleanupUserData;
   }

   public Long getCleanupUserDataInterval() {
      return this.cleanupUserDataInterval;
   }

   public void setCleanupUserDataInterval(Long cleanupUserDataInterval) {
      this.cleanupUserDataInterval = cleanupUserDataInterval;
   }

   public Long getAutoSave() {
      return this.autoSave;
   }

   public void setAutoSave(Long autoSave) {
      this.autoSave = autoSave;
   }

   public Long getAutoPowerOff() {
      return this.autoPowerOff;
   }

   public void setAutoPowerOff(Long autoPowerOff) {
      this.autoPowerOff = autoPowerOff;
   }

   public DeviceSMTPResource getSmtp() {
      return this.smtp;
   }

   public void setSmtp(String smtp) {
      if (null != smtp) {
         String[] split = smtp.split(";");
         int NUM_OF_SMTP_DATA = true;
         if (split.length >= 3) {
            DeviceSMTPResource resource = new DeviceSMTPResource();
            resource.setServerName(split[0]);

            try {
               resource.setPort(Long.parseLong(split[1]));
            } catch (Exception var6) {
            }

            resource.setSsl(Boolean.parseBoolean(split[2]));
            this.smtp = resource;
         }
      }
   }

   public DevicePrintServerResource getPrintServer() {
      return this.printServer;
   }

   public void setPrintServer(String printServer) {
      if (null != printServer) {
         String[] split = printServer.split(";");
         int NUM_OF_DATA = true;
         if (split.length >= 2) {
            DevicePrintServerResource resource = new DevicePrintServerResource();
            resource.setIp(split[0]);

            try {
               resource.setPort(Long.parseLong(split[1]));
            } catch (Exception var6) {
            }

            this.printServer = resource;
         }
      }
   }

   public Long getWeb_command() {
      return this.web_command;
   }

   public void setWeb_command(Long web_command) {
      this.web_command = web_command;
   }

   public Long getWeb_option() {
      return this.web_option;
   }

   public void setWeb_option(Long web_option) {
      this.web_option = web_option;
   }

   public String getWeb_url() {
      return this.web_url;
   }

   public void setWeb_url(String web_url) {
      this.web_url = web_url;
   }

   public Timestamp getWeb_end_time() {
      return this.web_end_time;
   }

   public void setWeb_end_time(Timestamp web_end_time) {
      this.web_end_time = web_end_time;
   }

   public Long getWeb_duration() {
      return this.web_duration;
   }

   public void setWeb_duration(Long web_duration) {
      this.web_duration = web_duration;
   }

   public Map getSettingResultSummary() {
      return this.settingResultSummary;
   }

   public void setSettingResultSummary(Map settingResultSummary) {
      this.settingResultSummary = settingResultSummary;
   }

   public Long getInstallEnvironment() {
      return this.installEnvironment;
   }

   public void setInstallEnvironment(Long installEnvironment) {
      this.installEnvironment = installEnvironment;
   }

   public Dehumidify getDehumidify() {
      return this.dehumidify;
   }

   public void setDehumidify(Dehumidify dehumidify) {
      this.dehumidify = dehumidify;
   }

   public Long getDimmingOption() {
      return this.dimmingOption;
   }

   public void setDimmingOption(Long dimmingOption) {
      this.dimmingOption = dimmingOption;
   }

   public Long getDimmingNightTimeOverride() {
      return this.dimmingNightTimeOverride;
   }

   public void setDimmingNightTimeOverride(Long dimmingNightTimeOverride) {
      this.dimmingNightTimeOverride = dimmingNightTimeOverride;
   }

   public DimmingEcoSensor getDimmingEcoSensor() {
      return this.dimmingEcoSensor;
   }

   public void setDimmingEcoSensor(DimmingEcoSensor dimmingEcoSensor) {
      this.dimmingEcoSensor = dimmingEcoSensor;
   }

   public DimmingSunriseSunset getDimmingSunriseSunset() {
      return this.dimmingSunriseSunset;
   }

   public void setDimmingSunriseSunset(DimmingSunriseSunset dimmingSunriseSunset) {
      this.dimmingSunriseSunset = dimmingSunriseSunset;
   }

   public List getDimmingSunriseSunsetTimes() {
      return this.dimmingSunriseSunsetTimes;
   }

   public void setDimmingSunriseSunsetTimes(List dimmingSunriseSunsetTimes) {
      this.dimmingSunriseSunsetTimes = dimmingSunriseSunsetTimes;
   }

   public DimmingBrightnessOutput getDimmingBrightnessOutput() {
      return this.dimmingBrightnessOutput;
   }

   public void setDimmingBrightnessOutput(DimmingBrightnessOutput dimmingBrightnessOutput) {
      this.dimmingBrightnessOutput = dimmingBrightnessOutput;
   }
}
