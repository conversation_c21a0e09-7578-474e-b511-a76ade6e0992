package com.samsung.magicinfo.webauthor2.repository.model.criteria;

import com.samsung.magicinfo.webauthor2.model.MediaType;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

public class ContentSearchCriteriaBuilder extends AbstractSearchCriteriaBuilder {
  private Integer pageSize;
  
  private String searchType;
  
  private Integer startPos;
  
  private Integer page;
  
  private String searchCreatorId;
  
  private String searchMediaType;
  
  private String searchText;
  
  private String expirationStatusFilter;
  
  public ContentSearchCriteriaBuilder setPageSize(int pageSize) {
    this.pageSize = Integer.valueOf(pageSize);
    return this;
  }
  
  public ContentSearchCriteriaBuilder setSearchType(String searchType) {
    this.searchType = searchType;
    return this;
  }
  
  public ContentSearchCriteriaBuilder setStartPos(int startPos) {
    this.startPos = Integer.valueOf(startPos);
    return this;
  }
  
  public ContentSearchCriteriaBuilder setSearchCreatorId(String searchCreatorId) {
    this.searchCreatorId = searchCreatorId;
    return this;
  }
  
  public ContentSearchCriteriaBuilder setSearchMediaType(String searchMediaType) {
    this.searchMediaType = searchMediaType;
    return this;
  }
  
  public ContentSearchCriteriaBuilder setSearchMediaType(List<MediaType> searchMediaType) {
    Assert.notNull(searchMediaType, "SearchMediaType must not be null.");
    Assert.isTrue(!searchMediaType.isEmpty(), "List of media types must not be empty");
    this.searchMediaType = StringUtils.join(searchMediaType, ',');
    return this;
  }
  
  public ContentSearchCriteriaBuilder setSearchText(String searchText) {
    this.searchText = searchText;
    return this;
  }
  
  public ContentSearchCriteriaBuilder setPage(int page) {
    this.page = Integer.valueOf(page);
    return this;
  }
  
  public ContentSearchCriteriaBuilder setExpirationStatusFilter(String expirationStatusFilter) {
    this.expirationStatusFilter = expirationStatusFilter;
    return this;
  }
  
  public ContentSearchCriteria createContentSearchCriteria() {
    if (this.startPos != null)
      return new ContentSearchCriteria(this.pageSize.intValue(), this.searchType, this.startPos.intValue(), this.searchCreatorId, this.searchMediaType, this.searchText, this.expirationStatusFilter); 
    if (this.page != null)
      return new ContentSearchCriteria(this.pageSize.intValue(), this.searchType, startPos(this.page.intValue(), this.pageSize.intValue()), this.searchCreatorId, this.searchMediaType, this.searchText, this.expirationStatusFilter); 
    throw new IllegalArgumentException("You need to add start position of page number");
  }
}
