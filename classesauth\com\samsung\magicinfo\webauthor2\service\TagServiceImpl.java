package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.Tag;
import com.samsung.magicinfo.webauthor2.repository.OpenAPITagRepository;
import com.samsung.magicinfo.webauthor2.repository.command.GetTagCommand;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.util.List;
import javax.inject.Inject;
import org.springframework.stereotype.Service;

@Service
public class TagServiceImpl implements TagService {
  private OpenAPITagRepository repository;
  
  private UserData userData;
  
  @Inject
  public TagServiceImpl(OpenAPITagRepository repository, UserData userData) {
    this.repository = repository;
    this.userData = userData;
  }
  
  public List<Tag> getTagResource() {
    return this.repository.getTagList(new GetTagCommand(this.userData.getUserId(), this.userData.getToken()));
  }
}
