package com.samsung.magicinfo.restapi.setting.service;

import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.externallink.manager.ExternalLinkInfo;
import com.samsung.magicinfo.framework.externallink.manager.ExternalLinkInfoImpl;
import com.samsung.magicinfo.framework.setup.entity.ExternalLinkEntity;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2ExternalLinkService")
@Transactional
public class V2ExternalLinkServiceImpl implements V2ExternalLinkService {
   public V2ExternalLinkServiceImpl() {
      super();
   }

   public ExternalLinkEntity addExternalLink(String linkId, String linkUrl) throws Exception {
      if (StringUtils.isNotBlank(linkId) && StringUtils.isNotBlank(linkUrl)) {
         Long organizationId = SecurityUtils.getLoginUserOrganizationId();
         ExternalLinkInfo externalLinkInfo = ExternalLinkInfoImpl.getInstance();
         if (externalLinkInfo.isExistLinkNameByOrgId(linkId, organizationId)) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_EXTERNAL_LINK_ID_DUPLICATED);
         }

         if (externalLinkInfo.addLink(linkId, linkUrl, organizationId)) {
            ExternalLinkEntity externalLink = new ExternalLinkEntity();
            externalLink.setLinkId(linkId);
            externalLink.setLinkUrl(linkUrl);
            externalLink.setOrganizationId(organizationId);
            return externalLink;
         }
      }

      return null;
   }

   public void deleteExternalLink(String linkId) throws Exception {
      if (StringUtils.isNotBlank(linkId)) {
         Long organizationId = SecurityUtils.getLoginUserOrganizationId();
         ExternalLinkInfo externalLinkInfo = ExternalLinkInfoImpl.getInstance();
         Map externalLink = externalLinkInfo.getExistingLinkByName(linkId, organizationId);
         if (externalLink == null || externalLink.size() == 0) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_EXTERNAL_LINK);
         }

         externalLinkInfo.deleteLink(linkId, organizationId);
      }

   }

   public List getExternalLinks() throws Exception {
      Long organizationId = SecurityUtils.getLoginUserOrganizationId();
      ExternalLinkInfo externalLinkInfo = ExternalLinkInfoImpl.getInstance();
      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Map infoMap = serverSetupDao.getServerInfoByOrgId(organizationId);
      List resultList = new ArrayList();
      new ArrayList();
      if (infoMap != null && infoMap.get("EXT_LINK_ENABLE") != null) {
         String externalLinkEnable = infoMap.get("EXT_LINK_ENABLE").toString();
         if (externalLinkEnable.equalsIgnoreCase("true")) {
            List linkList = externalLinkInfo.retSecurityFilter(organizationId);
            linkList.forEach((map) -> {
               resultList.add(new ExternalLinkEntity(Long.valueOf(map.get("ORGANIZATION_ID").toString()), map.get("LINK_ID").toString(), map.get("LINK_URL").toString()));
            });
         }
      }

      return resultList;
   }
}
