package com.samsung.magicinfo.framework.setup.manager;

import com.samsung.magicinfo.framework.setup.dao.CategoryDao;
import com.samsung.magicinfo.framework.setup.entity.CategoryEntity;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

public class CategoryInfoImpl implements CategoryInfo {
   private static CategoryDao dao = null;
   private static CategoryInfoImpl info = null;

   public CategoryInfoImpl() {
      super();
   }

   public static CategoryInfoImpl getInstance() {
      if (info == null) {
         info = new CategoryInfoImpl();
      }

      if (dao == null) {
         dao = new CategoryDao();
      }

      return info;
   }

   public List getCategoryWithContentId(String contentId) throws SQLException {
      return dao.getCategoryWithContentId(contentId);
   }

   public List getCategoryWithPlaylistId(String playlist) throws SQLException {
      return dao.getCategoryWithPlaylistId(playlist);
   }

   public List getCategoryWithPgroupId(long groupId) throws SQLException {
      return dao.getCategoryWithPgroupId(groupId);
   }

   public List getCategoryWithPgroupId(long groupId, String organization) throws SQLException {
      return organization != null && organization.equals("ALL") ? dao.getCategoryWithPgroupId(0L) : dao.getCategoryWithPgroupId(groupId, organization);
   }

   public long addCategory(CategoryEntity category) throws SQLException {
      return dao.addCategory(category);
   }

   public boolean deleteCategory(long groupId) throws SQLException {
      return dao.deleteCategory(groupId);
   }

   public CategoryEntity getCategory(long groupId) throws SQLException {
      return dao.getCategory(groupId);
   }

   public Map getCategoryTag(String contentId) throws SQLException {
      return dao.getCategoryTag(contentId);
   }

   public void setCategoryFromContentId(String category, String contentId) throws SQLException {
      dao.setCategoryFromContentId(category, contentId);
   }

   public void setCategoryFromPlaylistId(String category, String playlistId) throws SQLException {
      dao.setCategoryFromPlaylistId(category, playlistId);
   }

   public void setCategoryFromPlaylistId(List category, String playlistId) throws SQLException {
      dao.setCategoryFromPlaylistId(category, playlistId);
   }

   public boolean deleteCategoryFromPlaylistId(String playlistId) throws SQLException {
      return dao.deleteCategoryFromPlaylistId(playlistId);
   }

   public boolean moveCategory(long parentId, long groupId) throws SQLException {
      return dao.moveCategory(parentId, groupId);
   }

   public boolean deleteCategoryFromContentId(String contentId) throws SQLException {
      return dao.deleteCategoryFromContentId(contentId);
   }

   public boolean updateCategory(CategoryEntity category) throws SQLException {
      return dao.updateCategory(category);
   }

   public List getCategoryByMultipleOrg(List userList) throws SQLException {
      return dao.getCategoryByMultipleOrg(userList);
   }
}
