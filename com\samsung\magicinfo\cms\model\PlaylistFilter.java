package com.samsung.magicinfo.cms.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

public class PlaylistFilter {
   @ApiModelProperty(
      example = "1"
   )
   private int startIndex = 1;
   @ApiModelProperty(
      example = "10"
   )
   private int pageSize = 10;
   @ApiModelProperty(
      example = "last_modified_date"
   )
   @Pattern(
      regexp = "last_modified_date",
      message = "[PlaylistFilter][sortColumn]Only last_modified_date are available."
   )
   private String sortColumn = "last_modified_date";
   @ApiModelProperty(
      example = "desc"
   )
   @Pattern(
      regexp = "desc|asc",
      message = "[PlaylistFilter][sortOrder]Only asc, desc are available."
   )
   private String sortOrder = "desc";
   @ApiModelProperty(
      example = "test"
   )
   @Size(
      max = 20,
      message = "[PlaylistFilter][searchText]max size is 20."
   )
   private String searchText = "";
   @ApiModelProperty(
      example = "admin"
   )
   @Size(
      max = 20,
      message = "[PlaylistFilter][creatorId]max size is 20."
   )
   private String creatorId = "admin";
   @JsonIgnore
   private String endDate = "";
   @JsonIgnore
   private String startDate = "";
   @ApiModelProperty(
      example = "admin"
   )
   @Size(
      max = 20,
      message = "[PlaylistFilter][userId]max size is 20."
   )
   private String userId = "admin";
   @ApiModelProperty(
      example = "ALL"
   )
   private String listType = "ALL";
   @ApiModelProperty(
      example = "0"
   )
   private String groupId = "0";
   @ApiModelProperty(
      example = "SPLAYER"
   )
   private String deviceType = null;
   @ApiModelProperty(
      example = "0"
   )
   private String playlistType = null;
   @ApiModelProperty(
      example = "0"
   )
   private String category;

   public PlaylistFilter() {
      super();
   }

   public String getCategory() {
      return this.category;
   }

   public void setCategory(String category) {
      this.category = category;
   }

   public int getStartIndex() {
      return this.startIndex;
   }

   public void setStartIndex(int startIndex) {
      this.startIndex = startIndex;
   }

   public int getPageSize() {
      return this.pageSize;
   }

   public void setPageSize(int pageSize) {
      this.pageSize = pageSize;
   }

   public String getDeviceType() {
      return this.deviceType;
   }

   public void setDeviceType(String deviceType) {
      this.deviceType = deviceType;
   }

   public String getPlaylistType() {
      return this.playlistType;
   }

   public void setPlaylistType(String playlistType) {
      this.playlistType = playlistType;
   }

   public String getGroupId() {
      return this.groupId;
   }

   public void setGroupId(String groupId) {
      this.groupId = groupId;
   }

   public String getSortColumn() {
      return this.sortColumn;
   }

   public void setSortColumn(String sortColumn) {
      this.sortColumn = sortColumn;
   }

   public String getSortOrder() {
      return this.sortOrder;
   }

   public void setSortOrder(String sortOrder) {
      this.sortOrder = sortOrder;
   }

   public String getSearchText() {
      return this.searchText;
   }

   public void setSearchText(String searchText) {
      this.searchText = searchText;
   }

   public String getCreatorId() {
      return this.creatorId;
   }

   public void setCreatorId(String creatorId) {
      this.creatorId = creatorId;
   }

   public String getEndDate() {
      return this.endDate;
   }

   public void setEndDate(String endDate) {
      this.endDate = endDate;
   }

   public String getStartDate() {
      return this.startDate;
   }

   public void setStartDate(String startDate) {
      this.startDate = startDate;
   }

   public String getUserId() {
      return this.userId;
   }

   public void setUserId(String userId) {
      this.userId = userId;
   }

   public String getListType() {
      return this.listType;
   }

   public void setListType(String listType) {
      this.listType = listType;
   }
}
