package com.samsung.magicinfo.webauthor2.repository;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.repository.model.OpenAPIResponseData;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

public class AddDlkInfoOpenApiMethod extends OpenApiMethod<Void, OpenAPIResponseData> {
  private static final Logger logger = LoggerFactory.getLogger(AddDlkInfoOpenApiMethod.class);
  
  private final String userId;
  
  private final String token;
  
  private final String dlkContentId;
  
  private final String dlkVersionId;
  
  private final String lftContentId;
  
  private final List<String> contentsList;
  
  private final String convertTableXml;
  
  protected AddDlkInfoOpenApiMethod(RestTemplate restTemplate, String userId, String token, String dlkContentId, String dlkVersionId, String lftContentId, List<String> contentsList, String convertTableXml) {
    super(restTemplate, OpenApiMethodType.POST);
    this.userId = userId;
    this.token = token;
    this.dlkContentId = dlkContentId;
    this.dlkVersionId = dlkVersionId;
    this.lftContentId = lftContentId;
    this.contentsList = contentsList;
    this.convertTableXml = convertTableXml;
  }
  
  protected String getOpenApiClassName() {
    return "CommonContentService";
  }
  
  protected String getOpenApiMethodName() {
    return "addDlkInfo";
  }
  
  protected Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("userId", this.userId);
    vars.put("token", this.token);
    vars.put("dlk_content_id", this.dlkContentId);
    vars.put("dlk_version_id", this.dlkVersionId);
    vars.put("dlkMapContentList", this.lftContentId);
    vars.put("convertTableList", this.convertTableXml);
    return vars;
  }
  
  Class<OpenAPIResponseData> getResponseClass() {
    return OpenAPIResponseData.class;
  }
  
  Void convertResponseData(OpenAPIResponseData responseData) {
    String CODE_RESPONSE_OK = "0";
    if (responseData == null || Strings.isNullOrEmpty(responseData.getCode())) {
      logger.error("Unknown problem with adding dlk info!");
    } else if (!responseData.getCode().equals("0")) {
      logger.error("Problem with adding dlk info!");
      logger.error("Error code == " + responseData.getCode());
      logger.error("Error message == " + responseData.getErrorMessage());
    } else {
      logger.info("DLK info have been sent");
    } 
    return null;
  }
}
