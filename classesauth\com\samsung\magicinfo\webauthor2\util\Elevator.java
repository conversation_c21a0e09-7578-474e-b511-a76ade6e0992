package com.samsung.magicinfo.webauthor2.util;

import com.sun.jna.WString;
import com.sun.jna.platform.win32.Kernel32;
import com.sun.jna.platform.win32.Kernel32Util;

public class Elevator {
  public static void executeAsAdmin(String command, String args) {
    Shell32X.SHELLEXECUTEINFO execInfo = new Shell32X.SHELLEXECUTEINFO();
    execInfo.lpFile = new WString(command);
    if (args != null)
      execInfo.lpParameters = new WString(args); 
    execInfo.nShow = 10;
    execInfo.fMask = 64;
    execInfo.lpVerb = new WString("runas");
    boolean result = Shell32X.INSTANCE.ShellExecuteEx(execInfo);
    if (!result) {
      int lastError = Kernel32.INSTANCE.GetLastError();
      String errorMessage = Kernel32Util.formatMessageFromLastErrorCode(lastError);
      throw new RuntimeException("Error performing elevation: " + lastError + ": " + errorMessage + " (apperror=" + execInfo.hInstApp + ")");
    } 
  }
}
