package com.samsung.magicinfo.restapi.setting.service;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemSetupConf;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManager;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManagerImpl;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerInfo;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerInfoImpl;
import com.samsung.magicinfo.framework.setup.dao.DatalinkServerDao;
import com.samsung.magicinfo.framework.setup.entity.DatalinkServerEntity;
import com.samsung.magicinfo.framework.setup.entity.DatalinkServerTableEntity;
import com.samsung.magicinfo.framework.setup.entity.DatalinkTableItem;
import com.samsung.magicinfo.framework.setup.entity.DatalinkTableItemList;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.restapi.exception.BaseRestException;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.setting.model.V2DatalinkEditByPassResource;
import com.samsung.magicinfo.restapi.setting.model.V2DatalinkServerTableEntity;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.StringReader;
import java.net.Socket;
import java.net.URL;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.KeyManager;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLEngine;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509ExtendedTrustManager;
import javax.net.ssl.X509TrustManager;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import javax.xml.parsers.FactoryConfigurationError;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

@Service
@Transactional
public class V2DatalinkServiceImpl implements V2DatalinkService {
   private Logger logger = LoggingManagerV2.getLogger(V2DatalinkServiceImpl.class);
   private static final String datalinkTableApiUrl = "/getDataTableList.do";
   private static final int MAX_DATALINK_SERVER = 100;
   public DatalinkServerDao datalinkServerDao = new DatalinkServerDao();
   public ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();

   public V2DatalinkServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public Boolean addDatalink(DatalinkServerEntity datalinkServerEntity) throws Exception {
      int result = this.datalinkServerDao.addDatalinkServer(datalinkServerEntity);
      if (result > 0) {
         this.setDeviceDatalinkServerTriggering();
      }

      return result > 0;
   }

   public List getAllDatalinkServerList() throws Exception {
      List retList = null;
      retList = this.datalinkServerDao.getDatalinkServerListPage((Map)null, 1, 100);
      return retList;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public V2PageResource getDatalinkList(Map searchConfig, int startPos, int pageSize) throws Exception {
      List datalinkList = this.datalinkServerDao.getDatalinkServerListPage(searchConfig, startPos, pageSize);
      Integer totalCount = this.datalinkServerDao.getDatalinkServerListCnt(searchConfig);
      return V2PageResource.createPageResource(datalinkList, totalCount);
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public Boolean saveDatalink(DatalinkServerEntity datalinkServerEntity) throws Exception {
      Integer result = this.datalinkServerDao.updateDatalinkServer(datalinkServerEntity);
      return result == 1;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public Boolean checkDatalinkServerName(String nameToCheck) throws Exception {
      DatalinkServerEntity datalinkServerEntity = this.datalinkServerDao.getDatalinkServerInfo(nameToCheck);
      return datalinkServerEntity != null ? true : false;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public Integer deleteDatalinkServer(String serverName) throws Exception {
      int result = 0;
      DatalinkServerEntity datalinkServerEntity = this.datalinkServerDao.getDatalinkServerInfo(serverName);
      int result = result + this.datalinkServerDao.deleteDatalinkServer(datalinkServerEntity);
      this.serverSetupDao.deleteExternalServerForMonitoring("DATALINK", datalinkServerEntity.getIp_address());
      if (result > 0) {
         this.setDeviceDatalinkServerTriggering();
      }

      return result;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public Boolean editDatalinkBypass(V2DatalinkEditByPassResource v2DatalinkEditByPassResource) throws Exception {
      int result = false;
      Boolean bypass = v2DatalinkEditByPassResource.getBypass();
      String serverName = v2DatalinkEditByPassResource.getServerName();
      if (serverName != null && !serverName.equals("") && bypass != null) {
         DatalinkServerEntity datalinkServerEntity = new DatalinkServerEntity();
         datalinkServerEntity.setBypass(bypass);
         datalinkServerEntity.setServer_name(serverName);
         this.datalinkServerDao.updateDatalinkServer(datalinkServerEntity);
      }

      return null;
   }

   public Boolean setDeviceDatalinkServerTriggering() {
      try {
         MonitoringManager monMgr = MonitoringManagerImpl.getInstance();
         MonitoringManagerInfo dao = MonitoringManagerInfoImpl.getInstance("PREMIUM");
         DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
         Map connectionInfoMap = null;
         String deviceId = null;
         String dlsMO = null;
         int sendCount = 0;
         int len = false;
         List connectionInfo = dao.getConnectionInfo();
         int len = connectionInfo.size();
         if (len > 0) {
            dlsMO = this.getDatalinkServerMOString();
         }

         for(int i = 0; i < len; ++i) {
            connectionInfoMap = (Map)connectionInfo.get(i);
            deviceId = (String)connectionInfoMap.get("device_id");
            if (monMgr.isConnected(deviceId)) {
               DeviceSystemSetupConf info = new DeviceSystemSetupConf();
               info.setDevice_id(deviceId);
               info.setDatalink_server(dlsMO);
               confManager.reqSetSystemSetupToDevice(info, "");
               ++sendCount;
            }

            if (sendCount % 20 == 0) {
               Thread.sleep(1L);
            }
         }
      } catch (Exception var12) {
         this.logger.error("", var12);
      }

      return true;
   }

   public String getDatalinkServerMOString() {
      StringBuffer rtn = new StringBuffer("");

      try {
         List dlsEntityList = this.getAllDatalinkServerList();
         if (dlsEntityList == null) {
            return rtn.toString();
         }

         for(int i = 0; i < dlsEntityList.size(); ++i) {
            if (i > 0) {
               rtn.append("@");
            }

            rtn.append(((DatalinkServerEntity)dlsEntityList.get(i)).getServer_name() == null ? "" : ((DatalinkServerEntity)dlsEntityList.get(i)).getServer_name()).append(";");
            rtn.append(((DatalinkServerEntity)dlsEntityList.get(i)).getIp_address() == null ? "" : ((DatalinkServerEntity)dlsEntityList.get(i)).getIp_address()).append(";");
            rtn.append(((DatalinkServerEntity)dlsEntityList.get(i)).getPeriod() == null ? "10" : ((DatalinkServerEntity)dlsEntityList.get(i)).getPeriod()).append(";");
            rtn.append(((DatalinkServerEntity)dlsEntityList.get(i)).getPort() == null ? "8080" : ((DatalinkServerEntity)dlsEntityList.get(i)).getPort()).append(";");
            rtn.append(((DatalinkServerEntity)dlsEntityList.get(i)).getUse_ssl() == null ? "0" : (((DatalinkServerEntity)dlsEntityList.get(i)).getUse_ssl() ? "1" : "0")).append(";");
            rtn.append(((DatalinkServerEntity)dlsEntityList.get(i)).getFtp_port() == null ? "21" : ((DatalinkServerEntity)dlsEntityList.get(i)).getFtp_port()).append(";");
            rtn.append(((DatalinkServerEntity)dlsEntityList.get(i)).getBypass() == null ? "0" : (((DatalinkServerEntity)dlsEntityList.get(i)).getBypass() ? "1" : "0"));
         }
      } catch (Exception var4) {
         return rtn.toString();
      }

      return rtn.toString();
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public Integer addOrgToTableMapping(Long orgId, V2DatalinkServerTableEntity v2DatalinkServerTableEntity) throws Exception {
      Integer result = this.datalinkServerDao.addV2DatalinkTableToOrg(Math.toIntExact(orgId), v2DatalinkServerTableEntity);
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public Integer deleteAllOrgToTableMapping() throws Exception {
      Integer result = this.datalinkServerDao.deleteAllDatalinkMapOrgTable();
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public Integer deleteOrgToTableMappingInfoByServerName(String serverName) throws Exception {
      Integer result = this.datalinkServerDao.deleteDatalinkMapOrgTableByServer(serverName);
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public List getDatalinkServerTableByOrgIdAndServerName(Long orgId, String serverName) throws Exception {
      List selectedDatalinkServerTableByOrgId = this.datalinkServerDao.getSelectedV2DatalinkServerTableByOrgIdAndServer(orgId, serverName);
      return selectedDatalinkServerTableByOrgId;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public List getAllDatalinkServerTable(String serverName) throws Exception {
      List allDatalinkServerTableInfo = this.datalinkServerDao.getAllV2DatalinkServerTableInfo(serverName);
      return allDatalinkServerTableInfo;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public String updateDatalinkServerTables(String serverName) throws Exception {
      Boolean result = null;
      DatalinkServerEntity datalinkServerEntity = this.datalinkServerDao.getDatalinkServerInfo(serverName);
      String url = this.getDatalinkServerTableUrl(datalinkServerEntity);
      String body = null;
      if (datalinkServerEntity.getUse_ssl()) {
         body = this.updateDatalinkHttps(url);
      } else {
         body = this.updateDatalinkHttp(url);
      }

      if (body != null && !body.isEmpty() && body != "") {
         try {
            JAXBContext jaxbContext = JAXBContext.newInstance(new Class[]{DatalinkTableItemList.class});
            Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();

            try {
               int itemlistSize = false;
               XMLStreamReader reader = XMLInputFactory.newInstance().createXMLStreamReader(new StringReader(body));
               JAXBElement datalinkTableItemList = jaxbUnmarshaller.unmarshal(reader, DatalinkTableItemList.class);
               if (datalinkTableItemList != null && datalinkTableItemList.getValue() != null && ((DatalinkTableItemList)datalinkTableItemList.getValue()).getItem() != null) {
                  int itemlistSize = ((DatalinkTableItemList)datalinkTableItemList.getValue()).getItem().size();
                  if (itemlistSize <= 0) {
                     return "empty";
                  } else {
                     List datalinkTableList = null;
                     datalinkTableList = this.datalinkServerDao.getAllDatalinkServerTableInfo(datalinkServerEntity.getServer_name());
                     Map isDeleteService = new HashMap();
                     Iterator var13;
                     if (datalinkTableList != null) {
                        var13 = datalinkTableList.iterator();

                        while(var13.hasNext()) {
                           DatalinkServerTableEntity table = (DatalinkServerTableEntity)var13.next();
                           isDeleteService.put(table.getDyna_name(), true);
                        }
                     }

                     String key;
                     for(int i = 0; i < itemlistSize; ++i) {
                        key = ((DatalinkTableItem)((DatalinkTableItemList)datalinkTableItemList.getValue()).getItem().get(i)).getSvrcName();
                        String dynaName = ((DatalinkTableItem)((DatalinkTableItemList)datalinkTableItemList.getValue()).getItem().get(i)).getDynaName();
                        String tableName = ((DatalinkTableItem)((DatalinkTableItemList)datalinkTableItemList.getValue()).getItem().get(i)).getName();
                        Boolean isDataView = key == null;
                        DatalinkServerTableEntity dlkTableEntity = new DatalinkServerTableEntity();
                        isDeleteService.put(dynaName, false);

                        try {
                           if (dynaName != null && tableName != null) {
                              dlkTableEntity.setIs_dataView(isDataView);
                              dlkTableEntity.setServer_name(datalinkServerEntity.getServer_name());
                              if (key == null) {
                                 key = tableName;
                              }

                              dlkTableEntity.setService_name(key);
                              dlkTableEntity.setTable_name(tableName);
                              dlkTableEntity.setDyna_name(dynaName);
                              DatalinkServerTableEntity entity = this.datalinkServerDao.getTableInfoByDynaNameAndServerName(dynaName, serverName);
                              if (entity == null) {
                                 this.datalinkServerDao.addDatalinkTableInfo(dlkTableEntity);
                              } else {
                                 this.datalinkServerDao.updateDatalinkTableInfo(dlkTableEntity);
                              }
                           }
                        } catch (SQLException var21) {
                           this.logger.error("Error in method updateDatalinkServerTable " + var21.getMessage());
                           return "error";
                        }
                     }

                     try {
                        if (isDeleteService.size() > 0) {
                           var13 = isDeleteService.keySet().iterator();

                           while(var13.hasNext()) {
                              key = (String)var13.next();
                              if ((Boolean)isDeleteService.get(key)) {
                                 this.datalinkServerDao.deleteDatalinkMapOrgTableByDynaName(key);
                                 this.datalinkServerDao.deleteDatalinkInfoServerTableByDynaName(key);
                              }
                           }
                        }

                        return "success";
                     } catch (SQLException var20) {
                        this.logger.error("Error in deleteDatalinkServerTables " + var20.getMessage());
                        return "error";
                     }
                  }
               } else {
                  return "empty";
               }
            } catch (FactoryConfigurationError | XMLStreamException var22) {
               this.logger.error("Error in method updateDatalinkServerTable Unmarshall xml XMLStreamException" + var22.getMessage());
               return "error";
            }
         } catch (JAXBException var23) {
            this.logger.error("Error in method updateDatalinkServerTable Unmarshall xml JAXBException " + var23.getMessage());
            return "error";
         }
      } else {
         throw new BaseRestException(RestExceptionCode.INTERNAL_SERVER_ERROR_DATALINK_TABLE_UPDATE.getCode(), RestExceptionCode.INTERNAL_SERVER_ERROR_DATALINK_TABLE_UPDATE.getMessage());
      }
   }

   private String updateDatalinkHttps(String datalinkUrl) throws Exception {
      String body = "";
      HttpHeaders headers = new HttpHeaders();
      headers.set("Accept", "text/plain;charset=utf-8");
      HttpEntity entity = new HttpEntity(headers);

      try {
         URL url = new URL(datalinkUrl);
         HttpsURLConnection conn = (HttpsURLConnection)url.openConnection();
         conn.setHostnameVerifier(new HostnameVerifier() {
            @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
            public boolean verify(String hostname, SSLSession session) {
               return true;
            }
         });
         SSLContext context = SSLContext.getInstance("TLS");
         TrustManager[] trustAllCerts = new TrustManager[]{new X509ExtendedTrustManager() {
            @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
            public void checkClientTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
            }

            @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
            public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
               FileInputStream fis = null;

               try {
                  String keyStoreIdentityPath = "";
                  String keyStoreIdentityPassword = "";
                  keyStoreIdentityPath = CommonConfig.get("keystore.identity.path");
                  keyStoreIdentityPassword = CommonConfig.get("keystore.identity.password");
                  KeyStore trustStore = KeyStore.getInstance("JKS");
                  fis = new FileInputStream(keyStoreIdentityPath);
                  trustStore.load(fis, keyStoreIdentityPassword.toCharArray());
                  fis.close();
                  TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
                  tmf.init(trustStore);
                  TrustManager[] tms = tmf.getTrustManagers();
                  ((X509TrustManager)tms[0]).checkServerTrusted(chain, authType);
               } catch (KeyStoreException var23) {
                  V2DatalinkServiceImpl.this.logger.error("External Server Monitoring - KeyStore Exception");
               } catch (NoSuchAlgorithmException var24) {
                  V2DatalinkServiceImpl.this.logger.error("External Server Monitoring - No Such Algorithm Exception");
               } catch (IOException var25) {
                  V2DatalinkServiceImpl.this.logger.error("External Server Monitoring - Input Output Exception");
               } catch (ConfigException var26) {
                  V2DatalinkServiceImpl.this.logger.error("", var26);
               } finally {
                  try {
                     fis.close();
                  } catch (IOException var22) {
                     V2DatalinkServiceImpl.this.logger.error("External Server Monitoring - FIS IOException");
                  }

               }

            }

            @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
            public X509Certificate[] getAcceptedIssuers() {
               return null;
            }

            @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
            public void checkClientTrusted(X509Certificate[] x509Certificates, String s, Socket socket) throws CertificateException {
            }

            @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
            public void checkServerTrusted(X509Certificate[] x509Certificates, String s, Socket socket) throws CertificateException {
            }

            @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
            public void checkClientTrusted(X509Certificate[] x509Certificates, String s, SSLEngine sslEngine) throws CertificateException {
            }

            @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
            public void checkServerTrusted(X509Certificate[] x509Certificates, String s, SSLEngine sslEngine) throws CertificateException {
            }
         }};
         context.init((KeyManager[])null, trustAllCerts, (SecureRandom)null);
         SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(context, NoopHostnameVerifier.INSTANCE);
         Registry registry = RegistryBuilder.create().register("http", new PlainConnectionSocketFactory()).register("https", csf).build();
         PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager(registry);
         cm.setMaxTotal(100);
         CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(csf).setConnectionManager(cm).build();
         HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
         requestFactory.setHttpClient(httpClient);
         RestTemplate restTemplate = new RestTemplate(requestFactory);
         ResponseEntity response = restTemplate.exchange(datalinkUrl, HttpMethod.GET, entity, String.class, new Object[0]);
         body = (String)response.getBody();
      } catch (Exception var16) {
         this.logger.error("updateDatalinkHttps error : " + var16.getMessage());
      }

      return body;
   }

   private String updateDatalinkHttp(String url) {
      HttpHeaders headers = new HttpHeaders();
      RestTemplate restTemplate = getRestTemplate();
      headers.set("Accept", "text/plain;charset=utf-8");
      HttpEntity entity = new HttpEntity(headers);
      ResponseEntity responseData = restTemplate.exchange(url, HttpMethod.GET, entity, String.class, new Object[0]);
      return (String)responseData.getBody();
   }

   private String getDatalinkServerTableUrl(DatalinkServerEntity datalinkServerEntity) {
      String httpInfo = "";
      StringBuilder urlToReturn = new StringBuilder();
      if (datalinkServerEntity.getUse_ssl()) {
         httpInfo = "https://";
      } else {
         httpInfo = "http://";
      }

      if (datalinkServerEntity.getPrivate_mode()) {
         urlToReturn.append(httpInfo).append(datalinkServerEntity.getPrivate_ip_address()).append(":").append(datalinkServerEntity.getPrivate_web_port());
      } else {
         urlToReturn.append(httpInfo).append(datalinkServerEntity.getIp_address()).append(":").append(datalinkServerEntity.getPort());
      }

      urlToReturn.append("/DataLink/html").append("/getDataTableList.do");
      return urlToReturn.toString();
   }

   private static RestTemplate getRestTemplate() {
      HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
      factory.setConnectTimeout(1000);
      factory.setReadTimeout(1000);
      RestTemplate restTemplate = new RestTemplate(factory);
      return restTemplate;
   }
}
