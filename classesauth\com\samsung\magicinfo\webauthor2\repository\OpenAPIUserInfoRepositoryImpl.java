package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.model.UserInfo;
import com.samsung.magicinfo.webauthor2.util.UserData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.web.client.RestTemplate;

@Repository
public class OpenAPIUserInfoRepositoryImpl implements OpenAPIUserInfoRepository {
  private RestTemplate restTemplate;
  
  private UserData userData;
  
  @Autowired
  public OpenAPIUserInfoRepositoryImpl(RestTemplate restTemplate, UserData userData) {
    this.restTemplate = restTemplate;
    this.userData = userData;
  }
  
  public UserInfo getUserInfo(String userId) {
    GetUserInfoOpenApiMethod openApiMethod = new GetUserInfoOpenApiMethod(this.restTemplate, this.userData.getToken(), userId);
    return openApiMethod.callMethod();
  }
}
