package com.samsung.magicinfo.webauthor2.model;

import com.samsung.magicinfo.webauthor2.repository.model.ContentData;
import com.samsung.magicinfo.webauthor2.util.ResolutionUtil;

public class Image {
  private int width;
  
  private int height;
  
  public static Image fromData(ContentData contentData) {
    if (contentData.getResolution() == null)
      return new Image(10, 10); 
    if (contentData.getResolution().isEmpty())
      return new Image(10, 10); 
    int[] resolution = ResolutionUtil.parseResolution(contentData.getResolution());
    return new Image(resolution[0], resolution[1]);
  }
  
  public Image(int width, int height) {
    this.width = width;
    this.height = height;
  }
  
  public Image() {}
  
  public int getWidth() {
    return this.width;
  }
  
  public int getHeight() {
    return this.height;
  }
}
