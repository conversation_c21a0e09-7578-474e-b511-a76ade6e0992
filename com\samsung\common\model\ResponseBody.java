package com.samsung.common.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel
@JsonInclude(Include.NON_EMPTY)
public class ResponseBody {
   private String apiVersion;
   private String status;
   @JsonInclude(Include.ALWAYS)
   private Object items;
   private Integer totalCount;
   private Integer startIndex;
   private Integer pageSize;
   private String errorCode;
   private String errorMessage;

   public ResponseBody() {
      this("2.0", "Success");
      this.apiVersion = "";
      this.status = "";
      this.items = null;
      this.totalCount = null;
      this.startIndex = null;
      this.pageSize = null;
      this.errorCode = "";
      this.errorMessage = "";
   }

   public ResponseBody(Object items) {
      this();
      this.setItems(items);
   }

   public ResponseBody(String apiVersion, String status) {
      super();
      this.setApiVersion(apiVersion);
      this.setStatus(status);
   }

   @ApiModelProperty(
      example = "2.0"
   )
   public String getApiVersion() {
      return this.apiVersion;
   }

   public void setApiVersion(String apiVersion) {
      this.apiVersion = apiVersion;
   }

   @ApiModelProperty(
      example = "Success"
   )
   public String getStatus() {
      return this.status;
   }

   public void setStatus(String status) {
      this.status = status;
   }

   @ApiModelProperty(
      example = "200"
   )
   public String getErrorCode() {
      return this.errorCode;
   }

   public void setErrorCode(String errorCode) {
      this.errorCode = errorCode;
   }

   @ApiModelProperty(
      example = "OK"
   )
   public String getErrorMessage() {
      return this.errorMessage;
   }

   public void setErrorMessage(String message) {
      this.errorMessage = message;
   }

   @ApiModelProperty(
      example = "47"
   )
   public Integer getTotalCount() {
      return this.totalCount;
   }

   public void setTotalCount(Integer totalCount) {
      this.totalCount = totalCount;
   }

   @ApiModelProperty(
      example = "1"
   )
   public Integer getStartIndex() {
      return this.startIndex;
   }

   public void setStartIndex(Integer startIndex) {
      this.startIndex = startIndex;
   }

   @ApiModelProperty(
      example = "20"
   )
   public Integer getPageSize() {
      return this.pageSize;
   }

   public void setPageSize(Integer pageSize) {
      this.pageSize = pageSize;
   }

   public Object getItems() {
      return this.items;
   }

   public void setItems(Object items) {
      this.items = items;
   }
}
