<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceDaoMapper">

    <insert id="addDevice">
        INSERT INTO MI_DMS_INFO_DEVICE ( DEVICE_ID,
        DEVICE_MODEL_CODE, DEVICE_MODEL_NAME,
        DEVICE_NAME, SERIAL_DECIMAL, SCREEN_SIZE,
        FIRMWARE_VERSION, APPLICATION_VERSION,
        RULE_VERSION, OS_IMAGE_VERSION, CPU_TYPE,
        HDD_SIZE, MEM_SIZE, VIDEO_ADAPTER,
        VIDEO_MEMORY, VIDEO_DRIVER, NETWORK_ADAPTER,
        NETWORK_DRIVER, MAC_ADDRESS,
        IP_SETTING_TYPE, IP_ADDRESS, SUBNET_MASK,
        GATEWAY, DNS_SERVER_MAIN, DNS_SERVER_SUB,
        PORT, TRIGGER_INTERVAL, MONITORING_INTERVAL,
        LOCATION, IS_APPROVED, TUNNELING_SERVER,
        BG_COLOR, AUTO_TIME_SETTING,
        ON_TIMER_SETTING, OFF_TIMER_SETTING,
        TIME_ZONE_INDEX, DAY_LIGHT_SAVING,
        FTP_CONNECT_MODE, REPOSITORY_PATH,
        MAGICINFO_SERVER_URL, SCREEN_CAPTURE_INTERVAL,
        EWF_STATE, RESOLUTION, DEVICE_TYPE, DEVICE_TYPE_VERSION,
        IS_REVERSE, PROXY_SETTING, PROXY_EXCLUDE_LIST,
        CONNECTION_LIMIT_TIME, MNT_FOLDER_PATH,
        SYSTEM_RESTART_INTERVAL, LOG_MNT,
        PROOF_OF_PLAY_MNT, CONTENT_MNT,
        SCREEN_ROTATION, PLAY_MODE, RESET_PASSWORD,
        TIME_ZONE_VERSION, CREATOR_ID, CREATE_DATE, 
        <if test="device.has_child != null">
            HAS_CHILD,
        </if> 
        IS_CHILD, CHILD_CNT, CONN_CHILD_CNT, CABINET_GROUP_LAYOUT )
        VALUES ( #{device.device_id}, #{device.device_model_code}, #{device.device_model_name}, #{device.device_name},
        #{device.serial_decimal}, #{device.screen_size},
        #{device.firmware_version}, #{device.application_version}, #{device.rule_version}, #{device.os_image_version},
        #{device.cpu_type},
        #{device.hdd_size}, #{device.mem_size}, #{device.video_adapter}, #{device.video_memory}, #{device.video_driver},
        #{device.network_adapter},
        #{device.network_driver}, #{device.mac_address}, #{device.ip_setting_type}, #{device.ip_address},
        #{device.subnet_mask}, #{device.gateway},
        #{device.dns_server_main}, #{device.dns_server_sub}, #{device.port}, #{device.trigger_interval},
        #{device.monitoring_interval}, #{device.location},
        #{device.is_approved}, #{device.tunneling_server}, #{device.bg_color}, #{device.auto_time_setting},
        #{device.on_timer_setting}, #{device.off_timer_setting},
        #{device.time_zone_index}, #{device.day_light_saving}, #{device.ftp_connect_mode}, #{device.repository_path},
        #{device.magicinfo_server_url},
        #{device.screen_capture_interval}, #{device.ewf_state}, #{device.resolution}, #{device.device_type}, #{device.device_type_version},
        #{device.is_reverse}, #{device.proxy_setting}, #{device.proxy_exclude_list},
        #{device.connection_limit_time}, #{device.mnt_folder_path}, #{device.system_restart_interval},
        #{device.log_mnt}, #{device.proof_of_play_mnt},
        #{device.content_mnt}, #{device.screen_rotation}, #{device.play_mode}, #{device.reset_password},
        #{device.time_zone_version}, #{device.creator_id}, #{device.create_date},
        <if test="device.has_child != null">
            #{device.has_child}, 
        </if> 
        #{device.is_child}, #{device.child_cnt}, #{device.conn_child_cnt}, #{device.cabinet_group_layout} )
    </insert>

    <insert id="addDeviceList">
        INSERT INTO MI_DMS_INFO_DEVICE ( DEVICE_ID,
        DEVICE_MODEL_CODE, DEVICE_MODEL_NAME,
        DEVICE_NAME, SERIAL_DECIMAL, SCREEN_SIZE,
        FIRMWARE_VERSION, APPLICATION_VERSION,
        RULE_VERSION, OS_IMAGE_VERSION, CPU_TYPE,
        HDD_SIZE, MEM_SIZE, VIDEO_ADAPTER,
        VIDEO_MEMORY, VIDEO_DRIVER, NETWORK_ADAPTER,
        NETWORK_DRIVER, MAC_ADDRESS,
        IP_SETTING_TYPE, IP_ADDRESS, SUBNET_MASK,
        GATEWAY, DNS_SERVER_MAIN, DNS_SERVER_SUB,
        PORT, TRIGGER_INTERVAL, MONITORING_INTERVAL,
        LOCATION, IS_APPROVED, TUNNELING_SERVER,
        BG_COLOR, AUTO_TIME_SETTING,
        ON_TIMER_SETTING, OFF_TIMER_SETTING,
        TIME_ZONE_INDEX, DAY_LIGHT_SAVING,
        FTP_CONNECT_MODE, REPOSITORY_PATH,
        MAGICINFO_SERVER_URL, SCREEN_CAPTURE_INTERVAL,
        EWF_STATE, RESOLUTION, DEVICE_TYPE, DEVICE_TYPE_VERSION,
        IS_REVERSE, PROXY_SETTING, PROXY_EXCLUDE_LIST,
        CONNECTION_LIMIT_TIME, MNT_FOLDER_PATH,
        SYSTEM_RESTART_INTERVAL, LOG_MNT,
        PROOF_OF_PLAY_MNT, CONTENT_MNT,
        SCREEN_ROTATION, PLAY_MODE, RESET_PASSWORD,
        TIME_ZONE_VERSION, CREATOR_ID, CREATE_DATE, 
        HAS_CHILD, IS_CHILD, CHILD_CNT, CONN_CHILD_CNT, CABINET_GROUP_LAYOUT )
        VALUES 
        <foreach collection="deviceList" item="device"  separator=",">
        (
	        #{device.device_id}, #{device.device_model_code}, #{device.device_model_name}, #{device.device_name},
	        #{device.serial_decimal}, #{device.screen_size},
	        #{device.firmware_version}, #{device.application_version}, #{device.rule_version}, #{device.os_image_version},
	        #{device.cpu_type},
	        #{device.hdd_size}, #{device.mem_size}, #{device.video_adapter}, #{device.video_memory}, #{device.video_driver},
	        #{device.network_adapter},
	        #{device.network_driver}, #{device.mac_address}, #{device.ip_setting_type}, #{device.ip_address},
	        #{device.subnet_mask}, #{device.gateway},
	        #{device.dns_server_main}, #{device.dns_server_sub}, #{device.port}, #{device.trigger_interval},
	        #{device.monitoring_interval}, #{device.location},
	        #{device.is_approved}, #{device.tunneling_server}, #{device.bg_color}, #{device.auto_time_setting},
	        #{device.on_timer_setting}, #{device.off_timer_setting},
	        #{device.time_zone_index}, #{device.day_light_saving}, #{device.ftp_connect_mode}, #{device.repository_path},
	        #{device.magicinfo_server_url},
	        #{device.screen_capture_interval}, #{device.ewf_state}, #{device.resolution}, #{device.device_type}, #{device.device_type_version},
	        #{device.is_reverse}, #{device.proxy_setting}, #{device.proxy_exclude_list},
	        #{device.connection_limit_time}, #{device.mnt_folder_path}, #{device.system_restart_interval},
	        #{device.log_mnt}, #{device.proof_of_play_mnt},
	        #{device.content_mnt}, #{device.screen_rotation}, #{device.play_mode}, #{device.reset_password},
	        #{device.time_zone_version}, #{device.creator_id}, #{device.create_date},
            #{device.has_child},#{device.is_child}, #{device.child_cnt}, #{device.conn_child_cnt}, #{device.cabinet_group_layout}
            )
        </foreach>        
    </insert>

    <insert id="addDeviceGroupMapping">
        INSERT INTO MI_DMS_MAP_GROUP_DEVICE
        ( GROUP_ID, DEVICE_ID ) VALUES ( #{parentGroupId} , #{deviceId} )
    </insert>

    <insert id="addDeviceGroupMappingList">
        INSERT INTO MI_DMS_MAP_GROUP_DEVICE
        ( GROUP_ID, DEVICE_ID ) VALUES
        <foreach collection="deviceIdList" item="deviceId"  separator=","> 
            ( 
                #{parentGroupId} , #{deviceId} 
            )
        </foreach>
    </insert>

    <insert id="addDeviceModel">
        INSERT INTO MI_DMS_INFO_DEVICE_MODEL
        (DEVICE_MODEL_NAME, DEVICE_MODEL_CODE, DEVICE_MODEL_TYPE, VENDOR, DESCRIPTION, CREATOR_ID, CREATE_DATE)
        VALUES (#{deviceModel.device_model_name}, #{deviceModel.device_model_code}, #{deviceModel.device_model_type}, #{deviceModel.vendor},
        #{deviceModel.description}, #{deviceModel.creator_id}, CURRENT_TIMESTAMP)
    </insert>

    <insert id="addDeviceOperationInfo">
        INSERT INTO MI_DMS_INFO_DEVICE
        (DEVICE_ID, DEVICE_NAME, DEVICE_MODEL_CODE, MAC_ADDRESS, IP_ADDRESS, SUBNET_MASK,
        PORT, CPU_TYPE, HDD_SIZE, MEM_SIZE, TUNNELING_SERVER, APPLICATION_VERSION, PLAYER_VERSION, DEVICE_TYPE, DEVICE_TYPE_VERSION,
        <if test="device.has_child != null">
        HAS_CHILD,
        </if>
        DISK_SPACE_REPOSITORY, CREATE_DATE, DEVICE_MODEL_NAME, TRIGGER_INTERVAL, MONITORING_INTERVAL, BOOTSTRAP_TIME)
        VALUES (#{device.device_id}, #{device.device_name}, #{device.device_model_code}, #{device.mac_address},
        #{device.ip_address}, #{device.subnet_mask},
        #{device.port}, #{device.cpu_type}, #{device.hdd_size}, #{device.mem_size}, #{device.tunneling_server},
        #{device.application_version}, #{device.player_version}, #{device.device_type}, #{device.device_type_version},
        <if test="device.has_child != null">
        #{device.has_child},
        </if>
        #{device.disk_space_repository}, #{device.create_date}, #{device.device_model_name}, 5, 10, #{device.bootstrap_time})
    </insert>

    <update id="setUnapprovedGroupCode">
        UPDATE MI_DMS_INFO_DEVICE SET UNAPPROVED_GROUP_CODE = #{unapprovedGroupCode}
        WHERE DEVICE_ID = #{deviceId}
    </update>

	<update id="addVwtInfo">
		UPDATE MI_DMS_INFO_DEVICE SET
			POSITION_X = #{map.position_x}, 
			POSITION_Y = #{map.position_y},
			WIDTH = #{map.panel_width},
			HEIGHT = #{map.panel_height},
			ANGLE = #{map.angle}, 
			VWT_ID = #{map.vwt_id},
			BEZEL_LEFTRIGHT = #{map.bezel_leftright},
			BEZEL_TOPBOTTOM = #{map.bezel_topbottom},
			MAP_ID = #{map.map_id}
		WHERE DEVICE_ID = #{deviceId}
	</update>

	<update id="deleteVwtInfo">
		UPDATE MI_DMS_INFO_DEVICE SET
				POSITION_X = null, 
				POSITION_Y = null,
				WIDTH = null,
				HEIGHT = null,
				ANGLE = null, 
				VWT_ID = null,
				BEZEL_LEFTRIGHT = null,
				BEZEL_TOPBOTTOM = null,
				MAP_ID = null
				WHERE DEVICE_ID = #{deviceId}
	</update>
	
	<select id="getDeviceModelNameByDeviceId" resultType="map">
		SELECT DEVICE_MODEL_NAME 
		FROM MI_DMS_INFO_DEVICE 
		WHERE DEVICE_ID = #{deviceId}
	</select>
	
    <delete id="deleteDeviceGroupMappingByDeviceId">
        DELETE FROM MI_DMS_MAP_GROUP_DEVICE
        WHERE DEVICE_ID = #{deviceId}
    </delete>

    <delete id="deleteDevice">
        DELETE FROM MI_DMS_INFO_DEVICE
        WHERE DEVICE_ID = #{deviceId}
    </delete>

    <delete id="deleteDeviceClock">
        DELETE FROM MI_DMS_INFO_TIME_CLOCK
        WHERE DEVICE_ID = #{deviceId}
    </delete>

    <delete id="deleteDeviceHoliday">
        DELETE FROM MI_DMS_INFO_TIME_HOLIDAY
        WHERE DEVICE_ID = #{deviceId}
    </delete>

    <delete id="deleteDeviceTimer">
        DELETE FROM MI_DMS_INFO_TIME_TIMER
        WHERE DEVICE_ID = #{deviceId}
    </delete>

    <delete id="deleteDeviceGroupMapping">
        DELETE FROM MI_DMS_MAP_GROUP_DEVICE
        WHERE DEVICE_ID = #{deviceId} AND GROUP_ID = #{groupId}
    </delete>

    <delete id="deleteDeviceModel">
        DELETE FROM MI_DMS_INFO_DEVICE_MODEL
        WHERE DEVICE_MODEL_NAME = #{modelName}
    </delete>

    <delete id="deleteDeviceModels">
        DELETE FROM MI_DMS_INFO_DEVICE_MODEL
        WHERE DEVICE_MODEL_NAME IN
        <foreach item="deviceModelName" collection="deviceModelNameList" open="(" separator="," close=")">
            #{deviceModelName}
        </foreach>
    </delete>

    <select id="deleteDeviceModelSoftware" resultType="map">
        SELECT SOFTWARE_ID FROM MI_DMS_MAP_DEVICE_MODEL_SOFTWARE WHERE DEVICE_MODEL_NAME = #{modelName}
    </select>

    <delete id="deleteGroupDevices">
        DELETE FROM MI_DMS_MAP_GROUP_DEVICE
        WHERE DEVICE_ID IN
        <foreach item="deviceId" collection="deviceIdList" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </delete>

    <delete id="deleteDevices">
        DELETE FROM MI_DMS_INFO_DEVICE
        WHERE DEVICE_ID IN
        <foreach item="deviceId" collection="deviceIdList" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </delete>

    <select id="deleteModelAndRule" resultType="map">
        SELECT RULE_ID FROM MI_DMS_MAP_DEVICE_MODEL_RULE WHERE DEVICE_MODEL_NAME = #{modelName}
    </select>

    <select id="getApprovedDeviceListCnt" resultType="int">
        SELECT COUNT(DISTINCT A.DEVICE_ID)
        <include refid="getApprovedDeviceListCommon"/>
    </select>

    <select id="getApprovedDeviceList" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf">
        SELECT A.DEVICE_ID, A.DEVICE_NAME, B.GROUP_NAME, B.GROUP_ID, DEVICE_MODEL_CODE,DEVICE_MODEL_NAME, A.ERROR_FLAG,
        SERIAL_DECIMAL, SCREEN_SIZE, RESOLUTION, FIRMWARE_VERSION, OS_IMAGE_VERSION,
        EWF_STATE, APPLICATION_VERSION, RULE_VERSION, IP_ADDRESS, MAC_ADDRESS, DEVICE_TYPE, DEVICE_TYPE_VERSION, 
        LOCATION, TUNNELING_SERVER, A.CREATE_DATE, A.CREATOR_ID, A.IS_APPROVED, A.WEBCAM, A.VWT_ID, A.HAS_CHILD, A.CHILD_CNT, A.IS_CHILD, A.CONN_CHILD_CNT, A.MAP_LOCATION, A.LAST_CONNECTION_TIME
		<include refid="iconsSelect"/>
        <include refid="getApprovedDeviceListCommon"/>
              GROUP BY A.DEVICE_ID, A.DEVICE_NAME, B.GROUP_NAME, B.GROUP_ID, DEVICE_MODEL_CODE,DEVICE_MODEL_NAME, A.ERROR_FLAG, 
        SERIAL_DECIMAL, SCREEN_SIZE, RESOLUTION, FIRMWARE_VERSION, OS_IMAGE_VERSION,
        EWF_STATE, APPLICATION_VERSION, RULE_VERSION, IP_ADDRESS, MAC_ADDRESS, DEVICE_TYPE, DEVICE_TYPE_VERSION, 
        LOCATION, TUNNELING_SERVER, A.CREATE_DATE, A.CREATOR_ID, A.IS_APPROVED, A.WEBCAM, A.VWT_ID, A.HAS_CHILD, A.CHILD_CNT, A.IS_CHILD, A.CONN_CHILD_CNT, A.MAP_LOCATION, A.LAST_CONNECTION_TIME

        <if test="condition.sort_name != null and condition.sort_name != '' and condition.order_dir != null and condition.order_dir != ''">
            <bind name="safe_sortUpper" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
            <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
            ORDER BY 
            <choose>
                    <when test="safe_sortUpper == 'GROUP_NAME'">
                        B.${safe_sortUpper}
                    </when>                 
               		<when test="safe_sortUpper != 'DEVICE_ID' and safe_sortUpper != 'DEVICE_NAME'">
            			${safe_sortUpper}
            		</when>
               		<otherwise>
               			A.${safe_sortUpper}
               		</otherwise> 
            </choose>	
               			${safe_sortOrder}
        </if>
        LIMIT #{pageSize} OFFSET #{startPos}
    </select>
    
    <select id="getApprovedDeviceList" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf" databaseId="mssql">
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
        SELECT * FROM (
          SELECT A.DEVICE_ID, A.DEVICE_NAME, B.GROUP_NAME, B.GROUP_ID, DEVICE_MODEL_CODE,DEVICE_MODEL_NAME, A.ERROR_FLAG,
          SERIAL_DECIMAL, SCREEN_SIZE, RESOLUTION, FIRMWARE_VERSION, OS_IMAGE_VERSION,
          EWF_STATE, APPLICATION_VERSION, RULE_VERSION, IP_ADDRESS, MAC_ADDRESS, DEVICE_TYPE, DEVICE_TYPE_VERSION,
          LOCATION, TUNNELING_SERVER, A.CREATE_DATE, A.CREATOR_ID, A.IS_APPROVED, A.WEBCAM, A.VWT_ID, A.HAS_CHILD, A.CHILD_CNT, A.IS_CHILD, A.CONN_CHILD_CNT, A.MAP_LOCATION, A.LAST_CONNECTION_TIME,
                 ROW_NUMBER() OVER(ORDER BY
           <choose>
               <when test="condition.sort_name != null and condition.sort_name != '' and condition.order_dir != null and condition.order_dir != ''">
                   <bind name="safe_sortUpper" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
                   <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
               		<choose>
	                    <when test="safe_sortUpper == 'GROUP_NAME'">
	                        B.${safe_sortUpper}
	                    </when>                 		
               			<when test="safe_sortUpper != 'DEVICE_ID' and safe_sortUpper != 'DEVICE_NAME' and safe_sortUpper != 'CREATE_DATE' ">
               				${safe_sortUpper}
               			</when>
                   		<otherwise>
                   			A.${safe_sortUpper}
                   		</otherwise> 
                   	</choose>	
               			${safe_sortOrder}
               </when>
               <otherwise>
                   A.DEVICE_ID ASC
               </otherwise>
           </choose>
           ) as RowNum
		 <include refid="iconsSelect"/>
         <include refid="getApprovedDeviceListCommon"/>
                 GROUP BY 
        A.DEVICE_ID, A.DEVICE_NAME, B.GROUP_NAME, B.GROUP_ID, DEVICE_MODEL_CODE,DEVICE_MODEL_NAME, A.ERROR_FLAG, 
          SERIAL_DECIMAL, SCREEN_SIZE, RESOLUTION, FIRMWARE_VERSION, OS_IMAGE_VERSION,
          EWF_STATE, APPLICATION_VERSION, RULE_VERSION, IP_ADDRESS, MAC_ADDRESS, DEVICE_TYPE, DEVICE_TYPE_VERSION,
          LOCATION, TUNNELING_SERVER, A.CREATE_DATE, A.CREATOR_ID, A.IS_APPROVED, A.WEBCAM, A.VWT_ID, A.HAS_CHILD, A.CHILD_CNT, A.IS_CHILD, A.CONN_CHILD_CNT, A.MAP_LOCATION, A.LAST_CONNECTION_TIME
        ) as SubQuery
        WHERE RowNum > ${safe_startPos} and RowNum &lt;= ${safe_rownumLimit}
        ORDER BY RowNum
    </select>
    
    <select id="getApprovedDeviceListWithFilter" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf">
        SELECT A.DEVICE_ID, A.DEVICE_NAME, B.GROUP_NAME, B.GROUP_ID, DEVICE_MODEL_CODE,DEVICE_MODEL_NAME, A.ERROR_FLAG, A.PLAYER_VERSION,
        SERIAL_DECIMAL, SCREEN_SIZE, RESOLUTION, FIRMWARE_VERSION, OS_IMAGE_VERSION, A.DISK_SPACE_REPOSITORY, A.DISK_SPACE_USAGE,
        EWF_STATE, APPLICATION_VERSION, FIRMWARE_INDICATORS, RULE_VERSION, IP_ADDRESS, MAC_ADDRESS, DEVICE_TYPE, DEVICE_TYPE_VERSION,
        LOCATION, TUNNELING_SERVER, A.CREATE_DATE, A.CREATOR_ID, A.IS_APPROVED, A.WEBCAM, A.VWT_ID, A.HAS_CHILD, A.CHILD_CNT, A.IS_CHILD, A.CONN_CHILD_CNT, A.MAP_LOCATION, A.LAST_CONNECTION_TIME, A.SCREEN_ROTATION
        <include refid="iconsSelect"/>
        <include refid="getApprovedDeviceListCommon"/>
              GROUP BY A.DEVICE_ID, A.DEVICE_NAME, B.GROUP_NAME, B.GROUP_ID, DEVICE_MODEL_CODE,DEVICE_MODEL_NAME, A.ERROR_FLAG , A.PLAYER_VERSION, 
        SERIAL_DECIMAL, SCREEN_SIZE, RESOLUTION, FIRMWARE_VERSION, OS_IMAGE_VERSION, A.DISK_SPACE_REPOSITORY, A.DISK_SPACE_USAGE,
        EWF_STATE, APPLICATION_VERSION, FIRMWARE_INDICATORS, RULE_VERSION, IP_ADDRESS, MAC_ADDRESS, DEVICE_TYPE, DEVICE_TYPE_VERSION,
        LOCATION, TUNNELING_SERVER, A.CREATE_DATE, A.CREATOR_ID, A.IS_APPROVED, A.WEBCAM, A.VWT_ID, A.HAS_CHILD, A.CHILD_CNT, A.IS_CHILD, A.CONN_CHILD_CNT, A.MAP_LOCATION, A.LAST_CONNECTION_TIME, A.SCREEN_ROTATION

        <if test="condition.sort_name != null and condition.sort_name != '' and condition.order_dir != null and condition.order_dir != ''">
            <bind name="safe_sortUpper" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
            <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
            ORDER BY 
            <choose>
                    <when test="safe_sortUpper == 'GROUP_NAME'">
                        B.${safe_sortUpper}
                    </when>                 
                    <when test="safe_sortUpper != 'DEVICE_ID' and safe_sortUpper != 'DEVICE_NAME'">
                        ${safe_sortUpper}
                    </when>
                    <otherwise>
                        A.${safe_sortUpper}
                    </otherwise> 
            </choose>   
                        ${safe_sortOrder}
        </if>
        LIMIT #{pageSize} OFFSET #{startPos}
    </select>
    
    <select id="getApprovedDeviceListWithFilter" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf" databaseId="mssql">
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
        SELECT * FROM (
          SELECT A.DEVICE_ID, A.DEVICE_NAME, B.GROUP_NAME, B.GROUP_ID, DEVICE_MODEL_CODE,DEVICE_MODEL_NAME, A.ERROR_FLAG, A.PLAYER_VERSION,
          SERIAL_DECIMAL, SCREEN_SIZE, RESOLUTION, FIRMWARE_VERSION, OS_IMAGE_VERSION, A.DISK_SPACE_REPOSITORY, A.DISK_SPACE_USAGE,
          EWF_STATE, APPLICATION_VERSION, FIRMWARE_INDICATORS, RULE_VERSION, IP_ADDRESS, MAC_ADDRESS, DEVICE_TYPE, DEVICE_TYPE_VERSION,
          LOCATION, TUNNELING_SERVER, A.CREATE_DATE, A.CREATOR_ID, A.IS_APPROVED, A.WEBCAM, A.VWT_ID, A.HAS_CHILD, A.CHILD_CNT, A.IS_CHILD, A.CONN_CHILD_CNT, A.MAP_LOCATION, A.LAST_CONNECTION_TIME, A.SCREEN_ROTATION,
                 ROW_NUMBER() OVER(ORDER BY
           <choose>
               <when test="condition.sort_name != null and condition.sort_name != '' and condition.order_dir != null and condition.order_dir != ''">
                   <bind name="safe_sortUpper" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
                   <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
                    <choose>
                        <when test="safe_sortUpper == 'GROUP_NAME'">
                            B.${safe_sortUpper}
                        </when>                         
                        <when test="safe_sortUpper != 'DEVICE_ID' and safe_sortUpper != 'DEVICE_NAME' and safe_sortUpper != 'CREATE_DATE' ">
                            ${safe_sortUpper}
                        </when>
                        <otherwise>
                            A.${safe_sortUpper}
                        </otherwise> 
                    </choose>   
                        ${safe_sortOrder}
               </when>
               <otherwise>
                   A.DEVICE_ID ASC
               </otherwise>
           </choose>
           ) as RowNum
         <include refid="iconsSelect"/>
         <include refid="getApprovedDeviceListCommon"/>
                 GROUP BY 
        A.DEVICE_ID, A.DEVICE_NAME, B.GROUP_NAME, B.GROUP_ID, DEVICE_MODEL_CODE,DEVICE_MODEL_NAME, A.ERROR_FLAG, A.PLAYER_VERSION, 
          SERIAL_DECIMAL, SCREEN_SIZE, RESOLUTION, FIRMWARE_VERSION, OS_IMAGE_VERSION, A.DISK_SPACE_REPOSITORY, A.DISK_SPACE_USAGE,
          EWF_STATE, APPLICATION_VERSION, FIRMWARE_INDICATORS, RULE_VERSION, IP_ADDRESS, MAC_ADDRESS, DEVICE_TYPE, DEVICE_TYPE_VERSION,
          LOCATION, TUNNELING_SERVER, A.CREATE_DATE, A.CREATOR_ID, A.IS_APPROVED, A.WEBCAM, A.VWT_ID, A.HAS_CHILD, A.CHILD_CNT, A.IS_CHILD, A.CONN_CHILD_CNT, A.MAP_LOCATION, A.LAST_CONNECTION_TIME, A.SCREEN_ROTATION
        ) as SubQuery
        WHERE RowNum > ${safe_startPos} and RowNum &lt;= ${safe_rownumLimit}
        ORDER BY RowNum
    </select>

    <sql id="getApprovedDeviceListCommon">
        FROM MI_DMS_INFO_DEVICE A
        <include refid="tagFrom"/>
        <include refid="iconsFrom"><property name="alias" value="A"/></include>
        <if test="condition.sourceFilterList != null" >
        	LEFT JOIN MI_DMS_INFO_DISPLAY DP ON A.DEVICE_ID = DP.DEVICE_ID
        </if>        
        , MI_DMS_INFO_GROUP B, MI_DMS_MAP_GROUP_DEVICE C <include refid="deviceGroupAuthFrom"/>
        WHERE A.DEVICE_ID = C.DEVICE_ID AND C.GROUP_ID = B.GROUP_ID AND A.IS_APPROVED = <include refid="utils.true"/> <include refid="deviceGroupAuthWhere"/>
        <if test="condition.src_name != null and condition.src_name != ''">
            <bind name="srcUpper" value="_parameter.condition.src_name.toUpperCase()"/>
            <bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
            AND ((UPPER(A.DEVICE_NAME) 
            LIKE #{srcNamePattern} 
            ESCAPE '^') OR (UPPER(A.MAC_ADDRESS) 
            LIKE #{srcNamePattern} 
            ESCAPE '^') OR (UPPER(A.DEVICE_MODEL_NAME) 
            LIKE #{srcNamePattern} 
            ESCAPE '^') OR (UPPER(A.IP_ADDRESS) 
            LIKE #{srcNamePattern} 
            ESCAPE '^') OR (UPPER(E.TAG_VALUE)
            LIKE #{srcNamePattern}
            ESCAPE '^') OR (UPPER(LOCATION)
            LIKE #{srcNamePattern}
            ESCAPE '^') OR (UPPER(FIRMWARE_VERSION)
            LIKE #{srcNamePattern}
            ESCAPE '^') OR (UPPER(SERIAL_DECIMAL)
            LIKE #{srcNamePattern}
            ESCAPE '^') OR (UPPER(APPLICATION_VERSION)
            LIKE #{srcNamePattern}
            ESCAPE '^') OR (UPPER(E.TAG_VALUE)
            LIKE #{srcNamePattern}
            ESCAPE '^'))
        </if>
        <if test="condition.firmware_indicators != null and condition.firmware_indicators != ''">
            AND A.FIRMWARE_INDICATORS = #{condition.firmware_indicators}
        </if>
        <if test="condition.device_id != null and condition.device_id != ''">
            <bind name="deviceIdPattern" value="'%' + condition.device_id + '%'"/>
            AND A.DEVICE_ID LIKE #{deviceIdPattern}
        </if>
        <if test="condition.status_view_mode != null and !condition.status_view_mode.equals(constants.DEVICE_STATUS_VIEW_ALL)">
            <if test="condition.status_view_mode == constants.DEVICE_STATUS_VIEW_CONNECTION">
                 <include refid="connectionMode" />
            </if>

            <if test="condition.status_view_mode == constants.DEVICE_STATUS_VIEW_DISCONNECTION">
                 <include refid="disconnectionMode" />
            </if>
        </if>
        <include refid="deviceExpirationDate"/>
        <choose>
            <when test="deviceGroupListStr != null">
                AND C.GROUP_ID IN (${deviceGroupListStr})
            </when>
            <otherwise>
                <choose>
                    <when test="condition.group_id != null and condition.group_id != 0">
                        AND C.GROUP_ID = #{condition.group_id}
                    </when>
                </choose>
            </otherwise>
        </choose>
        <if test="condition.device_model_name != null and condition.device_model_name != ''">
            <bind name="deviceModelNamePattern" value="'%' + condition.device_model_name + '%'"/>
            AND A.DEVICE_MODEL_NAME LIKE #{deviceModelNamePattern}
        </if>
        
        <include refid="deviceTypeArrQuery" />
       
        AND IS_CHILD = <include refid="utils.false"/>
        <if test="search != null">

            <if test="search.device_id != null and search.device_id != ''">
                AND A.DEVICE_ID = #{search.device_id}
            </if>

            <if test="search.device_name != null and search.device_name != ''">
                AND DEVICE_NAME = #{search.device_name}
            </if>

            <if test="search.group_id != null and search.group_id != ''">
                AND C.GROUP_ID = #{search.group_id}
            </if>

            <if test="search.group_name != null and search.group_name != ''">
                AND B.GROUP_NAME = #{search.group_name}
            </if>

            <if test="search.ip_address != null and search.ip_address != ''">
                AND IP_ADDRESS = #{search.ip_address}
            </if>

            <if test="search.device_model_name != null and search.device_model_name != ''">
                AND DEVICE_MODEL_NAME = #{search.device_model_name}
            </if>

            <if test="search.firmware_version != null and search.firmware_version != ''">
                AND FIRMWARE_VERSION = #{search.firmware_version}
            </if>

            <if test="search.os_image_version != null and search.os_image_version != ''">
                AND OS_IMAGE_VERSION = #{search.os_image_version}
            </if>

            <if test="search.application_version != null and search.application_version != ''">
                AND APPLICATION_VERSION = #{search.application_version}
            </if>

            <if test="search.approval_start_date != null or search.approval_end_date != null">
                <if test="search.approval_start_date != null and search.approval_start_date != ''">
                    AND A.CREATE_DATE &gt;= #{search.approval_start_date}
                </if>
                <if test="search.approval_end_date != null and search.approval_end_date != ''">
                    AND A.CREATE_DATE &lt;= #{search.approval_end_date}
                </if>
            </if>
        </if>
   		<if test="tagFilter != null">
           <foreach collection="tagFilter" open="AND E.TAG_ID IN (" separator="," close=")" item="item">
               #{item}
           </foreach>
       </if>
       
       <if test="condition.sourceFilterList != null" >
       		AND DP.BASIC_SOURCE IN
       		   <foreach collection="condition.sourceFilterList" item="source" open="(" separator="," close=")" >
                   #{source}
               </foreach>
        </if>       
       
       <include refid="hasAlarmFilterQueries" />
       
       <include refid="hasFunctionFilterQueries" />

		<include refid="commonSearchKeywowrdQuery" />
       
    </sql>
	
	<sql id="connectionMode">
		<![CDATA[AND EXTRACT('epoch' from CURRENT_TIMESTAMP - A.LAST_CONNECTION_TIME)::int < (A.MONITORING_INTERVAL + 1) * 60 ]]>
	</sql>

	<sql id="connectionMode" databaseId="mssql">
		AND DATEDIFF(S, A.LAST_CONNECTION_TIME, GETDATE()) &lt; (A.MONITORING_INTERVAL + 1) * 60
	</sql>
	
	<sql id="connectionMode" databaseId="mysql">
		AND TIMESTAMPDIFF(SECOND, A.LAST_CONNECTION_TIME, NOW()) &lt; (A.MONITORING_INTERVAL + 1) * 60
	</sql>

    <sql id="disconnectionMode">
        <choose>
            <when test="condition != null">
                <if test= "condition.disconn_period == null">
                    <![CDATA[AND (EXTRACT('epoch' from CURRENT_TIMESTAMP - A.LAST_CONNECTION_TIME)::int > (A.MONITORING_INTERVAL + 1) * 60
                    OR A.LAST_CONNECTION_TIME IS NULL OR A.MONITORING_INTERVAL IS NULL)  ]]>
                </if>
                <if test= "condition.disconn_period != null and condition.disconn_period != ''">
                    <choose>
                        <when test=" condition.disconn_period == 30">
                            AND (CURRENT_TIMESTAMP - A.LAST_CONNECTION_TIME) &gt; INTERVAL '30 days'
                        </when>
                        <when test="condition.disconn_period == 60">
                            AND (CURRENT_TIMESTAMP - A.LAST_CONNECTION_TIME) &gt; INTERVAL '60 days'
                        </when>
                        <when test="condition.disconn_period == 90">
                            AND (CURRENT_TIMESTAMP - A.LAST_CONNECTION_TIME) &gt; INTERVAL '90 days'
                        </when>
                    </choose>
                </if>
            </when>
            <otherwise>
                <![CDATA[AND (EXTRACT('epoch' from CURRENT_TIMESTAMP - A.LAST_CONNECTION_TIME)::int > (A.MONITORING_INTERVAL + 1) * 60
                    OR A.LAST_CONNECTION_TIME IS NULL OR A.MONITORING_INTERVAL IS NULL)  ]]>
            </otherwise>
        </choose>
    </sql>

    <sql id="disconnectionMode" databaseId="mssql">
        <choose>
            <when test="condition != null">
                <if test= "condition.disconn_period == null">
                    AND ( DATEDIFF(S, A.LAST_CONNECTION_TIME, GETDATE()) &gt; (A.MONITORING_INTERVAL + 1) * 60 OR A.LAST_CONNECTION_TIME IS NULL OR A.MONITORING_INTERVAL IS NULL)
                </if>
                <if test= "condition.disconn_period != null and condition.disconn_period != ''">
                    <choose>
                        <when test="condition.disconn_period == 90">
                            AND DATEDIFF(minute, A.LAST_CONNECTION_TIME, GETDATE()) &gt; 90*1440
                        </when>
                        <when test="condition.disconn_period == 60">
                            AND DATEDIFF(minute, A.LAST_CONNECTION_TIME, GETDATE()) &gt; 60*1440
                        </when>
                        <when test=" condition.disconn_period == 30">
                            AND DATEDIFF(minute, A.LAST_CONNECTION_TIME, GETDATE()) &gt; 30*1440
                        </when>
                    </choose>
                </if>
            </when>
            <otherwise>
                AND ( DATEDIFF(S, A.LAST_CONNECTION_TIME, GETDATE()) &gt; (A.MONITORING_INTERVAL + 1) * 60 OR A.LAST_CONNECTION_TIME IS NULL OR A.MONITORING_INTERVAL IS NULL)
            </otherwise>
        </choose>

    </sql>
	  
	<sql id="disconnectionMode" databaseId="mysql">
		AND (TIMESTAMPDIFF(SECOND, A.LAST_CONNECTION_TIME, NOW()) &gt; (A.MONITORING_INTERVAL + 1) * 60 OR LAST_CONNECTION_TIME IS NULL OR A.MONITORING_INTERVAL IS NULL)
	</sql>
	
    <select id="getCntApprovedDeviceList" resultType="int">
        SELECT COUNT(DISTINCT A.DEVICE_ID)
        FROM MI_DMS_INFO_DEVICE A
        <include refid="tagFrom"/>
        <include refid="iconsFrom"><property name="alias" value="A"/></include>
        , MI_DMS_INFO_GROUP B, MI_DMS_MAP_GROUP_DEVICE C
        WHERE A.DEVICE_ID = C.DEVICE_ID AND C.GROUP_ID = B.GROUP_ID AND A.IS_APPROVED = <include refid="utils.true"/>
        <if test="search != null">
            <if test="search.device_id != null and !search.device_id.equals('')">
                AND A.DEVICE_ID = #{search.device_id}
            </if>
            <if test="search.device_name != null and !search.device_name.equals('')">
                AND A.DEVICE_NAME = #{search.device_name}
            </if>
        </if>
        <choose>
            <when test="search != null and search.group_id != null and !search.group_id.equals('')">
                AND C.GROUP_ID = #{search.group_id}
            </when>
            <otherwise>
                <choose>
                    <when test="isRoot">
                        <if test="deviceGroupList != null and deviceGroupList.size > 0">
                            AND C.GROUP_ID IN
                            <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
                                #{deviceGroupId.group_id}
                            </foreach>
                        </if>
                    </when>
                    <otherwise>
                        <if test="groupId != null">
                            AND C.GROUP_ID = #{groupId}
                        </if>
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
        <if test="search != null">
            <if test="search.group_name != null and !search.group_name.equals('')">
                AND B.GROUP_NAME = #{search.group_name}
            </if>
            <if test="search.ip_address != null and !search.ip_address.equals('')">
                AND IP_ADDRESS = #{search.ip_address}
            </if>
            <if test="search.device_model_name != null and !search.device_model_name.equals('')">
                AND DEVICE_MODEL_NAME = #{search.device_model_name}
            </if>
            <if test="search.firmware_version != null and !search.firmware_version.equals('')">
                AND FIRMWARE_VERSION = #{search.firmware_version}
            </if>
            <if test="search.os_image_version != null and !search.os_image_version.equals('')">
                AND OS_IMAGE_VERSION = #{search.os_image_version}
            </if>
            <if test="search.application_version != null and !search.application_version.equals('')">
                AND APPLICATION_VERSION = #{search.application_version}
            </if>
            <if test="search.approval_start_date != null and !search.approval_start_date.equals('')">
                AND A.CREATE_DATE &gt;= #{search.approval_start_date}
            </if>
            <if test="search.approval_end_date != null and !search.approval_end_date.equals('')">
                AND A.CREATE_DATE &lt;= #{search.approval_end_date}
            </if>
        </if>
    </select>

    <select id="getApprovedDeviceFilterList"
            resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf">
        SELECT A.DEVICE_ID, A.DEVICE_NAME, B.GROUP_NAME, DEVICE_MODEL_CODE,DEVICE_MODEL_NAME,
        SERIAL_DECIMAL, SCREEN_SIZE, RESOLUTION, FIRMWARE_VERSION, OS_IMAGE_VERSION,
        EWF_STATE, APPLICATION_VERSION, RULE_VERSION, IP_ADDRESS, MAC_ADDRESS, DEVICE_TYPE, DEVICE_TYPE_VERSION,
        LOCATION, TUNNELING_SERVER, A.CREATE_DATE, A.CREATOR_ID, A.IS_APPROVED, A.WEBCAM
        <include refid="iconsSelect"/>
        FROM MI_DMS_INFO_DEVICE A
        <include refid="tagFrom"/>
        <include refid="iconsFrom"><property name="alias" value="A"/></include>
        , MI_DMS_INFO_GROUP B, MI_DMS_MAP_GROUP_DEVICE C
        WHERE A.DEVICE_ID = C.DEVICE_ID AND C.GROUP_ID = B.GROUP_ID AND A.IS_APPROVED = <include refid="utils.true"/>

        <if test="condition.src_name != null and !condition.src_name.equals('')">
            <bind name="deviceNamePattern" value="'%' + condition.src_name + '%'"/>
            AND ((UPPER(A.DEVICE_NAME) LIKE #{deviceNamePattern} 
            ESCAPE '^') OR (UPPER(A.MAC_ADDRESS) LIKE #{deviceNamePattern} 
            ESCAPE '^') OR (UPPER(A.DEVICE_MODEL_NAME) LIKE #{deviceNamePattern} 
            ESCAPE '^') OR (UPPER(A.IP_ADDRESS) LIKE #{deviceNamePattern}
            ESCAPE '^') OR (UPPER(E.TAG_VALUE) LIKE #{deviceNamePattern}  
            ESCAPE '^'))
        </if>
        <if test="condition.device_id != null and !condition.device_id.equals('')">
            <bind name="deviceIdPattern" value="'%' + condition.device_id + '%'"/>
            AND A.DEVICE_ID LIKE #{deviceIdPattern}
        </if>

        <choose>
            <when test="condition.isRoot">
                <if test="deviceGroupList != null and deviceGroupList.size > 0">
                    AND C.GROUP_ID IN
                    <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
                        #{deviceGroupId.group_id}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <choose>
                    <when test="condition.group_id != null and condition.group_id != 0">
                        AND C.GROUP_ID = #{condition.group_id}
                    </when>
                    <otherwise>
                        AND C.GROUP_ID != #{non_approval_group}
                    </otherwise>
                </choose>
            </otherwise>
        </choose>

        <if test="condition.device_model_name != null and !condition.device_model_name.equals('')">
            <bind name="deviceModelNamePattern" value="'%' + condition.device_model_name + '%'"/>
            AND A.DEVICE_MODEL_NAME LIKE #{deviceModelNamePattern}
        </if>

		GROUP BY A.DEVICE_ID, A.DEVICE_NAME, B.GROUP_NAME, DEVICE_MODEL_CODE,DEVICE_MODEL_NAME, 
        SERIAL_DECIMAL, SCREEN_SIZE, RESOLUTION, FIRMWARE_VERSION, OS_IMAGE_VERSION,
        EWF_STATE, APPLICATION_VERSION, RULE_VERSION, IP_ADDRESS, MAC_ADDRESS, DEVICE_TYPE, DEVICE_TYPE_VERSION,
        LOCATION, TUNNELING_SERVER, A.CREATE_DATE, A.CREATOR_ID, A.IS_APPROVED
        <if test="condition.sort_name != null and !condition.sort_name.equals('') and condition.order_dir != null and !condition.order_dir.equals('')">
                <bind name="safe_sort" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(condition.sort_name)" />
        	<bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(condition.order_dir)" />
            ORDER BY ${safe_sort} ${safe_sortOrder}
        </if>
    </select>

    <select id="getCntEqualDevName" resultType="int">
        SELECT COUNT(DEVICE_NAME)
        FROM MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B
        WHERE DEVICE_NAME = #{deviceName} AND IS_APPROVED = <include refid="utils.true"/> AND A.DEVICE_ID = B.DEVICE_ID
        <if test="groupIdList != null and groupIdList.size > 0">
            AND B.GROUP_ID IN
            <foreach item="deviceGroupId" collection="groupIdList" open="(" separator="," close=")">
                #{deviceGroupId}
            </foreach>
        </if>
    </select>

    <select id="getCntModelByModelCode" resultType="int">
        SELECT COUNT(DEVICE_MODEL_CODE)
        FROM MI_DMS_INFO_DEVICE_MODEL
        WHERE DEVICE_MODEL_CODE = #{deviceModelCode}
    </select>
    
    <select id="getDeviceWithGroupId" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device">
        SELECT DEVICES.*, GROUPS.GROUP_ID FROM MI_DMS_INFO_DEVICE DEVICES
        LEFT JOIN MI_DMS_MAP_GROUP_DEVICE GROUPS ON DEVICES.DEVICE_ID = GROUPS.DEVICE_ID
        WHERE DEVICES.DEVICE_ID = #{deviceId}
    </select>

    <select id="getDevice" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device">
        SELECT * FROM MI_DMS_INFO_DEVICE
        WHERE DEVICE_ID = #{deviceId}
    </select>

    <select id="getDeviceAndModel" resultType="map">
        SELECT *
        FROM MI_DMS_INFO_DEVICE A, MI_DMS_INFO_DEVICE_MODEL B
        WHERE A.DEVICE_MODEL_NAME = B.DEVICE_MODEL_NAME
        AND A.DEVICE_ID = #{deviceId}
    </select>

    <select id="getDeviceGeneralConf"
            resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf">
        SELECT A.*
               <include refid="iconsSelect"/>
        FROM 
        (
	        SELECT A.DEVICE_ID, DEVICE_NAME, DEVICE_TYPE, B.GROUP_ID, GROUP_NAME, DEVICE_MODEL_CODE, DEVICE_MODEL_NAME,
	        	SERIAL_DECIMAL, SCREEN_SIZE, RESOLUTION, FIRMWARE_VERSION, PLAYER_VERSION,
				OS_IMAGE_VERSION, EWF_STATE, APPLICATION_VERSION, RULE_VERSION, IP_ADDRESS, DEVICE_TYPE_VERSION,  
				MAC_ADDRESS, A.CREATE_DATE, LOCATION, DISK_SPACE_USAGE, DISK_SPACE_AVAILABLE, DISK_SPACE_REPOSITORY,
				TUNNELING_SERVER, LATITUDE, LONGITUDE, ALTITUDE,
				NETWORK_ADAPTER, NETWORK_DRIVER, IP_SETTING_TYPE, SUBNET_MASK, GATEWAY, DNS_SERVER_MAIN, DNS_SERVER_SUB, PORT,  
				CPU_TYPE, HDD_SIZE, MEM_SIZE, VIDEO_ADAPTER, VIDEO_MEMORY, VIDEO_DRIVER,  
				A.MAP_ID, A.POSITION_X, A.POSITION_Y, A.WIDTH, A.HEIGHT, A.ANGLE, A.BEZEL_LEFTRIGHT, A.BEZEL_TOPBOTTOM, A.VWT_ID, A.HAS_CHILD,
				A.CHILD_CNT, A.IS_CHILD, A.CONN_CHILD_CNT, A.SUPPORT_FLAG, A.WEBCAM, A.MAP_LOCATION, A.RECOMMEND_PLAY, A.ERROR_FLAG,
	            A.THIRD_APPLICATION_VERSION, A.THIRD_APPLICATION_LAST_UPDATED, A.THIRD_APPLICATION_LOG_SIZE, A.SCREEN_ROTATION
			FROM MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B ,MI_DMS_INFO_GROUP C 
			WHERE A.DEVICE_ID = #{deviceId} AND A.DEVICE_ID = B.DEVICE_ID AND B.GROUP_ID = C.GROUP_ID
		) A
		<include refid="iconsFrom"><property name="alias" value="A"/></include>
		GROUP BY
			A.DEVICE_ID, A.DEVICE_NAME, A.DEVICE_TYPE, A.GROUP_ID, A.GROUP_NAME, A.DEVICE_MODEL_CODE, A.DEVICE_MODEL_NAME,
	        A.SERIAL_DECIMAL, A.SCREEN_SIZE, A.RESOLUTION, A.FIRMWARE_VERSION, A.PLAYER_VERSION,
	        A.OS_IMAGE_VERSION, A.EWF_STATE, A.APPLICATION_VERSION, A.RULE_VERSION, A.IP_ADDRESS, A.DEVICE_TYPE_VERSION,  
	        A.MAC_ADDRESS, A.CREATE_DATE, A.LOCATION, A.DISK_SPACE_USAGE, A.DISK_SPACE_AVAILABLE, A.DISK_SPACE_REPOSITORY,
	        A.TUNNELING_SERVER, A.LATITUDE, A.LONGITUDE, A.ALTITUDE,
	        NETWORK_ADAPTER, NETWORK_DRIVER, IP_SETTING_TYPE, SUBNET_MASK, GATEWAY, DNS_SERVER_MAIN, DNS_SERVER_SUB, PORT,  
	        A.CPU_TYPE, A.HDD_SIZE, A.MEM_SIZE, A.VIDEO_ADAPTER, A.VIDEO_MEMORY, A.VIDEO_DRIVER,  
	        A.MAP_ID, A.POSITION_X, A.POSITION_Y, A.WIDTH, A.HEIGHT, A.ANGLE, A.BEZEL_LEFTRIGHT, A.BEZEL_TOPBOTTOM, A.VWT_ID, A.HAS_CHILD,
	        A.CHILD_CNT, A.IS_CHILD, A.CONN_CHILD_CNT, A.SUPPORT_FLAG, A.WEBCAM, A.MAP_LOCATION, A.RECOMMEND_PLAY, A.ERROR_FLAG,
            A.THIRD_APPLICATION_VERSION, A.THIRD_APPLICATION_LAST_UPDATED, A.THIRD_APPLICATION_LOG_SIZE, A.SCREEN_ROTATION
    </select>
    
    <select id="getDeviceTypeInfo"
          resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf"> 
      SELECT DEVICE_TYPE, DEVICE_TYPE_VERSION
      FROM MI_DMS_INFO_DEVICE
      WHERE DEVICE_ID = #{deviceId} 
    </select>
    
	<select id="getListDeviceGeneralConf"
            resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf">
        SELECT A.DEVICE_ID, DEVICE_NAME, DEVICE_TYPE, B.GROUP_ID, GROUP_NAME, DEVICE_MODEL_CODE, DEVICE_MODEL_NAME,
        	SERIAL_DECIMAL, SCREEN_SIZE, RESOLUTION, FIRMWARE_VERSION,
			OS_IMAGE_VERSION, EWF_STATE, APPLICATION_VERSION, RULE_VERSION, IP_ADDRESS, DEVICE_TYPE_VERSION,  
			MAC_ADDRESS, A.CREATE_DATE, LOCATION, DISK_SPACE_USAGE, DISK_SPACE_AVAILABLE, TUNNELING_SERVER, LATITUDE, LONGITUDE, ALTITUDE,  
			NETWORK_ADAPTER, NETWORK_DRIVER, IP_SETTING_TYPE, SUBNET_MASK, GATEWAY, DNS_SERVER_MAIN, DNS_SERVER_SUB, PORT,  
			CPU_TYPE, HDD_SIZE, MEM_SIZE, VIDEO_ADAPTER, VIDEO_MEMORY, VIDEO_DRIVER,  
			A.MAP_ID, A.POSITION_X, A.POSITION_Y, A.WIDTH, A.HEIGHT, A.ANGLE, A.BEZEL_LEFTRIGHT, A.BEZEL_TOPBOTTOM, A.VWT_ID, A.WEBCAM  
		FROM MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B, MI_DMS_INFO_GROUP C  
		WHERE
		A.DEVICE_ID = B.DEVICE_ID AND B.GROUP_ID = C.GROUP_ID AND (
		<foreach collection="deviceIds" item="deviceId" index="index" separator="or">
			A.DEVICE_ID = #{deviceId}
		</foreach>
		)
		
    </select>

    <select id="getDeviceListByGroup" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device">
        SELECT *
        <include refid="getDeviceListByGroupCommon"/>
        ORDER BY CREATE_DATE DESC
        LIMIT #{pageSize} OFFSET #{startPos}
    </select>
    
    <select id="getDeviceListByGroup" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device" databaseId="mssql">
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
        SELECT * FROM (
          SELECT *, ROW_NUMBER() OVER(ORDER BY CREATE_DATE DESC ) as RowNum

          <include refid="getDeviceListByGroupCommon" />
        ) as SubQuery
        WHERE RowNum > ${safe_startPos} and RowNum &lt;= ${safe_rownumLimit}
        ORDER BY RowNum 
    </select>

    <select id="getDeviceListByGroupCnt" resultType="int">
        SELECT COUNT(DEVICE_ID)
        <include refid="getDeviceListByGroupCommon"/>
    </select>

    <sql id="getDeviceListByGroupCommon">
        FROM MI_DMS_INFO_DEVICE
        <bind name="cnt" value="0"/>
        <if test="modelName != null and !modelName.equals('')">
            WHERE DEVICE_MODEL_NAME = #{modelName}
            <bind name="cnt" value="1"/>
        </if>
        <if test="deviceId != null and !deviceId.equals('')">
            <choose>
                <when test="cnt > 0">
                    AND
                </when>
                <otherwise>
                    WHERE
                </otherwise>
            </choose>
            <bind name="deviceIdPattern" value="'%' + deviceId + '%'"/>
            DEVICE_ID LIKE #{deviceIdPattern}
            <bind name="cnt" value="1"/>
        </if>
        <if test="groupId != null and !groupId.equals('')">
            <choose>
                <when test="cnt > 0">
                    AND
                </when>
                <otherwise>
                    WHERE
                </otherwise>
            </choose>
            DEVICE_ID IN ( SELECT DEVICE_ID FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = #{groupId} )
        </if>
    </sql>

    <select id="getDeviceListByGroupId" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device">
        SELECT *  FROM MI_DMS_INFO_DEVICE WHERE DEVICE_ID IN ( SELECT DEVICE_ID FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = #{groupId})
            AND IS_CHILD = <include refid="utils.false"/>
        ORDER BY CREATE_DATE DESC
    </select>
    
    <select id="getDeviceAndTagListByGroupId" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTag">
        SELECT A.DEVICE_ID, A.DEVICE_NAME, B.TAG_ID, C.TAG_NAME 
        FROM MI_DMS_INFO_DEVICE A 
            LEFT JOIN MI_TAG_MAP_DEVICE_TAG B ON A.DEVICE_ID = B.DEVICE_ID AND (B.IS_VAR_TAG = <include refid="utils.false"/> OR B.IS_VAR_TAG = NULL)
            LEFT JOIN MI_TAG_INFO_TAG C on B.TAG_ID = C.TAG_ID
        WHERE A.DEVICE_ID IN ( SELECT DEVICE_ID FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = #{groupId} ) AND A.DEVICE_TYPE != 'SIG_CHILD' 
    </select>

    <select id="getDeviceUnapprovedGroupCode" resultType="java.lang.Long">
        SELECT UNAPPROVED_GROUP_CODE FROM MI_DMS_INFO_DEVICE WHERE DEVICE_ID = #{deviceId}
    </select>

    <select id="getDeviceAndTagListByGroupIds" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTag">
    	SELECT A.DEVICE_ID, A.DEVICE_NAME, B.TAG_ID, C.TAG_NAME FROM MI_DMS_INFO_DEVICE A left join MI_TAG_MAP_DEVICE_TAG B on A.DEVICE_ID = B.DEVICE_ID left join MI_TAG_INFO_TAG C on B.TAG_ID = C.TAG_ID
        WHERE A.DEVICE_ID IN (
        	SELECT DEVICE_ID FROM MI_DMS_MAP_GROUP_DEVICE WHERE 
	        <foreach collection="groupIds" item="groupId" index="index" separator=" OR ">
				GROUP_ID = #{groupId}
			</foreach>
		)
    </select>
    
    <select id="getDeviceAndTagListByDeviceIds" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTag">
        SELECT 
            A.DEVICE_ID, A.DEVICE_NAME, B.TAG_ID, C.TAG_NAME, C.TAG_DESC, C.TAG_TYPE, D.TAG_CONDITION, D.TAG_CONDITION_ID
        FROM 
            MI_DMS_INFO_DEVICE A 
                LEFT JOIN MI_TAG_MAP_DEVICE_TAG B ON A.DEVICE_ID = B.DEVICE_ID 
		            <choose>
		                <when test="isVarTag == true">
		                    AND B.IS_VAR_TAG = <include refid="utils.true"/>
		                </when>
		                <otherwise>
		                    AND (B.IS_VAR_TAG = <include refid="utils.false"/> OR B.IS_VAR_TAG = NULL)
		                </otherwise>
		            </choose>
                LEFT JOIN MI_TAG_INFO_TAG C ON B.TAG_ID = C.TAG_ID
                LEFT JOIN MI_TAG_INFO_TAG_CONDITION D ON D.TAG_CONDITION_ID = B.TAG_CONDITION_ID
        WHERE
            1 = 1
            <if test="deviceIds != null">
                AND A.DEVICE_ID IN
                <foreach collection="deviceIds" open="(" separator="," close=")" item="deviceId">
                    #{deviceId}
                </foreach>
            </if>
    </select>

    <select id="getDeviceListByModelNameAndGroup"
            resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device">
        SELECT * FROM MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B
        WHERE A.DEVICE_ID = B.DEVICE_ID AND B.GROUP_ID = #{groupId}
        AND A.DEVICE_MODEL_NAME = #{modelName}
    </select>

    <select id="getDeviceListCnt" resultType="int">
        SELECT COUNT(DEVICE_ID)
        <include refid="getDeviceListCommon"/>
    </select>

    <select id="getDeviceList" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device">
        SELECT *
        <include refid="getDeviceListCommon"/>
    </select>

    <select id="getDeviceListPaged" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device">
        SELECT *
        <include refid="getDeviceListCommon"/>
        LIMIT #{pageSize} OFFSET #{startPos}
    </select>
    
    <select id="getDeviceListPaged" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device" databaseId="mssql">
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
        SELECT * FROM (
          SELECT *, ROW_NUMBER() OVER(ORDER BY DEVICE_ID ASC ) as RowNum
          <include refid="getDeviceListCommon" />
        ) as SubQuery
        WHERE RowNum > ${safe_startPos} and RowNum &lt;= ${safe_rownumLimit}
        ORDER BY RowNum
    </select>

    <sql id="getDeviceListCommon">
        FROM MI_DMS_INFO_DEVICE
        <bind name="cnt" value="0"/>
        <if test="modelName != null and !modelName.equals('')">
            WHERE DEVICE_MODEL_NAME = #{modelName}
            <bind name="cnt" value="1"/>
        </if>
        <if test="deviceId != null and !deviceId.equals('')">
            <choose>
                <when test="cnt > 0">
                    AND
                </when>
                <otherwise>
                    WHERE
                </otherwise>
            </choose>
            <bind name="deviceIdPattern" value="'%' + deviceId + '%'"/>
            DEVICE_ID LIKE #{deviceIdPattern}
            <bind name="cnt" value="1"/>
        </if>
        <if test="modelCode != null and !modelCode.equals('')">
            <choose>
                <when test="cnt > 0">
                    AND
                </when>
                <otherwise>
                    WHERE
                </otherwise>
            </choose>
            DEVICE_MODEL_CODE = #{modelCode}
        </if>
    </sql>

    <select id="getDeviceMinInfo" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device">
        SELECT *
        FROM MI_DMS_INFO_DEVICE WHERE DEVICE_ID = #{deviceId}
    </select>

    <select id="getDeviceModel" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceModel">
        SELECT * FROM MI_DMS_INFO_DEVICE_MODEL
        WHERE DEVICE_MODEL_NAME = #{deviceModelName}
    </select>

    <select id="getDeviceModelListCnt" resultType="int">
        SELECT COUNT(DEVICE_MODEL_NAME)
        <include refid="getDeviceModelListCommon"/>
    </select>

    <select id="getDeviceModelList" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceModel">
        SELECT DEVICE_MODEL_NAME, DEVICE_MODEL_CODE, VENDOR, DESCRIPTION, CREATOR_ID, CREATE_DATE
        <include refid="getDeviceModelListCommon"/>
        <if test="pageSize != null and startPos != null">
            LIMIT #{pageSize} OFFSET #{startPos}
        </if>
    </select>

	<select id="getDeviceModelList"
		resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceModel"
		databaseId="mssql">
		<choose>
			<when test="pageSize != null and startPos != null">
				<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
                <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
                SELECT * FROM (
				SELECT DEVICE_MODEL_NAME, DEVICE_MODEL_CODE, VENDOR, DESCRIPTION,
				CREATOR_ID, CREATE_DATE,
				ROW_NUMBER() OVER(ORDER BY CREATE_DATE DESC ) as RowNum

				<include refid="getDeviceModelListCommon" />
				) as SubQuery
				WHERE RowNum > ${safe_startPos} and RowNum &lt;= ${safe_rownumLimit}
				ORDER BY RowNum
			</when>
			<otherwise>
				SELECT DEVICE_MODEL_NAME, DEVICE_MODEL_CODE, VENDOR, DESCRIPTION,
				CREATOR_ID, CREATE_DATE
				<include refid="getDeviceModelListCommon" />
				ORDER BY CREATE_DATE DESC
			</otherwise>
		</choose>
	</select>

    <sql id="getDeviceModelListCommon">
        FROM MI_DMS_INFO_DEVICE_MODEL
        <if test="deviceModelCode != null">
            WHERE DEVICE_MODEL_CODE = #{deviceModelCode}
        </if>
    </sql>

	<select id="getExpiredDeviceList" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring">
		SELECT A.* 
		FROM MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B 
		WHERE A.DEVICE_ID = B.DEVICE_ID AND IS_APPROVED = <include refid="utils.true"/> AND EXPIRATION_DATE IS NOT NULL <include refid="expirationDate"/>
	</select>

    <select id="getDeviceMonitoringListCnt" resultType="int">
        SELECT COUNT(DISTINCT A.DEVICE_ID)
        <include refid="getDeviceMonitoringListCommon"/>
    </select>

    <select id="getDeviceMonitoringList"
            resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring">
        SELECT A.DEVICE_ID, A.DEVICE_NAME, DEVICE_MODEL_NAME, DEVICE_TYPE, EXPIRATION_DATE, A.VWT_ID, A.IS_REDUNDANCY, A.WEBCAM, A.SCREEN_ROTATION, A.HAS_CHILD, A.IS_CHILD, A.CHILD_CNT, A.CONN_CHILD_CNT, A.ERROR_FLAG, A.RECOMMEND_PLAY,
        	   A.DEVICE_SERIES, A.DEVICE_HW_PLATFORM, A.RM_RULE_VERSION,
        	   <![CDATA[( EXTRACT('epoch' from CURRENT_TIMESTAMP - LAST_CONNECTION_TIME)::int < (MONITORING_INTERVAL + 1) * 60) AS POWER]]>,

               A.DEVICE_TYPE_VERSION, IP_ADDRESS, TUNNELING_SERVER, B.GROUP_ID, A.DISK_SPACE_REPOSITORY, A.PLAYER_VERSION, A.APPLICATION_VERSION, A.MAP_LOCATION, A.PRE_CONFIG_VERSION
               <if test="condition.group_mode != null and condition.group_mode == 'GROUP'">, GROUPS.GROUP_NAME, GROUPS.MIN_PRIORITY</if>
               <include refid="iconsSelect"/>
        <include refid="getDeviceMonitoringListCommon"/>
         GROUP BY A.DEVICE_ID, A.DEVICE_NAME, DEVICE_MODEL_NAME, DEVICE_TYPE, EXPIRATION_DATE, A.VWT_ID, A.IS_REDUNDANCY, A.WEBCAM, A.SCREEN_ROTATION, A.HAS_CHILD, A.IS_CHILD, A.CHILD_CNT, A.CONN_CHILD_CNT, A.ERROR_FLAG, A.RECOMMEND_PLAY, A.LAST_CONNECTION_TIME, A.MONITORING_INTERVAL,
         		 A.DEVICE_SERIES, A.DEVICE_HW_PLATFORM, A.RM_RULE_VERSION,
                 A.DEVICE_TYPE_VERSION, IP_ADDRESS, TUNNELING_SERVER, B.GROUP_ID, A.DISK_SPACE_REPOSITORY, A.PLAYER_VERSION, A.APPLICATION_VERSION, A.MAP_LOCATION, A.PRE_CONFIG_VERSION
                 <if test="condition.group_mode != null and condition.group_mode == 'GROUP'">, GROUPS.GROUP_NAME, GROUPS.MIN_PRIORITY</if>
     
        <if test="condition.sort_name != null and condition.sort_name != '' and condition.order_dir != null and condition.order_dir != ''">
            <bind name="safe_sortUpper" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
            <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir)" />
            ORDER BY ${safe_sortUpper} ${safe_sortOrder}
        </if>
        LIMIT #{pageSize} OFFSET #{startPos}
    </select>
	
	<select id="getDeviceMonitoringList"
            resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring" databaseId="mssql">
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
        SELECT * FROM (
           SELECT A.DEVICE_ID, A.DEVICE_NAME, DEVICE_MODEL_NAME, DEVICE_TYPE, EXPIRATION_DATE, A.VWT_ID, A.IS_REDUNDANCY, A.WEBCAM, A.SCREEN_ROTATION, A.HAS_CHILD, A.IS_CHILD, A.CHILD_CNT, A.CONN_CHILD_CNT, A.ERROR_FLAG, A.RECOMMEND_PLAY,
                  A.DEVICE_SERIES, A.DEVICE_HW_PLATFORM, A.RM_RULE_VERSION, A.PRE_CONFIG_VERSION,
                  <![CDATA[CAST(CASE WHEN DATEDIFF(S, LAST_CONNECTION_TIME, GETDATE()) < ((MONITORING_INTERVAL + 1) * 60) THEN 1 ELSE 0 END AS BIT) AS POWER]]>,
                  A.DEVICE_TYPE_VERSION, IP_ADDRESS, TUNNELING_SERVER, B.GROUP_ID, A.DISK_SPACE_REPOSITORY, A.PLAYER_VERSION, A.APPLICATION_VERSION, A.MAP_LOCATION,
                 ROW_NUMBER() OVER(ORDER BY
           <choose>
               <when test="condition.sort_name != null and condition.sort_name != '' and condition.order_dir != null and condition.order_dir != ''">
                   <bind name="safe_sortUpper" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
                   <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
               		<choose>
               			<when test="safe_sortUpper != 'DEVICE_ID' and safe_sortUpper != 'DEVICE_NAME'">
               				${safe_sortUpper}
               			</when>
                   		<otherwise>
                   			A.${safe_sortUpper}
                   		</otherwise> 
                   	</choose>	
               			${safe_sortOrder}
               </when>
               <otherwise>
                   A.DEVICE_ID ASC
               </otherwise>
           </choose>
           ) as RowNum

		  <include refid="iconsSelect"/>
	
          <include refid="getDeviceMonitoringListCommon" />
           GROUP BY A.DEVICE_ID, A.DEVICE_NAME, DEVICE_MODEL_NAME, DEVICE_TYPE, EXPIRATION_DATE, A.VWT_ID, A.IS_REDUNDANCY, A.WEBCAM, A.SCREEN_ROTATION, A.HAS_CHILD, A.IS_CHILD, A.CHILD_CNT, A.CONN_CHILD_CNT, A.ERROR_FLAG, A.LAST_CONNECTION_TIME, A.MONITORING_INTERVAL, A.MAP_LOCATION,
           		  A.DEVICE_SERIES, A.DEVICE_HW_PLATFORM, A.RM_RULE_VERSION, A.PRE_CONFIG_VERSION,
                  A.DEVICE_TYPE_VERSION, IP_ADDRESS, TUNNELING_SERVER, B.GROUP_ID, A.DISK_SPACE_REPOSITORY, A.PLAYER_VERSION, A.APPLICATION_VERSION, A.MAP_LOCATION, A.RECOMMEND_PLAY
       
        ) as SubQuery
        WHERE RowNum > ${safe_startPos} and RowNum &lt;= ${safe_rownumLimit}
        ORDER BY RowNum
    </select>
	
    <sql id="getDeviceMonitoringListCommon">
        FROM MI_DMS_INFO_DEVICE A
        <if test="condition.sourceFilterList != null" >
        	LEFT JOIN MI_DMS_INFO_DISPLAY DP ON A.DEVICE_ID = DP.DEVICE_ID
        </if>
        <include refid="tagFrom"/>
        <include refid="iconsFrom_monitoring"/>
        , MI_DMS_MAP_GROUP_DEVICE B <include refid="deviceGroupAuthFrom_C"/>
        <if test="condition.group_mode != null">
            <if test="condition.group_mode == 'GROUP'">
                LEFT JOIN MI_DMS_INFO_GROUP GROUPS ON B.GROUP_ID = GROUPS.GROUP_ID
            </if>
        </if>
        WHERE A.DEVICE_ID = B.DEVICE_ID AND IS_CHILD = <include refid="utils.false"/> AND IS_APPROVED = <include refid="utils.true"/> <include refid="deviceGroupAuthWhere_C"/>

        <if test="condition.src_name != null and condition.src_name != ''">
            <bind name="srcUpper" value="_parameter.condition.src_name.toUpperCase()"/>
            <bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
            AND ((UPPER(A.DEVICE_NAME) LIKE #{srcNamePattern}) 
            OR (UPPER(A.MAC_ADDRESS) LIKE #{srcNamePattern}) 
            OR (UPPER(A.DEVICE_MODEL_NAME) LIKE #{srcNamePattern}) 
            OR (UPPER(A.IP_ADDRESS) LIKE #{srcNamePattern})
            OR (UPPER(E.TAG_VALUE) LIKE #{srcNamePattern})
            )
        </if>
   	
   		<include refid="deviceTypeArrQuery" />
  
        <if test="condition.device_id != null and condition.device_id != ''">
            <bind name="deviceIdPattern" value="'%' + condition.device_id + '%'"/>
            AND A.DEVICE_ID LIKE #{deviceIdPattern}
        </if>
        <if test="condition.status_view_mode != null and !condition.status_view_mode.equals(constants.DEVICE_STATUS_VIEW_ALL)">
            <if test="condition.status_view_mode == constants.DEVICE_STATUS_VIEW_CONNECTION">
                <include refid="connectionMode" />
            </if>

            <if test="condition.status_view_mode == constants.DEVICE_STATUS_VIEW_DISCONNECTION">
                 <include refid="disconnectionMode" />
            </if>
        </if>
        <include refid="deviceExpirationDate" />
        <choose>
            <when test="deviceGroupList != null and deviceGroupList.size > 0">
                AND B.GROUP_ID IN
                <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
                    #{deviceGroupId.group_id}
                </foreach>
            </when>
            <otherwise>
                <if test="condition.group_id != null and condition.group_id != 0">
                    AND B.GROUP_ID = #{condition.group_id}
                </if>
            </otherwise>
        </choose>
        <if test="condition.device_model_name != null and condition.device_model_name != ''">
            <bind name="deviceModelNamePattern" value="'%' + condition.device_model_name + '%'"/>
            AND A.DEVICE_MODEL_NAME LIKE #{deviceModelNamePattern}
        </if>
		<if test="condition.tagFilterList != null">
               <foreach collection="condition.tagFilterList" open="AND E.TAG_ID IN (" separator="," close=")" item="item">
                   #{item}
               </foreach>
        </if> 
        <if test="condition.sourceFilterList != null" >
       		AND DP.BASIC_SOURCE IN
       		   <foreach collection="condition.sourceFilterList" item="source" open="(" separator="," close=")" >
                   #{source}
               </foreach>
        </if>          
        
        <if test="condition.view_mode != null and condition.view_mode == 'map'">
               AND A.MAP_LOCATION IS NOT NULL  
        </if>
        <if test="condition.recommend_play != null">
            AND A.RECOMMEND_PLAY = #{condition.recommend_play}
        </if>
        
        <include refid="hasAlarmFilterQueries" />
        
        <include refid="hasFunctionFilterQueries" />
        
        <include refid="commonSearchKeywowrdQuery" />
        
    </sql>


    <select id="getDeviceMonitoringFilterList"
            resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring">
        SELECT A.DEVICE_ID, A.DEVICE_NAME, DEVICE_MODEL_NAME, IP_ADDRESS, TUNNELING_SERVER, GROUP_ID, A.HAS_CHILD, A.IS_CHILD, A.CHILD_CNT, A.CONN_CHILD_CNT, A.FIRMWARE_VERSION
        <include refid="iconsSelect"/>
        
        FROM MI_DMS_INFO_DEVICE A
        <include refid="tagFrom"/>
        <include refid="iconsFrom"><property name="alias" value="A"/></include>
        , MI_DMS_MAP_GROUP_DEVICE B
        WHERE A.DEVICE_ID = B.DEVICE_ID AND IS_APPROVED = <include refid="utils.true"/>
        <if test="condition.src_name != null and !condition.src_name.equals('')">
            <bind name="deviceNamePattern" value="'%' + condition.src_name + '%'"/>
            AND ((UPPER(A.DEVICE_NAME) LIKE #{deviceNamePattern}) 
            OR (UPPER(A.MAC_ADDRESS) LIKE #{deviceNamePattern}) 
            OR (UPPER(A.DEVICE_MODEL_NAME) LIKE #{deviceNamePattern}) 
            OR (UPPER(A.IP_ADDRESS) LIKE #{deviceNamePattern})
            OR (UPPER(E.TAG_VALUE) LIKE #{deviceNamePattern})
            )
        </if>
        <if test="condition.device_id != null and !condition.device_id.equals('')">
            <bind name="deviceIdPattern" value="'%' + condition.device_id + '%'"/>
            AND A.DEVICE_ID LIKE #{deviceIdPattern}
        </if>

        <choose>
            <when test="condition.isRoot">
                <if test="deviceGroupList != null and deviceGroupList.size > 0">
                    AND GROUP_ID IN
                    <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
                        #{deviceGroupId.group_id}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <if test="condition.group_id != null and condition.group_id != 0">
                    AND GROUP_ID = #{condition.group_id}
                </if>
            </otherwise>
        </choose>

        <if test="condition.device_model_name != null and !condition.device_model_name.equals('')">
            <bind name="deviceModelNamePattern" value="'%' + condition.device_model_name + '%'"/>
            AND DEVICE_MODEL_NAME LIKE #{deviceModelNamePattern}
        </if>
		GROUP BY A.DEVICE_ID, A.DEVICE_NAME, DEVICE_MODEL_NAME, IP_ADDRESS, TUNNELING_SERVER, GROUP_ID, A.HAS_CHILD, A.IS_CHILD, A.CHILD_CNT, A.CONN_CHILD_CNT
  
        <if test="condition.sort_name != null and !condition.sort_name.equals('') and condition.order_dir != null and !condition.order_dir.equals('')">
                <bind name="safe_sort" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(condition.sort_name)" />
        	<bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(condition.order_dir)" />
            ORDER BY ${safe_sort} ${safe_sortOrder}
        </if>
    </select>

    <select id="getDeviceNameById" resultType="String">
        SELECT DEVICE_NAME FROM MI_DMS_INFO_DEVICE
        WHERE DEVICE_ID = #{deviceId}
    </select>

    <select id="getDeviceOperationInfo" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device">
        SELECT *
        FROM MI_DMS_INFO_DEVICE WHERE DEVICE_ID = #{deviceId}
    </select>

    <select id="getDMInfo" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device">
        SELECT * FROM MI_DMS_INFO_DEVICE
        <if test="deviceId != null">
            <bind name="deviceIdPattern" value="'%' + deviceId + '%'"/>
            WHERE DEVICE_ID LIKE #{deviceIdPattern}
        </if>
    </select>

    <select id="getModelNameByDeviceId" resultType="String">
        SELECT DEVICE_MODEL_NAME
        FROM MI_DMS_INFO_DEVICE
        WHERE DEVICE_ID = #{deviceId}
    </select>

    <select id="getModelNameListByDeviceId"
            resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceModel">
        SELECT DEVICE_MODEL_NAME
        FROM MI_DMS_INFO_DEVICE_MODEL
        WHERE DEVICE_MODEL_CODE IN
        (SELECT DEVICE_MODEL_CODE FROM MI_DMS_INFO_DEVICE WHERE DEVICE_ID = #{deviceId})
    </select>

    <select id="getModelNameListByModelCode"
            resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceModel">
        SELECT DEVICE_MODEL_NAME
        FROM MI_DMS_INFO_DEVICE_MODEL
        WHERE DEVICE_MODEL_CODE = #{deviceModelCode}
    </select>

    <select id="getMonitoringInfoByDeviceId"
            resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring">
        SELECT A.DEVICE_ID, A.DEVICE_NAME, A.DEVICE_TYPE, A.DEVICE_TYPE_VERSION, IP_ADDRESS, TUNNELING_SERVER, A.HAS_CHILD, A.IS_CHILD, A.CHILD_CNT, A.CONN_CHILD_CNT, A.FIRMWARE_VERSION,
        B.GROUP_ID, C.PROGRAM_ID, E.PROGRAM_NAME, E.DESCRIPTION, C.FRAME_ID,
        FRAME_INDEX, X, Y, A.HEIGHT, A.WIDTH, DEFAULT_CONTENT_ID
        FROM MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B, MI_CDS_INFO_FRAME C,
        MI_CDS_MAP_PROGRAM_DEVICE D, MI_CDS_INFO_PROGRAM E
        WHERE A.DEVICE_ID = B.DEVICE_ID AND B.GROUP_ID = D.DEVICE_GROUP_ID
        AND C.PROGRAM_ID = D.PROGRAM_ID AND C.PROGRAM_ID = E.PROGRAM_ID
        AND A.DEVICE_ID = #{deviceId}
    </select>

    <select id="getDeviceApprovalStatusByDeviceId"
            resultType="Boolean">
        SELECT IS_APPROVED FROM MI_DMS_INFO_DEVICE WHERE DEVICE_ID = #{deviceId}
    </select>

    <select id="getMonitoringInfoByDeviceIdList"
            resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring">
        SELECT A.DEVICE_ID, A.DEVICE_NAME, DEVICE_MODEL_NAME, DEVICE_TYPE, A.EXPIRATION_DATE, A.VWT_ID, A.IS_REDUNDANCY, A.WEBCAM, A.SCREEN_ROTATION, A.PLAYER_VERSION, A.APPLICATION_VERSION, A.ERROR_FLAG,
                <![CDATA[(EXTRACT('epoch' from CURRENT_TIMESTAMP - LAST_CONNECTION_TIME)::int < (MONITORING_INTERVAL + 1) * 60) AS POWER]]>,
               A.DEVICE_TYPE_VERSION, IP_ADDRESS, TUNNELING_SERVER, B.GROUP_ID, A.DISK_SPACE_REPOSITORY, A.HAS_CHILD, A.IS_CHILD, A.CHILD_CNT, A.CONN_CHILD_CNT, A.PRE_CONFIG_VERSION
               <include refid="iconsSelect"/>
        FROM MI_DMS_INFO_DEVICE A
        <include refid="iconsFrom"><property name="alias" value="A"/></include>
        , MI_DMS_MAP_GROUP_DEVICE B
        WHERE A.DEVICE_ID = B.DEVICE_ID AND IS_APPROVED = <include refid="utils.true"/>
        AND A.DEVICE_ID IN
        <foreach item="deviceId" collection="deviceIdList" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
        GROUP BY A.DEVICE_ID, A.DEVICE_NAME, DEVICE_MODEL_NAME, DEVICE_TYPE, A.EXPIRATION_DATE, A.VWT_ID, A.IS_REDUNDANCY, A.WEBCAM, A.SCREEN_ROTATION, A.PLAYER_VERSION, A.APPLICATION_VERSION, A.ERROR_FLAG, A.LAST_CONNECTION_TIME, A.MONITORING_INTERVAL,
               A.DEVICE_TYPE_VERSION, IP_ADDRESS, TUNNELING_SERVER, B.GROUP_ID, A.DISK_SPACE_REPOSITORY, A.HAS_CHILD, A.IS_CHILD, A.CHILD_CNT, A.CONN_CHILD_CNT, A.PRE_CONFIG_VERSION
    </select>
    
     <select id="getMonitoringInfoByDeviceIdList"
            resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring" databaseId="mssql">
        SELECT A.DEVICE_ID, A.DEVICE_NAME, DEVICE_MODEL_NAME, DEVICE_TYPE, A.EXPIRATION_DATE, A.VWT_ID, A.IS_REDUNDANCY, A.WEBCAM, A.SCREEN_ROTATION, A.PLAYER_VERSION, A.APPLICATION_VERSION, A.ERROR_FLAG,
        	   <![CDATA[CAST(CASE WHEN DATEDIFF(S, LAST_CONNECTION_TIME, GETDATE()) < ((MONITORING_INTERVAL + 1) * 60) THEN 1 ELSE 0 END AS BIT) AS POWER]]>,
               A.DEVICE_TYPE_VERSION, IP_ADDRESS, TUNNELING_SERVER, B.GROUP_ID, A.DISK_SPACE_REPOSITORY, A.HAS_CHILD, A.IS_CHILD, A.CHILD_CNT, A.CONN_CHILD_CNT, A.PRE_CONFIG_VERSION
               <include refid="iconsSelect"/>
        FROM MI_DMS_INFO_DEVICE A
        <include refid="iconsFrom"><property name="alias" value="A"/></include>
        , MI_DMS_MAP_GROUP_DEVICE B
        WHERE A.DEVICE_ID = B.DEVICE_ID AND IS_APPROVED = <include refid="utils.true"/>
        AND A.DEVICE_ID IN
        <foreach item="deviceId" collection="deviceIdList" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
        GROUP BY A.DEVICE_ID, A.DEVICE_NAME, DEVICE_MODEL_NAME, DEVICE_TYPE, A.EXPIRATION_DATE, A.VWT_ID, A.IS_REDUNDANCY, A.WEBCAM, A.SCREEN_ROTATION, A.PLAYER_VERSION, A.APPLICATION_VERSION, A.ERROR_FLAG, A.LAST_CONNECTION_TIME, A.MONITORING_INTERVAL,
               A.DEVICE_TYPE_VERSION, IP_ADDRESS, TUNNELING_SERVER, B.GROUP_ID, A.DISK_SPACE_REPOSITORY, A.HAS_CHILD, A.IS_CHILD, A.CHILD_CNT, A.CONN_CHILD_CNT, A.PRE_CONFIG_VERSION
    </select>
	
	<sql id="getNonApprovedDeviceListLimitByDeviceTypeQuery">
		FROM MI_DMS_INFO_DEVICE
        WHERE IS_APPROVED = <include refid="utils.false"/>
        
        <if test="deviceType != null and !deviceType.equals('')">
            AND 
			<include refid="deviceTypeChooseQueryNonConstants"></include>
        </if>
        <if test="!extraFlag">
            AND DEVICE_MODEL_CODE != '7000'
            AND DEVICE_MODEL_CODE != '7002'
        </if>
        <if test="condition.src_name != null and !condition.src_name.equals('')">
            <bind name="srcUpper" value="_parameter.condition.src_name.toUpperCase()"/>
            <bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
            AND ((UPPER(DEVICE_NAME) LIKE #{srcNamePattern} ESCAPE '^') OR (UPPER(MAC_ADDRESS) LIKE #{srcNamePattern} ESCAPE '^') OR (UPPER(DEVICE_MODEL_NAME) LIKE #{srcNamePattern} ESCAPE '^') OR (UPPER(IP_ADDRESS) LIKE #{srcNamePattern} ESCAPE '^'))
        </if>
        <if test="condition.deviceId != null and !condition.deviceId.equals('')">
            <bind name="deviceIdPattern" value="'%' + condition.deviceId + '%'"/>
            AND DEVICE_ID LIKE #{deviceIdPattern}
        </if>
	</sql>
	
    <select id="getNonApprovedDeviceListLimitByDeviceType" resultType="map">
        SELECT DEVICE_NAME, DEVICE_ID, DEVICE_TYPE, DEVICE_TYPE_VERSION, DEVICE_MODEL_NAME, IP_ADDRESS, DEVICE_MODEL_CODE, CREATE_DATE, HAS_CHILD, SUPPORT_FLAG
        <include refid="getNonApprovedDeviceListLimitByDeviceTypeQuery" />
        <if test="condition.sort_name != null and !condition.sort_name.equals('') and condition.order_dir != null and !condition.order_dir.equals('')">
                <bind name="safe_sort" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(condition.sort_name)" />
        	<bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(condition.order_dir)" />
            ORDER BY ${safe_sort} ${safe_sortOrder}
        </if>
        LIMIT #{pageSize} OFFSET #{startPos}
    </select>
    
    <select id="getNonApprovedDeviceListLimitByDeviceTypeCnt" resultType="int">
        SELECT COUNT(DEVICE_ID)
        <include refid="getNonApprovedDeviceListLimitByDeviceTypeQuery" />
        <if test="condition.sort_name != null and !condition.sort_name.equals('') and condition.order_dir != null and !condition.order_dir.equals('')">
                <bind name="safe_sort" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(condition.sort_name)" />
        	<bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(condition.order_dir)" />
            ORDER BY ${safe_sort} ${safe_sortOrder}
        </if>
        
    </select>
    
     <select id="getNonApprovedDeviceListLimitByDeviceType" resultType="map" databaseId="mssql">
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
         SELECT * FROM ( 
          SELECT DEVICE_NAME, DEVICE_ID, DEVICE_TYPE, DEVICE_TYPE_VERSION, DEVICE_MODEL_NAME, IP_ADDRESS, DEVICE_MODEL_CODE, CREATE_DATE, HAS_CHILD, SUPPORT_FLAG,
                 ROW_NUMBER() OVER(ORDER BY
           <choose>
               <when test="condition.sort_name != null and !condition.sort_name.equals('') and condition.order_dir != null and !condition.order_dir.equals('')">
                   <bind name="safe_sortUpper" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
                   <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir)" />
                   ${safe_sortUpper} ${safe_sortOrder}
               </when>
               <otherwise>
                   CREATE_DATE DESC
               </otherwise>
           </choose>
           ) as RowNum

           <include refid="getNonApprovedDeviceListLimitByDeviceTypeQuery" />
        ) as SubQuery
        WHERE RowNum > ${safe_startPos} and RowNum &lt;= ${safe_rownumLimit}
        ORDER BY RowNum
    </select>
    
    <select id="getNonApprovedDeviceListLimitByDeviceTypeCnt" resultType="int" databaseId="mssql">
         SELECT COUNT(DEVICE_ID) FROM (
          SELECT DEVICE_NAME, DEVICE_ID, DEVICE_TYPE, DEVICE_TYPE_VERSION, DEVICE_MODEL_NAME, IP_ADDRESS, DEVICE_MODEL_CODE, CREATE_DATE,
                 ROW_NUMBER() OVER(ORDER BY
           <choose>
               <when test="condition.sort_name != null and !condition.sort_name.equals('') and condition.order_dir != null and !condition.order_dir.equals('')">
                   <bind name="safe_sortUpper" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
                   <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir)" />
                   ${safe_sortUpper} ${safe_sortOrder}
               </when>
               <otherwise>
                   CREATE_DATE DESC
               </otherwise>
           </choose>
           ) as RowNum

           <include refid="getNonApprovedDeviceListLimitByDeviceTypeQuery" />
        ) as SubQuery
        ORDER BY RowNum
    </select>

    <select id="getNonApprovedDeviceListCnt" resultType="int">
        SELECT COUNT(DEVICE_ID)
        FROM MI_DMS_INFO_DEVICE
        WHERE IS_APPROVED = <include refid="utils.false"/>
        <if test="map.src_name != null and !map.src_name.equals('')">
            <bind name="deviceNamePattern" value="'%' + map.src_name + '%'"/>
            AND ((UPPER(DEVICE_NAME) LIKE #{deviceNamePattern} ESCAPE '^') OR (UPPER(MAC_ADDRESS) LIKE #{deviceNamePattern} ESCAPE '^') OR (UPPER(DEVICE_MODEL_NAME) LIKE #{deviceNamePattern} ESCAPE '^') OR (UPPER(IP_ADDRESS) LIKE #{deviceNamePattern} ESCAPE '^') OR (UPPER(MAC_ADDRESS) LIKE #{deviceNamePattern} ESCAPE '^'))
        </if>
        <if test="map.deviceId != null and !map.deviceId.equals('')">
            <bind name="deviceIdPattern" value="'%' + map.deviceId + '%'"/>
            AND DEVICE_ID LIKE #{deviceIdPattern}
        </if>
    </select>
	
	<sql id="getNonApprovedDeviceListQuery">
		FROM MI_DMS_INFO_DEVICE
        WHERE IS_APPROVED = <include refid="utils.false"/>
        <if test="map.src_name != null and !map.src_name.equals('')">
            <bind name="deviceNamePattern" value="'%' + map.src_name + '%'"/>
            AND ((UPPER(DEVICE_NAME) LIKE #{deviceNamePattern} ESCAPE '^') OR (UPPER(MAC_ADDRESS) LIKE #{deviceNamePattern} ESCAPE '^') OR (UPPER(DEVICE_MODEL_NAME) LIKE #{deviceNamePattern} ESCAPE '^') OR (UPPER(IP_ADDRESS) LIKE #{deviceNamePattern} ESCAPE '^') OR (UPPER(MAC_ADDRESS) LIKE #{deviceNamePattern} ESCAPE '^'))
        </if>
        <if test="map.deviceId != null and !map.deviceId.equals('')">
            <bind name="deviceIdPattern" value="'%' + map.deviceId + '%'"/>
            AND DEVICE_ID LIKE #{deviceIdPattern}
        </if>
	</sql>
	
    <select id="getNonApprovedDeviceList" resultType="map">
        SELECT DEVICE_NAME, DEVICE_ID, DEVICE_TYPE, DEVICE_TYPE_VERSION, DEVICE_MODEL_NAME, IP_ADDRESS, DEVICE_MODEL_CODE, CREATE_DATE, HAS_CHILD, SUPPORT_FLAG, SERIAL_DECIMAL
        <include refid="getNonApprovedDeviceListQuery" />
        <if test="map.sort != null and !map.sort.equals('') and map.dir != null and !map.dir.equals('')">
                <bind name="safe_sort" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(map.sort)" />
        	<bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(map.dir)" />
            ORDER BY ${safe_sort} ${safe_sortOrder}
        </if>
        LIMIT #{pageSize} OFFSET #{startPos}
    </select>

    <select id="getNonApprovedDeviceList" resultType="map" databaseId="mssql">
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
        SELECT * FROM (
          SELECT DEVICE_NAME, DEVICE_ID, DEVICE_TYPE, DEVICE_TYPE_VERSION, DEVICE_MODEL_NAME, IP_ADDRESS, DEVICE_MODEL_CODE, CREATE_DATE, HAS_CHILD, SUPPORT_FLAG, SERIAL_DECIMAL,
                 ROW_NUMBER() OVER(ORDER BY
           <choose>
               <when test="map.sort != null and !map.sort.equals('') and map.dir != null and !map.dir.equals('')">
                   <bind name="safe_sortUpper" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.map.sort)" />
                   <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.map.dir)" />
                   ${safe_sortUpper} ${safe_sortOrder}
               </when>
               <otherwise>
                   CREATE_DATE DESC
               </otherwise>
           </choose>
           ) as RowNum

          <include refid="getNonApprovedDeviceListQuery" />
        ) as SubQuery
        WHERE RowNum > ${safe_startPos} and RowNum &lt;= ${safe_rownumLimit}
        ORDER BY RowNum 
    </select>

    <select id="getNonApprovedPremiumOnlyDeviceListLimit" resultType="map">
        SELECT DEVICE_NAME, DEVICE_ID, DEVICE_TYPE, DEVICE_TYPE_VERSION, DEVICE_MODEL_NAME,
        IP_ADDRESS, DEVICE_MODEL_CODE, CREATE_DATE
        <include refid="getNonApprovedPremiumOnlyDeviceListLimitCommon"/>
        <if test="condition.sort != null and condition.sort != '' and condition.dir != null and condition.dir != ''">
            <bind name="safe_sortUpper" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
            <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.dir)" />
            ORDER BY ${safe_sortUpper} ${safe_sortOrder}
        </if>
        LIMIT #{pageSize} OFFSET #{startPos}
    </select>
    
    <select id="getNonApprovedPremiumOnlyDeviceListLimit" resultType="map" databaseId="mssql">
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
        SELECT * FROM (
          SELECT DEVICE_NAME, DEVICE_ID, DEVICE_TYPE, DEVICE_TYPE_VERSION, DEVICE_MODEL_NAME, IP_ADDRESS, DEVICE_MODEL_CODE, CREATE_DATE,
                 ROW_NUMBER() OVER(ORDER BY
           <choose>
               <when test="condition.sort != null and condition.sort != '' and condition.dir != null and condition.dir != ''">
                   <bind name="safe_sortUpper" value="_parameter.condition.sort.toUpperCase()"/>                   
                   <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.dir)" />
                   ${safe_sortUpper} ${safe_sortOrder}
               </when>
               <otherwise>
                   CREATE_DATE DESC
               </otherwise>
           </choose>
           ) as RowNum

          <include refid="getNonApprovedPremiumOnlyDeviceListLimitCommon" />
        ) as SubQuery
        WHERE RowNum > ${safe_startPos} and RowNum &lt;= ${safe_rownumLimit}
        ORDER BY RowNum
    </select>

    <select id="getNonApprovedPremiumOnlyDeviceListLimitCnt" resultType="int">
        SELECT COUNT(DEVICE_ID)
        <include refid="getNonApprovedPremiumOnlyDeviceListLimitCommon"/>
    </select>

    <sql id="getNonApprovedPremiumOnlyDeviceListLimitCommon">
        FROM MI_DMS_INFO_DEVICE
        WHERE IS_APPROVED = <include refid="utils.false"/> AND DEVICE_MODEL_CODE != '7000' AND DEVICE_MODEL_CODE != '7002'
        <if test="condition.src_name != null and condition.src_name != ''">
            <bind name="srcUpper" value="_parameter.condition.src_name.toUpperCase()"/>
            <bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
            AND ((UPPER(DEVICE_NAME) LIKE #{srcNamePattern} ESCAPE '^') OR (UPPER(MAC_ADDRESS) LIKE #{srcNamePattern} ESCAPE '^') OR (UPPER(DEVICE_MODEL_NAME) LIKE #{srcNamePattern} ESCAPE '^') OR (UPPER(IP_ADDRESS) LIKE #{srcNamePattern} ESCAPE '^'))
        </if>
        <if test="condition.device_id != null and condition.device_id != ''">
            <bind name="deviceIdPattern" value="'%' + condition.device_id + '%'"/>
            AND DEVICE_ID LIKE #{deviceIdPattern}
        </if>
        <if test="deviceTypeFilter != null and deviceTypeFilter.length > 0">
            AND DEVICE_TYPE IN
            <foreach item="deviceType" collection="deviceTypeFilter" open="(" separator="," close=")">
                #{deviceType}
            </foreach>
        </if>
    </sql>

    <select id="getNonApprovedPremiumOnlyDeviceListLimitOpenAPISelect" resultType="int">
        SELECT COUNT(DEVICE_ID) FROM MI_DMS_INFO_DEVICE WHERE DEVICE_MODEL_CODE = '7000' AND DEVICE_MODEL_CODE = '7002'
    </select>

    <select id="getNonApprovedPremiumOnlyDeviceListLimitOpenAPI" resultType="map">
        SELECT DEVICE_NAME, DEVICE_ID, DEVICE_TYPE, DEVICE_TYPE_VERSION, DEVICE_MODEL_NAME,
        IP_ADDRESS, DEVICE_MODEL_CODE, CREATE_DATE
        FROM MI_DMS_INFO_DEVICE
        WHERE IS_APPROVED = <include refid="utils.false"/>
        <if test="(countOfExtraDisplay != null and countOfExtraDisplay > condition.cost_license_count) or condition.cost_license_count == null">
            AND DEVICE_MODEL_CODE != '7000' AND DEVICE_MODEL_CODE = '7002'
        </if>
        <if test="condition.src_name != null and condition.src_name != ''">
            <bind name="srcUpper" value="_parameter.condition.src_name.toUpperCase()"/>
            <bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
            AND ((UPPER(DEVICE_NAME) LIKE #{srcNamePattern} ESCAPE '^') OR (UPPER(MAC_ADDRESS) LIKE #{srcNamePattern} ESCAPE '^') OR (UPPER(DEVICE_MODEL_NAME) LIKE #{srcNamePattern} ESCAPE '^') OR (UPPER(IP_ADDRESS) LIKE #{srcNamePattern} ESCAPE '^'))
        </if>
        <if test="condition.device_id != null and condition.device_id != ''">
            <bind name="deviceIdPattern" value="'%' + condition.device_id + '%'"/>
            AND DEVICE_ID LIKE #{deviceIdPattern}
        </if>
        <if test="deviceTypeFilter != null and deviceTypeFilter.length > 0">
        	AND
            <foreach item="deviceType" collection="deviceTypeFilter" open="(" separator=" or " close=")">
                <choose>
                	<when test="deviceType == constants.TYPE_SOC2">
                		DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_2_0} AND DEVICE_TYPE = #{constants.TYPE_SOC}
                	</when>
                	<when test="deviceType == constants.TYPE_SOC3">
                		DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_3_0} AND DEVICE_TYPE = #{constants.TYPE_SOC}
                	</when>
                	<when test="deviceType == constants.TYPE_SOC4">
                		DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_4_0} AND DEVICE_TYPE = #{constants.TYPE_SOC}
                	</when>
                	<when test="deviceType == constants.TYPE_SOC5">
                		DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_5_0} AND DEVICE_TYPE = #{constants.TYPE_SOC}
                	</when>
                	<when test="deviceType == constants.TYPE_SOC6">
                		DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_6_0} AND DEVICE_TYPE = #{constants.TYPE_SOC}
                	</when>
                	<when test="deviceType == constants.TYPE_SOC7">
                		DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_7_0} AND DEVICE_TYPE = #{constants.TYPE_SOC}
                	</when>
					<when test="deviceType == constants.TYPE_SOC9">
                		DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_9_0} AND DEVICE_TYPE = #{constants.TYPE_SOC}
                	</when>
                	<when test="deviceType == constants.TYPE_SOC10">
                		DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_10_0} AND DEVICE_TYPE = #{constants.TYPE_SOC}
                	</when>
                	<otherwise>
						DEVICE_TYPE = #{deviceType}
                	</otherwise>
                </choose>
            </foreach>
        </if>
        LIMIT #{pageSize} OFFSET #{startPos}        
    </select>

    <select id="getNonApprovedPremiumOnlyDeviceListLimitOpenAPI" resultType="map" databaseId="mssql">        
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
        
        SELECT * FROM (
        SELECT DEVICE_NAME, DEVICE_ID, DEVICE_TYPE, DEVICE_TYPE_VERSION, DEVICE_MODEL_NAME,
        IP_ADDRESS, DEVICE_MODEL_CODE, CREATE_DATE, ROW_NUMBER() OVER (
          ORDER BY
           <choose>
               <when test="condition.sort != null and condition.sort != '' and condition.dir != null and condition.dir != ''">
                   <bind name="safe_sortUpper" value="_parameter.condition.sort.toUpperCase()"/>                   
                   <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.dir)" />
                   ${safe_sortUpper} ${safe_sortOrder}
               </when>
               <otherwise>
                   CREATE_DATE DESC
               </otherwise>
           </choose> ) as RowNum
        FROM MI_DMS_INFO_DEVICE
        WHERE IS_APPROVED = <include refid="utils.false"/>
        <if test="(countOfExtraDisplay != null and countOfExtraDisplay > condition.cost_license_count) or condition.cost_license_count == null">
            AND DEVICE_MODEL_CODE != '7000' AND DEVICE_MODEL_CODE = '7002'
        </if>
        <if test="condition.src_name != null and condition.src_name != ''">
            <bind name="srcUpper" value="_parameter.condition.src_name.toUpperCase()"/>
            <bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
            AND ((UPPER(DEVICE_NAME) 
            LIKE #{srcNamePattern} 
            ESCAPE '^') OR (UPPER(MAC_ADDRESS) 
            LIKE #{srcNamePattern} 
            ESCAPE '^') OR (UPPER(DEVICE_MODEL_NAME) 
            LIKE #{srcNamePattern} 
            ESCAPE '^') OR (UPPER(IP_ADDRESS) 
            LIKE #{srcNamePattern} ESCAPE '^'))
        </if>
        <if test="condition.device_id != null and condition.device_id != ''">
            <bind name="deviceIdPattern" value="'%' + condition.device_id + '%'"/>
            AND DEVICE_ID LIKE #{deviceIdPattern}
        </if>
        <if test="deviceTypeFilter != null and deviceTypeFilter.length > 0">
        	AND
            <foreach item="deviceType" collection="deviceTypeFilter" open="(" separator=" or " close=")">
                <choose>
                	<when test="deviceType == constants.TYPE_SOC2">
                		DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_2_0} AND DEVICE_TYPE = #{constants.TYPE_SOC}
                	</when>
                	<otherwise>
						DEVICE_TYPE = #{deviceType}                	
                	</otherwise>
                </choose>
            </foreach>
        </if>
        ) as SubQuery
        WHERE RowNum > ${safe_startPos} and RowNum &lt;= ${safe_rownumLimit}
        ORDER BY RowNum        
    </select>
    
    <select id="getNonApprovedPremiumOnlyDeviceListLimitOpenAPIForMega" resultType="map">
        SELECT DEVICE_NAME, DEVICE_ID, DEVICE_TYPE, DEVICE_TYPE_VERSION, DEVICE_MODEL_NAME, APPLICATION_VERSION,SERIAL_DECIMAL,
        SCREEN_ROTATION, IP_ADDRESS, DEVICE_MODEL_CODE, CREATE_DATE
        FROM MI_DMS_INFO_DEVICE
        WHERE IS_APPROVED = <include refid="utils.false"/>
        <if test="(countOfExtraDisplay != null and countOfExtraDisplay > condition.cost_license_count) or condition.cost_license_count == null">
            AND DEVICE_MODEL_CODE != '7000' AND DEVICE_MODEL_CODE = '7002'
        </if>
        <if test="condition.src_name != null and condition.src_name != ''">
            <bind name="srcUpper" value="_parameter.condition.src_name.toUpperCase()"/>
            <bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
            AND ((UPPER(DEVICE_NAME) LIKE #{srcNamePattern} ESCAPE '^') OR (UPPER(MAC_ADDRESS) LIKE #{srcNamePattern} ESCAPE '^') OR (UPPER(DEVICE_MODEL_NAME) LIKE #{srcNamePattern} ESCAPE '^') OR (UPPER(IP_ADDRESS) LIKE #{srcNamePattern} ESCAPE '^'))
        </if>
        <if test="condition.device_id != null and condition.device_id != ''">
            <bind name="deviceIdPattern" value="'%' + condition.device_id + '%'"/>
            AND DEVICE_ID LIKE #{deviceIdPattern}
        </if>
        AND APPLICATION_VERSION IS NOT NULL AND SCREEN_ROTATION IS NOT NULL AND SERIAL_DECIMAL IS NOT NULL
        LIMIT #{pageSize} OFFSET #{startPos}        
    </select>

    <select id="getAppVersionList" resultType="map">
        SELECT DISTINCT APPLICATION_VERSION AS VERSION
        FROM MI_DMS_INFO_DEVICE
        ORDER BY APPLICATION_VERSION
    </select>

    <select id="getRuleVersionList" resultType="map">
        SELECT DISTINCT RULE_VERSION AS RULE_VERSION
        FROM MI_DMS_INFO_DEVICE
        ORDER BY RULE_VERSION
    </select>

    <update id="moveDeviceUpdate">
        UPDATE MI_DMS_MAP_GROUP_DEVICE SET GROUP_ID = #{newParentGroupId} WHERE GROUP_ID = #{currentParentGroupId}
        AND DEVICE_ID = #{deviceId}
    </update>

    <insert id="moveDeviceInsert">
        INSERT INTO MI_DMS_MAP_GROUP_DEVICE (GROUP_ID, DEVICE_ID) VALUES (#{newParentGroupId}, #{deviceId})
    </insert>

    <select id="moveDeviceSelect" resultType="map">
        SELECT MAP.*
        FROM MI_DMS_MAP_GROUP_DEVICE MAP, MI_DMS_INFO_GROUP GROUPINFO
        WHERE MAP.DEVICE_ID = #{deviceId}
        AND MAP.GROUP_ID = GROUPINFO.GROUP_ID
    </select>

    <select id="selAllApprovedDevice" resultType="map">
        SELECT A.DEVICE_NAME, A.DEVICE_ID, B.GROUP_NAME, A.IP_ADDRESS, A.DEVICE_MODEL_NAME, A.LOCATION, A.CREATE_DATE,
        A.CREATOR_ID, A.IS_APPROVED
        FROM MI_DMS_INFO_DEVICE A, MI_DMS_INFO_GROUP B, MI_DMS_MAP_GROUP_DEVICE C
        WHERE A.DEVICE_ID = C.DEVICE_ID AND C.GROUP_ID = B.GROUP_ID AND A.IS_APPROVED = <include refid="utils.true"/>
    </select>
    
   	<update id="setKeepaliveChangedStatus">
    	UPDATE MI_DMS_INFO_DEVICE SET IS_KEEPALIVE_CHANGED = #{keepaliveStatus} WHERE DEVICE_ID = #{deviceId}
    </update>
    
    <update id="initKeepaliveChangedStatus">
    	UPDATE MI_DMS_INFO_DEVICE SET IS_KEEPALIVE_CHANGED = <include refid="utils.false"/>
    </update>

    <update id="setDevice">
        UPDATE MI_DMS_INFO_DEVICE SET
        DEVICE_MODEL_CODE = #{device.device_model_code},
        DEVICE_MODEL_NAME = #{device.device_model_name}, DEVICE_NAME = #{device.device_name},
        SERIAL_DECIMAL = #{device.serial_decimal}, SCREEN_SIZE = #{device.screen_size},
        FIRMWARE_VERSION = #{device.firmware_version}, APPLICATION_VERSION = #{device.application_version},
        RULE_VERSION = #{device.rule_version}, OS_IMAGE_VERSION = #{device.os_image_version},
        CPU_TYPE = #{device.cpu_type}, HDD_SIZE = #{device.hdd_size},
        MEM_SIZE = #{device.mem_size}, VIDEO_ADAPTER = #{device.video_adapter},
        VIDEO_MEMORY = #{device.video_memory}, VIDEO_DRIVER = #{device.video_driver},
        NETWORK_ADAPTER = #{device.network_adapter}, NETWORK_DRIVER = #{device.network_driver},
        MAC_ADDRESS = #{device.mac_address}, IP_SETTING_TYPE = #{device.ip_setting_type},
        IP_ADDRESS = #{device.ip_address}, SUBNET_MASK = #{device.subnet_mask},
        GATEWAY = #{device.gateway}, DNS_SERVER_MAIN = #{device.dns_server_main},
        DNS_SERVER_SUB = #{device.dns_server_sub}, PORT = #{device.port},
        TRIGGER_INTERVAL = #{device.trigger_interval},
        MONITORING_INTERVAL = #{device.monitoring_interval}, LOCATION = #{device.location},
        IS_APPROVED = #{device.is_approved}, TUNNELING_SERVER = #{device.tunneling_server},
        BG_COLOR = #{device.bg_color}, AUTO_TIME_SETTING = #{device.auto_time_setting},
        ON_TIMER_SETTING = #{device.on_timer_setting},
        OFF_TIMER_SETTING = #{device.off_timer_setting},
        TIME_ZONE_INDEX = #{device.time_zone_index},
        DAY_LIGHT_SAVING = #{device.day_light_saving},
        FTP_CONNECT_MODE = #{device.ftp_connect_mode},
        REPOSITORY_PATH = #{device.repository_path},
        MAGICINFO_SERVER_URL = #{device.magicinfo_server_url},
        SCREEN_CAPTURE_INTERVAL = #{device.screen_capture_interval}, EWF_STATE = #{device.ewf_state},
        RESOLUTION = #{device.resolution}, DEVICE_TYPE = #{device.device_type},
        IS_REVERSE = #{device.is_reverse}, PROXY_SETTING = #{device.proxy_setting}, PROXY_EXCLUDE_LIST = #{device.proxy_exclude_list},
        CONNECTION_LIMIT_TIME = #{device.connection_limit_time},
        MNT_FOLDER_PATH = #{device.mnt_folder_path},
        SYSTEM_RESTART_INTERVAL = #{device.system_restart_interval},
        LOG_MNT = #{device.log_mnt}, PROOF_OF_PLAY_MNT = #{device.proof_of_play_mnt},
        CONTENT_MNT = #{device.content_mnt}, SCREEN_ROTATION = #{device.screen_rotation},
        PLAY_MODE = #{device.play_mode}, RESET_PASSWORD = #{device.reset_password},
        TIME_ZONE_VERSION = #{device.time_zone_version}, DISK_SPACE_REPOSITORY = #{device.disk_space_repository},
        CREATOR_ID = #{device.creator_id}, CREATE_DATE = #{device.create_date},
        <if test="device.cabinet_group_layout != null">
            CABINET_GROUP_LAYOUT = #{device.cabinet_group_layout},
        </if>
        <if test="device.pin_code != null">
        	PIN_CODE = #{device.pin_code},
        </if>
        <if test="device.has_child != null">
            HAS_CHILD = #{device.has_child},
        </if>
        <if test="device.protocol_priority != null">
        	PROTOCOL_PRIORITY = #{device.protocol_priority},
        </if>
        <if test="device.rm_rule_version != null">
        	RM_RULE_VERSION = #{device.rm_rule_version},
        </if>
		<if test="device.smart_download != null">
        	SMART_DOWNLOAD = #{device.smart_download},
        </if>
        <if test="device.third_application_update_domain != null">
            THIRD_APPLICATION_UPDATE_DOMAIN = #{device.third_application_update_domain},
        </if>
        CHILD_CNT = #{device.child_cnt}, IS_CHILD = #{device.is_child}, CONN_CHILD_CNT = #{device.conn_child_cnt}
        WHERE DEVICE_ID = #{device.device_id}
    </update>

    <update id="setDeviceGroupId">
        UPDATE MI_DMS_MAP_GROUP_DEVICE
        SET GROUP_ID = #{map.group_id}, ORGANIZATION = #{map.organization}
        WHERE DEVICE_ID = #{map.device_id}
    </update>
    
    <update id="setChildDeviceGroupId">
        UPDATE MI_DMS_MAP_GROUP_DEVICE
        SET GROUP_ID = #{map.group_id}, ORGANIZATION = #{map.organization}
        <bind name="deviceIdPattern" value="map.device_id + '\\_%'"/>
		WHERE DEVICE_ID LIKE #{deviceIdPattern}
    </update>
    
    <update id="setChildDeviceGroupId" databaseId="mssql">
        UPDATE MI_DMS_MAP_GROUP_DEVICE
        SET GROUP_ID = #{map.group_id}, ORGANIZATION = #{map.organization}
        <bind name="deviceIdPattern" value="map.device_id + '[_]%'"/>
		WHERE DEVICE_ID LIKE #{deviceIdPattern}
    </update>

    <update id="setDeviceOperationInfo">
        UPDATE MI_DMS_INFO_DEVICE SET
        DEVICE_NAME = #{device.device_name}, DEVICE_MODEL_CODE = #{device.device_model_code}, DEVICE_MODEL_NAME = #{device.device_model_name},
        MAC_ADDRESS = #{device.mac_address}, PORT = #{device.port}, CPU_TYPE = #{device.cpu_type}, MEM_SIZE = #{device.mem_size}, HDD_SIZE = #{device.hdd_size},
        TUNNELING_SERVER = #{device.tunneling_server}, APPLICATION_VERSION = #{device.application_version}, DEVICE_TYPE = #{device.device_type}, 
        DEVICE_TYPE_VERSION = #{device.device_type_version}, PLAYER_VERSION = #{device.player_version},
        <if test="device.ip_address != null">
        	IP_ADDRESS = #{device.ip_address}, 
        </if>
        <if test="device.subnet_mask != null">
        	SUBNET_MASK = #{device.subnet_mask},
        </if>
        <if test="device.has_child != null">
            HAS_CHILD = #{device.has_child},
        </if>
        DISK_SPACE_REPOSITORY = #{device.disk_space_repository}, IS_OVERWRITE_DEVICE_NAME = #{device.is_overwrite_device_name},
        ERROR_FLAG = #{device.error_flag},
        BOOTSTRAP_TIME = #{device.bootstrap_time}
        WHERE DEVICE_ID = #{device.device_id}
    </update>

    <update id="setIsApproved">
        UPDATE MI_DMS_INFO_DEVICE
        SET IS_APPROVED = #{map.is_approved} WHERE DEVICE_ID = #{map.device_id}
    </update>

    <update id="setNameDeviceAndModel">
        UPDATE MI_DMS_INFO_DEVICE
        SET LOCATION = #{map.location},
        IS_APPROVED = #{map.is_approved} ,CREATE_DATE = CURRENT_TIMESTAMP
        <if test="map.device_name != null and !map.device_name.equals('')">
            ,DEVICE_NAME = #{map.device_name}
        </if>
        <if test="map.device_model_name != null and !map.device_model_name.equals('')">
            ,DEVICE_MODEL_NAME = #{map.device_model_name}
        </if>
        <if test="map.calDate != null">
        	<choose>
              <when test="!map.calDate.equals('')">
                  , EXPIRATION_DATE = #{calTimestamp}
              </when>
              <otherwise>
                  , EXPIRATION_DATE = NULL
              </otherwise>
          </choose>		
        </if>
        <if test="map.is_overwrite_device_name != null and !map.is_overwrite_device_name.equals('')">
        	, IS_OVERWRITE_DEVICE_NAME = #{map.is_overwrite_device_name}
        </if>
        WHERE DEVICE_ID = #{map.device_id}
    </update>

    <select id="getConnectedDeviceLiteModelNameList" resultType="map">
        SELECT DISTINCT DEVICE_MODEL_NAME
        FROM MI_DMS_INFO_DEVICE
        ORDER BY DEVICE_MODEL_NAME ASC
    </select>

    <select id="getConnectedDeviceModelNameList" resultType="map">
        SELECT DISTINCT DEVICE_MODEL_NAME
        FROM MI_DMS_INFO_DEVICE
        ORDER BY DEVICE_MODEL_NAME ASC
    </select>

    <select id="getDeviceModelNameList" resultType="map">
        SELECT DEVICE_MODEL_NAME
        FROM MI_DMS_INFO_DEVICE_MODEL
        ORDER BY DEVICE_MODEL_NAME ASC
    </select>

    <select id="getDeviceLiteModelNameList" resultType="map">
        SELECT DEVICE_MODEL_NAME
        FROM MI_DMS_INFO_DEVICE_MODEL
        WHERE DEVICE_MODEL_CODE='68'
        ORDER BY DEVICE_MODEL_NAME ASC
    </select>

    <select id="getDeviceModelTypeList" resultType="map">
        SELECT DISTINCT DEVICE_MODEL_TYPE, DEVICE_MODEL_CODE
        FROM MI_DMS_INFO_DEVICE_MODEL
        ORDER BY DEVICE_MODEL_TYPE ASC
    </select>

    <update id="setDevicePostBootstrap">
        UPDATE MI_DMS_INFO_DEVICE SET
        SERIAL_DECIMAL = #{device.serial_decimal}, SCREEN_SIZE = #{device.screen_size}, RESOLUTION = #{device.resolution},
        FIRMWARE_VERSION = #{device.firmware_version}, FIRMWARE_INDICATORS = #{device.firmware_indicators},
        RULE_VERSION = #{device.rule_version}, OS_IMAGE_VERSION = #{device.os_image_version},
        VIDEO_ADAPTER = #{device.video_adapter}, VIDEO_MEMORY = #{device.video_memory}, VIDEO_DRIVER = #{device.video_driver},
        NETWORK_ADAPTER = #{device.network_adapter}, NETWORK_DRIVER = #{device.network_driver}, EWF_STATE = #{device.ewf_state},
        IP_SETTING_TYPE = #{device.ip_setting_type}, IP_ADDRESS = #{device.ip_address}, SUBNET_MASK = #{device.subnet_mask}, GATEWAY = #{device.gateway},
        DNS_SERVER_MAIN = #{device.dns_server_main}, DNS_SERVER_SUB = #{device.dns_server_sub},
        PORT = #{device.port}, TIME_ZONE_INDEX = #{device.time_zone_index},
        DAY_LIGHT_SAVING = #{device.day_light_saving}, DAY_LIGHT_SAVING_MANUAL = #{device.day_light_saving_manual},
        AUTO_TIME_SETTING = #{device.auto_time_setting}, ON_TIMER_SETTING = #{device.on_timer_setting},
        OFF_TIMER_SETTING = #{device.off_timer_setting}, MAGICINFO_SERVER_URL = #{device.magicinfo_server_url}, IS_REVERSE = #{device.is_reverse},
        TUNNELING_SERVER = #{device.tunneling_server}, TRIGGER_INTERVAL = #{device.trigger_interval},
        MONITORING_INTERVAL = #{device.monitoring_interval}, FTP_CONNECT_MODE = #{device.ftp_connect_mode},
        REPOSITORY_PATH = #{device.repository_path},
        SCREEN_CAPTURE_INTERVAL = #{device.screen_capture_interval}, BG_COLOR = #{device.bg_color}, 
        PROXY_SETTING = #{device.proxy_setting}, PROXY_EXCLUDE_LIST = #{device.proxy_exclude_list}, PROXY_SETTING_AUTHORIZATION = #{device.proxy_setting_authorization},
        CONNECTION_LIMIT_TIME = #{device.connection_limit_time}, MNT_FOLDER_PATH = #{device.mnt_folder_path},
        CONTENTS_PROGRESS_ENABLE = #{device.contents_progress_enable}, CONTENTS_PROGRESS_UNIT = #{device.contents_progress_unit}, CONTENTS_PROGRESS_INTERVAL = #{device.contents_progress_interval},
        CONTENTS_DOWNLOAD_MODE = #{device.contents_download_mode},
        FILEDATA_DEL_SIZE = #{device.filedata_del_size}, CONTENT_READY_INTERVAL = #{device.content_ready_interval}, PLAYER_START_TIMEOUT = #{device.player_start_timeout},
        SYSTEM_RESTART_INTERVAL = #{device.system_restart_interval},
        LOG_MNT = #{device.log_mnt}, PROOF_OF_PLAY_MNT = #{device.proof_of_play_mnt}, CONTENT_MNT = #{device.content_mnt}, SCREEN_ROTATION = #{device.screen_rotation},
        PLAY_MODE = #{device.play_mode}, RESET_PASSWORD = #{device.reset_password}, TIME_ZONE_VERSION = #{device.time_zone_version},
        WEBCAM = #{device.webcam}, AMS_PLAY_MODE= #{device.ams_play_mode}, PLAYER_RESOLUTION = #{device.player_resolution},
        <if test="device.cabinet_group_layout != null">
            CABINET_GROUP_LAYOUT = #{device.cabinet_group_layout},
        </if>
		<if test="device.pin_code != null">
        	PIN_CODE = #{device.pin_code},
        </if>
        <if test="device.has_child != null">
            HAS_CHILD = #{device.has_child},    
        </if>
        <if test="device.protocol_priority != null">
        	PROTOCOL_PRIORITY = #{device.protocol_priority},
        </if>
        <if test="device.rm_data_setting != null">
        	RM_DATA_SETTING = #{device.rm_data_setting},
        </if>
        <if test="device.child_monitoring_interval != null">
            CHILD_MONITORING_INTERVAL = #{device.child_monitoring_interval},    
        </if>             
        PRE_CONFIG_VERSION = #{device.pre_config_version}, RM_RULE_VERSION = #{device.rm_rule_version},
        DEVICE_SERIES = #{device.device_series}, DEVICE_HW_PLATFORM = #{device.device_hw_platform},
        MDC_UPDATE_TIME = CURRENT_TIMESTAMP,
        LAST_CONNECTION_TIME = CURRENT_TIMESTAMP,
        SWITCH_TIME = #{device.switch_time},
        BANDWIDTH = #{device.bandwidth},
        SUPPORT_FLAG = #{device.support_flag},
        SOFTWARE_UPDATE_VERSION = #{device.software_update_version},
        URL_LAUNCHER = #{device.url_launcher},
        <if test="device.third_application_version != null">
            THIRD_APPLICATION_VERSION = #{device.third_application_version},
        </if>
        <if test="device.third_application_last_updated != null">
            THIRD_APPLICATION_LAST_UPDATED = #{device.third_application_last_updated},
        </if>
        <if test="device.third_application_log_size != null">
            THIRD_APPLICATION_LOG_SIZE = #{device.third_application_log_size},
        </if>
        <if test="device.third_application_update_domain != null">
            THIRD_APPLICATION_UPDATE_DOMAIN = #{device.third_application_update_domain},
        </if>
        SMART_DOWNLOAD = #{device.smart_download}

        WHERE DEVICE_ID = #{device.device_id}
    </update>

    <update id="setDeviceNameAndLocation">
        UPDATE MI_DMS_INFO_DEVICE SET
        <bind name="cnt" value="0"/>
        <if test="deviceName != null and !deviceName.trim().equals('')">
            DEVICE_NAME = #{deviceName}
            <bind name="cnt" value="1"/>
        </if>
        <if test="location != null">
            <if test="cnt > 0">,</if>
            LOCATION = #{location}
            <bind name="cnt" value="2"/>
        </if>
        <if test="mapLocation != null">
            <if test="cnt > 0">,</if>
            MAP_LOCATION = #{mapLocation}
        </if>
        <if test="deviceModelName != null and !deviceModelName.trim().equals('')">
            <if test="cnt > 0">,</if>
            DEVICE_MODEL_NAME = #{deviceModelName}
        </if>
        WHERE DEVICE_ID = #{deviceId}
    </update>

    <update id="setLastConnectionTime">
        UPDATE MI_DMS_INFO_DEVICE
        SET LAST_CONNECTION_TIME = CURRENT_TIMESTAMP
        WHERE DEVICE_ID = #{deviceId}
    </update>

    <update id="setShutDownConnectionTime">
        UPDATE MI_DMS_INFO_DEVICE
        SET LAST_CONNECTION_TIME = #{currTime}
        WHERE DEVICE_ID = #{deviceId}
    </update>

    <select id="getFirmwareVersionList" resultType="map">
        SELECT DISTINCT FIRMWARE_VERSION FROM MI_DMS_INFO_DEVICE
    </select>

    <select id="getOSImageVersionList" resultType="map">
        SELECT DISTINCT OS_IMAGE_VERSION FROM MI_DMS_INFO_DEVICE
    </select>

    <select id="getDeviceResolutionList" resultType="map">
        SELECT * FROM MI_DMS_INFO_RESOLUTION WHERE DEVICE_TYPE = #{deviceType} ORDER BY IS_DEFAULT DESC
    </select>

    <delete id="deleteBindingDevice">
        DELETE FROM MI_DMS_INFO_BINDING_DEVICE
        WHERE IP_ADDRESS = #{ipAddress}
    </delete>

    <select id="addBindingDeviceSelect" resultType="java.lang.Long">
        SELECT COUNT(DEVICE_ID) FROM MI_DMS_INFO_DEVICE
        WHERE DEVICE_ID = #{map.device_id}
        AND IP_ADDRESS = #{map.ip_address}
    </select>

    <insert id="addBindingDeviceInsert">
        INSERT INTO MI_DMS_INFO_BINDING_DEVICE
        (DEVICE_ID, DEVICE_NAME, APPLICATION_VERSION, IP_ADDRESS, MAGICINFO_SERVER_URL)
        VALUES
        (#{map.device_id},#{map.device_name},#{map.application_version},#{map.ip_address},#{map.magicinfo_server_url})
    </insert>

    <update id="setOrganizationByDeviceId">
        UPDATE MI_DMS_MAP_GROUP_DEVICE
        SET ORGANIZATION = #{map.organization}
        WHERE DEVICE_ID = #{map.device_id}
    </update>

    <select id="getBindingDeviceList" resultType="map">
        SELECT DEVICE_ID, DEVICE_NAME, APPLICATION_VERSION, IP_ADDRESS, MAGICINFO_SERVER_URL
        FROM MI_DMS_INFO_BINDING_DEVICE
        <if test="deviceName != null">
            <bind name="deviceNamePattern" value="'%' + deviceName + '%'"/>
            WHERE DEVICE_NAME LIKE #{deviceNamePattern}
        </if>
        ORDER BY DEVICE_NAME ASC
    </select>

    <select id="getBindingDeviceListPage" resultType="map">
        SELECT DEVICE_ID, DEVICE_NAME, APPLICATION_VERSION, IP_ADDRESS, MAGICINFO_SERVER_URL
        <include refid="getBindingDeviceListPageQuery" />
        ORDER BY DEVICE_NAME ASC
        LIMIT #{pageSize} OFFSET #{startPos}
    </select>
    
    <sql id="getBindingDeviceListPageQuery">
        FROM MI_DMS_INFO_BINDING_DEVICE
        <if test="deviceName != null">
            <bind name="deviceNamePattern" value="'%' + deviceName + '%'"/>
            WHERE DEVICE_NAME LIKE #{deviceNamePattern}
        </if>
    </sql>
    
    <select id="getBindingDeviceListPage" resultType="map" databaseId="mssql">
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
        SELECT * FROM (
          SELECT DEVICE_ID, DEVICE_NAME, APPLICATION_VERSION, IP_ADDRESS, MAGICINFO_SERVER_URL,
                 ROW_NUMBER() OVER(ORDER BY DEVICE_NAME ASC ) as RowNum

          <include refid="getBindingDeviceListPageQuery" />
        ) as SubQuery
        WHERE RowNum > ${safe_startPos} and RowNum &lt;= ${safe_rownumLimit}
        ORDER BY RowNum
    </select>

    <select id="getBindingDeviceListCnt" resultType="int">
        SELECT COUNT(DEVICE_ID) FROM MI_DMS_INFO_BINDING_DEVICE
        <if test="deviceName != null">
            <bind name="deviceNamePattern" value="'%' + deviceName + '%'"/>
            WHERE DEVICE_NAME LIKE #{deviceNamePattern}
        </if>
    </select>

    <select id="getAllDeviceCount" resultType="int">
        SELECT COUNT(DEVICE_ID) FROM MI_DMS_INFO_DEVICE       
        <if test="deviceType != null and !deviceType.equals('')">
			WHERE <include refid="deviceTypeChooseQueryNonConstants" />
        </if>
    </select>

    <select id="getAllDeviceCountByOrganization" resultType="int">
        SELECT COUNT(A.DEVICE_ID) FROM MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B
        <if test="deviceType != null and !deviceType.equals('')">
         	WHERE A.DEVICE_ID = B.DEVICE_ID AND B.ORGANIZATION = #{organization}
			AND <include refid="deviceTypeChooseQueryNonConstants"></include>
        </if>
    </select>
    
    <select id="getApprovalDeviceCount" resultType="int">
        SELECT COUNT(DEVICE_ID) FROM MI_DMS_INFO_DEVICE WHERE IS_APPROVED = <include refid="utils.true"/>
        <if test="deviceType != null">
            AND <include refid="deviceTypeChooseQueryNonConstants"></include>
        </if>
    </select>

    <select id="getApprovalPremiumDeviceCount" resultType="int">
        SELECT COUNT(DEVICE_ID) FROM MI_DMS_INFO_DEVICE WHERE IS_APPROVED = <include refid="utils.true"/> AND DEVICE_MODEL_CODE != '7000' AND DEVICE_MODEL_CODE != '7002'
    </select>

    <select id="getApprovalExtraDisplayDeviceCount" resultType="int">
        SELECT COUNT(DEVICE_ID) FROM MI_DMS_INFO_DEVICE WHERE IS_APPROVED = <include refid="utils.true"/> AND DEVICE_MODEL_CODE = '7000'
    </select>

    <select id="addDeviceBindingInfoSelect" resultType="java.lang.Long">
        SELECT COUNT(DEVICE_ID) FROM MI_DMS_INFO_DEVICE WHERE DEVICE_ID = #{device.device_id} AND IP_ADDRESS =
        #{device.ip_address}
    </select>

    <insert id="addDeviceBindingInfoInsert">
        INSERT INTO MI_DMS_INFO_BINDING_DEVICE (DEVICE_ID, DEVICE_NAME, APPLICATION_VERSION, IP_ADDRESS,
        MAGICINFO_SERVER_URL)
        VALUES (#{device.device_id}, #{device.device_name}, #{device.application_version}, #{device.ip_address},
        #{device.magicinfo_server_url})
    </insert>

    <delete id="deleteDeviceBindingInfoByDeviceId">
        DELETE FROM MI_DMS_INFO_BINDING_DEVICE WHERE DEVICE_ID = #{deviceId}
    </delete>

    <delete id="deleteDeviceBindingInfo">
        DELETE FROM MI_DMS_INFO_BINDING_DEVICE
    </delete>

    <select id="getDeviceModelCodeByDeviceId" resultType="String">
        SELECT DEVICE_MODEL_CODE FROM MI_DMS_INFO_DEVICE WHERE DEVICE_ID = #{deviceId}
    </select>

    <select id="getDeviceGroupIdByDeviceId" resultType="String">
        SELECT GROUP_ID FROM MI_DMS_MAP_GROUP_DEVICE WHERE DEVICE_ID = #{deviceId}
    </select>

    <select id="getDeviceIdGroupIdByDeviceName" resultType="map">
        SELECT A.DEVICE_ID, B.GROUP_ID
        FROM MI_DMS_INFO_DEVICE A JOIN MI_DMS_MAP_GROUP_DEVICE B ON A.DEVICE_ID = B.DEVICE_ID
        WHERE A.DEVICE_NAME = #{deviceName}
    </select>

    <update id="setDeviceModel">
        UPDATE MI_DMS_INFO_DEVICE_MODEL SET
        DEVICE_MODEL_CODE = #{deviceModel.device_model_code}, VENDOR = #{deviceModel.vendor},
        DESCRIPTION = #{deviceModel.description} , CREATOR_ID = #{deviceModel.creator_id}, CREATE_DATE = #{deviceModel.create_date}
        WHERE DEVICE_MODEL_NAME = #{deviceModel.device_model_name}
    </update>

    <update id="updateDiskSpaceChannel">
        UPDATE MI_DMS_INFO_DEVICE SET
        DISK_SPACE_REPOSITORY = #{diskSpace},
        DIRECT_CHANNEL = #{channel}
        WHERE DEVICE_ID = #{deviceId}
    </update>

    <select id="getProgramIdByDeviceId" resultType="String">
        SELECT PROGRAM_ID FROM MI_CDS_MAP_PROGRAM_DEVICE AS A, MI_DMS_MAP_GROUP_DEVICE AS B
        WHERE A.DEVICE_GROUP_ID = B.GROUP_ID AND DEVICE_ID = #{deviceId}
    </select>

    <select id="getEventScheduleIdByDeviceId" resultType="String">
        SELECT SCHEDULE_ID FROM MI_EVENT_MAP_SCHEDULE_DEVICE AS A, MI_DMS_MAP_GROUP_DEVICE AS B
		WHERE A.DEVICE_GROUP_ID = B.GROUP_ID AND DEVICE_ID = #{deviceId}
    </select>



    <select id="getScheduleIdByProgramId" resultType="String">
        SELECT SCHEDULE_ID FROM MI_CDS_INFO_SCHEDULE WHERE PROGRAM_ID = #{programId}
    </select>

    <select id="getVersionByProgramId" resultType="java.lang.Long">
        SELECT VERSION FROM MI_CDS_INFO_PROGRAM WHERE PROGRAM_ID = #{programId}
    </select>

    <select id="getApprovalByDeviceId" resultType="map">
        SELECT IS_APPROVED FROM MI_DMS_INFO_DEVICE WHERE DEVICE_ID = #{deviceId}
    </select>

    <select id="getDeviceIdByDesc" resultType="map">
        SELECT DEVICE_ID FROM MI_DMS_INFO_DEVICE WHERE DEVICE_TYPE = #{deviceType} ORDER BY CREATE_DATE DESC
    </select>

    <select id="getDeviceIdByAsc" resultType="map">
        SELECT DEVICE_ID FROM MI_DMS_INFO_DEVICE WHERE DEVICE_TYPE = #{deviceType} ORDER BY CREATE_DATE ASC
    </select>

    <select id="getApprovalDeviceIdByAsc" resultType="map">
        SELECT DEVICE_ID FROM MI_DMS_INFO_DEVICE WHERE IS_APPROVED = <include refid="utils.true"/> ORDER BY CREATE_DATE ASC
    </select>

    <insert id="addDeviceDisplayInsert">
        INSERT INTO MI_DMS_INFO_DISPLAY (DEVICE_ID, BASIC_SOURCE, BASIC_PANEL_STATUS,
        MISC_REMOCON, MNT_SAFETY_LOCK, MISC_OSD, MISC_ALL_LOCK,
        DIAGNOSIS_MONITOR_TEMPERATURE, DIAGNOSIS_PANEL_ON_TIME)
        VALUES (#{deviceId}, 0, 0, 0, 0, 0, 0, 0, 0)
    </insert>

    <select id="isVwlConsole" resultType="Boolean">
        SELECT RESULT FROM MI_DMS_INFO_VWL_CONSOLE_DEVICE WHERE device_id = #{deviceId}
    </select>

    <select id="getCntDeviceByDeviceType" resultType="int">
        SELECT COUNT(DEVICE_ID) FROM MI_DMS_INFO_DEVICE WHERE DEVICE_TYPE = #{deviceType}
    </select>

    <select id="refreshDeviceGroupType" resultType="int">
        SELECT COUNT(*) FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = #{groupId}
    </select>

    <select id="getDeviceIdListByGroup" resultType="map">
        SELECT DEVICE_ID FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = #{groupId}
    </select>
    
    <select id="getNotChildDeviceIdListByGroup" resultType="map">
        SELECT A.DEVICE_ID 
        FROM MI_DMS_MAP_GROUP_DEVICE A, MI_DMS_INFO_DEVICE B
        WHERE GROUP_ID = #{groupId} AND B.IS_CHILD = <include refid="utils.false"/> AND A.DEVICE_ID = B.DEVICE_ID
    </select>

    <select id="getAppVersionListByDeviceType" resultType="map">
    	SELECT DISTINCT APPLICATION_VERSION AS VERSION
		FROM MI_DMS_INFO_DEVICE 
		WHERE 
        <choose>
            <when test="deviceType == 'SPLAYER'">
                DEVICE_TYPE = 'SPLAYER' OR DEVICE_TYPE = 'RSPLAYER' 
            </when>
            <when test="deviceType == 'iPLAYER'">
                DEVICE_TYPE = 'iPLAYER' OR DEVICE_TYPE = 'RIPLAYER' 
            </when>
            <when test="deviceType == 'LEDBOX'">
                DEVICE_TYPE = 'LEDBOX' OR DEVICE_TYPE = 'RLEDBOX' 
            </when>
            <when test="deviceType == 'SIGNAGE'">
                DEVICE_TYPE = 'SIGNAGE' OR DEVICE_TYPE = 'RSIGNAGE' 
            </when>
            <otherwise>
                DEVICE_TYPE = #{deviceType} 
             </otherwise>
         </choose>		
		ORDER BY APPLICATION_VERSION
    </select>

    <select id="getAppVersionListBy" resultType="map">
        SELECT DISTINCT APPLICATION_VERSION AS VERSION
        FROM MI_DMS_INFO_DEVICE
        WHERE
        1 = 1

        <if test="#{map.firmwareIndicators} != null">
            AND FIRMWARE_INDICATORS = #{map.firmwareIndicators}
        </if>


        <if test="#{map.deviceType} != null">
            AND

            <choose>
                <when test="map.deviceType == 'SPLAYER'">
                    DEVICE_TYPE = 'SPLAYER' OR DEVICE_TYPE = 'RSPLAYER'
                </when>
                <when test="map.deviceType == 'iPLAYER'">
                    DEVICE_TYPE = 'iPLAYER' OR DEVICE_TYPE = 'RIPLAYER'
                </when>
                <when test="map.deviceType == 'LEDBOX'">
                    DEVICE_TYPE = 'LEDBOX' OR DEVICE_TYPE = 'RLEDBOX'
                </when>
                <when test="map.deviceType == 'SIGNAGE'">
                    DEVICE_TYPE = 'SIGNAGE' OR DEVICE_TYPE = 'RSIGNAGE'
                </when>
                <otherwise>
                    DEVICE_TYPE = #{map.deviceType}
                </otherwise>
            </choose>
        </if>
        ORDER BY APPLICATION_VERSION
    </select>

    <select id="getDeviceModelNameListBy" resultType="map">
        SELECT DISTINCT DEVICE_MODEL_NAME
        FROM MI_DMS_INFO_DEVICE
        WHERE
        1 = 1

        <if test="#{map.firmwareIndicators} != null">
            AND FIRMWARE_INDICATORS = #{map.firmwareIndicators}
        </if>
    </select>


    <select id="addDeviceDisplaySelect" resultType="java.lang.Long">
        SELECT COUNT(DEVICE_ID) FROM MI_DMS_INFO_DISPLAY WHERE DEVICE_ID = #{deviceId}
    </select>

    <select id="addStatRequestTimeSelect" resultType="int">
        SELECT COUNT(DEVICE_ID) FROM MI_STATISTICS_REQUEST_TIME WHERE DEVICE_ID = #{deviceId}
    </select>

    <insert id="addStatRequestTimeInsert">
        INSERT INTO MI_STATISTICS_REQUEST_TIME (DEVICE_ID, REQUEST_TIME) VALUES (#{deviceId}, #{requestTime})
    </insert>
    
     <insert id="addStatRequestTimeInsertCurrent">
        INSERT INTO MI_STATISTICS_REQUEST_TIME (DEVICE_ID, REQUEST_TIME) VALUES (#{deviceId}, <include refid="utils.currentTimestamp"/>)
    </insert>

    <select id="getApprovalDeviceIdByDeviceTypeAsc" resultType="map">
        SELECT DEVICE_ID
        FROM MI_DMS_INFO_DEVICE
        WHERE DEVICE_TYPE = #{deviceType} AND IS_APPROVED = <include refid="utils.true"/>
        ORDER BY CREATE_DATE ASC
    </select>
    
    <select id="getApprovalDeviceIdByDeviceTypeListAsc" resultType="map">
    	SELECT DEVICE_ID
        FROM MI_DMS_INFO_DEVICE
        WHERE IS_APPROVED = <include refid="utils.true"/>
        <if test="deviceTypeList != null and deviceTypeList.size() > 0">
        	<foreach item="deviceType" collection="deviceTypeList" open=" AND (" separator=" OR " close=")">
        		DEVICE_TYPE = #{deviceType}
        	</foreach>
        </if>
        ORDER BY CREATE_DATE ASC
    </select>

    <select id="getApprovalDeviceIdByDeviceTypeListInOrgAssignedLicenseAsc" resultType="map">
        SELECT ID.DEVICE_ID
        FROM MI_DMS_INFO_DEVICE AS ID
            INNER JOIN MI_DMS_MAP_GROUP_DEVICE AS MGD ON
                ID.DEVICE_ID = MGD.DEVICE_ID
            INNER JOIN MI_DMS_INFO_GROUP AS IR ON
                IR.GROUP_NAME = MGD.ORGANIZATION
        WHERE IR.GROUP_ID IN
            (SELECT GROUP_ID FROM MI_SYSTEM_MAP_SLM_LICENSE_ORG)
            AND ID.IS_APPROVED = <include refid="utils.true"/>
            <if test="deviceTypeList != null and deviceTypeList.size() > 0">
                <foreach item="deviceType" collection="deviceTypeList" open=" AND (" separator=" OR " close=")">
                    ID.DEVICE_TYPE = #{deviceType}
                </foreach>
            </if>
        ORDER BY ID.CREATE_DATE ASC
    </select>

    <select id="getApprovalDeviceIdByDeviceTypeSocAsc" resultType="map">
        SELECT DEVICE_ID
        FROM MI_DMS_INFO_DEVICE
        WHERE
        IS_APPROVED = <include refid="utils.true"/>  
        <foreach item="deviceType" collection="types" open=" AND (" separator=" OR " close=")">
     		<include refid="deviceTypeChooseQueryNonConstants"></include>
        </foreach>
        AND IS_APPROVED = <include refid="utils.true"/>
        ORDER BY CREATE_DATE ASC
    </select>

    <select id="getDeviceMonitoringInterval" resultType="java.lang.Long">
        SELECT MONITORING_INTERVAL FROM MI_DMS_INFO_DEVICE WHERE DEVICE_ID = #{deviceId}
    </select>

    <select id="getConnectedDeviceModelNameListTypeS" resultType="map">
    	SELECT DISTINCT DEVICE_MODEL_NAME
    	FROM MI_DMS_MAP_GROUP_DEVICE GROUPS 
    	LEFT JOIN MI_DMS_INFO_DEVICE DEVICES ON GROUPS.DEVICE_ID = DEVICES.DEVICE_ID
    	WHERE 
    	<choose>
                <when test="deviceType == 'SPLAYER'">
                    (DEVICE_TYPE = 'SPLAYER' OR DEVICE_TYPE = 'RSPLAYER') 
                </when>
                <when test="deviceType == 'SIGNAGE'">
                    (DEVICE_TYPE = 'SIGNAGE' OR DEVICE_TYPE = 'RSIGNAGE') 
                </when>
                <when test="deviceType == 'LEDBOX'">
                    (DEVICE_TYPE = 'LEDBOX' OR DEVICE_TYPE = 'RLEDBOX') 
                </when>                                
                <otherwise>
                    (DEVICE_TYPE = #{deviceType})
                </otherwise>              
        </choose>
        <if test="organization != null and !organization.equals('ROOT')">
        	AND GROUPS.ORGANIZATION = #{organization}
        </if>
        ORDER BY DEVICE_MODEL_NAME ASC
    	
    </select>
    
    <select id="getIsRedundancy" resultType="map">
		SELECT A.DEVICE_ID
		FROM MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B
		WHERE A.DEVICE_ID = B.DEVICE_ID
		AND A.IS_REDUNDANCY= <include refid="utils.true"/>
		AND B.GROUP_ID = #{groupId}
    </select>
    
    <update id="addRedundancyStatus">
    	UPDATE MI_DMS_INFO_DEVICE SET 
    		IS_REDUNDANCY = #{redundanctStatus}
		WHERE DEVICE_ID = #{deviceId}
    </update>
    
    <select id="isRedundancyDevice" resultType="map">
		SELECT IS_REDUNDANCY FROM MI_DMS_INFO_DEVICE WHERE DEVICE_ID = #{deviceId}
    </select>
    
    <select id="getVwtIdByDeviceId" resultType="java.lang.String">
		SELECT VWT_ID 
		FROM MI_DMS_INFO_DEVICE 
		WHERE DEVICE_ID= #{deviceId}
    </select>
    
    <select id="getVwtFileName" resultType="map">
		SELECT VWT_FILE_NAME 
		FROM MI_DMS_INFO_VWT 
		WHERE VWT_ID = #{vwtId}
    </select>

    <update id="addDeviceTypeVersion">
    	UPDATE MI_DMS_INFO_DEVICE 
		SET DEVICE_TYPE_VERSION = #{deviceTypeVersion, javaType=java.math.BigDecimal, jdbcType=DECIMAL, typeHandler=org.apache.ibatis.type.BigDecimalTypeHandler} 
		WHERE DEVICE_ID = #{deviceId}
	</update>    
    
    <update id="addDeviceInfoAtApprove">
    	UPDATE MI_DMS_INFO_DEVICE
		SET AUTO_IP_SET = #{device.auto_ip_set}, 
			AUTO_COMPUTER_NAME_SET = #{device.auto_computer_name_set}, 
			USE_MPPLAYER = #{device.use_mpplayer}, 
			COMPUTER_NAME = #{device.computer_name}, 
			VNC_PASSWORD = #{device.vnc_password}, 
			PLAYER_START_TIMEOUT = #{device.player_start_timeout}, 
			CONTENT_READY_INTERVAL = #{device.content_ready_interval}, 
			FILEDATA_DEL_SIZE = #{device.filedata_del_size} 
		WHERE DEVICE_ID = #{device.deviceId}
	</update>  
	
	<select id="getPriorityByDeviceTypeAndVersion" resultType="long" >
		SELECT PRIORITY 
		FROM MI_DMS_INFO_DEVICE_PRIORITY 
		WHERE DEVICE_TYPE= #{deviceType} AND DEVICE_TYPE_VERSION= #{deviceTypeVersion, javaType=java.math.BigDecimal, jdbcType=DECIMAL, typeHandler=org.apache.ibatis.type.BigDecimalTypeHandler}
	</select>
	
	<select id="getMinimalDeviceTypeVersionByDeviceTypeAndGroupId" resultType="java.lang.Float">
		SELECT DEVICE_TYPE_VERSION
		FROM MI_DMS_INFO_DEVICE
		WHERE DEVICE_TYPE = #{deviceType}
			AND DEVICE_ID IN 
				(SELECT DEVICE_ID 
				FROM MI_DMS_MAP_GROUP_DEVICE 
				WHERE GROUP_ID = #{groupId} )
		ORDER BY DEVICE_TYPE_VERSION ASC
		LIMIT 1
	</select>

	<select id="getMinimalDeviceTypeVersionByDeviceTypeAndGroupId" resultType="java.lang.Float" databaseId="mssql">
		SELECT TOP 1 DEVICE_TYPE_VERSION
		FROM MI_DMS_INFO_DEVICE
		WHERE DEVICE_TYPE = #{deviceType}
			AND DEVICE_ID IN
				(SELECT DEVICE_ID
				FROM MI_DMS_MAP_GROUP_DEVICE
				WHERE GROUP_ID = #{groupId} )
		ORDER BY DEVICE_TYPE_VERSION ASC
	</select>

	<select id="getDeviceGroupPriority" resultType="java.lang.Long">
		SELECT MIN_PRIORITY 
		FROM MI_DMS_INFO_GROUP 
		WHERE GROUP_ID = #{groupId}
	</select>
	
	<select id="getDeviceListByModelNameAndType" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device">
		SELECT DEVICES.*  
		<include refid="getDeviceListByModelNameAndTypeCommon"/>
		ORDER BY CREATE_DATE DESC
	</select>
	
	<select id="getDeviceListByModelNameAndTypeCount" resultType="java.lang.Long">
		SELECT COUNT(DEVICE_ID)
		<include refid="getDeviceListByModelNameAndTypeCommon"/>
	</select>
	
	<sql id="getDeviceListByModelNameAndTypeCommon">
    	FROM MI_DMS_MAP_GROUP_DEVICE GROUPS 
    	LEFT JOIN MI_DMS_INFO_DEVICE DEVICES ON GROUPS.DEVICE_ID = DEVICES.DEVICE_ID
		<where>
			
			<if test="deviceType != null and !deviceType.equals('')">
				<choose>
	            	<when test="!deviceType.equals('iPLAYER')">
						DEVICES.DEVICE_TYPE = #{deviceType} 
						<if test="modelName != null and !modelName.equals('')">
							AND DEVICES.DEVICE_MODEL_NAME = #{modelName}
						</if>
	        		</when>
		         	<otherwise>
		                DEVICES.DEVICE_TYPE = #{deviceType}
		            </otherwise>
	          	</choose>
			</if>
			
			<if test="deviceId != null and !deviceId.equals('')">
				<bind name="deviceIdPattern" value="'%' + deviceId + '%'"/>
				AND DEVICES.DEVICE_ID LIKE #{deviceIdPattern}
			</if>

			<if test="modelCode != null and !modelCode.equals('')">
				AND DEVICES.DEVICE_MODEL_CODE = #{modelCode}
			</if>
			
			<if test="organization != null">
				<if test="!organization.equals('ROOT')">
					AND GROUPS.ORGANIZATION = #{organization}
				</if>
			</if>

		</where>
	</sql>
	
   	<sql id="deviceExpirationDate">
		<if test="deviceExpirationDate != null">
			<include refid="expirationDate"/>
		</if>
	</sql>
	
	<sql id="hasAlarmFilterQueries">
		<if test="condition.hasAlarmFilter != null">
			AND 
			  <foreach item="hasAlarm" collection="condition.hasAlarmFilter" separator=" OR " open="(" close=")">
                <choose>
                	<when test="hasAlarm == 'has_alarm_no_timezone'">
                		((TIME_ZONE_INDEX IS NULL OR TIME_ZONE_INDEX = '') AND A.DEVICE_TYPE != 'FLIP' )
                	</when>
                	<when test="hasAlarm == 'has_alarm_not_enough_storage'">
                		( DISK_SPACE_REPOSITORY / (1024*1024)  &lt; <include refid="getInsufficientCapacity" /> )
                	</when>
                	<when test="hasAlarm == 'has_alarm_no_schedule_deployed'">
                		(A.DEVICE_ID IN (SELECT DEVICE_ID FROM MI_DMS_MAP_GROUP_DEVICE
							WHERE GROUP_ID IN (
								SELECT DEVICE_GROUP_ID FROM MI_CDS_MAP_PROGRAM_DEVICE WHERE PROGRAM_ID IN (SELECT PROGRAM_ID FROM MI_CDS_INFO_PROGRAM WHERE IS_DEFAULT='Y')
			             	)
						))
						<if test="condition != null and condition.rm_device_types != null">
							AND A.DEVICE_TYPE NOT IN 
							<foreach item="item" index="index" collection="condition.rm_device_types" open="(" separator="," close=")">
								#{item}
							</foreach>
						</if>
                	</when>
                	<when test="hasAlarm == 'has_alarm_failed_schedule_deploy'">
                		(A.DEVICE_ID IN (SELECT DEVICE_ID FROM MI_CDS_MAP_PROGRAM_DEVICE_STATUS WHERE STATUS != 'SUCCESS')) 
                	</when>
                	<when test="hasAlarm == 'has_alarm_content_download'">
                		(A.DEVICE_ID IN (SELECT DISTINCT(DEVICE_ID) FROM MI_CDS_DOWNLOAD_STATUS WHERE PROGRESS IS NULL OR PROGRESS != '100 %')) 
                	</when>
                  	<when test="hasAlarm == 'filter_default_content'">
                		(A.CURR_CONTENT_ID IN ('DEFAULT_CONTENT_ID', 'C0C336FC-0EAA-416C-AC92-697C4A103EDF'))
                	</when> 
                </choose>  
            </foreach>
		</if>
	</sql>
	
	<sql id="hasFunctionFilterQueries">
		<if test="condition.hasFunctionFilter != null">
			AND 
			  <foreach item="hasFunction" collection="condition.hasFunctionFilter" separator=" OR " open="(" close=")">
                <choose>
                	<when test="hasFunction == 'is_videowall'">
                		(A.VWT_ID IS NOT NULL )
                	</when>
                	<when test="hasFunction == 'is_ams'">
                		(A.WEBCAM = <include refid="utils.true"/>) 
                	</when>
                </choose>
            </foreach>
		</if>
	</sql>
	
	<sql id="commonSearchKeywowrdQuery">
		<if test="condition.commonSearchKeyword != null and !condition.commonSearchKeyword.equals('')">
			AND (
				( LOWER(A.DEVICE_ID) LIKE #{condition.commonSearchKeyword} )
				OR
				( LOWER(A.DEVICE_NAME) LIKE #{condition.commonSearchKeyword} )
				OR
				( A.IP_ADDRESS LIKE #{condition.commonSearchKeyword} )
				OR
				( LOWER(A.DEVICE_MODEL_NAME) LIKE #{condition.commonSearchKeyword} )
				OR
				( LOWER(A.FIRMWARE_VERSION) LIKE #{condition.commonSearchKeyword} )
				OR
				( LOWER(A.APPLICATION_VERSION) LIKE #{condition.commonSearchKeyword} )
				OR
				( LOWER(A.LOCATION) LIKE #{condition.commonSearchKeyword} )
				OR
				( LOWER(A.SERIAL_DECIMAL) LIKE #{condition.commonSearchKeyword} )
                OR
                ( LOWER(E.TAG_VALUE) LIKE #{condition.commonSearchKeyword} )
			)
		</if>
	</sql>

	<sql id="expirationDate">
        <![CDATA[ AND (EXTRACT('epoch' from EXPIRATION_DATE - CURRENT_DATE)::int)/60/60/24<=#{deviceExpirationDate} ]]>
	</sql>

	<sql id="expirationDate" databaseId="mssql">
		<![CDATA[ AND DATEDIFF(D, GETDATE(), EXPIRATION_DATE) <= #{deviceExpirationDate} ]]> 
	</sql>
	
	<sql id="expirationDate" databaseId="mysql">
		<![CDATA[ AND TIMESTAMPDIFF(DAY, NOW(), EXPIRATION_DATE) <= #{deviceExpirationDate} ]]> 
	</sql>

	<sql id="deviceGroupAuthFrom">
		<if test="isDeviceGroupAuth">
			, MI_DMS_MAP_GROUP_USER D
		</if>
	</sql>
	
	<sql id="deviceGroupAuthWhere">
		<if test="isDeviceGroupAuth">
			AND C.GROUP_ID = D.GROUP_ID AND D.USER_ID = #{userId} 
		</if>
	</sql>
	
	<sql id="deviceGroupAuthFrom_C">
		<if test="isDeviceGroupAuth">
			, MI_DMS_MAP_GROUP_USER C
		</if>
	</sql>
	
	<sql id="deviceGroupAuthWhere_C">
		<if test="isDeviceGroupAuth">
			AND B.GROUP_ID = C.GROUP_ID AND C.USER_ID = #{userId} 
		</if>
	</sql>
	
	<sql id="deviceGroupAuthFrom_CC">
		<if test="isDeviceGroupAuth">
			, MI_DMS_MAP_GROUP_USER CC
		</if>
	</sql>
	
	<sql id="deviceGroupAuthWhere_CC">
		<if test="isDeviceGroupAuth">
			AND BB.GROUP_ID = CC.GROUP_ID AND CC.USER_ID = #{userId} 
		</if>
	</sql>

	<sql id="addDeviceType">
		 DEVICE_TYPE = 'SPLAYER'
	</sql>
    
    <insert id="addDeviceWaitingMo">
        INSERT INTO MI_DMS_INFO_WAITING_MO
        ( DEVICE_ID, SERVICE_NAME, INFO_VALUE )
        VALUES ( #{deviceId}, #{serviceName} , #{infoValue} )
    </insert>
    
    <select id="getDeviceWaitingMo" resultType="java.lang.String">
        SELECT INFO_VALUE
        FROM MI_DMS_INFO_WAITING_MO
        WHERE DEVICE_ID = #{deviceId}
    </select>
	
    <delete id="deleteWaitingMo">
        DELETE FROM MI_DMS_INFO_WAITING_MO 
        WHERE DEVICE_ID = #{deviceId}
    </delete>
    
    <update id="setDeviceWaitingMo">
        UPDATE MI_DMS_INFO_WAITING_MO
        SET INFO_VALUE = #{infoValue}, SERVICE_NAME = #{serviceName}
        WHERE DEVICE_ID = #{deviceId}
    </update>
    
    <select id="getDayLightSavingManual" resultType="java.lang.String">
        SELECT DAY_LIGHT_SAVING_MANUAL FROM MI_DMS_INFO_DEVICE WHERE DEVICE_ID = #{deviceId}
    </select>
    
    <select id="getDeviceGroupId" resultType="long">
		SELECT GROUP_ID FROM MI_DMS_MAP_GROUP_DEVICE WHERE DEVICE_ID = #{deviceId}
	</select>
	
	<select id="getExistCenterstage" resultType="boolean">
		SELECT COUNT(DEVICE_TYPE) FROM MI_DMS_INFO_DEVICE WHERE DEVICE_TYPE = '3rdPartyPLAYER'
	</select>
	
	 <insert id="insertDisasterAlertStatus">
    	INSERT INTO MI_DISASTER_ALERT_STATUS_INFO (ALERT_ID, FILE_NAME, DEVICE_ID, STATUS, MO_VALUE, PLAY_END_TIME, UPDATE_DATE)
    	VALUES(#{DisasterAlertStatus.alert_id}, #{DisasterAlertStatus.file_name}, #{DisasterAlertStatus.device_id}, #{DisasterAlertStatus.status}, #{DisasterAlertStatus.mo_value}, #{DisasterAlertStatus.play_end_time}, <include refid="utils.currentTimestamp"/> )
    </insert>
    
    <select	id="selectDisasterAlertStatus" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DisasterAlertStatusEntity">
    	SELECT *
    	FROM MI_DISASTER_ALERT_STATUS_INFO
    	WHERE ALERT_ID = #{alert_id} 
    </select>
    
     <select id="selectDisasterAlertStatusDisconnected" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DisasterAlertStatusEntity">
    		SELECT *
    	FROM MI_DISASTER_ALERT_STATUS_INFO
    	WHERE DEVICE_ID = #{device_id} AND STATUS = 'DISCONNECTED'
    	ORDER BY UPDATE_DATE
    </select>
    
    <select id="selectDisasterAlertStatusByDeviceId" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DisasterAlertStatusEntity">
    	SELECT *
    	FROM MI_DISASTER_ALERT_STATUS_INFO
    	WHERE DEVICE_ID = #{device_id}
    	ORDER BY UPDATE_DATE 
    </select>
    
    <select	id="selectSimpleDisasterAlertStatus" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.SimpleDisasterAlertStatusEntity">
    	SELECT *
    	FROM MI_DISASTER_ALERT_STATUS_INFO
    	WHERE ALERT_ID = #{alert_id} 
    </select>
    
  	<select	id="getDisconnectedDisasterAlertByDeviceIdAndAlertId" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.SimpleDisasterAlertStatusEntity">
    	SELECT *
    	FROM MI_DISASTER_ALERT_STATUS_INFO
    	WHERE ALERT_ID = #{alert_id} AND DEVICE_ID = #{device_id}  AND STATUS = 'DISCONNECTED'
    </select>
    
    <update id="updateDisasterAlertStatus">
    	UPDATE MI_DISASTER_ALERT_STATUS_INFO
        SET STATUS = #{DisasterAlertStatus.status}, MO_VALUE = #{DisasterAlertStatus.mo_value}, UPDATE_DATE = <include refid="utils.currentTimestamp"/>
        WHERE ALERT_ID = #{DisasterAlertStatus.alert_id} and DEVICE_ID=#{DisasterAlertStatus.device_id}
    </update>
    
    <delete id="deleteDisasterAlertStatus">
    	DELETE FROM MI_DISASTER_ALERT_STATUS_INFO 
    	WHERE ALERT_ID = #{alert_id}
    </delete>
    
    <delete id="deleteDisconnectedDisasterAlertStatus">
    	DELETE FROM MI_DISASTER_ALERT_STATUS_INFO 
    	WHERE ALERT_ID = #{alert_id} AND DEVICE_ID = #{device_id}  AND STATUS = 'DISCONNECTED'
    </delete>
    
    <!--  -->
    
    <insert id="insertExtDeviceInfo">
	    INSERT INTO MI_DMS_INFO_SUB_DEVICE (DEVICE_ID, DEVICE_NUMBER, POWER_STATUS, PANNEL_STATUS, INPUTSOURCE_STATUS, TYPE, UPDATE_DATE, SERIAL_CONNECTION_STATUS, BASIC_SOURCE)
		VALUES(#{deviceLoopOutInfo.device_id}, #{deviceLoopOutInfo.device_number}, #{deviceLoopOutInfo.power_status}, #{deviceLoopOutInfo.pannel_status}, #{deviceLoopOutInfo.inputSource_status}, #{deviceLoopOutInfo.type}, <include refid="utils.currentTimestamp"/>, #{deviceLoopOutInfo.serial_connection_status}, #{deviceLoopOutInfo.basic_source} )
    </insert>
    
    <select	id="selectExtDeviceInfo" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceLoopOutEntity">
    	SELECT *
    	FROM MI_DMS_INFO_SUB_DEVICE
    	WHERE DEVICE_ID = #{device_id} AND TYPE = #{type} 
    </select>
    
    <update id="updateExtDeviceInfo">
    	UPDATE MI_DMS_INFO_SUB_DEVICE
		SET POWER_STATUS = #{deviceLoopOutInfo.power_status}, PANNEL_STATUS = #{deviceLoopOutInfo.pannel_status}, INPUTSOURCE_STATUS = #{deviceLoopOutInfo.inputSource_status}, SERIAL_CONNECTION_STATUS =  #{deviceLoopOutInfo.serial_connection_status}, BASIC_SOURCE = #{deviceLoopOutInfo.basic_source}
		WHERE DEVICE_ID = #{deviceLoopOutInfo.device_id} and DEVICE_NUMBER = #{deviceLoopOutInfo.device_number}
    </update>
    
    <delete id="deleteExtDeviceInfo">
    	DELETE FROM MI_DMS_INFO_SUB_DEVICE
    	WHERE DEVICE_ID = #{device_id}
    </delete>
    
    <select id="getSoftwareUpdate" resultType="map">
    	SELECT APPLICATION_VERSION, SOFTWARE_UPDATE_VERSION FROM MI_DMS_INFO_DEVICE WHERE device_id = #{device_id}
    </select>

	<update id="setKeepAliveInfo">
        UPDATE MI_DMS_INFO_DEVICE SET
        LAST_CONNECTION_TIME = CURRENT_TIMESTAMP
         
         <if test="curr_content_id != null">
        ,CURR_CONTENT_ID = #{curr_content_id}
        </if>
        
        <if test="diskSpace != null">
        , DISK_SPACE_REPOSITORY = #{diskSpace}
        </if>

        <if test="channel != null">
        , DIRECT_CHANNEL = #{channel}
        </if>

        <if test="diskSpaceUsage != null">
        , DISK_SPACE_USAGE = #{diskSpaceUsage}
        </if>

        <if test="diskSpaceAvailable != null">
        , DISK_SPACE_AVAILABLE = #{diskSpaceAvailable}
        </if>

        WHERE DEVICE_ID = #{deviceId}
    </update>
	     
	<select id="getCheckDeviceListCntTimezone" resultType="int">
        SELECT COUNT(A.DEVICE_ID)
        FROM  MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B <include refid="deviceGroupAuthFrom_C"/>
        WHERE (TIME_ZONE_INDEX IS NULL OR TIME_ZONE_INDEX = '') AND A.DEVICE_TYPE != 'FLIP' AND IS_APPROVED = <include refid="utils.true"/> AND IS_CHILD = <include refid="utils.false"/> <include refid="deviceGroupAuthWhere_C"/>
       <include refid="deviceFilterQuery" />
       <include refid="searchQuery" />
       
       <if test="deviceGroupList != null and deviceGroupList.size > 0">
       		AND A.DEVICE_ID = B.DEVICE_ID
           AND B.GROUP_ID IN
           <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
               #{deviceGroupId.group_id}
           </foreach>
       </if>
    </select>

    <select id="getCheckDeviceListTimezone" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf">
        SELECT * 
        FROM MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B <include refid="deviceGroupAuthFrom_C"/>
        WHERE (TIME_ZONE_INDEX IS NULL OR TIME_ZONE_INDEX = '') AND A.DEVICE_TYPE != 'FLIP' AND IS_APPROVED = <include refid="utils.true"/> AND IS_CHILD = <include refid="utils.false"/>  <include refid="deviceGroupAuthWhere_C"/>
		<include refid="deviceFilterQuery" />
		<include refid="searchQuery" />
		
       <if test="deviceGroupList != null and deviceGroupList.size > 0">
       		AND A.DEVICE_ID = B.DEVICE_ID
           AND B.GROUP_ID IN
           <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
               #{deviceGroupId.group_id}
           </foreach>
       </if>
        
        <if test="pageSize != -1">
        LIMIT #{pageSize}
        </if>
        <if test="startPos != -1">
        OFFSET #{startPos}
        </if>
    </select>
    
    <select id="getCheckDeviceListTimezone" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf" databaseId="mssql">
        SELECT * FROM (
        	<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        	<bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
        	SELECT * FROM (
        		SELECT AA.*, ROW_NUMBER() OVER(ORDER BY AA.DEVICE_ID ASC ) as RowNum 
        		FROM  MI_DMS_INFO_DEVICE AA, MI_DMS_MAP_GROUP_DEVICE BB <include refid="deviceGroupAuthFrom_CC"/>
        		WHERE (TIME_ZONE_INDEX IS NULL OR TIME_ZONE_INDEX = '') AND AA.DEVICE_TYPE != 'FLIP' AND IS_APPROVED = <include refid="utils.true"/> AND IS_CHILD = <include refid="utils.false"/> <include refid="deviceGroupAuthWhere_CC"/>
				<include refid="deviceFilterQuery" />
				<include refid="searchQuery" />
				<if test="deviceGroupList != null and deviceGroupList.size > 0">
		       		AND AA.DEVICE_ID = BB.DEVICE_ID
                    AND BB.GROUP_ID IN
		           <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
		               #{deviceGroupId.group_id}
		           </foreach>
		       </if>
        	) as A
        )as SubQuery
        WHERE 
            1 = 1
            <if test="safe_startPos > -1">
            and RowNum > ${safe_startPos}
            </if>
            <if test="safe_rownumLimit > -1">
            and RowNum &lt;= ${safe_rownumLimit}
            </if>        
        ORDER BY RowNum
    </select>
    
     <select id="getCheckDeviceListCnt" resultType="int">
        SELECT COUNT(A.DEVICE_ID)
        FROM MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B <include refid="deviceGroupAuthFrom_C"/> 
        WHERE IS_APPROVED = <include refid="utils.true"/> AND IS_CHILD = <include refid="utils.false"/> <include refid="deviceGroupAuthWhere_C"/> 
        <include refid="deviceFilterQuery" />
        <include refid="searchQuery" />
        <if test="deviceGroupList != null and deviceGroupList.size > 0">
       		AND A.DEVICE_ID = B.DEVICE_ID
           AND B.GROUP_ID IN
           <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
               #{deviceGroupId.group_id}
           </foreach>
       </if> 
    </select>

    <select id="getCheckDeviceList" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf">
        SELECT *
        FROM MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B <include refid="deviceGroupAuthFrom_C"/>  
        WHERE IS_APPROVED = <include refid="utils.true"/> AND IS_CHILD = <include refid="utils.false"/> <include refid="deviceGroupAuthWhere_C"/>
        <include refid="deviceFilterQuery" />
        <include refid="searchQuery" />
        <if test="deviceGroupList != null and deviceGroupList.size > 0">
       		AND A.DEVICE_ID = B.DEVICE_ID
           AND B.GROUP_ID IN
           <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
               #{deviceGroupId.group_id}
           </foreach>
       </if>        
        <if test="pageSize != -1">
        LIMIT #{pageSize}
        </if>
        <if test="startPos != -1">
        OFFSET #{startPos}
        </if>
    </select>
    
    <select id="getCheckDeviceList" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf" databaseId="mssql">
    SELECT * FROM (
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
        SELECT * FROM (
        SELECT AA.*, ROW_NUMBER() OVER(ORDER BY AA.DEVICE_ID ASC ) as RowNum 
        FROM  MI_DMS_INFO_DEVICE AA, MI_DMS_MAP_GROUP_DEVICE BB  <include refid="deviceGroupAuthFrom_CC"/>
        WHERE IS_APPROVED = <include refid="utils.true"/> AND IS_CHILD = <include refid="utils.false"/> <include refid="deviceGroupAuthWhere_CC"/>
        <include refid="deviceFilterQuery" />
        <include refid="searchQuery" />
		<if test="deviceGroupList != null and deviceGroupList.size > 0">
       		AND AA.DEVICE_ID = BB.DEVICE_ID
           AND BB.GROUP_ID IN
           <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
               #{deviceGroupId.group_id}
           </foreach>
       </if>
		) as A
        )as SubQuery
        WHERE 
            1 = 1
            <if test="safe_startPos > -1">
            and RowNum > ${safe_startPos}
            </if>
            <if test="safe_rownumLimit > -1">
            and RowNum &lt;= ${safe_rownumLimit}
            </if>        
        ORDER BY RowNum
    </select>
    
    <select id="getCheckDeviceListCntSchedule" resultType="int">
        SELECT COUNT(A.DEVICE_ID) FROM  MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B <include refid="deviceGroupAuthFrom_C"/>
        WHERE A.DEVICE_ID IN (SELECT DEVICE_ID FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID NOT IN (SELECT DEVICE_GROUP_ID FROM MI_CDS_MAP_PROGRAM_DEVICE 
        WHERE PROGRAM_ID IN (SELECT PROGRAM_ID FROM MI_CDS_INFO_PROGRAM WHERE IS_DEFAULT='N'))) AND IS_CHILD = <include refid="utils.false"/> AND IS_APPROVED = <include refid="utils.true"/> <include refid="deviceGroupAuthWhere_C"/> 
        <include refid="deviceFilterQuery" />
        <include refid="searchQuery" />
		<if test="deviceGroupList != null and deviceGroupList.size > 0">
       		AND A.DEVICE_ID = B.DEVICE_ID
           AND B.GROUP_ID IN
           <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
               #{deviceGroupId.group_id}
           </foreach>
       </if>
    </select>

    <select id="getCheckDeviceListSchedule" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf">
        SELECT * FROM  MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B <include refid="deviceGroupAuthFrom_C"/> 
        WHERE A.DEVICE_ID IN (SELECT DEVICE_ID FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID NOT IN (SELECT DEVICE_GROUP_ID FROM MI_CDS_MAP_PROGRAM_DEVICE 
        WHERE PROGRAM_ID IN (SELECT PROGRAM_ID FROM MI_CDS_INFO_PROGRAM WHERE IS_DEFAULT='N'))) AND IS_CHILD = <include refid="utils.false"/> AND IS_APPROVED = <include refid="utils.true"/> <include refid="deviceGroupAuthWhere_C"/>
       <include refid="deviceFilterQuery" />
       <include refid="searchQuery" />
        <if test="deviceGroupList != null and deviceGroupList.size > 0">
       		AND A.DEVICE_ID = B.DEVICE_ID
           AND B.GROUP_ID IN
           <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
               #{deviceGroupId.group_id}
           </foreach>
       </if>
        <if test="pageSize != -1">
        LIMIT #{pageSize}
        </if>
        <if test="startPos != -1">
        OFFSET #{startPos}
        </if>
    </select>
    
    
    <select id="getCheckDeviceListSchedule" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf" databaseId="mssql">
    SELECT * FROM (
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
            SELECT * FROM (
        SELECT AA.*, ROW_NUMBER() OVER(ORDER BY AA.DEVICE_ID ASC ) as RowNum 
        FROM  MI_DMS_INFO_DEVICE AA, MI_DMS_MAP_GROUP_DEVICE BB <include refid="deviceGroupAuthFrom_CC"/>
        WHERE AA.DEVICE_ID IN (SELECT DEVICE_ID FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID NOT IN (SELECT DEVICE_GROUP_ID FROM MI_CDS_MAP_PROGRAM_DEVICE 
        WHERE PROGRAM_ID IN (SELECT PROGRAM_ID FROM MI_CDS_INFO_PROGRAM WHERE IS_DEFAULT='N'))) AND IS_CHILD = <include refid="utils.false"/> AND IS_APPROVED = <include refid="utils.true"/> <include refid="deviceGroupAuthWhere_CC"/>
       <include refid="deviceFilterQuery" />
       <include refid="searchQuery" />
       <if test="deviceGroupList != null and deviceGroupList.size > 0">
       		AND AA.DEVICE_ID = BB.DEVICE_ID
           AND BB.GROUP_ID IN
           <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
               #{deviceGroupId.group_id}
           </foreach>
       </if>
       
		) as A
        )as SubQuery
        WHERE 
            1 = 1
            <if test="safe_startPos > -1">
            and RowNum > ${safe_startPos}
            </if>
            <if test="safe_rownumLimit > -1">
            and RowNum &lt;= ${safe_rownumLimit}
            </if>
        ORDER BY RowNum
    </select>
    
    <select id="getCheckDeviceListCntScheduleFail" resultType="int">
        SELECT COUNT(A.DEVICE_ID) FROM  MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B <include refid="deviceGroupAuthFrom_C"/> 
        WHERE A.DEVICE_ID IN (
                SELECT DEVICE_ID FROM MI_CDS_MAP_PROGRAM_DEVICE_STATUS S INNER JOIN MI_CDS_INFO_PROGRAM P ON S.PROGRAM_ID = P.PROGRAM_ID 
                    WHERE S.STATUS != 'SUCCESS' AND P.DEPLOY_TIME != '' AND P.DEPLOY_TIME IS NOT NULL
            ) 
            AND IS_CHILD = <include refid="utils.false"/> AND IS_APPROVED = <include refid="utils.true"/> <include refid="deviceGroupAuthWhere_C"/> 
        <include refid="deviceFilterQuery" />
        <include refid="searchQuery" />
          <if test="deviceGroupList != null and deviceGroupList.size > 0">
       		AND A.DEVICE_ID = B.DEVICE_ID
           AND B.GROUP_ID IN
           <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
               #{deviceGroupId.group_id}
           </foreach>
       </if>
    </select>

    <select id="getCheckDeviceListScheduleFail" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf">
        SELECT * FROM  MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B <include refid="deviceGroupAuthFrom_C"/>
        WHERE A.DEVICE_ID IN (
                SELECT DEVICE_ID FROM MI_CDS_MAP_PROGRAM_DEVICE_STATUS S INNER JOIN MI_CDS_INFO_PROGRAM P ON S.PROGRAM_ID = P.PROGRAM_ID 
                    WHERE S.STATUS != 'SUCCESS' AND P.DEPLOY_TIME != '' AND P.DEPLOY_TIME IS NOT NULL                
            ) 
            AND IS_CHILD = <include refid="utils.false"/> AND IS_APPROVED = <include refid="utils.true"/> <include refid="deviceGroupAuthWhere_C"/>
        <include refid="deviceFilterQuery" />
        <include refid="searchQuery" />
       
        <if test="deviceGroupList != null and deviceGroupList.size > 0">
       		AND A.DEVICE_ID = B.DEVICE_ID
            AND B.GROUP_ID IN
            <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
               #{deviceGroupId.group_id}
            </foreach>
        </if>
        <if test="pageSize != -1">
            LIMIT #{pageSize}
        </if>
        <if test="startPos != -1">
            OFFSET #{startPos}
        </if>
    </select>
    
    <select id="getCheckDeviceListCntScheduleFail" resultType="int" databaseId="mssql">
        SELECT COUNT(*) FROM (
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
        SELECT * FROM (
        SELECT AA.*, ROW_NUMBER() OVER(ORDER BY AA.DEVICE_ID ASC ) as RowNum
        FROM  MI_DMS_INFO_DEVICE AA, MI_DMS_MAP_GROUP_DEVICE BB <include refid="deviceGroupAuthFrom_CC"/>
        WHERE AA.DEVICE_ID IN (SELECT DEVICE_ID FROM MI_CDS_MAP_PROGRAM_DEVICE_STATUS WHERE STATUS != 'SUCCESS') AND IS_CHILD = <include refid="utils.false"/> AND IS_APPROVED = <include refid="utils.true"/> <include refid="deviceGroupAuthWhere_CC"/>
       <include refid="deviceFilterQuery" />
       <include refid="searchQuery" />
        <if test="deviceGroupList != null and deviceGroupList.size > 0">
       		AND AA.DEVICE_ID = BB.DEVICE_ID
           AND BB.GROUP_ID IN
           <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
               #{deviceGroupId.group_id}
           </foreach>
       </if>
		) as A
        )as SubQuery
    </select>    
    
    <select id="getCheckDeviceListScheduleFail" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf" databaseId="mssql">
        SELECT * FROM (
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
        SELECT * FROM (
        SELECT AA.*, ROW_NUMBER() OVER(ORDER BY AA.DEVICE_ID ASC ) as RowNum
        FROM  MI_DMS_INFO_DEVICE AA, MI_DMS_MAP_GROUP_DEVICE BB <include refid="deviceGroupAuthFrom_CC"/>
        WHERE AA.DEVICE_ID IN (SELECT DEVICE_ID FROM MI_CDS_MAP_PROGRAM_DEVICE_STATUS WHERE STATUS != 'SUCCESS') AND IS_CHILD = <include refid="utils.false"/> AND IS_APPROVED = <include refid="utils.true"/> <include refid="deviceGroupAuthWhere_CC"/>
       <include refid="deviceFilterQuery" />
       <include refid="searchQuery" />
        <if test="deviceGroupList != null and deviceGroupList.size > 0">
       		AND AA.DEVICE_ID = BB.DEVICE_ID
           AND BB.GROUP_ID IN
           <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
               #{deviceGroupId.group_id}
           </foreach>
       </if>
		) as A
        )as SubQuery
        WHERE        
            1 = 1
            <if test="safe_startPos > -1">
            and RowNum > ${safe_startPos}
            </if>
            <if test="safe_rownumLimit > -1">
            and RowNum &lt;= ${safe_rownumLimit}
            </if>        
        ORDER BY RowNum
    </select>
    
    <select id="getCheckDeviceListReservationScheduleFail" resultType="map" databaseId="mssql">
    SELECT * FROM (
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />

        SELECT 
            C.DEVICE_ID, C.DEVICE_NAME, C.MAC_ADDRESS, C.DEVICE_MODEL_NAME, C.IP_ADDRESS, C.DISK_SPACE_AVAILABLE, 
            C.DEVICE_TYPE_VERSION, C.DEVICE_TYPE, C.LOCATION,
            P.PROGRAM_ID, P.PROGRAM_NAME, P.DEPLOY_TIME, P.VERSION, P.CREATE_DATE, 
            P.RESERVATION_REPEAT_TYPE, P.RESERVATION_START_DATE, P.RESERVATION_END_DATE, P.RESERVATION_WEEKLY, P.RESERVATION_MONTHLY,
            O.ORGANIZATION AS ORGAN_NAME,
            G.GROUP_NAME,            
            S.STATUS,
            ROW_NUMBER() OVER(ORDER BY C.DEVICE_ID ASC ) as RowNum
        FROM
            MI_CDS_MAP_PROGRAM_DEVICE_STATUS S 
                INNER JOIN MI_CDS_INFO_PROGRAM P ON S.PROGRAM_ID = P.PROGRAM_ID
                LEFT JOIN (
                    MI_DMS_MAP_GROUP_DEVICE A
                    INNER JOIN MI_DMS_MAP_GROUP_USER B ON A.GROUP_ID = B.GROUP_ID 
                    INNER JOIN MI_DMS_INFO_DEVICE C ON C.DEVICE_ID = A.DEVICE_ID
                    INNER JOIN MI_DMS_MAP_GROUP_DEVICE O ON C.DEVICE_ID = O.DEVICE_ID
                    INNER JOIN MI_DMS_INFO_GROUP G ON G.GROUP_ID = O.GROUP_ID
                ) ON S.DEVICE_ID = C.DEVICE_ID
        WHERE 
            C.IS_APPROVED = <include refid="utils.true"/>
            AND C.IS_CHILD = <include refid="utils.false"/>
            AND (P.DEPLOY_TIME IS NOT NULL AND P.DEPLOY_TIME != '')
            AND B.USER_ID = #{userId}
            AND S.STATUS != 'SUCCESS' OR S.STATUS IS NULL
            <include refid="deviceFilterQuery" />
            <include refid="searchQuery" />
            <if test="deviceGroupList != null and deviceGroupList.size > 0">
                AND B.GROUP_ID IN
                <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
                    #{deviceGroupId.group_id}
                </foreach>
            </if>
        )as SubQuery
        WHERE 
            1 = 1
            <if test="safe_startPos > -1">
            and RowNum > ${safe_startPos}
            </if>
            <if test="safe_rownumLimit > -1">
            and RowNum &lt;= ${safe_rownumLimit}
            </if>
        ORDER BY RowNum
    </select>    
    
    <select id="getCheckDeviceListReservationScheduleFail" resultType="map">
        SELECT 
            C.DEVICE_ID, C.DEVICE_NAME, C.MAC_ADDRESS, C.DEVICE_MODEL_NAME, C.IP_ADDRESS, C.DISK_SPACE_AVAILABLE, 
            C.DEVICE_TYPE_VERSION, C.DEVICE_TYPE, C.LOCATION,
            P.PROGRAM_ID, P.PROGRAM_NAME, P.DEPLOY_TIME, P.VERSION, P.CREATE_DATE, 
            P.RESERVATION_REPEAT_TYPE, P.RESERVATION_START_DATE, P.RESERVATION_END_DATE, P.RESERVATION_WEEKLY, P.RESERVATION_MONTHLY,
            O.ORGANIZATION AS ORGAN_NAME,
            G.GROUP_NAME,            
            S.STATUS
        FROM
            MI_CDS_MAP_PROGRAM_DEVICE_STATUS S 
                INNER JOIN MI_CDS_INFO_PROGRAM P ON S.PROGRAM_ID = P.PROGRAM_ID
                LEFT JOIN (
	                MI_DMS_MAP_GROUP_DEVICE A
	                INNER JOIN MI_DMS_MAP_GROUP_USER B ON A.GROUP_ID = B.GROUP_ID 
	                INNER JOIN MI_DMS_INFO_DEVICE C ON C.DEVICE_ID = A.DEVICE_ID
	                INNER JOIN MI_DMS_MAP_GROUP_DEVICE O ON C.DEVICE_ID = O.DEVICE_ID
	                INNER JOIN MI_DMS_INFO_GROUP G ON G.GROUP_ID = O.GROUP_ID
	            ) ON S.DEVICE_ID = C.DEVICE_ID
        WHERE 
            C.IS_APPROVED = <include refid="utils.true"/>
            AND C.IS_CHILD = <include refid="utils.false"/>
            AND (P.DEPLOY_TIME IS NOT NULL AND P.DEPLOY_TIME != '')
            AND B.USER_ID = #{userId}
            AND S.STATUS != 'SUCCESS' OR S.STATUS IS NULL
            <include refid="deviceFilterQuery" />
            <include refid="searchQuery" />
            <if test="deviceGroupList != null and deviceGroupList.size > 0">
                AND B.GROUP_ID IN
                <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
                    #{deviceGroupId.group_id}
                </foreach>
            </if>
            <if test="pageSize != -1">
                LIMIT #{pageSize}
            </if>
            <if test="startPos != -1">
                OFFSET #{startPos}
            </if>
    </select>
    
    <select id="getCheckDeviceListCntReservationScheduleFail" resultType="int">
        SELECT 
            COUNT(C.DEVICE_ID)
        FROM
            MI_CDS_MAP_PROGRAM_DEVICE_STATUS S 
                INNER JOIN MI_CDS_INFO_PROGRAM P ON S.PROGRAM_ID = P.PROGRAM_ID
                LEFT JOIN (
                    MI_DMS_MAP_GROUP_DEVICE A
                    INNER JOIN MI_DMS_MAP_GROUP_USER B ON A.GROUP_ID = B.GROUP_ID 
                    INNER JOIN MI_DMS_INFO_DEVICE C ON C.DEVICE_ID = A.DEVICE_ID
                    INNER JOIN MI_DMS_MAP_GROUP_DEVICE O ON C.DEVICE_ID = O.DEVICE_ID
                    INNER JOIN MI_DMS_INFO_GROUP G ON G.GROUP_ID = O.GROUP_ID
                ) ON S.DEVICE_ID = C.DEVICE_ID
        WHERE 
            C.IS_APPROVED = <include refid="utils.true"/>
            AND C.IS_CHILD = <include refid="utils.false"/>
            AND (P.DEPLOY_TIME IS NOT NULL AND P.DEPLOY_TIME != '')
            AND B.USER_ID = #{userId}
            AND S.STATUS != 'SUCCESS' OR S.STATUS IS NULL
            <include refid="deviceFilterQuery" />
            <include refid="searchQuery" />
            <if test="deviceGroupList != null and deviceGroupList.size > 0">
                AND B.GROUP_ID IN
                <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
                    #{deviceGroupId.group_id}
                </foreach>
            </if>
    </select>
    
    <select id="getCheckDeviceListCntContent" resultType="int">
        SELECT COUNT(A.DEVICE_ID) FROM  MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B <include refid="deviceGroupAuthFrom_C"/> 
        WHERE A.DEVICE_ID IN (SELECT DISTINCT(DEVICE_ID) FROM MI_CDS_DOWNLOAD_STATUS_DETAIL 
        WHERE PROGRESS IS NULL OR PROGRESS != '100 %') AND IS_CHILD = <include refid="utils.false"/> AND IS_APPROVED = <include refid="utils.true"/> <include refid="deviceGroupAuthWhere_C"/>
       <include refid="deviceFilterQuery" />
       <include refid="searchQuery" />
        <if test="deviceGroupList != null and deviceGroupList.size > 0">
       		AND A.DEVICE_ID = B.DEVICE_ID
           AND B.GROUP_ID IN
           <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
               #{deviceGroupId.group_id}
           </foreach>
       </if>
    </select>

    <select id="getCheckDeviceListContent" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf">
        SELECT * FROM  MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B <include refid="deviceGroupAuthFrom_C"/>
        WHERE A.DEVICE_ID IN (SELECT DISTINCT(DEVICE_ID) FROM MI_CDS_DOWNLOAD_STATUS_DETAIL 
        WHERE PROGRESS IS NULL OR PROGRESS != '100 %') AND IS_CHILD = <include refid="utils.false"/> AND IS_APPROVED = <include refid="utils.true"/> <include refid="deviceGroupAuthWhere_C"/>
        <include refid="deviceFilterQuery" />
        <include refid="searchQuery" />
        <if test="deviceGroupList != null and deviceGroupList.size > 0">
       		AND A.DEVICE_ID = B.DEVICE_ID
           AND B.GROUP_ID IN
           <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
               #{deviceGroupId.group_id}
           </foreach>
       </if>
        <if test="pageSize != -1">
        LIMIT #{pageSize}
        </if>
        <if test="startPos != -1">
        OFFSET #{startPos}
        </if>
    </select>
    
    <select id="getCheckDeviceListContent" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf" databaseId="mssql">
    SELECT * FROM (
        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
     SELECT * FROM (
        SELECT AA.*, ROW_NUMBER() OVER(ORDER BY AA.DEVICE_ID ASC ) as RowNum
        FROM  MI_DMS_INFO_DEVICE AA, MI_DMS_MAP_GROUP_DEVICE BB <include refid="deviceGroupAuthFrom_CC"/>
        WHERE AA.DEVICE_ID IN (SELECT DISTINCT(DEVICE_ID) FROM MI_CDS_DOWNLOAD_STATUS_DETAIL 
        WHERE PROGRESS IS NULL OR PROGRESS != '100 %') AND IS_CHILD = <include refid="utils.false"/> AND IS_APPROVED = <include refid="utils.true"/> <include refid="deviceGroupAuthWhere_CC"/>
        <include refid="deviceFilterQuery" />
        <include refid="searchQuery" />
        <if test="deviceGroupList != null and deviceGroupList.size > 0">
       		AND AA.DEVICE_ID = BB.DEVICE_ID
           AND BB.GROUP_ID IN
           <foreach item="deviceGroupId" collection="deviceGroupList" open="(" separator="," close=")">
               #{deviceGroupId.group_id}
           </foreach>
       </if>
		) as A
        )as SubQuery
        WHERE 
            1 = 1
            <if test="safe_startPos > -1">
            and RowNum > ${safe_startPos}
            </if>
            <if test="safe_rownumLimit > -1">
            and RowNum &lt;= ${safe_rownumLimit}
            </if>
        ORDER BY RowNum
    </select>


    <sql id="deviceTypeArrQuery">
        <if test="condition.device_type_arr != null">
            AND 
            <foreach item="deviceType" collection="condition.device_type_arr" separator=" OR " open="(" close=")">
        		<include refid="deviceTypeChooseQuery"></include>
            </foreach>
		</if>
	</sql>
	    
    <sql id="deviceFilterQuery">
		<if test="deviceTypeFilter != null">
		AND 
		  <foreach item="deviceType" collection="deviceTypeFilter" separator=" OR " open="(" close=")">
  				<include refid="deviceTypeChooseQuery"></include>
            </foreach>
		</if>
	</sql>


    <sql id="deviceTypeChooseQuery">
        <choose>
            <when test="deviceType == constants.TYPE_SOC">
                (DEVICE_TYPE = #{constants.TYPE_SOC} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_1_0})
            </when>
            <when test="deviceType == constants.TYPE_SOC2">
                (DEVICE_TYPE = #{constants.TYPE_SOC} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_2_0})
            </when>
            <when test="deviceType == constants.TYPE_SOC3">
                (DEVICE_TYPE = #{constants.TYPE_SOC} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_3_0})
            </when>
            <when test="deviceType == constants.TYPE_SOC4">
                (DEVICE_TYPE = #{constants.TYPE_SOC} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_4_0})
            </when>
            <when test="deviceType == constants.TYPE_SOC5">
                (DEVICE_TYPE = #{constants.TYPE_SOC} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_5_0})
            </when>
            <when test="deviceType == constants.TYPE_SOC6">
                (DEVICE_TYPE = #{constants.TYPE_SOC} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_6_0})
            </when>
            <when test="deviceType == constants.TYPE_SOC7">
                (DEVICE_TYPE = #{constants.TYPE_SOC} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_7_0})
            </when>
            <when test="deviceType == constants.TYPE_SOC9">
                (DEVICE_TYPE = #{constants.TYPE_SOC} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_9_0})
            </when>
            <when test="deviceType == constants.TYPE_SOC10">
                (DEVICE_TYPE = #{constants.TYPE_SOC} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_10_0})
            </when>
            <when test="deviceType == constants.TYPE_SIGNAGE3">
                (DEVICE_TYPE = #{constants.TYPE_SIGNAGE} AND (DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_1_0} OR
                DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_3_0}))
            </when>
            <when test="deviceType == constants.TYPE_SIGNAGE4">
                (DEVICE_TYPE = #{constants.TYPE_SIGNAGE} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_4_0})
            </when>
            <when test="deviceType == constants.TYPE_SIGNAGE6">
                (DEVICE_TYPE = #{constants.TYPE_SIGNAGE} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_6_0})
            </when>
            <when test="deviceType == constants.TYPE_LEDBOX4">
                (DEVICE_TYPE = #{constants.TYPE_LEDBOX} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_4_0})
            </when>
            <when test="deviceType == constants.TYPE_LEDBOX6">
                (DEVICE_TYPE = #{constants.TYPE_LEDBOX} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_6_0})
            </when>
            <when test="deviceType == constants.TYPE_LEDBOX9">
                (DEVICE_TYPE = #{constants.TYPE_LEDBOX} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_9_0})
            </when>
            <when test="deviceType == constants.TYPE_LEDBOX10">
                (DEVICE_TYPE = #{constants.TYPE_LEDBOX} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_10_0})
            </when>
            <when test="deviceType == constants.TYPE_FLIP">
                (DEVICE_TYPE = #{constants.TYPE_FLIP} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_1_0})
            </when>
            <when test="deviceType == constants.TYPE_FLIP2">
                (DEVICE_TYPE = #{constants.TYPE_FLIP} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_2_0})
            </when>
            <when test="deviceType == constants.TYPE_FLIP3">
                (DEVICE_TYPE = #{constants.TYPE_FLIP} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_3_0})
            </when>
            <when test="deviceType == constants.TYPE_FLIP4">    <!-- Flip4 is Flip pro-->
                (DEVICE_TYPE = #{constants.TYPE_FLIP} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_4_0})
            </when>
            <when test="deviceType == constants.TYPE_RMS_S4">
                (DEVICE_TYPE = #{constants.TYPE_RMS_S} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_4_0})
            </when>
            <when test="deviceType == constants.TYPE_RMS_S5">
                (DEVICE_TYPE = #{constants.TYPE_RMS_S} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_5_0})
            </when>
            <when test="deviceType == constants.TYPE_RMS_S6">
                (DEVICE_TYPE = #{constants.TYPE_RMS_S} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_6_0})
            </when>
            <when test="deviceType == constants.TYPE_RMS_S7">
                (DEVICE_TYPE = #{constants.TYPE_RMS_S} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_7_0})
            </when>
            <when test="deviceType == constants.TYPE_RMS_S9">
                (DEVICE_TYPE = #{constants.TYPE_RMS_S} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_9_0})
            </when>
            <when test="deviceType == constants.TYPE_RMS_S10">
                (DEVICE_TYPE = #{constants.TYPE_RMS_S} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_10_0})
            </when>
            <when test="deviceType == constants.TYPE_RMS_LEDBOX4">
                (DEVICE_TYPE = #{constants.TYPE_RMS_LEDBOX} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_4_0})
            </when>
            <when test="deviceType == constants.TYPE_RMS_LEDBOX6">
                (DEVICE_TYPE = #{constants.TYPE_RMS_LEDBOX} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_6_0})
            </when>
            <when test="deviceType == constants.TYPE_RMS_LEDBOX9">
                (DEVICE_TYPE = #{constants.TYPE_RMS_LEDBOX} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_9_0})
            </when>
            <when test="deviceType == constants.TYPE_RMS_LEDBOX10">
                (DEVICE_TYPE = #{constants.TYPE_RMS_LEDBOX} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_10_0})
            </when>
            <when test="deviceType == constants.TYPE_RMS_SIGNAGE4">
                (DEVICE_TYPE = #{constants.TYPE_RMS_SIGNAGE} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_4_0})
            </when>
            <when test="deviceType == constants.TYPE_RMS_SIGNAGE6">
                (DEVICE_TYPE = #{constants.TYPE_RMS_SIGNAGE} AND DEVICE_TYPE_VERSION = #{constants.TYPE_VERSION_6_0})
            </when>
            <otherwise>
                ( DEVICE_TYPE = #{deviceType} )
            </otherwise>
        </choose>
    </sql>

    <sql id="deviceTypeChooseQueryNonConstants">
        <choose>
            <when test="deviceType == 'SPLAYER'">
                (DEVICE_TYPE = 'SPLAYER' AND DEVICE_TYPE_VERSION = 1.0)
            </when>
            <when test="deviceType == 'S2PLAYER'">
                (DEVICE_TYPE = 'SPLAYER' AND DEVICE_TYPE_VERSION = 2.0)
            </when>
            <when test="deviceType == 'S3PLAYER'">
                (DEVICE_TYPE = 'SPLAYER' AND DEVICE_TYPE_VERSION = 3.0)
            </when>
            <when test="deviceType == 'S4PLAYER'">
                (DEVICE_TYPE = 'SPLAYER' AND DEVICE_TYPE_VERSION = 4.0)
            </when>
            <when test="deviceType == 'S5PLAYER'">
                (DEVICE_TYPE = 'SPLAYER' AND DEVICE_TYPE_VERSION = 5.0)
            </when>
            <when test="deviceType == 'S6PLAYER'">
                (DEVICE_TYPE = 'SPLAYER' AND DEVICE_TYPE_VERSION = 6.0)
            </when>
            <when test="deviceType == 'S7PLAYER'">
                (DEVICE_TYPE = 'SPLAYER' AND DEVICE_TYPE_VERSION = 7.0)
            </when>
            <when test="deviceType == 'S9PLAYER'">
                (DEVICE_TYPE = 'SPLAYER' AND DEVICE_TYPE_VERSION = 9.0)
            </when>
            <when test="deviceType == 'S10PLAYER'">
                (DEVICE_TYPE = 'SPLAYER' AND DEVICE_TYPE_VERSION = 10.0)
            </when>
            <when test="deviceType == 'SIGNAGE3'">
                (DEVICE_TYPE = 'SIGNAGE' AND (DEVICE_TYPE_VERSION = 3.0 OR DEVICE_TYPE_VERSION = 1.0))
            </when>
            <when test="deviceType == 'SIGNAGE4'">
                (DEVICE_TYPE = 'SIGNAGE' AND DEVICE_TYPE_VERSION = 4.0)
            </when>
            <when test="deviceType == 'SIGNAGE6'">
                (DEVICE_TYPE = 'SIGNAGE' AND DEVICE_TYPE_VERSION = 6.0)
            </when>
            <when test="deviceType == 'LEDBOX4'">
                (DEVICE_TYPE = 'LEDBOX' AND DEVICE_TYPE_VERSION = 4.0)
            </when>
            <when test="deviceType == 'LEDBOX6'">
                (DEVICE_TYPE = 'LEDBOX' AND DEVICE_TYPE_VERSION = 6.0)
            </when>
            <when test="deviceType == 'LEDBOX9'">
                (DEVICE_TYPE = 'LEDBOX' AND DEVICE_TYPE_VERSION = 9.0)
            </when>
            <when test="deviceType == 'LEDBOX10'">
                (DEVICE_TYPE = 'LEDBOX' AND DEVICE_TYPE_VERSION = 10.0)
            </when>
            <when test="deviceType == 'FLIP'">
                (DEVICE_TYPE = 'FLIP' AND DEVICE_TYPE_VERSION = 1.0)
            </when>
            <when test="deviceType == 'FLIP2'">
                (DEVICE_TYPE = 'FLIP' AND DEVICE_TYPE_VERSION = 2.0)
            </when>
            <when test="deviceType == 'FLIP3'">
                (DEVICE_TYPE = 'FLIP' AND DEVICE_TYPE_VERSION = 3.0)
            </when>
            <when test="deviceType == 'FLIP4'">   <!-- Flip4 is Flip pro -->
                (DEVICE_TYPE = 'FLIP' AND DEVICE_TYPE_VERSION = 4.0)
            </when>
            <when test="deviceType == 'RSPLAYER4'">
                (DEVICE_TYPE = 'RSPLAYER' AND DEVICE_TYPE_VERSION = 4.0)
            </when>
            <when test="deviceType == 'RSPLAYER5'">
                (DEVICE_TYPE = 'RSPLAYER' AND DEVICE_TYPE_VERSION = 5.0)
            </when>
            <when test="deviceType == 'RSPLAYER6'">
                (DEVICE_TYPE = 'RSPLAYER' AND DEVICE_TYPE_VERSION = 6.0)
            </when>
            <when test="deviceType == 'RSPLAYER7'">
                (DEVICE_TYPE = 'RSPLAYER' AND DEVICE_TYPE_VERSION = 7.0)
            </when>
            <when test="deviceType == 'RSPLAYER9'">
                (DEVICE_TYPE = 'RSPLAYER' AND DEVICE_TYPE_VERSION = 9.0)
            </when>
            <when test="deviceType == 'RSPLAYER10'">
                (DEVICE_TYPE = 'RSPLAYER' AND DEVICE_TYPE_VERSION = 10.0)
            </when>
            <when test="deviceType == 'RMS'">
                (DEVICE_TYPE = 'RSPLAYER' OR DEVICE_TYPE = 'RIPLAYER')
            </when>
            <when test="deviceType == 'RLEDBOX4'">
                (DEVICE_TYPE = 'RLEDBOX' AND DEVICE_TYPE_VERSION = 4.0 )
            </when>
            <when test="deviceType == 'RLEDBOX6'">
                (DEVICE_TYPE = 'RLEDBOX' AND DEVICE_TYPE_VERSION = 6.0 )
            </when>
            <when test="deviceType == 'RLEDBOX9'">
                (DEVICE_TYPE = 'RLEDBOX' AND DEVICE_TYPE_VERSION = 9.0 )
            </when>
            <when test="deviceType == 'RLEDBOX10'">
                (DEVICE_TYPE = 'RLEDBOX' AND DEVICE_TYPE_VERSION = 10.0 )
            </when>
            <when test="deviceType == 'RSIGNAGE4'">
                (DEVICE_TYPE = 'RSIGNAGE' AND DEVICE_TYPE_VERSION = 4.0)
            </when>
            <when test="deviceType == 'RSIGNAGE6'">
                (DEVICE_TYPE = 'RSIGNAGE' AND DEVICE_TYPE_VERSION = 6.0)
            </when>
            <otherwise>
                ( DEVICE_TYPE = #{deviceType} )
            </otherwise>
        </choose>
    </sql>
	
	<sql id="searchQuery">
	     <if test="condition!=null and condition.src_name != null and condition.src_name != ''">
            <bind name="srcUpper" value="_parameter.condition.src_name.toUpperCase()"/>
            <bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
             AND ((UPPER(DEVICE_NAME) 
             LIKE #{srcNamePattern} 
             ESCAPE '^') OR (UPPER(MAC_ADDRESS) 
             LIKE #{srcNamePattern} 
             ESCAPE '^') OR (UPPER(DEVICE_MODEL_NAME) 
             LIKE #{srcNamePattern} 
             ESCAPE '^') OR (UPPER(IP_ADDRESS) 
             LIKE #{srcNamePattern} 
             ESCAPE '^') OR (UPPER(APPLICATION_VERSION)
             LIKE #{srcNamePattern} ESCAPE '^')) 
        </if>
	</sql>
	
	<select id = "getChildDeviceIdList" resultType="String">
		SELECT DEVICE_ID FROM MI_DMS_INFO_DEVICE
		<bind name="deviceIdPattern" value="parentDeviceId + '\\_%'"/> 
		WHERE DEVICE_ID LIKE #{deviceIdPattern} ORDER BY DEVICE_ID ASC
	</select>
	
	<select id = "getChildDeviceIdList" resultType="String" databaseId="mssql">
		SELECT DEVICE_ID FROM MI_DMS_INFO_DEVICE
		<bind name="deviceIdPattern" value="parentDeviceId + '[_]%'"/> 
		WHERE DEVICE_ID LIKE #{deviceIdPattern} ORDER BY DEVICE_ID ASC
	</select>
	
	<delete id = "deleteChildDevice" >
		DELETE FROM MI_DMS_INFO_DEVICE
		<bind name="deviceIdPattern" value="parentDeviceId + '\\_%'"/> 
		WHERE DEVICE_ID LIKE #{deviceIdPattern}
	</delete>
	
	<delete id = "deleteChildDevice" databaseId="mssql">
		DELETE FROM MI_DMS_INFO_DEVICE
		<bind name="deviceIdPattern" value="parentDeviceId + '[_]%'"/> 
		WHERE DEVICE_ID LIKE #{deviceIdPattern}
	</delete>
	
	<delete id = "deleteChildDeviceDisplayConf" >
		DELETE FROM MI_DMS_INFO_LED_CABINET
		WHERE PARENT_DEVICE_ID = #{parentDeviceId};

		DELETE FROM MI_DMS_INFO_DISPLAY
		<bind name="deviceIdPattern" value="parentDeviceId + '\\_%'"/> 
		WHERE DEVICE_ID LIKE #{deviceIdPattern};
	</delete>

	<delete id = "deleteChildDeviceDisplayConf" databaseId="mssql">
        DELETE FROM MI_DMS_INFO_LED_CABINET
        WHERE PARENT_DEVICE_ID = #{parentDeviceId};	
		
		DELETE FROM MI_DMS_INFO_DISPLAY
		<bind name="deviceIdPattern" value="parentDeviceId + '[_]%'"/> 
		WHERE DEVICE_ID LIKE #{deviceIdPattern};
	</delete>
	
	<update id="setChildCnt">
        UPDATE MI_DMS_INFO_DEVICE SET CHILD_CNT = #{childCnt} WHERE DEVICE_ID = #{parentDeviceId}
    </update>
    
    <delete id="deleteChildDeviceGroupMapping">
        DELETE FROM MI_DMS_MAP_GROUP_DEVICE
        <bind name="deviceIdPattern" value="parentDeviceId + '\\_%'"/> 
		WHERE DEVICE_ID LIKE #{deviceIdPattern}
    </delete>
    
    <delete id="deleteChildDeviceGroupMapping" databaseId="mssql">
        DELETE FROM MI_DMS_MAP_GROUP_DEVICE
        <bind name="deviceIdPattern" value="parentDeviceId + '[_]%'"/> 
		WHERE DEVICE_ID LIKE #{deviceIdPattern}
    </delete>
    
	<update id="setConnectChildCnt">
        UPDATE MI_DMS_INFO_DEVICE
        SET CONN_CHILD_CNT = #{connChildCnt}
        WHERE DEVICE_ID = #{deviceId}
    </update>
	
	<select id="getDeviceMemo" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMemo">
		SELECT * FROM MI_DMS_INFO_MEMO WHERE DEVICE_ID = #{deviceId}
	</select>

	<insert id="addDeviceMemo">
        INSERT INTO MI_DMS_INFO_MEMO
        ( DEVICE_ID, MEMO, USER_ID, REG_DATE ) VALUES ( #{memo.device_id} , #{memo.memo}, #{memo.user_id}, #{memo.reg_date} );
    </insert>	

	<update id="setDeviceMemo">
		UPDATE MI_DMS_INFO_MEMO SET MEMO = #{memo.memo}, USER_ID = #{memo.user_id}, REG_DATE = #{memo.reg_date}  WHERE DEVICE_ID = #{memo.device_id}
	</update>
	
	<update id="deleteDeviceMemo">
		DELETE FROM MI_DMS_INFO_MEMO WHERE DEVICE_ID = #{memo.device_id}
	</update>
	
	<select id="getDeviceReportList" resultType="com.samsung.magicinfo.framework.setup.entity.ServerDeviceReportEntity">
        SELECT DEVICE_NAME, DEVICE_ID, IP_ADDRESS, DEVICE_MODEL_NAME, APPLICATION_VERSION as FIRMWARE_VERSION, SERIAL_DECIMAL
        FROM MI_DMS_INFO_DEVICE
        <include refid="deviceModelNotPreconfig" />
        ORDER BY DEVICE_ID ASC
    </select>
    
    <select id="getDeviceModelCount" resultType="com.samsung.magicinfo.framework.setup.entity.ServerDeviceReportEntity">
        SELECT DEVICE_MODEL_NAME, COUNT(*) AS MODEL_COUNT
		FROM MI_DMS_INFO_DEVICE
        <include refid="deviceModelNotPreconfig" />
		GROUP BY DEVICE_MODEL_NAME
		ORDER BY DEVICE_MODEL_NAME
    </select>
    
     <select id="getDeviceFirmwareCount" resultType="com.samsung.magicinfo.framework.setup.entity.ServerDeviceReportEntity">
        SELECT APPLICATION_VERSION as FIRMWARE_VERSION, COUNT(*) AS FIRMWARE_COUNT
		FROM MI_DMS_INFO_DEVICE
         <include refid="deviceModelNotPreconfig" />
		GROUP BY APPLICATION_VERSION
		ORDER BY APPLICATION_VERSION
    </select>
    
   	<update id="setDeviceAmsCam">
		UPDATE MI_DMS_INFO_DEVICE SET WEBCAM = #{isWebCam} WHERE DEVICE_ID = #{deviceId}
	</update>
	
	<insert id="addBackupPlayer">
        INSERT INTO MI_DMS_INFO_BACKUP 
        (BACKUP_DEVICE_ID, BUSY_LEVEL, BACKUP_MODE, GROUP_ID, OWN_CONTENT) VALUES ( #{backup.backup_device_id} , #{backup.busy_level}, #{backup.backup_mode}, #{backup.group_id}, #{backup.own_content});
    </insert>
    
    <insert id="addBackupTargetPlayer">
        INSERT INTO MI_DMS_MAP_BACKUP 
        (DEVICE_ID, BACKUP_DEVICE_ID, GROUP_ID) VALUES (#{backup.device_id}, #{backup.backup_device_id}, #{backup.group_id});
    </insert>
    
    <delete id="deleteBackupPlayer">
        DELETE FROM MI_DMS_INFO_BACKUP
        WHERE GROUP_ID = #{groupId}
    </delete>
    
    <delete id="deleteBackupTargetPlayer">
        DELETE FROM MI_DMS_MAP_BACKUP
        WHERE GROUP_ID = #{groupId}
    </delete>
    
    <update id="setBackupBusyLevel">
		UPDATE MI_DMS_INFO_BACKUP SET BUSY_LEVEL = #{busyLevel} WHERE BACKUP_DEVICE_ID = #{backupDeviceId}
	</update>
	
	 <update id="setWaitingMoCount">
		UPDATE MI_DMS_INFO_BACKUP SET WAITING_MO_COUNT = #{waitingMoCount} WHERE BACKUP_DEVICE_ID = #{backupDeviceId}
	</update>
	
	 <update id="setBackupDevice">
		UPDATE MI_DMS_MAP_BACKUP SET BACKUP_DEVICE_ID = #{backupDeviceId} WHERE DEVICE_ID = #{deviceId}
	</update>
	
	<select id="getBackupPlayers" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.BackupPlayEntity">
        SELECT *
        FROM MI_DMS_INFO_BACKUP
        WHERE GROUP_ID = #{groupId}
        ORDER BY BUSY_LEVEL
    </select>
    
    <select id="getBackupPlayerByWaitingMoCount" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.BackupPlayEntity">
        SELECT *
        FROM MI_DMS_INFO_BACKUP
        WHERE GROUP_ID = #{groupId}
        ORDER BY WAITING_MO_COUNT
    </select>
    
    <select id="getBackupPlayerByDeviceId" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.BackupPlayEntity">
        SELECT *
        FROM MI_DMS_INFO_BACKUP
        WHERE BACKUP_DEVICE_ID = #{deviceId}
    </select>
    
    <select id="getBackupTargetPlayers" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.BackupPlayEntity">
        SELECT MAP.*, INFO.OWN_CONTENT
        FROM MI_DMS_MAP_BACKUP MAP
        LEFT JOIN MI_DMS_INFO_BACKUP INFO ON MAP.GROUP_ID = INFO.GROUP_ID
        WHERE INFO.GROUP_ID = #{groupId}
    </select>
    
     <select id="getProgramInfoByDeviceGroupId"
            resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring">
        SELECT B.GROUP_ID, C.PROGRAM_ID, E.PROGRAM_NAME
        FROM MI_DMS_MAP_GROUP_DEVICE B, MI_CDS_INFO_FRAME C,
        MI_CDS_MAP_PROGRAM_DEVICE D, MI_CDS_INFO_PROGRAM E
        WHERE B.GROUP_ID = D.DEVICE_GROUP_ID
        AND C.PROGRAM_ID = D.PROGRAM_ID AND C.PROGRAM_ID = E.PROGRAM_ID
        AND B.GROUP_ID = #{groupId}
		AND E.IS_DEFAULT = 'N'
		LIMIT 1
    </select>
    
    <select id="getProgramInfoByDeviceGroupId"
            resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring" databaseId="mssql">
        SELECT TOP 1 B.GROUP_ID, C.PROGRAM_ID, E.PROGRAM_NAME
        FROM MI_DMS_MAP_GROUP_DEVICE B, MI_CDS_INFO_FRAME C,
        MI_CDS_MAP_PROGRAM_DEVICE D, MI_CDS_INFO_PROGRAM E
        WHERE B.GROUP_ID = D.DEVICE_GROUP_ID
        AND C.PROGRAM_ID = D.PROGRAM_ID AND C.PROGRAM_ID = E.PROGRAM_ID
        AND B.GROUP_ID = #{groupId}
		AND E.IS_DEFAULT = 'N'
    </select>

    <sql id="deviceModelNotPreconfig">
        WHERE DEVICE_MODEL_NAME != 'PRECONFIG'
    </sql>
        
    <sql id="tagFrom">
    	left join MI_TAG_MAP_DEVICE_TAG F on A.DEVICE_ID = F.DEVICE_ID
		left join MI_TAG_INFO_TAG E on E.TAG_ID = F.TAG_ID 
    </sql>
    
    <sql id="iconsSelect">
     , COUNT(ICON_ERROR_HW.DEVICE_ID) AS ICON_ERROR_HW
     <!-- 
	 , COUNT(ICON_ERROR_SW.DEVICE_ID) AS ICON_ERROR_SW
	 , COUNT(ICON_ALARM.DEVICE_ID) AS ICON_ALARM
	 -->
	 , COUNT(ICON_PROCESS_CONTENT_DOWNLOAD.PROGRESS) AS ICON_PROCESS_CONTENT_DOWNLOAD
	 , COUNT(ICON_PROCESS_LOG.STATUS) AS ICON_PROCESS_LOG
	 , COUNT(ICON_PROCESS_SW_DOWNLOAD.PROGRESS) AS ICON_PROCESS_SW_DOWNLOAD
	 , COUNT(ICON_MEMO.MEMO) AS ICON_MEMO
	 , COUNT(ICON_BACKUP.BACKUP_DEVICE_ID) AS ICON_BACKUP
    </sql>
    
    <sql id="iconsFrom">
         left join MI_FAULT_INFO_ERROR_WARNING ICON_ERROR_HW on ${alias}.DEVICE_ID = ICON_ERROR_HW.DEVICE_ID AND ICON_ERROR_HW.LEVEL = 'E' AND ICON_ERROR_HW.RECOVERED = <include refid="utils.false"/> 
         <!-- 
         left join MI_FAULT_INFO_CLIENT ICON_ERROR_SW on A.DEVICE_ID = ICON_ERROR_SW.DEVICE_ID AND (ICON_ERROR_SW.LEVEL = 'W' OR ICON_ERROR_SW.LEVEL = 'F' OR ICON_ERROR_SW.LEVEL = 'A')
         left join MI_FAULT_INFO_CLIENT ICON_ALARM on A.DEVICE_ID = ICON_ALARM.DEVICE_ID AND (ICON_ALARM.LEVEL = 'F' OR ICON_ALARM.LEVEL = 'A')
         -->
		 left join MI_CDS_DOWNLOAD_STATUS_DETAIL ICON_PROCESS_CONTENT_DOWNLOAD on ${alias}.DEVICE_ID = ICON_PROCESS_CONTENT_DOWNLOAD.DEVICE_ID AND (ICON_PROCESS_CONTENT_DOWNLOAD.PROGRESS IS NULL OR ICON_PROCESS_CONTENT_DOWNLOAD.PROGRESS != '100 %')
		 left join MI_DMS_INFO_LOG_PROCESS ICON_PROCESS_LOG on ${alias}.DEVICE_ID = ICON_PROCESS_LOG.DEVICE_ID AND ICON_PROCESS_LOG.STATUS != 'END'
		 left join (SELECT SW_RSV.SOFTWARE_TYPE, SW_DEV_MAP.* from MI_DMS_INFO_RESERVATION_SOFTWARE SW_RSV, MI_DMS_MAP_RESERVATION_SOFTWARE_DEVICE SW_DEV_MAP WHERE SW_RSV.SOFTWARE_RSV_ID = SW_DEV_MAP.SOFTWARE_RSV_ID
					AND SW_RSV.SOFTWARE_TYPE != '03' AND SW_RSV.SOFTWARE_TYPE != '04') ICON_PROCESS_SW_DOWNLOAD on ${alias}.DEVICE_ID = ICON_PROCESS_SW_DOWNLOAD.DEVICE_ID 
					AND (ICON_PROCESS_SW_DOWNLOAD.DOWNLOAD_STATUS = 'downloading' AND ICON_PROCESS_SW_DOWNLOAD.REGIST_TIME > NOW() - INTERVAL '12 HOUR')
		 left join MI_DMS_INFO_MEMO ICON_MEMO on ${alias}.DEVICE_ID = ICON_MEMO.DEVICE_ID
		 left join MI_DMS_INFO_BACKUP ICON_BACKUP on ${alias}.DEVICE_ID = ICON_BACKUP.BACKUP_DEVICE_ID
    </sql>

    <sql id="iconsFrom" databaseId="mssql">
         left join MI_FAULT_INFO_ERROR_WARNING ICON_ERROR_HW on ${alias}.DEVICE_ID = ICON_ERROR_HW.DEVICE_ID AND ICON_ERROR_HW.LEVEL = 'E' AND ICON_ERROR_HW.RECOVERED = <include refid="utils.false"/> 
         <!-- 
         left join MI_FAULT_INFO_CLIENT ICON_ERROR_SW on A.DEVICE_ID = ICON_ERROR_SW.DEVICE_ID AND (ICON_ERROR_SW.LEVEL = 'W' OR ICON_ERROR_SW.LEVEL = 'F' OR ICON_ERROR_SW.LEVEL = 'A')
         left join MI_FAULT_INFO_CLIENT ICON_ALARM on A.DEVICE_ID = ICON_ALARM.DEVICE_ID AND (ICON_ALARM.LEVEL = 'F' OR ICON_ALARM.LEVEL = 'A')
         -->
		 left join MI_CDS_DOWNLOAD_STATUS_DETAIL ICON_PROCESS_CONTENT_DOWNLOAD on ${alias}.DEVICE_ID = ICON_PROCESS_CONTENT_DOWNLOAD.DEVICE_ID AND (ICON_PROCESS_CONTENT_DOWNLOAD.PROGRESS IS NULL OR ICON_PROCESS_CONTENT_DOWNLOAD.PROGRESS != '100 %')
		 left join MI_DMS_INFO_LOG_PROCESS ICON_PROCESS_LOG on ${alias}.DEVICE_ID = ICON_PROCESS_LOG.DEVICE_ID AND ICON_PROCESS_LOG.STATUS != 'END'
		 left join (SELECT SW_RSV.SOFTWARE_TYPE, SW_DEV_MAP.* from MI_DMS_INFO_RESERVATION_SOFTWARE SW_RSV, MI_DMS_MAP_RESERVATION_SOFTWARE_DEVICE SW_DEV_MAP WHERE SW_RSV.SOFTWARE_RSV_ID = SW_DEV_MAP.SOFTWARE_RSV_ID
					AND SW_RSV.SOFTWARE_TYPE != '03' AND SW_RSV.SOFTWARE_TYPE != '04') ICON_PROCESS_SW_DOWNLOAD on ${alias}.DEVICE_ID = ICON_PROCESS_SW_DOWNLOAD.DEVICE_ID 
					AND (ICON_PROCESS_SW_DOWNLOAD.DOWNLOAD_STATUS = 'downloading' AND ICON_PROCESS_SW_DOWNLOAD.REGIST_TIME > DATEADD(HH, -12, GETDATE()))
		 left join MI_DMS_INFO_MEMO ICON_MEMO on ${alias}.DEVICE_ID = ICON_MEMO.DEVICE_ID
		 left join MI_DMS_INFO_BACKUP ICON_BACKUP on ${alias}.DEVICE_ID = ICON_BACKUP.BACKUP_DEVICE_ID
    </sql>

    <sql id="iconsFrom_monitoring">
         left join MI_FAULT_INFO_ERROR_WARNING ICON_ERROR_HW on A.DEVICE_ID = ICON_ERROR_HW.DEVICE_ID AND ICON_ERROR_HW.LEVEL = 'E' AND ICON_ERROR_HW.RECOVERED = <include refid="utils.false"/>
         <!-- 
         left join MI_FAULT_INFO_CLIENT ICON_ERROR_SW on A.DEVICE_ID = ICON_ERROR_SW.DEVICE_ID AND (ICON_ERROR_SW.LEVEL = 'W' OR ICON_ERROR_SW.LEVEL = 'F' OR ICON_ERROR_SW.LEVEL = 'A')
         left join MI_FAULT_INFO_CLIENT ICON_ALARM on A.DEVICE_ID = ICON_ALARM.DEVICE_ID AND (ICON_ALARM.LEVEL = 'F' OR ICON_ALARM.LEVEL = 'A')
         -->
         left join MI_CDS_DOWNLOAD_STATUS_DETAIL ICON_PROCESS_CONTENT_DOWNLOAD on A.DEVICE_ID = ICON_PROCESS_CONTENT_DOWNLOAD.DEVICE_ID AND (ICON_PROCESS_CONTENT_DOWNLOAD.PROGRESS IS NULL OR ICON_PROCESS_CONTENT_DOWNLOAD.PROGRESS != '100 %')
         left join MI_DMS_INFO_LOG_PROCESS ICON_PROCESS_LOG on A.DEVICE_ID = ICON_PROCESS_LOG.DEVICE_ID AND ICON_PROCESS_LOG.STATUS != 'END'
         left join (SELECT SW_RSV.SOFTWARE_TYPE, SW_DEV_MAP.* from MI_DMS_INFO_RESERVATION_SOFTWARE SW_RSV, MI_DMS_MAP_RESERVATION_SOFTWARE_DEVICE SW_DEV_MAP WHERE SW_RSV.SOFTWARE_RSV_ID = SW_DEV_MAP.SOFTWARE_RSV_ID
					AND SW_RSV.SOFTWARE_TYPE != '03' AND SW_RSV.SOFTWARE_TYPE != '04') ICON_PROCESS_SW_DOWNLOAD on A.DEVICE_ID = ICON_PROCESS_SW_DOWNLOAD.DEVICE_ID 
					AND (ICON_PROCESS_SW_DOWNLOAD.DOWNLOAD_STATUS = 'downloading' AND ICON_PROCESS_SW_DOWNLOAD.REGIST_TIME > NOW() - INTERVAL '12 HOUR')
         left join MI_DMS_INFO_MEMO ICON_MEMO on A.DEVICE_ID = ICON_MEMO.DEVICE_ID
         left join MI_DMS_INFO_BACKUP ICON_BACKUP on A.DEVICE_ID = ICON_BACKUP.BACKUP_DEVICE_ID
    </sql>

    <sql id="iconsFrom_monitoring" databaseId="mssql">
         left join MI_FAULT_INFO_ERROR_WARNING ICON_ERROR_HW on A.DEVICE_ID = ICON_ERROR_HW.DEVICE_ID AND ICON_ERROR_HW.LEVEL = 'E' AND ICON_ERROR_HW.RECOVERED = <include refid="utils.false"/>
         <!-- 
         left join MI_FAULT_INFO_CLIENT ICON_ERROR_SW on A.DEVICE_ID = ICON_ERROR_SW.DEVICE_ID AND (ICON_ERROR_SW.LEVEL = 'W' OR ICON_ERROR_SW.LEVEL = 'F' OR ICON_ERROR_SW.LEVEL = 'A')
         left join MI_FAULT_INFO_CLIENT ICON_ALARM on A.DEVICE_ID = ICON_ALARM.DEVICE_ID AND (ICON_ALARM.LEVEL = 'F' OR ICON_ALARM.LEVEL = 'A')
         -->
         left join MI_CDS_DOWNLOAD_STATUS_DETAIL ICON_PROCESS_CONTENT_DOWNLOAD on A.DEVICE_ID = ICON_PROCESS_CONTENT_DOWNLOAD.DEVICE_ID AND (ICON_PROCESS_CONTENT_DOWNLOAD.PROGRESS IS NULL OR ICON_PROCESS_CONTENT_DOWNLOAD.PROGRESS != '100 %')
         left join MI_DMS_INFO_LOG_PROCESS ICON_PROCESS_LOG on A.DEVICE_ID = ICON_PROCESS_LOG.DEVICE_ID AND ICON_PROCESS_LOG.STATUS != 'END'
         left join (SELECT SW_RSV.SOFTWARE_TYPE, SW_DEV_MAP.* from MI_DMS_INFO_RESERVATION_SOFTWARE SW_RSV, MI_DMS_MAP_RESERVATION_SOFTWARE_DEVICE SW_DEV_MAP WHERE SW_RSV.SOFTWARE_RSV_ID = SW_DEV_MAP.SOFTWARE_RSV_ID
					AND SW_RSV.SOFTWARE_TYPE != '03' AND SW_RSV.SOFTWARE_TYPE != '04') ICON_PROCESS_SW_DOWNLOAD on A.DEVICE_ID = ICON_PROCESS_SW_DOWNLOAD.DEVICE_ID 
					AND (ICON_PROCESS_SW_DOWNLOAD.DOWNLOAD_STATUS = 'downloading' AND ICON_PROCESS_SW_DOWNLOAD.REGIST_TIME > DATEADD(HH, -12, GETDATE()))
         left join MI_DMS_INFO_MEMO ICON_MEMO on A.DEVICE_ID = ICON_MEMO.DEVICE_ID
         left join MI_DMS_INFO_BACKUP ICON_BACKUP on A.DEVICE_ID = ICON_BACKUP.BACKUP_DEVICE_ID
    </sql>

    <select id="getDeviceAndGroupInfoByGroupId" resultType="map">
        SELECT A.DEVICE_ID, A.DEVICE_NAME, A.IS_REDUNDANCY, B.GROUP_NAME 
        FROM MI_DMS_INFO_DEVICE A, MI_DMS_INFO_GROUP B, MI_DMS_MAP_GROUP_DEVICE C  
        WHERE A.DEVICE_ID=C.DEVICE_ID AND B.GROUP_ID=C.GROUP_ID AND C.GROUP_ID=#{groupId} 
    </select>
    
    <select id="cntSyncPlayDevice" resultType="int">
		SELECT COUNT(DEVICE_ID) FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID IN (SELECT DEVICE_GROUP_ID FROM MI_CDS_MAP_PROGRAM_DEVICE WHERE PROGRAM_ID IN (SELECT PROGRAM_ID FROM MI_CDS_INFO_PROGRAM WHERE USE_SYNC_PLAY = 'Y')) AND DEVICE_ID = #{deviceId} 
	</select>
	
	<update id = "updateLastModifiedTime">
		 UPDATE MI_DMS_INFO_DEVICE
        SET LAST_MODIFIED_TIME = CURRENT_TIMESTAMP
        WHERE DEVICE_ID = #{deviceId}
	</update>

	<select id = "getNewAndModifiedDeviceList" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device">
		SELECT * FROM MI_DMS_INFO_DEVICE WHERE MDC_UPDATE_TIME &gt;= #{startDate} AND MDC_UPDATE_TIME &lt;= #{endDate}
	</select>
	
	<update id="addDeviceTotalCount">
		UPDATE MI_DMS_INFO_GROUP
		SET TOTAL_COUNT = GROUPS.TOTAL_COUNT
		FROM ( SELECT (TOTAL_COUNT + #{count}) AS TOTAL_COUNT FROM MI_DMS_INFO_GROUP WHERE GROUP_ID = #{groupId}) AS GROUPS
		WHERE GROUP_ID = #{groupId}
	</update>
	
	<update id="addDeviceTotalCount" databaseId="mysql">
		UPDATE MI_DMS_INFO_GROUP, ( SELECT (TOTAL_COUNT + #{count}) AS TOTAL_COUNT FROM MI_DMS_INFO_GROUP WHERE GROUP_ID = #{groupId}) GROUPS
		SET MI_DMS_INFO_GROUP.TOTAL_COUNT = GROUPS.TOTAL_COUNT
		WHERE GROUP_ID = #{groupId}
	</update>
	
	<select id="getPgorupIdLIsts" resultType="map">
		SELECT P_GROUP_ID FROM MI_DMS_INFO_GROUP WHERE GROUP_ID = #{groupId} AND P_GROUP_ID != -1
	</select>
	
	<update id="removeDeviceTotalCount">
		UPDATE MI_DMS_INFO_GROUP
		SET TOTAL_COUNT = GROUPS.TOTAL_COUNT
		FROM ( SELECT (TOTAL_COUNT - #{count}) AS TOTAL_COUNT FROM MI_DMS_INFO_GROUP WHERE GROUP_ID = #{groupId}) AS GROUPS
		WHERE GROUP_ID = #{groupId}
	</update>
	
	<update id="removeDeviceTotalCount" databaseId="mysql">
		UPDATE MI_DMS_INFO_GROUP, ( SELECT (TOTAL_COUNT - #{count}) AS TOTAL_COUNT FROM MI_DMS_INFO_GROUP WHERE GROUP_ID = #{groupId}) AS GROUPS
		SET MI_DMS_INFO_GROUP.TOTAL_COUNT = GROUPS.TOTAL_COUNT
		WHERE GROUP_ID = #{groupId}
	</update>

	<select id="getDeviceTotalCount" resultType="java.lang.Long">
		SELECT TOTAL_COUNT FROM MI_DMS_INFO_GROUP WHERE GROUP_ID = #{groupId}
	</select>
	<update id="setDeviceTotalCount">
		UPDATE MI_DMS_INFO_GROUP
		SET TOTAL_COUNT = #{count}
		WHERE GROUP_ID = #{groupId}
	</update>

	<select id="getDeviceCountBygroupId" resultType="int">
		SELECT COUNT(DEVICE_ID) FROM MI_DMS_MAP_GROUP_DEVICE WHERE GROUP_ID = #{groupId}
	</select>

	<select id="getStatisticsFileRequestTime" resultType="map">
		SELECT REQUEST_TIME 
		FROM MI_STATISTICS_REQUEST_TIME 
		WHERE DEVICE_ID = #{deviceId}
	</select>		
	
	<insert id="addDeviceLogProcessInfo">
        INSERT INTO MI_DMS_INFO_LOG_PROCESS
        ( DEVICE_ID, TYPE, CATEGORY_SCRIPT, STATUS, START_TIME, DURATION, PACKET_SIZE , TOKEN)
        VALUES ( #{deviceId}, #{type}, #{categoryScript}, #{status}, #{startTime}, #{duration}, #{packetSize}, #{token})
    </insert>	
    
    <select id="getDeviceLogProcessInfo" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceLogCollectEntity">
		SELECT * 
		FROM MI_DMS_INFO_LOG_PROCESS 
		WHERE DEVICE_ID = #{deviceId}
	</select>
	
	<select id="getAllDeviceLogProcess" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceLogCollectEntity">
		SELECT * 
		FROM MI_DMS_INFO_LOG_PROCESS 
		WHERE STATUS != 'END'
	</select>
	
	<update id="updateDeviceLogProcessStatus">
		UPDATE MI_DMS_INFO_LOG_PROCESS
		<set>
			STATUS = #{status}
		</set>
		WHERE DEVICE_ID = #{deviceId}
        <if test="type != null and !type.trim().equals('')">
            AND TYPE = #{type}
        </if>
        <if test="categoryScript != null">
            AND CATEGORY_SCRIPT = #{categoryScript}
        </if>
	</update>

    <update id = "updateLogFileName">
        UPDATE MI_DMS_INFO_LOG_PROCESS
        SET FILE_NAME = #{fileName}
        WHERE DEVICE_ID = #{deviceId}
        <if test="categoryScript != null">
            AND CATEGORY_SCRIPT = #{categoryScript}
        </if>
    </update>
	
	<update id="updateDeviceLogInfo">
		UPDATE MI_DMS_INFO_LOG_PROCESS
		<set>
			 <bind name="cnt" value="0"/>
	        <if test="categoryScript != null and !categoryScript.trim().equals('')">
	            CATEGORY_SCRIPT = #{categoryScript}
	            <bind name="cnt" value="1"/>
	        </if>
	        <if test="status != null and !status.trim().equals('')">
	            <if test="cnt > 0">,</if>
	            STATUS = #{status}
	            <bind name="cnt" value="2"/>
	        </if>
	        <if test="startTime != null">
	            <if test="cnt > 0">,</if>
	            START_TIME = #{startTime}
	            <bind name="cnt" value="3"/>
	        </if>
	        <if test="duration != null">
	            <if test="cnt > 0">,</if>
	            DURATION = #{duration}
	            <bind name="cnt" value="4"/>
	        </if>
	        <if test="packetSize != null">
	            <if test="cnt > 0">,</if>
	            PACKET_SIZE = #{packetSize}
	            <bind name="cnt" value="5"/>
	        </if>
	        <if test="token != null">
	            <if test="cnt > 0">,</if>
	            TOKEN = #{token}
	            <bind name="cnt" value="6"/>
	        </if>
	        <if test="encryptionKey != null and !encryptionKey.trim().equals('')">
	            <if test="cnt > 0">,</if>
	            ENCRYPTION_KEY = #{encryptionKey}
	        </if>
		</set>
		WHERE DEVICE_ID = #{deviceId}
        <if test="type != null and !type.trim().equals('')">
            AND TYPE = #{type}
        </if>
        <if test="categoryScript != null and !categoryScript.trim().equals('') and type != null and !type.trim().equals('platform')">
            AND CATEGORY_SCRIPT = #{categoryScript}
        </if>
	</update>
	
	<delete id="deleteDeviceLogProcessInfoByDeviceId">
        DELETE FROM MI_DMS_INFO_LOG_PROCESS
        WHERE DEVICE_ID = #{deviceId}
        <if test="categoryScript != null and !categoryScript.trim().equals('')">
            AND CATEGORY_SCRIPT = #{categoryScript}
        </if>
    </delete>	
    
    <select id="getLogProcessingDeviceCnt" resultType="int">
        SELECT COUNT(DISTINCT DEVICE_ID)
        FROM MI_DMS_INFO_LOG_PROCESS 
		WHERE STATUS != 'END'
    </select>
    
    <select id="getIsOverWriteDeviceName" resultType="Boolean">
		SELECT IS_OVERWRITE_DEVICE_NAME FROM MI_DMS_INFO_DEVICE WHERE DEVICE_ID = #{deviceId}
	</select>
	
	<!--  [2019.10 RC] KSY :  A.GROUP_ID 추가    -->
	   <select id="getDeviceListByOrgName" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device">
       SELECT A.DEVICE_ID, B.DEVICE_NAME, B.DEVICE_TYPE, B.IP_ADDRESS, B.DEVICE_MODEL_NAME, G.GROUP_NAME, B.CREATE_DATE, B.LAST_CONNECTION_TIME, A.GROUP_ID
       FROM MI_DMS_MAP_GROUP_DEVICE A, MI_DMS_INFO_DEVICE B, MI_DMS_INFO_GROUP G
       WHERE A.DEVICE_ID = B.DEVICE_ID AND ORGANIZATION = #{orgName} AND A.GROUP_ID = G.GROUP_ID
    </select>
    
    <select id="getDeviceCountForLicense" resultType="int">
    	SELECT COUNT(DEVICE_ID)
    	FROM MI_DMS_INFO_DEVICE
    	<if test="map != null and map.size > 0">
    		WHERE IS_APPROVED = <include refid="utils.true"/>
			<foreach item="device" collection="map" open=" AND (" separator=" OR " close=")">
				( DEVICE_TYPE = #{device.deviceType}
            	<if	test="device.deviceTypeVersion != null and device.deviceTypeVersion > -1">
            		AND DEVICE_TYPE_VERSION = #{device.deviceTypeVersion}
            	</if>
            	)
        	</foreach>    			
        	
    	</if>
    </select>
    
    <select id="getDeviceListFromDeviceId" resultType="map">
    	SELECT DEVICE_ID, DEVICE_NAME, DEVICE_TYPE, DEVICE_TYPE_VERSION, CREATE_DATE
    	FROM MI_DMS_INFO_DEVICE
    	<if test="deviceIds != null and deviceIds.size > 0">
    		WHERE IS_APPROVED = <include refid="utils.true"/>
    		<foreach item="device" collection="deviceIds" open=" AND (" separator=" OR " close=")">
            	DEVICE_ID = #{device.device_id}
        	</foreach> 
    	</if>
    
    </select>
    
    <select id="getFirstChildrenIDsOfSignageGroup" resultType="map">
        SELECT A.DEVICE_ID 
        FROM MI_DMS_MAP_GROUP_DEVICE A, MI_DMS_INFO_DEVICE B
        WHERE GROUP_ID = #{groupId} AND
        <bind name="deviceNamePattern" value="'%_1'"/> 
        A.DEVICE_ID LIKE  #{deviceNamePattern} AND A.DEVICE_ID = B.DEVICE_ID
    </select>
    
    <select id="getCheckUpcomingExpiryDateList" parameterType="map" resultType="com.samsung.magicinfo.framework.scheduler.entity.ScheduleAdminEntity">
    	SELECT INFO_PROGRAM.* 
    	FROM MI_CDS_INFO_PROGRAM INFO_PROGRAM
    	<include refid="getCheckUpcomingExpiryDateList_where" />
		<if test="condition != null">
			<if test="condition.sort_name != null and condition.sort_name != '' and condition.order_dir != null and condition.order_dir != ''">
				<bind name="safe_sortUpper"	value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
				<bind name="safe_sortOrder"	value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
				ORDER BY ${safe_sortUpper} ${safe_sortOrder}
			</if>
		</if>
		<if test="pageSize != -1">
        	LIMIT #{pageSize}
        </if>
        <if test="startPos != -1">
        	OFFSET #{startPos}
        </if>
		
    </select>
    
    <select id="getCheckUpcomingExpiryDateList" parameterType="map" resultType="com.samsung.magicinfo.framework.scheduler.entity.ScheduleAdminEntity" databaseId="mssql">
    	SELECT * FROM (
    		<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
        	<bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
        	<bind name="safe_sortUpper"	value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
			<bind name="safe_sortOrder"	value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />

	    	SELECT INFO_PROGRAM.*, ROW_NUMBER() OVER(ORDER BY ${safe_sortUpper} ${safe_sortOrder}) as RowNum
	    	FROM MI_CDS_INFO_PROGRAM INFO_PROGRAM
	    	<include refid="getCheckUpcomingExpiryDateList_where" />
			<if test="condition != null">
				<if test="condition.sort_name != null and condition.sort_name != '' and condition.order_dir != null and condition.order_dir != ''">
					<bind name="safe_sortUpper"	value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
					<bind name="safe_sortOrder"	value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
				</if>
			</if>
		)as SubQuery
        WHERE
            1 = 1
            <if test="safe_startPos > -1">
            and RowNum > ${safe_startPos}
            </if>
            <if test="safe_rownumLimit > -1">
            and RowNum &lt;= ${safe_rownumLimit}
            </if>        
        ORDER BY RowNum
    </select>
    
    <select id="getCheckUpcomingExpiryDateCnt" resultType="int">
    	SELECT COUNT(INFO_PROGRAM.PROGRAM_ID) 
    	FROM MI_CDS_INFO_PROGRAM INFO_PROGRAM
    	<include refid="getCheckUpcomingExpiryDateList_where" />
    </select>
    
	<sql id="getCheckUpcomingExpiryDateList_where">
		WHERE INFO_PROGRAM.PROGRAM_ID IN (
			SELECT DISTINCT(SCHEDULES.PROGRAM_ID) FROM MI_CDS_INFO_SCHEDULE SCHEDULES
			<if test="condition != null and condition.programGroupList != null and condition.programGroupList.size() > 0">
			, (SELECT PROGRAM_ID FROM MI_CDS_MAP_PROGRAM_GROUP WHERE 
				<foreach item="programGroupId" collection="condition.programGroupList" separator=" OR " >
            		GROUP_ID = #{programGroupId}
        		</foreach>) AS GROUP_LIST
        	</if>
			WHERE CAST(STOP_DATE AS DATE) &lt;= CAST(#{stopDate} AS DATE) <if test="condition != null and condition.programGroupList != null and condition.programGroupList.size() > 0">AND SCHEDULES.PROGRAM_ID = GROUP_LIST.PROGRAM_ID</if>
		)
		<if test="condition != null">
			<if test="condition.commonSearchKeyword != null and !condition.commonSearchKeyword.equals('')">
				AND PROGRAM_NAME = #{condition.commonSearchKeyword}
			</if>
			
			<if test="condition.src_name != null and condition.src_name != ''">
	            <bind name="srcUpper" value="_parameter.condition.src_name.toUpperCase()"/>
	            <bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
            	AND UPPER(INFO_PROGRAM.PROGRAM_NAME) 
            	LIKE #{srcNamePattern} 
        	</if>
        </if>
	</sql>
	
	<select id="getCheckUpcomingExpiryDatePlaylistList" parameterType="map" resultType="com.samsung.magicinfo.framework.content.entity.Playlist">
		SELECT PLAYLIST_INFO.*, VERSIONS.VERSION_ID, VERSIONS.PLAY_TIME, VERSIONS.TOTAL_SIZE
    	FROM MI_CMS_INFO_PLAYLIST PLAYLIST_INFO, MI_CMS_INFO_PLAYLIST_VERSION VERSIONS
    	<include refid="getCheckUpcomingExpiryDatePlaylistList_where" />
    	AND PLAYLIST_INFO.PLAYLIST_ID = VERSIONS.PLAYLIST_ID AND VERSIONS.IS_ACTIVE = 'Y'
		<if test="condition != null">
			<if test="condition.sort_name != null and condition.sort_name != '' and condition.order_dir != null and condition.order_dir != ''">
				<bind name="safe_sortUpper"	value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
				<bind name="safe_sortOrder"	value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
				ORDER BY ${safe_sortUpper} ${safe_sortOrder}
			</if>
		</if>
		<if test="pageSize != -1">
        	LIMIT #{pageSize}
        </if>
        <if test="startPos != -1">
        	OFFSET #{startPos}
        </if>
	</select>
	
	<select id="getCheckUpcomingExpiryDatePlaylistList" parameterType="map" resultType="com.samsung.magicinfo.framework.content.entity.Playlist" databaseId="mssql">
		SELECT * FROM (
	        <bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
	        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
	        <bind name="safe_sortUpper"	value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
			<bind name="safe_sortOrder"	value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
			
			SELECT PLAYLIST_INFO.*, VERSIONS.VERSION_ID, VERSIONS.PLAY_TIME, VERSIONS.TOTAL_SIZE, ROW_NUMBER() OVER(ORDER BY PLAYLIST_INFO.${safe_sortUpper} ${safe_sortOrder}) as RowNum
		    FROM MI_CMS_INFO_PLAYLIST PLAYLIST_INFO, MI_CMS_INFO_PLAYLIST_VERSION VERSIONS
		    <include refid="getCheckUpcomingExpiryDatePlaylistList_where" />
		    AND PLAYLIST_INFO.PLAYLIST_ID = VERSIONS.PLAYLIST_ID AND VERSIONS.IS_ACTIVE = 'Y'
		)as SubQuery
        WHERE 
            1 = 1
            <if test="safe_startPos > -1">
            and RowNum > ${safe_startPos}
            </if>
            <if test="safe_rownumLimit > -1">
            and RowNum &lt;= ${safe_rownumLimit}
            </if>        
        ORDER BY RowNum
	</select>
	
	<select id="getCheckUpcomingExpiryDatePlaylistCnt" resultType="int">
    	SELECT COUNT(PLAYLIST_INFO.PLAYLIST_ID) 
    	FROM MI_CMS_INFO_PLAYLIST PLAYLIST_INFO
    	<include refid="getCheckUpcomingExpiryDatePlaylistList_where" />
    </select>
	
	<sql id="getCheckUpcomingExpiryDatePlaylistList_where">
		WHERE PLAYLIST_INFO.PLAYLIST_ID IN (
			SELECT DISTINCT(INFO_SCHEDULE.CONTENT_ID) FROM MI_CDS_INFO_SCHEDULE INFO_SCHEDULE, MI_CMS_INFO_PLAYLIST_VERSION PLAYLIST_VERSION, MI_CMS_MAP_PLAYLIST_CONTENT MAP_PLAYLIST <if test="condition != null and condition.programGroupList != null and condition.programGroupList.size() > 0">, (SELECT PROGRAM_ID FROM MI_CDS_MAP_PROGRAM_GROUP WHERE <foreach item="programGroupId" collection="condition.programGroupList" separator=" OR " >GROUP_ID = #{programGroupId}</foreach> ) AS GROUP_LIST</if>
			WHERE CONTENT_TYPE = 'PLAYLIST' AND INFO_SCHEDULE.CONTENT_ID = PLAYLIST_VERSION.PLAYLIST_ID AND PLAYLIST_VERSION.PLAYLIST_ID = MAP_PLAYLIST.PLAYLIST_ID AND PLAYLIST_VERSION.IS_ACTIVE = 'Y' AND (MAP_PLAYLIST.EXPIRED_DATE IS NOT NULL AND MAP_PLAYLIST.EXPIRED_DATE &lt;= CAST(#{stopDate} AS DATE)) <if test="condition != null and condition.programGroupList != null and condition.programGroupList.size() > 0"> AND INFO_SCHEDULE.PROGRAM_ID = GROUP_LIST.PROGRAM_ID</if>
    		UNION 
    		SELECT DISTINCT(INFO_SCHEDULE.CONTENT_ID) FROM MI_CDS_INFO_SCHEDULE INFO_SCHEDULE, MI_CMS_INFO_PLAYLIST_VERSION PLAYLIST_VERSION, MI_CMS_MAP_PLAYLIST_TAG MAP_TAG <if test="condition != null and condition.programGroupList != null and condition.programGroupList.size() > 0">, (SELECT PROGRAM_ID FROM MI_CDS_MAP_PROGRAM_GROUP WHERE <foreach item="programGroupId" collection="condition.programGroupList" separator=" OR " >GROUP_ID = #{programGroupId}</foreach> ) AS GROUP_LIST</if>
			WHERE CONTENT_TYPE = 'PLAYLIST' AND INFO_SCHEDULE.CONTENT_ID = PLAYLIST_VERSION.PLAYLIST_ID AND PLAYLIST_VERSION.PLAYLIST_ID = MAP_TAG.PLAYLIST_ID AND PLAYLIST_VERSION.IS_ACTIVE = 'Y' AND (MAP_TAG.EXPIRED_DATE IS NOT NULL AND MAP_TAG.EXPIRED_DATE &lt;= CAST(#{stopDate} AS DATE)) <if test="condition != null and condition.programGroupList != null and condition.programGroupList.size() > 0"> AND INFO_SCHEDULE.PROGRAM_ID = GROUP_LIST.PROGRAM_ID</if>
		)
		<if test="condition != null">
			<if test="condition.src_name != null and condition.src_name != ''">
	            <bind name="srcUpper" value="_parameter.condition.src_name.toUpperCase()"/>
	            <bind name="srcNamePattern" value="'%' + srcUpper + '%'"/>
            	AND UPPER(PLAYLIST_INFO.PLAYLIST_NAME) 
            	LIKE #{srcNamePattern} 
        	</if>
        </if>
	</sql>
	
	<select id="getDeviceGroupIdFromMapProgramByProgramId" resultType="map">
		SELECT * 
		FROM MI_CDS_MAP_PROGRAM_DEVICE
		WHERE PROGRAM_ID = #{programId}
	</select>
	
	<select id="getProgramDeviceTypeByGroupId" resultType="int">
		SELECT PRIORITYS.PRIORITY
		FROM MI_DMS_INFO_DEVICE_PRIORITY PRIORITYS ,
		(
			SELECT PROGRAMS.DEVICE_TYPE, PROGRAMS.DEVICE_TYPE_VERSION
			FROM MI_CDS_MAP_PROGRAM_DEVICE GROUPS
			LEFT JOIN MI_CDS_INFO_PROGRAM PROGRAMS 
			ON GROUPS.PROGRAM_ID = PROGRAMS.PROGRAM_ID AND PROGRAMS.DESCRIPTION != 'Default Schedule'
			WHERE DEVICE_GROUP_ID = #{groupId}
		) DEVICES
		WHERE PRIORITYS.DEVICE_TYPE = DEVICES.DEVICE_TYPE AND PRIORITYS.DEVICE_TYPE_VERSION = DEVICES.DEVICE_TYPE_VERSION
	</select>
	
	<select id="checkFirstReceiveProgress" resultType="int">
		SELECT COUNT(DEVICE_ID) FROM MI_CDS_DOWNLOAD_STATUS WHERE DEVICE_ID = #{deviceId} AND PROGRESS != ''
	</select>
	
	<select id="getContentDownloadMode" resultType="java.lang.Integer">
		SELECT CONTENTS_DOWNLOAD_MODE FROM MI_DMS_INFO_DEVICE WHERE DEVICE_ID = #{deviceId} 
	</select>
	
	<select id="getTagFromDeviceId" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTag">
		SELECT A.*, B.TAG_NAME, B.TAG_TYPE, C.TAG_CONDITION FROM MI_TAG_MAP_DEVICE_TAG A 
		    LEFT JOIN MI_TAG_INFO_TAG B ON A.TAG_ID = B.TAG_ID
		    LEFT JOIN MI_TAG_INFO_TAG_CONDITION C ON A.TAG_CONDITION_ID = C.TAG_CONDITION_ID 
		WHERE DEVICE_ID = #{deviceId}
        <if test="isVarTag != null">
            <choose>
                <when test="isVarTag">
                    AND IS_VAR_TAG = <include refid="utils.true"/>
                </when>
                <otherwise>
                    AND (IS_VAR_TAG = <include refid="utils.false"/> OR IS_VAR_TAG IS NULL)
                </otherwise>
            </choose>
            
        </if>
	</select>
    
    <select id="getNonApprovalDeviceCount" resultType="int">
    	SELECT COUNT(DEVICE_ID) FROM MI_DMS_INFO_DEVICE WHERE IS_APPROVED = <include refid="utils.false"/>
    	<if test="deviceType != null and !deviceType.equals('')">
    		AND DEVICE_TYPE = #{deviceType}
    	</if>
    </select>
    
    <select id="getAllDeviceCountByDeviceTypeList" resultType="int">
    	SELECT COUNT(DEVICE_ID) FROM MI_DMS_INFO_DEVICE
    	<if test="deviceTypeList != null and deviceTypeList.size() > 0">
    		WHERE IS_APPROVED = <include refid="utils.true"/> AND 
    		<foreach item="deviceType" collection="deviceTypeList" open="(" separator=" OR " close=")">
				DEVICE_TYPE = #{deviceType}
			</foreach> 
    	</if>
    </select>
    
    <select id="getOrganiationByDeviceId" resultType="String">
		SELECT ORGANIZATION FROM MI_DMS_MAP_GROUP_DEVICE
		WHERE DEVICE_ID = #{deviceId}
    </select>
    
    <select id="getDeviceMinList" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device">
        SELECT *
        FROM MI_DMS_INFO_DEVICE WHERE DEVICE_ID IN
        <foreach item="deviceId" collection="deviceIdList" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </select>
    
    <update id="updateDeviceMapLocation">
        UPDATE MI_DMS_INFO_DEVICE SET MAP_LOCATION = #{location} 
        WHERE DEVICE_ID IN
        <foreach item="deviceId" collection="deviceIdList" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </update>
    
    <select id="getMapLocationByDeviceId" resultType="String">
		SELECT MAP_LOCATION FROM MI_DMS_INFO_DEVICE WHERE DEVICE_ID = #{deviceId}
	</select>
	
	<update id="updateDeviceMapLocationByLocation">
        UPDATE MI_DMS_INFO_DEVICE SET MAP_LOCATION = #{location} 
        WHERE MAP_LOCATION IN
        <foreach item="tmplocation" collection="locationList" open="(" separator="," close=")">
            #{tmplocation}
        </foreach>
    </update>
    
    <update id="addSboxVwtInfo">
    	UPDATE MI_DMS_INFO_DEVICE SET LED_VWT_ID = #{sboxVwtId}, LED_VWT_FILE_NAME = #{sboxVwtFileName} WHERE DEVICE_ID = #{deviceId}
    </update>
    
    <update id="deleteSboxVwtInfo">
    	UPDATE MI_DMS_INFO_DEVICE SET LED_VWT_ID = null, LED_VWT_FILE_NAME = null WHERE DEVICE_ID = #{deviceId}
    </update>
    
     <select id="getDisconnectedDeviceIdList" resultType="String">
    	SELECT DEVICE_ID FROM MI_DMS_INFO_DEVICE A WHERE IS_APPROVED = <include refid="utils.true"/>
    	<include refid="disconnectionMode" />
    </select>
    
     <update id="setRecommendPlayByDeviceId">
    	UPDATE MI_DMS_INFO_DEVICE SET RECOMMEND_PLAY = #{value} WHERE DEVICE_ID = #{deviceId}
    </update>
    
    <select id="getRecommendPlayByDeviceId" resultType="Boolean">
		SELECT RECOMMEND_PLAY FROM MI_DMS_INFO_DEVICE WHERE DEVICE_ID = #{deviceId}
	</select>
	
	<select id="getCntRecommendPlayDevice" resultType="Integer">
		SELECT COUNT(DEVICE_ID) FROM MI_DMS_INFO_DEVICE WHERE RECOMMEND_PLAY = <include refid="utils.true"/>
	</select>

    <select id="getCountDeviceAll" resultType="Integer">
        SELECT COUNT(DEVICES.DEVICE_ID)
        FROM MI_DMS_INFO_DEVICE DEVICES
        WHERE DEVICES.IS_CHILD = <include refid="utils.false"/>
        AND DEVICES.DEVICE_ID IN (
            SELECT DEVICE_ID FROM (
                <include refid="groupRecursiveQuery"/>
                SELECT DEVICE_ID
                FROM B, MI_DMS_MAP_GROUP_DEVICE MAPS <include refid="deviceGroupAuthFrom_C"/>
                WHERE B.GROUP_ID = MAPS.GROUP_ID AND MAPS.GROUP_ID != 999999 <include refid="deviceGroupAuthWhere_C"/>
            ) GROUPS
        )
    </select>

    <select id="getCountDeviceAll" resultType="Integer" databaseId="mssql">
        <include refid="groupRecursiveQuery"/>
        SELECT COUNT(DEVICES.DEVICE_ID)
        FROM MI_DMS_INFO_DEVICE DEVICES
        WHERE DEVICES.IS_CHILD = <include refid="utils.false"/>
        AND EXISTS (
            SELECT DEVICE_ID
            FROM B, MI_DMS_MAP_GROUP_DEVICE MAPS <include refid="deviceGroupAuthFrom_C"/>
            WHERE B.GROUP_ID = MAPS.GROUP_ID AND MAPS.GROUP_ID != 999999 AND DEVICES.DEVICE_ID = MAPS.DEVICE_ID <include refid="deviceGroupAuthWhere_C"/>
        )
    </select>
    
    <select id="getCountTimezoneNotSet" resultType="Integer">
    	SELECT COUNT(DEVICES.DEVICE_ID)
    	<include refid="getCountTimezoneNotSet_from"/>
    </select>
    
    <select id="getListTimezoneNotSet" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf">
    	SELECT 
			DEVICES.DEVICE_ID, DEVICES.DEVICE_TYPE, DEVICES.DEVICE_TYPE_VERSION, DEVICES.DEVICE_NAME,
			DEVICES.IP_ADDRESS, DEVICES.DISK_SPACE_AVAILABLE, DEVICES.DEVICE_MODEL_NAME, DEVICES.LOCATION,
			DEVICES.IS_CHILD, DEVICES.CHILD_CNT, DEVICES.LAST_CONNECTION_TIME, 
			DEVICE_GROUP.GROUP_ID, DEVICE_GROUP.GROUP_NAME,	GROUP_MAP.ORGANIZATION, DEVICES.CREATE_DATE    	
    	<include refid="getCountTimezoneNotSet_from"/>
    	<choose>
    		<when test="condition != null and condition.sort_name != null and condition.sort_name != '' and condition.order_dir != null and condition.order_dir != ''">
	    		<bind name="safe_sortUpper" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
	            <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
	            ORDER BY ${safe_sortUpper} ${safe_sortOrder}
    		</when>
    		<otherwise>
    			ORDER BY DEVICES.DEVICE_NAME
    		</otherwise>
    	</choose>
    	<if test="pageSize != -1">
        	LIMIT #{pageSize}
        </if>
        <if test="startPos != -1">
        	OFFSET #{startPos}
        </if>
    </select>
    
    <select id="getCountTimezoneNotSet" resultType="Integer" databaseId="mssql">
    	<include refid="groupRecursiveQuery"/>
    	SELECT COUNT(DEVICES.DEVICE_ID)
    	<include refid="getCountTimezoneNotSet_from"/>
    </select>
    
    <select id="getListTimezoneNotSet" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf" databaseId="mssql">
		<include refid="groupRecursiveQuery"/>
		SELECT * FROM (
	    	<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
	        <bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
	        SELECT * FROM (
		    	SELECT
					DEVICES.DEVICE_ID, DEVICES.DEVICE_TYPE, DEVICES.DEVICE_TYPE_VERSION, DEVICES.DEVICE_NAME,
					DEVICES.IP_ADDRESS, DEVICES.DISK_SPACE_AVAILABLE, DEVICES.DEVICE_MODEL_NAME, DEVICES.LOCATION,
					DEVICES.IS_CHILD, DEVICES.CHILD_CNT, DEVICES.LAST_CONNECTION_TIME, 
					DEVICE_GROUP.GROUP_ID, DEVICE_GROUP.GROUP_NAME,	GROUP_MAP.ORGANIZATION, DEVICES.CREATE_DATE,			    	
		    		ROW_NUMBER() OVER(
		    			<choose>
		    				<when test="condition != null and condition.sort_name != null and condition.sort_name != '' and condition.order_dir != null and condition.order_dir != ''">
					            <bind name="safe_sortUpper" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
					            <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
					            ORDER BY
					            <choose>
				                    <when test="safe_sortUpper == 'DEVICE_ID'">
				                        DEVICES.DEVICE_ID ${safe_sortOrder}
				                    </when>
				                    <otherwise>
					                    ${safe_sortUpper} ${safe_sortOrder}
				                    </otherwise>           
					            </choose>
		    				</when>
		    				<otherwise>
								ORDER BY DEVICES.DEVICE_NAME		    				
		    				</otherwise>
		    			</choose>
		    		) as RowNum
		    	<include refid="getCountTimezoneNotSet_from"/>
	    	) as A
	    ) as subQuery
        WHERE 
        1 = 1
        <if test="safe_startPos > -1">
        	and RowNum > ${safe_startPos}
        </if>
        <if test="safe_rownumLimit > -1">
        	and RowNum &lt;= ${safe_rownumLimit}
        </if>        
        ORDER BY RowNum
    </select>

    <sql id="getCountTimezoneNotSet_from">
        FROM 
			MI_DMS_INFO_DEVICE DEVICES 
			INNER JOIN MI_DMS_MAP_GROUP_DEVICE GROUP_MAP ON DEVICES.DEVICE_ID = GROUP_MAP.DEVICE_ID
			INNER JOIN MI_DMS_INFO_GROUP DEVICE_GROUP ON DEVICE_GROUP.GROUP_ID = GROUP_MAP.GROUP_ID 
        WHERE DEVICES.IS_CHILD = <include refid="utils.false"/> AND DEVICES.IS_APPROVED =  <include refid="utils.true"/> AND (TIME_ZONE_INDEX IS NULL OR TIME_ZONE_INDEX = '')
        AND DEVICES.DEVICE_ID IN (
            SELECT DEVICE_ID FROM (
                <include refid="groupRecursiveQuery"/>
                SELECT DEVICE_ID
                FROM B, MI_DMS_MAP_GROUP_DEVICE MAPS <include refid="deviceGroupAuthFrom_C"/>
                WHERE B.GROUP_ID = MAPS.GROUP_ID AND MAPS.GROUP_ID != 999999 <include refid="deviceGroupAuthWhere_C"/>
            ) GROUPS
        )
        AND DEVICES.DEVICE_TYPE != 'FLIP'
        <include refid="searchQuery" />
    </sql>

    <sql id="getCountTimezoneNotSet_from" databaseId="mssql">
        FROM 
			MI_DMS_INFO_DEVICE DEVICES 
			INNER JOIN MI_DMS_MAP_GROUP_DEVICE GROUP_MAP ON DEVICES.DEVICE_ID = GROUP_MAP.DEVICE_ID
			INNER JOIN MI_DMS_INFO_GROUP DEVICE_GROUP ON DEVICE_GROUP.GROUP_ID = GROUP_MAP.GROUP_ID        
        WHERE DEVICES.IS_CHILD = <include refid="utils.false"/> AND DEVICES.IS_APPROVED =  <include refid="utils.true"/> AND (TIME_ZONE_INDEX IS NULL OR TIME_ZONE_INDEX = '')
        AND DEVICES.DEVICE_ID IN (
            SELECT DEVICE_ID
            FROM B, MI_DMS_MAP_GROUP_DEVICE MAPS <include refid="deviceGroupAuthFrom_C"/>
            WHERE B.GROUP_ID = MAPS.GROUP_ID AND MAPS.GROUP_ID != 999999 <include refid="deviceGroupAuthWhere_C"/>
        )
        AND DEVICES.DEVICE_TYPE != 'FLIP'
        <include refid="searchQuery" />
    </sql>
    
    <select id="getCountInsufficientCapacity" resultType="Integer">
    	SELECT COUNT(DEVICES.DEVICE_ID)
    	<include refid="getCountInsufficientCapacity_from"/>
    </select>
    
    <select id="getCountInsufficientCapacity" resultType="Integer" databaseId="mssql">
    	<include refid="groupRecursiveQuery"/>
        SELECT COUNT(DEVICES.DEVICE_ID)
        <include refid="getCountInsufficientCapacity_from"/>
    </select>
    
	<select id="getListInsufficientCapacity" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf">
    	SELECT 
			DEVICES.DEVICE_ID, DEVICES.DEVICE_TYPE, DEVICES.DEVICE_TYPE_VERSION, DEVICES.DEVICE_NAME,
			DEVICES.IP_ADDRESS, DEVICES.DISK_SPACE_AVAILABLE, DEVICES.DEVICE_MODEL_NAME, DEVICES.LOCATION, 
			DEVICES.IS_CHILD, DEVICES.CHILD_CNT, DEVICES.LAST_CONNECTION_TIME, 
			DEVICE_GROUP.GROUP_ID, DEVICE_GROUP.GROUP_NAME,	GROUP_MAP.ORGANIZATION
    	<include refid="getCountInsufficientCapacity_from"/>
        <if test="condition != null and condition.sort_name != null and condition.sort_name != '' and condition.order_dir != null and condition.order_dir != ''">
            <bind name="safe_sortUpper" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
            <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
            ORDER BY ${safe_sortUpper} ${safe_sortOrder}
        </if>
        <if test="condition == null">
        	ORDER BY DEVICES.DEVICE_NAME
        </if>    	
    	<if test="pageSize != -1">
			LIMIT #{pageSize}
		</if>
		<if test="startPos != -1">
			OFFSET #{startPos}
		</if>
    </select>
    
    <select id="getListInsufficientCapacity" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf" databaseId="mssql">
    	<include refid="groupRecursiveQuery"/>
    	SELECT * FROM (
			<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
			<bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
			SELECT * FROM (
        		SELECT 
					DEVICES.DEVICE_ID, DEVICES.DEVICE_TYPE, DEVICES.DEVICE_TYPE_VERSION, DEVICES.DEVICE_NAME,
					DEVICES.IP_ADDRESS, DEVICES.DISK_SPACE_AVAILABLE, DEVICES.DEVICE_MODEL_NAME, DEVICES.LOCATION, 
					DEVICES.IS_CHILD, DEVICES.CHILD_CNT, DEVICES.LAST_CONNECTION_TIME, 
					DEVICE_GROUP.GROUP_ID, DEVICE_GROUP.GROUP_NAME,	GROUP_MAP.ORGANIZATION,        			
        			ROW_NUMBER() OVER(
        			<choose>
        				<when test="condition != null and condition.sort_name != null and condition.sort_name != '' and condition.order_dir != null and condition.order_dir != ''">
				            <bind name="safe_sortUpper" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
				            <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
				            ORDER BY
				            <choose>
			                    <when test="safe_sortUpper == 'DEVICE_ID'">
			                        DEVICES.DEVICE_ID ${safe_sortOrder}
			                    </when>
			                    <otherwise>
				                    ${safe_sortUpper} ${safe_sortOrder}
			                    </otherwise>                 
				            </choose>
        				</when>
        				<otherwise>
        					ORDER BY DEVICES.DEVICE_NAME
        				</otherwise>
        			</choose>
        		) as RowNum
        		<include refid="getCountInsufficientCapacity_from"/>
        	) AS A
        ) AS subQuery
        WHERE 
		1 = 1
		<if test="safe_startPos > -1">
			and RowNum > ${safe_startPos}
		</if>
		<if test="safe_rownumLimit > -1">
			and RowNum &lt;= ${safe_rownumLimit}
		</if>        
		ORDER BY RowNum
    </select>

    <sql id="getCountInsufficientCapacity_from">
        FROM 
        	MI_DMS_INFO_DEVICE DEVICES
			INNER JOIN MI_DMS_MAP_GROUP_DEVICE GROUP_MAP ON DEVICES.DEVICE_ID = GROUP_MAP.DEVICE_ID
			INNER JOIN MI_DMS_INFO_GROUP DEVICE_GROUP ON DEVICE_GROUP.GROUP_ID = GROUP_MAP.GROUP_ID
        WHERE DEVICES.IS_CHILD = <include refid="utils.false"/> AND DEVICES.IS_APPROVED =  <include refid="utils.true"/> 
        AND ( DISK_SPACE_REPOSITORY / (1024*1024)  &lt; <include refid="getInsufficientCapacity" /> )
        AND DEVICES.DEVICE_ID IN (
            SELECT DEVICE_ID FROM (
            <include refid="groupRecursiveQuery"/>
            SELECT DEVICE_ID
                FROM B, MI_DMS_MAP_GROUP_DEVICE MAPS <include refid="deviceGroupAuthFrom_C"/>
                WHERE B.GROUP_ID = MAPS.GROUP_ID AND MAPS.GROUP_ID != 999999 <include refid="deviceGroupAuthWhere_C"/>
            ) GROUPS
        )
        <include refid="searchQuery" />
    </sql>

    <sql id="getCountInsufficientCapacity_from" databaseId="mssql">
        FROM 
        	MI_DMS_INFO_DEVICE DEVICES
        	INNER JOIN MI_DMS_MAP_GROUP_DEVICE GROUP_MAP ON DEVICES.DEVICE_ID = GROUP_MAP.DEVICE_ID
        	INNER JOIN MI_DMS_INFO_GROUP DEVICE_GROUP ON DEVICE_GROUP.GROUP_ID = GROUP_MAP.GROUP_ID
        WHERE DEVICES.IS_CHILD = <include refid="utils.false"/> AND DEVICES.IS_APPROVED =  <include refid="utils.true"/> 
        AND ( DISK_SPACE_REPOSITORY / (1024*1024)  &lt; <include refid="getInsufficientCapacity" /> )
        AND DEVICES.DEVICE_ID IN (
            SELECT DEVICE_ID
            FROM B, MI_DMS_MAP_GROUP_DEVICE MAPS <include refid="deviceGroupAuthFrom_C"/>
            WHERE B.GROUP_ID = MAPS.GROUP_ID AND MAPS.GROUP_ID != 999999 <include refid="deviceGroupAuthWhere_C"/>
        )
        <include refid="searchQuery" />
    </sql>
    
    <select id="getCountScheduleNotPublish" resultType="Integer">
    	SELECT COUNT(DEVICES.DEVICE_ID)
    	<include refid="getCountScheduleNotPublish_from"/>
    </select>
    
    <select id="getCountScheduleNotPublish" resultType="Integer" databaseId="mssql">
    	<include refid="groupRecursiveQuery"/>
        SELECT COUNT(DEVICES.DEVICE_ID)
        <include refid="getCountScheduleNotPublish_from"/>
    </select>
    
    <select id="getListScheduleNotPublish" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf">
    	SELECT 
			DEVICES.DEVICE_ID, DEVICES.DEVICE_TYPE, DEVICES.DEVICE_TYPE_VERSION, DEVICES.DEVICE_NAME, 
			DEVICES.IP_ADDRESS, DEVICES.DISK_SPACE_AVAILABLE, DEVICES.DEVICE_MODEL_NAME, DEVICES.LOCATION,
			DEVICES.IS_CHILD, DEVICES.CHILD_CNT, DEVICES.LAST_CONNECTION_TIME, 
			DEVICE_GROUP.GROUP_ID, DEVICE_GROUP.GROUP_NAME, GROUP_MAP.ORGANIZATION, DEVICES.CREATE_DATE			
    	<include refid="getCountScheduleNotPublish_from"/>
        <if test="condition != null and condition.sort_name != null and condition.sort_name != '' and condition.order_dir != null and condition.order_dir != ''">
            <bind name="safe_sortUpper" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
            <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
            ORDER BY ${safe_sortUpper} ${safe_sortOrder}
        </if>
        <if test="condition == null">
        	ORDER BY DEVICES.DEVICE_NAME
        </if>
    	<if test="pageSize != -1">
			LIMIT #{pageSize}
		</if>
		<if test="startPos != -1">
			OFFSET #{startPos}
		</if>
    </select>
    
    <select id="getListScheduleNotPublish" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf" databaseId="mssql">
    	<include refid="groupRecursiveQuery"/>
    	SELECT * FROM (
			<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
			<bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
			SELECT * FROM (
	        	SELECT  
					DEVICES.DEVICE_ID, DEVICES.DEVICE_TYPE, DEVICES.DEVICE_TYPE_VERSION, DEVICES.DEVICE_NAME, 
					DEVICES.IP_ADDRESS, DEVICES.DISK_SPACE_AVAILABLE, DEVICES.DEVICE_MODEL_NAME, DEVICES.LOCATION,
					DEVICES.IS_CHILD, DEVICES.CHILD_CNT, DEVICES.LAST_CONNECTION_TIME, 
					DEVICE_GROUP.GROUP_ID, DEVICE_GROUP.GROUP_NAME, GROUP_MAP.ORGANIZATION,  DEVICES.CREATE_DATE, 					        	
	        		ROW_NUMBER() OVER(
	        			<choose>
	        				<when test="condition != null and condition.sort_name != null and condition.sort_name != '' and condition.order_dir != null and condition.order_dir != ''">
					            <bind name="safe_sortUpper" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
					            <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
					            ORDER BY
					            <choose>
					                    <when test="safe_sortUpper == 'DEVICE_ID'">
					                        DEVICES.DEVICE_ID ${safe_sortOrder}
					                    </when>
					                    <otherwise>
						                    ${safe_sortUpper} ${safe_sortOrder}
					                    </otherwise>                 
					            </choose>
	        				</when>
	        				<otherwise>
	        					ORDER BY DEVICES.DEVICE_NAME
	        				</otherwise>
	        			</choose>
	        		) as RowNum
        		<include refid="getCountScheduleNotPublish_from"/>
        	) as A
        ) as subQuery
        WHERE 
			1 = 1
			<if test="safe_startPos > -1">
				and RowNum > ${safe_startPos}
			</if>
			<if test="safe_rownumLimit > -1">
				and RowNum &lt;= ${safe_rownumLimit}
			</if>        
			ORDER BY RowNum
    </select>

    <sql id="getCountScheduleNotPublish_from">
        FROM MI_DMS_INFO_DEVICE DEVICES
			INNER JOIN MI_DMS_MAP_GROUP_DEVICE GROUP_MAP ON DEVICES.DEVICE_ID = GROUP_MAP.DEVICE_ID
			INNER JOIN MI_DMS_INFO_GROUP DEVICE_GROUP ON DEVICE_GROUP.GROUP_ID = GROUP_MAP.GROUP_ID        	
        WHERE 
        	GROUP_MAP.GROUP_ID IN (
	            SELECT 
	            	PGM_MAP.DEVICE_GROUP_ID
	            FROM 
					MI_CDS_MAP_PROGRAM_DEVICE PGM_MAP 
					INNER JOIN MI_CDS_INFO_PROGRAM PGM ON PGM.PROGRAM_ID = PGM_MAP.PROGRAM_ID
					<if test="isDeviceGroupAuth">
					LEFT JOIN MI_DMS_MAP_GROUP_USER AUTH ON AUTH.GROUP_ID = PGM_MAP.DEVICE_GROUP_ID
					</if>
	            WHERE 
	            	PGM.IS_DEFAULT = 'Y' AND PGM_MAP.DEVICE_GROUP_ID IN (
		                <include refid="groupRecursiveQuery"/>
	    	            SELECT B.GROUP_ID
	        	        FROM B
		            )
		            <if test="isDeviceGroupAuth">
		            AND AUTH.USER_ID = #{userId}
		            </if>
        )
        AND DEVICES.IS_CHILD = <include refid="utils.false"/> AND DEVICES.IS_APPROVED = <include refid="utils.true"/>
        <if test="condition != null and condition.rm_device_types != null">
        	AND DEVICES.DEVICE_TYPE NOT IN 
			<foreach item="item" index="index" collection="condition.rm_device_types" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
        <include refid="searchQuery" />
    </sql>

    <sql id="getCountScheduleNotPublish_from" databaseId="mssql">
		FROM MI_DMS_INFO_DEVICE DEVICES
			INNER JOIN MI_DMS_MAP_GROUP_DEVICE GROUP_MAP ON DEVICES.DEVICE_ID = GROUP_MAP.DEVICE_ID
			INNER JOIN MI_DMS_INFO_GROUP DEVICE_GROUP ON DEVICE_GROUP.GROUP_ID = GROUP_MAP.GROUP_ID
		WHERE 
			GROUP_MAP.GROUP_ID IN (
				SELECT 
					PGM_MAP.DEVICE_GROUP_ID AS GROUP_ID
				FROM
					B 
					INNER JOIN MI_CDS_MAP_PROGRAM_DEVICE PGM_MAP ON B.GROUP_ID = PGM_MAP.DEVICE_GROUP_ID
					INNER JOIN MI_CDS_INFO_PROGRAM PROGRAMS ON PGM_MAP.PROGRAM_ID = PROGRAMS.PROGRAM_ID
					<if test="isDeviceGroupAuth">
					LEFT JOIN MI_DMS_MAP_GROUP_USER AUTH ON AUTH.GROUP_ID = PGM_MAP.DEVICE_GROUP_ID
					</if>
				WHERE
					PROGRAMS.IS_DEFAULT = 'Y' 
					<if test="isDeviceGroupAuth">
						AND AUTH.USER_ID = #{userId}
					</if>						
			)
        AND DEVICES.IS_CHILD = <include refid="utils.false"/> AND DEVICES.IS_APPROVED = <include refid="utils.true"/> 
        <if test="condition != null and condition.rm_device_types != null">
        	AND DEVICES.DEVICE_TYPE NOT IN 
			<foreach item="item" index="index" collection="condition.rm_device_types" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>  
        <include refid="searchQuery" />
    </sql>

	<select id="getCountContentError" resultType="Integer">
		SELECT COUNT(DISTINCT DEVICES.DEVICE_ID)
		<include refid="getCountContentError_from" />
	</select>
	
	<select id="getCountContentError" resultType="Integer" databaseId="mssql">
		<include refid="groupRecursiveQuery"/>
		SELECT COUNT(DISTINCT DEVICES.DEVICE_ID)
		<include refid="getCountContentError_from" />
	</select>
	
	<select id="getListContentError" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf">
		SELECT
			DISTINCT DEVICES.DEVICE_ID, DEVICES.DEVICE_TYPE, DEVICES.DEVICE_TYPE_VERSION, DEVICES.DEVICE_NAME, 
			DEVICES.IP_ADDRESS, DEVICES.DISK_SPACE_AVAILABLE, DEVICES.DEVICE_MODEL_NAME, DEVICES.LOCATION,
			DEVICES.IS_CHILD, DEVICES.CHILD_CNT, DEVICES.LAST_CONNECTION_TIME, 
			DEVICE_GROUP.GROUP_ID, DEVICE_GROUP.GROUP_NAME, GROUP_MAP.ORGANIZATION, DEVICES.CREATE_DATE
		<include refid="getCountContentError_from" />
        <if test="condition != null and condition.sort_name != null and condition.sort_name != '' and condition.order_dir != null and condition.order_dir != ''">
            <bind name="safe_sortUpper" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
            <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
            ORDER BY ${safe_sortUpper} ${safe_sortOrder}
        </if>
        <if test="condition == null">
        	ORDER BY DEVICES.DEVICE_NAME
        </if>
		<if test="pageSize != -1">
			LIMIT #{pageSize}
		</if>
		<if test="startPos != -1">
			OFFSET #{startPos}
		</if>
	</select>
	
	<select id="getListContentError" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf" databaseId="mssql">
		<include refid="groupRecursiveQuery"/>
		SELECT * FROM (
			<bind name="safe_startPos" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos)" />
			<bind name="safe_rownumLimit" value="@com.samsung.common.utils.DaoTools@safeNumeric(startPos + pageSize)" />
			SELECT * FROM (
				SELECT
					DISTINCT DEVICES.DEVICE_ID, DEVICES.DEVICE_TYPE, DEVICES.DEVICE_TYPE_VERSION, DEVICES.DEVICE_NAME, 
					DEVICES.IP_ADDRESS, DEVICES.DISK_SPACE_AVAILABLE, DEVICES.DEVICE_MODEL_NAME, DEVICES.LOCATION,
					DEVICES.IS_CHILD, DEVICES.CHILD_CNT, DEVICES.LAST_CONNECTION_TIME, 
					DEVICE_GROUP.GROUP_ID, DEVICE_GROUP.GROUP_NAME,	GROUP_MAP.ORGANIZATION, DEVICES.CREATE_DATE, 			
					DENSE_RANK() OVER(
						<choose>
							<when test="condition != null and condition.sort_name != null and condition.sort_name != '' and condition.order_dir != null and condition.order_dir != ''">
					            <bind name="safe_sortUpper" value="@com.samsung.common.utils.DaoTools@safeTableOrColumnName(_parameter.condition.sort_name)" />
					            <bind name="safe_sortOrder" value="@com.samsung.common.utils.DaoTools@safeSortOrder(_parameter.condition.order_dir.toUpperCase())" />
					            ORDER BY
					            <choose>
					                    <when test="safe_sortUpper == 'DEVICE_ID'">
					                        DEVICES.DEVICE_ID ${safe_sortOrder}
					                    </when>
					                    <otherwise>
						                    ${safe_sortUpper} ${safe_sortOrder}
					                    </otherwise>                 
					            </choose>
							</when>
							<otherwise>
								ORDER BY DEVICES.DEVICE_NAME
							</otherwise>
						</choose>
					) as RowNum
					<include refid="getCountContentError_from" />
			) as A
		) as subQuery
		WHERE 
		1 = 1
		<if test="safe_startPos > -1">
			and RowNum > ${safe_startPos}
		</if>
		<if test="safe_rownumLimit > -1">
			and RowNum &lt;= ${safe_rownumLimit}
		</if>        
		ORDER BY RowNum
	</select>

    <sql id="getCountContentError_from">
		FROM MI_DMS_INFO_DEVICE DEVICES
			INNER JOIN MI_DMS_MAP_GROUP_DEVICE GROUP_MAP ON DEVICES.DEVICE_ID = GROUP_MAP.DEVICE_ID
			INNER JOIN MI_CDS_DOWNLOAD_STATUS STATUS ON DEVICES.DEVICE_ID = STATUS.DEVICE_ID
			INNER JOIN MI_DMS_INFO_GROUP DEVICE_GROUP ON DEVICE_GROUP.GROUP_ID = GROUP_MAP.GROUP_ID
		WHERE GROUP_MAP.GROUP_ID IN (
			SELECT 
				PGM_MAP.DEVICE_GROUP_ID AS GROUP_ID
			FROM 
				MI_CDS_MAP_PROGRAM_DEVICE PGM_MAP
				INNER JOIN MI_CDS_INFO_PROGRAM PGM ON PGM.PROGRAM_ID = PGM_MAP.PROGRAM_ID
				<if test="isDeviceGroupAuth">
				LEFT JOIN MI_DMS_MAP_GROUP_USER AUTH ON AUTH.GROUP_ID = PGM_MAP.DEVICE_GROUP_ID
				</if>
			WHERE PGM_MAP.DEVICE_GROUP_ID IN (
				<include refid="groupRecursiveQuery"/>
				SELECT GROUP_ID
				FROM B
			)
			AND PGM.IS_DEFAULT = 'N'
			<if test="isDeviceGroupAuth">
			AND AUTH.USER_ID = #{userId}
			</if>
		)
		AND DEVICES.IS_CHILD = <include refid="utils.false"/> AND DEVICES.IS_APPROVED = <include refid="utils.true"/> 
		AND (STATUS.PROGRESS IS NULL OR STATUS.PROGRESS != '100 %')
		<include refid="searchQuery" />
    </sql>

    <sql id="getCountContentError_from" databaseId="mssql">
		FROM MI_DMS_INFO_DEVICE DEVICES
			INNER JOIN MI_DMS_MAP_GROUP_DEVICE GROUP_MAP ON DEVICES.DEVICE_ID = GROUP_MAP.DEVICE_ID
			INNER JOIN MI_CDS_DOWNLOAD_STATUS STATUS ON DEVICES.DEVICE_ID = STATUS.DEVICE_ID
			INNER JOIN MI_DMS_INFO_GROUP DEVICE_GROUP ON DEVICE_GROUP.GROUP_ID = GROUP_MAP.GROUP_ID
		WHERE GROUP_MAP.GROUP_ID IN (
			SELECT 
				PGM_MAP.DEVICE_GROUP_ID AS GROUP_ID
			FROM 
				MI_CDS_MAP_PROGRAM_DEVICE PGM_MAP
				INNER JOIN MI_CDS_INFO_PROGRAM PGM ON PGM.PROGRAM_ID = PGM_MAP.PROGRAM_ID
				<if test="isDeviceGroupAuth">
				LEFT JOIN MI_DMS_MAP_GROUP_USER AUTH ON AUTH.GROUP_ID = PGM_MAP.DEVICE_GROUP_ID
				</if>
			WHERE PGM_MAP.DEVICE_GROUP_ID IN (
				SELECT GROUP_ID FROM B
			)
			AND PGM.IS_DEFAULT = 'N' 
			<if test="isDeviceGroupAuth">
			AND AUTH.USER_ID = #{userId}
			</if>			
		)
		AND DEVICES.IS_CHILD = <include refid="utils.false"/> AND DEVICES.IS_APPROVED = <include refid="utils.true"/> 
		AND (STATUS.PROGRESS IS NULL OR STATUS.PROGRESS != '100 %')
		<include refid="searchQuery" />
    </sql>

    <sql id="groupRecursiveQuery">
        WITH RECURSIVE B AS (
            SELECT GROUP_ID
            FROM MI_DMS_INFO_GROUP
            WHERE
            <choose>
                <when test="groupList != null">
                    <foreach item="group" collection="groupList" open="(" separator=" OR " close=")">
                        GROUP_ID = #{group.group_id}
                    </foreach>
                </when>
                <otherwise>
                    GROUP_ID = 999999
                </otherwise>
            </choose>
            UNION ALL
            SELECT CHILD_GROUP.GROUP_ID
            FROM MI_DMS_INFO_GROUP CHILD_GROUP
            JOIN B ON CHILD_GROUP.P_GROUP_ID = B.GROUP_ID
        )
    </sql>

    <sql id="groupRecursiveQuery" databaseId="mssql">
        WITH B AS (
            SELECT GROUP_ID
            FROM MI_DMS_INFO_GROUP
            WHERE
            <choose>
                <when test="groupList != null">
                    <foreach item="group" collection="groupList" open="(" separator=" OR " close=")">
                        GROUP_ID = #{group.group_id}
                    </foreach>
                </when>
                <otherwise>
                    GROUP_ID = 999999
                </otherwise>
            </choose>
            UNION ALL
            SELECT CHILD_GROUP.GROUP_ID
            FROM MI_DMS_INFO_GROUP CHILD_GROUP
            JOIN B ON CHILD_GROUP.P_GROUP_ID = B.GROUP_ID
        )
    </sql>

	
	<select id="getRmMonitoringList" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.RmMonitoring">
    	SELECT 
    		A.DEVICE_ID, 
    		A.LAST_CONNECTION_TIME,
    		A.RM_RULE_VERSION,
    		(SELECT COUNT(ID) FROM MI_FAULT_INFO_CLIENT WHERE DEVICE_ID = A.DEVICE_ID AND LEVEL = 'E' AND SERVER_TIME &gt;= #{errorStandardTime}) AS ERROR_COUNT,
    		(SELECT COUNT(ID) FROM MI_FAULT_INFO_CLIENT WHERE DEVICE_ID = A.DEVICE_ID AND (LEVEL = 'W' OR LEVEL = 'A' OR LEVEL = 'F') AND SERVER_TIME &gt;= #{errorStandardTime}) AS WARNING_COUNT
 		FROM 
   			MI_DMS_INFO_DEVICE A
   		WHERE A.DEVICE_ID IN
   		<foreach item="deviceId" collection="deviceIdList" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </select>
    
    <select id="getErrorList" resultType="com.samsung.magicinfo.framework.monitoring.entity.ClientFaultEntity">
    	SELECT A.ID, A.DEVICE_ID, A.CATEGORY, A.CODE, A.CLIENT_TIME, A.BODY_FORMAT, A.SERVER_TIME, B.ERROR_SCRIPT, C.RECOVERED,
    	(CASE A.LEVEL WHEN 'E' THEN 'ERROR' ELSE 'WARNING' END) AS LEVEL
    	FROM 
    		MI_FAULT_INFO_CLIENT A 
    		LEFT JOIN MI_FAULT_MAP_CODE B
    		ON (A.CODE = B.ERROR_CODE OR LEFT(A.CODE, 3) = B.ERROR_CODE)
    		LEFT JOIN MI_FAULT_INFO_ERROR_WARNING C 
    		ON A.DEVICE_ID = C.DEVICE_ID AND A.ID &gt;= C.FIRST_FAULT_ID AND A.ID &lt;= C.LAST_FAULT_ID
   		WHERE A.DEVICE_ID IN
   		<foreach item="deviceId" collection="deviceIdList" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
        <if test="type != null">
            <choose>
               <when test="type.equals('ERROR')">
                    AND A.LEVEL = 'E'
               </when>
               <when test="type.equals('WARNING')">
                    AND (A.LEVEL = 'W' OR A.LEVEL = 'A' OR A.LEVEL = 'F')
               </when>
            </choose>
		</if>
		<if test="status != null">
            <choose>
               <when test="status == 0">
                    AND C.RECOVERED = <include refid="utils.true"/>
               </when>
               <when test="status == 1">
                    AND C.RECOVERED = <include refid="utils.false"/>
               </when>
            </choose>
		</if>
        <if test="errorPeriod != null">
        	AND A.SERVER_TIME &gt;= #{errorPeriod}
        </if>
    </select>
    
    <select id="getMaxDeviceTypeVersion" resultType="map">
    	SELECT DEVICE_TYPE, MAX(DEVICE_TYPE_VERSION) AS MAX_DEVICE_VERSION FROM MI_DMS_INFO_DEVICE_PRIORITY GROUP BY DEVICE_TYPE
    </select>
    
    <select id="getDeviceIdListByCurrentContentIds" resultType="java.lang.String">
    	SELECT
    		A.DEVICE_ID
    	FROM
    		MI_DMS_INFO_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B
    	WHERE
    		A.DEVICE_ID = B.DEVICE_ID AND B.ORGANIZATION = #{orgName}
    		AND UPPER(A.CURR_CONTENT_ID) IN
    		<foreach item="contentId" collection="contentIdList" open="(" separator="," close=")">
            	#{contentId}
        	</foreach>
    </select>
    
    <select id="getCurrentContentIdByDeviceId" resultType="java.lang.String">
    	SELECT CURR_CONTENT_ID FROM MI_DMS_INFO_DEVICE WHERE DEVICE_ID = #{deviceId}
    </select>
    
    <select id="isDoneAtLast" resultType="java.lang.Boolean">
    	SELECT DONE FROM MI_DMS_TMP_PLAYING_DEFAULT_CONTENT_DEVICE WHERE DEVICE_ID = #{deviceId} ORDER BY TIME DESC LIMIT 1
    </select>
    
    <select id="isDoneAtLast" resultType="java.lang.Boolean" databaseId="mssql">
    	SELECT TOP 1 DONE FROM MI_DMS_TMP_PLAYING_DEFAULT_CONTENT_DEVICE WHERE DEVICE_ID = #{deviceId} ORDER BY TIME DESC
    </select>
    
    <select id="getAllNotDonePlayingDefaultContentHistory" resultType="map">
    	SELECT * FROM MI_DMS_TMP_PLAYING_DEFAULT_CONTENT_DEVICE WHERE DONE = <include refid="utils.false"/>
    </select>
    
    <select id="getPlayingDefaultContentHistory" resultType="map">
    	SELECT * FROM MI_DMS_TMP_PLAYING_DEFAULT_CONTENT_DEVICE WHERE DEVICE_ID = #{deviceId} ORDER BY TIME ASC
    </select>
    
    <insert id="addPlayingDefaultContentHistory">
    	INSERT INTO MI_DMS_TMP_PLAYING_DEFAULT_CONTENT_DEVICE (DEVICE_ID, TIME, ORGANIZATION, DONE) VALUES (#{deviceId}, CURRENT_TIMESTAMP, #{orgName}, <include refid="utils.false"/>)
    </insert>
    
    <update id="setPlayingDefaultContentDone">
    	UPDATE MI_DMS_TMP_PLAYING_DEFAULT_CONTENT_DEVICE SET DONE = <include refid="utils.true"/> WHERE DEVICE_ID = #{deviceId}
    </update>
    
    <delete id="deletePlayingDefaultContentHistoryByDeviceId">
    	DELETE FROM MI_DMS_TMP_PLAYING_DEFAULT_CONTENT_DEVICE WHERE DEVICE_ID = #{deviceId}
    </delete>
    
    <delete id="deletePlayingDefaultContentHistoryByOrganizationName">
    	DELETE FROM MI_DMS_TMP_PLAYING_DEFAULT_CONTENT_DEVICE WHERE ORGANIZATION = #{orgName}
    </delete>
    
    <select id="getPlayingDefaultContentHistoryList" resultType="map">
    	SELECT A.DEVICE_ID, A.TIME, A.ORGANIZATION, A.DONE, B.GROUP_ID
    	FROM MI_DMS_TMP_PLAYING_DEFAULT_CONTENT_DEVICE A, MI_DMS_MAP_GROUP_DEVICE B
    	WHERE A.ORGANIZATION = #{orgName}
    	AND    A.DEVICE_ID = B.DEVICE_ID 
    </select>
    
     <select id="getDeviceNameList" resultType="map">
        SELECT 
    		DISTINCT A.DEVICE_NAME
    	FROM 
    		MI_DMS_INFO_DEVICE A,
    		MI_DMS_MAP_GROUP_DEVICE B
   		WHERE A.DEVICE_ID = B.DEVICE_ID
   		<if test="organizationName != null">
   			AND B.ORGANIZATION = #{organizationName}
   		</if>
    </select>
    
    <sql id="getInsufficientCapacity" >
		( SELECT coalesce(INSUFFICIENT_CAPACITY, 1000) FROM MI_SYSTEM_INFO_SETUP WHERE ORGANIZATION_ID = 0 )
	</sql>

    <select id="getDevicesByGroupIds" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device">
        SELECT DEVICES.*
        FROM MI_DMS_INFO_DEVICE DEVICES
        LEFT JOIN MI_DMS_MAP_GROUP_DEVICE MAPS ON DEVICES.DEVICE_ID = MAPS.DEVICE_ID
        WHERE MAPS.GROUP_ID in
        <foreach item="groupId" index="index" collection="groupIds"
                 open="(" separator="," close=")">
            #{groupId}
        </foreach>
    </select>

    <select id="getDevicesByProgramId" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.Device">
        SELECT DEVICES.*, DEVICE_MAP.GROUP_ID
        FROM MI_DMS_INFO_DEVICE DEVICES
        INNER JOIN MI_DMS_MAP_GROUP_DEVICE DEVICE_MAP ON DEVICES.DEVICE_ID = DEVICE_MAP.DEVICE_ID
        INNER JOIN MI_CDS_MAP_PROGRAM_DEVICE PROGRAM_MAP ON DEVICE_MAP.GROUP_ID = PROGRAM_MAP.DEVICE_GROUP_ID
        WHERE PROGRAM_MAP.PROGRAM_ID = #{programId} AND IS_CHILD = <include refid="utils.false"/>
    </select>



    <select id="getDeviceCountByDeviceType" resultType="Map">
  		SELECT DEVICE_TYPE, CAST (DEVICE_TYPE_VERSION AS INTEGER) AS DEVICE_TYPE_VERSION, COUNT(DEVICE_ID) AS DEVICE_COUNT
		FROM MI_DMS_INFO_DEVICE AS D
		GROUP BY DEVICE_TYPE, DEVICE_TYPE_VERSION
	</select>


    <select id="getDeviceSbox" resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSboxResource">
        SELECT * FROM MI_DMS_INFO_SBOX
    </select>

</mapper>
