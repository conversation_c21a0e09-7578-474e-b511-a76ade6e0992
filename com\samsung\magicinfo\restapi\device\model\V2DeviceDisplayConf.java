package com.samsung.magicinfo.restapi.device.model;

import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf;
import com.samsung.magicinfo.openapi.annotation.CdataField;
import com.thoughtworks.xstream.annotations.XStreamOmitField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.lang.reflect.Field;

@ApiModel(
   description = "(timeCurrentTime) is based on server local time."
)
public class V2DeviceDisplayConf implements Serializable, Cloneable {
   @ApiModelProperty("Id of specific device")
   private String deviceId;
   @CdataField
   @ApiModelProperty("Name of specific device")
   private String deviceName;
   @ApiModelProperty("Model code of specific device")
   private String deviceModelCode;
   @ApiModelProperty("Model name of specific device")
   private String deviceModelName;
   @ApiModelProperty("Type of specific device")
   private String deviceType;
   @ApiModelProperty("Type version of specific device")
   private Float deviceTypeVersion;
   @ApiModelProperty("Genneral power of specific device")
   private String basicPower;
   @ApiModelProperty("Genneral volume of specific device")
   private Long basicVolume;
   @ApiModelProperty("Genneral mute of specific device")
   private Long basicMute;
   @ApiModelProperty("Genneral input source of specific device")
   private Long basicSource;
   @ApiModelProperty(
      example = "1;0;1;3;1;999",
      value = "Genneral direct channel of specific device.\r\nusage: [0~, country];[0~1, AtvDtv];[0~1, AirCable];[1~999, ch_num];[0~1, sel_minor];[0~999, min channel]"
   )
   private String basicDirectChannel;
   @ApiModelProperty("Genneral panel status of specific device")
   private Long basicPanelStatus;
   @ApiModelProperty("Checking the error condition")
   private String errorFlag;
   @ApiModelProperty("Network standby mode in advanced settings for specific devices")
   private String networkStandbyMode;
   @ApiModelProperty("Specialized PC video mode of specific device")
   private Long pvSplPictureMode;
   @ApiModelProperty("PC video mode of specific device")
   private Long pvMode;
   @ApiModelProperty("Contrast value for PC image of specific device")
   private Long pvContrast;
   @ApiModelProperty("Brightness value for PC video of specific device")
   private Long pvBrightness;
   @ApiModelProperty("Sharpness value for PC image of specific device")
   private Long pvSharpness;
   @ApiModelProperty("Color value for PC video of specific device")
   private Long pvColor;
   @ApiModelProperty("Color tint(Green/Red) value for PC video of specific device")
   private Long pvTint;
   @ApiModelProperty("Background color tone value for PC video of specific device")
   private Long pvColortone;
   @ApiModelProperty("Color temperature value for PC video of specific device")
   private Long pvColorTemperature;
   @ApiModelProperty("Screen size value for PC video of specific device")
   private Long pvSize;
   @ApiModelProperty("Screen noise reduction value for PC video of specific device")
   private Long pvDigitalnr;
   @ApiModelProperty("Film mode value for PC video of specific device")
   private Long pvFilmmode;
   @ApiModelProperty("Picture position size value for PC video of specific device")
   private String pvVideoPicturePositionSize;
   @ApiModelProperty("HDMI black level value for PC video of specific device")
   private Long pvHdmiBlackLevel;
   @ApiModelProperty("Codec of specific device checks noise filter for MPEG(Device type other than I PLAYER)")
   private Long pvMpegNoiseFilter;
   @ApiModelProperty("PC screen gamma of specific device (only I PLAYER device type)")
   private Long ppcGamma;
   @ApiModelProperty("PC screen HDMI black level of specific device (only I PLAYER device type)")
   private Long ppcHdmiBlackLevel;
   @ApiModelProperty("not used")
   private Long autoMotionPlus;
   @ApiModelProperty("not used")
   private Long autoMotionPlusJudderReduction;
   @ApiModelProperty("PC screen magic bright of specific device")
   private Long ppcMagicBright;
   @ApiModelProperty("PC screen contrast value of specific device")
   private Long ppcContrast;
   @ApiModelProperty("PC screen brightness of specific device")
   private Long ppcBrightness;
   @ApiModelProperty("PC screen colortone of specific device")
   private Long ppcColortone;
   @ApiModelProperty("PC screen colortone temperature of specific device")
   private Long ppcColorTemperature;
   @ApiModelProperty("PC screen Red of specific device")
   private Long ppcRed;
   @ApiModelProperty("PC screen Green of specific device")
   private Long ppcGreen;
   @ApiModelProperty("PC screen Blue of specific device")
   private Long ppcBlue;
   @ApiModelProperty("PC screen size of specific device")
   private Long ppcSize;
   @ApiModelProperty("Value for update time on mdc video wall type device")
   private String timeCurrentTime;
   @ApiModelProperty("not used")
   private String timeOnTime;
   @ApiModelProperty("not used")
   private String timeOffTime;
   @ApiModelProperty("Supported only on v1.0 (mobile) - (not supported on v2.0)")
   private Long pipSource;
   @ApiModelProperty("Supported only on v1.0 (mobile) - (not supported on v2.0)")
   private Long pipSize;
   @ApiModelProperty("Supported only on v1.0 (mobile) - (not supported on v2.0)")
   private Long pipPosition;
   @ApiModelProperty("Supported only on v1.0 (mobile) - (not supported on v2.0)")
   private Long pipSwap;
   @ApiModelProperty("Supported only on v1.0 (mobile) - (not supported on v2.0)")
   private Long pipControl;
   @ApiModelProperty("Sound mode for specific devices")
   private Long soundMode;
   @ApiModelProperty("Refer to POST /restapi/v2.0/rms/devices/display-info")
   private Long soundBass;
   @ApiModelProperty("Refer to POST /restapi/v2.0/rms/devices/display-info")
   private Long soundTreble;
   @ApiModelProperty("Refer to POST /restapi/v2.0/rms/devices/display-info")
   private Long soundBalance;
   @ApiModelProperty("not used")
   private Long soundSrs;
   @ApiModelProperty("not used")
   private Long soundEffect;
   @ApiModelProperty("Value of image coarse in picture info of specific device")
   private Long imageCoarse;
   @ApiModelProperty("Value of image good in picture info of specific device")
   private Long imageFine;
   @ApiModelProperty("Value of image horizontal position in picture info of specific device")
   private Long imageHpos;
   @ApiModelProperty("Value of image vertical position in picture info of specific device")
   private Long imageVpos;
   @ApiModelProperty("Value of image auto in picture info of specific device")
   private Long imageAuto;
   @ApiModelProperty("Sub signage device status in signal balance info of specific device")
   private Long sbStatus;
   @ApiModelProperty("RGB Red code value of subsignage device in signal balance info of specific device")
   private Long sbRgain;
   @ApiModelProperty("RGB Green code value of subsignage device in signal balance info of specific device")
   private Long sbGgain;
   @ApiModelProperty("RGB Blue code value of subsignage device in signal balance info of specific device")
   private Long sbBgain;
   @ApiModelProperty("RGB Red code value in signal balance info of a specific device")
   private Long sbROffset;
   @ApiModelProperty("RGB Green code value in signal balance info of a specific device")
   private Long sbGOffset;
   @ApiModelProperty("RGB Blue code value in signal balance info of a specific device")
   private Long sbBOffset;
   @ApiModelProperty("0~255")
   private Long sbGain;
   @ApiModelProperty("0~255")
   private Long sbSharp;
   @ApiModelProperty("Maintenance information for specific device (maximum time; maximum value; minimum time; minimum value)")
   private String mntAuto;
   @ApiModelProperty("Lamp setting for specific device (manual setting)")
   private Long mntManual;
   @ApiModelProperty("Video wall of automatic maintenance information for specific devicese")
   private Long mntVideoWall;
   @ApiModelProperty("not used")
   private Long mntFormat;
   @ApiModelProperty(
      example = "131;1;0;0;3;0;0 (Interval case)",
      value = "Set the screen protection timer for a specific device.\r\nusage:\r\n- Interval: [131(Scroll)/132(Pixel)/133(Bar)/134(Eraser)/144(Rolling Bar)/145(Fading Screen), Mode];[1~12, Start Time(hour)];[0~59, Start Time(min)];[0~1, PM/AM];[1~12, End Time(hour)];[0~59, End Time(min)];[0~1, PM/AM]\r\n- Repeat: [3(Scroll)/4(Pixel)/5(Bar)/6(Eraser)/9(All White)/10(Signal Pattern)/16(Rolling Bar)/17(Fading Screen), Mode];[0~10, Hour];[0~50, sec]\r\n- Off: 0;0;0"
   )
   private String mntSafetyScreenTimer;
   @ApiModelProperty("Instant display of specific devices")
   private Long mntSafetyScreenRun;
   @ApiModelProperty("Pixel of specific device (pixel horizontal; pixel vertical; pixel change time;")
   private String mntPixelShift;
   @ApiModelProperty("not used")
   private Long mntSafetyLock;
   @ApiModelProperty("Refresh rj45 settings in advanced settings for specific devices")
   private Long advancedRj45SettingRefresh;
   @ApiModelProperty(
      example = "1;0;1;1;0",
      value = "OSD display type in the advanced settings of a specific device.\r\nusage: [0(Off)/1(On), Source OSD];[0(Off)/1(On), Not Optimimum Mode OSD];[0(Off)/1(On), No Signal OSD];[0(Off)/1(On), MDC OSD];[0(Off)/1(On), Download Status Message]"
   )
   private String advancedOsdDisplayType;
   @ApiModelProperty("Value for OSD display type in the advanced settings of a specific device")
   private String advancedOsdDisplayTypeValue;
   @ApiModelProperty("Fan control in advanced settings for specific devices")
   private Long advancedFanControl;
   @ApiModelProperty("Fan speed in advanced settings for specific devices")
   private Long advancedFanSpeed;
   @ApiModelProperty("Restart from advanced settings on a specific device")
   private Long advancedReset;
   @ApiModelProperty("Auto power in advanced settings for specific devices")
   private Long advancedAutoPower;
   @ApiModelProperty("Auto color in advanced settings for specific devices")
   private Long advancedUserAutoColor;
   @ApiModelProperty("Network standby mode in the advanced settings of a specific device")
   private Long advancedStandBy;
   @ApiModelProperty("Hibernate in certain device advanced settings")
   private Long maxPowerSaving;
   @ApiModelProperty("Limit the brightness of a specific device")
   private Long brightnessLimit;
   @ApiModelProperty("not used")
   private Long touchControlLock;
   @ApiModelProperty("Automatic switching of sources for specific devices")
   private String autoSourceSwitching;
   @ApiModelProperty("Web browser URL of a specific device")
   private String webBrowserUrl;
   @ApiModelProperty("Custom logo for specific devices")
   private String customLogo;
   @ApiModelProperty("Freeze the screen of specific device")
   private Long screenFreeze;
   @ApiModelProperty("Mute the screen of specific device")
   private Long screenMute;
   @ApiModelProperty("Locking the remote control of specific device)")
   private Long miscRemocon;
   @ApiModelProperty("Panel lock on specific devices")
   private Long miscPanelLock;
   @ApiModelProperty("OSD of specific device")
   private Long miscOsd;
   @ApiModelProperty("Lock all keys on a specific device")
   private Long miscAllLock;
   @ApiModelProperty("Display status of specific device")
   private String diagnosisDisplayStatus;
   @ApiModelProperty("Monitor temperature for specific devices")
   private Long diagnosisMonitorTemperature;
   @ApiModelProperty("Alarm temperature for specific devices")
   private Long diagnosisAlarmTemperature;
   @ApiModelProperty("Panel time for a specific device")
   private String diagnosisPanelOnTime;
   @ApiModelProperty("Internal temperature sensor for a specific device")
   private Float sensorInternalTemperature;
   @ApiModelProperty("Humidity inside the sensor of a specific device")
   private Float sensorInternalHumidity;
   @ApiModelProperty("Sensor environmental temperature for a specific device")
   private Float sensorEnvironmentTemperature;
   @ApiModelProperty("Sensor front glass temperature for specific device")
   private Float sensorFrontglassTemperature;
   @ApiModelProperty("Sensor front glass humidity for specific device")
   private Float sensorFrontglassHumidity;
   @ApiModelProperty("Check schedule channel of a specific device")
   private Long chkSchChannel;
   @ApiModelProperty("Whether a specific device has a webcam")
   private Boolean webcam;
   @ApiModelProperty("Id of specific device of video wall type")
   private String vwtId;
   @ApiModelProperty("Check the display of software error icon for specific device")
   private Long iconErrorSw;
   @ApiModelProperty("Check the display of hardware error icon for specific device")
   private Long iconErrorHw;
   @ApiModelProperty("Check the display of alarm icon for specific device")
   private Long iconAlarm;
   @ApiModelProperty("Check the display of process content download icon for specific device.")
   private Long iconProcessContentDownload;
   @ApiModelProperty("Check the display of process log icon for specific device.")
   private Long iconProcessLog;
   @ApiModelProperty("Check the display of process software download icon for specific device.")
   private Long iconProcessSwDownload;
   @ApiModelProperty("Check the display of memo icon for specific device")
   private Long iconMemo;
   @ApiModelProperty("Check the display of backup icon for a specific device")
   private Long iconBackup;
   @ApiModelProperty("Automatic brightness of specific devices")
   private String autoBrightness;
   @ApiModelProperty("Sub-device alarm temperature for a specific device")
   private Long childAlarmTemperature;
   @ApiModelProperty("Black tone adjustment value for specific device")
   private Long blackTone;
   @ApiModelProperty("Flash tone adjustment value for specific device")
   private Long fleshTone;
   @ApiModelProperty("RGB color mode for specific devices")
   private Long rgbOnlyMode;
   @ApiModelProperty("Improved automatic image quality for specific devices")
   private Long pictureEnhancer;
   @ApiModelProperty("Range of color reproduction for specific devices")
   private String colorSpace;
   @ApiModelProperty("On screen display(OSD) menu size of a specific device")
   private Long osdMenuSize;
   @ApiModelProperty("LED picture size for specific devices")
   private String ledPictureSize;
   @ApiModelProperty("Value of LED HDR for specific devices")
   private String ledHdr;
   @ApiModelProperty("Value of LED HDR for specific devices")
   private Long ledHdrDre;
   @ApiModelProperty("Eco sensor for specific devices")
   private Long ecoSensor;
   @ApiModelProperty("Minimum brightness of a specific device")
   private Long minBrightness;
   @ApiModelProperty("Live streaming mode on specific devices")
   private Long liveMode;
   @ApiModelProperty("Display output mode of specific device")
   private Long displayOutputMode;
   @ApiModelProperty("Cleanup user data interval of a specific device")
   private Boolean cleanupUserData;
   @ApiModelProperty("Cleanup user data interval of a specific device")
   private Long cleanupUserDataInterval;
   @ApiModelProperty("Value for automatic save of a specific device")
   private Long autoSave;
   @ApiModelProperty("Value for auto power off of specific device")
   private Long autoPowerOff;
   @ApiModelProperty("Value for smtp setting of specific device")
   private String smtp;
   @ApiModelProperty("Value for print server of specific device")
   private String printServer;
   @XStreamOmitField
   @ApiModelProperty("Number of sub-devices for specific device")
   private Long childCnt;
   @XStreamOmitField
   @ApiModelProperty("Count of connected sub-devices for a specific device")
   private Long connChildCnt;
   @XStreamOmitField
   @ApiModelProperty("Check the sub-device of specific device")
   private Boolean isChild;
   @XStreamOmitField
   @ApiModelProperty("Check the sub-device of Ledbox type device")
   private Boolean hasChild;
   @ApiModelProperty("Check the video wall mode of a specific device")
   private String vwlMode;
   @ApiModelProperty("Video wall Layout position of specific device")
   private String vwlPosition;
   @ApiModelProperty("Video wall Layout format of specific device")
   private String vwlFormat;
   @ApiModelProperty("Video wall Layout of specific device")
   private String vwlLayout;
   @ApiModelProperty("led dimming install environment")
   private Long installEnvironment;
   @ApiModelProperty("led dimming dehumidify")
   private Long dehumidify;
   @ApiModelProperty("led dimming dehumidify progress")
   private String dehumidifyProgress;
   @ApiModelProperty("led dimming option")
   private Long dimmingOption;
   @ApiModelProperty("led dimming night time override")
   private Long dimmingNightTimeOverride;
   @ApiModelProperty("led dimming eco sensor")
   private String dimmingEcoSensor;
   @ApiModelProperty("led dimming sunrise sunset")
   private String dimmingSunriseSunset;
   @ApiModelProperty("led dimming sunrise sunset times")
   private String dimmingSunriseSunsetTimes;
   @ApiModelProperty("led dimming brightness output")
   private String dimmingBrightnessOutput;

   public V2DeviceDisplayConf() {
      super();
   }

   public V2DeviceDisplayConf(DeviceDisplayConf conf) {
      super();
      this.deviceId = conf.getDevice_id();
      this.deviceName = conf.getDevice_name();
      this.deviceModelCode = conf.getDevice_model_code();
      this.deviceModelName = conf.getDevice_model_name();
      this.deviceType = conf.getDevice_type();
      this.deviceTypeVersion = conf.getDevice_type_version();
      this.basicPower = conf.getBasic_power();
      this.basicVolume = conf.getBasic_volume();
      this.basicMute = conf.getBasic_mute();
      this.basicSource = conf.getBasic_source();
      this.basicDirectChannel = conf.getBasic_direct_channel();
      this.basicPanelStatus = conf.getBasic_panel_status();
      this.errorFlag = conf.getError_flag();
      this.networkStandbyMode = conf.getNetwork_standby_mode();
      this.pvSplPictureMode = conf.getSpecialized_picture_mode();
      this.pvMode = conf.getPv_mode();
      this.pvContrast = conf.getPv_contrast();
      this.pvBrightness = conf.getPv_brightness();
      this.pvSharpness = conf.getPv_sharpness();
      this.pvColor = conf.getPv_color();
      this.pvTint = conf.getPv_tint();
      this.pvColortone = conf.getPv_colortone();
      this.pvColorTemperature = conf.getPv_color_temperature();
      this.pvSize = conf.getPv_size();
      this.pvDigitalnr = conf.getPv_digitalnr();
      this.pvFilmmode = conf.getPv_filmmode();
      this.pvVideoPicturePositionSize = conf.getPv_video_picture_position_size();
      this.pvHdmiBlackLevel = conf.getPv_hdmi_black_level();
      this.pvMpegNoiseFilter = conf.getPv_mpeg_noise_filter();
      this.ppcGamma = conf.getPpc_gamma();
      this.ppcHdmiBlackLevel = conf.getPpc_hdmi_black_level();
      this.autoMotionPlus = conf.getAuto_motion_plus();
      this.autoMotionPlusJudderReduction = conf.getAuto_motion_plus_judder_reduction();
      this.ppcMagicBright = conf.getPpc_magic_bright();
      this.ppcContrast = conf.getPpc_contrast();
      this.ppcBrightness = conf.getPpc_brightness();
      this.ppcColortone = conf.getPpc_colortone();
      this.ppcColorTemperature = conf.getPpc_color_temperature();
      this.ppcRed = conf.getPpc_red();
      this.ppcGreen = conf.getPpc_green();
      this.ppcBlue = conf.getPpc_blue();
      this.ppcSize = conf.getPpc_size();
      this.timeCurrentTime = conf.getTime_current_time();
      this.timeOnTime = conf.getTime_on_time();
      this.timeOffTime = conf.getTime_off_time();
      this.pipSource = conf.getPip_source();
      this.pipSize = conf.getPip_size();
      this.pipPosition = conf.getPip_position();
      this.pipSwap = conf.getPip_swap();
      this.pipControl = conf.getPip_control();
      this.soundMode = conf.getSound_mode();
      this.soundBass = conf.getSound_bass();
      this.soundTreble = conf.getSound_treble();
      this.soundBalance = conf.getSound_balance();
      this.soundSrs = conf.getSound_srs();
      this.soundEffect = conf.getSound_effect();
      this.imageCoarse = conf.getImage_coarse();
      this.imageFine = conf.getImage_fine();
      this.imageHpos = conf.getImage_hpos();
      this.imageVpos = conf.getImage_vpos();
      this.imageAuto = conf.getImage_auto();
      this.sbStatus = conf.getSb_status();
      this.sbRgain = conf.getSb_rgain();
      this.sbGgain = conf.getSb_ggain();
      this.sbBgain = conf.getSb_bgain();
      this.sbROffset = conf.getSb_r_offset();
      this.sbGOffset = conf.getSb_g_offset();
      this.sbBOffset = conf.getSb_b_offset();
      this.sbGain = conf.getSb_gain();
      this.sbSharp = conf.getSb_sharp();
      this.mntAuto = conf.getMnt_auto();
      this.mntManual = conf.getMnt_manual();
      this.mntVideoWall = conf.getMnt_video_wall();
      this.mntFormat = conf.getMnt_format();
      this.mntSafetyScreenTimer = conf.getMnt_safety_screen_timer();
      this.mntSafetyScreenRun = conf.getMnt_safety_screen_run();
      this.mntPixelShift = conf.getMnt_pixel_shift();
      this.mntSafetyLock = conf.getMnt_safety_lock();
      this.advancedRj45SettingRefresh = conf.getAdvanced_rj45_setting_refresh();
      this.advancedOsdDisplayType = conf.getAdvanced_osd_display_type();
      this.advancedOsdDisplayTypeValue = conf.getAdvanced_osd_display_type_value();
      this.advancedFanControl = conf.getAdvanced_fan_control();
      this.advancedFanSpeed = conf.getAdvanced_fan_speed();
      this.advancedReset = conf.getAdvanced_reset();
      this.advancedAutoPower = conf.getAdvanced_auto_power();
      this.advancedUserAutoColor = conf.getAdvanced_user_auto_color();
      this.advancedStandBy = conf.getAdvanced_stand_by();
      this.maxPowerSaving = conf.getMax_power_saving();
      this.brightnessLimit = conf.getBrightness_limit();
      this.touchControlLock = conf.getTouch_control_lock();
      this.autoSourceSwitching = conf.getAuto_source_switching();
      this.webBrowserUrl = conf.getWeb_browser_url();
      this.customLogo = conf.getCustom_logo();
      this.screenFreeze = conf.getScreen_freeze();
      this.screenMute = conf.getScreen_mute();
      this.miscRemocon = conf.getMisc_remocon();
      this.miscPanelLock = conf.getMisc_panel_lock();
      this.miscOsd = conf.getMisc_osd();
      this.miscAllLock = conf.getMisc_all_lock();
      this.diagnosisDisplayStatus = conf.getDiagnosis_display_status();
      this.diagnosisMonitorTemperature = conf.getDiagnosis_monitor_temperature();
      this.diagnosisAlarmTemperature = conf.getDiagnosis_alarm_temperature();
      this.diagnosisPanelOnTime = conf.getDiagnosis_panel_on_time();
      this.sensorInternalTemperature = conf.getSensor_internal_temperature();
      this.sensorInternalHumidity = conf.getSensor_internal_humidity();
      this.sensorEnvironmentTemperature = conf.getSensor_environment_temperature();
      this.sensorFrontglassTemperature = conf.getSensor_frontglass_temperature();
      this.sensorFrontglassHumidity = conf.getSensor_frontglass_humidity();
      this.chkSchChannel = conf.getChkSchChannel();
      this.webcam = conf.getWebcam();
      this.vwtId = conf.getVwt_id();
      this.iconErrorSw = conf.getIcon_error_hw();
      this.iconErrorHw = conf.getIcon_error_sw();
      this.iconAlarm = conf.getIcon_alarm();
      this.iconProcessContentDownload = conf.getIcon_process_content_download();
      this.iconProcessLog = conf.getIcon_process_log();
      this.iconProcessSwDownload = conf.getIcon_process_sw_download();
      this.iconMemo = conf.getIcon_memo();
      this.iconBackup = conf.getIcon_backup();
      this.autoBrightness = conf.getAuto_brightness();
      this.childAlarmTemperature = conf.getChild_alarm_temperature();
      this.blackTone = conf.getBlack_tone();
      this.fleshTone = conf.getFlesh_tone();
      this.rgbOnlyMode = conf.getRgb_only_mode();
      this.pictureEnhancer = conf.getPicture_enhancer();
      this.colorSpace = conf.getColor_space();
      this.osdMenuSize = conf.getOsd_menu_size();
      this.ledPictureSize = conf.getLed_picture_size();
      this.ledHdr = conf.getLed_hdr();
      this.ledHdrDre = conf.getLed_hdr_dre();
      this.ecoSensor = conf.getEco_sensor();
      this.minBrightness = conf.getMin_brightness();
      this.liveMode = conf.getLive_mode();
      this.displayOutputMode = conf.getDisplay_output_mode();
      this.cleanupUserData = conf.getCleanup_user_data();
      this.cleanupUserDataInterval = conf.getCleanup_user_data_interval();
      this.autoSave = conf.getAuto_save();
      this.autoPowerOff = conf.getAuto_power_off();
      this.smtp = conf.getSmtp();
      this.printServer = conf.getPrint_server();
      this.childCnt = conf.getChild_cnt();
      this.connChildCnt = conf.getConn_child_cnt();
      this.isChild = conf.getIs_child();
      this.hasChild = conf.getHas_child();
      this.vwlMode = conf.getVwl_mode();
      this.vwlPosition = conf.getVwl_position();
      this.vwlFormat = conf.getVwl_format();
      this.vwlLayout = conf.getVwl_layout();
      this.installEnvironment = conf.getInstall_environment();
      this.dimmingOption = conf.getDimming_option();
      this.dimmingNightTimeOverride = conf.getDimming_night_time_override();
      this.dimmingEcoSensor = conf.getDimming_eco_sensor();
      this.dimmingSunriseSunset = conf.getDimming_sunrise_sunset();
      this.dimmingSunriseSunsetTimes = conf.getDimming_sunrise_sunset_times();
      this.dimmingBrightnessOutput = conf.getDimming_brightness_output();
   }

   public String getDeviceId() {
      return this.deviceId;
   }

   public void setDeviceId(String deviceId) {
      this.deviceId = deviceId;
   }

   public String getDeviceName() {
      return this.deviceName;
   }

   public void setDeviceName(String deviceName) {
      this.deviceName = deviceName;
   }

   public String getDeviceModelCode() {
      return this.deviceModelCode;
   }

   public void setDeviceModelCode(String deviceModelCode) {
      this.deviceModelCode = deviceModelCode;
   }

   public String getDeviceModelName() {
      return this.deviceModelName;
   }

   public void setDeviceModelName(String deviceModelName) {
      this.deviceModelName = deviceModelName;
   }

   public String getDeviceType() {
      return this.deviceType;
   }

   public void setDeviceType(String deviceType) {
      this.deviceType = deviceType;
   }

   public Float getDeviceTypeVersion() {
      return this.deviceTypeVersion;
   }

   public void setDeviceTypeVersion(Float deviceTypeVersion) {
      this.deviceTypeVersion = deviceTypeVersion;
   }

   public String getBasicPower() {
      return this.basicPower;
   }

   public void setBasicPower(String basicPower) {
      this.basicPower = basicPower;
   }

   public Long getBasicVolume() {
      return this.basicVolume;
   }

   public void setBasicVolume(Long basicVolume) {
      this.basicVolume = basicVolume;
   }

   public Long getBasicMute() {
      return this.basicMute;
   }

   public void setBasicMute(Long basicMute) {
      this.basicMute = basicMute;
   }

   public Long getBasicSource() {
      return this.basicSource;
   }

   public void setBasicSource(Long basicSource) {
      this.basicSource = basicSource;
   }

   public String getBasicDirectChannel() {
      return this.basicDirectChannel;
   }

   public void setBasicDirectChannel(String basicDirectChannel) {
      this.basicDirectChannel = basicDirectChannel;
   }

   public Long getBasicPanelStatus() {
      return this.basicPanelStatus;
   }

   public void setBasicPanelStatus(Long basicPanelStatus) {
      this.basicPanelStatus = basicPanelStatus;
   }

   public String getErrorFlag() {
      return this.errorFlag;
   }

   public void setErrorFlag(String errorFlag) {
      this.errorFlag = errorFlag;
   }

   public String getNetworkStandbyMode() {
      return this.networkStandbyMode;
   }

   public void setNetworkStandbyMode(String networkStandbyMode) {
      this.networkStandbyMode = networkStandbyMode;
   }

   public Long getPvSplPictureMode() {
      return this.pvSplPictureMode;
   }

   public void setPvSplPictureMode(Long pvSplPictureMode) {
      this.pvSplPictureMode = pvSplPictureMode;
   }

   public Long getPvMode() {
      return this.pvMode;
   }

   public void setPvMode(Long pvMode) {
      this.pvMode = pvMode;
   }

   public Long getPvContrast() {
      return this.pvContrast;
   }

   public void setPvContrast(Long pvContrast) {
      this.pvContrast = pvContrast;
   }

   public Long getPvBrightness() {
      return this.pvBrightness;
   }

   public void setPvBrightness(Long pvBrightness) {
      this.pvBrightness = pvBrightness;
   }

   public Long getPvSharpness() {
      return this.pvSharpness;
   }

   public void setPvSharpness(Long pvSharpness) {
      this.pvSharpness = pvSharpness;
   }

   public Long getPvColor() {
      return this.pvColor;
   }

   public void setPvColor(Long pvColor) {
      this.pvColor = pvColor;
   }

   public Long getPvTint() {
      return this.pvTint;
   }

   public void setPvTint(Long pvTint) {
      this.pvTint = pvTint;
   }

   public Long getPvColortone() {
      return this.pvColortone;
   }

   public void setPvColortone(Long pvColortone) {
      this.pvColortone = pvColortone;
   }

   public Long getPvColorTemperature() {
      return this.pvColorTemperature;
   }

   public void setPvColorTemperature(Long pvColorTemperature) {
      this.pvColorTemperature = pvColorTemperature;
   }

   public Long getPvSize() {
      return this.pvSize;
   }

   public void setPvSize(Long pvSize) {
      this.pvSize = pvSize;
   }

   public Long getPvDigitalnr() {
      return this.pvDigitalnr;
   }

   public void setPvDigitalnr(Long pvDigitalnr) {
      this.pvDigitalnr = pvDigitalnr;
   }

   public Long getPvFilmmode() {
      return this.pvFilmmode;
   }

   public void setPvFilmmode(Long pvFilmmode) {
      this.pvFilmmode = pvFilmmode;
   }

   public String getPvVideoPicturePositionSize() {
      return this.pvVideoPicturePositionSize;
   }

   public void setPvVideoPicturePositionSize(String pvVideoPicturePositionSize) {
      this.pvVideoPicturePositionSize = pvVideoPicturePositionSize;
   }

   public Long getPvHdmiBlackLevel() {
      return this.pvHdmiBlackLevel;
   }

   public void setPvHdmiBlackLevel(Long pvHdmiBlackLevel) {
      this.pvHdmiBlackLevel = pvHdmiBlackLevel;
   }

   public Long getPvMpegNoiseFilter() {
      return this.pvMpegNoiseFilter;
   }

   public void setPvMpegNoiseFilter(Long pvMpegNoiseFilter) {
      this.pvMpegNoiseFilter = pvMpegNoiseFilter;
   }

   public Long getPpcGamma() {
      return this.ppcGamma;
   }

   public void setPpcGamma(Long ppcGamma) {
      this.ppcGamma = ppcGamma;
   }

   public Long getPpcHdmiBlackLevel() {
      return this.ppcHdmiBlackLevel;
   }

   public void setPpcHdmiBlackLevel(Long ppcHdmiBlackLevel) {
      this.ppcHdmiBlackLevel = ppcHdmiBlackLevel;
   }

   public Long getAutoMotionPlus() {
      return this.autoMotionPlus;
   }

   public void setAutoMotionPlus(Long autoMotionPlus) {
      this.autoMotionPlus = autoMotionPlus;
   }

   public Long getAutoMotionPlusJudderReduction() {
      return this.autoMotionPlusJudderReduction;
   }

   public void setAutoMotionPlusJudderReduction(Long autoMotionPlusJudderReduction) {
      this.autoMotionPlusJudderReduction = autoMotionPlusJudderReduction;
   }

   public Long getPpcMagicBright() {
      return this.ppcMagicBright;
   }

   public void setPpcMagicBright(Long ppcMagicBright) {
      this.ppcMagicBright = ppcMagicBright;
   }

   public Long getPpcContrast() {
      return this.ppcContrast;
   }

   public void setPpcContrast(Long ppcContrast) {
      this.ppcContrast = ppcContrast;
   }

   public Long getPpcBrightness() {
      return this.ppcBrightness;
   }

   public void setPpcBrightness(Long ppcBrightness) {
      this.ppcBrightness = ppcBrightness;
   }

   public Long getPpcColortone() {
      return this.ppcColortone;
   }

   public void setPpcColortone(Long ppcColortone) {
      this.ppcColortone = ppcColortone;
   }

   public Long getPpcColorTemperature() {
      return this.ppcColorTemperature;
   }

   public void setPpcColorTemperature(Long ppcColorTemperature) {
      this.ppcColorTemperature = ppcColorTemperature;
   }

   public Long getPpcRed() {
      return this.ppcRed;
   }

   public void setPpcRed(Long ppcRed) {
      this.ppcRed = ppcRed;
   }

   public Long getPpcGreen() {
      return this.ppcGreen;
   }

   public void setPpcGreen(Long ppcGreen) {
      this.ppcGreen = ppcGreen;
   }

   public Long getPpcBlue() {
      return this.ppcBlue;
   }

   public void setPpcBlue(Long ppcBlue) {
      this.ppcBlue = ppcBlue;
   }

   public Long getPpcSize() {
      return this.ppcSize;
   }

   public void setPpcSize(Long ppcSize) {
      this.ppcSize = ppcSize;
   }

   public String getTimeCurrentTime() {
      return this.timeCurrentTime;
   }

   public void setTimeCurrentTime(String timeCurrentTime) {
      this.timeCurrentTime = timeCurrentTime;
   }

   public String getTimeOnTime() {
      return this.timeOnTime;
   }

   public void setTimeOnTime(String timeOnTime) {
      this.timeOnTime = timeOnTime;
   }

   public String getTimeOffTime() {
      return this.timeOffTime;
   }

   public void setTimeOffTime(String timeOffTime) {
      this.timeOffTime = timeOffTime;
   }

   public Long getPipSource() {
      return this.pipSource;
   }

   public void setPipSource(Long pipSource) {
      this.pipSource = pipSource;
   }

   public Long getPipSize() {
      return this.pipSize;
   }

   public void setPipSize(Long pipSize) {
      this.pipSize = pipSize;
   }

   public Long getPipPosition() {
      return this.pipPosition;
   }

   public void setPipPosition(Long pipPosition) {
      this.pipPosition = pipPosition;
   }

   public Long getPipSwap() {
      return this.pipSwap;
   }

   public void setPipSwap(Long pipSwap) {
      this.pipSwap = pipSwap;
   }

   public Long getPipControl() {
      return this.pipControl;
   }

   public void setPipControl(Long pipControl) {
      this.pipControl = pipControl;
   }

   public Long getSoundMode() {
      return this.soundMode;
   }

   public void setSoundMode(Long soundMode) {
      this.soundMode = soundMode;
   }

   public Long getSoundBass() {
      return this.soundBass;
   }

   public void setSoundBass(Long soundBass) {
      this.soundBass = soundBass;
   }

   public Long getSoundTreble() {
      return this.soundTreble;
   }

   public void setSoundTreble(Long soundTreble) {
      this.soundTreble = soundTreble;
   }

   public Long getSoundBalance() {
      return this.soundBalance;
   }

   public void setSoundBalance(Long soundBalance) {
      this.soundBalance = soundBalance;
   }

   public Long getSoundSrs() {
      return this.soundSrs;
   }

   public void setSoundSrs(Long soundSrs) {
      this.soundSrs = soundSrs;
   }

   public Long getSoundEffect() {
      return this.soundEffect;
   }

   public void setSoundEffect(Long soundEffect) {
      this.soundEffect = soundEffect;
   }

   public Long getImageCoarse() {
      return this.imageCoarse;
   }

   public void setImageCoarse(Long imageCoarse) {
      this.imageCoarse = imageCoarse;
   }

   public Long getImageFine() {
      return this.imageFine;
   }

   public void setImageFine(Long imageFine) {
      this.imageFine = imageFine;
   }

   public Long getImageHpos() {
      return this.imageHpos;
   }

   public void setImageHpos(Long imageHpos) {
      this.imageHpos = imageHpos;
   }

   public Long getImageVpos() {
      return this.imageVpos;
   }

   public void setImageVpos(Long imageVpos) {
      this.imageVpos = imageVpos;
   }

   public Long getImageAuto() {
      return this.imageAuto;
   }

   public void setImageAuto(Long imageAuto) {
      this.imageAuto = imageAuto;
   }

   public Long getSbStatus() {
      return this.sbStatus;
   }

   public void setSbStatus(Long sbStatus) {
      this.sbStatus = sbStatus;
   }

   public Long getSbRgain() {
      return this.sbRgain;
   }

   public void setSbRgain(Long sbRgain) {
      this.sbRgain = sbRgain;
   }

   public Long getSbGgain() {
      return this.sbGgain;
   }

   public void setSbGgain(Long sbGgain) {
      this.sbGgain = sbGgain;
   }

   public Long getSbBgain() {
      return this.sbBgain;
   }

   public void setSbBgain(Long sbBgain) {
      this.sbBgain = sbBgain;
   }

   public Long getSbROffset() {
      return this.sbROffset;
   }

   public void setSbROffset(Long sbROffset) {
      this.sbROffset = sbROffset;
   }

   public Long getSbGOffset() {
      return this.sbGOffset;
   }

   public void setSbGOffset(Long sbGOffset) {
      this.sbGOffset = sbGOffset;
   }

   public Long getSbBOffset() {
      return this.sbBOffset;
   }

   public void setSbBOffset(Long sbBOffset) {
      this.sbBOffset = sbBOffset;
   }

   public Long getSbGain() {
      return this.sbGain;
   }

   public void setSbGain(Long sbGain) {
      this.sbGain = sbGain;
   }

   public Long getSbSharp() {
      return this.sbSharp;
   }

   public void setSbSharp(Long sbSharp) {
      this.sbSharp = sbSharp;
   }

   public String getMntAuto() {
      return this.mntAuto;
   }

   public void setMntAuto(String mntAuto) {
      this.mntAuto = mntAuto;
   }

   public Long getMntManual() {
      return this.mntManual;
   }

   public void setMntManual(Long mntManual) {
      this.mntManual = mntManual;
   }

   public Long getMntVideoWall() {
      return this.mntVideoWall;
   }

   public void setMntVideoWall(Long mntVideoWall) {
      this.mntVideoWall = mntVideoWall;
   }

   public Long getMntFormat() {
      return this.mntFormat;
   }

   public void setMntFormat(Long mntFormat) {
      this.mntFormat = mntFormat;
   }

   public String getMntSafetyScreenTimer() {
      return this.mntSafetyScreenTimer;
   }

   public void setMntSafetyScreenTimer(String mntSafetyScreenTimer) {
      this.mntSafetyScreenTimer = mntSafetyScreenTimer;
   }

   public Long getMntSafetyScreenRun() {
      return this.mntSafetyScreenRun;
   }

   public void setMntSafetyScreenRun(Long mntSafetyScreenRun) {
      this.mntSafetyScreenRun = mntSafetyScreenRun;
   }

   public String getMntPixelShift() {
      return this.mntPixelShift;
   }

   public void setMntPixelShift(String mntPixelShift) {
      this.mntPixelShift = mntPixelShift;
   }

   public Long getMntSafetyLock() {
      return this.mntSafetyLock;
   }

   public void setMntSafetyLock(Long mntSafetyLock) {
      this.mntSafetyLock = mntSafetyLock;
   }

   public Long getAdvancedRj45SettingRefresh() {
      return this.advancedRj45SettingRefresh;
   }

   public void setAdvancedRj45SettingRefresh(Long advancedRj45SettingRefresh) {
      this.advancedRj45SettingRefresh = advancedRj45SettingRefresh;
   }

   public String getAdvancedOsdDisplayType() {
      return this.advancedOsdDisplayType;
   }

   public void setAdvancedOsdDisplayType(String advancedOsdDisplayType) {
      this.advancedOsdDisplayType = advancedOsdDisplayType;
   }

   public String getAdvancedOsdDisplayTypeValue() {
      return this.advancedOsdDisplayTypeValue;
   }

   public void setAdvancedOsdDisplayTypeValue(String advancedOsdDisplayTypeValue) {
      this.advancedOsdDisplayTypeValue = advancedOsdDisplayTypeValue;
   }

   public Long getAdvancedFanControl() {
      return this.advancedFanControl;
   }

   public void setAdvancedFanControl(Long advancedFanControl) {
      this.advancedFanControl = advancedFanControl;
   }

   public Long getAdvancedFanSpeed() {
      return this.advancedFanSpeed;
   }

   public void setAdvancedFanSpeed(Long advancedFanSpeed) {
      this.advancedFanSpeed = advancedFanSpeed;
   }

   public Long getAdvancedReset() {
      return this.advancedReset;
   }

   public void setAdvancedReset(Long advancedReset) {
      this.advancedReset = advancedReset;
   }

   public Long getAdvancedAutoPower() {
      return this.advancedAutoPower;
   }

   public void setAdvancedAutoPower(Long advancedAutoPower) {
      this.advancedAutoPower = advancedAutoPower;
   }

   public Long getAdvancedUserAutoColor() {
      return this.advancedUserAutoColor;
   }

   public void setAdvancedUserAutoColor(Long advancedUserAutoColor) {
      this.advancedUserAutoColor = advancedUserAutoColor;
   }

   public Long getAdvancedStandBy() {
      return this.advancedStandBy;
   }

   public void setAdvancedStandBy(Long advancedStandBy) {
      this.advancedStandBy = advancedStandBy;
   }

   public Long getMaxPowerSaving() {
      return this.maxPowerSaving;
   }

   public void setMaxPowerSaving(Long maxPowerSaving) {
      this.maxPowerSaving = maxPowerSaving;
   }

   public Long getBrightnessLimit() {
      return this.brightnessLimit;
   }

   public void setBrightnessLimit(Long brightnessLimit) {
      this.brightnessLimit = brightnessLimit;
   }

   public Long getTouchControlLock() {
      return this.touchControlLock;
   }

   public void setTouchControlLock(Long touchControlLock) {
      this.touchControlLock = touchControlLock;
   }

   public String getAutoSourceSwitching() {
      return this.autoSourceSwitching;
   }

   public void setAutoSourceSwitching(String autoSourceSwitching) {
      this.autoSourceSwitching = autoSourceSwitching;
   }

   public String getWebBrowserUrl() {
      return this.webBrowserUrl;
   }

   public void setWebBrowserUrl(String webBrowserUrl) {
      this.webBrowserUrl = webBrowserUrl;
   }

   public String getCustomLogo() {
      return this.customLogo;
   }

   public void setCustomLogo(String customLogo) {
      this.customLogo = customLogo;
   }

   public Long getScreenFreeze() {
      return this.screenFreeze;
   }

   public void setScreenFreeze(Long screenFreeze) {
      this.screenFreeze = screenFreeze;
   }

   public Long getScreenMute() {
      return this.screenMute;
   }

   public void setScreenMute(Long screenMute) {
      this.screenMute = screenMute;
   }

   public Long getMiscRemocon() {
      return this.miscRemocon;
   }

   public void setMiscRemocon(Long miscRemocon) {
      this.miscRemocon = miscRemocon;
   }

   public Long getMiscPanelLock() {
      return this.miscPanelLock;
   }

   public void setMiscPanelLock(Long miscPanelLock) {
      this.miscPanelLock = miscPanelLock;
   }

   public Long getMiscOsd() {
      return this.miscOsd;
   }

   public void setMiscOsd(Long miscOsd) {
      this.miscOsd = miscOsd;
   }

   public Long getMiscAllLock() {
      return this.miscAllLock;
   }

   public void setMiscAllLock(Long miscAllLock) {
      this.miscAllLock = miscAllLock;
   }

   public String getDiagnosisDisplayStatus() {
      return this.diagnosisDisplayStatus;
   }

   public void setDiagnosisDisplayStatus(String diagnosisDisplayStatus) {
      this.diagnosisDisplayStatus = diagnosisDisplayStatus;
   }

   public Long getDiagnosisMonitorTemperature() {
      return this.diagnosisMonitorTemperature;
   }

   public void setDiagnosisMonitorTemperature(Long diagnosisMonitorTemperature) {
      this.diagnosisMonitorTemperature = diagnosisMonitorTemperature;
   }

   public Long getDiagnosisAlarmTemperature() {
      return this.diagnosisAlarmTemperature;
   }

   public void setDiagnosisAlarmTemperature(Long diagnosisAlarmTemperature) {
      this.diagnosisAlarmTemperature = diagnosisAlarmTemperature;
   }

   public String getDiagnosisPanelOnTime() {
      return this.diagnosisPanelOnTime;
   }

   public void setDiagnosisPanelOnTime(String diagnosisPanelOnTime) {
      this.diagnosisPanelOnTime = diagnosisPanelOnTime;
   }

   public Float getSensorInternalTemperature() {
      return this.sensorInternalTemperature;
   }

   public void setSensorInternalTemperature(Float sensorInternalTemperature) {
      this.sensorInternalTemperature = sensorInternalTemperature;
   }

   public Float getSensorInternalHumidity() {
      return this.sensorInternalHumidity;
   }

   public void setSensorInternalHumidity(Float sensorInternalHumidity) {
      this.sensorInternalHumidity = sensorInternalHumidity;
   }

   public Float getSensorEnvironmentTemperature() {
      return this.sensorEnvironmentTemperature;
   }

   public void setSensorEnvironmentTemperature(Float sensorEnvironmentTemperature) {
      this.sensorEnvironmentTemperature = sensorEnvironmentTemperature;
   }

   public Float getSensorFrontglassTemperature() {
      return this.sensorFrontglassTemperature;
   }

   public void setSensorFrontglassTemperature(Float sensorFrontglassTemperature) {
      this.sensorFrontglassTemperature = sensorFrontglassTemperature;
   }

   public Float getSensorFrontglassHumidity() {
      return this.sensorFrontglassHumidity;
   }

   public void setSensorFrontglassHumidity(Float sensorFrontglassHumidity) {
      this.sensorFrontglassHumidity = sensorFrontglassHumidity;
   }

   public Long getChkSchChannel() {
      return this.chkSchChannel;
   }

   public void setChkSchChannel(Long chkSchChannel) {
      this.chkSchChannel = chkSchChannel;
   }

   public Boolean getWebcam() {
      return this.webcam;
   }

   public void setWebcam(Boolean webcam) {
      this.webcam = webcam;
   }

   public String getVwtId() {
      return this.vwtId;
   }

   public void setVwtId(String vwtId) {
      this.vwtId = vwtId;
   }

   public Long getIconErrorSw() {
      return this.iconErrorSw;
   }

   public void setIconErrorSw(Long iconErrorSw) {
      this.iconErrorSw = iconErrorSw;
   }

   public Long getIconErrorHw() {
      return this.iconErrorHw;
   }

   public void setIconErrorHw(Long iconErrorHw) {
      this.iconErrorHw = iconErrorHw;
   }

   public Long getIconAlarm() {
      return this.iconAlarm;
   }

   public void setIconAlarm(Long iconAlarm) {
      this.iconAlarm = iconAlarm;
   }

   public Long getIconProcessContentDownload() {
      return this.iconProcessContentDownload;
   }

   public void setIconProcessContentDownload(Long iconProcessContentDownload) {
      this.iconProcessContentDownload = iconProcessContentDownload;
   }

   public Long getIconProcessLog() {
      return this.iconProcessLog;
   }

   public void setIconProcessLog(Long iconProcessLog) {
      this.iconProcessLog = iconProcessLog;
   }

   public Long getIconProcessSwDownload() {
      return this.iconProcessSwDownload;
   }

   public void setIconProcessSwDownload(Long iconProcessSwDownload) {
      this.iconProcessSwDownload = iconProcessSwDownload;
   }

   public Long getIconMemo() {
      return this.iconMemo;
   }

   public void setIconMemo(Long iconMemo) {
      this.iconMemo = iconMemo;
   }

   public Long getIconBackup() {
      return this.iconBackup;
   }

   public void setIconBackup(Long iconBackup) {
      this.iconBackup = iconBackup;
   }

   public String getAutoBrightness() {
      return this.autoBrightness;
   }

   public void setAutoBrightness(String autoBrightness) {
      this.autoBrightness = autoBrightness;
   }

   public Long getChildAlarmTemperature() {
      return this.childAlarmTemperature;
   }

   public void setChildAlarmTemperature(Long childAlarmTemperature) {
      this.childAlarmTemperature = childAlarmTemperature;
   }

   public Long getBlackTone() {
      return this.blackTone;
   }

   public void setBlackTone(Long blackTone) {
      this.blackTone = blackTone;
   }

   public Long getFleshTone() {
      return this.fleshTone;
   }

   public void setFleshTone(Long fleshTone) {
      this.fleshTone = fleshTone;
   }

   public Long getRgbOnlyMode() {
      return this.rgbOnlyMode;
   }

   public void setRgbOnlyMode(Long rgbOnlyMode) {
      this.rgbOnlyMode = rgbOnlyMode;
   }

   public Long getPictureEnhancer() {
      return this.pictureEnhancer;
   }

   public void setPictureEnhancer(Long pictureEnhancer) {
      this.pictureEnhancer = pictureEnhancer;
   }

   public String getColorSpace() {
      return this.colorSpace;
   }

   public void setColorSpace(String colorSpace) {
      this.colorSpace = colorSpace;
   }

   public Long getOsdMenuSize() {
      return this.osdMenuSize;
   }

   public void setOsdMenuSize(Long osdMenuSize) {
      this.osdMenuSize = osdMenuSize;
   }

   public String getLedPictureSize() {
      return this.ledPictureSize;
   }

   public void setLedPictureSize(String ledPictureSize) {
      this.ledPictureSize = ledPictureSize;
   }

   public String getLedHdr() {
      return this.ledHdr;
   }

   public void setLedHdr(String ledHdr) {
      this.ledHdr = ledHdr;
   }

   public Long getLedHdrDre() {
      return this.ledHdrDre;
   }

   public void setLedHdrDre(Long ledHdrDre) {
      this.ledHdrDre = ledHdrDre;
   }

   public Long getEcoSensor() {
      return this.ecoSensor;
   }

   public void setEcoSensor(Long ecoSensor) {
      this.ecoSensor = ecoSensor;
   }

   public Long getMinBrightness() {
      return this.minBrightness;
   }

   public void setMinBrightness(Long minBrightness) {
      this.minBrightness = minBrightness;
   }

   public Long getLiveMode() {
      return this.liveMode;
   }

   public void setLiveMode(Long liveMode) {
      this.liveMode = liveMode;
   }

   public Long getDisplayOutputMode() {
      return this.displayOutputMode;
   }

   public void setDisplayOutputMode(Long displayOutputMode) {
      this.displayOutputMode = displayOutputMode;
   }

   public Boolean getCleanupUserData() {
      return this.cleanupUserData;
   }

   public void setCleanupUserData(Boolean cleanupUserData) {
      this.cleanupUserData = cleanupUserData;
   }

   public Long getCleanupUserDataInterval() {
      return this.cleanupUserDataInterval;
   }

   public void setCleanupUserDataInterval(Long cleanupUserDataInterval) {
      this.cleanupUserDataInterval = cleanupUserDataInterval;
   }

   public Long getAutoSave() {
      return this.autoSave;
   }

   public void setAutoSave(Long autoSave) {
      this.autoSave = autoSave;
   }

   public Long getAutoPowerOff() {
      return this.autoPowerOff;
   }

   public void setAutoPowerOff(Long autoPowerOff) {
      this.autoPowerOff = autoPowerOff;
   }

   public String getSmtp() {
      return this.smtp;
   }

   public void setSmtp(String smtp) {
      this.smtp = smtp;
   }

   public String getPrintServer() {
      return this.printServer;
   }

   public void setPrintServer(String printServer) {
      this.printServer = printServer;
   }

   public Long getChildCnt() {
      return this.childCnt;
   }

   public void setChildCnt(Long childCnt) {
      this.childCnt = childCnt;
   }

   public Long getConnChildCnt() {
      return this.connChildCnt;
   }

   public void setConnChildCnt(Long connChildCnt) {
      this.connChildCnt = connChildCnt;
   }

   public Boolean getIsChild() {
      return this.isChild;
   }

   public void setIsChild(Boolean isChild) {
      this.isChild = isChild;
   }

   public Boolean getHasChild() {
      return !"SIGNAGE".equalsIgnoreCase(this.deviceType) && !"RSIGNAGE".equalsIgnoreCase(this.deviceType) ? this.hasChild : true;
   }

   public void setHasChild(Boolean hasChild) {
      if (!"SIGNAGE".equalsIgnoreCase(this.deviceType) && !"RSIGNAGE".equalsIgnoreCase(this.deviceType)) {
         this.hasChild = hasChild;
      } else {
         this.hasChild = true;
      }

   }

   public String getVwlMode() {
      return this.vwlMode;
   }

   public void setVwlMode(String vwlMode) {
      this.vwlMode = vwlMode;
   }

   public String getVwlPosition() {
      return this.vwlPosition;
   }

   public void setVwlPosition(String vwlPosition) {
      this.vwlPosition = vwlPosition;
   }

   public String getVwlFormat() {
      return this.vwlFormat;
   }

   public void setVwlFormat(String vwlFormat) {
      this.vwlFormat = vwlFormat;
   }

   public String getVwlLayout() {
      return this.vwlLayout;
   }

   public void setVwlLayout(String vwlLayout) {
      this.vwlLayout = vwlLayout;
   }

   public void setModelType(String type) {
      this.deviceModelName = type;
      this.deviceModelCode = type;
      this.deviceType = type;
   }

   public void setDefaultDBValues() {
      this.childCnt = 0L;
      this.isChild = false;
      this.deviceTypeVersion = 1.0F;
   }

   public Long getInstallEnvironment() {
      return this.installEnvironment;
   }

   public void setInstallEnvironment(Long installEnvironment) {
      this.installEnvironment = installEnvironment;
   }

   public Long getDehumidify() {
      return this.dehumidify;
   }

   public void setDehumidify(Long dehumidify) {
      this.dehumidify = dehumidify;
   }

   public String getDehumidifyProgress() {
      return this.dehumidifyProgress;
   }

   public void setDehumidifyProgress(String dehumidifyProgress) {
      this.dehumidifyProgress = dehumidifyProgress;
   }

   public Long getDimmingOption() {
      return this.dimmingOption;
   }

   public void setDimmingOption(Long dimmingOption) {
      this.dimmingOption = dimmingOption;
   }

   public Long getDimmingNightTimeOverride() {
      return this.dimmingNightTimeOverride;
   }

   public void setDimmingNightTimeOverride(Long dimmingNightTimeOverride) {
      this.dimmingNightTimeOverride = dimmingNightTimeOverride;
   }

   public String getDimmingEcoSensor() {
      return this.dimmingEcoSensor;
   }

   public void setDimmingEcoSensor(String dimmingEcoSensor) {
      this.dimmingEcoSensor = dimmingEcoSensor;
   }

   public String getDimmingSunriseSunset() {
      return this.dimmingSunriseSunset;
   }

   public void setDimmingSunriseSunset(String dimmingSunriseSunset) {
      this.dimmingSunriseSunset = dimmingSunriseSunset;
   }

   public String getDimmingSunriseSunsetTimes() {
      return this.dimmingSunriseSunsetTimes;
   }

   public void setDimmingSunriseSunsetTimes(String dimmingSunriseSunsetTimes) {
      this.dimmingSunriseSunsetTimes = dimmingSunriseSunsetTimes;
   }

   public String getDimmingBrightnessOutput() {
      return this.dimmingBrightnessOutput;
   }

   public void setDimmingBrightnessOutput(String dimmingBrightnessOutput) {
      this.dimmingBrightnessOutput = dimmingBrightnessOutput;
   }

   public void initParamsForJson() {
      if (this.deviceType.equalsIgnoreCase("LPLAYER")) {
         this.advancedFanControl = null;
         this.advancedFanSpeed = null;
      } else if (this.deviceType.equalsIgnoreCase("SIG_CHILD")) {
         if (this.basicPower == null) {
            this.basicPower = "";
         }

         if (this.basicVolume == null) {
            this.basicVolume = 0L;
         }

         if (this.basicMute == null) {
            this.basicMute = 0L;
         }

         if (this.basicSource == null) {
            this.basicSource = 0L;
         }

         this.basicDirectChannel = null;
         if (this.basicPanelStatus == null) {
            this.basicPanelStatus = 0L;
         }

         this.networkStandbyMode = null;
         if (this.pvSplPictureMode == null) {
            this.pvSplPictureMode = -1L;
         }

         if (this.pvMode == null) {
            this.pvMode = 0L;
         }

         if (this.pvContrast == null) {
            this.pvContrast = 0L;
         }

         if (this.pvBrightness == null) {
            this.pvBrightness = 0L;
         }

         if (this.pvSharpness == null) {
            this.pvSharpness = 0L;
         }

         if (this.pvColor == null) {
            this.pvColor = 0L;
         }

         if (this.pvTint == null) {
            this.pvTint = 0L;
         }

         if (this.pvColortone == null) {
            this.pvColortone = 0L;
         }

         if (this.pvColorTemperature == null) {
            this.pvColorTemperature = 0L;
         }

         if (this.pvSize == null) {
            this.pvSize = 0L;
         }

         if (this.pvDigitalnr == null) {
            this.pvDigitalnr = 0L;
         }

         if (this.pvFilmmode == null) {
            this.pvFilmmode = 0L;
         }

         if (this.pvVideoPicturePositionSize == null) {
            this.pvVideoPicturePositionSize = "";
         }

         if (this.pvHdmiBlackLevel == null) {
            this.pvHdmiBlackLevel = 0L;
         }

         if (this.ppcGamma == null) {
            this.ppcGamma = 0L;
         }

         if (this.ppcHdmiBlackLevel == null) {
            this.ppcHdmiBlackLevel = 0L;
         }

         if (this.ppcMagicBright == null) {
            this.ppcMagicBright = 0L;
         }

         if (this.ppcContrast == null) {
            this.ppcContrast = 0L;
         }

         if (this.ppcBrightness == null) {
            this.ppcBrightness = 0L;
         }

         if (this.ppcColortone == null) {
            this.ppcColortone = 0L;
         }

         if (this.ppcColorTemperature == null) {
            this.ppcColorTemperature = 0L;
         }

         if (this.ppcRed == null) {
            this.ppcRed = 0L;
         }

         if (this.ppcGreen == null) {
            this.ppcGreen = 0L;
         }

         if (this.ppcBlue == null) {
            this.ppcBlue = 0L;
         }

         if (this.ppcSize == null) {
            this.ppcSize = 0L;
         }

         this.timeCurrentTime = null;
         this.timeOnTime = null;
         this.timeOffTime = null;
         this.pipSource = null;
         this.pipSize = null;
         this.pipPosition = null;
         this.pipSwap = null;
         this.pipControl = null;
         this.soundMode = null;
         this.soundBass = null;
         this.soundTreble = null;
         this.soundBalance = null;
         this.soundSrs = null;
         this.soundEffect = null;
         if (this.imageCoarse == null) {
            this.imageCoarse = 0L;
         }

         if (this.imageFine == null) {
            this.imageFine = 0L;
         }

         if (this.imageHpos == null) {
            this.imageHpos = 0L;
         }

         if (this.imageVpos == null) {
            this.imageVpos = 0L;
         }

         if (this.imageAuto == null) {
            this.imageAuto = 0L;
         }

         if (this.sbStatus == null) {
            this.sbStatus = 0L;
         }

         if (this.sbRgain == null) {
            this.sbRgain = 0L;
         }

         if (this.sbGgain == null) {
            this.sbGgain = 0L;
         }

         if (this.sbBgain == null) {
            this.sbBgain = 0L;
         }

         if (this.sbROffset == null) {
            this.sbROffset = 0L;
         }

         if (this.sbGOffset == null) {
            this.sbGOffset = 0L;
         }

         if (this.sbBOffset == null) {
            this.sbBOffset = 0L;
         }

         if (this.sbGain == null) {
            this.sbGain = 0L;
         }

         if (this.sbSharp == null) {
            this.sbSharp = 0L;
         }

         if (this.mntAuto == null) {
            this.mntAuto = "";
         }

         if (this.mntManual == null) {
            this.mntManual = 0L;
         }

         this.mntVideoWall = null;
         if (this.mntFormat == null) {
            this.mntFormat = 0L;
         }

         if (this.mntSafetyScreenTimer == null) {
            this.mntSafetyScreenTimer = null;
         }

         if (this.mntSafetyScreenRun == null) {
            this.mntSafetyScreenRun = 0L;
         }

         if (this.mntPixelShift == null) {
            this.mntPixelShift = "";
         }

         if (this.mntSafetyLock == null) {
            this.mntSafetyLock = 0L;
         }

         this.advancedRj45SettingRefresh = null;
         this.advancedOsdDisplayType = null;
         this.advancedFanControl = null;
         this.advancedFanSpeed = null;
         this.advancedReset = null;
         this.advancedAutoPower = null;
         this.advancedUserAutoColor = null;
         this.advancedStandBy = null;
         if (this.miscOsd == null) {
            this.miscOsd = 0L;
         }

         this.diagnosisDisplayStatus = null;
         if (this.diagnosisMonitorTemperature == null) {
            this.diagnosisMonitorTemperature = 0L;
         }

         if (this.diagnosisAlarmTemperature == null) {
            this.diagnosisAlarmTemperature = 0L;
         }

         if (this.diagnosisPanelOnTime == null) {
            this.diagnosisPanelOnTime = "";
         }

         this.chkSchChannel = null;
      } else if (this.deviceType.equals("APLAYER")) {
         this.basicSource = null;
         this.miscOsd = null;
         this.diagnosisMonitorTemperature = null;
         this.diagnosisAlarmTemperature = null;
         this.diagnosisPanelOnTime = null;
      }

   }

   public void setEmptyProperties() throws IllegalArgumentException, IllegalAccessException {
      Field[] var1 = this.getClass().getDeclaredFields();
      int var2 = var1.length;

      for(int var3 = 0; var3 < var2; ++var3) {
         Field f = var1[var3];
         f.setAccessible(true);
         if (f.getType().equals(String.class)) {
            f.set(this, "");
         } else if (f.getType().equals(Long.class)) {
            f.set(this, new Long(-1L));
         } else if (f.getType().equals(Float.class)) {
            f.set(this, new Float(-1.0F));
         } else if (f.getType().equals(Double.class)) {
            f.set(this, new Double(-1.0D));
         } else if (f.getType().equals(Integer.class)) {
            f.set(this, new Integer(-1));
         } else if (f.getType().equals(Boolean.class)) {
            f.set(this, new Boolean(false));
         }

         f.setAccessible(false);
      }

   }

   public Object clone() throws CloneNotSupportedException {
      return super.clone();
   }
}
