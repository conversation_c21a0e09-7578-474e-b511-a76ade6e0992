package com.samsung.magicinfo.webauthor2.repository;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.repository.model.CsdMappingResponse;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Repository;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.UnknownHttpStatusCodeException;
import org.springframework.web.util.UriComponentsBuilder;

@Repository
public class CsdRepositoryImpl implements CsdRepository {
  private static final Logger logger = LoggerFactory.getLogger(CsdRepositoryImpl.class);
  
  private static final String CSD_FILENAME = "ContentsMetadata.CSD";
  
  private RestTemplate restTemplate;
  
  private UserData userData;
  
  @Autowired
  public CsdRepositoryImpl(RestTemplate restTemplate, UserData userData) {
    this.restTemplate = restTemplate;
    this.userData = userData;
  }
  
  public CsdMappingResponse postCsdToMips(String csdXml, String cid, String templateId) {
    Boolean isUrlAuthNotAllowed = this.userData.isUrlAuthNotAllowedToThisMisServletSession();
    UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.newInstance().path("/servlet/FtpMetaUploader");
    if (false == isUrlAuthNotAllowed.booleanValue())
      uriComponentsBuilder.queryParam("id", new Object[] { this.userData.getUserId() }).queryParam("passwd", new Object[] { this.userData.getToken() }); 
    URI uri = uriComponentsBuilder.build().encode().toUri();
    logger.debug("CSD Request: " + csdXml);
    HttpHeaders headers = new HttpHeaders();
    headers.add("userId", this.userData.getUserId());
    headers.add("token", this.userData.getToken());
    headers.add("CID", cid);
    if (!Strings.isNullOrEmpty(templateId))
      headers.add("TEMPLATE_ID", templateId); 
    headers.setContentType(MediaType.MULTIPART_FORM_DATA);
    headers.setAccept(Arrays.asList(new MediaType[] { MediaType.TEXT_HTML }));
    ByteArrayResource contentsAsResource = new ByteArrayResource(csdXml.getBytes(StandardCharsets.UTF_8)) {
        public String getFilename() {
          return "ContentsMetadata.CSD";
        }
      };
    LinkedMultiValueMap linkedMultiValueMap = new LinkedMultiValueMap();
    linkedMultiValueMap.add("ContentsMetadata.CSD", contentsAsResource);
    HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity(linkedMultiValueMap, (MultiValueMap)headers);
    try {
      ResponseEntity<String> response = this.restTemplate.exchange(uri, HttpMethod.POST, requestEntity, String.class);
      logger.debug("CSD Response: " + response.toString());
      String versionId = response.getHeaders().get("VERSION_ID").get(0);
      String contentDuplicate = response.getHeaders().get("CONTENT_DUP").get(0);
      String body = (String)response.getBody();
      return new CsdMappingResponse(response.getStatusCode(), versionId, contentDuplicate, body);
    } catch (UnknownHttpStatusCodeException ex) {
      throw new UploaderException(500, "Unknown http status code response from MagicINFO Server");
    } 
  }
}
