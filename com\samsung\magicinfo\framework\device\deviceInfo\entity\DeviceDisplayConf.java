package com.samsung.magicinfo.framework.device.deviceInfo.entity;

import com.samsung.magicinfo.framework.monitoring.entity.CurrentPlayingEntity;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.openapi.annotation.CdataField;
import com.thoughtworks.xstream.annotations.XStreamOmitField;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.regex.Pattern;

public class DeviceDisplayConf implements Serializable, Cloneable {
   private static final long serialVersionUID = 8957592273431816027L;
   private String device_id;
   @CdataField
   private String device_name;
   private String device_model_code;
   private String device_model_name;
   private String device_type;
   private Float device_type_version;
   private String basic_power;
   private Long basic_volume;
   private Long basic_mute;
   private Long basic_source;
   private String basic_direct_channel;
   private Long basic_panel_status;
   private String error_flag;
   private String network_standby_mode;
   private Long specialized_picture_mode;
   private Long pv_mode;
   private Long pv_contrast;
   private Long pv_brightness;
   private Long pv_sharpness;
   private Long pv_color;
   private Long pv_tint;
   private Long pv_colortone;
   private Long pv_color_temperature;
   private Long pv_size;
   private Long pv_digitalnr;
   private Long pv_filmmode;
   private String pv_video_picture_position_size;
   private Long pv_hdmi_black_level;
   private Long pv_mpeg_noise_filter;
   private Long ppc_gamma;
   private Long ppc_hdmi_black_level;
   private Long auto_motion_plus;
   private Long auto_motion_plus_judder_reduction;
   private Long ppc_magic_bright;
   private Long ppc_contrast;
   private Long ppc_brightness;
   private Long ppc_colortone;
   private Long ppc_color_temperature;
   private Long ppc_red;
   private Long ppc_green;
   private Long ppc_blue;
   private Long ppc_size;
   private String time_current_time;
   private String time_on_time;
   private String time_off_time;
   private Long pip_source;
   private Long pip_size;
   private Long pip_position;
   private Long pip_swap;
   private Long pip_control;
   private Long sound_mode;
   private Long sound_bass;
   private Long sound_treble;
   private Long sound_balance;
   private Long sound_srs;
   private Long sound_effect;
   private Long image_coarse;
   private Long image_fine;
   private Long image_hpos;
   private Long image_vpos;
   private Long image_auto;
   private Long sb_status;
   private Long sb_rgain;
   private Long sb_ggain;
   private Long sb_bgain;
   private Long sb_r_offset;
   private Long sb_g_offset;
   private Long sb_b_offset;
   private Long sb_gain;
   private Long sb_sharp;
   private String mnt_auto;
   private Long mnt_manual;
   private Long mnt_video_wall;
   private Long mnt_format;
   private String mnt_safety_screen_timer;
   private Long mnt_safety_screen_run;
   private String mnt_pixel_shift;
   private Long mnt_safety_lock;
   private Long advanced_rj45_setting_refresh;
   private String advanced_osd_display_type;
   private String advanced_osd_display_type_value;
   private Long advanced_fan_control;
   private Long advanced_fan_speed;
   private Long advanced_reset;
   private Long advanced_auto_power;
   private Long advanced_user_auto_color;
   private Long advanced_stand_by;
   private Long max_power_saving;
   private Long brightness_limit;
   private Long touch_control_lock;
   private String auto_source_switching;
   private String web_browser_url;
   private String custom_logo;
   private Long screen_freeze;
   private Long screen_mute;
   private Long misc_remocon;
   private Long misc_panel_lock;
   private Long misc_osd;
   private Long misc_all_lock;
   private String diagnosis_display_status;
   private Long diagnosis_monitor_temperature;
   private Long diagnosis_alarm_temperature;
   private String diagnosis_panel_on_time;
   private Float sensor_internal_temperature;
   private Float sensor_internal_humidity;
   private Float sensor_environment_temperature;
   private Float sensor_frontglass_temperature;
   private Float sensor_frontglass_humidity;
   private Long chkSchChannel;
   private Boolean webcam;
   private String vwt_id;
   private Long icon_error_sw;
   private Long icon_error_hw;
   private Long icon_alarm;
   private Long icon_process_content_download;
   private Long icon_process_log;
   private Long icon_process_sw_download;
   private Long icon_memo;
   private Long icon_backup;
   private String auto_brightness;
   private Long child_alarm_temperature;
   private Long black_tone;
   private Long flesh_tone;
   private Long rgb_only_mode;
   private Long picture_enhancer;
   private String color_space;
   private Long osd_menu_size;
   private String led_picture_size;
   private String led_hdr;
   private Long led_hdr_dre;
   private Long eco_sensor;
   private Long min_brightness;
   private Long live_mode;
   private Long display_output_mode;
   private Boolean cleanup_user_data;
   private Long cleanup_user_data_interval;
   private Long auto_save;
   private Long auto_power_off;
   private String smtp;
   private String print_server;
   private Long web_command;
   private Long web_option;
   private String web_url;
   private String web_end_time;
   private Long web_duration;
   @XStreamOmitField
   private Long child_cnt;
   @XStreamOmitField
   private Long conn_child_cnt;
   @XStreamOmitField
   private Boolean is_child;
   @XStreamOmitField
   private Boolean has_child;
   private String vwl_mode;
   private String vwl_position;
   private String vwl_format;
   private String vwl_layout;
   private Long install_environment;
   private String dehumidify;
   private Long dimming_option;
   private Long dimming_night_time_override;
   private String dimming_eco_sensor;
   private String dimming_sunrise_sunset;
   private String dimming_sunrise_sunset_times;
   private String dimming_brightness_output;
   private Timestamp mdc_update_time;

   public DeviceDisplayConf() {
      super();
   }

   public String getVwl_mode() {
      return this.vwl_mode;
   }

   public void setVwl_mode(String vwl_mode) {
      this.vwl_mode = vwl_mode;
   }

   public String getVwl_position() {
      return this.vwl_position;
   }

   public void setVwl_position(String vwl_position) {
      this.vwl_position = vwl_position;
   }

   public String getVwl_format() {
      return this.vwl_format;
   }

   public void setVwl_format(String vwl_format) {
      this.vwl_format = vwl_format;
   }

   public String getVwl_layout() {
      return this.vwl_layout;
   }

   public void setVwl_layout(String vwl_layout) {
      this.vwl_layout = vwl_layout;
   }

   public String getDevice_id() {
      return this.device_id;
   }

   public void setDevice_id(String device_id) {
      this.device_id = device_id;
   }

   public String getDevice_name() {
      return this.device_name;
   }

   public void setDevice_name(String device_name) {
      this.device_name = device_name;
   }

   public String getDevice_model_code() {
      return this.device_model_code;
   }

   public void setDevice_model_code(String device_model_code) {
      this.device_model_code = device_model_code;
   }

   public String getDevice_model_name() {
      return this.device_model_name;
   }

   public void setDevice_model_name(String device_model_name) {
      this.device_model_name = device_model_name;
   }

   public String getDevice_type() {
      return this.device_type;
   }

   public void setDevice_type(String device_type) {
      this.device_type = device_type;
   }

   public Float getDevice_type_version() {
      return this.device_type_version;
   }

   public void setDevice_type_version(Float device_type_version) {
      this.device_type_version = device_type_version;
   }

   public String getBasic_power() {
      return this.basic_power;
   }

   public void setBasic_power(String basic_power) {
      this.basic_power = basic_power;
   }

   public Long getBasic_volume() {
      return this.basic_volume;
   }

   public void setBasic_volume(Long basic_volume) {
      this.basic_volume = basic_volume;
   }

   public Long getBasic_mute() {
      return this.basic_mute;
   }

   public void setBasic_mute(Long basic_mute) {
      this.basic_mute = basic_mute;
   }

   public Long getBasic_source() {
      return this.basic_source;
   }

   public void setBasic_source(Long basic_source) {
      this.basic_source = basic_source;
      if (this.device_id != null && basic_source != null) {
         MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
         CurrentPlayingEntity playingEntity = motMgr.getPlayingContent(this.device_id);
         if (playingEntity != null && playingEntity.getInputSource() != 1000) {
            playingEntity.setInputSource(basic_source.intValue());
         }
      }

   }

   public String getBasic_direct_channel() {
      return this.basic_direct_channel;
   }

   public void setBasic_direct_channel(String basic_direct_channel) {
      this.basic_direct_channel = basic_direct_channel;
   }

   public Long getBasic_panel_status() {
      return this.basic_panel_status;
   }

   public void setBasic_panel_status(Long basic_panel_status) {
      this.basic_panel_status = basic_panel_status;
      if (this.device_id != null && basic_panel_status != null) {
         MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
         CurrentPlayingEntity playingEntity = motMgr.getPlayingContent(this.device_id);
         if (playingEntity != null) {
            playingEntity.setPanelStatus(basic_panel_status);
         }
      }

   }

   public String getNetwork_standby_mode() {
      return this.network_standby_mode;
   }

   public void setNetwork_standby_mode(String network_standby_mode) {
      this.network_standby_mode = network_standby_mode;
   }

   public Long getSpecialized_picture_mode() {
      return this.specialized_picture_mode;
   }

   public Long getPv_mode() {
      return this.pv_mode;
   }

   public void setPv_mode(Long pv_mode) {
      this.pv_mode = pv_mode;
   }

   public Long getPv_contrast() {
      return this.pv_contrast;
   }

   public void setPv_contrast(Long pv_contrast) {
      this.pv_contrast = pv_contrast;
   }

   public Long getPv_brightness() {
      return this.pv_brightness;
   }

   public void setSpecialized_picture_mode(Long specialized_picture_mode) {
      this.specialized_picture_mode = specialized_picture_mode;
   }

   public void setPv_brightness(Long pv_brightness) {
      this.pv_brightness = pv_brightness;
   }

   public Long getPv_sharpness() {
      return this.pv_sharpness;
   }

   public void setPv_sharpness(Long pv_sharpness) {
      this.pv_sharpness = pv_sharpness;
   }

   public Long getPv_color() {
      return this.pv_color;
   }

   public void setPv_color(Long pv_color) {
      this.pv_color = pv_color;
   }

   public Long getPv_tint() {
      return this.pv_tint;
   }

   public void setPv_tint(Long pv_tint) {
      this.pv_tint = pv_tint;
   }

   public Long getPv_colortone() {
      return this.pv_colortone;
   }

   public void setPv_colortone(Long pv_colortone) {
      this.pv_colortone = pv_colortone;
   }

   public Long getPv_color_temperature() {
      return this.pv_color_temperature;
   }

   public void setPv_color_temperature(Long pv_color_temperature) {
      this.pv_color_temperature = pv_color_temperature;
   }

   public Long getPv_size() {
      return this.pv_size;
   }

   public void setPv_size(Long pv_size) {
      this.pv_size = pv_size;
   }

   public Long getPv_digitalnr() {
      return this.pv_digitalnr;
   }

   public void setPv_digitalnr(Long pv_digitalnr) {
      this.pv_digitalnr = pv_digitalnr;
   }

   public Long getPv_filmmode() {
      return this.pv_filmmode;
   }

   public void setPv_filmmode(Long pv_filmmode) {
      this.pv_filmmode = pv_filmmode;
   }

   public Long getPpc_magic_bright() {
      return this.ppc_magic_bright;
   }

   public void setPpc_magic_bright(Long ppc_magic_bright) {
      this.ppc_magic_bright = ppc_magic_bright;
   }

   public Long getPpc_contrast() {
      return this.ppc_contrast;
   }

   public void setPpc_contrast(Long ppc_contrast) {
      this.ppc_contrast = ppc_contrast;
   }

   public Long getPpc_brightness() {
      return this.ppc_brightness;
   }

   public void setPpc_brightness(Long ppc_brightness) {
      this.ppc_brightness = ppc_brightness;
   }

   public Long getPpc_colortone() {
      return this.ppc_colortone;
   }

   public void setPpc_colortone(Long ppc_colortone) {
      this.ppc_colortone = ppc_colortone;
   }

   public Long getPpc_red() {
      return this.ppc_red;
   }

   public void setPpc_red(Long ppc_red) {
      this.ppc_red = ppc_red;
   }

   public Long getPpc_green() {
      return this.ppc_green;
   }

   public void setPpc_green(Long ppc_green) {
      this.ppc_green = ppc_green;
   }

   public Long getPpc_blue() {
      return this.ppc_blue;
   }

   public void setPpc_blue(Long ppc_blue) {
      this.ppc_blue = ppc_blue;
   }

   public Long getPpc_size() {
      return this.ppc_size;
   }

   public void setPpc_size(Long ppc_size) {
      this.ppc_size = ppc_size;
   }

   public String getTime_current_time() {
      return this.time_current_time;
   }

   public void setTime_current_time(String time_current_time) {
      this.time_current_time = time_current_time;
   }

   public String getTime_on_time() {
      return this.time_on_time;
   }

   public void setTime_on_time(String time_on_time) {
      this.time_on_time = time_on_time;
   }

   public String getTime_off_time() {
      return this.time_off_time;
   }

   public void setTime_off_time(String time_off_time) {
      this.time_off_time = time_off_time;
   }

   public Long getPip_source() {
      return this.pip_source;
   }

   public void setPip_source(Long pip_source) {
      this.pip_source = pip_source;
   }

   public Long getPip_size() {
      return this.pip_size;
   }

   public void setPip_size(Long pip_size) {
      this.pip_size = pip_size;
   }

   public Long getPip_position() {
      return this.pip_position;
   }

   public void setPip_position(Long pip_position) {
      this.pip_position = pip_position;
   }

   public Long getPip_swap() {
      return this.pip_swap;
   }

   public void setPip_swap(Long pip_swap) {
      this.pip_swap = pip_swap;
   }

   public Long getPip_control() {
      return this.pip_control;
   }

   public void setPip_control(Long pip_control) {
      this.pip_control = pip_control;
   }

   public Long getSound_mode() {
      return this.sound_mode;
   }

   public void setSound_mode(Long sound_mode) {
      this.sound_mode = sound_mode;
   }

   public Long getSound_bass() {
      return this.sound_bass;
   }

   public void setSound_bass(Long sound_bass) {
      this.sound_bass = sound_bass;
   }

   public Long getSound_treble() {
      return this.sound_treble;
   }

   public void setSound_treble(Long sound_treble) {
      this.sound_treble = sound_treble;
   }

   public Long getSound_balance() {
      return this.sound_balance;
   }

   public void setSound_balance(Long sound_balance) {
      this.sound_balance = sound_balance;
   }

   public Long getSound_srs() {
      return this.sound_srs;
   }

   public void setSound_srs(Long sound_srs) {
      this.sound_srs = sound_srs;
   }

   public Long getSound_effect() {
      return this.sound_effect;
   }

   public void setSound_effect(Long sound_effect) {
      this.sound_effect = sound_effect;
   }

   public Long getImage_coarse() {
      return this.image_coarse;
   }

   public void setImage_coarse(Long image_coarse) {
      this.image_coarse = image_coarse;
   }

   public Long getImage_fine() {
      return this.image_fine;
   }

   public void setImage_fine(Long image_fine) {
      this.image_fine = image_fine;
   }

   public Long getImage_hpos() {
      return this.image_hpos;
   }

   public void setImage_hpos(Long image_hpos) {
      this.image_hpos = image_hpos;
   }

   public Long getImage_vpos() {
      return this.image_vpos;
   }

   public void setImage_vpos(Long image_vpos) {
      this.image_vpos = image_vpos;
   }

   public Long getImage_auto() {
      return this.image_auto;
   }

   public void setImage_auto(Long image_auto) {
      this.image_auto = image_auto;
   }

   public Long getSb_status() {
      return this.sb_status;
   }

   public void setSb_status(Long sb_status) {
      this.sb_status = sb_status;
   }

   public Long getSb_rgain() {
      return this.sb_rgain;
   }

   public void setSb_rgain(Long sb_rgain) {
      this.sb_rgain = sb_rgain;
   }

   public Long getSb_ggain() {
      return this.sb_ggain;
   }

   public void setSb_ggain(Long sb_ggain) {
      this.sb_ggain = sb_ggain;
   }

   public Long getSb_bgain() {
      return this.sb_bgain;
   }

   public void setSb_bgain(Long sb_bgain) {
      this.sb_bgain = sb_bgain;
   }

   public Long getSb_r_offset() {
      return this.sb_r_offset;
   }

   public void setSb_r_offset(Long sb_r_offset) {
      this.sb_r_offset = sb_r_offset;
   }

   public Long getSb_g_offset() {
      return this.sb_g_offset;
   }

   public void setSb_g_offset(Long sb_g_offset) {
      this.sb_g_offset = sb_g_offset;
   }

   public Long getSb_b_offset() {
      return this.sb_b_offset;
   }

   public void setSb_b_offset(Long sb_b_offset) {
      this.sb_b_offset = sb_b_offset;
   }

   public Long getSb_gain() {
      return this.sb_gain;
   }

   public void setSb_gain(Long sb_gain) {
      this.sb_gain = sb_gain;
   }

   public Long getSb_sharp() {
      return this.sb_sharp;
   }

   public void setSb_sharp(Long sb_sharp) {
      this.sb_sharp = sb_sharp;
   }

   public String getMnt_auto() {
      return this.mnt_auto;
   }

   public void setMnt_auto(String mnt_auto) {
      this.mnt_auto = mnt_auto;
   }

   public Long getMnt_manual() {
      return this.mnt_manual;
   }

   public void setMnt_manual(Long mnt_manual) {
      this.mnt_manual = mnt_manual;
   }

   public Long getMnt_video_wall() {
      return this.mnt_video_wall;
   }

   public void setMnt_video_wall(Long mnt_video_wall) {
      this.mnt_video_wall = mnt_video_wall;
   }

   public Long getMnt_format() {
      return this.mnt_format;
   }

   public void setMnt_format(Long mnt_format) {
      this.mnt_format = mnt_format;
   }

   public String getMnt_safety_screen_timer() {
      return this.mnt_safety_screen_timer;
   }

   public void setMnt_safety_screen_timer(String mnt_safety_screen_timer) {
      this.mnt_safety_screen_timer = mnt_safety_screen_timer;
   }

   public Long getMnt_safety_screen_run() {
      return this.mnt_safety_screen_run;
   }

   public void setMnt_safety_screen_run(Long mnt_safety_screen_run) {
      this.mnt_safety_screen_run = mnt_safety_screen_run;
   }

   public String getMnt_pixel_shift() {
      return this.mnt_pixel_shift;
   }

   public void setMnt_pixel_shift(String mnt_pixel_shift) {
      this.mnt_pixel_shift = mnt_pixel_shift;
   }

   public Long getMnt_safety_lock() {
      return this.mnt_safety_lock;
   }

   public void setMnt_safety_lock(Long mnt_safety_lock) {
      this.mnt_safety_lock = mnt_safety_lock;
   }

   public Long getMisc_remocon() {
      return this.misc_remocon;
   }

   public void setMisc_remocon(Long misc_remocon) {
      this.misc_remocon = misc_remocon;
   }

   public Long getMisc_panel_lock() {
      return this.misc_panel_lock;
   }

   public void setMisc_panel_lock(Long misc_panel_lock) {
      this.misc_panel_lock = misc_panel_lock;
   }

   public Long getMisc_osd() {
      return this.misc_osd;
   }

   public void setMisc_osd(Long misc_osd) {
      this.misc_osd = misc_osd;
   }

   public Long getMisc_all_lock() {
      return this.misc_all_lock;
   }

   public void setMisc_all_lock(Long misc_all_lock) {
      this.misc_all_lock = misc_all_lock;
   }

   public String getDiagnosis_display_status() {
      return this.diagnosis_display_status;
   }

   public void setDiagnosis_display_status(String diagnosis_display_status) {
      this.diagnosis_display_status = diagnosis_display_status;
   }

   public Long getDiagnosis_monitor_temperature() {
      return this.diagnosis_monitor_temperature;
   }

   public void setDiagnosis_monitor_temperature(Long diagnosis_monitor_temperature) {
      this.diagnosis_monitor_temperature = diagnosis_monitor_temperature;
   }

   public Long getDiagnosis_alarm_temperature() {
      return this.diagnosis_alarm_temperature;
   }

   public void setDiagnosis_alarm_temperature(Long diagnosis_alarm_temperature) {
      this.diagnosis_alarm_temperature = diagnosis_alarm_temperature;
   }

   public String getDiagnosis_panel_on_time() {
      return this.diagnosis_panel_on_time;
   }

   public void setDiagnosis_panel_on_time(String diagnosis_panel_on_time) {
      this.diagnosis_panel_on_time = diagnosis_panel_on_time;
   }

   public String getPv_video_picture_position_size() {
      return this.pv_video_picture_position_size;
   }

   public void setPv_video_picture_position_size(String pv_video_picture_position_size) {
      this.pv_video_picture_position_size = pv_video_picture_position_size;
   }

   public Long getPv_hdmi_black_level() {
      return this.pv_hdmi_black_level;
   }

   public void setPv_hdmi_black_level(Long pv_hdmi_black_level) {
      this.pv_hdmi_black_level = pv_hdmi_black_level;
   }

   public Long getPpc_gamma() {
      return this.ppc_gamma;
   }

   public void setPpc_gamma(Long ppc_gamma) {
      this.ppc_gamma = ppc_gamma;
   }

   public Long getPpc_hdmi_black_level() {
      return this.ppc_hdmi_black_level;
   }

   public void setPpc_hdmi_black_level(Long ppc_hdmi_black_level) {
      this.ppc_hdmi_black_level = ppc_hdmi_black_level;
   }

   public Long getAdvanced_rj45_setting_refresh() {
      return this.advanced_rj45_setting_refresh;
   }

   public void setAdvanced_rj45_setting_refresh(Long advanced_rj45_setting_refresh) {
      this.advanced_rj45_setting_refresh = advanced_rj45_setting_refresh;
   }

   public String getAdvanced_osd_display_type() {
      return this.advanced_osd_display_type;
   }

   public void setAdvanced_osd_display_type(String advanced_osd_display_type) {
      this.advanced_osd_display_type = advanced_osd_display_type;
   }

   public String getAdvanced_osd_display_type_value() {
      return this.advanced_osd_display_type_value;
   }

   public void setAdvanced_osd_display_type_value(String advanced_osd_display_type_value) {
      this.advanced_osd_display_type_value = advanced_osd_display_type_value;
   }

   public Long getAdvanced_fan_control() {
      return this.advanced_fan_control;
   }

   public void setAdvanced_fan_control(Long advanced_fan_control) {
      this.advanced_fan_control = advanced_fan_control;
   }

   public Long getAdvanced_fan_speed() {
      return this.advanced_fan_speed;
   }

   public void setAdvanced_fan_speed(Long advanced_fan_speed) {
      this.advanced_fan_speed = advanced_fan_speed;
   }

   public Long getAdvanced_reset() {
      return this.advanced_reset;
   }

   public void setAdvanced_reset(Long advanced_reset) {
      this.advanced_reset = advanced_reset;
   }

   public Long getAdvanced_auto_power() {
      return this.advanced_auto_power;
   }

   public void setAdvanced_auto_power(Long advanced_auto_power) {
      this.advanced_auto_power = advanced_auto_power;
   }

   public Long getAdvanced_user_auto_color() {
      return this.advanced_user_auto_color;
   }

   public void setAdvanced_user_auto_color(Long advanced_user_auto_color) {
      this.advanced_user_auto_color = advanced_user_auto_color;
   }

   public Long getAdvanced_stand_by() {
      return this.advanced_stand_by;
   }

   public void setAdvanced_stand_by(Long advanced_stand_by) {
      this.advanced_stand_by = advanced_stand_by;
   }

   public Long getPpc_color_temperature() {
      return this.ppc_color_temperature;
   }

   public void setPpc_color_temperature(Long ppc_color_temperature) {
      this.ppc_color_temperature = ppc_color_temperature;
   }

   public Timestamp getMdc_update_time() {
      return this.mdc_update_time;
   }

   public void setMdc_update_time(Timestamp mdc_update_time) {
      this.mdc_update_time = mdc_update_time;
   }

   public Long getChkSchChannel() {
      return this.chkSchChannel;
   }

   public void setChkSchChannel(Long chkSchChannel) {
      this.chkSchChannel = chkSchChannel;
   }

   public Boolean getWebcam() {
      return this.webcam;
   }

   public void setWebcam(Boolean webcam) {
      this.webcam = webcam;
   }

   public String getVwt_id() {
      return this.vwt_id;
   }

   public void setVwt_id(String vwt_id) {
      this.vwt_id = vwt_id;
   }

   public Long getIcon_error_sw() {
      return this.icon_error_sw;
   }

   public void setIcon_error_sw(Long icon_error_sw) {
      this.icon_error_sw = icon_error_sw;
   }

   public Long getIcon_error_hw() {
      return this.icon_error_hw;
   }

   public void setIcon_error_hw(Long icon_error_hw) {
      this.icon_error_hw = icon_error_hw;
   }

   public Long getIcon_alarm() {
      return this.icon_alarm;
   }

   public void setIcon_alarm(Long icon_alarm) {
      this.icon_alarm = icon_alarm;
   }

   public Long getIcon_process_content_download() {
      return this.icon_process_content_download;
   }

   public void setIcon_process_content_download(Long icon_process_content_download) {
      this.icon_process_content_download = icon_process_content_download;
   }

   public Long getIcon_process_log() {
      return this.icon_process_log;
   }

   public void setIcon_process_log(Long icon_process_log) {
      this.icon_process_log = icon_process_log;
   }

   public Long getIcon_process_sw_download() {
      return this.icon_process_sw_download;
   }

   public void setIcon_process_sw_download(Long icon_process_sw_download) {
      this.icon_process_sw_download = icon_process_sw_download;
   }

   public Long getIcon_memo() {
      return this.icon_memo;
   }

   public void setIcon_memo(Long icon_memo) {
      this.icon_memo = icon_memo;
   }

   public Long getIcon_backup() {
      return this.icon_backup;
   }

   public void setIcon_backup(Long icon_backup) {
      this.icon_backup = icon_backup;
   }

   public Long getChild_cnt() {
      return this.child_cnt;
   }

   public void setChild_cnt(Long child_cnt) {
      this.child_cnt = child_cnt;
   }

   public Long getConn_child_cnt() {
      return this.conn_child_cnt;
   }

   public void setConn_child_cnt(Long conn_child_cnt) {
      this.conn_child_cnt = conn_child_cnt;
   }

   public Boolean getIs_child() {
      return this.is_child;
   }

   public void setIs_child(Boolean is_child) {
      this.is_child = is_child;
   }

   public Boolean getHas_child() {
      return !"SIGNAGE".equalsIgnoreCase(this.device_type) && !"RSIGNAGE".equalsIgnoreCase(this.device_type) ? this.has_child : true;
   }

   public void setHas_child(Boolean has_child) {
      if (!"SIGNAGE".equalsIgnoreCase(this.device_type) && !"RSIGNAGE".equalsIgnoreCase(this.device_type)) {
         this.has_child = has_child;
      } else {
         this.has_child = true;
      }

   }

   public void setDefaultDBValues() {
      this.child_cnt = 0L;
      this.is_child = false;
      this.device_type_version = 1.0F;
   }

   public void initParamsForJson() {
      if (this.device_type.equalsIgnoreCase("LPLAYER")) {
         this.advanced_fan_control = null;
         this.advanced_fan_speed = null;
      } else if (this.device_type.equalsIgnoreCase("SIG_CHILD")) {
         if (this.basic_power == null) {
            this.basic_power = "";
         }

         if (this.basic_volume == null) {
            this.basic_volume = 0L;
         }

         if (this.basic_mute == null) {
            this.basic_mute = 0L;
         }

         if (this.basic_source == null) {
            this.basic_source = 0L;
         }

         this.basic_direct_channel = null;
         if (this.basic_panel_status == null) {
            this.basic_panel_status = 0L;
         }

         this.network_standby_mode = null;
         if (this.specialized_picture_mode == null) {
            this.specialized_picture_mode = -1L;
         }

         if (this.pv_mode == null) {
            this.pv_mode = 0L;
         }

         if (this.pv_contrast == null) {
            this.pv_contrast = 0L;
         }

         if (this.pv_brightness == null) {
            this.pv_brightness = 0L;
         }

         if (this.pv_sharpness == null) {
            this.pv_sharpness = 0L;
         }

         if (this.pv_color == null) {
            this.pv_color = 0L;
         }

         if (this.pv_tint == null) {
            this.pv_tint = 0L;
         }

         if (this.pv_colortone == null) {
            this.pv_colortone = 0L;
         }

         if (this.pv_color_temperature == null) {
            this.pv_color_temperature = 0L;
         }

         if (this.pv_size == null) {
            this.pv_size = 0L;
         }

         if (this.pv_digitalnr == null) {
            this.pv_digitalnr = 0L;
         }

         if (this.pv_filmmode == null) {
            this.pv_filmmode = 0L;
         }

         if (this.pv_video_picture_position_size == null) {
            this.pv_video_picture_position_size = "";
         }

         if (this.pv_hdmi_black_level == null) {
            this.pv_hdmi_black_level = 0L;
         }

         if (this.ppc_gamma == null) {
            this.ppc_gamma = 0L;
         }

         if (this.ppc_hdmi_black_level == null) {
            this.ppc_hdmi_black_level = 0L;
         }

         if (this.ppc_magic_bright == null) {
            this.ppc_magic_bright = 0L;
         }

         if (this.ppc_contrast == null) {
            this.ppc_contrast = 0L;
         }

         if (this.ppc_brightness == null) {
            this.ppc_brightness = 0L;
         }

         if (this.ppc_colortone == null) {
            this.ppc_colortone = 0L;
         }

         if (this.ppc_color_temperature == null) {
            this.ppc_color_temperature = 0L;
         }

         if (this.ppc_red == null) {
            this.ppc_red = 0L;
         }

         if (this.ppc_green == null) {
            this.ppc_green = 0L;
         }

         if (this.ppc_blue == null) {
            this.ppc_blue = 0L;
         }

         if (this.ppc_size == null) {
            this.ppc_size = 0L;
         }

         this.time_current_time = null;
         this.time_on_time = null;
         this.time_off_time = null;
         this.pip_source = null;
         this.pip_size = null;
         this.pip_position = null;
         this.pip_swap = null;
         this.pip_control = null;
         this.sound_mode = null;
         this.sound_bass = null;
         this.sound_treble = null;
         this.sound_balance = null;
         this.sound_srs = null;
         this.sound_effect = null;
         if (this.image_coarse == null) {
            this.image_coarse = 0L;
         }

         if (this.image_fine == null) {
            this.image_fine = 0L;
         }

         if (this.image_hpos == null) {
            this.image_hpos = 0L;
         }

         if (this.image_vpos == null) {
            this.image_vpos = 0L;
         }

         if (this.image_auto == null) {
            this.image_auto = 0L;
         }

         if (this.sb_status == null) {
            this.sb_status = 0L;
         }

         if (this.sb_rgain == null) {
            this.sb_rgain = 0L;
         }

         if (this.sb_ggain == null) {
            this.sb_ggain = 0L;
         }

         if (this.sb_bgain == null) {
            this.sb_bgain = 0L;
         }

         if (this.sb_r_offset == null) {
            this.sb_r_offset = 0L;
         }

         if (this.sb_g_offset == null) {
            this.sb_g_offset = 0L;
         }

         if (this.sb_b_offset == null) {
            this.sb_b_offset = 0L;
         }

         if (this.sb_gain == null) {
            this.sb_gain = 0L;
         }

         if (this.sb_sharp == null) {
            this.sb_sharp = 0L;
         }

         if (this.mnt_auto == null) {
            this.mnt_auto = "";
         }

         if (this.mnt_manual == null) {
            this.mnt_manual = 0L;
         }

         this.mnt_video_wall = null;
         if (this.mnt_format == null) {
            this.mnt_format = 0L;
         }

         if (this.mnt_safety_screen_timer == null) {
            this.mnt_safety_screen_timer = "";
         }

         if (this.mnt_safety_screen_run == null) {
            this.mnt_safety_screen_run = 0L;
         }

         if (this.mnt_pixel_shift == null) {
            this.mnt_pixel_shift = "";
         }

         if (this.mnt_safety_lock == null) {
            this.mnt_safety_lock = 0L;
         }

         this.advanced_rj45_setting_refresh = null;
         this.advanced_osd_display_type = null;
         this.advanced_fan_control = null;
         this.advanced_fan_speed = null;
         this.advanced_reset = null;
         this.advanced_auto_power = null;
         this.advanced_user_auto_color = null;
         this.advanced_stand_by = null;
         if (this.misc_osd == null) {
            this.misc_osd = 0L;
         }

         this.diagnosis_display_status = null;
         if (this.diagnosis_monitor_temperature == null) {
            this.diagnosis_monitor_temperature = 0L;
         }

         if (this.diagnosis_alarm_temperature == null) {
            this.diagnosis_alarm_temperature = 0L;
         }

         if (this.diagnosis_panel_on_time == null) {
            this.diagnosis_panel_on_time = "";
         }

         this.chkSchChannel = null;
      } else if (this.device_type.equals("APLAYER")) {
         this.basic_source = null;
         this.misc_osd = null;
         this.diagnosis_monitor_temperature = null;
         this.diagnosis_alarm_temperature = null;
         this.diagnosis_panel_on_time = null;
      }

   }

   public void setEmptyProperties() throws IllegalArgumentException, IllegalAccessException {
      Field[] var1 = this.getClass().getDeclaredFields();
      int var2 = var1.length;

      for(int var3 = 0; var3 < var2; ++var3) {
         Field f = var1[var3];
         f.setAccessible(true);
         if (f.getType().equals(String.class)) {
            f.set(this, "");
         } else if (f.getType().equals(Long.class)) {
            f.set(this, new Long(-1L));
         } else if (f.getType().equals(Float.class)) {
            f.set(this, new Float(-1.0F));
         } else if (f.getType().equals(Double.class)) {
            f.set(this, new Double(-1.0D));
         } else if (f.getType().equals(Integer.class)) {
            f.set(this, new Integer(-1));
         } else if (f.getType().equals(Boolean.class)) {
            f.set(this, new Boolean(false));
         }

         f.setAccessible(false);
      }

   }

   public Object clone() throws CloneNotSupportedException {
      return super.clone();
   }

   public Long getMax_power_saving() {
      return this.max_power_saving;
   }

   public void setMax_power_saving(Long max_power_saving) {
      this.max_power_saving = max_power_saving;
   }

   public Long getBrightness_limit() {
      return this.brightness_limit;
   }

   public void setBrightness_limit(Long brightness_limit) {
      this.brightness_limit = brightness_limit;
   }

   public Long getTouch_control_lock() {
      return this.touch_control_lock;
   }

   public void setTouch_control_lock(Long touch_control_lock) {
      this.touch_control_lock = touch_control_lock;
   }

   public String getAuto_source_switching() {
      return this.auto_source_switching;
   }

   public void setAuto_source_switching(String auto_source_switching) {
      this.auto_source_switching = auto_source_switching;
   }

   public String getWeb_browser_url() {
      return this.web_browser_url;
   }

   public void setWeb_browser_url(String web_browser_url) {
      this.web_browser_url = web_browser_url;
   }

   public String getCustom_logo() {
      return this.custom_logo;
   }

   public void setCustom_logo(String custom_logo) {
      this.custom_logo = custom_logo;
   }

   public String getError_flag() {
      return this.error_flag;
   }

   public void setError_flag(String error_flag) {
      this.error_flag = error_flag;
   }

   public Long getScreen_freeze() {
      return this.screen_freeze;
   }

   public void setScreen_freeze(Long screen_freeze) {
      this.screen_freeze = screen_freeze;
   }

   public Long getScreen_mute() {
      return this.screen_mute;
   }

   public void setScreen_mute(Long screen_mute) {
      this.screen_mute = screen_mute;
   }

   public Long getPv_mpeg_noise_filter() {
      return this.pv_mpeg_noise_filter;
   }

   public void setPv_mpeg_noise_filter(Long pv_mpeg_noise_filter) {
      this.pv_mpeg_noise_filter = pv_mpeg_noise_filter;
   }

   public String getAuto_brightness() {
      return this.auto_brightness;
   }

   public void setAuto_brightness(String auto_brightness) {
      this.auto_brightness = auto_brightness;
   }

   public Long getChild_alarm_temperature() {
      return this.child_alarm_temperature;
   }

   public void setChild_alarm_temperature(Long child_alarm_temperature) {
      this.child_alarm_temperature = child_alarm_temperature;
   }

   public Long getBlack_tone() {
      return this.black_tone;
   }

   public void setBlack_tone(Long black_tone) {
      this.black_tone = black_tone;
   }

   public Long getFlesh_tone() {
      return this.flesh_tone;
   }

   public void setFlesh_tone(Long flesh_tone) {
      this.flesh_tone = flesh_tone;
   }

   public Long getRgb_only_mode() {
      return this.rgb_only_mode;
   }

   public void setRgb_only_mode(Long rgb_only_mode) {
      this.rgb_only_mode = rgb_only_mode;
   }

   public Long getOsd_menu_size() {
      return this.osd_menu_size;
   }

   public void setOsd_menu_size(Long osd_menu_size) {
      this.osd_menu_size = osd_menu_size;
   }

   public String getLed_picture_size() {
      return this.led_picture_size;
   }

   public void setLed_picture_size(String led_picture_size) {
      this.led_picture_size = led_picture_size;
   }

   public String getLed_hdr() {
      return this.led_hdr;
   }

   public void setLed_hdr(String led_hdr) {
      this.led_hdr = led_hdr;
   }

   public Long getEco_sensor() {
      return this.eco_sensor;
   }

   public void setEco_sensor(Long eco_sensor) {
      this.eco_sensor = eco_sensor;
   }

   public Long getMin_brightness() {
      return this.min_brightness;
   }

   public void setMin_brightness(Long min_brightness) {
      this.min_brightness = min_brightness;
   }

   public Long getLive_mode() {
      return this.live_mode;
   }

   public void setLive_mode(Long live_mode) {
      this.live_mode = live_mode;
   }

   public Long getDisplay_output_mode() {
      return this.display_output_mode;
   }

   public void setDisplay_output_mode(Long display_output_mode) {
      this.display_output_mode = display_output_mode;
   }

   public Float getSensor_internal_temperature() {
      return this.sensor_internal_temperature;
   }

   public void setSensor_internal_temperature(Float sensor_internal_temperature) {
      this.sensor_internal_temperature = sensor_internal_temperature;
   }

   public Float getSensor_internal_humidity() {
      return this.sensor_internal_humidity;
   }

   public void setSensor_internal_humidity(Float sensor_internal_humidity) {
      this.sensor_internal_humidity = sensor_internal_humidity;
   }

   public Float getSensor_environment_temperature() {
      return this.sensor_environment_temperature;
   }

   public void setSensor_environment_temperature(Float sensor_environment_temperature) {
      this.sensor_environment_temperature = sensor_environment_temperature;
   }

   public Float getSensor_frontglass_temperature() {
      return this.sensor_frontglass_temperature;
   }

   public void setSensor_frontglass_temperature(Float sensor_frontglass_temperature) {
      this.sensor_frontglass_temperature = sensor_frontglass_temperature;
   }

   public Float getSensor_frontglass_humidity() {
      return this.sensor_frontglass_humidity;
   }

   public void setSensor_frontglass_humidity(Float sensor_frontglass_humidity) {
      this.sensor_frontglass_humidity = sensor_frontglass_humidity;
   }

   public void setModelType(String type) {
      this.device_model_name = type;
      this.device_model_code = type;
      this.device_type = type;
   }

   public Long getPicture_enhancer() {
      return this.picture_enhancer;
   }

   public void setPicture_enhancer(Long picture_enhancer) {
      this.picture_enhancer = picture_enhancer;
   }

   public String getColor_space() {
      return this.color_space;
   }

   public void setColor_space(String color_space) {
      this.color_space = color_space;
   }

   public Long getLed_hdr_dre() {
      return this.led_hdr_dre;
   }

   public void setLed_hdr_dre(Long led_hdr_dre) {
      this.led_hdr_dre = led_hdr_dre;
   }

   public Long getAuto_motion_plus_judder_reduction() {
      return this.auto_motion_plus_judder_reduction;
   }

   public void setAuto_motion_plus_judder_reduction(Long auto_motion_plus_judder_reduction) {
      this.auto_motion_plus_judder_reduction = auto_motion_plus_judder_reduction;
   }

   public Long getAuto_motion_plus() {
      return this.auto_motion_plus;
   }

   public void setAuto_motion_plus(Long auto_motion_plus) {
      this.auto_motion_plus = auto_motion_plus;
   }

   public Long getCleanup_user_data_interval() {
      return this.cleanup_user_data_interval;
   }

   public void setCleanup_user_data_interval(Long cleanup_user_data_interval) {
      this.cleanup_user_data_interval = cleanup_user_data_interval;
   }

   public Long getAuto_save() {
      return this.auto_save;
   }

   public void setAuto_save(Long auto_save) {
      this.auto_save = auto_save;
   }

   public Long getAuto_power_off() {
      return this.auto_power_off;
   }

   public void setAuto_power_off(Long auto_power_off) {
      this.auto_power_off = auto_power_off;
   }

   public String getSmtp() {
      return this.smtp;
   }

   public void setSmtp(String smtp) {
      this.smtp = smtp;
   }

   public String getPrint_server() {
      return this.print_server;
   }

   public void setPrint_server(String print_server) {
      this.print_server = print_server;
   }

   public Long getWeb_command() {
      return this.web_command;
   }

   public void setWeb_command(Long web_command) {
      this.web_command = web_command;
   }

   public Long getWeb_option() {
      return this.web_option;
   }

   public void setWeb_option(Long web_option) {
      this.web_option = web_option;
   }

   public String getWeb_url() {
      return this.web_url;
   }

   public void setWeb_url(String web_url) {
      this.web_url = web_url;
   }

   public String getWeb_end_time() {
      return this.web_end_time;
   }

   public void setWeb_end_time(String web_end_time) {
      this.web_end_time = web_end_time;
   }

   public Long getWeb_duration() {
      return this.web_duration;
   }

   public void setWeb_duration(Long web_duration) {
      this.web_duration = web_duration;
   }

   public Boolean getCleanup_user_data() {
      return this.cleanup_user_data;
   }

   public void setCleanup_user_data(Boolean cleanup_user_data) {
      this.cleanup_user_data = cleanup_user_data;
   }

   public Long getInstall_environment() {
      return this.install_environment;
   }

   public void setInstall_environment(Long install_environment) {
      this.install_environment = install_environment;
   }

   public void setInstall_environment(String install_environment) {
      if (Pattern.matches("[0-2]", install_environment)) {
         this.install_environment = Long.parseLong(install_environment);
      }

   }

   public String getDehumidify() {
      return this.dehumidify;
   }

   public void setDehumidify(String dehumidify) {
      this.dehumidify = dehumidify;
   }

   public Long getDimming_option() {
      return this.dimming_option;
   }

   public void setDimming_option(String dimming_option) {
      if (Pattern.matches("[0-3]", dimming_option)) {
         this.dimming_option = Long.parseLong(dimming_option);
      }

   }

   public void setDimming_option(Long dimming_option) {
      this.dimming_option = dimming_option;
   }

   public Long getDimming_night_time_override() {
      return this.dimming_night_time_override;
   }

   public void setDimming_night_time_override(Long dimming_night_time_override) {
      this.dimming_night_time_override = dimming_night_time_override;
   }

   public void setDimming_night_time_override(String dimming_night_time_override) {
      if (Pattern.matches("[0-1]", dimming_night_time_override)) {
         this.dimming_night_time_override = Long.parseLong(dimming_night_time_override);
      }

   }

   public String getDimming_eco_sensor() {
      return this.dimming_eco_sensor;
   }

   public void setDimming_eco_sensor(String dimming_eco_sensor) {
      this.dimming_eco_sensor = dimming_eco_sensor;
   }

   public String getDimming_sunrise_sunset() {
      return this.dimming_sunrise_sunset;
   }

   public void setDimming_sunrise_sunset(String dimming_sunrise_sunset) {
      this.dimming_sunrise_sunset = dimming_sunrise_sunset;
   }

   public String getDimming_sunrise_sunset_times() {
      return this.dimming_sunrise_sunset_times;
   }

   public void setDimming_sunrise_sunset_times(String dimming_sunrise_sunset_times) {
      this.dimming_sunrise_sunset_times = dimming_sunrise_sunset_times;
   }

   public String getDimming_brightness_output() {
      return this.dimming_brightness_output;
   }

   public void setDimming_brightness_output(String dimming_brightness_output) {
      this.dimming_brightness_output = dimming_brightness_output;
   }
}
