package com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.util.UserData;
import java.net.URI;
import java.util.Arrays;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Repository;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Repository
public class CidMappingRepositoryImpl implements CidMappingRepository {
  private final RestTemplate restTemplate;
  
  private final UserData userData;
  
  @Autowired
  public CidMappingRepositoryImpl(RestTemplate restTemplate, UserData userData) {
    this.restTemplate = restTemplate;
    this.userData = userData;
  }
  
  public String cidMapping(String contentId) {
    Boolean isUrlAuthNotAllowed = this.userData.isUrlAuthNotAllowedToThisMisServletSession();
    UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.newInstance().path("/servlet/CidMapping");
    if (false == isUrlAuthNotAllowed.booleanValue())
      uriComponentsBuilder.queryParam("id", new Object[] { this.userData.getUserId() }).queryParam("passwd", new Object[] { this.userData.getToken() }); 
    URI uri = uriComponentsBuilder.build().encode().toUri();
    HttpHeaders headers = new HttpHeaders();
    headers.add("userId", this.userData.getUserId());
    headers.add("token", this.userData.getToken());
    headers.setAccept(Arrays.asList(new MediaType[] { MediaType.APPLICATION_XML }));
    headers.setContentType(MediaType.MULTIPART_FORM_DATA);
    LinkedMultiValueMap linkedMultiValueMap = new LinkedMultiValueMap();
    linkedMultiValueMap.add("RequestCID", contentId);
    HttpEntity<MultiValueMap<String, String>> request = new HttpEntity(linkedMultiValueMap, (MultiValueMap)headers);
    ResponseEntity<String> response = this.restTemplate.exchange(uri, HttpMethod.POST, request, String.class);
    String newCid = response.getHeaders().get("CID").get(0);
    return newCid;
  }
}
