package com.samsung.magicinfo.framework.content.dao;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.ContentUtils;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.SequenceDB;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.content.constants.ContentConstants;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.ContentProductCodeHistoryEntity;
import com.samsung.magicinfo.framework.content.entity.ContentSearch;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.content.entity.TemplateDisplay;
import com.samsung.magicinfo.framework.content.entity.TemplateElement;
import com.samsung.magicinfo.framework.content.entity.TemplateElementData;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.ContentSearchInfo;
import com.samsung.magicinfo.framework.content.manager.ContentSearchInfoImpl;
import com.samsung.magicinfo.framework.content.manager.ConvertDataInfoImpl;
import com.samsung.magicinfo.framework.content.manager.SessionInfo;
import com.samsung.magicinfo.framework.content.manager.SessionInfoImpl;
import com.samsung.magicinfo.framework.role.manager.AbilityInfo;
import com.samsung.magicinfo.framework.role.manager.AbilityInfoImpl;
import com.samsung.magicinfo.framework.ruleset.manager.RuleSetInfo;
import com.samsung.magicinfo.framework.ruleset.manager.RuleSetInfoImpl;
import com.samsung.magicinfo.framework.scheduler.dao.EventInfoDao;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.setup.entity.InsightIndexValueWithLastModifiedDateEntity;
import com.samsung.magicinfo.framework.setup.entity.TagEntity;
import com.samsung.magicinfo.framework.setup.manager.TagInfo;
import com.samsung.magicinfo.framework.setup.manager.TagInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.restapi.contents.model.V2AdsContentPublisherInfo;
import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.lang.StringUtils;
import org.apache.ftpserver.db.DownloadInfo;
import org.apache.ftpserver.db.DownloadInfoImpl;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class ContentDao extends SqlSessionBaseDao {
   private Logger logger = LoggingManagerV2.getLogger(ContentDao.class);

   public ContentDao() {
      super();
   }

   public ContentDao(SqlSession sqlSession) {
      super(sqlSession);
   }

   public String getContentName(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentName(contentId);
   }

   public String getContentOrgCreatorId(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentOrgCreatorId(contentId);
   }

   public List getContentAllVerInfo(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentAllVerInfo(contentId);
   }

   public Map getAdsContentActiveVersionInfo(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getAdsContentActiveVersionInfo(contentId);
   }

   public Content getContentActiveVerInfo(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentActiveVerInfo(contentId);
   }

   public String getContentActiveVerVwtId(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentActiveVerVwtId(contentId);
   }

   public Content getContentActiveVerInfoTemporary(String contentId) throws SQLException {
      List contentList = ((ContentDaoMapper)this.getMapper()).getContentActiveVerInfoTemporary(contentId);
      return contentList.size() > 0 ? (Content)contentList.get(0) : null;
   }

   public Content getThumbInfoOfActiveVersion(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getThumbInfoOfActiveVersion(contentId);
   }

   public Map getThumbFileInfoOfActiveVersion(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getThumbFileInfoOfActiveVersion(contentId);
   }

   public Map getThumbFileInfoOfActiveVersionTemporary(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getThumbFileInfoOfActiveVersionTemporary(contentId);
   }

   public Content getContentVerInfo(String contentId, Long versionId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentVerInfo(contentId, versionId);
   }

   public List getSearchList(Map map) throws SQLException {
      try {
         Map newMap = this.createMap(map);
         return ((ContentDaoMapper)this.getMapper()).getSearchList(newMap);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   private Map createMap(Map oldMap) throws SQLException {
      Map map = new HashMap(oldMap);
      String userId = (String)map.get("creatorID");
      String searchText = (String)map.get("searchText");
      String searchId = (String)map.get("searchID");
      String sortColumn = (String)map.get("sortColumn");
      Long byUserOrganizationId = (Long)map.get("byUserOrganizationId");
      Long groupID = (Long)map.get("groupID");
      Long shareFolderId = (Long)map.get("shareFolderId");
      UserInfo uInfo = UserInfoImpl.getInstance();
      if (byUserOrganizationId != null) {
         map.put("organizationId", byUserOrganizationId);
      } else {
         Long organizationId = uInfo.getRootGroupIdByUserId(userId);
         if (organizationId != 0L) {
            map.put("organizationId", organizationId);
         }
      }

      if (groupID != null) {
         map.put("groupID", groupID);
      }

      if (userId != null) {
         map.put("userId", userId);
      }

      if (shareFolderId != null) {
         map.put("organizationId", "");
      }

      if (StringUtils.isNotEmpty(searchText)) {
         map.put("searchText", searchText.toUpperCase().replaceAll("_", "^_"));
      }

      ContentSearch contentSearch = this.getContentSearch(searchId);
      if (contentSearch != null) {
         map.put("search", contentSearch);
         map.put("searchRootId", this.getRootId(contentSearch.getCreator_id()));
         map.put("searchConstGROUPED", "GROUPED");
         map.put("searchConstUNGROUPED", "UNGROUPED");
         map.put("searchConstSHARED", "SHARED");
         map.put("searchConstORGAN", "ORGAN");
         map.put("searchConstSHARE_FLAG_DEFAULT", ContentConstants.SHARE_FLAG_DEFAULT);
         map.put("searchConstMEDIA_TYPE_TEMPLATE_EXTENSION", "LFT");
         if (contentSearch.getSearch_start_date() != null) {
            map.put("searchStartDate", DateUtils.timestamp2String(contentSearch.getSearch_start_date(), "yyyy-MM-dd HH:mm:ss"));
         }

         if (contentSearch.getSearch_end_date() != null) {
            map.put("searchEndDate", DateUtils.timestamp2String(contentSearch.getSearch_end_date(), "yyyy-MM-dd HH:mm:ss"));
         }
      }

      if (StringUtils.isNotEmpty(sortColumn)) {
         map.put("sortColumn", sortColumn.toUpperCase());
      }

      map.put("ConstMEDIA_TYPE_FOR_AUTHOR", ContentConstants.getMediaTypeForAuthor());
      return map;
   }

   public List getSearchListPage(Map map, int startPos, int pageSize) throws SQLException {
      try {
         Map newMap = this.createMap(map);
         --startPos;
         newMap.put("startPos", startPos);
         newMap.put("pageSize", pageSize);
         return ((ContentDaoMapper)this.getMapper()).getSearchListPage(newMap);
      } catch (Exception var5) {
         this.logger.error(var5);
         return null;
      }
   }

   public int getSearchListCnt(Map map) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getSearchListCnt(this.createMap(map));
      } catch (Exception var3) {
         this.logger.error(var3);
         return 0;
      }
   }

   private ContentSearch getContentSearch(String searchId) throws NumberFormatException, SQLException {
      if (StringUtils.isNotEmpty(searchId)) {
         ContentSearchInfo searchInfo = ContentSearchInfoImpl.getInstance();
         ContentSearch condition = searchInfo.getContentSearchBySearchId(Long.parseLong(searchId));
         return condition;
      } else {
         return null;
      }
   }

   public List getAllDeletedContentList(String creatorId) throws SQLException {
      try {
         UserInfo uInfo = UserInfoImpl.getInstance();
         long organizationId = uInfo.getRootGroupIdByUserId(creatorId);
         return ((ContentDaoMapper)this.getMapper()).getAllDeletedContentList(creatorId, organizationId);
      } catch (Exception var5) {
         this.logger.error(var5);
         return null;
      }
   }

   public List getAllDeletedContentListWithoutCreatorId(String creatorId) throws SQLException {
      try {
         UserInfo uInfo = UserInfoImpl.getInstance();
         long organizationId = uInfo.getRootGroupIdByUserId(creatorId);
         List manageGroupList = null;
         if (organizationId == 0L) {
            UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
            manageGroupList = userGroupInfo.getUserManageGroupListByUserId(creatorId);
         }

         return ((ContentDaoMapper)this.getMapper()).getAllDeletedContentListWithoutCreatorId(organizationId, manageGroupList);
      } catch (Exception var7) {
         this.logger.error(var7);
         return null;
      }
   }

   public List getAllContentList(String creatorId, String mediaType) throws SQLException {
      try {
         Map map = new HashMap();
         map.put("creatorId", creatorId);
         map.put("mediaType", mediaType);
         map.put("mediaTypeTemplate", "LFT");
         UserInfo uInfo = UserInfoImpl.getInstance();
         long organizationId = uInfo.getRootGroupIdByUserId(creatorId);
         map.put("organizationId", organizationId);
         return ((ContentDaoMapper)this.getMapper()).getAllContentList(map);
      } catch (Exception var7) {
         this.logger.error(var7);
         return null;
      }
   }

   public List getAllContentList(String creatorId, Long groupId, String mediaType) throws SQLException {
      try {
         Map map = new HashMap();
         map.put("creatorId", creatorId);
         map.put("groupId", groupId);
         map.put("mediaType", mediaType);
         map.put("mediaTypeTemplate", "LFT");
         UserInfo uInfo = UserInfoImpl.getInstance();
         long organizationId = uInfo.getRootGroupIdByUserId(creatorId);
         map.put("organizationId", organizationId);
         return ((ContentDaoMapper)this.getMapper()).getAllContentList(map);
      } catch (Exception var8) {
         this.logger.error(var8);
         return null;
      }
   }

   public List getContentList(Map map) throws SQLException {
      try {
         Map newMap = this.createContentListWithExtraParams(map);
         return ((ContentDaoMapper)this.getMapper()).getContentList(newMap);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public List getContentListPage(Map map, int startPos, int pageSize) throws SQLException {
      try {
         Map newMap = this.createContentListWithExtraParams(map);
         this.putDeviceTypeVersion(map, newMap);
         --startPos;
         newMap.put("startPos", startPos);
         newMap.put("pageSize", pageSize);
         if (newMap.get("contentFilter") != null && !newMap.get("contentFilter").equals("")) {
            newMap.put("media_type_filter", newMap.get("contentFilter"));
         }

         String tagInputType = null;

         try {
            tagInputType = (String)newMap.get("tagInputType");
         } catch (Exception var13) {
            tagInputType = null;
         }

         List contentIds = new ArrayList();
         String[] tag;
         if (newMap.get("tagFilter") != null && !newMap.get("tagFilter").equals("") && newMap.get("category") != null && !newMap.get("category").equals("")) {
            tag = (String[])((String[])newMap.get("tagFilter"));
            String category = (String)newMap.get("category");
            String[] categoryList = category.split(",");
            List contentIdList = ((ContentDaoMapper)this.getMapper()).getCategorywithTagFilter(categoryList, tag, tagInputType);
            Iterator var22 = contentIdList.iterator();

            while(var22.hasNext()) {
               Map content = (Map)var22.next();
               contentIds.add((String)content.get("content_id"));
            }

            if (contentIds.size() == 0) {
               contentIds.add("null");
            }

            newMap.put("contentIdList", contentIds);
         } else {
            if (newMap.get("tagFilter") != null && !newMap.get("tagFilter").equals("")) {
               tag = (String[])((String[])newMap.get("tagFilter"));
               List contentIdList = ((ContentDaoMapper)this.getMapper()).getcontentIdfromTag(tag, tagInputType);
               Iterator var9 = contentIdList.iterator();

               while(var9.hasNext()) {
                  Map content = (Map)var9.next();
                  contentIds.add((String)content.get("content_id"));
               }

               if (contentIds.size() == 0) {
                  contentIds.add("null");
               }

               newMap.put("contentIdList", contentIds);
            }

            if (newMap.get("category") != null && !newMap.get("category").equals("")) {
               String category = (String)newMap.get("category");
               String[] categoryList = category.split(",");
               List contentIdList = ((ContentDaoMapper)this.getMapper()).getcontentIdfromCategory(categoryList);
               Iterator var20 = contentIdList.iterator();

               while(var20.hasNext()) {
                  Map content = (Map)var20.next();
                  contentIds.add((String)content.get("content_id"));
               }

               if (contentIds.size() == 0) {
                  contentIds.add("null");
               }

               newMap.put("contentIdList", contentIds);
            }
         }

         if (map.get("isTLFD") != null && ((String)map.get("isTLFD")).equals("Y")) {
            newMap.put("isTLFD", "true");
            return ((ContentDaoMapper)this.getMapper()).getTLFDListPage(newMap);
         } else {
            return ((ContentDaoMapper)this.getMapper()).getContentListPage(newMap);
         }
      } catch (Exception var14) {
         this.logger.error(var14);
         return null;
      }
   }

   public int getContentListCnt(Map map) throws SQLException {
      try {
         Map newMap = this.createContentListWithExtraParams(map);
         this.putDeviceTypeVersion(map, newMap);
         if (newMap.get("contentFilter") != null && !newMap.get("contentFilter").equals("")) {
            newMap.put("media_type_filter", newMap.get("contentFilter"));
         }

         String tagInputType = null;

         try {
            tagInputType = (String)newMap.get("tagInputType");
         } catch (Exception var11) {
         }

         List contentIds = new ArrayList();
         String[] tag;
         if (newMap.get("tagFilter") != null && !newMap.get("tagFilter").equals("") && newMap.get("category") != null && !newMap.get("category").equals("")) {
            tag = (String[])((String[])newMap.get("tagFilter"));
            String category = (String)newMap.get("category");
            String[] categoryList = category.split(",");
            List contentIdList = ((ContentDaoMapper)this.getMapper()).getCategorywithTagFilter(categoryList, tag, tagInputType);
            Iterator var20 = contentIdList.iterator();

            while(var20.hasNext()) {
               Map content = (Map)var20.next();
               contentIds.add((String)content.get("content_id"));
            }

            if (contentIds.size() == 0) {
               contentIds.add("null");
            }

            newMap.put("contentIdList", contentIds);
         } else {
            if (newMap.get("tagFilter") != null && !newMap.get("tagFilter").equals("")) {
               tag = (String[])((String[])newMap.get("tagFilter"));
               List contentIdList = ((ContentDaoMapper)this.getMapper()).getcontentIdfromTag(tag, tagInputType);
               Iterator var7 = contentIdList.iterator();

               while(var7.hasNext()) {
                  Map content = (Map)var7.next();
                  contentIds.add((String)content.get("content_id"));
               }

               if (contentIds.size() == 0) {
                  contentIds.add("null");
               }

               newMap.put("contentIdList", contentIds);
            }

            if (newMap.get("category") != null && !newMap.get("category").equals("")) {
               String category = (String)newMap.get("category");
               String[] categoryList = category.split(",");
               List contentIdList = ((ContentDaoMapper)this.getMapper()).getcontentIdfromCategory(categoryList);
               Iterator var18 = contentIdList.iterator();

               while(var18.hasNext()) {
                  Map content = (Map)var18.next();
                  contentIds.add((String)content.get("content_id"));
               }

               if (contentIds.size() == 0) {
                  contentIds.add("null");
               }

               newMap.put("contentIdList", contentIds);
            }
         }

         if (map.get("isTLFD") != null && ((String)map.get("isTLFD")).equals("Y")) {
            newMap.put("isTLFD", "true");
            return ((ContentDaoMapper)this.getMapper()).getTLFDListCnt(newMap);
         } else {
            return ((ContentDaoMapper)this.getMapper()).getContentListCnt(newMap);
         }
      } catch (Exception var12) {
         this.logger.error("", var12);
         return 0;
      }
   }

   public int getRejectCnt(long organizationId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getRejectCnt(organizationId);
   }

   private void putDeviceTypeVersion(Map map, Map newMap) {
      String deviceTypeVersion = StringUtils.isBlank((String)map.get("deviceTypeVersion")) ? Float.toString(CommonDataConstants.TYPE_VERSION_1_0) : (String)map.get("deviceTypeVersion");
      float f_deviceTypeVersion = CommonDataConstants.TYPE_VERSION_1_0;
      if (deviceTypeVersion != null) {
         f_deviceTypeVersion = Float.parseFloat(deviceTypeVersion);
      }

      newMap.put("deviceTypeVersion", f_deviceTypeVersion);
   }

   public ContentFile getFileInfo(String fileId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getFileInfo(fileId);
   }

   public List getFilesByIds(List fileIds) throws SQLException {
      int index = 0;
      List files = new ArrayList();
      if (fileIds != null && fileIds.size() > 0) {
         try {
            while(fileIds.size() - index > 0) {
               if (fileIds.size() - index > 500) {
                  files.addAll(((ContentDaoMapper)this.getMapper()).getFilesByIds(fileIds.subList(index, index + 500)));
                  index += 500;
               } else {
                  files.addAll(((ContentDaoMapper)this.getMapper()).getFilesByIds(fileIds.subList(index, fileIds.size())));
                  index += fileIds.size();
               }
            }
         } catch (Exception var5) {
            files = null;
         }

         return files;
      } else {
         return files;
      }
   }

   public ContentFile getMainFileInfo(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getMainFileInfo(contentId);
   }

   public ContentFile getRulesetFileInfo(String rulesetId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getRulesetFileInfo(rulesetId);
   }

   public ContentFile getMainFileInfoTemporary(String contentId) throws SQLException {
      List list = ((ContentDaoMapper)this.getMapper()).getMainFileInfoTemporary(contentId);
      return list != null && list.size() > 0 ? (ContentFile)list.get(0) : null;
   }

   public String getMainFileName(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getMainFileName(contentId);
   }

   public ContentFile getThumbFileInfo(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getThumbFileInfo(contentId);
   }

   public List getContentUseInPlaylist(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentUseInPlaylist(contentId);
   }

   public List getContentUseInSchedule(String contentID) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentUseInSchedule(contentID);
   }

   public ContentFile getSfiFileInfo(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getSfiFileInfo(contentId);
   }

   public ContentFile getMainFileInfoOfTmpVer(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getMainFileInfoOfTmpVer(contentId);
   }

   public ContentFile getThumbFileInfoOfTempVer(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getThumbFileInfoOfTempVer(contentId);
   }

   public String getFileHash(String fileId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getFileHash(fileId);
   }

   public Long getFileSize(String fileId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getFileSize(fileId);
   }

   public int updateFileSize(String fileID, Long fileSize, Long old_total_size) throws SQLException {
      if (((ContentDaoMapper)this.getMapper()).update_UpdateFileSize(fileSize, fileID) == 1) {
         Long totalSize = this.getTotalSize(fileID) - old_total_size + fileSize;
         ((ContentDaoMapper)this.getMapper()).update2_UpdateFileSize(totalSize, fileID);
      }

      return 0;
   }

   public Long getTotalSize(String fileID) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getTotalSize(fileID);
   }

   public String getFileName(String fileId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getFileName(fileId);
   }

   public String getFilePath(String fileID) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getFilePath(fileID);
   }

   public List getThumbFileList(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getThumbFileList(contentId);
   }

   public List getThumbMovieFileList(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getThumbMovieFileList(contentId);
   }

   public List getFileList(String contentId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getFileList(contentId);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public List getFileListForApi(String contentId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getFileListForApi(contentId);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public List getFileList(String contentId, Long versionId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getFileListByContentAndVersion(contentId, versionId);
      } catch (Exception var4) {
         this.logger.error(var4);
         return null;
      }
   }

   public List getFileListPage(String contentId, Long versionId, int startPos, int pageSize) throws SQLException {
      try {
         ContentDaoMapper var10000 = (ContentDaoMapper)this.getMapper();
         --startPos;
         return var10000.getFileListPage(contentId, versionId, startPos, pageSize);
      } catch (Exception var6) {
         this.logger.error(var6);
         return null;
      }
   }

   public int getFileListCnt(String contentId, Long versionId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getFileListCnt(contentId, versionId);
      } catch (Exception var4) {
         this.logger.error(var4);
         return 0;
      }
   }

   public List getActiveFileList(String contentId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getActiveFileList(contentId);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public List getActiveFileListByType(String contentId, boolean isThumbnail) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getActiveFileListByType(contentId, isThumbnail);
      } catch (Exception var4) {
         this.logger.error(var4);
         return null;
      }
   }

   public List getActiveFileListPage(String contentId, int startPos, int pageSize) throws SQLException {
      try {
         ContentDaoMapper var10000 = (ContentDaoMapper)this.getMapper();
         --startPos;
         return var10000.getActiveFileListPage(contentId, startPos, pageSize);
      } catch (Exception var5) {
         this.logger.error(var5);
         return null;
      }
   }

   public int getActiveFileListCnt(String contentId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getActiveFileListCnt(contentId);
      } catch (Exception var3) {
         this.logger.error(var3);
         return 0;
      }
   }

   public Boolean isExistFile(ContentFile file) throws Exception {
      return this.isExistFile(file, (SqlSession)null);
   }

   private Boolean isExistFile(ContentFile file, SqlSession session) throws Exception {
      if (file.getFile_type().equalsIgnoreCase("STRM_MAIN")) {
         return false;
      } else if (CommonConfig.get("saas.eu.enable") != null && CommonConfig.get("saas.eu.enable").equalsIgnoreCase("TRUE")) {
         try {
            return ((ContentDaoMapper)this.getMapper(session)).existFileByIDCnt(file.getFile_id()) > 0;
         } catch (Exception var4) {
            this.logger.error(var4);
            return false;
         }
      } else {
         try {
            if (file.getFile_id() != null && !file.getFile_id().isEmpty()) {
               if (((ContentDaoMapper)this.getMapper(session)).existFileByIDCnt(file.getFile_id()) > 0) {
                  return true;
               }

               if (file.getFile_name().equalsIgnoreCase("FtpMetadata.FTP") || file.getFile_name().equalsIgnoreCase("CifsMetadata.CIFS")) {
                  return false;
               }
            }

            return ((ContentDaoMapper)this.getMapper(session)).numberOfExistingFileByHash(file.getFile_name(), file.getFile_size(), file.getHash_code()) > 0;
         } catch (Exception var5) {
            this.logger.error(var5);
            return false;
         }
      }
   }

   private Boolean isExistFileByID(String fileId, SqlSession session) throws SQLException {
      try {
         return session == null ? ((ContentDaoMapper)this.getMapper()).existFileByIDCnt(fileId) > 0 : ((ContentDaoMapper)this.getMapper(session)).existFileByIDCnt(fileId) > 0;
      } catch (Exception var4) {
         this.logger.error(var4);
         return false;
      }
   }

   public Boolean isExistFileByID(String fileId) throws SQLException {
      return this.isExistFileByID(fileId, (SqlSession)null);
   }

   private Boolean isExistFileByHash(String fileName, Long fileSize, String hashCode, SqlSession session) throws SQLException {
      try {
         return session == null ? ((ContentDaoMapper)this.getMapper()).numberOfExistingFileByHash(fileName, fileSize, hashCode) > 0 : ((ContentDaoMapper)this.getMapper(session)).numberOfExistingFileByHash(fileName, fileSize, hashCode) > 0;
      } catch (Exception var6) {
         this.logger.error(var6);
         return false;
      }
   }

   public int numberOfExistingFileByHash(String fileName, Long fileSize, String hashCode) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).numberOfExistingFileByHash(fileName, fileSize, hashCode);
   }

   public String getFileIdByHash(String fileName, String hashCode) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getFileIdByHash(fileName, hashCode);
   }

   public Boolean isExistFileByHash(String fileName, Long fileSize, String hashCode) throws SQLException {
      return this.isExistFileByHash(fileName, fileSize, hashCode, (SqlSession)null);
   }

   public String getFileIDByHash(String fileName, Long fileSize, String hashCode) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getFileIDByHash(fileName, fileSize, hashCode);
   }

   public String getFileIDByHashCreator(String fileName, Long fileSize, String hashCode, String creatorId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getFileIDByHashCreator(fileName, fileSize, hashCode, creatorId);
   }

   public Boolean isExistFileByIDAndHash(String fileId, String fileName, String hashCode) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).numberOfExistingFileByIDAndHash(fileId, fileName, hashCode) > 0;
      } catch (Exception var5) {
         this.logger.error(var5);
         return false;
      }
   }

   public Boolean isExistContentID(String contentId, String creatorId) throws SQLException {
      try {
         UserInfo uInfo = UserInfoImpl.getInstance();
         long organizationId = uInfo.getRootGroupIdByUserId(creatorId);
         return ((ContentDaoMapper)this.getMapper()).numberOfExistActiveContentID(contentId, creatorId, organizationId) > 0;
      } catch (Exception var6) {
         this.logger.error(var6);
         return false;
      }
   }

   public Boolean isExistContentForCidMapping(String contentId, String creatorId) throws SQLException {
      try {
         UserInfo uInfo = UserInfoImpl.getInstance();
         long organizationId = uInfo.getRootGroupIdByUserId(creatorId);
         return ((ContentDaoMapper)this.getMapper()).numberOfExistContentForCidMapping(contentId, creatorId, organizationId) > 0;
      } catch (Exception var6) {
         this.logger.error(var6);
         return false;
      }
   }

   private Boolean isExistContentForCidMapping(String contentId, String creatorId, SqlSession session) throws SQLException {
      try {
         UserInfo uInfo = UserInfoImpl.getInstance(session);
         long organizationId = uInfo.getRootGroupIdByUserId(creatorId);
         return ((ContentDaoMapper)this.getMapper(session)).numberOfExistContentForCidMapping(contentId, creatorId, organizationId) > 0;
      } catch (Exception var7) {
         this.logger.error(var7);
         return false;
      }
   }

   public Boolean isExistContentID(String contentId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).numberOfExistContentID(contentId) > 0;
      } catch (Exception var3) {
         this.logger.error(var3);
         return false;
      }
   }

   private Boolean isExistContentVersion(String contentId, Long versionId, SqlSession session) throws SQLException {
      try {
         return session == null ? ((ContentDaoMapper)this.getMapper()).numberOfExistContentVersion(contentId, versionId) > 0 : ((ContentDaoMapper)this.getMapper(session)).numberOfExistContentVersion(contentId, versionId) > 0;
      } catch (Exception var5) {
         this.logger.error(var5);
         return false;
      }
   }

   public Boolean isExistContentVersion(String contentId, Long versionId) throws SQLException {
      return this.isExistContentVersion(contentId, versionId, (SqlSession)null);
   }

   public Boolean isUpdatableContent(String contentId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).numberOfUpdatableContent(contentId) > 0;
      } catch (Exception var3) {
         this.logger.error(var3);
         return false;
      }
   }

   public Boolean isDeletableFile(String fileId, String contentId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).numberOfDeletableFile(fileId, contentId) == 0;
      } catch (Exception var4) {
         this.logger.error(var4);
         return false;
      }
   }

   public Boolean isDeletableFileByVersion(String fileId, String contentId, long versionId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).numberOfDeletableFileByVersion(fileId, contentId, versionId) == 0;
      } catch (Exception var6) {
         this.logger.error(var6);
         return false;
      }
   }

   public Boolean isDeletableContent(String contentId, String sessionId) throws SQLException {
      ScheduleInfo sInfo = ScheduleInfoImpl.getInstance();
      ConvertDataInfoImpl convertData = ConvertDataInfoImpl.getInstance();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      SessionInfo sessionInfo = SessionInfoImpl.getInstance();
      EventInfoDao eventInfoDao = new EventInfoDao();
      RuleSetInfo rulesetDao = RuleSetInfoImpl.getInstance();
      if (!this.isLockedContent(contentId, sessionId) && !sessionInfo.isExistTarget(contentId)) {
         if (rulesetDao.isUsedContentsForRuleset(contentId)) {
            return false;
         } else if (!this.isUsedForPlaylist(contentId)) {
            List listTemplateContent = this.getContentByTemplateId(contentId);
            if (listTemplateContent != null && listTemplateContent.size() != 0) {
               return false;
            } else {
               List listDlkElementContent = this.getDlkContentsIncludedElementContentId(contentId);
               if (listDlkElementContent != null && listDlkElementContent.size() != 0) {
                  return false;
               } else {
                  List list1 = sInfo.getProgramByContentId(contentId);
                  if (list1 != null && list1.size() != 0) {
                     return false;
                  } else {
                     List list5 = convertData.getConvertDataNameByContentId(contentId);
                     if (list5 != null && list5.size() != 0) {
                        return false;
                     } else {
                        List list6 = eventInfoDao.getEventIdListByContentId(contentId);
                        if (list6 != null && list6.size() != 0) {
                           return false;
                        } else {
                           List list7 = contentInfo.getDlkContentIdByIputDataContentId(contentId);
                           return list7 != null && list7.size() != 0 ? false : true;
                        }
                     }
                  }
               }
            }
         } else {
            return false;
         }
      } else {
         return false;
      }
   }

   public Boolean isLockedContent(String contentId, String sessionId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).numberOfLockedContent(contentId, sessionId) > 0;
      } catch (Exception var4) {
         this.logger.error(var4);
         return false;
      }
   }

   public Boolean isUsedForPlaylist(String contentId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).numberOfUsedForPlaylist(contentId) > 0;
      } catch (Exception var3) {
         this.logger.error(var3);
         return false;
      }
   }

   public Boolean isUsedForLitePlaylist(String contentId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).numberOfUsedForLitePlaylist(contentId) > 0;
      } catch (Exception var3) {
         this.logger.error(var3);
         return false;
      }
   }

   public int addContent(Content content) throws SQLException, Exception {
      SqlSession session = this.openNewSession(false);

      byte var4;
      try {
         int var3 = this.addContent(content, session);
         return var3;
      } catch (SQLException var8) {
         this.logger.error(var8);
         session.rollback();
         var4 = -1;
      } finally {
         session.close();
      }

      return var4;
   }

   public int addContentForMega(Content content) throws SQLException, Exception {
      SqlSession session = this.openNewSession(false);

      byte var4;
      try {
         int var3 = this.addContentForMega(content, session);
         return var3;
      } catch (SQLException var8) {
         this.logger.error(var8);
         session.rollback();
         var4 = -1;
      } finally {
         session.close();
      }

      return var4;
   }

   private int addContent(Content content, SqlSession session) throws SQLException, Exception {
      content.setContent_name(StrUtils.nvl(content.getContent_name()).replaceAll("\\\\", "<"));
      content.setContent_meta_data(StrUtils.nvl(content.getContent_meta_data()).replaceAll("\\\\", "<"));
      if (!this.isExistContentForCidMapping(content.getContent_id(), content.getOrg_creator_id(), session) && ((ContentDaoMapper)this.getMapper(session)).addContentInfo(content) <= 0) {
         session.rollback();
         return -1;
      } else {
         content.setVersion_id(0L);
         if (ContentConstants.getMediaTypeForAuthor().contains(content.getMedia_type())) {
            content.setIs_active("Y");
         } else {
            content.setIs_active("N");
         }

         if (this.isExistContentVersion(content.getContent_id(), 0L, session)) {
            this.deleteVersionContent(content.getContent_id(), 0L, session);
         }

         if (this.addContentVersionInfo(content, session) <= 0) {
            session.rollback();
            return -1;
         } else {
            if (!content.getMedia_type().equals("TLFD")) {
               ((ContentDaoMapper)this.getMapper(session)).deleteMapGroupContent(content.getContent_id());
               if (((ContentDaoMapper)this.getMapper(session)).addMapGroupContent(content.getContent_id(), content.getGroup_id()) <= 0) {
                  session.rollback();
                  return -1;
               }
            } else if (((ContentDaoMapper)this.getMapper(session)).addMapGroupTLFD(content.getContent_id(), content.getGroup_id()) <= 0) {
               session.rollback();
               return -1;
            }

            if (content.getArr_file_list() != null) {
               for(int i = 0; i < content.getArr_file_list().size(); ++i) {
                  ContentFile file = (ContentFile)content.getArr_file_list().get(i);
                  if ((!this.isExistFile(file, session) || file.getFile_size() == 0L && file.getFile_type().equalsIgnoreCase("DLK")) && ((ContentDaoMapper)this.getMapper(session)).addFile(file) <= 0) {
                     session.rollback();
                     return -1;
                  }

                  if (!this.isExistMapContentFile(content.getContent_id(), content.getVersion_id(), file.getFile_id(), session) && ((ContentDaoMapper)this.getMapper(session)).addMapContentFile(content.getContent_id(), content.getVersion_id(), file.getFile_id()) <= 0) {
                     session.rollback();
                     return -1;
                  }
               }
            }

            this.setApprovalStatus(content.getContent_id(), content.getApproval_status(), "", session);
            if (content.getApproval_status() != null && content.getApproval_status().equalsIgnoreCase("UNAPPROVED")) {
               UserInfo userInfo = UserInfoImpl.getInstance();
               if (userInfo.getOrganNameByUserId(content.getCreator_id()).equals("ROOT")) {
                  this.addContentApproverMap(content.getContent_id(), "admin", session);
               } else {
                  List approverList = userInfo.getContentApproverListByGroupId(userInfo.getRootGroupIdByUserId(content.getCreator_id()));
                  if (approverList != null) {
                     for(int i = 0; i < approverList.size(); ++i) {
                        String tmpUserId = (String)((Map)approverList.get(i)).get("user_id");
                        if (!this.isContentApproverMap(content.getContent_id(), tmpUserId, session)) {
                           this.addContentApproverMap(content.getContent_id(), tmpUserId, session);
                        }
                     }
                  }
               }
            }

            if (this.setContentInfo(content.getContent_id(), content.getContent_name(), content.getContent_meta_data(), content.getShare_flag(), content.getPolling_interval(), session) <= 0) {
               session.rollback();
               return -1;
            } else {
               session.commit();
               return 1;
            }
         }
      }
   }

   private int addContentForMega(Content content, SqlSession session) throws SQLException, Exception {
      content.setContent_name(content.getContent_name().replaceAll("\\\\", "<"));
      if (content.getContent_meta_data() == null) {
         content.setContent_meta_data("");
      } else {
         content.setContent_meta_data(content.getContent_meta_data().replaceAll("\\\\", "<"));
      }

      boolean isModify = this.isExistContentForCidMapping(content.getContent_id(), content.getOrg_creator_id(), session);
      if (!isModify && ((ContentDaoMapper)this.getMapper(session)).addContentInfo(content) <= 0) {
         session.rollback();
         return -1;
      } else {
         if (isModify) {
            this.deleteVersionContent(content.getContent_id(), content.getVersion_id() - 1L, session);
         }

         if (this.addContentVersionInfo(content, session) <= 0) {
            session.rollback();
            return -1;
         } else {
            ((ContentDaoMapper)this.getMapper(session)).deleteMapGroupContent(content.getContent_id());
            if (((ContentDaoMapper)this.getMapper(session)).addMapGroupContent(content.getContent_id(), content.getGroup_id()) <= 0) {
               session.rollback();
               return -1;
            } else {
               if (content.getArr_file_list() != null) {
                  for(int i = 0; i < content.getArr_file_list().size(); ++i) {
                     ContentFile file = (ContentFile)content.getArr_file_list().get(i);
                     if (file.getFile_id() == null || file.getFile_id().equals("")) {
                        session.rollback();
                        return -1;
                     }

                     if (!this.isExistFile(file, session)) {
                        if (((ContentDaoMapper)this.getMapper(session)).addFile(file) <= 0) {
                           session.rollback();
                           return -1;
                        }

                        if (!this.isExistMapContentFile(content.getContent_id(), content.getVersion_id(), file.getFile_id(), session) && ((ContentDaoMapper)this.getMapper(session)).addMapContentFile(content.getContent_id(), content.getVersion_id(), file.getFile_id()) <= 0) {
                           session.rollback();
                           return -1;
                        }
                     }
                  }
               }

               if (this.setContentInfo(content.getContent_id(), content.getContent_name(), content.getContent_meta_data(), content.getShare_flag(), content.getPolling_interval(), session) <= 0) {
                  session.rollback();
                  return -1;
               } else {
                  session.commit();
                  return 1;
               }
            }
         }
      }
   }

   private boolean isExistMapContentFile(String contentId, Long versionId, String fileId, SqlSession session) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper(session)).numberOfMapContentFile(contentId, versionId, fileId) > 0;
      } catch (Exception var6) {
         this.logger.error(var6);
         return false;
      }
   }

   public boolean isExistMapContentFile(String contentId, Long versionId, String fileId) throws SQLException {
      return this.isExistMapContentFile(contentId, versionId, fileId, (SqlSession)null);
   }

   public Long setActiveVersion(String contentId, boolean bTrigger) throws SQLException, Exception {
      SqlSession session = this.openNewSession(false);

      Long var5;
      try {
         Long var4 = this.setActiveVersion(contentId, bTrigger, session);
         return var4;
      } catch (SQLException var9) {
         this.logger.error(var9);
         session.rollback();
         var5 = -1L;
      } finally {
         session.close();
      }

      return var5;
   }

   private Long setActiveVersion(String contentId, boolean bTrigger, SqlSession session) throws SQLException, Exception {
      Long versionId = this.getContentNextVer(contentId, session);
      List fileList = this.getFileList(contentId, 0L);
      if (this.setTempVersionContentActive(contentId, versionId, session) <= 0) {
         session.rollback();
         return -1L;
      } else {
         if (this.isExistContentVersion(contentId, 0L, session)) {
            this.deleteVersionContent(contentId, 0L, session);
         }

         if (fileList != null) {
            for(int i = 0; i < fileList.size(); ++i) {
               ContentFile file = (ContentFile)fileList.get(i);
               if (!this.isExistMapContentFile(contentId, versionId, file.getFile_id(), session) && ((ContentDaoMapper)this.getMapper(session)).addMapContentFile(contentId, versionId, file.getFile_id()) <= 0) {
                  session.rollback();
                  return -1L;
               }
            }
         }

         ((ContentDaoMapper)this.getMapper(session)).setOtherVersionInactive(contentId, versionId);
         if (((ContentDaoMapper)this.getMapper(session)).setContentModifiedDate(contentId) <= 0) {
            session.rollback();
            return -1L;
         } else {
            session.commit();
            if (bTrigger) {
               ScheduleInfo sInfo = ScheduleInfoImpl.getInstance();
               sInfo.setContentTrigger(contentId);
               EventInfo eInfo = EventInfoImpl.getInstance();
               eInfo.setContentTrigger(contentId);
               List pList = this.getPlaylistListUsingContent(contentId);

               for(int i = 0; i < pList.size(); ++i) {
                  Map map = (Map)pList.get(i);
                  String playlistId = (String)map.get("playlist_id");
                  sInfo.setPlaylistTrigger(playlistId);
               }
            }

            return versionId;
         }
      }
   }

   public int setAdsContentActiveVersion(String contentId, Long versionId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      try {
         byte var4;
         if (((ContentDaoMapper)this.getMapper(session)).setAdsContentActiveVersion(contentId, versionId) <= 0) {
            session.rollback();
            var4 = -1;
            return var4;
         }

         ((ContentDaoMapper)this.getMapper(session)).setOtherAdsContentVersionInactive(contentId, versionId);
         if (((ContentDaoMapper)this.getMapper(session)).setContentModifiedDate(contentId) <= 0) {
            session.rollback();
            var4 = -1;
            return var4;
         }

         session.commit();
      } catch (Exception var9) {
         this.logger.error(var9);
         session.rollback();
         byte var5 = -1;
         return var5;
      } finally {
         session.close();
      }

      return 1;
   }

   public int setActiveVersion(String contentId, Long versionId, boolean bTrigger) throws SQLException, Exception {
      SqlSession session = this.openNewSession(false);

      byte var5;
      try {
         if (((ContentDaoMapper)this.getMapper(session)).setVersionContentActive(contentId, versionId) <= 0) {
            session.rollback();
            var5 = -1;
            return var5;
         }

         ((ContentDaoMapper)this.getMapper(session)).setOtherVersionInactive(contentId, versionId);
         if (((ContentDaoMapper)this.getMapper(session)).setContentModifiedDate(contentId) > 0) {
            session.commit();
            if (bTrigger) {
               ScheduleInfo sInfo = ScheduleInfoImpl.getInstance();
               sInfo.setContentTrigger(contentId);
               EventInfo eInfo = EventInfoImpl.getInstance();
               eInfo.setContentTrigger(contentId);
               List pList = this.getPlaylistListUsingContent(contentId);

               for(int i = 0; i < pList.size(); ++i) {
                  Map map = (Map)pList.get(i);
                  String playlistId = (String)map.get("playlist_id");
                  sInfo.setPlaylistTrigger(playlistId);
               }

               TagInfo tagInfo = TagInfoImpl.getInstance();
               List tagList = tagInfo.getContentTagList(contentId);
               if (tagList != null && tagList.size() > 0) {
                  List triggerTagList = new ArrayList();
                  Iterator var11 = tagList.iterator();

                  while(true) {
                     if (!var11.hasNext()) {
                        if (triggerTagList.size() > 0) {
                           tagInfo.setPlaylistTrigger(triggerTagList);
                        }
                        break;
                     }

                     TagEntity tag = (TagEntity)var11.next();
                     if (!triggerTagList.contains((long)tag.getTag_id())) {
                        triggerTagList.add((long)tag.getTag_id());
                     }
                  }
               }
            }

            byte var19 = 1;
            return var19;
         }

         session.rollback();
         var5 = -1;
      } catch (SQLException var16) {
         this.logger.error(var16);
         session.rollback();
         byte var6 = -1;
         return var6;
      } finally {
         session.close();
      }

      return var5;
   }

   public List getPlaylistListUsingContent(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getPlaylistListUsingContent(contentId);
   }

   public List getActivePlaylistListUsingContent(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getActivePlaylistListUsingContent(contentId);
   }

   public List getPlaylistVersionListUsingContent(String playlistId, String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getPlaylistVersionListUsingContent(playlistId, contentId);
   }

   public List getLitePlaylistListUsingContent(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getLitePlaylistListUsingContent(contentId);
   }

   public int setContentActive(String contentID, Long versionID) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).setContentActive(contentID, versionID);
   }

   private int setTempVersionContentActive(String contentId, Long versionId, SqlSession session) throws SQLException {
      Content content = ((ContentDaoMapper)this.getMapper(session)).getContentVerInfo(contentId, 0L);
      int cnt = 0;
      if (content != null) {
         content.setIs_active("Y");
         content.setVersion_id(versionId);
         cnt = this.addContentVersionInfo(content, session);
         if (cnt <= 0) {
            return -1;
         }
      }

      return cnt;
   }

   public Long getContentNextVer(String contentId) throws SQLException {
      SqlSession session = null;

      Long var3;
      try {
         session = this.openNewSession(true);
         var3 = this.getContentNextVer(contentId, session);
      } catch (SQLException var7) {
         throw var7;
      } finally {
         if (session != null) {
            session.close();
         }

      }

      return var3;
   }

   private Long getContentNextVer(String contentId, SqlSession session) throws SQLException {
      Long returnValue = ((ContentDaoMapper)this.getMapper(session)).getContentNextVer(contentId);
      return returnValue != null && returnValue > 0L ? returnValue + 1L : 1L;
   }

   private Long getContentMaxVer(String contentId) throws SQLException {
      Long returnValue = ((ContentDaoMapper)this.getMapper()).getContentMaxVer(contentId);
      return returnValue != null && returnValue.intValue() > 0 ? returnValue : 0L;
   }

   public Long getContentActiveVer(String contentId) throws SQLException {
      Long returnValue = ((ContentDaoMapper)this.getMapper()).getContentActiveVer(contentId);
      return returnValue != null && returnValue.intValue() > 0 ? returnValue : 0L;
   }

   public int addAdsContentVersionInfo(Map adsSetting) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var4;
      try {
         int cnt = this.addAdsContentVersionInfo(adsSetting, session);
         session.commit();
         int var10 = cnt;
         return var10;
      } catch (SQLException var8) {
         this.logger.error(var8);
         session.rollback();
         var4 = -1;
      } finally {
         session.close();
      }

      return var4;
   }

   private int addAdsContentVersionInfo(Map adsSetting, SqlSession session) throws SQLException {
      if (((ContentDaoMapper)this.getMapper(session)).addAdsContentVersionInfo(adsSetting) <= 0) {
         session.rollback();
         session.close();
         return -1;
      } else {
         return 1;
      }
   }

   public int addContentVersionInfo(Content content) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var4;
      try {
         int cnt = this.addContentVersionInfo(content, session);
         session.commit();
         int var10 = cnt;
         return var10;
      } catch (SQLException var8) {
         this.logger.error(var8);
         session.rollback();
         var4 = -1;
      } finally {
         session.close();
      }

      return var4;
   }

   private int addContentVersionInfo(Content content, SqlSession session) throws SQLException {
      if (((ContentDaoMapper)this.getMapper(session)).addContentVersionInfo(content) <= 0) {
         session.rollback();
         session.close();
         return -1;
      } else {
         String media_type = content.getMedia_type();
         if (media_type.equals("LFD") || media_type.equals("LFT") || media_type.equals("VWL") || media_type.equals("DLK") || media_type.equals("TLFD")) {
            if (this.setDeviceTypeVersionByMediaType(content.getContent_id(), content.getDevice_type_version(), session) <= 0) {
               session.rollback();
               session.close();
               return -1;
            }

            if ("LFT".equals(media_type)) {
               ((ContentDaoMapper)this.getMapper(session)).updateAsTemplateByContentIdAndVersionId(content.getContent_id(), "Y", content.getVersion_id());
            }
         }

         if (((ContentDaoMapper)this.getMapper(session)).setContentModifiedDate(content.getContent_id()) <= 0) {
            session.rollback();
            session.close();
            return -1;
         } else {
            return 1;
         }
      }
   }

   private int setDeviceTypeVersionByMediaType(String content_id, float device_type_version, SqlSession session) {
      int cnt = 0;

      try {
         cnt = ((ContentDaoMapper)this.getMapper(session)).setDeviceTypeVersionByMediaType(device_type_version, content_id);
      } catch (SQLException var6) {
         this.logger.error("", var6);
      }

      return cnt <= 0 ? -1 : cnt;
   }

   public int addContentInfo(Content content) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).addContentInfo(content);
   }

   public int addMapContentFile(String contentId, Long versionId, String fileId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).addMapContentFile(contentId, versionId, fileId);
   }

   public int addMapGroupContent(String contentId, Long groupId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).addMapGroupContent(contentId, groupId);
   }

   public int setContentInfo(String contentId, String contentName, String contentMetaData, int shareFlag, int pollingInterval) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var8;
      try {
         int cnt = this.setContentInfo(contentId, contentName, contentMetaData, shareFlag, pollingInterval, session);
         session.commit();
         int var14 = cnt;
         return var14;
      } catch (SQLException var12) {
         this.logger.error(var12);
         session.rollback();
         var8 = -1;
      } finally {
         session.close();
      }

      return var8;
   }

   private int setContentInfo(String contentId, String contentName, String contentMetaData, int shareFlag, int pollingInterval, SqlSession session) throws SQLException {
      if (((ContentDaoMapper)this.getMapper(session)).setContentInfo(contentId, contentName, contentMetaData, shareFlag, pollingInterval) <= 0) {
         session.rollback();
         return -1;
      } else if (((ContentDaoMapper)this.getMapper(session)).setContentModifiedDate(contentId) <= 0) {
         session.rollback();
         return -1;
      } else {
         return 1;
      }
   }

   public int deleteContent(String contentId, String userId, String sessionId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var6;
      try {
         byte var5;
         if (((ContentDaoMapper)this.getMapper(session)).setIsdeleteContent(contentId) <= 0) {
            session.rollback();
            var5 = -1;
            return var5;
         }

         if (((ContentDaoMapper)this.getMapper(session)).setRootGroupToMapContentGroup(contentId, this.getRootId(userId)) > 0) {
            if (((ContentDaoMapper)this.getMapper(session)).setContentModifiedDate(contentId) <= 0) {
               session.rollback();
               var5 = -1;
               return var5;
            }

            ((ContentDaoMapper)this.getMapper(session)).setContentUnlockBySessionID(sessionId);
            session.commit();
            byte var12 = 1;
            return var12;
         }

         session.rollback();
         var5 = -1;
         return var5;
      } catch (SQLException var10) {
         this.logger.error(var10);
         session.rollback();
         var6 = -1;
      } finally {
         session.close();
      }

      return var6;
   }

   public int deleteMaxVersionContent(String contentId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var4;
      try {
         Long versionId = this.getContentMaxVer(contentId);
         if (versionId <= 0L) {
            session.rollback();
            var4 = -1;
            return var4;
         }

         if (((ContentDaoMapper)this.getMapper(session)).deleteContentVersion(contentId, versionId) <= 0) {
            session.rollback();
            var4 = -1;
            return var4;
         }

         session.commit();
         byte var10 = 1;
         return var10;
      } catch (SQLException var8) {
         this.logger.error(var8);
         session.rollback();
         var4 = -1;
      } finally {
         session.close();
      }

      return var4;
   }

   private int deleteVersionContent(String contentId, Long versionId, SqlSession session) throws SQLException {
      try {
         if (((ContentDaoMapper)this.getMapper(session)).deleteContentVersion(contentId, versionId) == 0) {
            return -1;
         } else {
            return ((ContentDaoMapper)this.getMapper(session)).deleteMapContentVersionFile(contentId, versionId) == 0 ? -1 : 1;
         }
      } catch (SQLException var5) {
         this.logger.error(var5);
         session.rollback();
         return -1;
      }
   }

   public int deleteVersionContent(String contentId, Long versionId) throws SQLException {
      SqlSession session = this.openNewSession(true);

      int var4;
      try {
         var4 = this.deleteVersionContent(contentId, versionId, session);
      } catch (SQLException var8) {
         throw var8;
      } finally {
         session.close();
      }

      return var4;
   }

   public int restoreContent(String contentId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var3;
      try {
         if (((ContentDaoMapper)this.getMapper(session)).restoreContent(contentId) > 0) {
            if (((ContentDaoMapper)this.getMapper(session)).setContentModifiedDate(contentId) <= 0) {
               session.rollback();
               var3 = -1;
               return var3;
            }

            session.commit();
            byte var10 = 1;
            return var10;
         }

         session.rollback();
         var3 = -1;
      } catch (SQLException var8) {
         this.logger.error(var8);
         session.rollback();
         byte var4 = -1;
         return var4;
      } finally {
         session.close();
      }

      return var3;
   }

   public int deleteContentCompletely(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).deleteContentCompletely(contentId);
   }

   public int setContentLock(String contentId, String sessionId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).setContentLock(contentId, sessionId);
   }

   public int setContentUnlockBySessionID(String sessionId) throws SQLException {
      int result = -1;
      int retry = 5;

      while(retry > 0) {
         try {
            result = ((ContentDaoMapper)this.getMapper()).setContentUnlockBySessionID(sessionId);
            retry = 0;
         } catch (SQLException var7) {
            if (retry == 1) {
               throw var7;
            }

            try {
               --retry;
               Thread.sleep(1L);
            } catch (InterruptedException var6) {
               this.logger.error(var6.getMessage(), var6);
            }
         }
      }

      return result;
   }

   public int deleteAllContentLockData() throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var3;
      try {
         ((ContentDaoMapper)this.getMapper(session)).deleteAllContentLockData();
         int cnt = ((ContentDaoMapper)this.getMapper(session)).deleteAllPlaylistLockData();
         session.commit();
         int var9 = cnt;
         return var9;
      } catch (SQLException var7) {
         session.rollback();
         this.logger.error(var7);
         var3 = -1;
      } finally {
         session.close();
      }

      return var3;
   }

   public int setContentModifiedDate(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).setContentModifiedDate(contentId);
   }

   public int setContentGroup(String contentId, Long groupId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var5;
      try {
         if (((ContentDaoMapper)this.getMapper(session)).setContentGroup(contentId, groupId) <= 0) {
            session.rollback();
            byte var11 = -1;
            return var11;
         }

         int cnt = ((ContentDaoMapper)this.getMapper(session)).setContentModifiedDate(contentId);
         if (cnt > 0) {
            session.commit();
            int var12 = cnt;
            return var12;
         }

         session.rollback();
         var5 = -1;
         return var5;
      } catch (SQLException var9) {
         session.rollback();
         this.logger.error(var9);
         var5 = -1;
      } finally {
         session.close();
      }

      return var5;
   }

   public int setContentShare(String contentId, Long shareFlag) throws SQLException {
      SqlSession session = this.openNewSession(false);

      int var12;
      try {
         byte var5;
         try {
            if (((ContentDaoMapper)this.getMapper(session)).setContentShare(contentId, shareFlag) <= 0) {
               session.rollback();
               byte var11 = -1;
               return var11;
            }

            int cnt = ((ContentDaoMapper)this.getMapper(session)).setContentModifiedDate(contentId);
            if (cnt <= 0) {
               session.rollback();
               var5 = -1;
               return var5;
            }

            session.commit();
            var12 = cnt;
         } catch (SQLException var9) {
            session.rollback();
            this.logger.error(var9);
            var5 = -1;
            return var5;
         }
      } finally {
         session.close();
      }

      return var12;
   }

   public int setContentMetaData(String contentId, String metaData) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var4;
      try {
         byte var5;
         try {
            if (((ContentDaoMapper)this.getMapper(session)).setContentMetaData(contentId, metaData) > 0) {
               int cnt = ((ContentDaoMapper)this.getMapper(session)).setContentModifiedDate(contentId);
               if (cnt <= 0) {
                  session.rollback();
                  var5 = -1;
                  return var5;
               }

               session.commit();
               int var12 = cnt;
               return var12;
            }

            session.rollback();
            var4 = -1;
         } catch (SQLException var9) {
            session.rollback();
            this.logger.error(var9);
            var5 = -1;
            return var5;
         }
      } finally {
         session.close();
      }

      return var4;
   }

   public int addFile(ContentFile file) throws SQLException {
      try {
         if (this.isExistFile(file)) {
            return 1;
         }
      } catch (Exception var3) {
         this.logger.error("", var3);
      }

      return ((ContentDaoMapper)this.getMapper()).addFile(file);
   }

   public int deleteFile(String fileId) throws SQLException {
      this.logger.error("[HENNRY deleteFile][DELETE mi_cms_info_file.file_id = ]" + fileId);
      return ((ContentDaoMapper)this.getMapper()).deleteFile(fileId);
   }

   public int deleteFileInfoIfNoExistFile(String fileId) throws SQLException {
      this.logger.error("[HENNRY deleteFileInfoIfNoExistFile][DELETE mi_cms_info_file.file_id = ]" + fileId);
      return 0;
   }

   public int setFileHashCode(String fileId, String hashCode) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).setFileHashCode(fileId, hashCode);
   }

   public String getHashCodeFromContentByFileName(String fileName, String fileType) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getHashCodeFromContentByFileName(fileName, fileType);
      } catch (Exception var4) {
         this.logger.error(var4);
         return "";
      }
   }

   public String getHashCodeFromContentByFileNameAndSize(String fileName, String fileType, long fileSize) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getHashCodeFromContentByFileNameAndSize(fileName, fileType, fileSize);
      } catch (Exception var6) {
         this.logger.error(var6);
         return "";
      }
   }

   public int setFilePath(String fileId, String filePath) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).setFilePath(fileId, filePath);
   }

   public Long getRootId(String userId) throws SQLException {
      UserInfo uInfo = UserInfoImpl.getInstance();
      long organizationId = uInfo.getRootGroupIdByUserId(userId);
      return ((ContentDaoMapper)this.getMapper()).getRootId(userId, new Long(organizationId), ContentConstants.PARENT_GROUP_OF_UNGROUPED);
   }

   public Long getTLFDRootId(String userId) throws SQLException {
      UserInfo uInfo = UserInfoImpl.getInstance();
      long organizationId = uInfo.getRootGroupIdByUserId(userId);
      return ((ContentDaoMapper)this.getMapper()).getTLFDRootId(userId, new Long(organizationId), ContentConstants.PARENT_GROUP_OF_UNGROUPED);
   }

   public Boolean isExistGroupName(String groupName, String userId) throws SQLException {
      try {
         UserInfo uInfo = UserInfoImpl.getInstance();
         long organizationId = uInfo.getRootGroupIdByUserId(userId);
         return ((ContentDaoMapper)this.getMapper()).numberOfExistingGroupName(groupName, userId, organizationId) > 0;
      } catch (Exception var6) {
         this.logger.error(var6);
         return false;
      }
   }

   public Boolean isExistGroupName(String groupName, String userId, long organizationId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).numberOfExistingGroupName(groupName, userId, organizationId) > 0;
      } catch (Exception var6) {
         this.logger.error(var6);
         return false;
      }
   }

   public long getGroupId(String groupName, String userId, long organizationId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getGroupId(groupName, userId, organizationId);
      } catch (Exception var6) {
         this.logger.error(var6);
         return -1L;
      }
   }

   public Long getGroupId(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getGroupIdByContentId(contentId);
   }

   public String getGroupName(Long groupId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getGroupName(groupId);
   }

   public Long getRoot_GroupId(String groupID) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getRoot_GroupId(Long.parseLong(groupID));
   }

   public Group getGroupInfo(Long groupId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getGroupInfo(groupId);
   }

   public List getGroupList(String creatorId) throws SQLException {
      try {
         long organizationId = 0L;
         UserInfo uInfo = UserInfoImpl.getInstance();
         User user = uInfo.getUserByUserId(creatorId);
         if (user.isMu()) {
            organizationId = uInfo.getCurMngOrgId(creatorId);
         } else {
            organizationId = uInfo.getRootGroupIdByUserId(creatorId);
         }

         return ((ContentDaoMapper)this.getMapper()).getGroupList(creatorId, organizationId);
      } catch (Exception var6) {
         this.logger.error(var6);
         return null;
      }
   }

   public List getGroupList(String creatorId, long organization_id) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getGroupList(creatorId, organization_id);
      } catch (Exception var5) {
         this.logger.error(var5);
         return null;
      }
   }

   public List getOrganizationList() throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getOrganizationList();
      } catch (Exception var2) {
         this.logger.error(var2);
         return null;
      }
   }

   public Long addGroup(Group group) throws SQLException {
      long groupId = (long)SequenceDB.getNextValue("MI_CMS_INFO_CONTENT_GROUP");
      if (groupId <= 0L) {
         return -1L;
      } else {
         UserInfo uInfo = UserInfoImpl.getInstance();
         Group newGroup = new Group();
         newGroup.setGroup_id(groupId);
         newGroup.setP_group_id(group.getP_group_id());
         newGroup.setGroup_depth(group.getGroup_depth());
         newGroup.setGroup_name(group.getGroup_name());
         newGroup.setCreator_id(group.getCreator_id());
         newGroup.setOrganization_id(uInfo.getRootGroupIdByUserId(group.getCreator_id()));
         return ((ContentDaoMapper)this.getMapper()).addGroup(newGroup) < 1L ? -1L : groupId;
      }
   }

   public Long addGroup(String table, Group group) throws SQLException {
      long groupId = (long)SequenceDB.getNextValue("MI_CMS_INFO_CONTENT_GROUP");
      if (groupId <= 0L) {
         return -1L;
      } else {
         UserInfo uInfo = UserInfoImpl.getInstance();
         Group newGroup = new Group();
         newGroup.setGroup_id(groupId);
         newGroup.setP_group_id(group.getP_group_id());
         newGroup.setGroup_depth(group.getGroup_depth());
         newGroup.setGroup_name(group.getGroup_name());
         newGroup.setCreator_id(group.getCreator_id());
         newGroup.setOrganization_id(uInfo.getRootGroupIdByUserId(group.getCreator_id()));
         return ((ContentDaoMapper)this.getMapper()).addGroupTable(table, newGroup) < 1L ? -1L : groupId;
      }
   }

   public Long addGroup(Group group, long organizationId) throws SQLException {
      long groupId = (long)SequenceDB.getNextValue("MI_CMS_INFO_CONTENT_GROUP");
      if (groupId <= 0L) {
         return -1L;
      } else {
         Group newGroup = new Group();
         newGroup.setGroup_id(groupId);
         newGroup.setP_group_id(group.getP_group_id());
         newGroup.setGroup_depth(group.getGroup_depth());
         newGroup.setGroup_name(group.getGroup_name());
         newGroup.setCreator_id(group.getCreator_id());
         newGroup.setOrganization_id(organizationId);
         return ((ContentDaoMapper)this.getMapper()).addGroup(newGroup) < 1L ? -1L : groupId;
      }
   }

   public Long addDefaultGroup(String userId) throws SQLException {
      long groupId = (long)SequenceDB.getNextValue("MI_CMS_INFO_CONTENT_GROUP");
      if (groupId <= 0L) {
         return -1L;
      } else {
         Long orgGroupId = this.getRootId(userId);
         if (orgGroupId == null) {
            UserInfo uInfo = UserInfoImpl.getInstance();
            long organizationId = uInfo.getRootGroupIdByUserId(userId);
            Group newGroup = new Group();
            newGroup.setGroup_id(groupId);
            newGroup.setP_group_id(ContentConstants.PARENT_GROUP_OF_UNGROUPED);
            newGroup.setGroup_depth(ContentConstants.GROUP_DEPTH_OF_UNGROUPED);
            newGroup.setGroup_name("default");
            newGroup.setCreator_id(userId);
            newGroup.setOrganization_id(organizationId);
            return ((ContentDaoMapper)this.getMapper()).addGroup(newGroup) < 1L ? -1L : groupId;
         } else {
            return orgGroupId;
         }
      }
   }

   public Long addDefaultGroup(String userId, long organizationId) throws SQLException {
      long groupId = (long)SequenceDB.getNextValue("MI_CMS_INFO_CONTENT_GROUP");
      if (((ContentDaoMapper)this.getMapper()).isExistDefaultGroup(userId, organizationId) <= 0 && groupId > 0L) {
         Group newGroup = new Group();
         newGroup.setGroup_id(groupId);
         newGroup.setP_group_id(ContentConstants.PARENT_GROUP_OF_UNGROUPED);
         newGroup.setGroup_depth(ContentConstants.GROUP_DEPTH_OF_UNGROUPED);
         newGroup.setGroup_name("default");
         newGroup.setCreator_id(userId);
         newGroup.setOrganization_id(organizationId);
         return ((ContentDaoMapper)this.getMapper()).addGroup(newGroup) < 1L ? -1L : groupId;
      } else {
         return -1L;
      }
   }

   public int setGroupInfo(Group group) throws SQLException {
      if (group.getGroup_id() <= 0L) {
         this.logger.fatal("Block to set device group of root_group_id");
         return 0;
      } else {
         return ((ContentDaoMapper)this.getMapper()).setGroupInfo(group);
      }
   }

   public int setGroupInfo(String table, Group group) throws SQLException {
      if (group.getGroup_id() <= 0L) {
         this.logger.fatal("Block to set device group of root_group_id");
         return 0;
      } else {
         return ((ContentDaoMapper)this.getMapper()).setGroupInfoTable(table, group);
      }
   }

   public Boolean isDeletableGroup(Long groupId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getCountDeletableGroup(groupId) == 0L;
      } catch (Exception var3) {
         this.logger.error(var3);
         return false;
      }
   }

   public int deleteGroup(Long groupId) throws SQLException {
      if (groupId <= 0L) {
         this.logger.fatal("Block to remove device group of root_group_id");
         return 0;
      } else {
         try {
            int cnt = ((ContentDaoMapper)this.getMapper()).deleteGroup(groupId);
            return cnt > 0 ? cnt : -1;
         } catch (Exception var3) {
            this.logger.error(var3);
            return -1;
         }
      }
   }

   public int deleteGroup(String table, Long groupId) throws SQLException {
      if (groupId <= 0L) {
         this.logger.fatal("Block to remove device group of root_group_id");
         return 0;
      } else {
         try {
            int cnt = ((ContentDaoMapper)this.getMapper()).deleteGroupTable(table, groupId);
            return cnt > 0 ? cnt : -1;
         } catch (Exception var4) {
            this.logger.error(var4);
            return -1;
         }
      }
   }

   public List getGroupedContentIdList(Long groupId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getGroupedContentIdList(groupId);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public List getChildGroupList(Long groupId, boolean recursive, String creatorId) throws SQLException {
      List groupList = new ArrayList();
      List resList = null;
      UserInfo uInfo = UserInfoImpl.getInstance();
      long organizationId = uInfo.getRootGroupIdByUserId(creatorId);
      resList = ((ContentDaoMapper)this.getMapper()).getChildGroupList(groupId, organizationId, creatorId);
      groupList.addAll(resList);
      if (recursive) {
         Iterator var9 = resList.iterator();

         while(var9.hasNext()) {
            Group group = (Group)var9.next();
            groupList.addAll(this.getChildGroupList(group.getGroup_id(), recursive, creatorId));
         }
      }

      return groupList;
   }

   public List getChildGroupList(Long groupId, boolean recursive, String creatorId, long organizationId) throws SQLException {
      List groupList = new ArrayList();
      List resList = null;
      resList = ((ContentDaoMapper)this.getMapper()).getChildGroupList(groupId, organizationId, creatorId);
      groupList.addAll(resList);
      if (recursive) {
         Iterator var8 = resList.iterator();

         while(var8.hasNext()) {
            Group group = (Group)var8.next();
            groupList.addAll(this.getChildGroupList(group.getGroup_id(), recursive, creatorId, organizationId));
         }
      }

      return groupList;
   }

   public List getChildGroupIdList(int groupId, boolean recursive) throws SQLException {
      List rtList = new ArrayList();
      List groupIdList = null;
      groupIdList = ((ContentDaoMapper)this.getMapper()).getChildGroupIdList(new Long((long)groupId));
      if (groupIdList != null) {
         for(int i = 0; i < groupIdList.size(); ++i) {
            if (recursive) {
               Long group = (Long)((Map)groupIdList.get(i)).get("group_id");
               rtList.add(group);
               List temp = this.getChildGroupIdList(group.intValue(), recursive);
               if (temp != null && temp.size() != 0) {
                  rtList.addAll(temp);
               }
            } else {
               rtList.add((Long)((Map)groupIdList.get(i)).get("group_id"));
            }
         }
      }

      return rtList;
   }

   public List getCmsGroupItems(Long pGroupId, String creatorId) throws Exception {
      try {
         UserInfo uInfo = UserInfoImpl.getInstance();
         return ((ContentDaoMapper)this.getMapper()).getCmsGroupItems(pGroupId, creatorId, ContentConstants.GROUP_DEPTH_OF_UNGROUPED, uInfo.getRootGroupIdByUserId(creatorId));
      } catch (SQLException var4) {
         this.logger.error(var4);
         return null;
      }
   }

   public List getCmsSelfGroupItems(Long groupId, String creatorId) throws Exception {
      try {
         UserInfo uInfo = UserInfoImpl.getInstance();
         return ((ContentDaoMapper)this.getMapper()).getCmsSelfGroupItems(groupId, creatorId, ContentConstants.GROUP_DEPTH_OF_UNGROUPED, uInfo.getRootGroupIdByUserId(creatorId));
      } catch (SQLException var4) {
         this.logger.error(var4);
         return null;
      }
   }

   public List getAllContentByGroupIdForAuth(Long groupId, String creatorId, String deviceType) throws SQLException {
      try {
         Map map = new HashMap();
         map.put("ConstTYPE_PREMIUM", "iPLAYER");
         map.put("ConstTYPE_SOC", "SPLAYER");
         map.put("ConstTYPE_APLAYER", "APLAYER");
         map.put("ConstTYPE_WPLAYER", "WPLAYER");
         map.put("ConstMEDIA_TYPE_FOR_AUTHOR", ContentConstants.getMediaTypeForAuthor());
         map.put("groupId", groupId);
         map.put("creatorId", creatorId);
         map.put("deviceType", deviceType);
         UserInfo uInfo = UserInfoImpl.getInstance();
         map.put("organizationId", uInfo.getRootGroupIdByUserId(creatorId));
         return ((ContentDaoMapper)this.getMapper()).getAllContentByGroupIdForAuth(map);
      } catch (SQLException var6) {
         this.logger.error(var6);
         return null;
      }
   }

   public Object getMaxDepth(String table) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getMaxDepth(table);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public List getDefaultContentGroup(String table, String creatorId, String sortType, String skipId) {
      try {
         if (StringUtils.isEmpty(sortType)) {
            sortType = "ASC";
         }

         UserInfo uInfo = UserInfoImpl.getInstance();
         return ((ContentDaoMapper)this.getMapper()).getDefaultContentGroup(table, creatorId, uInfo.getRootGroupIdByUserId(creatorId), sortType, skipId);
      } catch (Exception var6) {
         this.logger.error(var6);
         return null;
      }
   }

   public List getSpecificDepthUserGroupList(String table, int depth, String creatorId, String sortType, String skipId) throws SQLException {
      try {
         if (StringUtils.isEmpty(sortType)) {
            sortType = "ASC";
         }

         UserInfo uInfo = UserInfoImpl.getInstance();
         return ((ContentDaoMapper)this.getMapper()).getSpecificDepthUserGroupList(table, depth, creatorId, uInfo.getRootGroupIdByUserId(creatorId), sortType, skipId);
      } catch (Exception var7) {
         this.logger.error(var7);
         return null;
      }
   }

   public int updateVwlVersion(String contentId, int vwlVersion) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).updateVwlVersion(contentId, vwlVersion);
      } catch (SQLException var4) {
         this.logger.error(var4);
         return 0;
      }
   }

   public List getAllContentListByUser(String creatorId, boolean canReadUnsharedContent, Long currentUserOrganId) throws SQLException {
      try {
         Map map = new HashMap();
         map.put("ConstROOT_GROUP_ID", 0);
         map.put("ConstSHARE_FLAG_DEFAULT", ContentConstants.SHARE_FLAG_DEFAULT);
         map.put("ConstMEDIA_TYPE_FOR_AUTHOR", ContentConstants.getMediaTypeForAuthor());
         map.put("currentUserOrganId", currentUserOrganId);
         map.put("canReadUnsharedContent", canReadUnsharedContent);
         map.put("creatorId", creatorId);
         return ((ContentDaoMapper)this.getMapper()).getAllContentListByUser(map);
      } catch (Exception var5) {
         this.logger.error(var5);
         return null;
      }
   }

   public List getAllContentListByType(boolean byAll, String mediaType, boolean canReadUnsharedContent, String currentUserId, Long currentUserOrganId) throws SQLException {
      try {
         Map map = new HashMap();
         map.put("ConstROOT_GROUP_ID", 0);
         map.put("ConstSHARE_FLAG_DEFAULT", ContentConstants.SHARE_FLAG_DEFAULT);
         map.put("currentUserOrganId", currentUserOrganId);
         map.put("canReadUnsharedContent", canReadUnsharedContent);
         map.put("currentUserId", currentUserId);
         map.put("mediaType", mediaType);
         map.put("byAll", byAll);
         return ((ContentDaoMapper)this.getMapper()).getAllContentListByType(map);
      } catch (Exception var7) {
         this.logger.error(var7);
         return null;
      }
   }

   public boolean updateDatalinkLFDToContentInfo(String contentId, long versionId, long pageCount) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).updateDatalinkLFDToPageCount(contentId, versionId, pageCount) > 0;
      } catch (SQLException var7) {
         this.logger.error(var7);
         return false;
      }
   }

   public List getFtpContentSettingList() throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getFtpContentSettingList();
      } catch (Exception var2) {
         this.logger.error(var2);
         return null;
      }
   }

   public List getFtpContentSettingByContentId(String contentId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getFtpContentSettingByContentId(contentId);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public ContentFile getContentFileInfoByFileId(String fileId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getContentFileInfoByFileId(fileId);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public List getFileListByContentId(String contentId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getFileListByContentId(contentId);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public List getFileListByContentIdAndVersion(String contentId, long versionId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getFileListByContentIdAndVersion(contentId, versionId);
      } catch (Exception var5) {
         this.logger.error(var5);
         return null;
      }
   }

   public Content getContentInfoByContentName(String contentName) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getContentInfoByContentName(contentName);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public long getVersionInfoByContentId(String contentId) throws SQLException {
      try {
         List verList = ((ContentDaoMapper)this.getMapper()).getVersionInfoByContentId(contentId);
         return verList != null && verList.size() > 0 ? (Long)verList.get(0) : 0L;
      } catch (Exception var3) {
         this.logger.error(var3);
         return 0L;
      }
   }

   public void updateContentVersionInfoWithFileId(String contentId, String mainFileId, long version) throws SQLException {
      try {
         ((ContentDaoMapper)this.getMapper()).updateContentVersionInfoWithFileId(contentId, mainFileId, version);
      } catch (SQLException var6) {
         this.logger.error(var6);
      }

   }

   public void updateContentVersionInfoWithFileId(String contentId, long version) throws SQLException {
      try {
         ((ContentDaoMapper)this.getMapper()).updateContentVersionInfoWithFileIdContentIdVersion(contentId, version);
      } catch (SQLException var5) {
         this.logger.error("", var5);
      }

   }

   public List getCifsContentSettingList() throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getCifsContentSettingList();
      } catch (Exception var2) {
         this.logger.error(var2);
         return null;
      }
   }

   public List getCifsContentSettingByContentId(String contentId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getCifsContentSettingByContentId(contentId);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public List getUrlContentSettingByContentId(String contentId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getUrlContentSettingByContentId(contentId);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public List getAdsContentSettingByContentId(String contentId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getAdsContentSettingByContentId(contentId);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public void updateAdsSettingAsDeleted(String contentId, String isDeleted) throws SQLException {
      try {
         ((ContentDaoMapper)this.getMapper()).updateAdsSettingAsDeleted(contentId, isDeleted);
      } catch (SQLException var4) {
         this.logger.error(var4);
      }

   }

   public void updateAdsSettingContentName(String contentId, String contentName) throws SQLException {
      try {
         ((ContentDaoMapper)this.getMapper()).updateAdsSettingContentName(contentId, contentName);
      } catch (SQLException var4) {
         this.logger.error(var4);
      }

   }

   public int addFtpSetting(String contentId, String ftpContentName, int port, String ftpIP, String ftpLoginId, String ftpPassword, String ftpDirectory, long ftpRefreshInterval, String isSsl, String canRefresh, long loginRetryMaxCount, long loginRetryCount, String canLoginRetry) throws SQLException {
      Map map = new HashMap();
      map.put("contentId", contentId);
      map.put("ftpContentName", ftpContentName);
      map.put("ftpIP", ftpIP);
      map.put("port", port);
      map.put("ftpLoginId", ftpLoginId);
      map.put("ftpPassword", ftpPassword);
      map.put("ftpDirectory", ftpDirectory);
      map.put("ftpRefreshInterval", ftpRefreshInterval);
      map.put("isSsl", isSsl);
      map.put("canRefresh", canRefresh);
      map.put("loginRetryMaxCount", loginRetryMaxCount);
      map.put("loginRetryCount", loginRetryCount);
      map.put("canLoginRetry", canLoginRetry);
      return ((ContentDaoMapper)this.getMapper()).addFtpSetting(map);
   }

   public int addCifsSetting(String contentId, String cifsContentName, String cifsIP, String cifsLoginId, String cifsPassword, String cifsDirectory, long cifsRefreshInterval, String canRefresh, long loginRetryMaxCount, long loginRetryCount, String canLoginRetry) throws SQLException {
      Map map = new HashMap();
      map.put("contentId", contentId);
      map.put("cifsContentName", cifsContentName);
      map.put("cifsIP", cifsIP);
      map.put("cifsLoginId", cifsLoginId);
      map.put("cifsPassword", cifsPassword);
      map.put("cifsDirectory", cifsDirectory);
      map.put("cifsRefreshInterval", cifsRefreshInterval);
      map.put("canRefresh", canRefresh);
      map.put("loginRetryMaxCount", loginRetryMaxCount);
      map.put("loginRetryCount", loginRetryCount);
      map.put("canLoginRetry", canLoginRetry);
      return ((ContentDaoMapper)this.getMapper()).addCifsSetting(map);
   }

   public int addUrlSetting(String contentId, String urlContentName, String urlAddress) throws SQLException {
      Map map = new HashMap();
      map.put("contentId", contentId);
      map.put("urlContentName", urlContentName);
      map.put("urlAddress", urlAddress);
      return ((ContentDaoMapper)this.getMapper()).addUrlSetting(map);
   }

   public int updateUrlSetting(String contentId, String urlContentName, String urlAddress) throws SQLException {
      Map map = new HashMap();
      map.put("contentId", contentId);
      map.put("urlContentName", urlContentName);
      map.put("urlAddress", urlAddress);
      return ((ContentDaoMapper)this.getMapper()).updateUrlSetting(map);
   }

   public void updateFtpSettingAsDeleted(String contentId, String isDeleted) throws SQLException {
      try {
         ((ContentDaoMapper)this.getMapper()).updateFtpSettingAsDeleted(contentId, isDeleted);
      } catch (SQLException var4) {
         this.logger.error(var4);
      }

   }

   public void updateCifsSettingAsDeleted(String contentId, String isDeleted) throws SQLException {
      try {
         ((ContentDaoMapper)this.getMapper()).updateCifsSettingAsDeleted(contentId, isDeleted);
      } catch (SQLException var4) {
         this.logger.error(var4);
      }

   }

   public void updateUrlSettingAsDeleted(String contentId, String isDeleted) throws SQLException {
      try {
         ((ContentDaoMapper)this.getMapper()).updateUrlSettingAsDeleted(contentId, isDeleted);
      } catch (SQLException var4) {
         this.logger.error(var4);
      }

   }

   public String getCodeFile(String fileType) throws SQLException {
      Map map = new HashMap();
      map.put("fileType", fileType);
      map.put("ConstMEDIA_TYPE_IMAGE", "IMAGE");
      map.put("ConstMEDIA_TYPE_MOVIE", "MOVIE");
      map.put("ConstMEDIA_TYPE_FLASH", "FLASH");
      map.put("ConstMEDIA_TYPE_OFFICE", "OFFICE");
      map.put("ConstMEDIA_TYPE_PDF", "PDF");
      List list = ((ContentDaoMapper)this.getMapper()).getCodeFile(map);
      return list != null && list.size() > 0 ? (String)list.get(0) : "";
   }

   public long getGroupIdByContentId(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getGroupIdByContentId(contentId);
   }

   public List getContentIdListByContentFileId(String contentFileId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getContentIdListByContentFileId(contentFileId);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public boolean existContentForCidMappingOfUploader(String contentId, String creatorId) throws SQLException {
      try {
         UserInfo uInfo = UserInfoImpl.getInstance();
         long organizationId = uInfo.getRootGroupIdByUserId(creatorId);
         int cnt = ((ContentDaoMapper)this.getMapper()).countContentForCidMappingOfUploader(contentId, creatorId, organizationId);
         return cnt > 0;
      } catch (Exception var7) {
         this.logger.error(var7);
         return false;
      }
   }

   public boolean isDeletedContentByContentId(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getDeletedContentByContentId(contentId).equalsIgnoreCase("Y");
   }

   public int addMapTemplateContent(String dlkContentId, long versionId, String contentType, String contentId) throws SQLException {
      Map map = new HashMap();
      map.put("dlkIdx", this.createNewId());
      map.put("dlkContentId", dlkContentId);
      map.put("versionId", versionId);
      map.put("contentType", contentType);
      map.put("contentId", contentId);
      int result = -1;
      int retry = 5;

      while(retry > 0) {
         try {
            result = ((ContentDaoMapper)this.getMapper()).addMapTemplateContent(map);
            retry = 0;
         } catch (SQLException var12) {
            if (retry == 1) {
               throw var12;
            }

            try {
               --retry;
               Thread.sleep(1L);
            } catch (InterruptedException var11) {
               this.logger.error(var11.getMessage(), var11);
            }
         }
      }

      return result;
   }

   public String getLfdContentIdByDlkContentId(String dlkContentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getLfdContentIdByDlkContentId(dlkContentId, "LFT");
   }

   public List getContentIdListByDlkContentId(String dlkContentId, long versionId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentIdListByDlkContentId(dlkContentId, versionId, "LFT");
   }

   public Boolean isExistTempateContentMapping(String contentId) throws SQLException {
      try {
         int cnt = ((ContentDaoMapper)this.getMapper()).countExistTempateContentMapping(contentId, "LFT");
         return cnt > 0;
      } catch (Exception var3) {
         this.logger.error(var3);
         return false;
      }
   }

   public int updateUsedTemplateByContentId(String lfdContentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).updateUsedTemplateByContentId(lfdContentId, this.isExistTempateContentMapping(lfdContentId) ? "Y" : "N");
   }

   public String getContentIdByTemplateThumbnailFileId(String fileName, String creatorId) throws SQLException {
      String res = ((ContentDaoMapper)this.getMapper()).getContentIdByTemplateThumbnailFileId(fileName, creatorId);
      return res == null ? "" : res;
   }

   public void updateAsTemplateByContentId(String contentId, String creatorId) throws SQLException {
      try {
         ((ContentDaoMapper)this.getMapper()).updateAsTemplateByContentId(contentId, creatorId);
      } catch (SQLException var4) {
         this.logger.error(var4);
      }

   }

   public String getFtpUserIdByContentId(String contentId) throws SQLException {
      List ftpUserIdStrings = ((ContentDaoMapper)this.getMapper()).getFtpUserIdByContentId(contentId);
      return ftpUserIdStrings != null && ftpUserIdStrings.size() != 0 && ftpUserIdStrings.get(0) != null ? (String)ftpUserIdStrings.get(0) : "";
   }

   public String getFtpIpByContentId(String contentId) throws SQLException {
      List ftpIpStrings = ((ContentDaoMapper)this.getMapper()).getFtpIpByContentId(contentId);
      return ftpIpStrings != null && ftpIpStrings.size() != 0 && ftpIpStrings.get(0) != null ? (String)ftpIpStrings.get(0) : "";
   }

   public String getFtpPathByContentId(String contentId) throws SQLException {
      List ftpPathStrings = ((ContentDaoMapper)this.getMapper()).getFtpPathByContentId(contentId);
      return ftpPathStrings != null && ftpPathStrings.size() != 0 && ftpPathStrings.get(0) != null ? (String)ftpPathStrings.get(0) : "";
   }

   public String getCifsUserIdByContentId(String contentId) throws SQLException {
      List ftpCifsUserIdStrings = ((ContentDaoMapper)this.getMapper()).getCifsUserIdByContentId(contentId);
      return ftpCifsUserIdStrings != null && ftpCifsUserIdStrings.size() != 0 && ftpCifsUserIdStrings.get(0) != null ? (String)ftpCifsUserIdStrings.get(0) : "";
   }

   public String getCifsIpByContentId(String contentId) throws SQLException {
      List ftpCifsIpStrings = ((ContentDaoMapper)this.getMapper()).getCifsIpByContentId(contentId);
      return ftpCifsIpStrings != null && ftpCifsIpStrings.size() != 0 && ftpCifsIpStrings.get(0) != null ? (String)ftpCifsIpStrings.get(0) : "";
   }

   public String getCifsPathByContentId(String contentId) throws SQLException {
      List ftpCifsPathStrings = ((ContentDaoMapper)this.getMapper()).getCifsPathByContentId(contentId);
      return ftpCifsPathStrings != null && ftpCifsPathStrings.size() != 0 && ftpCifsPathStrings.get(0) != null ? (String)ftpCifsPathStrings.get(0) : "";
   }

   public boolean updateContentVersionInfoByContentId(long totalSize, String contentId) throws SQLException {
      try {
         int updated = ((ContentDaoMapper)this.getMapper()).updateContentVersionInfoByContentId(totalSize, contentId);
         return updated > 0;
      } catch (SQLException var5) {
         this.logger.error(var5);
         return false;
      }
   }

   public boolean updateFtpSettingByContentId(String contentId, String ftpContentName, String ftpIP, int port, String ftpLoginId, String ftpPassword, String ftpDirectory, long ftpRefreshInterval, String canRefresh, long loginRetryMaxCount, long loginRetryCount, String canLoginRetry) throws SQLException {
      try {
         Map map = new HashMap();
         map.put("contentId", contentId);
         map.put("ftpContentName", ftpContentName);
         map.put("ftpIP", ftpIP);
         map.put("port", port);
         map.put("ftpLoginId", ftpLoginId);
         map.put("ftpPassword", ftpPassword);
         map.put("ftpDirectory", ftpDirectory);
         map.put("ftpRefreshInterval", ftpRefreshInterval);
         map.put("canRefresh", canRefresh);
         map.put("loginRetryMaxCount", loginRetryMaxCount);
         map.put("loginRetryCount", loginRetryCount);
         map.put("canLoginRetry", canLoginRetry);
         int updated = ((ContentDaoMapper)this.getMapper()).updateFtpSettingByContentId(map);
         return updated > 0;
      } catch (SQLException var18) {
         this.logger.error(var18);
         return false;
      }
   }

   public boolean updateCifsSettingByContentId(String contentId, String cifsContentName, String cifsIP, String cifsLoginId, String cifsPassword, String cifsDirectory, long cifsRefreshInterval, String canRefresh, long loginRetryMaxCount, long loginRetryCount, String canLoginRetry) throws SQLException {
      try {
         Map map = new HashMap();
         map.put("contentId", contentId);
         map.put("cifsContentName", cifsContentName);
         map.put("cifsIP", cifsIP);
         map.put("cifsLoginId", cifsLoginId);
         map.put("cifsPassword", cifsPassword);
         map.put("cifsDirectory", cifsDirectory);
         map.put("cifsRefreshInterval", cifsRefreshInterval);
         map.put("canRefresh", canRefresh);
         map.put("loginRetryMaxCount", loginRetryMaxCount);
         map.put("loginRetryCount", loginRetryCount);
         map.put("canLoginRetry", canLoginRetry);
         int updated = ((ContentDaoMapper)this.getMapper()).updateCifsSettingByContentId(map);
         return updated > 0;
      } catch (SQLException var17) {
         this.logger.error(var17);
         return false;
      }
   }

   public int deleteFileByFileNameByContentId(String contentId, String fileName) throws SQLException {
      List list = ((ContentDaoMapper)this.getMapper()).selectForDeleteFileByFileNameByContentId(contentId, fileName);
      if (list != null && list.size() > 0) {
         DownloadInfo downloadDao = DownloadInfoImpl.getInstance();
         Iterator var5 = list.iterator();

         while(var5.hasNext()) {
            String fileId = (String)var5.next();
            List contentUsingFile = this.getContentIdListByContentFileId(fileId);
            if (contentUsingFile.size() == 0) {
               this.logger.error("[HENNRY deleteFileByFileNameByContentId][DELETE mi_cms_info_file.file_id = ]" + fileId);
               ((ContentDaoMapper)this.getMapper()).deleteFileByFileNameByContentId(fileId);
            }
         }
      }

      return 1;
   }

   public List getContentByTemplateId(String templateId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentByTemplateId(templateId, "LFT");
   }

   public List getDlkContentsIncludedElementContentId(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getDlkContentsIncludedElementContentId(contentId, "NOTLFT");
   }

   public int addTemplateElement(String contentId, long versionId, TemplateElement templateElement) throws SQLException {
      try {
         int result = -1;
         int retry = 5;

         while(retry > 0) {
            try {
               result = ((ContentDaoMapper)this.getMapper()).addTemplateElement(contentId, versionId, templateElement);
               retry = 0;
            } catch (SQLException var10) {
               if (retry == 1) {
                  throw var10;
               }

               try {
                  --retry;
                  Thread.sleep(1L);
               } catch (InterruptedException var9) {
                  this.logger.error(var9.getMessage(), var9);
               }
            }
         }

         return result;
      } catch (SQLException var11) {
         this.logger.error(var11);
         return 0;
      }
   }

   public int addTemplateDisplaySize(String contentId, long versionId, float displayWidth, float displayHeight) throws SQLException {
      try {
         int result = -1;
         int retry = 5;

         while(retry > 0) {
            try {
               result = ((ContentDaoMapper)this.getMapper()).addTemplateDisplaySize(contentId, versionId, displayWidth, displayHeight);
               retry = 0;
            } catch (SQLException var11) {
               if (retry == 1) {
                  throw var11;
               }

               try {
                  --retry;
                  Thread.sleep(1L);
               } catch (InterruptedException var10) {
                  this.logger.error(var10.getMessage(), var10);
               }
            }
         }

         return result;
      } catch (SQLException var12) {
         this.logger.error(var12);
         return 0;
      }
   }

   public TemplateDisplay getTemplateDisplaySize(String contentId, long versionId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getTemplateDisplaySize(contentId, versionId);
      } catch (SQLException var5) {
         this.logger.error(var5);
         return null;
      }
   }

   public List getTemplateElementList(String contentId, long versionId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getTemplateElementList(contentId, versionId);
      } catch (SQLException var5) {
         this.logger.error(var5);
         return null;
      }
   }

   public List getTemplateElementDataList(String contentId, long versionId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getTemplateElementDataList(contentId, versionId);
      } catch (SQLException var5) {
         this.logger.error(var5);
         return null;
      }
   }

   public int addTemplateElementData(String lftContentId, long lftVersionId, String dlkContentId, long dlkVersionId, TemplateElementData templateElementData) throws SQLException {
      try {
         int inserted = ((ContentDaoMapper)this.getMapper()).addTemplateElementData(lftContentId, lftVersionId, dlkContentId, dlkVersionId, templateElementData);
         return inserted > 0 ? inserted : -1;
      } catch (SQLException var9) {
         this.logger.error(var9);
         return -1;
      }
   }

   public int addTemplateElementData(String lftContentId, long lftVersionId, String dlkContentId, long dlkVersionId, String templateElementDataList) throws SQLException {
      try {
         String[] arrContentDataItem = templateElementDataList.split("[|]");
         if (arrContentDataItem.length <= 0) {
            return -1;
         } else {
            TemplateElementData templateElementData = new TemplateElementData();
            templateElementData.setPage_no(Integer.parseInt(arrContentDataItem[0].replace("p", "")));
            templateElementData.setElement_no(Integer.parseInt(arrContentDataItem[1].replace("e", "")));
            templateElementData.setItem_no(Integer.parseInt(arrContentDataItem[2].replace("i", "")));
            templateElementData.setData_no(Integer.parseInt(arrContentDataItem[3].replace("d", "")));
            templateElementData.setElement_name(arrContentDataItem[4]);
            templateElementData.setInput_type(arrContentDataItem[5]);
            templateElementData.setElement_type(arrContentDataItem[6]);
            templateElementData.setItem_type(arrContentDataItem[7]);
            templateElementData.setItem_name(arrContentDataItem[8]);
            templateElementData.setIs_inner_datalink(Boolean.parseBoolean(arrContentDataItem[9]));
            templateElementData.setInput_data(arrContentDataItem[10]);
            templateElementData.setValue_location(arrContentDataItem[11]);
            templateElementData.setContent_src(arrContentDataItem[12]);
            templateElementData.setContent_id(arrContentDataItem[13]);
            templateElementData.setMain_tag(arrContentDataItem[15]);
            templateElementData.setTag_match_type(arrContentDataItem[16]);
            templateElementData.setView_h_v(arrContentDataItem[17]);
            templateElementData.setServer_address(arrContentDataItem[18]);
            templateElementData.setSub_tag(arrContentDataItem[19]);
            templateElementData.setConvert_table(arrContentDataItem[20]);
            int inserted = ((ContentDaoMapper)this.getMapper()).addTemplateElementData(lftContentId, lftVersionId, dlkContentId, dlkVersionId, templateElementData);
            return inserted > 0 ? inserted : -1;
         }
      } catch (SQLException var11) {
         this.logger.error(var11);
         return -1;
      }
   }

   public int setTemplateContentParsed(String contentId, Long versionId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).setTemplateContentParsed(contentId, versionId);
   }

   public int setContentFileSize(String fileId, long fileSize) throws SQLException {
      try {
         int updated = ((ContentDaoMapper)this.getMapper()).setContentFileSize(fileId, fileSize);
         return updated > 0 ? updated : -1;
      } catch (SQLException var5) {
         this.logger.error(var5);
         return -1;
      }
   }

   public int setContentTotalSize(String contentId, long versionId, long fileSize) throws SQLException {
      try {
         int updated = ((ContentDaoMapper)this.getMapper()).setContentTotalSize(contentId, versionId, fileSize);
         return updated > 0 ? updated : -1;
      } catch (SQLException var7) {
         this.logger.error(var7);
         return -1;
      }
   }

   public boolean getContentParsingState(String contentId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getContentParsingState(contentId) > 0;
      } catch (SQLException var3) {
         this.logger.error(var3);
         return false;
      }
   }

   public List getAllTemplateElementDataList(String dlkContentId, long dlkVersionId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getAllTemplateElementDataList(dlkContentId, dlkVersionId);
      } catch (SQLException var5) {
         this.logger.error(var5);
         return null;
      }
   }

   public void updateThumbnailIdOfDlkByContentId(String templateContentId, String versionId, String contentId) throws SQLException {
      try {
         ((ContentDaoMapper)this.getMapper()).updateThumbnailIdOfDlkByContentId(templateContentId, Integer.parseInt(versionId), contentId);
      } catch (SQLException var5) {
         this.logger.error(var5);
      }

   }

   public List getDlkContentIdByTemplateId(String templateContentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getDlkContentIdByTemplateId(templateContentId);
   }

   public String getFileInfoByContentIdVersionId(String contentId, String versionId) throws SQLException {
      List list = ((ContentDaoMapper)this.getMapper()).getFileInfoByContentIdVersionId(contentId, Integer.parseInt(versionId));
      return list != null && list.size() > 0 ? (String)list.get(0) : "";
   }

   public String getActiveVersionByContentId(String contentId) throws SQLException {
      List list = ((ContentDaoMapper)this.getMapper()).getActiveVersionByContentId(contentId);
      return list != null && list.size() > 0 ? (String)list.get(0) : "";
   }

   public String getMediaTypeByContentId(String contentId) throws SQLException {
      List list = ((ContentDaoMapper)this.getMapper()).getMediaTypeByContentId(contentId);
      return list != null && list.size() > 0 ? (String)list.get(0) : "";
   }

   public void updateHashCodeByMainFileId(String dlkMainFileId, long fileSize, String hashCode) throws SQLException {
      try {
         ((ContentDaoMapper)this.getMapper()).updateHashCodeByMainFileId(dlkMainFileId, fileSize, hashCode);
      } catch (SQLException var6) {
         this.logger.error(var6);
      }

   }

   public void updateVersionAndMainFileIdInContentVersionInfo(long version, String mainFileId, String contentId) throws SQLException {
      try {
         ((ContentDaoMapper)this.getMapper()).updateVersionAndMainFileIdInContentVersionInfo(version, mainFileId, contentId);
      } catch (SQLException var6) {
         this.logger.error(var6);
      }

   }

   public String getFtpPasswordByContentId(String contentId) throws SQLException {
      List list = ((ContentDaoMapper)this.getMapper()).getFtpPasswordByContentId(contentId);
      return list != null && list.size() > 0 ? (String)list.get(0) : "";
   }

   public String getCifsPasswordByContentId(String contentId) throws SQLException {
      List list = ((ContentDaoMapper)this.getMapper()).getCifsPasswordByContentId(contentId);
      return list != null && list.size() > 0 ? (String)list.get(0) : "";
   }

   public List getDlkContentIdByIputDataContentId(String iputDataContentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getDlkContentIdByIputDataContentId(iputDataContentId);
   }

   public String getCreatorIdByContentId(String contentId) throws SQLException {
      List list = ((ContentDaoMapper)this.getMapper()).getCreatorIdByContentId(contentId);
      return list != null && list.size() > 0 ? (String)list.get(0) : "";
   }

   public String getIsReadyForNextFtpThread(String contentId) throws SQLException {
      List list = ((ContentDaoMapper)this.getMapper()).getIsReadyForNextFtpThread(contentId);
      return list != null && list.size() > 0 ? (String)list.get(0) : "";
   }

   public void setIsReadyForNextFtpThread(String isReady, String contentId) throws SQLException {
      try {
         ((ContentDaoMapper)this.getMapper()).setIsReadyForNextFtpThread(isReady, contentId);
      } catch (SQLException var4) {
         this.logger.error(var4);
      }

   }

   public String getIsReadyForNextCifsThread(String contentId) throws SQLException {
      List list = ((ContentDaoMapper)this.getMapper()).getIsReadyForNextCifsThread(contentId);
      return list != null && list.size() > 0 ? (String)list.get(0) : "";
   }

   public void setIsReadyForNextCifsThread(String isReady, String contentId) throws SQLException {
      try {
         ((ContentDaoMapper)this.getMapper()).setIsReadyForNextCifsThread(isReady, contentId);
      } catch (SQLException var4) {
         this.logger.error(var4);
      }

   }

   public List getTempContentList() throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getTempContentList();
      } catch (Exception var2) {
         this.logger.error(var2);
         return null;
      }
   }

   public int deleteTempContentCompletely(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).deleteTempContentCompletely(contentId);
   }

   public int setContentUnlock() throws SQLException {
      int retry = 5;

      while(retry > 0) {
         try {
            ((ContentDaoMapper)this.getMapper()).setContentContentUnlock();
            retry = 0;
         } catch (SQLException var5) {
            if (retry == 1) {
               throw var5;
            }

            try {
               --retry;
               Thread.sleep(1L);
            } catch (InterruptedException var4) {
               this.logger.error(var4.getMessage(), var4);
            }
         }
      }

      return ((ContentDaoMapper)this.getMapper()).setPlaylistContentUnlock();
   }

   public int setContentUnlock(String contentId, String sessionId) throws SQLException {
      int result = -1;
      int retry = 5;

      while(retry > 0) {
         try {
            result = ((ContentDaoMapper)this.getMapper()).setContentUnlock(contentId, sessionId);
            retry = 0;
         } catch (SQLException var8) {
            if (retry == 1) {
               throw var8;
            }

            try {
               --retry;
               Thread.sleep(1L);
            } catch (InterruptedException var7) {
               this.logger.error(var7.getMessage(), var7);
            }
         }
      }

      return result;
   }

   public void setIsActive(String contentId, String isActive) throws SQLException {
      try {
         ((ContentDaoMapper)this.getMapper()).setIsActive(contentId, isActive);
      } catch (SQLException var4) {
         this.logger.error(var4);
      }

   }

   public void setApprovalStatus(String contentId, String approvalStatus, String approvalOpinion) throws SQLException {
      try {
         ((ContentDaoMapper)this.getMapper()).setApprovalStatus(contentId, approvalStatus, approvalOpinion);
         Long versionId = this.getContentActiveVer(contentId);
         if ("APPROVED".equalsIgnoreCase(approvalStatus)) {
            try {
               this.setActiveVersion(contentId, versionId, true);
            } catch (Exception var6) {
               this.logger.error("", var6);
            }
         }
      } catch (SQLException var7) {
         this.logger.error(var7);
      }

   }

   public void setApprovalStatus(String contentId, String approvalStatus, String approvalOpinion, SqlSession session) throws SQLException {
      try {
         ((ContentDaoMapper)this.getMapper(session)).setApprovalStatus(contentId, approvalStatus, approvalOpinion);
      } catch (SQLException var6) {
         this.logger.error(var6);
      }

   }

   public int modifyDlkContentByConvertData(Content dlkContent) throws SQLException {
      int cnt = 0;
      SqlSession session = this.openNewSession(false);

      try {
         cnt = this.addContent(dlkContent, session);
         if (cnt <= 0) {
            session.rollback();
         }

         long version = this.setActiveVersion(dlkContent.getContent_id(), true, session);
         if (version <= 0L) {
            session.rollback();
         }

         cnt = ((ContentDaoMapper)this.getMapper(session)).setTemplateContentParsed(dlkContent.getContent_id(), version);
         if (cnt <= 0) {
            session.rollback();
         }

         cnt = this.setTemplateElementDataVersionUp(dlkContent.getContent_id(), version, session);
         if (cnt <= 0) {
            session.rollback();
         }

         cnt = this.setMappingDlkContentVersionUp(dlkContent.getContent_id(), version, session);
         if (cnt <= 0) {
            session.rollback();
         }

         cnt = ((ContentDaoMapper)this.getMapper(session)).deleteContentVersion(dlkContent.getContent_id(), version - 1L);
         if (cnt <= 0) {
            session.rollback();
         } else {
            session.commit();
         }
      } catch (Exception var9) {
         this.logger.error(var9);
      } finally {
         session.rollback();
         session.close();
      }

      return cnt;
   }

   private int setTemplateElementDataVersionUp(String dlkContentId, Long dlkVersionId, SqlSession session) throws SQLException {
      return ((ContentDaoMapper)this.getMapper(session)).setTemplateElementDataVersionUp(dlkVersionId, dlkContentId, dlkVersionId - 1L);
   }

   private int setMappingDlkContentVersionUp(String contentId, Long newVersionId, SqlSession session) throws SQLException {
      return ((ContentDaoMapper)this.getMapper(session)).setMappingDlkContentVersionUp(contentId, newVersionId, newVersionId - 1L);
   }

   public List getMediaTypeByDeviceType(String deviceType) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getMediaTypeByDeviceType(deviceType);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public List getMediaTypeByDeviceTypeAndVersion(String deviceType, Float deviceTypeVersion) throws SQLException {
      List object = null;

      try {
         if (deviceTypeVersion == null) {
            deviceTypeVersion = 1.0F;
         }

         object = ((ContentDaoMapper)this.getMapper()).getMediaTypeByDeviceTypeAndVersion(deviceType, deviceTypeVersion);
      } catch (Exception var5) {
         this.logger.error("", var5);
      }

      return object;
   }

   public List getFileTypeByDeviceTypeAndVersion(String deviceType, Float deviceTypeVersion) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getFileTypeByDeviceTypeAndVersion(deviceType, deviceTypeVersion);
   }

   public List getAllDeviceType() throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getAllDeviceType();
      } catch (Exception var2) {
         this.logger.error(var2);
         return null;
      }
   }

   public String checkFileTypeByDeviceTypeAndContendID(String deviceType, float deviceTypeVersion, String contentId) throws SQLException {
      List list = ((ContentDaoMapper)this.getMapper()).checkFileTypeByDeviceTypeAndContendID(deviceType, deviceTypeVersion, contentId);
      return list != null && list.size() > 0 ? (String)list.get(0) : "";
   }

   public int setAMSLastMemory(String contentId, Map data) throws SQLException {
      String gender = (String)data.get("gender");
      String age = (String)data.get("age");
      return ((ContentDaoMapper)this.getMapper()).setAMSLastMemory(gender, age, contentId);
   }

   public Map getAMSLastMemory(String contentId) throws SQLException {
      List list = ((ContentDaoMapper)this.getMapper()).getAMSLastMemory(contentId);
      return list != null && list.size() > 0 && list.get(0) != null ? (Map)list.get(0) : Collections.emptyMap();
   }

   public String isDelete(String contentId) throws SQLException {
      List list = ((ContentDaoMapper)this.getMapper()).isDelete(contentId);
      return list != null && list.size() > 0 ? (String)list.get(0) : null;
   }

   public String getMainFileIdFromContentId(String ContentId) {
      String rtn = null;

      try {
         rtn = ((ContentDaoMapper)this.getMapper()).getMainFileIdFromContentId(ContentId);
      } catch (SQLException var4) {
         this.logger.error("", var4);
      }

      return rtn;
   }

   public boolean updateVwlModelCountInfo(String content_id, String modelCountInfo) throws SQLException {
      boolean retVal = false;

      try {
         if (((ContentDaoMapper)this.getMapper()).updateVwlModelCountInfo(modelCountInfo, content_id) > 0) {
            retVal = true;
         }
      } catch (SQLException var5) {
         this.logger.error("", var5);
      }

      return retVal;
   }

   public String getModelCountInfo(String content_id) {
      String rtn = null;

      try {
         rtn = ((ContentDaoMapper)this.getMapper()).getModelCountInfo(content_id);
      } catch (SQLException var4) {
         this.logger.error("", var4);
      }

      return rtn;
   }

   public Object[] getContentPriority(String content_id) {
      Object[] obj = new Object[2];

      try {
         Map map = ((ContentDaoMapper)this.getMapper()).getContentPriority(content_id);
         if (map.size() == 2) {
            obj[0] = map.get("device_type");
            obj[1] = map.get("device_type_version");
         } else {
            obj = null;
         }
      } catch (SQLException var4) {
         this.logger.error("", var4);
      }

      return obj;
   }

   public boolean updateMultiVWL(String content_id) {
      boolean retVal = false;

      try {
         if (((ContentDaoMapper)this.getMapper()).updateMultiVWL(content_id) > 0) {
            retVal = true;
         }
      } catch (SQLException var4) {
         this.logger.error("", var4);
      }

      return retVal;
   }

   public int addMapVwlDevice(String content_id, Long deivce_group_id) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).addMapVwlDevice(content_id, deivce_group_id);
   }

   public List getDeviceGroupListByMultiVwlContentId(String content_id) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getDeviceGroupListByMultiVwlContentId(content_id);
   }

   /** @deprecated */
   @Deprecated
   public Long getGroupIdfromContentId(String content_id) throws SQLException {
      throw new SQLException("org.postgresql.util.PSQLException: ERROR: column \"content_id\" does not exist in table MI_DMS_MAP_GROUP_DEVICE");
   }

   public String getMappingDevicesFilePath(String content_id) throws SQLException {
      Map map = ((ContentDaoMapper)this.getMapper()).getMappingDevicesFilePath(content_id);
      return map.get("FILE_PATH") + File.separator + map.get("FILE_NAME");
   }

   public Map getMappingDeviceInformation(String contentID) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getMappingDeviceInformation(contentID);
   }

   public List getContentIdListByContentFileId(String contentFileId, String excludeContentId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getContentIdListByFileAndContentIds(contentFileId, excludeContentId);
      } catch (Exception var4) {
         this.logger.error("", var4);
         return null;
      }
   }

   public boolean isUsedAfterVersion(String fileId, long versionId) {
      try {
         return ((ContentDaoMapper)this.getMapper()).isUsedAfterVersion(fileId, versionId);
      } catch (SQLException var5) {
         this.logger.error("", var5);
         return false;
      }
   }

   public boolean isUsedThumbAfterVersion(String fileId, long versionId) {
      try {
         int count = false;
         boolean result = false;
         int count = ((ContentDaoMapper)this.getMapper()).isUsedThumbAfterVersion(fileId, versionId);
         if (count > 0) {
            result = true;
         } else {
            result = false;
         }

         return result;
      } catch (SQLException var6) {
         this.logger.error("", var6);
         return false;
      }
   }

   private void addWhereOrgan(Map map) throws SQLException {
      String creatorID = (String)map.get("creatorID");
      UserInfo uInfo = UserInfoImpl.getInstance();
      AbilityInfo abilityInfo = AbilityInfoImpl.getInstance();
      User user = uInfo.getAllByUserId(creatorID);
      String organization = user.getOrganization();
      List abilityList = abilityInfo.getAllAbilityListByUserId(creatorID);
      if (!organization.equalsIgnoreCase("ROOT")) {
         List userList = uInfo.getAllUserByRootGroupId(user.getRoot_group_id());
         if (userList.size() > 0) {
            Map organMap = new HashMap();
            Iterator var10 = userList.iterator();

            while(true) {
               while(var10.hasNext()) {
                  Map userMap = (Map)var10.next();
                  String userName = (String)userMap.get("user_id");
                  if (!abilityList.contains("Content Manage Authority") && !creatorID.equalsIgnoreCase(userName)) {
                     organMap.put(userName, ContentConstants.SHARE_FLAG_YES);
                  } else {
                     organMap.put(userName, (Object)null);
                  }
               }

               map.put("whereOrganMap", organMap);
               break;
            }
         }
      }

   }

   private Map createContentListWithExtraParams(Map oldMap) throws SQLException, ConfigException {
      Map map = new HashMap(oldMap);
      String deviceFilter = (String)map.get("deviceFilter");
      String deviceType = (String)map.get("deviceType");
      String deviceTypeVersion = (String)map.get("deviceTypeVersion");
      String[] deviceFilterList = null;
      String listType;
      if (deviceFilter != null && !deviceFilter.equals("")) {
         deviceFilterList = CommonUtils.getAllSupportDeviceListByDeviceFilterList(deviceFilter.split(","));
      } else if (deviceType != null && !deviceType.equals("") && deviceTypeVersion != null && !deviceTypeVersion.equals("")) {
         listType = CommonUtils.convertDeviceTypeAndVerisonToDeviceType(deviceType, deviceTypeVersion);
         deviceFilterList = CommonUtils.getAllSupportDeviceListByDeviceFilterList(listType.split(","));
      }

      if (deviceFilterList != null) {
         map.put("deviceTypeArr", deviceFilterList);
      }

      listType = (String)map.get("listType");
      String creatorID = (String)map.get("creatorID");
      String searchCreator = (String)map.get("searchCreator");
      String groupID = (String)map.get("groupID");
      String searchText = (String)map.get("searchText");
      String sortColumn = (String)map.get("sortColumn");
      String sortOrder = (String)map.get("sortOrder");
      String media_type_filter = (String)map.get("media_type_filter");
      String approval_status = (String)map.get("approval_status");
      String search_start_date = (String)map.get("startDate");
      String search_end_date = (String)map.get("endDate");
      String created_date_from = (String)map.get("createdDateFrom");
      String created_date_to = (String)map.get("createdDateTo");
      Boolean isContentApprove = ContentUtils.nvl((Boolean)map.get("isContentApprove"));
      map.put("isContentApprove", isContentApprove);
      map.put("isMultiApproval", true);
      Boolean canReadUnshared = (Boolean)map.get("canReadUnshared");
      Boolean myContentDelete = ContentUtils.nvl((Boolean)map.get("myContentDelete"));
      map.put("myContentDelete", myContentDelete);
      String expirationStatusFilter = (String)map.get("expirationStatusFilter");
      if (expirationStatusFilter != null && expirationStatusFilter.length() > 0) {
         map.put("expirationStatusFilter", expirationStatusFilter);
         map.put("curDate", DateUtils.getCurrentTime("yyyyMMdd"));
      }

      String source = (String)map.get("source");
      if (source != null && source.length() > 0) {
         map.put("source", source);
         map.put("curDate2", DateUtils.getCurrentTime("yyyyMMdd"));
      }

      if (canReadUnshared == null) {
         map.put("canReadUnshared", false);
      }

      UserInfo uInfo = UserInfoImpl.getInstance();
      if (map.get("creatorID") != null && map.get("creatorID") != "") {
         long organizationId = 0L;
         User logged;
         if (map.get("byUserOrganizationId") != null && !"".equals(map.get("byUserOrganizationId"))) {
            String byUserOrganizationId = (String)map.get("byUserOrganizationId");
            organizationId = Long.valueOf(byUserOrganizationId);
         } else if (SecurityUtils.getUserContainer() != null) {
            organizationId = SecurityUtils.getUserContainer().getUser().getRoot_group_id();
         } else {
            logged = uInfo.getUserInfo((String)map.get("creatorID"));
            if (logged.isMu()) {
               organizationId = uInfo.getCurMngOrgId((String)map.get("creatorID"));
            } else {
               organizationId = uInfo.getRootGroupIdByUserId((String)map.get("creatorID"));
            }
         }

         if (organizationId != 0L) {
            map.put("organizationId", organizationId);
            if (map.containsKey("isTLFD") && map.get("isTLFD").equals("Y")) {
               map.remove("organizationId");
            }
         }

         logged = uInfo.getUserInfo((String)map.get("creatorID"));
         if (logged != null) {
            boolean hasPermission = this.hasRoleForAllContents(logged);
            map.put("hasPermission", hasPermission);
            if (hasPermission && logged.getRole_name().equals("Content All Manager")) {
               map.remove("organizationId");
            }
         }
      }

      if (listType.equalsIgnoreCase("UNGROUPED")) {
         map.put("groupRootId", this.getRootId(creatorID));
      }

      if (listType.equalsIgnoreCase("DELETED")) {
         map.put("listType", "DELETED");
      }

      User logged;
      if (groupID != null && !"".equals(groupID)) {
         String userId = null;
         if (SecurityUtils.getUserContainer() != null) {
            logged = SecurityUtils.getLoginUser();
            userId = logged.getUser_id();
         } else {
            userId = (String)map.get("creatorID");
         }

         List groupIdList = new ArrayList();
         String[] groupArr = groupID.split(",");

         for(int i = 0; i < groupArr.length; ++i) {
            boolean recursive = false;
            String tmpGroupId = groupArr[i];
            if (tmpGroupId.indexOf("c") != -1) {
               tmpGroupId = tmpGroupId.substring(0, tmpGroupId.length() - 1);
               recursive = true;
            }

            groupIdList.add(Long.parseLong(tmpGroupId));
            if (!tmpGroupId.equals("0") && recursive) {
               List tmp = this.getChildGroupList(Long.valueOf(tmpGroupId), false, userId);

               for(int j = 0; j < tmp.size(); ++j) {
                  groupIdList.add(((Group)tmp.get(j)).getGroup_id());
               }
            }
         }

         map.put("groupIds", groupIdList);
      }

      if (canReadUnshared != null && !canReadUnshared && listType.equalsIgnoreCase("ORGAN")) {
         this.addWhereOrgan(map);
      }

      if (searchText != null && searchText.length() > 0) {
         map.put("searchText", searchText.toUpperCase().replaceAll("_", "^_").replaceAll("\\[", "^["));
      }

      if (searchCreator != null && searchCreator.length() > 0) {
         map.put("searchCreator", searchCreator.toUpperCase().replaceAll("_", "^_"));
      }

      if (sortColumn != null && sortColumn.length() > 0) {
         map.put("sortColumn", ((String)map.get("sortColumn")).toUpperCase());
         if (sortOrder != null && sortOrder.length() > 0) {
            map.put("sortOrder", ((String)map.get("sortOrder")).toUpperCase());
         }
      }

      if (approval_status != null && approval_status.length() > 0) {
         map.put("approval_status", approval_status);
      }

      if (media_type_filter != null && media_type_filter.length() > 0) {
         String[] media_type_filter_array = media_type_filter.split(",");
         if (media_type_filter_array != null && media_type_filter_array.length > 0) {
            map.put("media_type_filter", Arrays.asList(media_type_filter_array));
         }
      }

      if (search_start_date != null && search_start_date.length() > 0) {
         map.put("searchStartDate", search_start_date + " 00:00:00");
      }

      if (search_end_date != null && search_end_date.length() > 0) {
         map.put("searchEndDate", search_end_date + " 23:59:59");
      }

      if (created_date_from != null && created_date_from.length() > 0) {
         if (DateUtils.checkFormat("yyyy-MM-dd HH:mm:ss", created_date_from)) {
            map.put("createdDateFrom", created_date_from);
         } else {
            map.put("createdDateFrom", created_date_from + " 00:00:00");
         }
      }

      if (created_date_to != null && created_date_to.length() > 0) {
         if (DateUtils.checkFormat("yyyy-MM-dd HH:mm:ss", created_date_to)) {
            map.put("createdDateTo", created_date_to);
         } else {
            map.put("createdDateTo", created_date_to + " 23:59:59");
         }
      }

      if (map.get("creatorID") != null && map.get("creatorID") != "") {
         logged = uInfo.getUserInfo((String)map.get("creatorID"));
         Long organizationId;
         if (logged.isMu()) {
            organizationId = uInfo.getCurMngOrgId((String)map.get("creatorID"));
         } else {
            organizationId = uInfo.getRootGroupIdByUserId((String)map.get("creatorID"));
         }

         if (organizationId != 0L && map.containsKey("isTLFD") && map.get("isTLFD").equals("Y")) {
            map.remove("creatorID");
         }
      }

      this.castToLong(map, "screen_count");
      this.castToLong(map, "x_count");
      this.castToLong(map, "y_count");
      this.castToLong(map, "x_range");
      this.castToLong(map, "y_range");
      map.put("ConstCONTENT_TYPE_CONTENT", "CONTENT");
      map.put("ConstCONTENT_TYPE_TEMPLATE", "TEMPLATE");
      map.put("ConstGROUP_TYPE_UNGROUPED", "UNGROUPED");
      map.put("ConstGROUP_TYPE_GROUPED", "GROUPED");
      map.put("ConstGROUP_TYPE_USER", "USER");
      map.put("ConstGROUP_TYPE_ORGAN", "ORGAN");
      map.put("ConstGROUP_TYPE_ALL", "ALL");
      map.put("ConstGROUP_TYPE_DELETED", "DELETED");
      map.put("ConstGROUP_TYPE_SUBMITTED", "SUBMITTED");
      map.put("ConstGROUP_TYPE_SHAREFOLDER", "SHAREFOLDER");
      map.put("ConstMEDIA_TYPE_SOUND", "SOUND");
      map.put("ConstMEDIA_TYPE_VWL", "VWL");
      map.put("ConstMEDIA_TYPE_TEMPLATE_EXTENSION", "LFT");
      map.put("ConstMEDIA_TYPE_MOVIE", "MOVIE");
      map.put("ConstMEDIA_TYPE_FLASH", "FLASH");
      map.put("ConstMEDIA_TYPE_IMAGE", "IMAGE");
      map.put("ConstMEDIA_TYPE_PDF", "PDF");
      map.put("ConstMEDIA_TYPE_OFFICE", "OFFICE");
      map.put("ConstMEDIA_TYPE_FTP", "FTP");
      map.put("ConstMEDIA_TYPE_CIFS", "CIFS");
      map.put("ConstMEDIA_TYPE_RULE", "RULE");
      map.put("ConstMEDIA_TYPE_STRM", "STRM");
      map.put("ConstMEDIA_TYPE_DLK", "DLK");
      map.put("ConstSHARE_FLAG_DEFAULT", ContentConstants.SHARE_FLAG_DEFAULT);
      map.put("ConstTYPE_PREMIUM", "iPLAYER");
      map.put("ConstTYPE_ALL", "ALL");
      map.put("ConstTYPE_LITE", "LPLAYER");
      map.put("ConstTYPE_SOC", "SPLAYER");
      map.put("ConstTYPE_SOC2", "S2PLAYER");
      map.put("ConstTYPE_SOC3", "S3PLAYER");
      map.put("ConstTYPE_SOC4", "S4PLAYER");
      map.put("ConstTYPE_SOC5", "S5PLAYER");
      map.put("ConstTYPE_SOC6", "S6PLAYER");
      map.put("ConstTYPE_SOC7", "S7PLAYER");
      map.put("ConstTYPE_SOC9", "S9PLAYER");
      map.put("ConstTYPE_SOC10", "S10PLAYER");
      map.put("ConstTYPE_APLAYER", "APLAYER");
      map.put("ConstTYPE_WPLAYER", "WPLAYER");
      map.put("TYPE_VERSION_10_0", CommonDataConstants.TYPE_VERSION_10_0);
      map.put("TYPE_VERSION_9_0", CommonDataConstants.TYPE_VERSION_9_0);
      map.put("TYPE_VERSION_7_0", CommonDataConstants.TYPE_VERSION_7_0);
      map.put("TYPE_VERSION_6_0", CommonDataConstants.TYPE_VERSION_6_0);
      map.put("TYPE_VERSION_5_0", CommonDataConstants.TYPE_VERSION_5_0);
      map.put("TYPE_VERSION_4_0", CommonDataConstants.TYPE_VERSION_4_0);
      map.put("TYPE_VERSION_3_0", CommonDataConstants.TYPE_VERSION_3_0);
      map.put("TYPE_VERSION_2_0", CommonDataConstants.TYPE_VERSION_2_0);
      map.put("TYPE_VERSION_1_0", CommonDataConstants.TYPE_VERSION_1_0);
      map.put("ConstMEDIA_TYPE_LFD", "LFD");
      map.put("ConstTYPE_VERSION_1_0", Float.toString(CommonDataConstants.TYPE_VERSION_1_0));
      map.put("ConstTYPE_THIRDPARTY", "3rdPartyPLAYER");
      map.put("ConstPLAYLIST_TYPE_PREMIUM", "0");
      map.put("ConstPLAYLIST_TYPE_SYNCPLAY", "3");
      map.put("ConstPLAYLIST_TYPE_AMS", "1");
      map.put("ConstPLAYLIST_TYPE_VWL", "2");
      map.put("ConstPLAYLIST_TYPE_ADVERTISEMENT", "4");
      map.put("ConstAPPROVED", "APPROVED");
      map.put("ConstUNAPPROVED", "UNAPPROVED");
      map.put("ConstREJECTED", "REJECTED");
      map.put("useMultiVWL", StringUtils.isBlank((String)oldMap.get("useMultiVWL")) ? "N" : oldMap.get("useMultiVWL"));
      map.put("ConstMEDIA_TYPE_FOR_AUTHOR", ContentConstants.getMediaTypeForAuthor());
      return map;
   }

   private boolean hasRoleForAllContents(User logged) {
      AbilityInfoImpl aInfo = AbilityInfoImpl.getInstance();

      try {
         List abilityList = aInfo.getAllAbilityListByUserId(logged.getUser_id());
         Iterator it = abilityList.iterator();

         while(it.hasNext()) {
            Map abilityMap = (Map)it.next();
            String abilityValue = (String)abilityMap.get("ability_name");
            if (abilityValue.equalsIgnoreCase("Content Manage Authority")) {
               return true;
            }
         }
      } catch (SQLException var7) {
         this.logger.error("", var7);
      }

      return false;
   }

   private void castToLong(Map map, String param) {
      String value = (String)map.get(param);
      if (value != null && value.length() > 0) {
         map.put(param, Long.parseLong(value));
      } else {
         map.put(param, (Object)null);
      }

   }

   private int createNewId() throws SQLException {
      return SequenceDB.getNextValue("MI_CMS_MAP_DLKCONTENT_LFDCONTENT");
   }

   public int deleteFileFromContentMap(String contentId, String fileId) throws SQLException {
      ((ContentDaoMapper)this.getMapper()).deleteMapContentFile(contentId, fileId);
      List contentUsingFile = this.getContentIdListByContentFileId(fileId);
      boolean canDelete = this.isDeletableFile(fileId, contentId);
      if (canDelete && contentUsingFile.size() == 0) {
         DownloadInfo downloadDao = DownloadInfoImpl.getInstance();
         ((ContentDaoMapper)this.getMapper()).deleteFileByFileNameByContentId(fileId);
      }

      return 1;
   }

   public int deleteFileFromContentMapByFileName(String contentId, String fileName) throws SQLException {
      List list = ((ContentDaoMapper)this.getMapper()).selectForDeleteFileByFileNameByContentId(contentId, fileName);
      if (list != null && list.size() > 0) {
         DownloadInfo downloadDao = DownloadInfoImpl.getInstance();
         Iterator var5 = list.iterator();

         while(var5.hasNext()) {
            String fileId = (String)var5.next();
            ((ContentDaoMapper)this.getMapper()).deleteMapContentFile(contentId, fileId);
            List contentUsingFile = this.getContentIdListByContentFileId(fileId);
            if (contentUsingFile.size() == 0) {
               ((ContentDaoMapper)this.getMapper()).deleteFileByFileNameByContentId(fileId);
            }
         }
      }

      return 1;
   }

   public void setDeleteLock(String fileID, String deleteLock) throws SQLException {
      ((ContentDaoMapper)this.getMapper()).setDeleteLock(fileID, deleteLock);
   }

   public String getDeleteLock(String fileID) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getDeleteLock(fileID);
   }

   public int setVersionId(String contentId, long oldVersionId, long newVersionId) throws SQLException, ConfigException, Exception {
      return ((ContentDaoMapper)this.getMapper()).setVersionId(contentId, oldVersionId, newVersionId);
   }

   public void deleteContentFromDLK(String contentId) throws SQLException {
      ((ContentDaoMapper)this.getMapper()).deleteContentFromDLK(contentId);
   }

   public List getPlaylistInfoUsingContent(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getPlaylistInfoUsingContent(contentId);
   }

   public List getLitePlaylistInfoUsingContent(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getLitePlaylistInfoUsingContent(contentId);
   }

   public void deleteContentIdFromDlkContentIdMap(String contentId) throws SQLException {
      ((ContentDaoMapper)this.getMapper()).deleteContentIdFromDlkContentIdMap(contentId);
   }

   public long getLastestDlkVersionId(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getLastestDlkVersionId(contentId);
   }

   public long getMaxContentVersionId(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentNextVer(contentId);
   }

   public long getLastestDlkDataVersionId(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getLastestDlkDataVersionId(contentId);
   }

   public long deleteOldVersionContent(String contentId, long maxVersionId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).deleteOldVersionContent(contentId, maxVersionId);
   }

   public int isExistMapVersionfile(String contentId, long versionId, String fileId) throws Exception {
      return ((ContentDaoMapper)this.getMapper()).isExistMapVersionfile(contentId, versionId, fileId);
   }

   public List getContentInfoByContentIdAndVersion(String contentId, long versionId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentInfoByContentIdAndVersion(contentId, versionId);
   }

   public int getUsedContentCount(long organizationId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getUsedContentCount(organizationId);
   }

   public int getUnapprovedContentCnt(long organizationId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getUnapprovedContentCnt(organizationId);
   }

   public int getUnapprovedContentCnt() throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getUnapprovedContentCnt();
   }

   public List getContentListWithThumbnailFromTagId(long tagId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentListWithThumbnailFromTagId(tagId);
   }

   public List getContentListWithThumbnailFromTagNumber(long tagId, String[] tagConditionEqual, String[] tagConditionUp, String[] tagConditionDown) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentListWithThumbnailFromTagNumber(tagId, tagConditionEqual, tagConditionUp, tagConditionDown);
   }

   public List getContentListWithThumbnailFromTagBoolean(long tagId, long tagConditionId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentListWithThumbnailFromTagBoolean(tagId, tagConditionId);
   }

   public List getTagContentList(long tagId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getTagContentList(tagId);
   }

   public List getTagFromContentId(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getTagFromContentId(contentId);
   }

   public List getContentApproverListByContentId(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentApproverListByContentId(contentId);
   }

   public List getContentApproverInfoByUserId(String userId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentApproverInfoByUserId(userId);
   }

   public int addContentApproverMap(String contentId, String userId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).addContentApproverMap(contentId, userId);
   }

   public int addContentApproverMap(String contentId, String userId, SqlSession session) throws SQLException {
      return ((ContentDaoMapper)this.getMapper(session)).addContentApproverMap(contentId, userId);
   }

   public int deleteContentApproverMap(String contentId, String userId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).deleteContentApproverMap(contentId, userId);
   }

   public int deleteContentApproverMapByContentId(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).deleteContentApproverMapByContentId(contentId);
   }

   public boolean setThumbnailMap(String contentId, String fileId, long index, String mode) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).setThumbnailMap(contentId, fileId, index, mode);
   }

   public List getMovieThumbnails(String contentId, String mode) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getMovieThumbnails(contentId, mode);
   }

   public boolean deleteContentApproverMapByUserId(String userId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).deleteContentApproverMapByUserId(userId);
   }

   public boolean deleteThumbMovieMap(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).deleteThumbMovieMap(contentId);
   }

   public String getThumbIdByMainFileId(String main_file_id) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getThumbIdByMainFileId(main_file_id);
   }

   public List getTLFDGroupList(String creatorId) {
      try {
         UserInfo uInfo = UserInfoImpl.getInstance();
         long organizationId = uInfo.getRootGroupIdByUserId(creatorId);
         List userManageGroupList = null;
         if (organizationId == 0L) {
            UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
            userManageGroupList = userGroupInfo.getUserManageGroupListByUserId(creatorId);
         }

         return ((ContentDaoMapper)this.getMapper()).getTLFDGroupList(creatorId, organizationId, userManageGroupList);
      } catch (Exception var7) {
         this.logger.error(var7);
         return null;
      }
   }

   public List getTLFDChildGroupList(Long groupId, boolean recursive, String creatorId) throws SQLException {
      List groupList = new ArrayList();
      List resList = null;
      UserInfo uInfo = UserInfoImpl.getInstance();
      long organizationId = uInfo.getRootGroupIdByUserId(creatorId);
      List userManageGroupList = null;
      if (organizationId == 0L) {
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         userManageGroupList = userGroupInfo.getUserManageGroupListByUserId(creatorId);
      }

      resList = ((ContentDaoMapper)this.getMapper()).getTLFDChildGroupList(groupId, organizationId, creatorId, userManageGroupList);
      groupList.addAll(resList);
      if (recursive) {
         Iterator var12 = resList.iterator();

         while(var12.hasNext()) {
            Group group = (Group)var12.next();
            groupList.addAll(this.getTLFDChildGroupList(group.getGroup_id(), recursive, creatorId));
         }
      }

      return groupList;
   }

   public Long getTLFDOrganizationIdByGroupId(long groupId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getTLFDOrganizationIdByGroupId(groupId);
   }

   public List getTLFDGroupIdsByOrgId(long orgId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getTLFDGroupIdsByOrgId(orgId);
   }

   public List getTLFDListByGroupId(long groupId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getTLFDListByGroupId(groupId);
   }

   public int setTLFDGroup(String contentId, Long groupId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      byte var4;
      try {
         byte var5;
         try {
            if (((ContentDaoMapper)this.getMapper(session)).isExistTLFDGroup(groupId) <= 0) {
               session.rollback();
               var4 = -1;
               return var4;
            }

            if (((ContentDaoMapper)this.getMapper(session)).setTLFDGroup(contentId, groupId) > 0) {
               int cnt = ((ContentDaoMapper)this.getMapper(session)).setContentModifiedDate(contentId);
               if (cnt <= 0) {
                  session.rollback();
                  var5 = -1;
                  return var5;
               }

               session.commit();
               int var12 = cnt;
               return var12;
            }

            session.rollback();
            var4 = -1;
         } catch (SQLException var9) {
            session.rollback();
            this.logger.error(var9);
            var5 = -1;
            return var5;
         }
      } finally {
         session.close();
      }

      return var4;
   }

   public Content getTLFDInfo(String contentID) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getTLFDInfo(contentID);
   }

   public Long getTLFDRoot_GroupId(String groupID) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getTLFDRoot_GroupId(Long.parseLong(groupID));
   }

   public int addTLFDGroup(Long groupId, String groupName, Long pGroupId, Long groupDepth, String creatorId, Long organizationId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).addTLFDGroup(groupId, groupName, pGroupId, groupDepth, creatorId, organizationId);
   }

   public List getSupportedDeviceTypeByContentType(String contentType) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getSupportedDeviceTypeByContentType(contentType);
   }

   public boolean updateContentForStartPageRefreshInterval(Content content) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).updateContentForStartPageRefreshInterval(content);
   }

   public boolean isForceDeletableContent(String contentId) throws SQLException {
      int count = ((ContentDaoMapper)this.getMapper()).numberOfSyncPlayUsingContent(contentId);
      return count <= 0;
   }

   public boolean checkMappingSchedule(long groupId) throws SQLException {
      boolean rtn = false;
      List groups = ((ContentDaoMapper)this.getMapper()).getAllContentGroups(groupId);

      try {
         if (groups != null && groups.size() > 0) {
            Iterator var5 = groups.iterator();

            while(true) {
               while(true) {
                  List contents;
                  do {
                     do {
                        if (!var5.hasNext()) {
                           return rtn;
                        }

                        Group group = (Group)var5.next();
                        contents = this.getGroupedContentIdList(group.getGroup_id());
                     } while(contents == null);
                  } while(contents.size() <= 0);

                  Iterator var8 = contents.iterator();

                  while(var8.hasNext()) {
                     Map content = (Map)var8.next();
                     String contentId = (String)content.get("content_id");
                     if (!this.isDeletableContent(contentId, (String)null)) {
                        rtn = true;
                        break;
                     }
                  }
               }
            }
         }
      } catch (Exception var11) {
         this.logger.error("", var11);
      }

      return rtn;
   }

   public Content getThumbnailByFileId(String fileId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getThumbnailByFileId(fileId);
   }

   public Map getThumbnailByThumbnailFileId(String thumbnailFileId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getThumbnailByThumbnailFileId(thumbnailFileId);
   }

   public List getShareFolderList(long groupId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getShareFolderList(groupId);
   }

   public List getContentPollingHistory(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentPollingHistory(contentId);
   }

   public long getDefaultContentGroupId(String userId, long orgnizationId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getRootId(userId, orgnizationId, ContentConstants.PARENT_GROUP_OF_UNGROUPED);
   }

   public int addPollingInfo(String contentId, Date pollingTime, String pollingStatus, int fileCount, String statusDescription, String creatorId) throws SQLException {
      Map map = new HashMap();
      map.put("contentId", contentId);
      map.put("pollingTime", pollingTime);
      map.put("pollingStatus", pollingStatus);
      map.put("fileCount", fileCount);
      map.put("statusDescription", statusDescription);
      map.put("creatorId", creatorId);
      return ((ContentDaoMapper)this.getMapper()).addPollingInfo(map);
   }

   public int addPollingFileInfo(String contentId, Date pollingTime, String fileName, long fileSize, String fileStatus, String creatorId) throws SQLException {
      Map map = new HashMap();
      map.put("contentId", contentId);
      map.put("pollingTime", pollingTime);
      map.put("fileName", fileName);
      map.put("fileSize", fileSize);
      map.put("fileStatus", fileStatus);
      map.put("creatorId", creatorId);
      return ((ContentDaoMapper)this.getMapper()).addPollingFileInfo(map);
   }

   public int deletePollingInfo(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).deletePollingInfo(contentId);
   }

   public int deletePollingFileInfo(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).deletePollingFileInfo(contentId);
   }

   public boolean deleteOldPollingInfoAndPollingFileInfo(String contentId) {
      SqlSession session = this.openNewSession(false);

      try {
         ((ContentDaoMapper)this.getMapper(session)).deleteOldPollingFileInfo(contentId);
         ((ContentDaoMapper)this.getMapper(session)).deleteOldPollingInfo(contentId);
         session.commit();
         boolean var3 = true;
         return var3;
      } catch (Exception var7) {
         session.rollback();
         this.logger.error(var7);
      } finally {
         session.close();
      }

      return false;
   }

   public String getOneContentByFileId(String fileId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getOneContentByFileId(fileId);
   }

   public String getFileIdFromContentByFileNameAndSize(String contentId, String fileName, long fileSize) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getFileIdFromContentByFileNameAndSize(contentId, fileName, fileSize);
   }

   public List getPollableCifsContentSettingList() throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getPollableCifsContentSettingList();
   }

   public List getPollableFtpContentSettingList() throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getPollableFtpContentSettingList();
   }

   public void setIsReadyForAllCifsThread(String isReady) throws SQLException {
      ((ContentDaoMapper)this.getMapper()).setIsReadyForAllCifsThread(isReady);
   }

   public void setIsReadyForAllFtpThread(String isReady) throws SQLException {
      ((ContentDaoMapper)this.getMapper()).setIsReadyForAllFtpThread(isReady);
   }

   public int getCntContentByOrganizationId(Long organizationId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getCntContentByOrganizationId(organizationId);
   }

   public void changeGroupIdOf_MI_CMS_MAP_GROUP_CONTENT(Long groupId, String fromUserId, Long organizationId) throws SQLException {
      ((ContentDaoMapper)this.getMapper()).changeGroupIdOf_MI_CMS_MAP_GROUP_CONTENT(groupId, fromUserId, organizationId);
   }

   public void changeCreatorIdOf_MI_CMS_INFO_CONTENT(String fromUserId, String toUserId, Long organizationId) throws SQLException {
      ((ContentDaoMapper)this.getMapper()).changeCreatorIdOf_MI_CMS_INFO_CONTENT(fromUserId, toUserId, organizationId);
   }

   public void updateRulesetGroup(String current_creator, String new_creator) throws SQLException {
      ((ContentDaoMapper)this.getMapper()).updateRulesetGroup(current_creator, new_creator);
   }

   public void changeCreatorIdOf_MI_CMS_INFO_CONTENT_VERSION(String fromUserId, String toUserId, Long organizationId) throws SQLException {
      ((ContentDaoMapper)this.getMapper()).changeCreatorIdOf_MI_CMS_INFO_CONTENT_VERSION(fromUserId, toUserId, organizationId);
   }

   public void changeCreatorIdOf_MI_CMS_INFO_FILE(String fromUserId, String toUserId, Long organizationId) throws SQLException {
      ((ContentDaoMapper)this.getMapper()).changeCreatorIdOf_MI_CMS_INFO_FILE(fromUserId, toUserId, organizationId);
   }

   public void deleteGroupByCreatorId(String creatorId) throws SQLException {
      ((ContentDaoMapper)this.getMapper()).deleteGroupByCreatorId(creatorId);
   }

   public long getCntAllContents(String creatorId, Long organizationId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getCntAllContents(creatorId, organizationId);
   }

   public String getPlayTimeOfLftByDlk(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getPlayTimeOfLftByDlk(contentId);
   }

   public boolean setContentPlayTime(String contentId, String playTime) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).setContentPlayTime(contentId, playTime);
   }

   public boolean setExpirationDate(String contentId, String expirationDate) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).setExpirationDate(contentId, expirationDate);
   }

   public List getExpiredContentList() throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getExpiredContentList(DateUtils.getCurrentTime("yyyyMMdd"));
   }

   public boolean isContentApproverMap(String contentId, String userId, SqlSession session) throws SQLException {
      return ((ContentDaoMapper)this.getMapper(session)).countContentApproverMap(contentId, userId) > 0;
   }

   public List getGroupList(long organization_id) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getGroupListByOrganizationId(organization_id);
   }

   public List getTLFDGroupListByOrgId(Long organizationId) throws SQLException {
      try {
         return ((ContentDaoMapper)this.getMapper()).getTLFDGroupListByOrgId(organizationId);
      } catch (Exception var3) {
         this.logger.error(var3);
         return null;
      }
   }

   public List getContentIdListByContentName(String[] contentNameList) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentIdListByContentName(contentNameList);
   }

   public List getContentIdListByRegex(String regex) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentIdListByRegex(regex);
   }

   public List getContentGroupBySearch(String searchText, long organizationId, String userId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentGroupBySearch(searchText, organizationId, userId);
   }

   public List getTLFDGroupBySearch(String searchText, Long organizationId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getTLFDGroupBySearch(searchText, organizationId);
   }

   public List getParentsGroupList(int pGroupId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getParentsGroupList(pGroupId);
   }

   public Long getOrganizationIdByGroupId(Long groupId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getOrganizationIdByGroupId(groupId);
   }

   public Long getTLFDOrganizationIdByGroupId(Long groupId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getTLFDOrganizationIdByGroupId(groupId);
   }

   public List getContentIdsByFileId(String fileId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentIdsByFileId(fileId);
   }

   public List getSubGroupList(Long groupId, boolean recursive, Long organizationId) throws SQLException {
      List groupList = new ArrayList();
      List resList = null;
      resList = ((ContentDaoMapper)this.getMapper()).getSubGroupList(groupId, organizationId);
      groupList.addAll(resList);
      if (recursive) {
         Iterator var6 = resList.iterator();

         while(var6.hasNext()) {
            Group group = (Group)var6.next();
            groupList.addAll(this.getSubGroupList(group.getGroup_id(), recursive, organizationId));
         }
      }

      return groupList;
   }

   public Group getTLFDGroupInfo(Long groupId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getTLFDGroupInfo(groupId);
   }

   public List getContentListByFilter(Map map, int startPos, int pageSize) throws SQLException {
      try {
         Map newMap = this.createContentListWithExtraParams(map);
         this.putDeviceTypeVersion(map, newMap);
         --startPos;
         newMap.put("startPos", startPos);
         newMap.put("pageSize", pageSize);
         if (newMap.get("contentFilter") != null && !newMap.get("contentFilter").equals("")) {
            newMap.put("media_type_filter", newMap.get("contentFilter"));
         }

         String tagInputType = null;

         try {
            tagInputType = (String)newMap.get("tagInputType");
         } catch (Exception var13) {
            tagInputType = null;
         }

         List contentIds = new ArrayList();
         String[] tag;
         if (newMap.get("tagFilter") != null && !newMap.get("tagFilter").equals("") && newMap.get("category") != null && !newMap.get("category").equals("")) {
            tag = (String[])((String[])newMap.get("tagFilter"));
            String category = (String)newMap.get("category");
            String[] categoryList = category.split(",");
            List contentIdList = ((ContentDaoMapper)this.getMapper()).getCategorywithTagFilter(categoryList, tag, tagInputType);
            Iterator var22 = contentIdList.iterator();

            while(var22.hasNext()) {
               Map content = (Map)var22.next();
               contentIds.add((String)content.get("content_id"));
            }

            if (contentIds.size() == 0) {
               contentIds.add("-1");
            }

            newMap.put("contentIdList", contentIds);
         } else {
            if (newMap.get("tagFilter") != null && !newMap.get("tagFilter").equals("")) {
               tag = (String[])((String[])newMap.get("tagFilter"));
               List contentIdList = ((ContentDaoMapper)this.getMapper()).getcontentIdfromTag(tag, tagInputType);
               Iterator var9 = contentIdList.iterator();

               while(var9.hasNext()) {
                  Map content = (Map)var9.next();
                  contentIds.add((String)content.get("content_id"));
               }

               if (contentIds.size() == 0) {
                  contentIds.add("-1");
               }

               newMap.put("contentIdList", contentIds);
            }

            if (newMap.get("category") != null && !newMap.get("category").equals("")) {
               String category = (String)newMap.get("category");
               String[] categoryList = category.split(",");
               List contentIdList = ((ContentDaoMapper)this.getMapper()).getcontentIdfromCategory(categoryList);
               Iterator var20 = contentIdList.iterator();

               while(var20.hasNext()) {
                  Map content = (Map)var20.next();
                  contentIds.add((String)content.get("content_id"));
               }

               if (contentIds.size() == 0) {
                  contentIds.add("-1");
               }

               newMap.put("contentIdList", contentIds);
            }
         }

         if (map.get("isTLFD") != null && ((String)map.get("isTLFD")).equals("Y")) {
            newMap.put("isTLFD", "true");
            return ((ContentDaoMapper)this.getMapper()).getTLFDListPage(newMap);
         } else {
            return ((ContentDaoMapper)this.getMapper()).getContentListPage(newMap);
         }
      } catch (Exception var14) {
         this.logger.error(var14);
         return null;
      }
   }

   public int getContentListByFilterCnt(Map map) throws SQLException {
      try {
         Map newMap = this.createContentListWithExtraParams(map);
         this.putDeviceTypeVersion(map, newMap);
         if (newMap.get("contentFilter") != null && !newMap.get("contentFilter").equals("")) {
            newMap.put("media_type_filter", newMap.get("contentFilter"));
         }

         String tagInputType = null;

         try {
            tagInputType = (String)newMap.get("tagInputType");
         } catch (Exception var11) {
         }

         List contentIds = new ArrayList();
         String[] tag;
         if (newMap.get("tagFilter") != null && !newMap.get("tagFilter").equals("") && newMap.get("category") != null && !newMap.get("category").equals("")) {
            tag = (String[])((String[])newMap.get("tagFilter"));
            String category = (String)newMap.get("category");
            String[] categoryList = category.split(",");
            List contentIdList = ((ContentDaoMapper)this.getMapper()).getCategorywithTagFilter(categoryList, tag, tagInputType);
            Iterator var20 = contentIdList.iterator();

            while(var20.hasNext()) {
               Map content = (Map)var20.next();
               contentIds.add((String)content.get("content_id"));
            }

            if (contentIds.size() == 0) {
               contentIds.add("-1");
            }

            newMap.put("contentIdList", contentIds);
         } else {
            if (newMap.get("tagFilter") != null && !newMap.get("tagFilter").equals("")) {
               tag = (String[])((String[])newMap.get("tagFilter"));
               List contentIdList = ((ContentDaoMapper)this.getMapper()).getcontentIdfromTag(tag, tagInputType);
               Iterator var7 = contentIdList.iterator();

               while(var7.hasNext()) {
                  Map content = (Map)var7.next();
                  contentIds.add((String)content.get("content_id"));
               }

               if (contentIds.size() == 0) {
                  contentIds.add("-1");
               }

               newMap.put("contentIdList", contentIds);
            }

            if (newMap.get("category") != null && !newMap.get("category").equals("")) {
               String category = (String)newMap.get("category");
               String[] categoryList = category.split(",");
               List contentIdList = ((ContentDaoMapper)this.getMapper()).getcontentIdfromCategory(categoryList);
               Iterator var18 = contentIdList.iterator();

               while(var18.hasNext()) {
                  Map content = (Map)var18.next();
                  contentIds.add((String)content.get("content_id"));
               }

               if (contentIds.size() == 0) {
                  contentIds.add("-1");
               }

               newMap.put("contentIdList", contentIds);
            }
         }

         if (map.get("isTLFD") != null && ((String)map.get("isTLFD")).equals("Y")) {
            newMap.put("isTLFD", "true");
            return ((ContentDaoMapper)this.getMapper()).getTLFDListCnt(newMap);
         } else {
            return ((ContentDaoMapper)this.getMapper()).getContentListCnt(newMap);
         }
      } catch (Exception var12) {
         this.logger.error(var12);
         return 0;
      }
   }

   public int V2GetUnapprovedContentCnt() throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).V2GetUnapprovedContentCnt();
   }

   public int V2GetUnapprovedContentCnt(Long organizationId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).V2GetUnapprovedContentCnt(organizationId);
   }

   public int getAllContentCount() throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getAllContentCount();
   }

   public List getContentCountByContentType() throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentCountByContentType();
   }

   public List getContentCountByContentResolution() throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentCountByContentResolution();
   }

   public int getContentGroupTotalCount() throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentGroupTotalCount();
   }

   public List getValueIdsByContentIdAndIndexId(String contentId, String indexId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getValueIdsByContentIdAndIndexId(contentId, indexId);
   }

   public void addProductCodeHistory(SqlSession session, String contentId) throws SQLException {
      List productCodeValueList = ((ContentDaoMapper)this.getMapper(session)).getAssignedAdvertisementByContentId(contentId, "PRODUCT_CODE");
      String historyId = UUID.randomUUID().toString();
      ((ContentDaoMapper)this.getMapper(session)).addProductCodeHistory(contentId, historyId);
      Iterator var5 = productCodeValueList.iterator();

      while(var5.hasNext()) {
         InsightIndexValueWithLastModifiedDateEntity productCodeValueEntity = (InsightIndexValueWithLastModifiedDateEntity)var5.next();
         Long valueId = productCodeValueEntity.getValue_id();
         String value = productCodeValueEntity.getValue();
         ((ContentDaoMapper)this.getMapper(session)).addProductCodeHistoryValue(historyId, valueId, value);
      }

   }

   public boolean setAdvertisement(String contentId, String indexId, List assignValueIds, List removeValueIds) {
      SqlSession session = this.openNewSession(false);

      try {
         Iterator var6 = assignValueIds.iterator();

         Long valueId;
         while(var6.hasNext()) {
            valueId = (Long)var6.next();
            ((ContentDaoMapper)this.getMapper(session)).assignAdvertisement(contentId, indexId, valueId);
         }

         var6 = removeValueIds.iterator();

         while(var6.hasNext()) {
            valueId = (Long)var6.next();
            ((ContentDaoMapper)this.getMapper(session)).removeAssignedAdvertisement(contentId, indexId, valueId);
         }

         if (StringUtils.equals(indexId, "PRODUCT_CODE")) {
            this.addProductCodeHistory(session, contentId);
         }

         session.commit();
         boolean var13 = true;
         return var13;
      } catch (Exception var11) {
         this.logger.error(var11.getMessage());
         session.rollback();
      } finally {
         session.close();
      }

      return false;
   }

   public List getAssignedAdvertisementByContentId(String contentId, String type) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getAssignedAdvertisementByContentId(contentId, type);
   }

   public List getContentIdListByMappedValueId(SqlSession session, Long valueId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper(session)).getContentIdListByMappedValueId(valueId);
   }

   public int deleteProductCodeHistoryByDate(String date) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).deleteProductCodeHistoryByDate(date);
   }

   public List getContentProductCodeHistory(String contentId) throws SQLException {
      List list = ((ContentDaoMapper)this.getMapper()).getProductCodeHistoryByContentId(contentId);
      Iterator var3 = list.iterator();

      while(var3.hasNext()) {
         ContentProductCodeHistoryEntity history = (ContentProductCodeHistoryEntity)var3.next();
         List values = ((ContentDaoMapper)this.getMapper()).getProductCodeHistoryValueListByHistoryId(history.getHistory_id());
         history.setValues(values);
      }

      return list;
   }

   public int removeAssignedAdvertisement(String contentId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).removeAssignedAdvertisement(contentId, (String)null, (Long)null);
   }

   public int getContentFileCount(String type) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getContentFileCount(type);
   }

   public List getUnapprovedContentIdList(Long orgId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getUnapprovedContentIdList(orgId);
   }

   public List getAdsContentPublisherInfoSuggestionListByUser(String userId, int count) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getAdsContentPublisherInfoSuggestionListByUser(userId, count);
   }

   public List getAdsContentAdUnitIdSuggestionListByUser(String userId, int count) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getAdsContentAdUnitIdSuggestionListByUser(userId, count);
   }

   public int existPublisherInfoByPublisherId(String publisherId, String userId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).existPublisherInfoByPublisherId(publisherId, userId);
   }

   public int existAdUnitIdById(String adUnitId, String userId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).existAdUnitIdById(adUnitId, userId);
   }

   public boolean updateAdsContentPublisherSuggestionInfo(V2AdsContentPublisherInfo publisherInfo, String userId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).updateAdsContentPublisherSuggestionInfo(publisherInfo, userId);
   }

   public boolean updateAdsContentAdUnitIdSuggestionInfo(String adUnitId, String userId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).updateAdsContentAdUnitIdSuggestionInfo(adUnitId, userId);
   }

   public int addAdsContentPublisherInfoSuggestion(V2AdsContentPublisherInfo publisherInfo, String userId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).addAdsContentPublisherInfoSuggestion(publisherInfo, userId);
   }

   public int addAdsContentAdUnitIdSuggestion(String adUnitId, String userId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).addAdsContentAdUnitIdSuggestion(adUnitId, userId);
   }

   public V2AdsContentPublisherInfo getPublisherInfoById(String publisherId, String userId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getPublisherInfoById(publisherId, userId);
   }

   public String getAdUnitIdById(String adUnitId, String userId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).getAdUnitIdById(adUnitId, userId);
   }

   public int deletePublisherInfoById(String publisherId, String userId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).deletePublisherInfoById(publisherId, userId);
   }

   public int deleteAdUnitIdInfoById(String adUnitId, String userId) throws SQLException {
      return ((ContentDaoMapper)this.getMapper()).deleteAdUnitIdInfoById(adUnitId, userId);
   }
}
