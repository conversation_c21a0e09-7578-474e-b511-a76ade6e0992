package com.samsung.magicinfo.protocol.compiler;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import java.io.FileWriter;
import java.io.IOException;
import org.apache.logging.log4j.Logger;

public class ConfigFileGenerator {
   private static Logger logger = LoggingManagerV2.getLogger(ConfigFileGenerator.class);
   private static final String szMOTreeProperty = "MOCompiler.MOTreeFileName";
   private static final String szMOConstProperty = "MOCompiler.MONodeConstraintsFileName";
   private static final String szPropertySep = "=";
   private static final String szNewLine = "\r\n";

   public ConfigFileGenerator() {
      super();
   }

   public static boolean writePathInfoToConfigFile(String szFileName, String szMOTreeFilePath, String szMOConstFilePath) {
      FileWriter writer = null;

      boolean var4;
      try {
         if (szMOTreeFilePath != null && szMOTreeFilePath.length() >= 1) {
            if (szMOConstFilePath != null && szMOConstFilePath.length() >= 1) {
               writer = new FileWriter(SecurityUtils.directoryTraversalChecker(szFileName, (String)null));
               writer.write("MOCompiler.MOTreeFileName=" + szMOTreeFilePath + "\r\n");
               writer.write("MOCompiler.MONodeConstraintsFileName=" + szMOConstFilePath + "\r\n");
               return true;
            }

            var4 = false;
            return var4;
         }

         var4 = false;
      } catch (Exception var16) {
         logger.error("", var16);
         return true;
      } finally {
         try {
            if (writer != null) {
               writer.close();
            }
         } catch (IOException var15) {
            logger.error("", var15);
         }

      }

      return var4;
   }
}
