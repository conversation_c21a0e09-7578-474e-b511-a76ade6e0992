package com.samsung.magicinfo.webauthor2.model.weather;

import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "Languages")
@XmlAccessorType(XmlAccessType.FIELD)
public class Languages {
  @XmlElement(name = "Language")
  private List<Language> languages = null;
  
  public List<Language> getLanguages() {
    return this.languages;
  }
  
  public void setLanguages(List<Language> languages) {
    this.languages = languages;
  }
}
