package com.samsung.magicinfo.cms.model;

import io.swagger.annotations.ApiModel;

@ApiModel
public class DashboardPlaylistResource {
   public int totalCount;
   public int usedCount;

   public DashboardPlaylistResource() {
      super();
   }

   public int getTotalCount() {
      return this.totalCount;
   }

   public void setTotalCount(int totalCount) {
      this.totalCount = totalCount;
   }

   public int getUsedCount() {
      return this.usedCount;
   }

   public void setUsedCount(int usedCount) {
      this.usedCount = usedCount;
   }
}
