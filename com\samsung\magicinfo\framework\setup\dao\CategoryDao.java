package com.samsung.magicinfo.framework.setup.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SequenceDB;
import com.samsung.magicinfo.framework.setup.entity.CategoryEntity;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.logging.log4j.Logger;

public class CategoryDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(CategoryDao.class);

   public CategoryDao() {
      super();
   }

   public List getCategoryWithPgroupId(long groupId) throws SQLException {
      return ((CategoryDaoMapper)this.getMapper()).getCategoryWithPgroupId(groupId);
   }

   public List getCategoryWithPgroupId(long groupId, String organization) throws SQLException {
      return ((CategoryDaoMapper)this.getMapper()).getCategoryWithPgroupIdOrganization(groupId, organization);
   }

   public long addCategory(CategoryEntity category) throws SQLException {
      long group_id = (long)SequenceDB.getNextValue("MI_CATEGORY_INFO_GROUP");

      try {
         category.setGroup_id(group_id);
         return ((CategoryDaoMapper)this.getMapper()).addCategory(category) ? group_id : -1L;
      } catch (Exception var5) {
         this.logger.error("[MagicInfo_Category] fail add a category! e : " + var5.getMessage());
         return -1L;
      }
   }

   public boolean deleteCategory(long groupId) throws SQLException {
      return ((CategoryDaoMapper)this.getMapper()).deleteCategory(groupId);
   }

   public CategoryEntity getCategory(long groupId) throws SQLException {
      return ((CategoryDaoMapper)this.getMapper()).getCategory(groupId);
   }

   public List getCategoryWithContentId(String contentId) throws SQLException {
      return ((CategoryDaoMapper)this.getMapper()).getCategoryWithContentId(contentId);
   }

   public Map getCategoryTag(String contentId) throws SQLException {
      List categoryList = ((CategoryDaoMapper)this.getMapper()).getCategoryList(contentId);
      List tagList = ((CategoryDaoMapper)this.getMapper()).getTagList(contentId);
      Map results = new HashMap();
      if (categoryList.size() > 0) {
         results.put("contentName", ((Map)categoryList.get(0)).get("content_name"));
      }

      results.put("category", categoryList);
      results.put("tag", tagList);
      return results;
   }

   public void setCategoryFromContentId(String category, String contentId) throws SQLException {
      if (category != null) {
         String[] categoryList = category.split(",");
         String[] var4 = categoryList;
         int var5 = categoryList.length;

         for(int var6 = 0; var6 < var5; ++var6) {
            String categoryStr = var4[var6];
            if (categoryStr != null && !categoryStr.equals("")) {
               ((CategoryDaoMapper)this.getMapper()).setCategoryFromContentId(Long.valueOf(categoryStr), contentId);
            }
         }
      }

   }

   public void setCategoryFromPlaylistId(String category, String contentId) throws SQLException {
      if (category != null) {
         String[] categoryList = category.split(",");
         String[] var4 = categoryList;
         int var5 = categoryList.length;

         for(int var6 = 0; var6 < var5; ++var6) {
            String categoryStr = var4[var6];
            ((CategoryDaoMapper)this.getMapper()).setCategoryFromPlaylistId(Long.valueOf(categoryStr), contentId);
         }
      }

   }

   public void setCategoryFromPlaylistId(List categoryList, String contentId) throws SQLException {
      Iterator var3 = categoryList.iterator();

      while(var3.hasNext()) {
         Long category = (Long)var3.next();
         ((CategoryDaoMapper)this.getMapper()).setCategoryFromPlaylistId(category, contentId);
      }

   }

   public boolean deleteCategoryFromContentId(String contentId) throws SQLException {
      return ((CategoryDaoMapper)this.getMapper()).deleteCategoryFromContentId(contentId);
   }

   public boolean deleteCategoryFromPlaylistId(String playlistId) throws SQLException {
      return ((CategoryDaoMapper)this.getMapper()).deleteCategoryFromPlaylistId(playlistId);
   }

   public boolean moveCategory(long parentId, long groupId) throws SQLException {
      return ((CategoryDaoMapper)this.getMapper()).moveCategory(parentId, groupId);
   }

   public boolean updateCategory(CategoryEntity category) throws SQLException {
      return ((CategoryDaoMapper)this.getMapper()).updateCategory(category);
   }

   public List getCategoryWithPlaylistId(String playlistId) throws SQLException {
      return ((CategoryDaoMapper)this.getMapper()).getCategoryWithPlaylistId(playlistId);
   }

   public List getCategoryByMultipleOrg(List userList) throws SQLException {
      return ((CategoryDaoMapper)this.getMapper()).getCategoryByMultipleOrg(userList);
   }
}
