package com.samsung.magicinfo.framework.scheduler.dao;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.db.TypeFilterPagedListInfo;
import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.SequenceDB;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.monitoring.dao.DownloadStatusDAO;
import com.samsung.magicinfo.framework.monitoring.manager.DownloadStatusInfo;
import com.samsung.magicinfo.framework.monitoring.manager.DownloadStatusInfoImpl;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramGroup;
import com.samsung.magicinfo.framework.scheduler.entity.ScheduleAdminEntity;
import com.samsung.magicinfo.framework.scheduler.entity.SelectConditionScheduleAdmin;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.user.entity.UserGroup;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.CaseInsensitiveMap;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;
import org.springframework.util.StringUtils;

public class ScheduleAdminDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(ScheduleAdminDao.class);

   public ScheduleAdminDao() {
      super();
   }

   public ScheduleAdminDao(SqlSession session) {
      super(session);
   }

   public boolean checkProgGroupChange(String strProgIds, String strNewGroup, SqlSession session) throws SQLException {
      String newRootGroup = this.getProgramGroupRoot(Integer.valueOf(strNewGroup), session);
      if (StringUtils.hasLength(strProgIds)) {
         String[] ids = strProgIds.split(",");

         for(int i = 0; i < ids.length; ++i) {
            String strProg = ids[i];
            int iOldGroup = this.getProgramGroupById(strProg, session).intValue();
            String oldRootGroup = this.getProgramGroupRoot(iOldGroup, session);
            if (!oldRootGroup.equalsIgnoreCase(newRootGroup)) {
               return false;
            }

            ++i;
         }
      }

      return true;
   }

   public boolean checkProgGroupChange(String strProgIds, String strNewGroup) throws SQLException {
      return this.checkProgGroupChange(strProgIds, strNewGroup, (SqlSession)null);
   }

   public boolean recoverSchedule(String strProgId, String progGrpId) throws SQLException {
      Long groupId = Long.parseLong(progGrpId);
      SqlSession session = this.openNewSession(false);

      boolean var6;
      try {
         ScheduleAdminDaoMapper mapper = (ScheduleAdminDaoMapper)this.getMapper(session);
         if (!mapper.recoverScheduleMarkUndeleted(strProgId)) {
            session.rollback();
            var6 = false;
            return var6;
         }

         mapper.recoverScheduleDelete(strProgId);
         if (!mapper.recoverScheduleUpdateGroup(strProgId, groupId)) {
            session.rollback();
            var6 = false;
            return var6;
         }

         session.commit();
         var6 = true;
         return var6;
      } catch (Exception var10) {
         session.rollback();
         var6 = false;
      } finally {
         session.close();
      }

      return var6;
   }

   public boolean deleteSchedule(String strProgId, String userId) throws SQLException {
      if (StringUtils.hasLength(strProgId)) {
         List programs = new ArrayList();
         ScheduleInfoDAO scheduleInfoDao = new ScheduleInfoDAO();
         String[] ids = strProgId.split(":");
         SqlSession session = this.openNewSession(false);
         ScheduleAdminDaoMapper mapper = (ScheduleAdminDaoMapper)this.getMapper(session);
         DownloadStatusInfo downloadDao = DownloadStatusInfoImpl.getInstacne();
         String logType = "";

         try {
            boolean updated;
            try {
               for(int i = 0; i < ids.length; ++i) {
                  if (StringUtils.hasLength(ids[i])) {
                     if ("N".equalsIgnoreCase(mapper.getDeletedStatus(ids[i]))) {
                        updated = mapper.markProgramInfoDeleted(ids[i]);
                        List groups = mapper.getDeviceGroupInfo(ids[i]);
                        if (CollectionUtils.isNotEmpty(groups)) {
                           for(int index = 0; index < groups.size(); ++index) {
                              String programId = (String)((Map)groups.get(index)).get("DEFAULT_PROGRAM_ID");
                              Long devGroupId = (Long)((Map)groups.get(index)).get("DEVICE_GROUP_ID");
                              boolean success = mapper.updateProgramId(ids[i], programId, devGroupId);
                              if (!success) {
                                 session.rollback();
                                 boolean var33 = false;
                                 return var33;
                              }

                              ProgramEntity defaultProg = scheduleInfoDao.getProgram(programId, session);
                              defaultProg.setDevice_group_ids(String.valueOf(devGroupId));
                              programs.add(defaultProg);
                           }
                        }

                        if (!updated) {
                           session.rollback();
                           boolean var29 = false;
                           return var29;
                        }

                        Long groupId = mapper.getGroupId(ids[i]);
                        if (groupId != null) {
                           ProgramGroupDao groupDao = new ProgramGroupDao();
                           int orgGroupId = groupDao.getProgramOrgGroupId(groupId.intValue());
                           mapper.updateGroupId(ids[i], orgGroupId);
                        }

                        long logId = (long)SequenceDB.getNextValue("MI_CDS_LOG_PROGRAM");
                        scheduleInfoDao.getProgramName(ids[i]);
                        logType = "02";
                     } else {
                        List scheduleIdList = mapper.getScheduleIdListByProgramId(ids[i]);
                        if (scheduleIdList != null) {
                           for(int idx = 0; idx < scheduleIdList.size(); ++idx) {
                              String scheduleId = (String)scheduleIdList.get(idx);
                              scheduleInfoDao.deleteDynaminTagInfo(scheduleId);
                           }
                        }

                        mapper.deleteProgramGroup(ids[i]);
                        mapper.deleteProgramDevice(ids[i]);
                        if (!mapper.deleteProgramInfo(ids[i])) {
                           session.rollback();
                           boolean var25 = false;
                           return var25;
                        }

                        mapper.deleteScheduleInfo(ids[i]);
                        mapper.deleteFrameInfo(ids[i]);
                        mapper.deleteChannel(ids[i]);
                        mapper.deleteADScheduleInfo(ids[i]);
                        mapper.deleteADSlotInfo(ids[i]);
                        logType = "04";
                        CommonUtils.deleteScheduleJob(ids[i]);
                     }

                     downloadDao.deleteDownloadStatusByProgramId(ids[i]);
                  }
               }

               boolean deployed;
               if (!CollectionUtils.isNotEmpty(programs)) {
                  session.commit();
                  deployed = true;
                  return deployed;
               } else {
                  deployed = this.deployDefaultPrograms(programs, session);
                  if (deployed) {
                     session.commit();
                     updated = true;
                     return updated;
                  } else {
                     session.rollback();
                     updated = false;
                     return updated;
                  }
               }
            } catch (Exception var21) {
               session.rollback();
               updated = false;
               return updated;
            }
         } finally {
            session.close();
         }
      } else {
         return false;
      }
   }

   public boolean deleteSchedule(String strProgId, String userId, String ipAddress) throws SQLException {
      if (StringUtils.hasLength(strProgId)) {
         List programs = new ArrayList();
         String[] ids = strProgId.split(":");
         SqlSession session = this.openNewSession(false);
         ScheduleInfoDAO scheduleInfoDao = new ScheduleInfoDAO(session);
         ScheduleAdminDaoMapper mapper = (ScheduleAdminDaoMapper)this.getMapper(session);
         new DownloadStatusDAO(session);
         String var10 = "";

         label154: {
            boolean updated;
            try {
               for(int i = 0; i < ids.length; ++i) {
                  if (StringUtils.hasLength(ids[i]) && "N".equalsIgnoreCase(mapper.getDeletedStatus(ids[i]))) {
                     updated = mapper.markProgramInfoDeleted(ids[i]);
                     List groups = mapper.getDeviceGroupInfo(ids[i]);
                     if (CollectionUtils.isNotEmpty(groups)) {
                        for(int index = 0; index < groups.size(); ++index) {
                           String programId = (String)((Map)groups.get(index)).get("DEFAULT_PROGRAM_ID");
                           Long devGroupId = (Long)((Map)groups.get(index)).get("DEVICE_GROUP_ID");
                           boolean success = mapper.updateProgramId(ids[i], programId, devGroupId);
                           if (!success) {
                              session.rollback();
                              boolean var28 = false;
                              return var28;
                           }

                           ProgramEntity defaultProg = scheduleInfoDao.getProgram(programId, session);
                           defaultProg.setDevice_group_ids(String.valueOf(devGroupId));
                           programs.add(defaultProg);
                        }

                        mapper.deleteDownloadStatus(groups);
                     }

                     mapper.deleteProgramStatus(ids[i]);
                     if (!updated) {
                        session.rollback();
                        boolean var25 = false;
                        return var25;
                     }

                     Long groupId = mapper.getGroupId(ids[i]);
                     if (groupId != null) {
                        ProgramGroupDao groupDao = new ProgramGroupDao();
                        int orgGroupId = groupDao.getProgramOrgGroupId(groupId.intValue());
                        mapper.updateGroupId(ids[i], orgGroupId);
                     }
                  }
               }

               session.commit();
               break label154;
            } catch (Exception var22) {
               this.logger.error("", var22);
               session.rollback();
               updated = false;
            } finally {
               session.close();
            }

            return updated;
         }

         if (CollectionUtils.isNotEmpty(programs)) {
            this.deployDefaultPrograms(programs);
         }

         return true;
      } else {
         return false;
      }
   }

   public boolean deleteSchedulePerm(String strProgId, String userId, String ipAddress) throws SQLException {
      if (StringUtils.hasLength(strProgId)) {
         String[] ids = strProgId.split(":");
         List programs = new ArrayList();
         SqlSession session = this.openNewSession(false);
         ScheduleInfoDAO scheduleInfoDao = new ScheduleInfoDAO(session);
         DownloadStatusDAO downloadStatusDAO = new DownloadStatusDAO(session);
         ScheduleAdminDaoMapper mapper = (ScheduleAdminDaoMapper)this.getMapper(session);

         boolean var21;
         try {
            for(int i = 0; i < ids.length; ++i) {
               if (StringUtils.hasLength(ids[i])) {
                  int idx;
                  String scheduleId;
                  List scheduleIdList;
                  if ("N".equalsIgnoreCase(mapper.getDeletedStatus(ids[i]))) {
                     scheduleIdList = mapper.getDeviceGroupInfo(ids[i]);
                     if (CollectionUtils.isNotEmpty(scheduleIdList)) {
                        for(idx = 0; idx < scheduleIdList.size(); ++idx) {
                           scheduleId = (String)((Map)scheduleIdList.get(idx)).get("DEFAULT_PROGRAM_ID");
                           Long devGroupId = (Long)((Map)scheduleIdList.get(idx)).get("DEVICE_GROUP_ID");
                           mapper.updateProgramId(ids[i], scheduleId, devGroupId);
                           ProgramEntity defaultProg = scheduleInfoDao.getProgram(scheduleId, session);
                           defaultProg.setDevice_group_ids(String.valueOf(devGroupId));
                           programs.add(defaultProg);
                        }
                     }
                  }

                  scheduleIdList = mapper.getScheduleIdListByProgramId(ids[i]);
                  if (scheduleIdList != null) {
                     for(idx = 0; idx < scheduleIdList.size(); ++idx) {
                        scheduleId = (String)scheduleIdList.get(idx);
                        scheduleInfoDao.deleteDynaminTagInfo(scheduleId);
                     }
                  }

                  mapper.deleteProgramGroup(ids[i]);
                  mapper.deleteProgramDevice(ids[i]);
                  if (!mapper.deleteProgramInfo(ids[i])) {
                     session.rollback();
                     boolean var23 = false;
                     return var23;
                  }

                  mapper.deleteScheduleInfo(ids[i]);
                  mapper.deleteFrameInfo(ids[i]);
                  mapper.deleteChannel(ids[i]);
                  mapper.deleteADScheduleInfo(ids[i]);
                  mapper.deleteADSlotInfo(ids[i]);
                  CommonUtils.deleteScheduleJob(ids[i]);
                  downloadStatusDAO.deleteDownloadStatusByProgramId(ids[i]);
               }
            }

            if (!CollectionUtils.isNotEmpty(programs)) {
               session.commit();
               var21 = true;
               return var21;
            }

            if (this.deployDefaultPrograms(programs, session)) {
               session.commit();
               var21 = true;
               return var21;
            }

            session.rollback();
            var21 = false;
         } catch (Exception var19) {
            session.rollback();
            boolean var11 = false;
            return var11;
         } finally {
            session.close();
         }

         return var21;
      } else {
         return false;
      }
   }

   public boolean deployDefaultPrograms(List programs) throws SQLException {
      return this.deployDefaultPrograms(programs, (SqlSession)null);
   }

   public boolean deployDefaultPrograms(List programs, SqlSession session) throws SQLException {
      boolean result = true;
      ScheduleInfo schedule;
      if (session == null) {
         schedule = ScheduleInfoImpl.getInstance();
      } else {
         schedule = ScheduleInfoImpl.getInstance(session);
      }

      int length = programs.size();

      try {
         for(int index = 0; index < length; ++index) {
            ProgramEntity defaultProg = (ProgramEntity)programs.get(index);
            boolean indvResult = schedule.reserveSchedule(defaultProg, session);
            if (!indvResult) {
               result = false;
            }
         }

         return result;
      } catch (Exception var9) {
         return false;
      }
   }

   public boolean deleteAllSchedule(String groupId, String userOrg, String userId, String ipAddress) throws SQLException {
      ScheduleInfoDAO scheduleInfoDao = new ScheduleInfoDAO();
      SqlSession session = this.openNewSession(false);

      try {
         List programs = new ArrayList();
         List programIds = this.getAllProgramIdsForGroup(groupId, userOrg, session);
         ScheduleAdminDaoMapper mapper = (ScheduleAdminDaoMapper)this.getMapper(session);
         String logType = "";

         for(int i = 0; i < programIds.size(); ++i) {
            String programId = (String)((Map)programIds.get(i)).get("PROGRAM_ID");
            if (StringUtils.hasLength(programId)) {
               boolean updated;
               if ("N".equalsIgnoreCase(mapper.getDeletedStatus(programId))) {
                  updated = mapper.markProgramInfoDeleted(programId);
                  List groups = mapper.getDeviceGroupInfo(programId);
                  if (CollectionUtils.isNotEmpty(groups)) {
                     for(int index = 0; index < groups.size(); ++index) {
                        Long devGroup = (Long)((Map)groups.get(index)).get("DEVICE_GROUP_ID");
                        String progId = (String)((Map)groups.get(index)).get("DEFAULT_PROGRAM_ID");
                        if (!mapper.updateProgramId(programId, progId, devGroup)) {
                           session.rollback();
                           boolean var39 = false;
                           return var39;
                        }

                        ProgramEntity defaultProgram = scheduleInfoDao.getProgram(progId, session);
                        defaultProgram.setDevice_group_ids(String.valueOf(devGroup));
                        programs.add(defaultProgram);
                     }
                  }

                  if (!updated) {
                     session.rollback();
                     boolean var35 = false;
                     return var35;
                  }

                  Long newGroupId = mapper.getGroupId(programId);
                  if (newGroupId != null) {
                     ProgramGroupDao groupDao = new ProgramGroupDao();
                     int orgGroupId = groupDao.getProgramOrgGroupId(newGroupId.intValue());
                     mapper.updateGroupId(programId, orgGroupId);
                  }

                  logType = "02";
               } else {
                  mapper.deleteProgramGroup(programId);
                  mapper.deleteProgramDevice(programId);
                  if (!mapper.deleteProgramInfo(programId)) {
                     session.rollback();
                     updated = false;
                     return updated;
                  }

                  mapper.deleteScheduleInfo(programId);
                  mapper.deleteFrameInfo(programId);
                  mapper.deleteChannel(programId);
                  List scheduleIdList = mapper.getScheduleIdListByProgramId(programId);
                  if (scheduleIdList != null) {
                     for(int idx = 0; idx < scheduleIdList.size(); ++idx) {
                        String scheduleId = (String)scheduleIdList.get(idx);
                        scheduleInfoDao.deleteDynaminTagInfo(scheduleId);
                     }
                  }

                  logType = "04";
                  CommonUtils.deleteScheduleJob(programId);
               }
            }
         }

         session.commit();
         if (!CollectionUtils.isNotEmpty(programs)) {
            return true;
         } else {
            boolean var31 = this.deployDefaultPrograms(programs, session);
            return var31;
         }
      } catch (SQLException var27) {
         try {
            session.rollback();
         } catch (Exception var26) {
         }

         boolean var8 = false;
         return var8;
      } catch (Exception var28) {
         try {
            session.rollback();
         } catch (Exception var25) {
         }

         throw var28;
      } finally {
         session.close();
      }
   }

   public boolean deleteAllSchedulePerm(String groupId, String userOrg, String userId, String ipAddress) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var7;
      try {
         List programs = new ArrayList();
         ScheduleInfoDAO scheduleInfoDao = new ScheduleInfoDAO();
         List programIds = this.getAllProgramIdsForGroup(groupId, userOrg, session);
         ScheduleAdminDaoMapper mapper = (ScheduleAdminDaoMapper)this.getMapper(session);

         for(int i = 0; i < programIds.size(); ++i) {
            String programId = (String)((Map)programIds.get(i)).get("PROGRAM_ID");
            if (StringUtils.hasLength(programId)) {
               if ("N".equalsIgnoreCase(mapper.getDeletedStatus(programId))) {
                  List groups = mapper.getDeviceGroupInfo(programId);
                  if (CollectionUtils.isNotEmpty(groups)) {
                     for(int index = 0; index < groups.size(); ++index) {
                        Long devGroup = (Long)((Map)groups.get(index)).get("DEVICE_GROUP_ID");
                        String progId = (String)((Map)groups.get(index)).get("DEFAULT_PROGRAM_ID");
                        if (!mapper.updateProgramId(programId, progId, devGroup)) {
                           session.rollback();
                           boolean var31 = false;
                           return var31;
                        }

                        ProgramEntity defaultProgram = scheduleInfoDao.getProgram(progId, session);
                        defaultProgram.setDevice_group_ids(String.valueOf(devGroup));
                        programs.add(defaultProgram);
                     }
                  }
               }

               mapper.deleteProgramGroup(programId);
               mapper.deleteProgramDevice(programId);
               if (!mapper.deleteProgramInfo(programId)) {
                  session.rollback();
                  boolean var30 = false;
                  return var30;
               }

               mapper.deleteScheduleInfo(programId);
               mapper.deleteFrameInfo(programId);
               mapper.deleteChannel(programId);
               CommonUtils.deleteScheduleJob(programId);
            }
         }

         session.commit();
         if (CollectionUtils.isNotEmpty(programs)) {
            boolean var29 = this.deployDefaultPrograms(programs, session);
            return var29;
         }

         return true;
      } catch (SQLException var25) {
         try {
            session.rollback();
         } catch (Exception var23) {
         }

         var7 = false;
      } catch (Exception var26) {
         try {
            session.rollback();
         } catch (Exception var24) {
         }

         throw var26;
      } finally {
         session.close();
      }

      return var7;
   }

   public List getAllProgramIdsForGroup(String groupId, String rootGrpOrg, SqlSession session) throws SQLException {
      Map parameters = new HashMap();
      if (StringUtils.hasLength(groupId)) {
         if (!"trash".equalsIgnoreCase(groupId) && !"all".equalsIgnoreCase(groupId)) {
            parameters.put("deleted", "N");
            parameters.put("groupId", Long.valueOf(groupId));
         } else {
            if ("trash".equalsIgnoreCase(groupId)) {
               parameters.put("deleted", "Y");
            } else if ("all".equalsIgnoreCase(groupId)) {
               parameters.put("deleted", "N");
            }

            int group_id = this.getProgramGroupForUser(rootGrpOrg, session);
            if (group_id != -1) {
               List groupIds = this.getChildGroupIdList(group_id, true);
               groupIds.add((long)group_id);
               parameters.put("groupIds", groupIds);
            }
         }
      }

      ScheduleAdminDaoMapper mapper = session != null ? (ScheduleAdminDaoMapper)this.getMapper(session) : (ScheduleAdminDaoMapper)this.getMapper();
      return mapper.getAllProgramIdsForGroup(parameters);
   }

   public List getAllProgramIdsForGroup(String groupId, String rootGrpOrg) throws SQLException {
      return this.getAllProgramIdsForGroup(groupId, rootGrpOrg, (SqlSession)null);
   }

   public boolean updateProgramGroup(String strGroupId, String strProgId, String userId, String ipAddress) throws SQLException {
      if (!StringUtils.hasLength(strProgId)) {
         return false;
      } else {
         SqlSession session = this.openNewSession(false);

         try {
            String[] ids = strProgId.split(",");
            ScheduleAdminDaoMapper mapper = (ScheduleAdminDaoMapper)this.getMapper(session);
            ScheduleInfoDAO scheduleInfoDao = new ScheduleInfoDAO();
            List programs = new ArrayList();

            for(int i = 0; i < ids.length; ++i) {
               if (StringUtils.hasLength(ids[i])) {
                  boolean compatible = this.checkProgGroupChange(ids[i], strGroupId, session);
                  if (!mapper.updateGroupId(ids[i], Integer.parseInt(strGroupId))) {
                     session.rollback();
                     boolean var24 = false;
                     return var24;
                  }

                  if (!compatible) {
                     List groups = mapper.getDeviceGroupInfo(ids[i]);
                     mapper.deleteProgramDevice(ids[i]);
                     if (CollectionUtils.isNotEmpty(groups)) {
                        for(int idx = 0; idx < groups.size(); ++idx) {
                           Long deviceGroupId = (Long)((Map)groups.get(idx)).get("DEVICE_GROUP_ID");
                           String defaultProgramId = (String)((Map)groups.get(idx)).get("DEFAULT_PROGRAM_ID");
                           if (!mapper.insertDefaultMapping(defaultProgramId, deviceGroupId.intValue())) {
                              session.rollback();
                              boolean var27 = false;
                              return var27;
                           }

                           ProgramEntity defaultProg = scheduleInfoDao.getProgram(defaultProgramId, session);
                           defaultProg.setDevice_group_ids(String.valueOf(deviceGroupId));
                           programs.add(defaultProg);
                        }
                     }

                     mapper.deleteScheduleInfo(ids[i]);
                     if (CollectionUtils.isNotEmpty(programs)) {
                        boolean deployed = this.deployDefaultPrograms(programs, session);
                        boolean var26;
                        if (deployed) {
                           session.commit();
                           var26 = true;
                           return var26;
                        }

                        session.rollback();
                        var26 = false;
                        return var26;
                     }
                  }
               }
            }

            session.commit();
            boolean var23 = true;
            return var23;
         } catch (Exception var20) {
            session.rollback();
            boolean var7 = false;
            return var7;
         } finally {
            session.close();
         }
      }
   }

   public boolean updateDeviceGroup(String groupId, String programId, String userId, String ipAddress) throws SQLException {
      SqlSession session = this.openNewSession(true);

      boolean var18;
      try {
         boolean devMapping = this.checkDeviceMapping(programId, groupId, session);
         ScheduleAdminDaoMapper mapper = (ScheduleAdminDaoMapper)this.getMapper(session);
         new ScheduleInfoDAO();
         mapper.deleteProgramDevice(programId);
         String[] ids = groupId.split(",");

         for(int i = 0; i < ids.length; ++i) {
            if (StringUtils.hasLength(ids[i])) {
               mapper.deleteOtherProgramDevice(Integer.valueOf(ids[i]));
               if (!mapper.insertDefaultMapping(programId, Integer.valueOf(ids[i]))) {
                  session.rollback();
                  boolean var11 = false;
                  return var11;
               }
            }
         }

         if (devMapping) {
            mapper.updateProgramModifyDate(programId);
            session.commit();
            var18 = true;
            return var18;
         }

         session.rollback();
         var18 = false;
      } catch (Exception var15) {
         session.rollback();
         boolean var7 = false;
         return var7;
      } finally {
         session.close();
      }

      return var18;
   }

   public boolean updateModifyDate(String programId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var4;
      try {
         ScheduleAdminDaoMapper mapper = (ScheduleAdminDaoMapper)this.getMapper(session);
         mapper.updateProgramModifyDate(programId);
         session.commit();
         var4 = true;
         return var4;
      } catch (Exception var8) {
         session.rollback();
         var4 = false;
      } finally {
         session.close();
      }

      return var4;
   }

   public boolean checkDeviceMapping(String programId, String deviceGroupIds, SqlSession session) throws SQLException {
      boolean result = true;
      if (deviceGroupIds != null) {
         ScheduleInfoDAO scheduleInfoDao = new ScheduleInfoDAO();
         List previousPrograms = scheduleInfoDao.getDeviceProgramMapList(programId, session);
         if (CollectionUtils.isNotEmpty(previousPrograms)) {
            String[] groupIds = deviceGroupIds.split(",");
            List programs = new ArrayList();

            for(int i = 0; i < previousPrograms.size(); ++i) {
               Map devMap = (Map)previousPrograms.get(i);
               boolean match = false;
               String previousDevId = "" + devMap.get("device_group_id");

               for(int k = 0; k < groupIds.length; ++k) {
                  if (previousDevId.equals(groupIds[k])) {
                     match = true;
                     break;
                  }
               }

               if (!match) {
                  Long deviceGroupId = (Long)devMap.get("device_group_id");
                  String defaultProgramId = (String)devMap.get("default_program_id");
                  scheduleInfoDao.mapDeviceGroupWithDefault(deviceGroupId, defaultProgramId, session);
                  ProgramEntity programEntity = scheduleInfoDao.getProgram(defaultProgramId, session);
                  Map devGroupNames = scheduleInfoDao.getDeviceGroupIdsAndName(defaultProgramId, session);
                  programEntity.setDevice_group_ids((String)devGroupNames.get("device_group_ids"));
                  programs.add(programEntity);
               }
            }

            if (CollectionUtils.isNotEmpty(programs)) {
               result = this.deployDefaultPrograms(programs, session);
            }
         }
      }

      return result;
   }

   public boolean checkDeviceMapping(String program_id, String device_group_ids) throws SQLException {
      return this.checkDeviceMapping(program_id, device_group_ids, (SqlSession)null);
   }

   public List getProgramChannelsInfo(SelectConditionScheduleAdmin condition) throws SQLException {
      return ((ScheduleAdminDaoMapper)this.getMapper()).getProgramChannelsInfo(condition.getProgram_id());
   }

   public List getProgramFramesInfo(SelectConditionScheduleAdmin condition) throws SQLException {
      int channel_no = 1;
      if (condition != null) {
         channel_no = condition.getChannel_no();
      }

      String strProgramId = null;
      if (condition != null) {
         strProgramId = condition.getProgram_id();
      } else {
         this.logger.error("condition is null");
      }

      return ((ScheduleAdminDaoMapper)this.getMapper()).getProgramFramesInfo(strProgramId, channel_no);
   }

   public List getDistinctChannels(SelectConditionScheduleAdmin condObj) throws SQLException {
      return null;
   }

   public List getDistinctFrames(SelectConditionScheduleAdmin condObj) throws SQLException {
      Map params = new HashMap();
      String groupType = condObj.getGroupType();
      String[] deletedValues = new String[]{"Y", "N"};
      if (StringUtils.hasLength(groupType)) {
         if ("trash".equalsIgnoreCase(groupType)) {
            deletedValues = new String[]{"Y"};
         } else if ("all".equalsIgnoreCase(groupType)) {
            deletedValues = new String[]{"N"};
         }
      }

      params.put("deletedValues", deletedValues);
      String like = condObj.getNameLike();
      if (StringUtils.hasLength(like)) {
         params.put("like", like.replaceAll("_", "^_"));
      }

      List result = ((ScheduleAdminDaoMapper)this.getMapper()).getDistinctFrames(params);
      if (result == null) {
         result = new ArrayList();
      }

      return (List)result;
   }

   public PagedListInfo getScheduleAdminInfo(int startPos, int pageSize, Map condition) throws SQLException {
      SelectConditionScheduleAdmin condObj = (SelectConditionScheduleAdmin)condition.get("condition");
      String strGroup = condObj.getGroupType();
      Map parameters = new HashMap();
      String strRootGroup;
      if (StringUtils.hasLength(strGroup)) {
         int group_id;
         List manageGroupList;
         if (!"trash".equalsIgnoreCase(strGroup) && !"all".equalsIgnoreCase(strGroup)) {
            if ("default".equalsIgnoreCase(strGroup)) {
               parameters.put("default", this.getProgramGroupForUser(condObj.getUserRootGroup()));
               strRootGroup = condObj.getUserRootGroup();
               if (strRootGroup.equalsIgnoreCase("ROOT")) {
                  parameters.put("root", true);
               }
            } else if (strGroup.contains(",")) {
               List groupIds = new ArrayList();
               String[] tempIds = strGroup.split(",");
               String[] var30 = tempIds;
               int var33 = tempIds.length;

               for(int var35 = 0; var35 < var33; ++var35) {
                  String item = var30[var35];
                  groupIds.add(Long.parseLong(item));
               }

               parameters.put("groupIds", groupIds);
            } else {
               ProgramGroupDao groupDao = new ProgramGroupDao();
               group_id = Integer.parseInt(strGroup);
               ProgramGroup programGroup = groupDao.getGroup(group_id);
               if (programGroup.getP_group_id() == 0L) {
                  manageGroupList = this.getAllChildGroupIdList((long)group_id);
                  parameters.put("groupIds", manageGroupList);
               } else {
                  parameters.put("addGroupId", Long.valueOf(strGroup));
               }
            }
         } else {
            parameters.put("trashOrAll", true);
            if ("trash".equalsIgnoreCase(strGroup)) {
               parameters.put("trash", true);
            } else if ("all".equalsIgnoreCase(strGroup)) {
               parameters.put("all", true);
            }

            strRootGroup = condObj.getUserRootGroup();
            group_id = this.getProgramGroupForUser(strRootGroup);
            if ("trash".equalsIgnoreCase(strGroup)) {
               parameters.put("addGroupId", group_id);
            } else if (group_id != -1) {
               List childGroups = this.getAllChildGroupIdList((long)group_id);
               if (CollectionUtils.isNotEmpty(childGroups)) {
                  parameters.put("groupIds", childGroups);
               }
            } else if (group_id == -1) {
               UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
               manageGroupList = userGroupInfo.getUserManageGroupListByUserId(SecurityUtils.getLoginUserId());
               if (manageGroupList != null && manageGroupList.size() > 0) {
                  List childGroups = new ArrayList();
                  Iterator var12 = manageGroupList.iterator();

                  while(var12.hasNext()) {
                     UserGroup userGroup = (UserGroup)var12.next();
                     if (userGroup.getGroup_id() != 0L) {
                        long schOrgId = (long)this.getProgramGroupForUser(userGroup.getGroup_name());
                        childGroups.addAll(this.getAllChildGroupIdList(schOrgId));
                     }
                  }

                  if (CollectionUtils.isNotEmpty(childGroups)) {
                     parameters.put("groupIds", childGroups);
                  }
               }
            }
         }
      } else {
         parameters.put("notDefault", true);
      }

      strRootGroup = condObj.getNameLike();
      if (StringUtils.hasLength(strRootGroup)) {
         parameters.put("like", strRootGroup.replaceAll("_", "^_").replaceAll("\\[", "^["));
      }

      String frameCount = condObj.getFrameCount();
      if (StringUtils.hasLength(frameCount) && frameCount.contains("ALL")) {
         parameters.put("frames", Arrays.asList(frameCount.split(":")));
      }

      String selId = condObj.getSelId();
      if (CommonUtils.checkNull(condObj.getIsSelect()) && CommonUtils.checkNull(selId)) {
         parameters.put("selId", selId);
      }

      if (StringUtils.hasLength(condObj.getDevice_type())) {
         parameters.put("deviceTypes", Arrays.asList(condObj.getDevice_type().split(",")));
         this.addDeviceTypeAndVersion(parameters);
      }

      if (condObj.getProgram_type() != null && !condObj.getProgram_type().equals("")) {
         String[] playlist_type_array = condObj.getProgram_type().split(",");
         if (playlist_type_array != null && playlist_type_array.length > 0) {
            parameters.put("program_type_filter", Arrays.asList(playlist_type_array));
         }
      }

      String sort = condObj.getSort_name();
      String dir = condObj.getOrder_dir();
      if (StringUtils.hasLength(sort) && StringUtils.hasLength(dir)) {
         parameters.put("sort", sort);
         parameters.put("dir", dir);
      }

      parameters.put("pageSize", pageSize);
      parameters.put("startPos", startPos - 1);
      List result = ((ScheduleAdminDaoMapper)this.getMapper()).getScheduleAdminInfoBase(parameters);
      int totalCount = ((ScheduleAdminDaoMapper)this.getMapper()).getScheduleAdminInfoCount(parameters);
      Object typeFilterMapList = null;

      try {
         if (StrUtils.nvl(CommonConfig.get("saas.ac.enable")).equals("true")) {
            Map e = new CaseInsensitiveMap();
            e.put("device_type", "S2PLAYER");
            e.put("device_type_version", 2.0D);
            typeFilterMapList = new ArrayList();
            ((List)typeFilterMapList).add(e);
         } else {
            typeFilterMapList = ((ScheduleAdminDaoMapper)this.getMapper()).getScheduleTypeFilter(parameters);
         }
      } catch (ConfigException var24) {
         typeFilterMapList = ((ScheduleAdminDaoMapper)this.getMapper()).getScheduleTypeFilter(parameters);
      }

      if (!"trash".equalsIgnoreCase(strGroup) && result != null) {
         Iterator var42 = result.iterator();

         while(true) {
            while(true) {
               ScheduleAdminEntity entity;
               StringBuffer groupStr;
               StringBuffer groupIdStr;
               String progId;
               List groups;
               do {
                  if (!var42.hasNext()) {
                     return new TypeFilterPagedListInfo(result, totalCount, (List)typeFilterMapList);
                  }

                  entity = (ScheduleAdminEntity)var42.next();
                  groupStr = new StringBuffer();
                  groupIdStr = new StringBuffer();
                  progId = entity.getProgram_id();
                  groups = ((ScheduleAdminDaoMapper)this.getMapper()).getGroupForSchedule(progId, parameters.get("default") != null);
               } while(groups == null);

               Iterator it = groups.iterator();
               if ("default".equalsIgnoreCase(strGroup)) {
                  if (it.hasNext()) {
                     Map group = (Map)it.next();
                     Long creatorGroupId = (Long)group.get("GROUP_ID");
                     groupStr.append(group.get("GROUP_NAME"));
                     groupIdStr.append(creatorGroupId);
                     if (((ScheduleAdminDaoMapper)this.getMapper()).getProgramIdCount(progId, parameters.get("default") != null) > 0L) {
                        entity.setCreator_group_attached("Y");
                     } else {
                        entity.setCreator_group_attached("N");
                     }
                  }

                  entity.setGroup_name("Default");
                  entity.setDevice_group_name(groupStr.toString());
                  entity.setDevice_group_id(groupIdStr.toString());
               } else {
                  boolean firstLoop = true;

                  while(it.hasNext()) {
                     Map group;
                     if (!firstLoop) {
                        group = (Map)it.next();
                        groupStr.append(",").append(group.get("GROUP_NAME"));
                        groupIdStr.append(",").append(group.get("GROUP_ID"));
                     } else {
                        firstLoop = false;
                        group = (Map)it.next();
                        groupStr.append(group.get("GROUP_NAME"));
                        groupIdStr.append(group.get("GROUP_ID"));
                     }
                  }

                  entity.setDevice_group_name(groupStr.toString());
                  entity.setDevice_group_id(groupIdStr.toString());
               }
            }
         }
      } else {
         return new TypeFilterPagedListInfo(result, totalCount, (List)typeFilterMapList);
      }
   }

   private void addDeviceTypeAndVersion(Map parameters) {
      parameters.put("TYPE_APLAYER", "APLAYER");
      parameters.put("TYPE_WPLAYER", "WPLAYER");
      parameters.put("TYPE_SOC", "SPLAYER");
      parameters.put("TYPE_SOC2", "S2PLAYER");
      parameters.put("TYPE_SOC3", "S3PLAYER");
      parameters.put("TYPE_SOC4", "S4PLAYER");
      parameters.put("TYPE_SOC5", "S5PLAYER");
      parameters.put("TYPE_SOC6", "S6PLAYER");
      parameters.put("TYPE_SOC7", "S7PLAYER");
      parameters.put("TYPE_SOC9", "S9PLAYER");
      parameters.put("TYPE_SOC10", "S10PLAYER");
      parameters.put("TYPE_PREMIUM", "iPLAYER");
      parameters.put("TYPE_VERSION_1_0", CommonDataConstants.TYPE_VERSION_1_0);
      parameters.put("TYPE_VERSION_2_0", CommonDataConstants.TYPE_VERSION_2_0);
      parameters.put("TYPE_VERSION_3_0", CommonDataConstants.TYPE_VERSION_3_0);
      parameters.put("TYPE_VERSION_4_0", CommonDataConstants.TYPE_VERSION_4_0);
      parameters.put("TYPE_VERSION_5_0", CommonDataConstants.TYPE_VERSION_5_0);
      parameters.put("TYPE_VERSION_6_0", CommonDataConstants.TYPE_VERSION_6_0);
      parameters.put("TYPE_VERSION_7_0", CommonDataConstants.TYPE_VERSION_7_0);
      parameters.put("TYPE_VERSION_9_0", CommonDataConstants.TYPE_VERSION_9_0);
      parameters.put("TYPE_VERSION_10_0", CommonDataConstants.TYPE_VERSION_10_0);
   }

   public PagedListInfo getScheduleByUser(int startPos, int pageSize, Map condition) throws SQLException {
      SelectConditionScheduleAdmin condObj = (SelectConditionScheduleAdmin)condition.get("condition");
      String strGroup = condObj.getGroupType();
      Map parameters = new HashMap();
      String strRootGroup;
      if (StringUtils.hasLength(strGroup)) {
         if (!"trash".equalsIgnoreCase(strGroup) && !"all".equalsIgnoreCase(strGroup)) {
            if ("default".equalsIgnoreCase(strGroup)) {
               parameters.put("default", this.getProgramGroupForUser(condObj.getUserRootGroup()));
               strRootGroup = condObj.getUserRootGroup();
               if (strRootGroup.equalsIgnoreCase("ROOT")) {
                  parameters.put("root", true);
               }
            } else {
               parameters.put("addGroupId", Long.valueOf(strGroup));
            }
         } else {
            parameters.put("trashOrAll", true);
            if ("trash".equalsIgnoreCase(strGroup)) {
               parameters.put("trash", true);
            } else if ("all".equalsIgnoreCase(strGroup)) {
               parameters.put("all", true);
            }

            strRootGroup = condObj.getUserRootGroup();
            int group_id = this.getProgramGroupForUser(strRootGroup);
            if (group_id != -1) {
               List childGroups = this.getChildGroupIdList(group_id, true);
               childGroups.add(new Long((long)group_id));
               if (CollectionUtils.isNotEmpty(childGroups)) {
                  parameters.put("groupIds", childGroups);
               }
            } else if (group_id == -1) {
               UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
               List manageGroupList = userGroupInfo.getUserManageGroupListByUserId(SecurityUtils.getLoginUserId());
               if (manageGroupList != null && manageGroupList.size() > 0) {
                  List childGroups = new ArrayList();
                  Iterator var12 = manageGroupList.iterator();

                  while(var12.hasNext()) {
                     UserGroup userGroup = (UserGroup)var12.next();
                     int orgId = this.getProgramGroupForUser(userGroup.getGroup_name());
                     childGroups.addAll(this.getAllChildGroupIdList((long)orgId));
                  }

                  if (CollectionUtils.isNotEmpty(childGroups)) {
                     parameters.put("groupIds", childGroups);
                  }
               }
            }
         }
      } else {
         parameters.put("notDefault", true);
      }

      strRootGroup = condObj.getNameLike();
      if (StringUtils.hasLength(strRootGroup)) {
         parameters.put("like", strRootGroup.replaceAll("_", "^_").replaceAll("\\[", "^["));
      }

      String frameCount = condObj.getFrameCount();
      if (StringUtils.hasLength(frameCount) && frameCount.contains("ALL")) {
         parameters.put("frames", Arrays.asList(frameCount.split(":")));
      }

      String selId = condObj.getSelId();
      if (CommonUtils.checkNull(condObj.getIsSelect()) && CommonUtils.checkNull(selId)) {
         parameters.put("selId", selId);
      }

      if (StringUtils.hasLength(condObj.getDevice_type())) {
         parameters.put("deviceTypes", Arrays.asList(condObj.getDevice_type().split(",")));
         this.addDeviceTypeAndVersion(parameters);
      }

      String sort = condObj.getSort_name();
      String dir = condObj.getOrder_dir();
      if (StringUtils.hasLength(sort) && StringUtils.hasLength(dir)) {
         parameters.put("sort", sort);
         parameters.put("dir", dir);
      }

      parameters.put("pageSize", pageSize);
      parameters.put("startPos", startPos - 1);
      List schedules = ((ScheduleAdminDaoMapper)this.getMapper()).getScheduleByUserBase(parameters);
      int totalCount = ((ScheduleAdminDaoMapper)this.getMapper()).getScheduleByUserCount(parameters);
      List typeFilterMapList = ((ScheduleAdminDaoMapper)this.getMapper()).getScheduleTypeFilter(parameters);
      return new TypeFilterPagedListInfo(schedules, totalCount, typeFilterMapList);
   }

   public PagedListInfo getScheduleAdminInfoSearch(int startPos, int pageSize, Map condition) throws SQLException {
      Map parameters = new HashMap();
      SelectConditionScheduleAdmin condObj = (SelectConditionScheduleAdmin)condition.get("condition");
      String strGroup = condObj.getGroupType();
      String sort;
      List schedules;
      if (StringUtils.hasLength(strGroup)) {
         if ("all".equalsIgnoreCase(strGroup)) {
            parameters.put("all", true);
            sort = condObj.getUserRootGroup();
            int groupId = this.getProgramGroupForUser(sort);
            if (groupId != -1) {
               schedules = this.getChildGroupIdList(groupId, true);
               if (CollectionUtils.isNotEmpty(schedules)) {
                  parameters.put("groupIds", schedules);
               }
            }
         } else {
            parameters.put("notDefaultNorDeleted", true);
            if (!"trash".equalsIgnoreCase(strGroup)) {
               ProgramGroupDao groupDao = new ProgramGroupDao();
               ProgramGroup programGroup = groupDao.getGroup(Integer.parseInt(strGroup));
               if (programGroup.getP_group_id() != 0L && programGroup.getP_group_id() != -1L) {
                  parameters.put("groupId", Integer.parseInt(strGroup));
               } else {
                  schedules = this.getChildGroupIdList(programGroup.getGroup_id().intValue(), true);
                  if (CollectionUtils.isNotEmpty(schedules)) {
                     parameters.put("groupIds", schedules);
                  }
               }
            }
         }
      } else {
         parameters.put("notDefault", true);
      }

      String[] devGroupIds_array;
      if (condObj.getSelect_content_ids() != null && condObj.getSelect_content_ids() != "") {
         devGroupIds_array = condObj.getSelect_content_ids().split(",");
         if (devGroupIds_array != null && devGroupIds_array.length > 0) {
            parameters.put("contentId", Arrays.asList(devGroupIds_array));
         }
      }

      if (condObj.getSelect_devgroup_ids() != null && condObj.getSelect_devgroup_ids() != "") {
         devGroupIds_array = condObj.getSelect_devgroup_ids().split(",");
         if (devGroupIds_array != null && devGroupIds_array.length > 0) {
            List devGroupIdsArr = new ArrayList();

            for(int i = 0; i < devGroupIds_array.length; ++i) {
               devGroupIdsArr.add(new Integer(devGroupIds_array[i]));
            }

            parameters.put("deviceGroupId", devGroupIdsArr);
         }
      }

      if (condObj.getProgram_type() != null && !condObj.getProgram_type().equals("")) {
         devGroupIds_array = condObj.getProgram_type().split(",");
         if (devGroupIds_array != null && devGroupIds_array.length > 0) {
            parameters.put("program_type_filter", Arrays.asList(devGroupIds_array));
         }
      }

      if (condObj.getStart_modified_date() != null && condObj.getStart_modified_date() != "") {
         parameters.put("startModifyDate", condObj.getStart_modified_date() + " 00:00:00");
      }

      if (condObj.getEnd_modified_date() != null && condObj.getEnd_modified_date() != "") {
         parameters.put("endModifyDate", condObj.getEnd_modified_date() + " 23:59:59");
      }

      if (condObj.getNameLike() != null && condObj.getNameLike() != "") {
         parameters.put("like", condObj.getNameLike().replaceAll("_", "^_").replaceAll("\\[", "^["));
      }

      sort = condObj.getSort_name();
      String dir = condObj.getOrder_dir();
      if (StringUtils.hasLength(sort) && StringUtils.hasLength(dir)) {
         parameters.put("sort", sort);
         parameters.put("dir", dir);
      }

      parameters.put("pageSize", pageSize);
      parameters.put("startPos", startPos - 1);
      schedules = ((ScheduleAdminDaoMapper)this.getMapper()).getScheduleAdminInfoSearchBase(parameters);
      int totalCount = ((ScheduleAdminDaoMapper)this.getMapper()).getScheduleAdminInfoSearchCount(parameters);
      List typeFilterMapList = ((ScheduleAdminDaoMapper)this.getMapper()).getScheduleTypeFilter(parameters);
      if (!"trash".equalsIgnoreCase(strGroup) && schedules != null) {
         Iterator var12 = schedules.iterator();

         while(true) {
            while(true) {
               ScheduleAdminEntity entity;
               StringBuffer groupStr;
               StringBuffer groupIdStr;
               String progId;
               List groups;
               do {
                  if (!var12.hasNext()) {
                     return new TypeFilterPagedListInfo(schedules, totalCount, typeFilterMapList);
                  }

                  entity = (ScheduleAdminEntity)var12.next();
                  groupStr = new StringBuffer();
                  groupIdStr = new StringBuffer();
                  progId = entity.getProgram_id();
                  groups = ((ScheduleAdminDaoMapper)this.getMapper()).getGroupForSchedule(progId, parameters.get("default") != null);
               } while(groups == null);

               Iterator it = groups.iterator();
               if ("default".equalsIgnoreCase(strGroup)) {
                  if (it.hasNext()) {
                     Map group = (Map)it.next();
                     Long creatorGroupId = (Long)group.get("GROUP_ID");
                     groupStr.append(group.get("GROUP_NAME"));
                     groupIdStr.append(creatorGroupId);
                     if (((ScheduleAdminDaoMapper)this.getMapper()).getProgramIdCount(progId, parameters.get("default") != null) > 0L) {
                        entity.setCreator_group_attached("Y");
                     } else {
                        entity.setCreator_group_attached("N");
                     }
                  }

                  entity.setGroup_name("Default");
                  entity.setDevice_group_name(groupStr.toString());
                  entity.setDevice_group_id(groupIdStr.toString());
               } else {
                  boolean firstLoop = true;

                  while(it.hasNext()) {
                     Map group;
                     if (!firstLoop) {
                        group = (Map)it.next();
                        groupStr.append(",").append(group.get("GROUP_NAME"));
                        groupIdStr.append(",").append(group.get("GROUP_ID"));
                     } else {
                        firstLoop = false;
                        group = (Map)it.next();
                        groupStr.append(group.get("GROUP_NAME"));
                        groupIdStr.append(group.get("GROUP_ID"));
                     }
                  }

                  entity.setDevice_group_name(groupStr.toString());
                  entity.setDevice_group_id(groupIdStr.toString());
               }
            }
         }
      } else {
         return new TypeFilterPagedListInfo(schedules, totalCount, typeFilterMapList);
      }
   }

   /** @deprecated */
   @Deprecated
   public PagedListInfo getScheduleByUserSearch(int startPos, int pageSize, Map condition) throws SQLException {
      throw new SQLException("ERROR: column cntquery.promgram_id does not exist Query: SELECT COUNT(CNTQUERY.PROMGRAM_ID)");
   }

   public List getScheduleAdminInfo(SelectConditionScheduleAdmin condObj) throws SQLException {
      Map parameters = new HashMap();
      String groupType = condObj.getGroupType();
      if ("default".equalsIgnoreCase(groupType)) {
         parameters.put("default", true);
         if (!"ROOT".equals(condObj.getUserRootGroup())) {
            parameters.put("addRemainingWhereClause", true);
         }
      } else {
         parameters.put("addRemainingWhereClause", true);
      }

      String like = condObj.getNameLike();
      if (StringUtils.hasLength(like)) {
         parameters.put("like", like.replaceAll("_", "^_"));
      }

      if (parameters.containsKey("addRemainingWhereClause")) {
         if (StringUtils.hasLength(groupType)) {
            if (com.samsung.magicinfo.protocol.util.StringUtils.isNumberString(groupType)) {
               ScheduleInfoDAO dao = new ScheduleInfoDAO();
               int groupId = Integer.parseInt(groupType);
               if (dao.getProgramGroupParentId(groupId) == 0L) {
                  List childGroups = this.getChildGroupIdList(groupId, true);
                  if (childGroups != null) {
                     childGroups.add((long)groupId);
                     parameters.put("groupIds", childGroups);
                  }

                  parameters.put("deletedNotDefault", true);
               } else {
                  parameters.put("groupId", Integer.parseInt(groupType));
               }
            } else {
               int groupId;
               if (!"trash".equalsIgnoreCase(groupType) && !"all".equalsIgnoreCase(groupType)) {
                  if ("default".equalsIgnoreCase(groupType)) {
                     groupId = this.getProgramGroupForUser(condObj.getUserRootGroup());
                     if (groupId != -1) {
                        parameters.put("groupId", groupId);
                     }
                  }
               } else {
                  if ("trash".equalsIgnoreCase(groupType)) {
                     parameters.put("deleted", true);
                  } else if ("all".equalsIgnoreCase(groupType)) {
                     parameters.put("deletedNotDefault", true);
                  }

                  groupId = this.getProgramGroupForUser(condObj.getUserRootGroup());
                  if (groupId != -1) {
                     List childGroups = this.getChildGroupIdList(groupId, true);
                     if (childGroups != null) {
                        childGroups.add((long)groupId);
                        parameters.put("groupIds", childGroups);
                     }
                  }
               }
            }
         } else {
            parameters.put("notDefaultGroup", true);
         }
      }

      String sort = condObj.getSort_name();
      String dir = condObj.getOrder_dir();
      if (StringUtils.hasLength(sort) && StringUtils.hasLength(dir)) {
         parameters.put("sort", sort.toUpperCase());
         parameters.put("dir", dir.toUpperCase());
      }

      return ((ScheduleAdminDaoMapper)this.getMapper()).getScheduleAdminInfo(parameters);
   }

   public int getProgramGroupForUser(String strOrg, SqlSession session) throws SQLException {
      ScheduleAdminDaoMapper mapper = session != null ? (ScheduleAdminDaoMapper)this.getMapper(session) : (ScheduleAdminDaoMapper)this.getMapper();
      Long group = mapper.getProgramGroupForUser(strOrg);
      return group != null ? group.intValue() : -1;
   }

   public int getProgramGroupForUser(String strOrg) throws SQLException {
      return this.getProgramGroupForUser(strOrg, (SqlSession)null);
   }

   private List getChildGroupIdListRecur(int groupId, boolean recursive, SqlSession session) throws SQLException {
      ScheduleAdminDaoMapper mapper = (ScheduleAdminDaoMapper)this.getMapper(session);
      ArrayList result = new ArrayList();

      try {
         List groupIds = mapper.getChildGroupIdList(groupId, 999999);
         if (CollectionUtils.isNotEmpty(groupIds)) {
            for(int i = 0; i < groupIds.size(); ++i) {
               if (recursive) {
                  result.add(groupIds.get(i));
                  List temp = this.getChildGroupIdListRecur(((Long)groupIds.get(i)).intValue(), recursive, this.openNewSession(false));
                  if (CollectionUtils.isNotEmpty(temp)) {
                     result.addAll(temp);
                  }
               } else {
                  result.add((Long)groupIds.get(i));
               }
            }
         }

         session.commit();
      } catch (Exception var12) {
         session.rollback();
      } finally {
         if (session != null) {
            session.close();
         }

      }

      return result;
   }

   public List getChildGroupIdList(int group_id, boolean recursive) throws SQLException {
      return this.getChildGroupIdListRecur(group_id, recursive, this.openNewSession(false));
   }

   public Long getProgramGroupById(String programId, SqlSession session) throws SQLException {
      return session != null ? ((ScheduleAdminDaoMapper)this.getMapper(session)).getGroupId(programId) : ((ScheduleAdminDaoMapper)this.getMapper()).getGroupId(programId);
   }

   public Long getProgramGroupById(String programId) throws SQLException {
      return this.getProgramGroupById(programId, (SqlSession)null);
   }

   public String getProgramGroupRoot(int groupId, SqlSession session) throws SQLException {
      ScheduleAdminDaoMapper mapper = session != null ? (ScheduleAdminDaoMapper)this.getMapper(session) : (ScheduleAdminDaoMapper)this.getMapper();
      Map values = mapper.getProgramGroupRoot(groupId);
      int newGroupParentId = ((Long)values.get("P_GROUP_ID")).intValue();
      return newGroupParentId != 0 ? this.getProgramGroupRoot(newGroupParentId, session) : (String)values.get("GROUP_NAME");
   }

   public String getProgramGroupRoot(int groupId) throws SQLException {
      return this.getProgramGroupRoot(groupId, (SqlSession)null);
   }

   public String getDeviceGroupRoot(int groupId) throws SQLException {
      Map result = ((ScheduleAdminDaoMapper)this.getMapper()).getDeviceGroupRoot(groupId);
      int newGroupParentId = ((Long)result.get("P_GROUP_ID")).intValue();
      return newGroupParentId != 0 ? this.getDeviceGroupRoot(newGroupParentId) : (String)result.get("GROUP_NAME");
   }

   public String getProgramIdByDeviceGroupId(long deviceGroupId) throws SQLException {
      return ((ScheduleAdminDaoMapper)this.getMapper()).getProgramIdByDeviceGroupId(deviceGroupId);
   }

   public boolean updateProgramPublishStatus(String programId, String publishStatus) throws SQLException {
      boolean result = false;
      int retry = 5;

      while(retry > 0) {
         try {
            result = ((ScheduleAdminDaoMapper)this.getMapper()).updateProgramPublishStatus(programId, publishStatus);
            retry = 0;
         } catch (SQLException var8) {
            if (retry == 1) {
               throw var8;
            }

            try {
               --retry;
               Thread.sleep(1L);
            } catch (InterruptedException var7) {
               this.logger.error(var7.getMessage(), var7);
            }
         }
      }

      return result;
   }

   public void deleteContentFromSchedule(String contentId) {
      try {
         ((ScheduleAdminDaoMapper)this.getMapper()).deleteContentFromSchedule(contentId);
         ((ScheduleAdminDaoMapper)this.getMapper()).deleteContentFromAdSchedule(contentId);
      } catch (SQLException var3) {
         this.logger.error("", var3);
      }

   }

   public void deleteContentFromScheduleReserved(String contentId) {
      try {
         ((ScheduleAdminDaoMapper)this.getMapper()).deleteContentFromScheduleReserved(contentId);
      } catch (SQLException var3) {
         this.logger.error("", var3);
      }

   }

   public void deleteContentFromScheduleTemp(String contentId) {
      try {
         ((ScheduleAdminDaoMapper)this.getMapper()).deleteContentFromScheduleTemp(contentId);
      } catch (SQLException var3) {
         this.logger.error("", var3);
      }

   }

   public String getUseSyncByDeviceGroupId(long deviceGroupId) throws SQLException {
      return ((ScheduleAdminDaoMapper)this.getMapper()).getUseSyncByDeviceGroupId(deviceGroupId);
   }

   public List getAllChildGroupIdList(long groupId) throws SQLException {
      return ((ScheduleAdminDaoMapper)this.getMapper()).getAllChildGroupIdList(groupId);
   }

   public List getDeviceCountBySchedule() throws SQLException {
      return ((ScheduleAdminDaoMapper)this.getMapper()).getDeviceCountBySchedule();
   }

   public List getContentCountBySchedule() throws SQLException {
      return ((ScheduleAdminDaoMapper)this.getMapper()).getContentCountBySchedule();
   }
}
