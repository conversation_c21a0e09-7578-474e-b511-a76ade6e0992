package com.samsung.magicinfo.webauthor2.repository;

import com.google.common.base.Preconditions;
import com.samsung.magicinfo.webauthor2.exception.repository.TagNotFoundException;
import com.samsung.magicinfo.webauthor2.model.Tag;
import com.samsung.magicinfo.webauthor2.repository.command.GetTagCommand;
import com.samsung.magicinfo.webauthor2.repository.model.tag.TagData;
import com.samsung.magicinfo.webauthor2.repository.model.tag.TagResponseData;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.web.client.RestTemplate;

@Repository
public class OpenAPITagRepositoryImpl implements OpenAPITagRepository {
  private static final Logger logger = LoggerFactory.getLogger(OpenAPITagRepositoryImpl.class);
  
  private final String REST_PATH = "/openapi/open?service=CommonSettingService.getTagList ";
  
  private final RestTemplate restTemplate;
  
  @Inject
  public OpenAPITagRepositoryImpl(RestTemplate restTemplate) {
    this.restTemplate = restTemplate;
  }
  
  public List<Tag> getTagList(GetTagCommand command) {
    Map<String, String> vars = variables(command);
    TagResponseData responseData = (TagResponseData)this.restTemplate.getForObject(path(vars), TagResponseData.class, vars);
    List<Tag> result = convert(responseData);
    return result;
  }
  
  private List<Tag> convert(TagResponseData responseData) {
    Preconditions.checkNotNull(responseData);
    if (responseData.getErrorMessage() != null)
      throw new TagNotFoundException(responseData.getCode(), responseData.getErrorMessage()); 
    Preconditions.checkNotNull(responseData.getResponseClass());
    List<Tag> result = new ArrayList<>();
    if (responseData.getResponseClass().getTotalCount().intValue() > 0)
      for (TagData tagData : responseData.getResponseClass().getResultList()) {
        logger.debug("TagId: ", Integer.valueOf(tagData.getId()));
        try {
          result.add(Tag.fromData(tagData));
        } catch (IllegalArgumentException ex) {
          logger.warn("Couldn't map tag data " + tagData.getId() + ". " + ex.getMessage());
        } 
      }  
    return result;
  }
  
  private Map<String, String> variables(GetTagCommand command) {
    Map<String, String> vars = new HashMap<>();
    vars.put("userId", command.getUserId());
    vars.put("token", command.getToken());
    return vars;
  }
  
  private String path(Map<String, String> vars) {
    String path = "/openapi/open?service=CommonSettingService.getTagList ";
    StringBuilder sb = new StringBuilder(path);
    for (String parameterName : vars.keySet()) {
      path = path + "&" + parameterName + "={" + parameterName + "}";
      sb.append("&").append(parameterName).append("={").append(parameterName).append("}");
    } 
    return sb.toString();
  }
}
