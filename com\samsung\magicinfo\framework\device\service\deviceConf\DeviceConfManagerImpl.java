package com.samsung.magicinfo.framework.device.service.deviceConf;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.DBCacheUtils;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.MDCTimeStrUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.device.constants.DeviceMOConstants;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceControl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceLogCollectEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceNetworkConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSecurityConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemInfoConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemSetupConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTimeConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.LedCabinet;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceGeneralConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceGeneralConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceNetworkConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceNetworkConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSboxConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSboxConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSecurityConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSecurityConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.LedCabinetConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.LedCabinetConfManagerImpl;
import com.samsung.magicinfo.framework.monitoring.entity.CurrentPlayingEntity;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.protocol.rmql.RMQLException;
import com.samsung.magicinfo.protocol.rmql.ResultSet;
import com.samsung.magicinfo.protocol.servicemanager.ServiceDispatcher;
import com.samsung.magicinfo.protocol.servicemanager.WSRMServiceDispatcher;
import com.samsung.magicinfo.protocol.servicestatus.ServiceStatusManager;
import com.samsung.magicinfo.protocol.servicestatus.ServiceStatusManagerImpl;
import com.samsung.magicinfo.restapi.device.model.V2DeviceExternalPowerResource;
import java.io.File;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.logging.log4j.Logger;

public class DeviceConfManagerImpl implements DeviceConfManager {
   private Logger logger = LoggingManagerV2.getLogger(DeviceConfManagerImpl.class);

   public static DeviceConfManager getInstance() {
      return new DeviceConfManagerImpl();
   }

   private DeviceConfManagerImpl() {
      super();
   }

   public void cleareDisplayResultSet(String deviceId, String sessionId) {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      statusManager.setResultset(sessionId, "SET_DEVICE_DISPLAY_CONF", deviceId, (ResultSet)null);
   }

   public void clearSystemInfoResultSet(String deviceId, String sessionId) {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      statusManager.setResultset(sessionId, "SET_DEVICE_SYSTEM_INFO_CONF", deviceId, (ResultSet)null);
   }

   public List getCabinetConfResultSet(String deviceId, String sessionId, String serviceId, String type) {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = statusManager.getResultset(sessionId, serviceId, deviceId);
      if (rs == null) {
         return null;
      } else {
         ArrayList cabinetList = null;

         try {
            String[] childIndex = rs.getResultAddressAll();
            if (childIndex != null && childIndex.length > 0) {
               cabinetList = new ArrayList();
               String[] ablArr = null;
               String[] childTypeArr = null;
               String[] inputSourceArr = null;
               String[] gamutArr = null;
               String[] backlightArr = null;
               String[] pixelRgbCcArr = null;
               String[] moduleRgbCcArr = null;
               String[] edgeCorrectionArr = null;
               if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.CABINET_TYPE", childIndex[0]) != null) {
                  childTypeArr = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.CABINET_TYPE", childIndex[0]).split(";");
               }

               if (rs.getString(".MO.CABINET_CONF.BASIC.ABL", childIndex[0]) != null) {
                  ablArr = rs.getString(".MO.CABINET_CONF.BASIC.ABL", childIndex[0]).split(";");
               }

               if (rs.getString(".MO.CABINET_CONF.BASIC.SOURCE", childIndex[0]) != null) {
                  inputSourceArr = rs.getString(".MO.CABINET_CONF.BASIC.SOURCE", childIndex[0]).split(";");
               }

               if (rs.getString(".MO.CABINET_CONF.BASIC.GAMUT", childIndex[0]) != null) {
                  gamutArr = rs.getString(".MO.CABINET_CONF.BASIC.GAMUT", childIndex[0]).split(";");
               }

               if (rs.getString(".MO.CABINET_CONF.BASIC.BACKLIGHT", childIndex[0]) != null) {
                  backlightArr = rs.getString(".MO.CABINET_CONF.BASIC.BACKLIGHT", childIndex[0]).split(";");
               }

               if (rs.getString(".MO.CABINET_CONF.CALIBRATION.PIXEL_RGB_CC", childIndex[0]) != null) {
                  pixelRgbCcArr = rs.getString(".MO.CABINET_CONF.CALIBRATION.PIXEL_RGB_CC", childIndex[0]).split(";");
               }

               if (rs.getString(".MO.CABINET_CONF.CALIBRATION.MODULE_RGB_CC", childIndex[0]) != null) {
                  moduleRgbCcArr = rs.getString(".MO.CABINET_CONF.CALIBRATION.MODULE_RGB_CC", childIndex[0]).split(";");
               }

               if (rs.getString(".MO.CABINET_CONF.CALIBRATION.EDGE_CORRECTION", childIndex[0]) != null) {
                  edgeCorrectionArr = rs.getString(".MO.CABINET_CONF.CALIBRATION.EDGE_CORRECTION", childIndex[0]).split(";");
               }

               for(int i = 0; i < childIndex.length; ++i) {
                  String targetChildIndex = childIndex[i];
                  LedCabinet info = new LedCabinet(deviceId);
                  if (!"0".equals(targetChildIndex) && targetChildIndex.indexOf("-") > 0) {
                     info.setCabinet_group_id(Long.parseLong(targetChildIndex.split("-")[0]));
                     if (!"0".equals(targetChildIndex.split("-")[1])) {
                        info.setCabinet_id(Long.parseLong(targetChildIndex.split("-")[1]));
                     }
                  }

                  if (childTypeArr != null && childTypeArr.length > i) {
                     info.setCabinet_type(Long.parseLong(childTypeArr[i]));
                  }

                  if (ablArr != null && ablArr.length > i) {
                     info.setAbl(Long.parseLong(ablArr[i]));
                  }

                  if (inputSourceArr != null && inputSourceArr.length > i) {
                     info.setInput_source(Long.parseLong(inputSourceArr[i]));
                  }

                  if (gamutArr != null && gamutArr.length > i) {
                     info.setGamut(Long.parseLong(gamutArr[i]));
                  }

                  if (backlightArr != null && backlightArr.length > i) {
                     info.setBacklight(Long.parseLong(backlightArr[i]));
                  }

                  if (pixelRgbCcArr != null & pixelRgbCcArr.length > i) {
                     info.setPixel_rgb_cc(Long.parseLong(pixelRgbCcArr[i]));
                  }

                  if (moduleRgbCcArr != null && moduleRgbCcArr.length > i) {
                     info.setModule_rgb_cc(Long.parseLong(moduleRgbCcArr[i]));
                  }

                  if (edgeCorrectionArr != null && edgeCorrectionArr.length > i) {
                     info.setEdge_correction(Long.parseLong(edgeCorrectionArr[i]));
                  }

                  cabinetList.add(info);
               }
            }
         } catch (Exception var20) {
            this.logger.error("getCabinetConfResultSet Exception", var20);
            cabinetList = null;
         }

         return cabinetList;
      }
   }

   public Map getDisplayResultSetSummary(String deviceId, String sessionId, String serviceId, String type) {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = statusManager.getResultset(sessionId, serviceId, deviceId);
      if (rs == null) {
         return null;
      } else {
         Map ret = new HashMap();
         ret.put("FAIL", rs.getCountOfValue("FAIL"));
         ret.put("SUCCESS", rs.getCountOfValue("SUCCESS"));
         return ret;
      }
   }

   public DeviceDisplayConf getDisplayResultSet(String deviceId, String sessionId, String serviceId, String type) {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = statusManager.getResultset(sessionId, serviceId, deviceId);
      DeviceDisplayConf info = null;

      try {
         if (rs != null) {
            if (info == null) {
               info = new DeviceDisplayConf();
            }

            DeviceDisplayConfManager displayDao = DeviceDisplayConfManagerImpl.getInstance();
            DeviceDisplayConf tmp = displayDao.getDeviceDisplayConf(deviceId);
            if (tmp != null && tmp.getMdc_update_time() != null) {
               info.setMdc_update_time(tmp.getMdc_update_time());
            }

            if (type.toUpperCase().equals("MOBILE_MDC")) {
               if (rs.getString(".MO.DISPLAY_CONF.BASIC.POWER") != null) {
                  info.setBasic_power(rs.getString(".MO.DISPLAY_CONF.BASIC.POWER"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.BASIC.VOLUME") != null) {
                  info.setBasic_volume(rs.getLong(".MO.DISPLAY_CONF.BASIC.VOLUME"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.BASIC.MUTE") != null) {
                  info.setBasic_mute(rs.getLong(".MO.DISPLAY_CONF.BASIC.MUTE"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.BASIC.SOURCE") != null) {
                  info.setBasic_source(rs.getLong(".MO.DISPLAY_CONF.BASIC.SOURCE"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS") != null) {
                  info.setBasic_panel_status(rs.getLong(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SPECIALIZED_PICTURE_MODE") != null) {
                  info.setSpecialized_picture_mode(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.SPECIALIZED_PICTURE_MODE"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.MODE") != null) {
                  info.setPv_mode(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.MODE"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.CONTRAST") != null) {
                  info.setPv_contrast(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.CONTRAST"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.BRIGHTNESS") != null) {
                  info.setPv_brightness(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.BRIGHTNESS"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SHARPNESS") != null) {
                  info.setPv_sharpness(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.SHARPNESS"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR") != null) {
                  info.setPv_color(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.TINT") != null) {
                  info.setPv_tint(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.TINT"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TONE") != null) {
                  info.setPv_colortone(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TONE"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TEMPERATURE") != null) {
                  info.setPv_color_temperature(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TEMPERATURE"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.MAGIC_BRIGHT") != null) {
                  info.setPpc_magic_bright(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.MAGIC_BRIGHT"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_CONTRAST") != null) {
                  info.setPpc_contrast(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.RGB_CONTRAST"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS") != null) {
                  info.setPpc_brightness(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PC_COLOR_TONE") != null) {
                  info.setPpc_colortone(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.PC_COLOR_TONE"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.COLOR_TEMPERATURE") != null) {
                  info.setPpc_color_temperature(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.COLOR_TEMPERATURE"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_RED") != null) {
                  info.setPpc_red(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.RGB_RED"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_GREEN") != null) {
                  info.setPpc_green(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.RGB_GREEN"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BLUE") != null) {
                  info.setPpc_blue(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BLUE"));
               }
            } else if (type.toUpperCase().equals("ADVANCED_MDC")) {
               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.AUTO_BRIGHTNESS") != null) {
                  info.setAuto_brightness(rs.getString(".MO.DISPLAY_CONF.ADVANCED.AUTO_BRIGHTNESS"));
               }
            } else {
               if (!type.toUpperCase().equals("ONLY_EXT_MDC")) {
                  if (type.toUpperCase().equals("LIST")) {
                     if (rs.getString(".MO.DISPLAY_CONF.BASIC.VOLUME") != null) {
                        if (rs.getString(".MO.DISPLAY_CONF.BASIC.POWER") != null) {
                           info.setBasic_power(rs.getString(".MO.DISPLAY_CONF.BASIC.POWER"));
                        }

                        if (rs.getString(".MO.DISPLAY_CONF.BASIC.VOLUME") != null) {
                           info.setBasic_volume(rs.getLong(".MO.DISPLAY_CONF.BASIC.VOLUME"));
                        }

                        if (rs.getString(".MO.DISPLAY_CONF.BASIC.MUTE") != null) {
                           info.setBasic_mute(rs.getLong(".MO.DISPLAY_CONF.BASIC.MUTE"));
                        }

                        if (rs.getString(".MO.DISPLAY_CONF.BASIC.SOURCE") != null) {
                           info.setBasic_source(rs.getLong(".MO.DISPLAY_CONF.BASIC.SOURCE"));
                        }

                        if (rs.getString(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS") != null) {
                           info.setBasic_panel_status(rs.getLong(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS"));
                        }

                        if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS") != null) {
                           info.setPpc_brightness(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS"));
                        }

                        if (rs.getString(".MO.DISPLAY_CONF.TIME.CURRENT_TIME") != null) {
                           info.setTime_current_time(rs.getString(".MO.DISPLAY_CONF.TIME.CURRENT_TIME"));
                        } else if (rs.getString(".MO.DISPLAY_CONF.TIMER.CLOCK") != null) {
                           info.setTime_current_time(rs.getString(".MO.DISPLAY_CONF.TIMER.CLOCK"));
                        }

                        if (rs.getString(".MO.DISPLAY_CONF.TIME.ON_TIME") != null) {
                           info.setTime_on_time(rs.getString(".MO.DISPLAY_CONF.TIME.ON_TIME"));
                        }

                        if (rs.getString(".MO.DISPLAY_CONF.TIME.OFF_TIME") != null) {
                           info.setTime_off_time(rs.getString(".MO.DISPLAY_CONF.TIME.OFF_TIME"));
                        }

                        if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_LOCK") != null) {
                           info.setMnt_safety_lock(rs.getLong(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_LOCK"));
                        }

                        if (rs.getString(".MO.DISPLAY_CONF.MISC.REMOCON") != null) {
                           info.setMisc_remocon(rs.getLong(".MO.DISPLAY_CONF.MISC.REMOCON"));
                        }

                        if (rs.getString(".MO.DISPLAY_CONF.MISC.PANEL_LOCK") != null) {
                           info.setMisc_panel_lock(rs.getLong(".MO.DISPLAY_CONF.MISC.PANEL_LOCK"));
                        }

                        if (rs.getString(".MO.DISPLAY_CONF.MISC.OSD") != null) {
                           info.setMisc_osd(rs.getLong(".MO.DISPLAY_CONF.MISC.OSD"));
                        }

                        if (rs.getString(".MO.DISPLAY_CONF.MISC.ALL_LOCK") != null) {
                           info.setMisc_all_lock(rs.getLong(".MO.DISPLAY_CONF.MISC.ALL_LOCK"));
                        }

                        if (rs.getString(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS") != null) {
                           info.setBasic_panel_status(rs.getLong(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS"));
                        }

                        if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.ALARM_TEMPERATURE") != null) {
                           info.setDiagnosis_alarm_temperature(rs.getLong(".MO.DISPLAY_CONF.DIAGNOSIS.ALARM_TEMPERATURE"));
                        }

                        if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.MONITOR_TEMPERATURE") != null) {
                           info.setDiagnosis_monitor_temperature(rs.getLong(".MO.DISPLAY_CONF.DIAGNOSIS.MONITOR_TEMPERATURE"));
                        }

                        if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.PANEL_ON_TIME") != null) {
                           info.setDiagnosis_panel_on_time(rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.PANEL_ON_TIME"));
                        }
                     }
                  } else {
                     if (info == null) {
                        info = new DeviceDisplayConf();
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.BASIC.POWER") != null) {
                        info.setBasic_power(rs.getString(".MO.DISPLAY_CONF.BASIC.POWER"));
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.BASIC.VOLUME") != null) {
                        info.setBasic_volume(rs.getLong(".MO.DISPLAY_CONF.BASIC.VOLUME"));
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.BASIC.MUTE") != null) {
                        info.setBasic_mute(rs.getLong(".MO.DISPLAY_CONF.BASIC.MUTE"));
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.BASIC.SOURCE") != null) {
                        info.setBasic_source(rs.getLong(".MO.DISPLAY_CONF.BASIC.SOURCE"));
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS") != null) {
                        info.setBasic_panel_status(rs.getLong(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS"));
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS") != null) {
                        info.setPpc_brightness(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS"));
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.TIME.CURRENT_TIME") != null) {
                        info.setTime_current_time(rs.getString(".MO.DISPLAY_CONF.TIME.CURRENT_TIME"));
                     } else if (rs.getString(".MO.DISPLAY_CONF.TIMER.CLOCK") != null) {
                        info.setTime_current_time(rs.getString(".MO.DISPLAY_CONF.TIMER.CLOCK"));
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.TIME.ON_TIME") != null) {
                        info.setTime_on_time(rs.getString(".MO.DISPLAY_CONF.TIME.ON_TIME"));
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.TIME.OFF_TIME") != null) {
                        info.setTime_off_time(rs.getString(".MO.DISPLAY_CONF.TIME.OFF_TIME"));
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_LOCK") != null) {
                        info.setMnt_safety_lock(rs.getLong(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_LOCK"));
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.MISC.REMOCON") != null) {
                        info.setMisc_remocon(rs.getLong(".MO.DISPLAY_CONF.MISC.REMOCON"));
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.MISC.PANEL_LOCK") != null) {
                        info.setMisc_panel_lock(rs.getLong(".MO.DISPLAY_CONF.MISC.PANEL_LOCK"));
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.MISC.OSD") != null) {
                        info.setMisc_osd(rs.getLong(".MO.DISPLAY_CONF.MISC.OSD"));
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.MISC.ALL_LOCK") != null) {
                        info.setMisc_all_lock(rs.getLong(".MO.DISPLAY_CONF.MISC.ALL_LOCK"));
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS") != null) {
                        info.setBasic_panel_status(rs.getLong(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS"));
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.ALARM_TEMPERATURE") != null) {
                        info.setDiagnosis_alarm_temperature(rs.getLong(".MO.DISPLAY_CONF.DIAGNOSIS.ALARM_TEMPERATURE"));
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.MONITOR_TEMPERATURE") != null) {
                        info.setDiagnosis_monitor_temperature(rs.getLong(".MO.DISPLAY_CONF.DIAGNOSIS.MONITOR_TEMPERATURE"));
                     }

                     if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.PANEL_ON_TIME") != null) {
                        info.setDiagnosis_panel_on_time(rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.PANEL_ON_TIME"));
                     }
                  }
               }

               if (type.toUpperCase().equals("ALL_MDC") || type.toUpperCase().equals("ONLY_EXT_MDC")) {
                  if (rs.getString(".MO.DISPLAY_CONF.BASIC.DIRECT_CHANNEL") != null) {
                     info.setBasic_direct_channel(rs.getString(".MO.DISPLAY_CONF.BASIC.DIRECT_CHANNEL"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SPECIALIZED_PICTURE_MODE") != null) {
                     info.setSpecialized_picture_mode(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.SPECIALIZED_PICTURE_MODE"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.MODE") != null) {
                     info.setPv_mode(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.MODE"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.CONTRAST") != null) {
                     info.setPv_contrast(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.CONTRAST"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.BRIGHTNESS") != null) {
                     info.setPv_brightness(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.BRIGHTNESS"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SHARPNESS") != null) {
                     info.setPv_sharpness(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.SHARPNESS"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR") != null) {
                     info.setPv_color(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.TINT") != null) {
                     info.setPv_tint(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.TINT"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TONE") != null) {
                     info.setPv_colortone(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TONE"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TEMPERATURE") != null) {
                     info.setPv_color_temperature(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TEMPERATURE"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SIZE") != null) {
                     info.setPv_size(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.SIZE"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.DIGITAL_NR") != null) {
                     info.setPv_digitalnr(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.DIGITAL_NR"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.FILM_MODE") != null) {
                     info.setPv_filmmode(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.FILM_MODE"));
                  }

                  info.setPv_video_picture_position_size(rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.VIDEO_PICTURE_POSITION_SIZE"));
                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.HDMI_BLACK_LEVEL") != null) {
                     info.setPv_hdmi_black_level(rs.getLong(".MO.DISPLAY_CONF.PICTURE_VIDEO.HDMI_BLACK_LEVEL"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.MAGIC_BRIGHT") != null) {
                     info.setPpc_magic_bright(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.MAGIC_BRIGHT"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_CONTRAST") != null) {
                     info.setPpc_contrast(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.RGB_CONTRAST"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS") != null) {
                     info.setPpc_brightness(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PC_COLOR_TONE") != null) {
                     info.setPpc_colortone(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.PC_COLOR_TONE"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.COLOR_TEMPERATURE") != null) {
                     info.setPpc_color_temperature(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.COLOR_TEMPERATURE"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_RED") != null) {
                     info.setPpc_red(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.RGB_RED"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_GREEN") != null) {
                     info.setPpc_green(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.RGB_GREEN"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BLUE") != null) {
                     info.setPpc_blue(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BLUE"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PICTURE_SIZE") != null) {
                     info.setPpc_size(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.PICTURE_SIZE"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.GAMMA") != null) {
                     info.setPpc_gamma(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.GAMMA"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.HDMI_BLACK_LEVEL") != null) {
                     info.setPpc_hdmi_black_level(rs.getLong(".MO.DISPLAY_CONF.PICTURE_PC.HDMI_BLACK_LEVEL"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SOUND.MODE") != null) {
                     info.setSound_mode(rs.getLong(".MO.DISPLAY_CONF.SOUND.MODE"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SOUND.BASS") != null) {
                     info.setSound_bass(rs.getLong(".MO.DISPLAY_CONF.SOUND.BASS"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SOUND.TREBLE") != null) {
                     info.setSound_treble(rs.getLong(".MO.DISPLAY_CONF.SOUND.TREBLE"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SOUND.BALANCE") != null) {
                     info.setSound_balance(rs.getLong(".MO.DISPLAY_CONF.SOUND.BALANCE"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SOUND.SRS") != null) {
                     info.setSound_srs(rs.getLong(".MO.DISPLAY_CONF.SOUND.SRS"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SOUND.SOUND_EFFECT") != null) {
                     info.setSound_effect(rs.getLong(".MO.DISPLAY_CONF.SOUND.SOUND_EFFECT"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.STATE") != null) {
                     info.setSb_status(rs.getLong(".MO.DISPLAY_CONF.SIGNAL_BALANCE.STATE"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_GAIN") != null) {
                     info.setSb_rgain(rs.getLong(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_GAIN"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_GAIN") != null) {
                     info.setSb_ggain(rs.getLong(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_GAIN"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_GAIN") != null) {
                     info.setSb_bgain(rs.getLong(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_GAIN"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_OFFSET") != null) {
                     info.setSb_r_offset(rs.getLong(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_OFFSET"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_OFFSET") != null) {
                     info.setSb_g_offset(rs.getLong(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_OFFSET"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_OFFSET") != null) {
                     info.setSb_b_offset(rs.getLong(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_OFFSET"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_GAIN") != null) {
                     info.setSb_gain(rs.getLong(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_GAIN"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_SHARPNESS") != null) {
                     info.setSb_sharp(rs.getLong(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_SHARPNESS"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.AUTO") != null) {
                     info.setMnt_auto(rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.AUTO"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.MANUAL") != null) {
                     info.setMnt_manual(rs.getLong(".MO.DISPLAY_CONF.MAINTENANCE.MANUAL"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_TIMER") != null) {
                     info.setMnt_safety_screen_timer(rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_TIMER"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_RUN") != null) {
                     info.setMnt_safety_screen_run(rs.getLong(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_RUN"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.PIXEL_SHIFT") != null) {
                     info.setMnt_pixel_shift(rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.PIXEL_SHIFT"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.OSD_DISPLAY_TYPE") != null) {
                     info.setAdvanced_osd_display_type(rs.getString(".MO.DISPLAY_CONF.ADVANCED.OSD_DISPLAY_TYPE"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.FAN_CONTROL") != null) {
                     info.setAdvanced_fan_control(rs.getLong(".MO.DISPLAY_CONF.ADVANCED.FAN_CONTROL"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.FAN_SPEED") != null) {
                     info.setAdvanced_fan_speed(rs.getLong(".MO.DISPLAY_CONF.ADVANCED.FAN_SPEED"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.AUTO_POWER") != null) {
                     info.setAdvanced_auto_power(rs.getLong(".MO.DISPLAY_CONF.ADVANCED.AUTO_POWER"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.STAND_BY") != null) {
                     info.setAdvanced_stand_by(rs.getLong(".MO.DISPLAY_CONF.ADVANCED.STAND_BY"));
                  }

                  if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.NETWORK_STAND_BY") != null && tmp != null && tmp.getDevice_type() != null && !tmp.getDevice_type().equalsIgnoreCase("iPLAYER")) {
                     info.setNetwork_standby_mode(rs.getString(".MO.DISPLAY_CONF.ADVANCED.NETWORK_STAND_BY"));
                  }
               }
            }
         }

         if (info != null && info.getDevice_model_name() != null) {
            MonitoringManager motMgr = MonitoringManagerImpl.getInstance();
            CurrentPlayingEntity playingEntity = motMgr.getPlayingContent(deviceId);
            if (playingEntity == null) {
               playingEntity = new CurrentPlayingEntity();
            }

            if (info.getBasic_source() != null) {
               playingEntity.setInputSource(info.getBasic_source().intValue());
            }

            if (info.getBasic_direct_channel() != null && info.getBasic_direct_channel().length() > 0) {
               playingEntity.setDirectChannel(info.getBasic_direct_channel());
            }

            if (info.getBasic_panel_status() != null) {
               playingEntity.setPanelStatus((long)info.getBasic_panel_status().intValue());
            }
         }
      } catch (Exception var10) {
         this.logger.error("", var10);
      }

      return info;
   }

   public Map getConnStatusResult(String deviceId, String sessionId, String serviceId) {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = statusManager.getResultset(sessionId, serviceId, deviceId);
      HashMap ret = null;

      try {
         if (rs != null) {
            ret = new HashMap();
            if (rs.getString(".MO.DEVICE_CONF.PROCESS.SWITCH_DEV_TYPE") != null) {
               ret.put(".MO.DEVICE_CONF.PROCESS.SWITCH_DEV_TYPE", rs.getString(".MO.DEVICE_CONF.PROCESS.SWITCH_DEV_TYPE"));
            }
         }
      } catch (Exception var8) {
         this.logger.error("", var8);
      }

      return ret;
   }

   public V2DeviceExternalPowerResource getExternalPowerConfResultSet(String deviceId, String sessionId, String serviceId, String type) {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = statusManager.getResultset(sessionId, serviceId, deviceId);
      V2DeviceExternalPowerResource v2DeviceExternalPowerResource = null;
      if (rs != null) {
         try {
            String externalPowerNumber = null;
            String externalPowerInfo = null;
            if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.EXTERNAL_POWER_NUMBER") != null) {
               externalPowerNumber = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.EXTERNAL_POWER_NUMBER");
            }

            if (rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.EXTERNAL_POWER_INFO") != null) {
               externalPowerInfo = rs.getString(".MO.MONITORING_INFO.SBOX_CHILD.EXTERNAL_POWER_INFO");
            }

            if (null != externalPowerNumber && null != externalPowerInfo) {
               DeviceSboxConfManager sboxManager = DeviceSboxConfManagerImpl.getInstance();
               sboxManager.setExternalPower(deviceId, externalPowerNumber, externalPowerInfo);
               v2DeviceExternalPowerResource = sboxManager.getExternalPower(deviceId);
            }
         } catch (Exception var11) {
            this.logger.error(var11);
         }
      }

      return v2DeviceExternalPowerResource;
   }

   public DeviceSecurityConf getSecurityResultSet(String deviceId, String sessionId, String serviceId, String type) {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = statusManager.getResultset(sessionId, serviceId, deviceId);
      DeviceSecurityConf info = null;

      try {
         if (rs != null) {
            if (info == null) {
               info = new DeviceSecurityConf();
            }

            DeviceSecurityConfManager securityDao = DeviceSecurityConfManagerImpl.getInstance();
            DeviceSecurityConf tmp = securityDao.getDeviceSecurityConf(deviceId);
            if (tmp != null && tmp.getMdc_update_time() != null) {
               info.setMdc_update_time(tmp.getMdc_update_time());
            }

            if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_LOCK") != null) {
               info.setMnt_safety_lock(rs.getLong(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_LOCK"));
            }

            if (rs.getString(".MO.DISPLAY_CONF.MISC.REMOCON") != null) {
               info.setMisc_remocon(rs.getLong(".MO.DISPLAY_CONF.MISC.REMOCON"));
            }

            if (rs.getString(".MO.DISPLAY_CONF.MISC.PANEL_LOCK") != null) {
               info.setMisc_panel_lock(rs.getLong(".MO.DISPLAY_CONF.MISC.PANEL_LOCK"));
            }

            if (rs.getString(".MO.DISPLAY_CONF.MISC.ALL_LOCK") != null) {
               info.setMisc_all_lock(rs.getLong(".MO.DISPLAY_CONF.MISC.ALL_LOCK"));
            }

            if (rs.getString(".MO.SECURITY.MISC.BLOCK_USB_PORT") != null) {
               info.setMisc_block_usb_port(rs.getLong(".MO.DISPLAY_CONF.MISC.BLOCK_USB_PORT"));
            }

            if (rs.getString(".MO.SECURITY.MISC.BLOCK_NETWORK_CONNECTION") != null) {
               info.setMisc_block_network_connection(rs.getLong(".MO.DISPLAY_CONF.MISC.BLOCK_NETWORK_CONNECTION"));
            }

            if (rs.getString(".MO.SECURITY.MISC.SERVER_NETWORK_SETTING_LOCK") != null) {
               info.setMisc_server_network_setting(rs.getLong(".MO.DISPLAY_CONF.MISC.SERVER_NETWORK_SETTING_LOCK"));
            }

            if (rs.getString(".MO.SECURITY.MISC.WHITE_LIST") != null) {
               info.setMisc_white_list(rs.getString(".MO.DISPLAY_CONF.MISC.WHITE_LIST"));
            }

            if (rs.getString(".MO.DISPLAY_CONF.SECURITY.BLUETOOTH_LOCK") != null) {
               info.setBluetooth_lock(rs.getLong(".MO.DISPLAY_CONF.SECURITY.BLUETOOTH_LOCK"));
            }

            if (rs.getString(".MO.DISPLAY_CONF.SECURITY.WIFI_LOCK") != null) {
               info.setWifi_lock(rs.getLong(".MO.DISPLAY_CONF.SECURITY.WIFI_LOCK"));
            }

            if (rs.getString(".MO.DISPLAY_CONF.SECURITY.SOURCE_LOCK") != null) {
               info.setSource_lock(rs.getString(".MO.DISPLAY_CONF.SECURITY.SOURCE_LOCK"));
            }

            if (rs.getString(".MO.DISPLAY_CONF.SECURITY.SCREEN_MONITORING_LOCK") != null) {
               info.setScreen_monitoring_lock(rs.getLong(".MO.DISPLAY_CONF.SECURITY.SCREEN_MONITORING_LOCK"));
            }
         }
      } catch (Exception var10) {
         this.logger.error("", var10);
      }

      return info;
   }

   public DeviceDisplayConf getSettingResultByDisplay(String deviceId, String sessionId, String serviceId) throws SQLException {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = statusManager.getResultset(sessionId, serviceId, deviceId);
      DeviceDisplayConf info = null;
      DeviceDisplayConfManager displayDao = DeviceDisplayConfManagerImpl.getInstance();

      try {
         if (rs != null) {
            DeviceDisplayConf display = displayDao.getDeviceDisplayConf(deviceId);
            info = new DeviceDisplayConf();
            if (rs.getString(".MO.DISPLAY_CONF.BASIC.POWER") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.BASIC.POWER").equals("SUCCESS")) {
                  info.setBasic_power(display.getBasic_power());
               } else {
                  info.setBasic_power("");
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.BASIC.DIRECT_CHANNEL") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.BASIC.DIRECT_CHANNEL").equals("SUCCESS")) {
                  info.setBasic_direct_channel(display.getBasic_direct_channel());
               } else {
                  info.setBasic_direct_channel("");
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.BASIC.VOLUME") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.BASIC.VOLUME").equals("SUCCESS")) {
                  info.setBasic_volume(display.getBasic_volume());
               } else {
                  info.setBasic_volume(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.BASIC.MUTE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.BASIC.MUTE").equals("SUCCESS")) {
                  info.setBasic_mute(display.getBasic_mute());
               } else {
                  info.setBasic_mute(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.BASIC.SOURCE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.BASIC.SOURCE").equals("SUCCESS")) {
                  info.setBasic_source(display.getBasic_source());
               } else {
                  info.setBasic_source(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS").equals("SUCCESS")) {
                  info.setBasic_panel_status(display.getBasic_panel_status());
               } else {
                  info.setBasic_panel_status(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS").equals("SUCCESS")) {
                  info.setPpc_brightness(display.getPpc_brightness());
               } else {
                  info.setPpc_brightness(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.TIME.ON_TIME") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.TIME.ON_TIME").equals("SUCCESS")) {
                  info.setTime_on_time(display.getTime_on_time());
               } else {
                  info.setTime_on_time("");
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.TIME.OFF_TIME") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.TIME.OFF_TIME").equals("SUCCESS")) {
                  info.setTime_off_time(display.getTime_off_time());
               } else {
                  info.setTime_off_time("");
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_LOCK") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_LOCK").equals("SUCCESS")) {
                  info.setMnt_safety_lock(display.getMnt_safety_lock());
               } else {
                  info.setMnt_safety_lock(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.MISC.REMOCON") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.MISC.REMOCON").equals("SUCCESS")) {
                  info.setMisc_remocon(display.getMisc_remocon());
               } else {
                  info.setMisc_remocon(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.MISC.PANEL_LOCK") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.MISC.PANEL_LOCK").equals("SUCCESS")) {
                  info.setMisc_panel_lock(display.getMisc_panel_lock());
               } else {
                  info.setMisc_panel_lock(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.MISC.OSD") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.MISC.OSD").equals("SUCCESS")) {
                  info.setMisc_osd(display.getMisc_osd());
               } else {
                  info.setMisc_osd(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.MISC.ALL_LOCK") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.MISC.ALL_LOCK").equals("SUCCESS")) {
                  info.setMisc_all_lock(display.getMisc_all_lock());
               } else {
                  info.setMisc_all_lock(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS").equals("SUCCESS")) {
                  info.setBasic_panel_status(display.getBasic_panel_status());
               } else {
                  info.setBasic_panel_status(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.ALARM_TEMPERATURE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.ALARM_TEMPERATURE").equals("SUCCESS")) {
                  info.setDiagnosis_alarm_temperature(display.getDiagnosis_alarm_temperature());
               } else {
                  info.setDiagnosis_alarm_temperature(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.MONITOR_TEMPERATURE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.MONITOR_TEMPERATURE").equals("SUCCESS")) {
                  info.setDiagnosis_monitor_temperature(display.getDiagnosis_monitor_temperature());
               } else {
                  info.setDiagnosis_monitor_temperature(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.PANEL_ON_TIME") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.PANEL_ON_TIME").equals("SUCCESS")) {
                  info.setDiagnosis_panel_on_time(display.getDiagnosis_panel_on_time());
               } else {
                  info.setDiagnosis_panel_on_time("");
               }
            }
         }
      } catch (RMQLException var9) {
         this.logger.error("", var9);
      }

      return info;
   }

   public DeviceDisplayConf getSettingResultByDisplayAll(String deviceId, String sessionId, String serviceId) throws SQLException {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = statusManager.getResultset(sessionId, serviceId, deviceId);
      DeviceDisplayConf info = null;
      DeviceDisplayConfManager displayDao = DeviceDisplayConfManagerImpl.getInstance();

      try {
         if (rs != null) {
            DeviceDisplayConf display = displayDao.getDeviceDisplayConf(deviceId);
            info = new DeviceDisplayConf();
            if (rs.getString(".MO.DISPLAY_CONF.BASIC.POWER") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.BASIC.POWER").equals("SUCCESS")) {
                  info.setBasic_power(display.getBasic_power());
               } else {
                  info.setBasic_power("");
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.BASIC.VOLUME") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.BASIC.VOLUME").equals("SUCCESS")) {
                  info.setBasic_volume(display.getBasic_volume());
               } else {
                  info.setBasic_volume(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.BASIC.MUTE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.BASIC.MUTE").equals("SUCCESS")) {
                  info.setBasic_mute(display.getBasic_mute());
               } else {
                  info.setBasic_mute(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.BASIC.SOURCE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.BASIC.SOURCE").equals("SUCCESS")) {
                  info.setBasic_source(display.getBasic_source());
               } else {
                  info.setBasic_source(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS").equals("SUCCESS")) {
                  info.setBasic_panel_status(display.getBasic_panel_status());
               } else {
                  info.setBasic_panel_status(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS").equals("SUCCESS")) {
                  info.setPpc_brightness(display.getPpc_brightness());
               } else {
                  info.setPpc_brightness(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.TIME.ON_TIME") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.TIME.ON_TIME").equals("SUCCESS")) {
                  info.setTime_on_time(display.getTime_on_time());
               } else {
                  info.setTime_on_time("");
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.TIME.OFF_TIME") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.TIME.OFF_TIME").equals("SUCCESS")) {
                  info.setTime_off_time(display.getTime_off_time());
               } else {
                  info.setTime_off_time("");
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_LOCK") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_LOCK").equals("SUCCESS")) {
                  info.setMnt_safety_lock(display.getMnt_safety_lock());
               } else {
                  info.setMnt_safety_lock(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.MISC.REMOCON") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.MISC.REMOCON").equals("SUCCESS")) {
                  info.setMisc_remocon(display.getMisc_remocon());
               } else {
                  info.setMisc_remocon(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.MISC.PANEL_LOCK") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.MISC.PANEL_LOCK").equals("SUCCESS")) {
                  info.setMisc_panel_lock(display.getMisc_panel_lock());
               } else {
                  info.setMisc_panel_lock(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.MISC.OSD") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.MISC.OSD").equals("SUCCESS")) {
                  info.setMisc_osd(display.getMisc_osd());
               } else {
                  info.setMisc_osd(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.MISC.ALL_LOCK") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.MISC.ALL_LOCK").equals("SUCCESS")) {
                  info.setMisc_all_lock(display.getMisc_all_lock());
               } else {
                  info.setMisc_all_lock(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.BASIC.PANEL_STATUS").equals("SUCCESS")) {
                  info.setBasic_panel_status(display.getBasic_panel_status());
               } else {
                  info.setBasic_panel_status(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.ALARM_TEMPERATURE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.ALARM_TEMPERATURE").equals("SUCCESS")) {
                  info.setDiagnosis_alarm_temperature(display.getDiagnosis_alarm_temperature());
               } else {
                  info.setDiagnosis_alarm_temperature(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.MONITOR_TEMPERATURE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.MONITOR_TEMPERATURE").equals("SUCCESS")) {
                  info.setDiagnosis_monitor_temperature(display.getDiagnosis_monitor_temperature());
               } else {
                  info.setDiagnosis_monitor_temperature(100L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.PANEL_ON_TIME") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.DIAGNOSIS.PANEL_ON_TIME").equals("SUCCESS")) {
                  info.setDiagnosis_panel_on_time(display.getDiagnosis_panel_on_time());
               } else {
                  info.setDiagnosis_panel_on_time("");
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.BASIC.DIRECT_CHANNEL") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.BASIC.DIRECT_CHANNEL").equals("SUCCESS")) {
                  info.setBasic_direct_channel(display.getBasic_direct_channel());
               } else {
                  info.setBasic_direct_channel("");
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SPECIALIZED_PICTURE_MODE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SPECIALIZED_PICTURE_MODE").equals("SUCCESS")) {
                  info.setSpecialized_picture_mode(display.getSpecialized_picture_mode());
               } else {
                  info.setSpecialized_picture_mode(-1L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.MODE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.MODE").equals("SUCCESS")) {
                  info.setPv_mode(display.getPv_mode());
               } else {
                  info.setPv_mode(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.CONTRAST") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.CONTRAST").equals("SUCCESS")) {
                  info.setPv_contrast(display.getPv_contrast());
               } else {
                  info.setPv_contrast(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.BRIGHTNESS") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.BRIGHTNESS").equals("SUCCESS")) {
                  info.setPv_brightness(display.getPv_brightness());
               } else {
                  info.setPv_brightness(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SHARPNESS") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SHARPNESS").equals("SUCCESS")) {
                  info.setPv_sharpness(display.getPv_sharpness());
               } else {
                  info.setPv_sharpness(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR").equals("SUCCESS")) {
                  info.setPv_color(display.getPv_color());
               } else {
                  info.setPv_color(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.TINT") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.TINT").equals("SUCCESS")) {
                  info.setPv_tint(display.getPv_tint());
               } else {
                  info.setPv_tint(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TONE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TONE").equals("SUCCESS")) {
                  info.setPv_colortone(display.getPv_colortone());
               } else {
                  info.setPv_colortone(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TEMPERATURE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TEMPERATURE").equals("SUCCESS")) {
                  info.setPv_color_temperature(display.getPv_color_temperature());
               } else {
                  info.setPv_color_temperature(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SIZE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SIZE").equals("SUCCESS")) {
                  info.setPv_size(display.getPv_size());
               } else {
                  info.setPv_size(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.DIGITAL_NR") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.DIGITAL_NR").equals("SUCCESS")) {
                  info.setPv_digitalnr(display.getPv_digitalnr());
               } else {
                  info.setPv_digitalnr(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.FILM_MODE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.FILM_MODE").equals("SUCCESS")) {
                  info.setPv_filmmode(display.getPv_filmmode());
               } else {
                  info.setPv_filmmode(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.VIDEO_PICTURE_POSITION_SIZE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.VIDEO_PICTURE_POSITION_SIZE").equals("SUCCESS")) {
                  info.setPv_video_picture_position_size(display.getPv_video_picture_position_size());
               } else {
                  info.setPv_video_picture_position_size("");
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.HDMI_BLACK_LEVEL") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.HDMI_BLACK_LEVEL").equals("SUCCESS")) {
                  info.setPv_hdmi_black_level(display.getPv_hdmi_black_level());
               } else {
                  info.setPv_hdmi_black_level(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.MAGIC_BRIGHT") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.MAGIC_BRIGHT").equals("SUCCESS")) {
                  info.setPpc_magic_bright(display.getPpc_magic_bright());
               } else {
                  info.setPpc_magic_bright(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_CONTRAST") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_CONTRAST").equals("SUCCESS")) {
                  info.setPpc_contrast(display.getPpc_contrast());
               } else {
                  info.setPpc_contrast(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS").equals("SUCCESS")) {
                  info.setPpc_brightness(display.getPpc_brightness());
               } else {
                  info.setPpc_brightness(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PC_COLOR_TONE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PC_COLOR_TONE").equals("SUCCESS")) {
                  info.setPpc_colortone(display.getPpc_colortone());
               } else {
                  info.setPpc_colortone(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.COLOR_TEMPERATURE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.COLOR_TEMPERATURE").equals("SUCCESS")) {
                  info.setPpc_color_temperature(display.getPpc_color_temperature());
               } else {
                  info.setPpc_color_temperature(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_RED") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_RED").equals("SUCCESS")) {
                  info.setPpc_red(display.getPpc_red());
               } else {
                  info.setPpc_red(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_GREEN") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_GREEN").equals("SUCCESS")) {
                  info.setPpc_green(display.getPpc_green());
               } else {
                  info.setPpc_green(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BLUE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BLUE").equals("SUCCESS")) {
                  info.setPpc_blue(display.getPpc_blue());
               } else {
                  info.setPpc_blue(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PICTURE_SIZE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PICTURE_SIZE").equals("SUCCESS")) {
                  info.setPpc_size(display.getPpc_size());
               } else {
                  info.setPpc_size(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.GAMMA") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.GAMMA").equals("SUCCESS")) {
                  info.setPpc_gamma(display.getPpc_gamma());
               } else {
                  info.setPpc_gamma(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.HDMI_BLACK_LEVEL") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.HDMI_BLACK_LEVEL").equals("SUCCESS")) {
                  info.setPpc_hdmi_black_level(display.getPpc_hdmi_black_level());
               } else {
                  info.setPpc_hdmi_black_level(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SOUND.MODE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SOUND.MODE").equals("SUCCESS")) {
                  info.setSound_mode(display.getSound_mode());
               } else {
                  info.setSound_mode(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SOUND.BASS") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SOUND.BASS").equals("SUCCESS")) {
                  info.setSound_bass(display.getSound_bass());
               } else {
                  info.setSound_bass(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SOUND.TREBLE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SOUND.TREBLE").equals("SUCCESS")) {
                  info.setSound_treble(display.getSound_treble());
               } else {
                  info.setSound_treble(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SOUND.BALANCE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SOUND.BALANCE").equals("SUCCESS")) {
                  info.setSound_balance(display.getSound_balance());
               } else {
                  info.setSound_balance(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SOUND.SRS") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SOUND.SRS").equals("SUCCESS")) {
                  info.setSound_srs(display.getSound_srs());
               } else {
                  info.setSound_srs(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.STATE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.STATE").equals("SUCCESS")) {
                  info.setSb_status(display.getSb_status());
               } else {
                  info.setSb_status(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_GAIN") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_GAIN").equals("SUCCESS")) {
                  info.setSb_gain(display.getSb_gain());
               } else {
                  info.setSb_gain(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_SHARPNESS") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_SHARPNESS").equals("SUCCESS")) {
                  info.setSb_sharp(display.getSb_sharp());
               } else {
                  info.setSb_sharp(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_GAIN") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_GAIN").equals("SUCCESS")) {
                  info.setSb_rgain(display.getSb_rgain());
               } else {
                  info.setSb_rgain(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_GAIN") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_GAIN").equals("SUCCESS")) {
                  info.setSb_ggain(display.getSb_ggain());
               } else {
                  info.setSb_ggain(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_GAIN") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_GAIN").equals("SUCCESS")) {
                  info.setSb_bgain(display.getSb_bgain());
               } else {
                  info.setSb_bgain(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_OFFSET") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_OFFSET").equals("SUCCESS")) {
                  info.setSb_r_offset(display.getSb_r_offset());
               } else {
                  info.setSb_r_offset(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_OFFSET") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_OFFSET").equals("SUCCESS")) {
                  info.setSb_g_offset(display.getSb_g_offset());
               } else {
                  info.setSb_g_offset(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_OFFSET") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_OFFSET").equals("SUCCESS")) {
                  info.setSb_b_offset(display.getSb_b_offset());
               } else {
                  info.setSb_b_offset(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.AUTO") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.AUTO").equals("SUCCESS")) {
                  info.setMnt_auto(display.getMnt_auto());
               } else {
                  info.setMnt_auto("");
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.MANUAL") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.MANUAL").equals("SUCCESS")) {
                  info.setMnt_manual(display.getMnt_manual());
               } else {
                  info.setMnt_manual(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_TIMER") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_TIMER").equals("SUCCESS")) {
                  info.setMnt_safety_screen_timer(display.getMnt_safety_screen_timer());
               } else {
                  info.setMnt_safety_screen_timer("");
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_RUN") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_RUN").equals("SUCCESS")) {
                  info.setMnt_safety_screen_run(display.getMnt_safety_screen_run());
               } else {
                  info.setMnt_safety_screen_run(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.PIXEL_SHIFT") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.PIXEL_SHIFT").equals("SUCCESS")) {
                  info.setMnt_pixel_shift(display.getMnt_pixel_shift());
               } else {
                  info.setMnt_pixel_shift("");
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.RJ45_SETTING_REFRESH") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.RJ45_SETTING_REFRESH").equals("SUCCESS")) {
                  info.setAdvanced_rj45_setting_refresh(display.getAdvanced_rj45_setting_refresh());
               } else {
                  info.setAdvanced_rj45_setting_refresh(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.OSD_DISPLAY_TYPE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.OSD_DISPLAY_TYPE").equals("SUCCESS")) {
                  info.setAdvanced_osd_display_type(display.getAdvanced_osd_display_type());
               } else {
                  info.setAdvanced_osd_display_type("");
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.FAN_CONTROL") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.FAN_CONTROL").equals("SUCCESS")) {
                  info.setAdvanced_fan_control(display.getAdvanced_fan_control());
               } else {
                  info.setAdvanced_fan_control(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.FAN_SPEED") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.FAN_SPEED").equals("SUCCESS")) {
                  info.setAdvanced_fan_control(0L);
                  info.setAdvanced_fan_speed(display.getAdvanced_fan_speed());
               } else {
                  info.setAdvanced_fan_speed(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.RESET") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.RESET").equals("SUCCESS")) {
                  info.setAdvanced_reset(display.getAdvanced_reset());
               } else {
                  info.setAdvanced_reset(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.AUTO_POWER") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.AUTO_POWER").equals("SUCCESS")) {
                  info.setAdvanced_auto_power(display.getAdvanced_auto_power());
               } else {
                  info.setAdvanced_auto_power(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.USER_AUTO_COLOR") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.USER_AUTO_COLOR").equals("SUCCESS")) {
                  info.setAdvanced_user_auto_color(display.getAdvanced_user_auto_color());
               } else {
                  info.setAdvanced_user_auto_color(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.STAND_BY") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.STAND_BY").equals("SUCCESS")) {
                  info.setAdvanced_stand_by(display.getAdvanced_stand_by());
               } else {
                  info.setAdvanced_stand_by(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.NETWORK_STAND_BY") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.NETWORK_STAND_BY").equals("SUCCESS")) {
                  info.setNetwork_standby_mode(display.getNetwork_standby_mode());
               } else {
                  info.setNetwork_standby_mode("F");
               }
            }
         }
      } catch (RMQLException var9) {
         this.logger.error("", var9);
      }

      return info;
   }

   public DeviceDisplayConf getSettingResultByDisplayExt(String deviceId, String sessionId, String serviceId) {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = statusManager.getResultset(sessionId, serviceId, deviceId);
      DeviceDisplayConf info = null;
      DeviceDisplayConfManager displayDao = DeviceDisplayConfManagerImpl.getInstance();

      try {
         if (rs != null) {
            DeviceDisplayConf display = displayDao.getDeviceDisplayConf(deviceId);
            info = new DeviceDisplayConf();
            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SPECIALIZED_PICTURE_MODE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SPECIALIZED_PICTURE_MODE").equals("SUCCESS")) {
                  info.setSpecialized_picture_mode(display.getSpecialized_picture_mode());
               } else {
                  info.setSpecialized_picture_mode(-1L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.MODE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.MODE").equals("SUCCESS")) {
                  info.setPv_mode(display.getPv_mode());
               } else {
                  info.setPv_mode(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.CONTRAST") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.CONTRAST").equals("SUCCESS")) {
                  info.setPv_contrast(display.getPv_contrast());
               } else {
                  info.setPv_contrast(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.BRIGHTNESS") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.BRIGHTNESS").equals("SUCCESS")) {
                  info.setPv_brightness(display.getPv_brightness());
               } else {
                  info.setPv_brightness(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SHARPNESS") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SHARPNESS").equals("SUCCESS")) {
                  info.setPv_sharpness(display.getPv_sharpness());
               } else {
                  info.setPv_sharpness(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR").equals("SUCCESS")) {
                  info.setPv_color(display.getPv_color());
               } else {
                  info.setPv_color(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.TINT") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.TINT").equals("SUCCESS")) {
                  info.setPv_tint(display.getPv_tint());
               } else {
                  info.setPv_tint(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TONE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TONE").equals("SUCCESS")) {
                  info.setPv_colortone(display.getPv_colortone());
               } else {
                  info.setPv_colortone(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TEMPERATURE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.COLOR_TEMPERATURE").equals("SUCCESS")) {
                  info.setPv_color_temperature(display.getPv_color_temperature());
               } else {
                  info.setPv_color_temperature(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SIZE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.SIZE").equals("SUCCESS")) {
                  info.setPv_size(display.getPv_size());
               } else {
                  info.setPv_size(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.DIGITAL_NR") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.DIGITAL_NR").equals("SUCCESS")) {
                  info.setPv_digitalnr(display.getPv_digitalnr());
               } else {
                  info.setPv_digitalnr(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.FILM_MODE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.FILM_MODE").equals("SUCCESS")) {
                  info.setPv_filmmode(display.getPv_filmmode());
               } else {
                  info.setPv_filmmode(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.VIDEO_PICTURE_POSITION_SIZE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.VIDEO_PICTURE_POSITION_SIZE").equals("SUCCESS")) {
                  info.setPv_video_picture_position_size(display.getPv_video_picture_position_size());
               } else {
                  info.setPv_video_picture_position_size("");
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.HDMI_BLACK_LEVEL") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_VIDEO.HDMI_BLACK_LEVEL").equals("SUCCESS")) {
                  info.setPv_hdmi_black_level(display.getPv_hdmi_black_level());
               } else {
                  info.setPv_hdmi_black_level(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.MAGIC_BRIGHT") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.MAGIC_BRIGHT").equals("SUCCESS")) {
                  info.setPpc_magic_bright(display.getPpc_magic_bright());
               } else {
                  info.setPpc_magic_bright(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_CONTRAST") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_CONTRAST").equals("SUCCESS")) {
                  info.setPpc_contrast(display.getPpc_contrast());
               } else {
                  info.setPpc_contrast(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BRIGHTNESS").equals("SUCCESS")) {
                  info.setPpc_brightness(display.getPpc_brightness());
               } else {
                  info.setPpc_brightness(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PC_COLOR_TONE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PC_COLOR_TONE").equals("SUCCESS")) {
                  info.setPpc_colortone(display.getPpc_colortone());
               } else {
                  info.setPpc_colortone(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.COLOR_TEMPERATURE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.COLOR_TEMPERATURE").equals("SUCCESS")) {
                  info.setPpc_color_temperature(display.getPpc_color_temperature());
               } else {
                  info.setPpc_color_temperature(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_RED") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_RED").equals("SUCCESS")) {
                  info.setPpc_red(display.getPpc_red());
               } else {
                  info.setPpc_red(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_GREEN") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_GREEN").equals("SUCCESS")) {
                  info.setPpc_green(display.getPpc_green());
               } else {
                  info.setPpc_green(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BLUE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.RGB_BLUE").equals("SUCCESS")) {
                  info.setPpc_blue(display.getPpc_blue());
               } else {
                  info.setPpc_blue(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PICTURE_SIZE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.PICTURE_SIZE").equals("SUCCESS")) {
                  info.setPpc_size(display.getPpc_size());
               } else {
                  info.setPpc_size(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.GAMMA") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.GAMMA").equals("SUCCESS")) {
                  info.setPpc_gamma(display.getPpc_gamma());
               } else {
                  info.setPpc_gamma(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.HDMI_BLACK_LEVEL") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.PICTURE_PC.HDMI_BLACK_LEVEL").equals("SUCCESS")) {
                  info.setPpc_hdmi_black_level(display.getPpc_hdmi_black_level());
               } else {
                  info.setPpc_hdmi_black_level(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SOUND.MODE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SOUND.MODE").equals("SUCCESS")) {
                  info.setSound_mode(display.getSound_mode());
               } else {
                  info.setSound_mode(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SOUND.BASS") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SOUND.BASS").equals("SUCCESS")) {
                  info.setSound_bass(display.getSound_bass());
               } else {
                  info.setSound_bass(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SOUND.TREBLE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SOUND.TREBLE").equals("SUCCESS")) {
                  info.setSound_treble(display.getSound_treble());
               } else {
                  info.setSound_treble(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SOUND.BALANCE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SOUND.BALANCE").equals("SUCCESS")) {
                  info.setSound_balance(display.getSound_balance());
               } else {
                  info.setSound_balance(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SOUND.SRS") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SOUND.SRS").equals("SUCCESS")) {
                  info.setSound_srs(display.getSound_srs());
               } else {
                  info.setSound_srs(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.STATE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.STATE").equals("SUCCESS")) {
                  info.setSb_status(display.getSb_status());
               } else {
                  info.setSb_status(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_GAIN") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_GAIN").equals("SUCCESS")) {
                  info.setSb_gain(display.getSb_gain());
               } else {
                  info.setSb_gain(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_SHARPNESS") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.SB_SHARPNESS").equals("SUCCESS")) {
                  info.setSb_sharp(display.getSb_sharp());
               } else {
                  info.setSb_sharp(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_GAIN") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_GAIN").equals("SUCCESS")) {
                  info.setSb_rgain(display.getSb_rgain());
               } else {
                  info.setSb_rgain(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_GAIN") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_GAIN").equals("SUCCESS")) {
                  info.setSb_ggain(display.getSb_ggain());
               } else {
                  info.setSb_ggain(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_GAIN") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_GAIN").equals("SUCCESS")) {
                  info.setSb_bgain(display.getSb_bgain());
               } else {
                  info.setSb_bgain(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_OFFSET") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.R_OFFSET").equals("SUCCESS")) {
                  info.setSb_r_offset(display.getSb_r_offset());
               } else {
                  info.setSb_r_offset(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_OFFSET") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.G_OFFSET").equals("SUCCESS")) {
                  info.setSb_g_offset(display.getSb_g_offset());
               } else {
                  info.setSb_g_offset(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_OFFSET") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.SIGNAL_BALANCE.B_OFFSET").equals("SUCCESS")) {
                  info.setSb_b_offset(display.getSb_b_offset());
               } else {
                  info.setSb_b_offset(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.AUTO") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.AUTO").equals("SUCCESS")) {
                  info.setMnt_auto(display.getMnt_auto());
                  info.setMnt_manual(50L);
               } else {
                  info.setMnt_auto("");
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.MANUAL") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.MANUAL").equals("SUCCESS")) {
                  info.setMnt_manual(display.getMnt_manual());
               } else {
                  info.setMnt_manual(255L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_TIMER") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_TIMER").equals("SUCCESS")) {
                  info.setMnt_safety_screen_timer(display.getMnt_safety_screen_timer());
               } else {
                  info.setMnt_safety_screen_timer("");
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_RUN") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.SAFETY_SCREEN_RUN").equals("SUCCESS")) {
                  info.setMnt_safety_screen_run(display.getMnt_safety_screen_run());
               } else {
                  info.setMnt_safety_screen_run(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.PIXEL_SHIFT") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.MAINTENANCE.PIXEL_SHIFT").equals("SUCCESS")) {
                  info.setMnt_pixel_shift(display.getMnt_pixel_shift());
               } else {
                  info.setMnt_pixel_shift("");
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.RJ45_SETTING_REFRESH") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.RJ45_SETTING_REFRESH").equals("SUCCESS")) {
                  info.setAdvanced_rj45_setting_refresh(display.getAdvanced_rj45_setting_refresh());
               } else {
                  info.setAdvanced_rj45_setting_refresh(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.OSD_DISPLAY_TYPE") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.OSD_DISPLAY_TYPE").equals("SUCCESS")) {
                  info.setAdvanced_osd_display_type(display.getAdvanced_osd_display_type());
               } else {
                  info.setAdvanced_osd_display_type("");
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.FAN_CONTROL") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.FAN_CONTROL").equals("SUCCESS")) {
                  info.setAdvanced_fan_control(display.getAdvanced_fan_control());
               } else {
                  info.setAdvanced_fan_control(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.FAN_SPEED") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.FAN_SPEED").equals("SUCCESS")) {
                  info.setAdvanced_fan_speed(display.getAdvanced_fan_speed());
                  info.setAdvanced_fan_control(0L);
               } else {
                  info.setAdvanced_fan_speed(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.RESET") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.RESET").equals("SUCCESS")) {
                  info.setAdvanced_reset(display.getAdvanced_reset());
               } else {
                  info.setAdvanced_reset(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.AUTO_POWER") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.AUTO_POWER").equals("SUCCESS")) {
                  info.setAdvanced_auto_power(display.getAdvanced_auto_power());
               } else {
                  info.setAdvanced_auto_power(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.USER_AUTO_COLOR") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.USER_AUTO_COLOR").equals("SUCCESS")) {
                  info.setAdvanced_user_auto_color(display.getAdvanced_user_auto_color());
               } else {
                  info.setAdvanced_user_auto_color(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.STAND_BY") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.STAND_BY").equals("SUCCESS")) {
                  info.setAdvanced_stand_by(display.getAdvanced_stand_by());
               } else {
                  info.setAdvanced_stand_by(99L);
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.NETWORK_STAND_BY") != null) {
               if (rs.getString(".MO.DISPLAY_CONF.ADVANCED.NETWORK_STAND_BY").equals("SUCCESS")) {
                  info.setNetwork_standby_mode(display.getNetwork_standby_mode());
               } else {
                  info.setNetwork_standby_mode("F");
               }
            }
         }
      } catch (SQLException var9) {
         this.logger.error("", var9);
      } catch (RMQLException var10) {
         this.logger.error("", var10);
      }

      return info;
   }

   public DeviceGeneralConf getSettingResultByGeneralConf(String deviceId, String sessionId, String serviceId) {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = statusManager.getResultset(sessionId, serviceId, deviceId);
      DeviceGeneralConf info = null;
      DeviceGeneralConfManager generalDao = DeviceGeneralConfManagerImpl.getInstance();

      try {
         if (rs != null) {
            DeviceGeneralConf generalConf = generalDao.getDeviceGeneralConf(deviceId);
            info = new DeviceGeneralConf();
            if (rs.getString(".MO.DEVICE_CONF.GENERAL.DEVICE_NAME") != null) {
               if (rs.getString(".MO.DEVICE_CONF.GENERAL.DEVICE_NAME").equals("SUCCESS")) {
                  info.setDevice_name(generalConf.getDevice_name());
               } else {
                  info.setDevice_name("FAIL");
               }
            }
         }
      } catch (Exception var9) {
         this.logger.error("", var9);
      }

      return info;
   }

   public DeviceNetworkConf getSettingResultByNetworkConf(String deviceId, String sessionId, String serviceId) {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = statusManager.getResultset(sessionId, serviceId, deviceId);
      DeviceNetworkConf info = null;
      DeviceNetworkConfManager networkDao = DeviceNetworkConfManagerImpl.getInstance();

      try {
         if (rs != null) {
            DeviceNetworkConf networkConf = networkDao.getDeviceNetworkConf(deviceId);
            info = new DeviceNetworkConf();
            if (rs.getString(".MO.DEVICE_CONF.NETWORK.PORT") != null) {
               if (rs.getString(".MO.DEVICE_CONF.NETWORK.PORT").equals("SUCCESS")) {
                  info.setPort(networkConf.getPort());
               } else {
                  info.setPort(-1L);
               }
            }
         }
      } catch (Exception var9) {
         this.logger.error("", var9);
      }

      return info;
   }

   public DeviceSystemSetupConf getSettingResultBySystemSetup(String deviceId, String sessionId, String serviceId) {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = statusManager.getResultset(sessionId, serviceId, deviceId);
      DeviceSystemSetupConf info = null;
      DeviceSystemSetupConfManagerImpl systemSetupDao = DeviceSystemSetupConfManagerImpl.getInstance();

      try {
         if (rs != null) {
            DeviceSystemSetupConf systemSetup = systemSetupDao.getDeviceSystemSetupConf(deviceId);
            info = new DeviceSystemSetupConf();
            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.TIME_ZONE_INDEX") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.TIME_ZONE_INDEX").equals("SUCCESS")) {
                  info.setTime_zone_index(systemSetup.getTime_zone_index());
               } else {
                  info.setTime_zone_index("FAIL");
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DAY_LIGHT_SAVING") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DAY_LIGHT_SAVING").equals("SUCCESS")) {
                  info.setDay_light_saving(systemSetup.getDay_light_saving());
               } else {
                  info.setDay_light_saving(false);
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DAY_LIGHT_SAVING_MANUAL") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DAY_LIGHT_SAVING_MANUAL").equals("SUCCESS")) {
                  info.setDay_light_saving_manual(systemSetup.getDay_light_saving_manual());
               } else {
                  info.setDay_light_saving_manual("FAIL");
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.AUTO_TIME_SETTING") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.AUTO_TIME_SETTING").equals("SUCCESS")) {
                  info.setAuto_time_setting(systemSetup.getAuto_time_setting());
               } else {
                  info.setAuto_time_setting(false);
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.ON_TIMER_SETTING") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.ON_TIMER_SETTING").equals("SUCCESS")) {
                  info.setOn_timer_setting(systemSetup.getOn_timer_setting());
               } else {
                  info.setOn_timer_setting(false);
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.OFF_TIMER_SETTING") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.OFF_TIMER_SETTING").equals("SUCCESS")) {
                  info.setOff_timer_setting(systemSetup.getOff_timer_setting());
               } else {
                  info.setOff_timer_setting(false);
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.MAGICINFO_SERVER_URL") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.MAGICINFO_SERVER_URL").equals("SUCCESS")) {
                  info.setMagicinfo_server_url(systemSetup.getMagicinfo_server_url());
               } else {
                  info.setMagicinfo_server_url("FAIL");
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.TUNNELING_SERVER") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.TUNNELING_SERVER").equals("SUCCESS")) {
                  info.setTunneling_server(systemSetup.getTunneling_server());
               } else {
                  info.setTunneling_server("FAIL");
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.TRIGGER_INTERVAL") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.TRIGGER_INTERVAL").equals("SUCCESS")) {
                  info.setTrigger_interval(systemSetup.getTrigger_interval());
               } else {
                  info.setTrigger_interval(-1L);
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.MONITORING_INTERVAL") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.MONITORING_INTERVAL").equals("SUCCESS")) {
                  info.setMonitoring_interval(systemSetup.getMonitoring_interval());
               } else {
                  info.setMonitoring_interval(-1L);
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.FTP_CONNECT_MODE") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.FTP_CONNECT_MODE").equals("SUCCESS")) {
                  info.setFtp_connect_mode(systemSetup.getFtp_connect_mode());
               } else {
                  info.setFtp_connect_mode("FAIL");
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.REPOSITORY_PATH") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.REPOSITORY_PATH").equals("SUCCESS")) {
                  info.setRepository_path(systemSetup.getRepository_path());
               } else {
                  info.setRepository_path("FAIL");
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.SCREEN_CAPTURE_INTERVAL") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.SCREEN_CAPTURE_INTERVAL").equals("SUCCESS")) {
                  info.setScreen_capture_interval(systemSetup.getScreen_capture_interval());
               } else {
                  info.setScreen_capture_interval(-1L);
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.BG_COLOR") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.BG_COLOR").equals("SUCCESS")) {
                  info.setBg_color(systemSetup.getBg_color());
               } else {
                  info.setBg_color("FAIL");
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PROXY_SETTING") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PROXY_SETTING").equals("SUCCESS")) {
                  info.setProxy_setting(systemSetup.getProxy_setting());
               } else {
                  info.setProxy_setting("FAIL");
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PROXY_EXCLUDE_LIST") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PROXY_EXCLUDE_LIST").equals("SUCCESS")) {
                  info.setProxy_exclude_list(systemSetup.getProxy_exclude_list());
               } else {
                  info.setProxy_exclude_list("FAIL");
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.CONNECTION_LIMIT_TIME") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.CONNECTION_LIMIT_TIME").equals("SUCCESS")) {
                  info.setConnection_limit_time(systemSetup.getConnection_limit_time());
               } else {
                  info.setConnection_limit_time(-1L);
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.MNT_FOLDER_PATH") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.MNT_FOLDER_PATH").equals("SUCCESS")) {
                  info.setMnt_folder_path(systemSetup.getMnt_folder_path());
               } else {
                  info.setMnt_folder_path("FAIL");
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.SYSTEM_RESTART_INTERVAL") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.SYSTEM_RESTART_INTERVAL").equals("SUCCESS")) {
                  info.setSystem_restart_interval(systemSetup.getSystem_restart_interval());
               } else {
                  info.setSystem_restart_interval("FAIL");
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.LOG_MNT") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.LOG_MNT").equals("SUCCESS")) {
                  info.setLog_mnt(systemSetup.getLog_mnt());
               } else {
                  info.setLog_mnt("FAIL");
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PROOF_OF_PLAY_MNT") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PROOF_OF_PLAY_MNT").equals("SUCCESS")) {
                  info.setProof_of_play_mnt(systemSetup.getProof_of_play_mnt());
               } else {
                  info.setProof_of_play_mnt("FAIL");
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.CONTENT_MNT") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.CONTENT_MNT").equals("SUCCESS")) {
                  info.setContent_mnt(systemSetup.getContent_mnt());
               } else {
                  info.setContent_mnt("FAIL");
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.SCREEN_ROTATION") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.SCREEN_ROTATION").equals("SUCCESS")) {
                  info.setScreen_rotation(systemSetup.getScreen_rotation());
               } else {
                  info.setScreen_rotation(-1L);
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PLAY_MODE") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PLAY_MODE").equals("SUCCESS")) {
                  info.setPlay_mode(systemSetup.getPlay_mode());
               } else {
                  info.setPlay_mode(-1L);
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.RESET_PASSWORD") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.RESET_PASSWORD").equals("SUCCESS")) {
                  info.setReset_password(systemSetup.getReset_password());
               } else {
                  info.setReset_password(-1L);
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.COMPUTERNAME") != null) {
               info.setComputer_name(systemSetup.getComputer_name());
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.AUTO_IP_SET") != null) {
               info.setAuto_ip_set(systemSetup.getAuto_ip_set());
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.AUTO_COMPUTERNAME_SET") != null) {
               info.setAuto_computer_name_set(systemSetup.getAuto_computer_name_set());
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.USE_MPPLAYER") != null) {
               info.setUse_mpplayer(systemSetup.getUse_mpplayer());
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.VNC_PASSWORD") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT.VNC_PASSWORD").equals("SUCCESS")) {
                  info.setVnc_password(systemSetup.getVnc_password());
               } else {
                  info.setVnc_password("FAIL");
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.TAG") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.TAG").equals("SUCCESS")) {
                  info.setTag_value("SUCCESS");
               } else {
                  info.setTag_value("FAIL");
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.FILEDATA_DEL_SIZE") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.FILEDATA_DEL_SIZE").equals("SUCCESS")) {
                  info.setFiledata_del_size(systemSetup.getFiledata_del_size());
               } else {
                  info.setFiledata_del_size(-1L);
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.CONTENT_READY_INTERVAL") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.CONTENT_READY_INTERVAL").equals("SUCCESS")) {
                  info.setContent_ready_interval(systemSetup.getContent_ready_interval());
               } else {
                  info.setContent_ready_interval(-1L);
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.PLAYER_START_TIMEOUT") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK.PLAYER_START_TIMEOUT").equals("SUCCESS")) {
                  info.setPlayer_start_timeout(systemSetup.getPlayer_start_timeout());
               } else {
                  info.setPlayer_start_timeout(-1L);
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DOWN_PROGRESS.ENABLE") != null) {
               info.setContents_progress_enable(systemSetup.getContents_progress_enable());
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DOWN_PROGRESS.MEASURE_UNIT") != null) {
               info.setContents_progress_unit(systemSetup.getContents_progress_unit());
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DOWN_PROGRESS.MEASURE_INTERVAL") != null) {
               info.setContents_progress_interval(systemSetup.getContents_progress_interval());
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.FACE_RECOGNITION.PLAY_MODE") != null) {
               info.setAms_play_mode(systemSetup.getAms_play_mode());
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.DOWNLOAD_SERVER") != null) {
               info.setOnly_dn_server(systemSetup.getOnly_dn_server());
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PLAYER_RESOLUTION") != null) {
               info.setPlayer_resolution(systemSetup.getPlayer_resolution());
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.HAS_SIGNAGE_CHILD") != null) {
               if (!"SIGNAGE".equalsIgnoreCase(systemSetup.getDevice_type()) && !"RSIGNAGE".equalsIgnoreCase(systemSetup.getDevice_type())) {
                  info.setHas_child(systemSetup.getHas_child());
               } else {
                  info.setHas_child(true);
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PAGE_SWITCH_TIME") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.PAGE_SWITCH_TIME").equals("SUCCESS")) {
                  info.setSwitch_time(systemSetup.getSwitch_time());
               } else {
                  info.setSwitch_time("FAIL");
               }
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.BANDWIDTH_LIMIT") != null) {
               if (rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.BANDWIDTH_LIMIT").equals("SUCCESS")) {
                  info.setBandwidth(systemSetup.getBandwidth());
               } else {
                  info.setBandwidth(-1L);
               }
            }
         }
      } catch (SQLException var9) {
         this.logger.error("", var9);
      } catch (RMQLException var10) {
         this.logger.error("", var10);
      }

      return info;
   }

   public Map getSettingResultByTime(String deviceId, String sessionId, String serviceId) {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = statusManager.getResultset(sessionId, serviceId, deviceId);
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = null;

      try {
         device = deviceDao.getDeviceMinInfo(deviceId);
      } catch (SQLException var14) {
         this.logger.error("", var14);
      }

      String deviceModelCode = device.getDevice_model_code();
      int timerCnt = MDCTimeStrUtils.getTimerCnt(deviceModelCode);
      Map map = null;
      int cnt = 0;

      try {
         if (rs != null) {
            map = new HashMap();
            if (rs.getString(".MO.DISPLAY_CONF.TIME.ON_TIME") != null) {
               map.put("on_time", rs.getString(".MO.DISPLAY_CONF.TIME.ON_TIME"));
               ++cnt;
            }

            if (rs.getString(".MO.DISPLAY_CONF.TIME.OFF_TIME") != null) {
               map.put("off_time", rs.getString(".MO.DISPLAY_CONF.TIME.OFF_TIME"));
               ++cnt;
            }

            if (rs.getString(".MO.DISPLAY_CONF.TIME.CURRENT_TIME") != null) {
               map.put("current_time", rs.getString(".MO.DISPLAY_CONF.TIME.CURRENT_TIME"));
               ++cnt;
            }

            if (rs.getString(".MO.DISPLAY_CONF.TIMER.CLOCK") != null) {
               map.put("clock", rs.getString(".MO.DISPLAY_CONF.TIMER.CLOCK"));
               ++cnt;
            }

            if (rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER1") != null) {
               map.put("timer1", rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER1"));
               ++cnt;
            }

            if (rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER2") != null) {
               map.put("timer2", rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER2"));
               ++cnt;
            }

            if (rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER3") != null) {
               map.put("timer3", rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER3"));
               ++cnt;
            }

            if (timerCnt == 7) {
               if (rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER4") != null) {
                  map.put("timer4", rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER4"));
                  ++cnt;
               }

               if (rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER5") != null) {
                  map.put("timer5", rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER5"));
                  ++cnt;
               }

               if (rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER6") != null) {
                  map.put("timer6", rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER6"));
                  ++cnt;
               }

               if (rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER7") != null) {
                  map.put("timer7", rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER7"));
                  ++cnt;
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.TIMER.HOLIDAY") != null) {
               map.put("holiday", rs.getString(".MO.DISPLAY_CONF.TIMER.HOLIDAY"));
               ++cnt;
            }

            if (cnt < 1) {
               map = null;
            }
         }
      } catch (RMQLException var13) {
         this.logger.error("", var13);
      }

      return map;
   }

   public DeviceSystemInfoConf getSystemInfoResultSet(String deviceId, String sessionId, String serviceId) {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = statusManager.getResultset(sessionId, serviceId, deviceId);
      DeviceSystemInfoConf info = null;

      try {
         if (rs != null) {
            info = new DeviceSystemInfoConf();
            info.setCpu_usage(rs.getLong(".MO.DEVICE_CONF.SYSTEM_INFO.CPU_USAGE"));
            info.setRam_usage(rs.getLong(".MO.DEVICE_CONF.SYSTEM_INFO.RAM_USAGE"));
            info.setNetwork_usage(rs.getLong(".MO.DEVICE_CONF.SYSTEM_INFO.NETWORK_USAGE"));
            info.setDisk_space_available(rs.getString(".MO.DEVICE_CONF.SYSTEM_INFO.DISK_SPACE_AVAILABLE"));
            info.setDisk_space_usage(rs.getString(".MO.DEVICE_CONF.SYSTEM_INFO.DISK_SPACE_USAGE"));
            Timestamp system_time = null;

            try {
               system_time = DateUtils.dateTime2TimeStamp(rs.getDate(".MO.DEVICE_CONF.SYSTEM_INFO.SYSTEM_TIME"));
               info.setSystem_time(system_time);
            } catch (ParseException var9) {
               this.logger.error("", var9);
            }
         }
      } catch (RMQLException var10) {
         this.logger.error("", var10);
      }

      return info;
   }

   public DeviceTimeConf getTimeResultSet(String deviceId, String sessionId, String serviceId) {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = statusManager.getResultset(sessionId, serviceId, deviceId);
      DeviceTimeConf info = null;
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = null;

      try {
         device = deviceDao.getDeviceMinInfo(deviceId);
      } catch (SQLException var13) {
         this.logger.error("", var13);
      }

      String deviceModelCode = device.getDevice_model_code();
      int timerCnt = MDCTimeStrUtils.getTimerCnt(deviceModelCode);

      try {
         if (rs != null) {
            info = new DeviceTimeConf();
            if (rs.getString(".MO.DISPLAY_CONF.TIME.ON_TIME") != null) {
               info.setTime_on_time(rs.getString(".MO.DISPLAY_CONF.TIME.ON_TIME"));
            }

            if (rs.getString(".MO.DISPLAY_CONF.TIME.OFF_TIME") != null) {
               info.setTime_off_time(rs.getString(".MO.DISPLAY_CONF.TIME.OFF_TIME"));
            }

            if (rs.getString(".MO.DISPLAY_CONF.TIME.CURRENT_TIME") != null) {
               info.setTime_current_time(rs.getString(".MO.DISPLAY_CONF.TIME.CURRENT_TIME"));
            }

            if (rs.getString(".MO.DISPLAY_CONF.TIMER.CLOCK") != null) {
               info.setTimer_clock(rs.getString(".MO.DISPLAY_CONF.TIMER.CLOCK"));
            }

            if (rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER1") != null) {
               info.setTimer_timer1(rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER1"));
            }

            if (rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER2") != null) {
               info.setTimer_timer2(rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER2"));
            }

            if (rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER3") != null) {
               info.setTimer_timer3(rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER3"));
            }

            if (timerCnt == 7) {
               if (rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER4") != null) {
                  info.setTimer_timer4(rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER4"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER5") != null) {
                  info.setTimer_timer5(rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER5"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER6") != null) {
                  info.setTimer_timer6(rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER6"));
               }

               if (rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER7") != null) {
                  info.setTimer_timer7(rs.getString(".MO.DISPLAY_CONF.TIMER.TIMER7"));
               }
            }

            if (rs.getString(".MO.DISPLAY_CONF.TIMER.HOLIDAY") != null) {
               info.setTimer_holiday(rs.getString(".MO.DISPLAY_CONF.TIMER.HOLIDAY"));
            }
         }
      } catch (RMQLException var12) {
         this.logger.error("", var12);
      }

      return info;
   }

   public Object getNewTimeZoneListByDeviceId(String deviceId, String sessionId, String serviceId) {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = statusManager.getResultset(sessionId, serviceId, deviceId);

      try {
         if (rs != null && rs.getString(".MO.DEVICE_CONF.SYSTEM_SETUP.TIME_ZONE_LIST") != null) {
         }
      } catch (Exception var7) {
         this.logger.error("", var7);
      }

      return null;
   }

   public Object reqGetCabinetConfFromDevice(String deviceId, List childIds, String sessionId, String mode) throws Exception {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = null;
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      String[] childIndex = null;
      if (childIds != null && childIds.size() > 0) {
         childIndex = (String[])((String[])childIds.toArray(new String[childIds.size()]));
      }

      Device device = deviceDao.getDeviceMinInfo(deviceId);
      if (!sessionId.equals("") && childIds != null) {
         Iterator var10 = childIds.iterator();

         while(var10.hasNext()) {
            String childId = (String)var10.next();
            statusManager.setResultset(sessionId, "GET_LED_CABINET_CONF", deviceId + "_" + childId, (ResultSet)rs);
         }
      }

      HashMap params = new HashMap();
      params.put("device", device);
      params.put("session_id", sessionId);
      params.put("led_cabinet_view_mode", mode);
      if (childIndex != null) {
         params.put("childIndex", childIndex);
      }

      ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
      Object rtnObj = dispatcher.startService("GET_LED_CABINET_CONF", params);
      return rtnObj;
   }

   public Object reqGetDisplayFromDevice(String deviceId, String sessionId, String mode) throws Exception {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = null;
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      String[] childIndex = null;
      if (deviceId.contains("_")) {
         childIndex = new String[]{deviceId.split("_")[1]};
         deviceId = deviceId.split("_")[0];
      }

      Device device = deviceDao.getDeviceMinInfo(deviceId);
      if (!sessionId.equals("")) {
         statusManager.setResultset(sessionId, "GET_DEVICE_DISPLAY_CONF", deviceId, (ResultSet)rs);
      }

      HashMap params = new HashMap();
      params.put("device", device);
      params.put("session_id", sessionId);
      params.put("device_display_view_mode", mode);
      if (childIndex != null) {
         params.put("childIndex", childIndex);
      }

      ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
      Object rtnObj = dispatcher.startService("GET_DEVICE_DISPLAY_CONF", params);
      return rtnObj;
   }

   public Object reqGetSecurityFromDevice(String deviceId, String sessionId, String mode) throws Exception {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = null;
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      String[] childIndex = null;
      if (deviceId.contains("_")) {
         childIndex = new String[]{deviceId.split("_")[1]};
         deviceId = deviceId.split("_")[0];
      }

      Device device = deviceDao.getDeviceMinInfo(deviceId);
      if (!sessionId.equals("")) {
         statusManager.setResultset(sessionId, "GET_DEVICE_SECURITY_CONF", deviceId, (ResultSet)rs);
      }

      HashMap params = new HashMap();
      params.put("device", device);
      params.put("session_id", sessionId);
      if (childIndex != null) {
         params.put("childIndex", childIndex);
      }

      ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
      Object rtnObj = dispatcher.startService("GET_DEVICE_SECURITY_CONF", params);
      return rtnObj;
   }

   public Object reqGetGeneralFromDevice(String deviceId, String sessionId) throws Exception {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = null;
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      String[] childIndex = null;
      if (deviceId.contains("_")) {
         childIndex = new String[]{deviceId.split("_")[1]};
         deviceId = deviceId.split("_")[0];
      }

      Device device = deviceDao.getDeviceMinInfo(deviceId);
      statusManager.setResultset(sessionId, "GET_DEVICE_GENERAL_CONF", deviceId, (ResultSet)rs);
      HashMap params = new HashMap();
      params.put("device", device);
      params.put("session_id", sessionId);
      if (childIndex != null) {
         params.put("childIndex", childIndex);
      }

      ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
      Object rtnObj = dispatcher.startService("GET_DEVICE_GENERAL_CONF", params);
      return rtnObj;
   }

   public DeviceGeneralConf getGeneralResultSet(String deviceId, String sessionId) throws Exception {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = statusManager.getResultset(sessionId, "GET_DEVICE_GENERAL_CONF", deviceId);
      if (rs == null) {
         return null;
      } else {
         DeviceGeneralConf deviceGeneralConf = new DeviceGeneralConf();

         try {
            String device_name = rs.getString(".MO.DEVICE_CONF.GENERAL.DEVICE_NAME");
            String serial_decimal = rs.getString(".MO.DEVICE_CONF.GENERAL.SERIAL_DECIMAL");
            String screen_size = rs.getString(".MO.DEVICE_CONF.GENERAL.SCREEN_SIZE");
            String resolution = rs.getString(".MO.DEVICE_CONF.GENERAL.RESOLUTION");
            String firmware_version = rs.getString(".MO.DEVICE_CONF.GENERAL.FIRMWARE_VERSION");
            String os_image_version = rs.getString(".MO.DEVICE_CONF.GENERAL.OS_VERSION");
            String video_adapter = rs.getString(".MO.DEVICE_CONF.GENERAL.VIDEO_ADAPTER");
            Long video_memory = rs.getLong(".MO.DEVICE_CONF.GENERAL.VIDEO_MEMORY");
            String video_driver = rs.getString(".MO.DEVICE_CONF.GENERAL.VIDEO_DRIVER");
            String network_adapter = rs.getString(".MO.DEVICE_CONF.GENERAL.NETWORK_ADPATER");
            String network_driver = rs.getString(".MO.DEVICE_CONF.GENERAL.NETWORK_DRIVER ");
            Boolean ewf_state = rs.getBoolean(".MO.DEVICE_CONF.GENERAL.EWF_STATE");
            if (rs.getString(".MO.DEVICE_CONF.GENERAL.PERIPHERALS") != null && !rs.getString(".MO.DEVICE_CONF.GENERAL.PERIPHERALS").equals("")) {
               String peripherals = rs.getString(".MO.DEVICE_CONF.GENERAL.PERIPHERALS");
               deviceGeneralConf.setPeripherals(peripherals);
            }

            Long thirdApplicationLogSize = null;
            if (rs.getString(".MO.DEVICE_CONF.GENERAL.THIRD_APPLICATION_LOG_SIZE") != null && !rs.getString(".MO.DEVICE_CONF.GENERAL.THIRD_APPLICATION_LOG_SIZE").equals("")) {
               thirdApplicationLogSize = rs.getLong(".MO.DEVICE_CONF.GENERAL.THIRD_APPLICATION_LOG_SIZE");
            }

            deviceGeneralConf.setDevice_name(device_name);
            deviceGeneralConf.setSerial_decimal(serial_decimal);
            deviceGeneralConf.setScreen_size(CommonUtils.isInteger(screen_size) ? screen_size : null);
            deviceGeneralConf.setResolution(resolution);
            deviceGeneralConf.setFirmware_version(firmware_version);
            deviceGeneralConf.setOs_image_version(os_image_version);
            deviceGeneralConf.setVideo_adapter(video_adapter);
            deviceGeneralConf.setVideo_memory(video_memory);
            deviceGeneralConf.setVideo_driver(video_driver);
            deviceGeneralConf.setNetwork_adapter(network_adapter);
            deviceGeneralConf.setNetwork_driver(network_driver);
            deviceGeneralConf.setEwf_state(ewf_state);
            deviceGeneralConf.setThird_application_log_size(thirdApplicationLogSize);
            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_INFO.DISK_SPACE_USAGE") != null) {
               deviceGeneralConf.setDisk_space_usage(rs.getString(".MO.DEVICE_CONF.SYSTEM_INFO.DISK_SPACE_USAGE"));
            }

            if (rs.getString(".MO.DEVICE_CONF.SYSTEM_INFO.DISK_SPACE_AVAILABLE") != null) {
               deviceGeneralConf.setDisk_space_available(rs.getString(".MO.DEVICE_CONF.SYSTEM_INFO.DISK_SPACE_AVAILABLE"));
            }

            DBCacheUtils.setDeviceGeneralConf(deviceGeneralConf, deviceId);
            return deviceGeneralConf;
         } catch (Exception var19) {
            this.logger.error("", var19);
            return null;
         }
      }
   }

   public Object reqGetNetworkFromDevice(String deviceId, String sessionId) throws Exception {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = null;
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      statusManager.setResultset(sessionId, "GET_DEVICE_NETWORK_CONF", deviceId, (ResultSet)rs);
      HashMap params = new HashMap();
      params.put("device", device);
      params.put("session_id", sessionId);
      ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
      Object rtnObj = dispatcher.startService("GET_DEVICE_NETWORK_CONF", params);
      return rtnObj;
   }

   public Object reqGetSystemInfoFromDevice(String deviceId, String sessionId) throws Exception {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = null;
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      if (!sessionId.equals("")) {
         statusManager.setResultset(sessionId, "GET_DEVICE_SYSTEM_INFO_CONF", deviceId, (ResultSet)rs);
      }

      HashMap params = new HashMap();
      params.put("device", device);
      params.put("session_id", sessionId);
      ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
      Object rtnObj = dispatcher.startService("GET_DEVICE_SYSTEM_INFO_CONF", params);
      return rtnObj;
   }

   public Object reqGetSystemSetupFromDevice(String deviceId, String sessionId) throws Exception {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = null;
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      statusManager.setResultset(sessionId, "GET_DEVICE_SYSTEM_SETUP_CONF", deviceId, (ResultSet)rs);
      HashMap params = new HashMap();
      params.put("device", device);
      params.put("session_id", sessionId);
      ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
      Object rtnObj = dispatcher.startService("GET_DEVICE_SYSTEM_SETUP_CONF", params);
      return rtnObj;
   }

   public Object reqGetTimeFromDevice(String deviceId, String sessionId) throws Exception {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = null;
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      if (!sessionId.equals("")) {
         statusManager.setResultset(sessionId, "GET_DEVICE_TIME_CONF", deviceId, (ResultSet)rs);
      }

      HashMap params = new HashMap();
      params.put("device", device);
      params.put("session_id", sessionId);
      ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
      Object rtnObj = dispatcher.startService("GET_DEVICE_TIME_CONF", params);
      return rtnObj;
   }

   public Object reqSetCabinetConfToDevice(LedCabinet ledCabinet, List childIds, String sessionId, String mode) throws Exception {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      String parentDeviceId = ledCabinet.getParent_device_id();
      String[] childIndex = null;
      Device device = deviceDao.getDeviceMinInfo(parentDeviceId);
      LedCabinetConfManager ledCabinetConfMgr = LedCabinetConfManagerImpl.getInstance();
      if (childIds != null && childIds.size() > 0) {
         String groupId = ((String)childIds.get(0)).split("-")[0];

         int i;
         for(i = 0; i < childIds.size(); ++i) {
            if (!groupId.equals(((String)childIds.get(i)).split("-")[0])) {
               childIds.remove(i);
            }
         }

         if (ledCabinetConfMgr.getLedCabinetCount(parentDeviceId, Long.parseLong(groupId)) == childIds.size()) {
            childIndex = new String[]{groupId + "-0"};
         } else {
            childIndex = new String[childIds.size()];

            for(i = 0; i < childIds.size(); ++i) {
               String childId = (String)childIds.get(i);
               childIndex[i] = childId;
            }
         }
      }

      Object rtnObj = null;
      if (device != null) {
         HashMap params = new HashMap();
         params.put("device", device);
         params.put("session_id", sessionId);
         if (childIndex != null) {
            params.put("childIndex", childIndex);
         }

         ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
         ResultSet result = null;
         statusManager.setResultset(sessionId, "SET_LED_CABINET_CONF", parentDeviceId, (ResultSet)result);
         params.put("led_cabinet_conf", ledCabinet);
         params.put("led_cabinet_view_mode", mode);
         ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
         rtnObj = dispatcher.startService("SET_LED_CABINET_CONF", params);
      }

      return rtnObj;
   }

   public Object reqSetDisplayToDevice(DeviceDisplayConf displayConf, String sessionId, String mode) throws Exception {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      String deviceId = displayConf.getDevice_id();
      String[] childIndex = null;
      int targetChildCnt = 0;
      if (deviceId.contains("_")) {
         String[] deviceIdNChildIndexArr = deviceId.split("_");
         deviceId = deviceIdNChildIndexArr[0];
         targetChildCnt = deviceIdNChildIndexArr.length - 1;
         childIndex = new String[targetChildCnt];

         for(int currentChildIndex = 0; currentChildIndex < targetChildCnt; ++currentChildIndex) {
            childIndex[currentChildIndex] = deviceIdNChildIndexArr[currentChildIndex + 1];
         }

         displayConf.setDevice_id(deviceId);
      }

      Device device = deviceDao.getDeviceMinInfo(deviceId);
      if (device.getChild_cnt() != null && device.getChild_cnt() > 0L && device.getChild_cnt() == (long)targetChildCnt) {
         childIndex = new String[]{"0"};
      }

      Object rtnObj = null;
      if (device != null) {
         HashMap params = new HashMap();
         params.put("device", device);
         params.put("session_id", sessionId);
         if (childIndex != null) {
            params.put("childIndex", childIndex);
         }

         ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
         ResultSet result = null;
         statusManager.setResultset(sessionId, "SET_DEVICE_DISPLAY_CONF", deviceId, (ResultSet)result);
         params.put("device_display_conf", displayConf);
         params.put("device_display_view_mode", mode);
         ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
         rtnObj = dispatcher.startService("SET_DEVICE_DISPLAY_CONF", params);
      }

      return rtnObj;
   }

   public Object reqSetSecurityToDevice(DeviceSecurityConf securityConf, String sessionId, String mode) throws Exception {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      String deviceId = securityConf.getDevice_id();
      String[] childIndex = null;
      int targetChildCnt = 0;
      if (deviceId.contains("_")) {
         String[] deviceIdNChildIndexArr = deviceId.split("_");
         deviceId = deviceIdNChildIndexArr[0];
         targetChildCnt = deviceIdNChildIndexArr.length - 1;
         childIndex = new String[targetChildCnt];

         for(int currentChildIndex = 0; currentChildIndex < targetChildCnt; ++currentChildIndex) {
            childIndex[currentChildIndex] = deviceIdNChildIndexArr[currentChildIndex + 1];
         }

         securityConf.setDevice_id(deviceId);
      }

      Device device = deviceDao.getDeviceMinInfo(deviceId);
      if (device.getChild_cnt() != null && device.getChild_cnt() > 0L && device.getChild_cnt() == (long)targetChildCnt) {
         childIndex = new String[]{"0"};
      }

      Object rtnObj = null;
      if (device != null) {
         HashMap params = new HashMap();
         params.put("device", device);
         params.put("session_id", sessionId);
         if (childIndex != null) {
            params.put("childIndex", childIndex);
         }

         ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
         ResultSet result = null;
         statusManager.setResultset(sessionId, "SET_DEVICE_SECURITY_CONF", deviceId, (ResultSet)result);
         params.put("device_security_conf", securityConf);
         ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
         rtnObj = dispatcher.startService("SET_DEVICE_SECURITY_CONF", params);
      }

      return rtnObj;
   }

   public Object reqSetGeneralToDevice(DeviceGeneralConf generalConf, String sessionId) throws Exception {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      String[] childIndex = null;
      String deviceId = generalConf.getDevice_id();
      int targetChildCnt = 0;
      if (deviceId.contains("_")) {
         String[] deviceIdNChildIndexArr = deviceId.split("_");
         deviceId = deviceIdNChildIndexArr[0];
         targetChildCnt = deviceIdNChildIndexArr.length - 1;
         childIndex = new String[targetChildCnt];

         for(int currentChildIndex = 0; currentChildIndex < targetChildCnt; ++currentChildIndex) {
            childIndex[currentChildIndex] = deviceIdNChildIndexArr[currentChildIndex + 1];
         }

         generalConf.setDevice_id(deviceId);
      }

      Device device = deviceDao.getDeviceMinInfo(deviceId);
      if (device.getChild_cnt() != null && device.getChild_cnt() > 0L && device.getChild_cnt() == (long)targetChildCnt) {
         childIndex = new String[]{"0"};
      }

      HashMap params = new HashMap();
      params.put("device", device);
      params.put("session_id", sessionId);
      if (childIndex != null) {
         params.put("childIndex", childIndex);
      }

      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet result = null;
      statusManager.setResultset(sessionId, "SET_DEVICE_GENERAL_CONF", deviceId, (ResultSet)result);
      params.put("device_general_conf", generalConf);
      ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
      Object rtnObj = dispatcher.startService("SET_DEVICE_GENERAL_CONF", params);
      return rtnObj;
   }

   public Object reqSetNetworkToDevice(DeviceNetworkConf networkConf, String sessionId) throws Exception {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      String deviceId = networkConf.getDevice_id();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      HashMap params = new HashMap();
      params.put("device", device);
      params.put("session_id", sessionId);
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet result = null;
      statusManager.setResultset(sessionId, "SET_DEVICE_NETWORK_CONF", deviceId, (ResultSet)result);
      params.put("device_network_conf", networkConf);
      ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
      Object rtnObj = dispatcher.startService("SET_DEVICE_NETWORK_CONF", params);
      return rtnObj;
   }

   public Object reqSetSystemInfoToDevice(DeviceSystemInfoConf systemInfo, String sessionId) throws Exception {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      String deviceId = systemInfo.getDevice_id();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      HashMap params = new HashMap();
      params.put("device", device);
      params.put("session_id", sessionId);
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet result = null;
      statusManager.setResultset(sessionId, "SET_DEVICE_SYSTEM_INFO_CONF", deviceId, (ResultSet)result);
      params.put("device_system_info_conf", systemInfo);
      ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
      Object rtnObj = dispatcher.startService("SET_DEVICE_SYSTEM_INFO_CONF", params);
      return rtnObj;
   }

   public Object reqSetSystemSetupToDevice(DeviceSystemSetupConf systemSetup, String sessionId) throws Exception {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      String deviceId = systemSetup.getDevice_id();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      HashMap params = new HashMap();
      params.put("device", device);
      params.put("session_id", sessionId);
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet result = null;
      statusManager.setResultset(sessionId, "SET_DEVICE_SYSTEM_SETUP_CONF", deviceId, (ResultSet)result);
      params.put("device_system_setup_conf", systemSetup);
      ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
      Object rtnObj = dispatcher.startService("SET_DEVICE_SYSTEM_SETUP_CONF", params);
      return rtnObj;
   }

   public Object reqSetSystemSetupToDevice(DeviceSystemSetupConf systemSetup, String sessionId, String productType) throws Exception {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      String deviceId = systemSetup.getDevice_id();
      String[] childIndex = null;
      int targetChildCnt = 0;
      if (deviceId.contains("_")) {
         String[] deviceIdNChildIndexArr = deviceId.split("_");
         deviceId = deviceIdNChildIndexArr[0];
         targetChildCnt = deviceIdNChildIndexArr.length - 1;
         childIndex = new String[targetChildCnt];

         for(int currentChildIndex = 0; currentChildIndex < targetChildCnt; ++currentChildIndex) {
            childIndex[currentChildIndex] = deviceIdNChildIndexArr[currentChildIndex + 1];
         }

         systemSetup.setDevice_id(deviceId);
      }

      Device device = deviceDao.getDeviceMinInfo(deviceId);
      if (device.getChild_cnt() != null && device.getChild_cnt() > 0L && device.getChild_cnt() == (long)targetChildCnt) {
         childIndex = new String[]{"0"};
      }

      Object rtnObj = null;
      if (device != null) {
         HashMap params = new HashMap();
         params.put("device", device);
         params.put("session_id", sessionId);
         if (childIndex != null) {
            params.put("childIndex", childIndex);
         }

         ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
         ResultSet result = null;
         statusManager.setResultset(sessionId, "SET_DEVICE_SYSTEM_SETUP_CONF", deviceId, (ResultSet)result);
         params.put("device_system_setup_conf", systemSetup);
         ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
         rtnObj = dispatcher.startService("SET_DEVICE_SYSTEM_SETUP_CONF", params);
      }

      return rtnObj;
   }

   public Object reqSetTimeToDevice(DeviceTimeConf timeConf, String sessionId) throws Exception {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = null;
      String deviceId = null;
      if (timeConf != null) {
         deviceId = timeConf.getDevice_id();
         if (deviceId != null) {
            device = deviceDao.getDeviceMinInfo(deviceId);
         }
      }

      if (device == null) {
         DeviceInfo liteDeviceDao = DeviceInfoImpl.getInstance();
         device = liteDeviceDao.getDeviceMinInfo(deviceId);
      }

      Object rtnObj = null;
      if (deviceId != null && device != null) {
         HashMap params = new HashMap();
         params.put("device", device);
         params.put("session_id", sessionId);
         if (timeConf != null && timeConf.getDevice_model_code() == null) {
            timeConf.setDevice_model_code(device.getDevice_model_code());
         }

         ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
         ResultSet result = null;
         statusManager.setResultset(sessionId, "SET_DEVICE_TIME_CONF", deviceId, (ResultSet)result);
         params.put("device_time_conf", timeConf);
         ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
         rtnObj = dispatcher.startService("SET_DEVICE_TIME_CONF", params);
      }

      return rtnObj;
   }

   public Object reqGetRequestStatistics(String deviceId, String sessionId) throws Exception {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      HashMap params = new HashMap();
      params.put("device", device);
      ArrayList moPathList = new ArrayList();
      moPathList.add(".MO.DEVICE_CONF.STAT.FILE_UPLOAD");
      params.put("mo_path", moPathList);
      ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
      Object rtnObj = dispatcher.startService("GET_DEVICE_COMMON_CONF", params);
      return rtnObj;
   }

   public Object reqGetRequestPreconfigResult(String deviceId, String sessionId) throws Exception {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      HashMap params = new HashMap();
      params.put("device", device);
      ArrayList moPathList = new ArrayList();
      moPathList.add(".MO.DEVICE_CONF.PRE_CONFIG_RESULT.FILE_UPLOAD");
      params.put("mo_path", moPathList);
      ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
      Object rtnObj = dispatcher.startService("GET_DEVICE_COMMON_CONF", params);
      return rtnObj;
   }

   public Object reqSetDeviceConnStatus(String deviceId, String moPath, String moValue) throws Exception {
      return this.reqSetDeviceConnStatus(deviceId, moPath, moValue, (String)null, 0L);
   }

   public Object reqSetDeviceConnStatus(String deviceId, String moPath, String moValue, String sessionId, long timeout) throws Exception {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      HashMap params = new HashMap();
      params.put("device", device);
      params.put("session_id", sessionId);
      if (timeout > 0L) {
         params.put("time_out", timeout);
      }

      Map momap = new HashMap();
      momap.put(moPath, moValue);
      params.put("mo_list", momap);
      ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
      Object rtnObj = dispatcher.startService("SET_DEVICE_COMMON_CONF", params);
      return rtnObj;
   }

   public Object reqSetDisasterAler(String deviceId, String moPath, String moValue) throws Exception {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      Object rtnObj = null;
      if (device != null) {
         HashMap params = new HashMap();
         params.put("device", device);
         params.put("device", device);
         Map momap = new HashMap();
         momap.put(moPath, moValue);
         params.put("mo_list", momap);
         this.logger.info("---DisasterAlert: mo_path: " + moPath + " mo_vlaue: " + moValue);
         ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
         rtnObj = dispatcher.startService("SET_DEVICE_COMMON_CONF", params);
      }

      return rtnObj;
   }

   public Object reqGetDatalinkAndMdcExt(String deviceId) throws Exception {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      HashMap params = new HashMap();
      ArrayList moPathList = new ArrayList();
      moPathList.add(".MO.DEVICE_CONF.SYSTEM_SETUP_EXT");
      moPathList.add(".MO.DEVICE_CONF.SYSTEM_SETUP_DATALINK");
      params.put("mo_path", moPathList);
      params.put("device", device);
      ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
      Object rtnObj = dispatcher.startService("GET_DEVICE_COMMON_CONF", params);
      return rtnObj;
   }

   public String getDevicePredefinedCmdResultSet(String deviceId, String sessionId, String cmd) throws Exception {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      String result = null;
      String serviceId = "GET_DEVICE_PREDEFINED_CMD";
      ResultSet rs = statusManager.getResultset(sessionId, serviceId, deviceId);

      try {
         if (rs != null && rs.getString(".MO.DEVICE_CONF.PROCESS.CMD") != null) {
            result = rs.getString(".MO.DEVICE_CONF.PROCESS.CMD");
            if (result.contains(cmd)) {
               result = result.split(cmd + ";")[1];
            } else {
               result = null;
            }
         }
      } catch (Exception var9) {
         this.logger.error("", var9);
      }

      return result;
   }

   public Object reqGetDevicePredefinedCmd(String deviceId, String moValue, String sessionId) throws Exception {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      Object rtnObj = null;
      ResultSet rs = null;
      HashMap params = new HashMap();
      if (device != null) {
         statusManager.setResultset(sessionId, "GET_DEVICE_PREDEFINED_CMD", deviceId, (ResultSet)rs);
         params.put("device", device);
         params.put("mo_path", ".MO.DEVICE_CONF.PROCESS.CMD");
         params.put("mo_value", moValue);
         params.put("session_id", sessionId);
         ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
         rtnObj = dispatcher.startService("GET_DEVICE_PREDEFINED_CMD", params);
      }

      return rtnObj;
   }

   public Object reqSetDevicePredefinedCmd(String deviceId, String moValue, String sessionId) throws Exception {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      Object rtnObj = null;
      ResultSet rs = null;
      HashMap params = new HashMap();
      if (device != null) {
         statusManager.setResultset(sessionId, "SET_DEVICE_PREDEFINED_CMD", deviceId, (ResultSet)rs);
         params.put("device", device);
         params.put("mo_path", ".MO.DEVICE_CONF.PROCESS.CMD");
         params.put("mo_value", moValue);
         params.put("session_id", sessionId);
         ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
         rtnObj = dispatcher.startService("SET_DEVICE_PREDEFINED_CMD", params);
      }

      return rtnObj;
   }

   public boolean reqSetSignageCmd(String deviceId, String signageCmd, String value, String sessionId) throws Exception {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      if (device == null) {
         this.logger.error("REQ Set AuthorCmd : invalid device. id=" + deviceId);
         return false;
      } else {
         HashMap params = new HashMap();
         params.put("device", device);
         if (signageCmd != null && !signageCmd.equals("")) {
            params.put("signage_cmd", signageCmd);
         }

         if (value != null && !value.equals("")) {
            params.put("signage_value", value);
         }

         ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
         dispatcher.startService("SET_SIGNAGE_CMD", params);
         return true;
      }
   }

   public boolean reqSetSboxCmd(String deviceId, List childIds, String cmd, String value, String sessionId) throws Exception {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      LedCabinetConfManager ledCabinetConfMgr = LedCabinetConfManagerImpl.getInstance();
      String[] childIndex = null;
      if (device == null) {
         this.logger.error("REQ Set AuthorCmd : invalid device. id=" + deviceId);
         return false;
      } else {
         if (childIds != null && childIds.size() > 0) {
            String groupId = ((String)childIds.get(0)).split("-")[0];

            int i;
            for(i = 0; i < childIds.size(); ++i) {
               if (!groupId.equals(((String)childIds.get(i)).split("-")[0])) {
                  childIds.remove(i);
               }
            }

            if (ledCabinetConfMgr.getLedCabinetCount(deviceId, Long.parseLong(groupId)) == childIds.size()) {
               childIndex = new String[]{groupId + "-0"};
            } else {
               childIndex = new String[childIds.size()];

               for(i = 0; i < childIds.size(); ++i) {
                  String childId = (String)childIds.get(i);
                  childIndex[i] = childId;
               }
            }
         }

         HashMap params = new HashMap();
         params.put("device", device);
         if (cmd != null && !cmd.equals("")) {
            params.put("sbox_cmd", cmd);
         }

         if (value != null && !value.equals("")) {
            params.put("sbox_value", value);
         }

         if (childIndex != null) {
            params.put("childIndex", childIndex);
         }

         ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
         dispatcher.startService("SET_SBOX_CMD", params);
         return true;
      }
   }

   public boolean setDeviceLogProcessing(String deviceId, String categoryScript, String sessionId) throws Exception {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = null;
      if (deviceId == null) {
         this.logger.error("[DeviceConfManagerImpl] setDeviceLogProcessing : invalid device. id=" + deviceId);
         return false;
      } else {
         DeviceLogCollectEntity logInfo = null;
         Device device = deviceDao.getDeviceMinInfo(deviceId);
         List deviceLogCollectEntityList = deviceDao.getDeviceLogProcessInfo(deviceId);
         Iterator var10 = deviceLogCollectEntityList.iterator();

         while(var10.hasNext()) {
            DeviceLogCollectEntity logCollectEntity = (DeviceLogCollectEntity)var10.next();
            if (categoryScript.equals(logCollectEntity.getCategory_script())) {
               logInfo = logCollectEntity;
            }
         }

         if (logInfo == null) {
            return false;
         } else {
            HashMap params = new HashMap();
            if (sessionId != null && !sessionId.equals("")) {
               statusManager.setResultset(sessionId, "DEVICE_LOG_PROCESS", deviceId, (ResultSet)rs);
            }

            params.put("device", device);
            params.put("log_data", logInfo);
            params.put("session_id", sessionId);
            ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
            dispatcher.startService("DEVICE_LOG_PROCESS", params);
            return true;
         }
      }
   }

   public String GetDeviceLogProcessingResultSet(String deviceId, String sessionId) throws Exception {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      String serviceId = "DEVICE_LOG_PROCESS";
      ResultSet rs = statusManager.getResultset(sessionId, serviceId, deviceId);
      String result = null;

      try {
         if (rs != null) {
            result = rs.getAttribute("RESULT");
         }

         return result;
      } catch (RMQLException var8) {
         this.logger.error("", var8);
         return null;
      }
   }

   public boolean reqSetSboxAuthorCmd(String deviceId, String authorCmd, String authorValue) throws Exception {
      DeviceInfo devInfo = DeviceInfoImpl.getInstance();
      Device device = devInfo.getDeviceMinInfo(deviceId);
      if (device == null) {
         this.logger.error("REQ Set AuthorCmd : invalid device. id=" + deviceId);
         return false;
      } else {
         HashMap params = new HashMap();
         params.put("device", device);
         if (authorCmd != null && !authorCmd.equals("")) {
            params.put("author_cmd", authorCmd);
         }

         if (authorValue != null && !authorValue.equals("")) {
            params.put("author_value", authorValue);
         }

         ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
         dispatcher.startService("SET_SBOX_AUTHOR_CMD", params);
         return true;
      }
   }

   public boolean deploySBOXLayout(String deviceId, String layoutType, String layoutId) throws Exception {
      DeviceInfo devInfo = DeviceInfoImpl.getInstance();
      Device device = devInfo.getDeviceMinInfo(deviceId);
      if (device == null) {
         this.logger.error("SBOX VWT Deploy failed: invalid device. id=" + deviceId);
         return false;
      } else {
         String vwtHome = CommonConfig.get("VWT_HOME");
         vwtHome = vwtHome.replace('/', File.separatorChar);
         File file = SecurityUtils.getSafeFile(vwtHome + File.separator + layoutId + File.separator + layoutId + ".VWL");
         if (file != null && file.exists()) {
            HashMap params = new HashMap();
            params.put("device", device);
            params.put("layout_type", layoutType);
            params.put("layout_id", layoutId);
            ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
            dispatcher.startService("DEPLOY_SBOX_LAYOUT", params);
            return true;
         } else {
            this.logger.error("SBOX VWT Deploy failed: VWT file not found. id=" + layoutId);
            return false;
         }
      }
   }

   public boolean deploySBOXLayout(String deviceId, String layoutType, String layoutId, String filePath) throws Exception {
      DeviceInfo devInfo = DeviceInfoImpl.getInstance();
      Device device = devInfo.getDeviceMinInfo(deviceId);
      if (device == null) {
         this.logger.error("SBOX VWT Deploy failed: invalid device. id=" + deviceId);
         return false;
      } else {
         File file = SecurityUtils.getSafeFile(filePath);
         if (file != null && file.exists()) {
            HashMap params = new HashMap();
            params.put("device", device);
            params.put("layout_type", layoutType);
            params.put("layout_id", layoutId);
            params.put("file_path", filePath);
            ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
            dispatcher.startService("DEPLOY_SBOX_LAYOUT", params);
            return true;
         } else {
            this.logger.error("[MagicInfo_SBOX Layout] SBOX VWT Deploy failed: Layout file not found. path=" + filePath);
            return false;
         }
      }
   }

   public Object reqSetCommonConfToDevice(Map deviceConf, String deviceId, String sessionId) throws Exception {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      HashMap params = new HashMap();
      params.put("device", device);
      params.put("device_common_conf", deviceConf);
      params.put("session_id", sessionId);
      Map momap = new HashMap();
      Map MO = new HashMap();
      MO.putAll(DeviceMOConstants.getGeneralEntityMO());
      MO.putAll(DeviceMOConstants.getSetupEntityMO());
      MO.putAll(DeviceMOConstants.getDisplayEntityMO());
      MO.putAll(DeviceMOConstants.getSecurityEntityMO());
      Iterator var9 = deviceConf.keySet().iterator();

      while(var9.hasNext()) {
         String entity = (String)var9.next();
         String moValue = (String)deviceConf.get(entity);
         if (MO.containsKey(entity) && moValue != null && moValue.length() > 0) {
            String moPath = (String)MO.get(entity);
            momap.put(moPath, moValue);
         }
      }

      params.put("mo_list", momap);
      if (momap.size() <= 0) {
         return null;
      } else {
         ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
         statusManager.setResultset(sessionId, "SET_DEVICE_COMMON_CONF", deviceId, (ResultSet)null);
         ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
         dispatcher.startService("SET_DEVICE_COMMON_CONF", params);
         return true;
      }
   }

   public Object reqGetCommonConfToDevice(Map deviceConf, String deviceId, String sessionId) throws Exception {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      HashMap params = new HashMap();
      Map MO = new HashMap();
      MO.putAll(DeviceMOConstants.getGeneralEntityMO());
      MO.putAll(DeviceMOConstants.getSetupEntityMO());
      MO.putAll(DeviceMOConstants.getDisplayEntityMO());
      MO.putAll(DeviceMOConstants.getSecurityEntityMO());
      params.put("device", device);
      params.put("device_common_conf", deviceConf);
      params.put("session_id", sessionId);
      ArrayList moPathList = new ArrayList();
      Iterator var9 = deviceConf.keySet().iterator();

      while(var9.hasNext()) {
         String entity = (String)var9.next();
         String moValue = (String)deviceConf.get(entity);
         if (MO.containsKey(entity)) {
            String moPath = (String)MO.get(entity);
            moPathList.add(moPath);
         }
      }

      params.put("mo_path", moPathList);
      ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
      Object rtnObj = dispatcher.startService("GET_DEVICE_COMMON_CONF", params);
      return rtnObj;
   }

   public Object reqGetAllCommonConfToDevice(String getType, String deviceId, String sessionId) throws Exception {
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      HashMap params = new HashMap();
      params.put("device", device);
      Map commonConf = new HashMap();
      commonConf.put("deviceId", deviceId);
      params.put("device_common_conf", commonConf);
      params.put("session_id", sessionId);
      ArrayList moPathList = DeviceMOConstants.getAllStatusMO(getType);
      params.put("mo_path", moPathList);
      ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
      Object rtnObj = dispatcher.startService("GET_DEVICE_COMMON_CONF", params);
      return rtnObj;
   }

   public DeviceControl getCommonConfSetResult(String deviceId, String sessionId) {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = statusManager.getResultset(sessionId, "SET_DEVICE_COMMON_CONF", deviceId);
      if (rs == null) {
         return null;
      } else {
         DeviceControl device = DeviceUtils.getDeviceControlInfo(deviceId, true);
         return device;
      }
   }

   public DeviceControl getCommonConfGetResult(String deviceId, String sessionId) {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = statusManager.getResultset(sessionId, "GET_DEVICE_COMMON_CONF", deviceId);
      if (rs == null) {
         return null;
      } else {
         DeviceControl device = DeviceUtils.getDeviceControlInfo(deviceId, true);
         return device;
      }
   }

   public Object reqSboxChildMonitoringNotifyFromDevice(String deviceId, String sessionId) throws Exception {
      ServiceStatusManager statusManager = ServiceStatusManagerImpl.getInstance();
      ResultSet rs = null;
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      String[] childIndex = null;
      if (deviceId.contains("_")) {
         childIndex = new String[]{deviceId.split("_")[1]};
         deviceId = deviceId.split("_")[0];
      }

      Device device = deviceDao.getDeviceMinInfo(deviceId);
      statusManager.setResultset(sessionId, "GET_LED_CABINET_CONF", deviceId, (ResultSet)rs);
      HashMap params = new HashMap();
      params.put("device", device);
      params.put("session_id", sessionId);
      params.put("led_cabinet_view_mode", "EXTERNAL_POWER");
      if (childIndex != null) {
         params.put("childIndex", childIndex);
      }

      ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
      Object rtnObj = dispatcher.startService("GET_LED_CABINET_CONF", params);
      return rtnObj;
   }

   public DeviceControl parsingSettingResultSetToEntity(Map deviceConf, String deviceId, ResultSet rs) {
      try {
         Map generalMO = DeviceMOConstants.getGeneralEntityMO();
         Map setupMO = DeviceMOConstants.getSetupEntityMO();
         Map displayMO = DeviceMOConstants.getDisplayEntityMO();
         Map securityMO = DeviceMOConstants.getSecurityEntityMO();
         Map generalResult = new HashMap();
         Map setupResult = new HashMap();
         Map displayResult = new HashMap();
         Map securityResult = new HashMap();
         Iterator var12 = deviceConf.keySet().iterator();

         while(var12.hasNext()) {
            String entity = (String)var12.next();
            String moPath;
            if (setupMO.containsKey(entity)) {
               moPath = (String)setupMO.get(entity);

               try {
                  if (rs.getString(moPath) != null && rs.getString(moPath).contentEquals("SUCCESS")) {
                     setupResult.put(entity, deviceConf.get(entity));
                  }
               } catch (RMQLException var20) {
                  this.logger.error("", var20);
               }
            } else if (displayMO.containsKey(entity)) {
               moPath = (String)displayMO.get(entity);

               try {
                  if (rs.getString(moPath) != null && rs.getString(moPath).contentEquals("SUCCESS")) {
                     displayResult.put(entity, deviceConf.get(entity));
                  }
               } catch (RMQLException var23) {
                  this.logger.error("", var23);
               }
            } else if (securityMO.containsKey(entity)) {
               moPath = (String)securityMO.get(entity);

               try {
                  if (rs.getString(moPath) != null && rs.getString(moPath).contentEquals("SUCCESS")) {
                     securityResult.put(entity, deviceConf.get(entity));
                  }
               } catch (RMQLException var22) {
                  this.logger.error("", var22);
               }
            } else if (generalMO.containsKey(entity)) {
               moPath = (String)generalMO.get(entity);

               try {
                  if (rs.getString(moPath) != null && rs.getString(moPath).contentEquals("SUCCESS")) {
                     generalResult.put(entity, deviceConf.get(entity));
                  }
               } catch (RMQLException var21) {
                  this.logger.error("", var21);
               }
            }
         }

         DeviceControl device = new DeviceControl();
         ObjectMapper mapper = new ObjectMapper();
         mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
         mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
         mapper.configure(DeserializationFeature.FAIL_ON_MISSING_CREATOR_PROPERTIES, false);

         try {
            if (!setupResult.isEmpty()) {
               DeviceSystemSetupConf setup = (DeviceSystemSetupConf)mapper.convertValue(setupResult, DeviceSystemSetupConf.class);
               setup.setDevice_id(deviceId);
               device.setSetup(setup);
            } else {
               device.setSetup((DeviceSystemSetupConf)null);
            }
         } catch (Exception var19) {
            this.logger.error("", var19);
            device.setSetup((DeviceSystemSetupConf)null);
         }

         try {
            if (!securityResult.isEmpty()) {
               DeviceSecurityConf security = (DeviceSecurityConf)mapper.convertValue(securityResult, DeviceSecurityConf.class);
               security.setDevice_id(deviceId);
               device.setSecurity(security);
            } else {
               device.setSecurity((DeviceSecurityConf)null);
            }
         } catch (Exception var18) {
            this.logger.error("", var18);
            device.setSecurity((DeviceSecurityConf)null);
         }

         try {
            if (!displayResult.isEmpty()) {
               DeviceDisplayConf display = (DeviceDisplayConf)mapper.convertValue(displayResult, DeviceDisplayConf.class);
               display.setDevice_id(deviceId);
               device.setDisplay(display);
            } else {
               device.setDisplay((DeviceDisplayConf)null);
            }
         } catch (Exception var17) {
            this.logger.error("", var17);
            device.setDisplay((DeviceDisplayConf)null);
         }

         try {
            if (!generalResult.isEmpty()) {
               DeviceGeneralConf general = (DeviceGeneralConf)mapper.convertValue(generalResult, DeviceGeneralConf.class);
               general.setDevice_id(deviceId);
               device.setGeneral(general);
            } else {
               device.setGeneral((DeviceGeneralConf)null);
            }
         } catch (Exception var16) {
            this.logger.error("", var16);
            device.setGeneral((DeviceGeneralConf)null);
         }

         return device;
      } catch (Exception var24) {
         this.logger.error("", var24);
         return null;
      }
   }

   public DeviceControl parsingGettingResultSetToEntity(String deviceId, ResultSet rs) {
      return this.parsingGettingResultSetToEntity(deviceId, rs, true, true, true, true);
   }

   public DeviceControl parsingGettingResultSetToEntity(String deviceId, ResultSet rs, boolean flagGeneral, boolean flagSetup, boolean flagDisplay, boolean flagSecurity) {
      try {
         Map resultMap = rs.getTreeMap();
         Map generalMO = DeviceMOConstants.getGeneralMOEntity();
         Map setupMO = DeviceMOConstants.getSetupMOEntity();
         Map displayMO = DeviceMOConstants.getDisplayMOEntity();
         Map securityMO = DeviceMOConstants.getSecurityMOEntity();
         Map generalResult = new HashMap();
         Map setupResult = new HashMap();
         Map displayResult = new HashMap();
         Map securityResult = new HashMap();
         Iterator var16 = resultMap.keySet().iterator();

         while(true) {
            while(var16.hasNext()) {
               Object moObj = var16.next();
               String mo = moObj.toString();
               if (flagSetup && setupMO.containsKey(mo)) {
                  try {
                     if (rs.getString(mo) != null) {
                        setupResult.put(setupMO.get(mo), rs.getString(mo));
                     }
                  } catch (RMQLException var24) {
                     this.logger.error("", var24);
                  }
               } else if (flagDisplay && displayMO.containsKey(mo)) {
                  try {
                     if (rs.getString(mo) != null) {
                        displayResult.put(displayMO.get(mo), rs.getString(mo));
                     }
                  } catch (RMQLException var27) {
                     this.logger.error("", var27);
                  }
               } else if (flagSecurity && securityMO.containsKey(mo)) {
                  try {
                     if (rs.getString(mo) != null) {
                        securityResult.put(securityMO.get(mo), rs.getString(mo));
                     }
                  } catch (RMQLException var26) {
                     this.logger.error("", var26);
                  }
               } else if (flagGeneral && generalMO.containsKey(mo)) {
                  try {
                     if (rs.getString(mo) != null) {
                        generalResult.put(generalMO.get(mo), rs.getString(mo));
                     }
                  } catch (RMQLException var25) {
                     this.logger.error("", var25);
                  }
               }
            }

            DeviceControl device = new DeviceControl();
            device.setTargetId(deviceId);
            ObjectMapper mapper = new ObjectMapper();
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
            mapper.configure(DeserializationFeature.FAIL_ON_MISSING_CREATOR_PROPERTIES, false);

            try {
               if (!setupResult.isEmpty()) {
                  DeviceSystemSetupConf setup = (DeviceSystemSetupConf)mapper.convertValue(setupResult, DeviceSystemSetupConf.class);
                  setup.setDevice_id(deviceId);
                  device.setSetup(setup);
               } else {
                  device.setSetup((DeviceSystemSetupConf)null);
               }
            } catch (Exception var23) {
               this.logger.error("", var23);
               device.setSetup((DeviceSystemSetupConf)null);
            }

            try {
               if (!securityResult.isEmpty()) {
                  DeviceSecurityConf security = (DeviceSecurityConf)mapper.convertValue(securityResult, DeviceSecurityConf.class);
                  security.setDevice_id(deviceId);
                  device.setSecurity(security);
               } else {
                  device.setSecurity((DeviceSecurityConf)null);
               }
            } catch (Exception var22) {
               this.logger.error("", var22);
               device.setSecurity((DeviceSecurityConf)null);
            }

            try {
               if (!displayResult.isEmpty()) {
                  DeviceDisplayConf display = (DeviceDisplayConf)mapper.convertValue(displayResult, DeviceDisplayConf.class);
                  display.setDevice_id(deviceId);
                  device.setDisplay(display);
               } else {
                  device.setDisplay((DeviceDisplayConf)null);
               }
            } catch (Exception var21) {
               this.logger.error("", var21);
               device.setDisplay((DeviceDisplayConf)null);
            }

            try {
               if (!generalResult.isEmpty()) {
                  DeviceGeneralConf general = (DeviceGeneralConf)mapper.convertValue(generalResult, DeviceGeneralConf.class);
                  general.setDevice_id(deviceId);
                  device.setGeneral(general);
               } else {
                  device.setGeneral((DeviceGeneralConf)null);
               }
            } catch (Exception var20) {
               this.logger.error("", var20);
               device.setGeneral((DeviceGeneralConf)null);
            }

            return device;
         }
      } catch (Exception var28) {
         this.logger.error("", var28);
         return null;
      }
   }
}
