package com.samsung.magicinfo.openapi.scenario.model;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.openapi.auth.TokenUtil;
import com.samsung.magicinfo.openapi.custom.auth.AuthService;
import com.samsung.magicinfo.openapi.scenario.info.ScenarioGroup;
import com.samsung.magicinfo.openapi.scenario.info.ScenarioId;
import com.samsung.magicinfo.openapi.scenario.info.ScenarioItem;
import com.samsung.magicinfo.openapi.scenario.info.Scenarios;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Collection;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

@Service("scenarioModelManager")
@Scope("prototype")
public class ScenarioModelManager {
   protected Logger logger = LoggingManagerV2.getLogger(ScenarioModelManager.class);
   private String authToken;
   @Autowired
   private OpenApiScenarioModel scenarioModel;
   @Autowired
   private ScenarioOperationProvider operationProvider;

   public ScenarioModelManager() {
      super();
   }

   public ScenarioItem getScenario(ScenarioId id) {
      return this.scenarioModel.getScenarioByName(id.getScenarioGroup(), id.getScenarioName());
   }

   public ScenarioItem getScenario(String groupName, String scenarioName) {
      return this.scenarioModel.getScenarioByName(groupName, scenarioName);
   }

   public void updateScenario(String groupName, ScenarioItem scenarioItem) {
      this.scenarioModel.updateScenario(groupName, scenarioItem);
   }

   public void removeScenario(String scenarioGroup, String scenarioName) {
      this.scenarioModel.removeScenario(scenarioGroup, scenarioName);
   }

   public void removeScenarioGroup(String scenarioGroup) {
      List scList = new LinkedList();
      scList.addAll(this.scenarioModel.getScenariosByGroup(scenarioGroup));
      Iterator var3 = scList.iterator();

      while(var3.hasNext()) {
         ScenarioItem sc = (ScenarioItem)var3.next();
         this.scenarioModel.removeScenario(scenarioGroup, sc.getName());
      }

   }

   public void resetScenarios() {
      this.scenarioModel.reset();
   }

   public Map executeScenarios(String groupName) {
      if (this.authToken != null && !this.authToken.isEmpty()) {
         return this.executeScenarios(groupName, this.authToken);
      } else {
         this.logger.error("Unable to run scenarios without token information");
         return null;
      }
   }

   public Map executeScenarios(String groupName, String token) {
      List itemsToExecute = this.scenarioModel.getScenariosByGroup(groupName);
      return this.operationProvider.execute(itemsToExecute, token);
   }

   public Map executeAllScenarios(String address, String userName, String password) {
      Map res = new LinkedHashMap();
      String token = TokenUtil.getAuthToken(address, userName, password);
      Set groupSet = this.scenarioModel.getAllGroups().keySet();
      Iterator var7 = groupSet.iterator();

      while(var7.hasNext()) {
         ScenarioGroup group = (ScenarioGroup)var7.next();
         List scenario = group.getScenarioItem();
         Map scenarioRes = this.operationProvider.execute(scenario, token);
         res.put(group, scenarioRes);
      }

      return res;
   }

   public Scenarios importAllScenarios(String location) {
      Scenarios importedScenarios = null;

      try {
         URL urlLoc = new URL(location);
         importedScenarios = this.importAllScenarios(urlLoc);
      } catch (MalformedURLException var4) {
         this.logger.error("Scenario location is not corect. Check URL: " + location, var4);
      }

      return importedScenarios;
   }

   public Scenarios importAllScenarios(URL location) {
      Scenarios importedScenarios = this.operationProvider.importScenarios(location);
      if (importedScenarios != null) {
         this.scenarioModel.setScenarios(importedScenarios);
      } else {
         this.logger.error("Could not import scenarios from the selected location, scenario data format is not correct: " + location);
      }

      return importedScenarios;
   }

   public boolean exportAllScenarios(String location) {
      boolean isSuccess = false;

      try {
         URL urlLoc = new URL(location);
         isSuccess = this.exportAllScenarios(urlLoc);
      } catch (MalformedURLException var4) {
         this.logger.error("Scenario location is not corect. Check URL: " + location, var4);
      }

      return isSuccess;
   }

   public boolean exportAllScenarios(URL targetLocation) {
      Scenarios scenariosToExport = this.scenarioModel.getScenarios();
      return this.operationProvider.exportScenarios(targetLocation, scenariosToExport);
   }

   public void addScenario(String groupName, String scenarioName, Collection actionIdList) {
      ScenarioItem scenarioItem = this.operationProvider.buildEmptyScenarioItem(scenarioName, actionIdList);
      this.scenarioModel.addScenario(groupName, scenarioItem);
   }

   public Map getScenarioGroupMap() {
      Map groupMap = this.scenarioModel.getGroupNamesMap();
      return groupMap;
   }

   public Map getServicesMap() {
      return this.operationProvider.getAvailableServices();
   }

   public void setUserInfo(UserContainer uc) {
      AuthService authService = new AuthService();
      this.authToken = authService.getAuthToken(uc, "not_supported");
   }
}
