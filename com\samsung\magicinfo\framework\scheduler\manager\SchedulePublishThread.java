package com.samsung.magicinfo.framework.scheduler.manager;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.ruleset.entity.RuleSet;
import com.samsung.magicinfo.framework.ruleset.manager.RuleSetInfo;
import com.samsung.magicinfo.framework.ruleset.manager.RuleSetInfoImpl;
import com.samsung.magicinfo.framework.ruleset.manager.RulesetUtils;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity;
import java.sql.SQLException;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;

public class SchedulePublishThread implements Runnable {
   private static final Logger logger = LoggingManagerV2.getLogger(SchedulePublishThread.class);
   private static final int SLEEP_TIME = 1000;
   private String id;
   private int mode;

   public SchedulePublishThread(int mode, String id) {
      super();
      this.mode = mode;
      this.id = id;
   }

   public void run() {
      logger.warn("[SchedulePublishThread] start a thread for publish schedules");
      int publishCount = 0;
      if (this.mode == 1) {
         publishCount = this.triggerContents();
      } else if (this.mode == 2) {
         publishCount = this.triggerPlaylists();
      }

      publishCount += this.rulesetTrigger(this.id);
      logger.warn("[SchedulePublishThread] end thread for publish schedules deploy count : " + publishCount);
   }

   private int triggerContents() {
      return this.triggerContents(this.id);
   }

   private int triggerContents(String contentId) {
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      int publishCount = 0;

      try {
         List list = scheduleInfo.getProgramByContentId(contentId);

         for(int i = 0; i < list.size(); ++i) {
            String programId = (String)((Map)list.get(i)).get("program_id");
            if (this.publishSchedule(programId)) {
               ++publishCount;
            }

            if (i % 10 == 0) {
               Thread.sleep(1000L);
            }
         }
      } catch (Exception var7) {
      }

      return publishCount;
   }

   private int triggerPlaylists() {
      return this.triggerPlaylists(this.id);
   }

   private int triggerPlaylists(String playlistId) {
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      PlaylistInfo playlistDao = PlaylistInfoImpl.getInstance();
      int publishCount = 0;

      try {
         Playlist playlist = playlistDao.getPlaylistActiveVerInfo(playlistId);
         List list;
         int i;
         String programId;
         if (playlist.getPlaylist_type() != null && playlist.getPlaylist_type().equals("6")) {
            list = playlistDao.getListLinkedPlaylistProgramId(playlistId);

            for(i = 0; i < list.size(); ++i) {
               programId = (String)list.get(i);
               if (this.publishSchedule(programId)) {
                  ++publishCount;
               }

               if (i % 10 == 0) {
                  Thread.sleep(1000L);
               }
            }
         } else {
            list = scheduleInfo.getProgramByPlaylistId(this.id);

            for(i = 0; i < list.size(); ++i) {
               programId = (String)((Map)list.get(i)).get("program_id");
               if (this.publishSchedule(programId)) {
                  ++publishCount;
               }

               if (i % 10 == 0) {
                  Thread.sleep(1000L);
               }
            }
         }
      } catch (Exception var9) {
         logger.error("[SchedulePublishThread] error to publish schedules e : " + var9.getMessage());
      }

      return publishCount;
   }

   private int rulesetTrigger(String id) {
      String type = this.mode == 1 ? "content" : "playlist";
      RuleSetInfo rulesetInfo = RuleSetInfoImpl.getInstance();
      int publishCount = 0;

      try {
         List rulesetList = rulesetInfo.getRulesetUsingContents(id);

         String rulesetId;
         for(Iterator var6 = rulesetList.iterator(); var6.hasNext(); publishCount += this.triggerContents(rulesetId)) {
            RuleSet ruleset = (RuleSet)var6.next();
            rulesetId = ruleset.getRuleset_id();
            ContentFile ruleMetaFile = RulesetUtils.updateContentInRuleset(rulesetId, id, type);
            String oldFileId = ruleset.getFile_id();
            if (rulesetInfo.updateRulesetMetaFile(rulesetId, ruleMetaFile.getFile_id())) {
               try {
                  if (StringUtils.isNotBlank(oldFileId)) {
                     ContentInfo contentInfo = ContentInfoImpl.getInstance();
                     contentInfo.deleteFile(oldFileId);
                  }
               } catch (Exception var12) {
                  logger.error(var12);
               }
            }
         }
      } catch (SQLException var13) {
         var13.printStackTrace();
      }

      return publishCount;
   }

   private boolean publishSchedule(String programId) throws Exception {
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();
      scheduleInfo.programVersionUp(programId);
      ProgramEntity program = scheduleInfo.getProgram(programId);
      if (program.getDeploy_time() == null || program.getDeploy_time().equals("null") || program.getDeploy_time().equals("")) {
         try {
            scheduleInfo.reserveSchedule(program);
            logger.warn("[SchedulePublishThread] Playlist trigger playlistId : " + this.id + " programId : " + program.getProgram_id());
         } catch (Exception var5) {
            logger.error("[SchedulePublishThread] fail to publish schedule when triggering playlist programId : " + program.getProgram_id());
            return false;
         }
      }

      return true;
   }
}
