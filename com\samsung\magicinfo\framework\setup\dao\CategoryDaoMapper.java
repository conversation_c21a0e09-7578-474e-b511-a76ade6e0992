package com.samsung.magicinfo.framework.setup.dao;

import com.samsung.magicinfo.framework.setup.entity.CategoryEntity;
import java.sql.SQLException;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CategoryDaoMapper {
   List getCategoryWithPgroupId(@Param("groupId") long var1);

   List getCategoryWithContentId(@Param("contentId") String var1) throws SQLException;

   List getCategoryWithPgroupIdOrganization(@Param("groupId") long var1, @Param("organization") String var3);

   boolean addCategory(@Param("category") CategoryEntity var1) throws SQLException;

   boolean deleteCategory(@Param("groupId") long var1) throws SQLException;

   CategoryEntity getCategory(@Param("groupId") long var1) throws SQLException;

   List getCategoryList(@Param("contentId") String var1) throws SQLException;

   List getTagList(@Param("contentId") String var1) throws SQLException;

   boolean setCategoryFromContentId(@Param("groupId") long var1, @Param("contentId") String var3) throws SQLException;

   boolean setCategoryFromPlaylistId(@Param("groupId") long var1, @Param("playlistId") String var3) throws SQLException;

   boolean deleteCategoryFromContentId(@Param("contentId") String var1) throws SQLException;

   boolean deleteCategoryFromPlaylistId(@Param("playlistId") String var1) throws SQLException;

   boolean moveCategory(@Param("parentId") long var1, @Param("groupId") long var3) throws SQLException;

   boolean updateCategory(@Param("category") CategoryEntity var1) throws SQLException;

   List getCategoryWithPlaylistId(@Param("playlistId") String var1) throws SQLException;

   List getCategoryByMultipleOrg(@Param("userList") List var1) throws SQLException;
}
