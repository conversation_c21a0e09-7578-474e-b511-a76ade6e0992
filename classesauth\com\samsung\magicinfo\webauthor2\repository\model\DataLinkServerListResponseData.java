package com.samsung.magicinfo.webauthor2.repository.model;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.FIELD)
public class DataLinkServerListResponseData implements Serializable {
  private static final long serialVersionUID = 1681346742386561631L;
  
  @XmlAttribute
  private String code;
  
  @XmlElement
  private String errorMessage;
  
  @XmlElement
  private DataLinkServerListResultListData responseClass;
  
  public String getCode() {
    return this.code;
  }
  
  public String getErrorMessage() {
    return this.errorMessage;
  }
  
  public DataLinkServerListResultListData getResponseClass() {
    return this.responseClass;
  }
}
