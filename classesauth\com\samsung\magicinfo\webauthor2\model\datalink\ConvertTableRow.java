package com.samsung.magicinfo.webauthor2.model.datalink;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

public class ConvertTableRow {
  private String type = "";
  
  private final String from;
  
  private final Value toValue;
  
  @JsonCreator
  public ConvertTableRow(@JsonProperty("type") String type, @JsonProperty("from") String from, @JsonProperty("toValue") Value toValue) {
    this.type = type;
    this.from = from;
    this.toValue = toValue;
  }
  
  public String getType() {
    return this.type;
  }
  
  public String getFrom() {
    return this.from;
  }
  
  public Value getToValue() {
    return this.toValue;
  }
}
