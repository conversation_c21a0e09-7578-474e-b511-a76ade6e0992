package com.samsung.magicinfo.cms.model;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Pattern;

public class ContentFileResource {
   @ApiModelProperty(
      example = "00000000-0000-0000-0000-000000000000"
   )
   @Pattern(
      regexp = "^[0-9A-Fa-f]{8}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{12}$",
      message = "Uploaded file id."
   )
   private String fileId = "";
   @ApiModelProperty(
      example = "00000000-0000-0000-0000-000000000000"
   )
   @Pattern(
      regexp = "^[0-9A-Fa-f]{8}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{12}$",
      message = "Created content id with uploaded file."
   )
   private String contentId = "";

   public ContentFileResource() {
      super();
   }

   public String getFileId() {
      return this.fileId;
   }

   public void setFileId(String fileId) {
      this.fileId = fileId;
   }

   public String getContentId() {
      return this.contentId;
   }

   public void setContentId(String contentId) {
      this.contentId = contentId;
   }
}
