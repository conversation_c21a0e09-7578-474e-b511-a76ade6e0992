package com.samsung.magicinfo.framework.device.deviceInfo.dao;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.exception.BasicException;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SequenceDB;
import com.samsung.magicinfo.framework.device.constants.DeviceConstants;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.log.entity.ServerLogEntity;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManager;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManagerImpl;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.interfaces.WSCall;
import com.samsung.magicinfo.rms.model.DeviceGroupFilter;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;
import org.springframework.context.support.ResourceBundleMessageSource;

public class DeviceGroupDao extends SqlSessionBaseDao {
   private static final Logger logger = LoggingManagerV2.getLogger(DeviceGroupDao.class);

   public DeviceGroupDao() {
      super();
   }

   public DeviceGroupDao(SqlSession session) {
      super(session);
   }

   public int addGroup(DeviceGroup deviceGroup) throws SQLException, ConfigException {
      if (deviceGroup.getP_group_id().intValue() >= -1 && deviceGroup.getP_group_id().intValue() != 999999) {
         String groupType = "";
         DeviceGroup parentDeviceGroup = this.getGroup(deviceGroup.getP_group_id().intValue());
         if (parentDeviceGroup != null && parentDeviceGroup.getGroup_type() != null && !parentDeviceGroup.getGroup_type().equals("")) {
            groupType = parentDeviceGroup.getGroup_type();
         }

         if (groupType == null || groupType.equals("")) {
            parentDeviceGroup = this.getDeviceTopGroup(deviceGroup.getP_group_id().intValue());
            if (parentDeviceGroup != null && parentDeviceGroup.getGroup_type() != null && !parentDeviceGroup.getGroup_type().equals("")) {
               groupType = parentDeviceGroup.getGroup_type();
            }
         }

         deviceGroup.setGroup_type(groupType);
         SqlSession session = this.openNewSession(false);

         byte var8;
         try {
            int group_id = false;
            int cnt = false;
            boolean isRoot = false;
            int group_id = SequenceDB.getNextValue("MI_DMS_INFO_GROUP");
            if (group_id != -1) {
               if (deviceGroup.getIs_root() != null) {
                  isRoot = deviceGroup.getIs_root();
               }

               int cnt = ((DeviceGroupDaoMapper)this.getMapper(session)).addGroup(group_id, deviceGroup, isRoot);
               ((DeviceGroupDaoMapper)this.getMapper(session)).addUserToGroup(group_id, deviceGroup.getCreator_id());
               if (cnt > 0) {
                  session.commit();
                  int var16 = group_id;
                  return var16;
               }

               session.rollback();
               var8 = -1;
               return var8;
            }

            var8 = -1;
         } catch (SQLException var12) {
            logger.error("An unexpected error occurred during persistence operation.", var12);
            session.rollback();
            throw var12;
         } finally {
            session.close();
         }

         return var8;
      } else {
         throw new SQLException("Cannot add this group to appointed location ");
      }
   }

   public boolean addGroupForOrg(String strOrgName, String userId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var9;
      try {
         DeviceGroupDaoMapper mapper = (DeviceGroupDaoMapper)this.getMapper(session);
         int group_id_basic = false;
         int cnt2 = false;
         int group_id = SequenceDB.getNextValue("MI_DMS_INFO_GROUP");
         int group_id_basic = SequenceDB.getNextValue("MI_DMS_INFO_GROUP");
         if (group_id != -1 && group_id_basic != -1) {
            int cnt = mapper.addGroupForOrg(strOrgName, userId, new Long((long)group_id), new Long(0L), new Long(1L), "default org group", "");
            int cnt2 = mapper.addGroupForOrg("default", userId, new Long((long)group_id_basic), new Long((long)group_id), new Long(2L), "basic", "");
            if (cnt2 > 0 && cnt > 0) {
               session.commit();
               var9 = true;
               return var9;
            }

            session.rollback();
            var9 = false;
            return var9;
         }

         var9 = false;
      } catch (SQLException var13) {
         session.rollback();
         throw var13;
      } finally {
         session.close();
      }

      return var9;
   }

   public boolean canDeleteOrgGroups(String strOrg) throws SQLException {
      int iOrgRootGroup = this.getDeviceGroupForUser(strOrg);
      boolean result = false;
      if (iOrgRootGroup < 0) {
         return result;
      } else {
         List children = this.getChildDeviceList(iOrgRootGroup, true);
         if (children != null && children.size() != 0) {
            if (children.size() > 0) {
               result = false;
            }
         } else {
            result = true;
         }

         return result;
      }
   }

   public boolean canDeleteDeviceGroup(int groupId) throws SQLException {
      DeviceGroup group = ((DeviceGroupDaoMapper)this.getMapper()).getGroup(groupId);
      if (group.getP_group_id() <= 0L) {
         return false;
      } else {
         if (group.getGroup_depth() == 2L) {
            DeviceGroupFilter condition = new DeviceGroupFilter();
            condition.setParentGroupId(group.getP_group_id());
            List skipIds = new ArrayList();
            skipIds.add(group.getGroup_id());
            List groupsSameDepth = ((DeviceGroupDaoMapper)this.getMapper()).getDeviceGroupList(condition, skipIds);
            if (groupsSameDepth == null || groupsSameDepth.size() <= 0) {
               return false;
            }
         }

         List children = this.getChildDeviceList(groupId, true);
         return children == null || children.size() <= 0;
      }
   }

   public boolean deleteChildGroupAndDevice(Long groupId, String userId) throws SQLException, ConfigException {
      if (groupId <= 0L) {
         logger.error("Block to remove device group of root_group_id");
         return false;
      } else {
         List deviceIdList = this.getChildDeviceIdList(groupId.intValue(), true);
         if (CommonConfig.get("e2e.enable") != null && CommonConfig.get("e2e.enable").equalsIgnoreCase("true") && deviceIdList.size() > 0) {
            logger.error("Cannot delete device group which contains device on E2E mode");
            return false;
         } else {
            DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
            ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
            DeviceLogManager logMgr = DeviceLogManagerImpl.getInstance();
            ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
            rms.setBasename("resource/messages");
            SqlSession session = this.openNewSession(false);

            try {
               MonitoringManager monMgr = MonitoringManagerImpl.getInstance();

               boolean result;
               String eventType;
               String strMenu;
               for(int i = 0; i < deviceIdList.size(); ++i) {
                  result = deviceDao.deleteDevice((String)deviceIdList.get(i));
                  if (!result) {
                     boolean var12 = false;
                     return var12;
                  }

                  try {
                     WSCall.setPlayerRequest((String)deviceIdList.get(i), "agent restart");
                  } catch (BasicException var23) {
                     logger.error("", var23);
                  }

                  new ServerLogEntity();
                  String eventType = "";
                  String strMenu = "";
                  eventType = "";
                  strMenu = "";
                  eventType = "TEXT_TITLE_DELETE_DEVICE_P";
                  strMenu = rms.getMessage("COM_SID_MENU", (Object[])null, Locale.ENGLISH);
                  eventType = rms.getMessage("TEXT_DEVICE_P", (Object[])null, Locale.ENGLISH);
                  strMenu = rms.getMessage("TEXT_TITLE_DELETE_DEVICE_P", (Object[])null, Locale.ENGLISH);
                  monMgr.connectionReload((String)deviceIdList.get(i), 0);
                  monMgr.scheduleReload((String)deviceIdList.get(i), 0);
               }

               List groupIdList = this.getChildGroupIdList(groupId.intValue(), true);
               groupIdList.add(groupId);

               for(int j = 0; j < groupIdList.size(); ++j) {
                  String programId = this.getDefaultProgramId((Long)groupIdList.get(j));
                  if (programId != null && !programId.equals("")) {
                     schInfo.deleteProgram(programId);
                  }

                  int cnt = ((DeviceGroupDaoMapper)this.getMapper(session)).deleteChildGroupAndDevice((Long)groupIdList.get(j));
                  ((DeviceGroupDaoMapper)this.getMapper(session)).delMapGroupUser((Long)groupIdList.get(j));
                  if (cnt <= 0) {
                     session.rollback();
                     boolean var30 = false;
                     return var30;
                  }

                  new ServerLogEntity();
                  eventType = "";
                  strMenu = "";
                  String strMenuName = "";
                  String strCommand = "";
                  eventType = rms.getMessage("TEXT_TITLE_DEVICE_GROUP_DELETE_P", (Object[])null, Locale.ENGLISH);
                  strMenu = rms.getMessage("COM_SID_MENU", (Object[])null, Locale.ENGLISH);
                  strMenuName = rms.getMessage("TEXT_DEVICE_P", (Object[])null, Locale.ENGLISH);
                  strCommand = rms.getMessage("TEXT_TITLE_DELETE_GROUP_P", (Object[])null, Locale.ENGLISH);
               }

               session.commit();
               result = true;
               return result;
            } catch (SQLException var24) {
               logger.error("An unexpected error occurred during persistence operation.", var24);
               session.rollback();
               throw var24;
            } finally {
               session.close();
            }
         }
      }
   }

   public boolean deleteOrgGroups(String strOrg) throws SQLException {
      int iOrgRootGroup = this.getDeviceGroupForUser(strOrg);
      List groupIdList = this.getChildGroupIdList(iOrgRootGroup, true);
      groupIdList.add((long)iOrgRootGroup);
      SqlSession session = this.openNewSession(false);

      try {
         for(int i = 0; i < groupIdList.size(); ++i) {
            Long groupId = (Long)groupIdList.get(i);
            int cnt = ((DeviceGroupDaoMapper)this.getMapper(session)).deleteOrgGroup(groupId);
            ((DeviceGroupDaoMapper)this.getMapper(session)).delMapGroupUser(groupId);
            if (cnt <= 0) {
               session.rollback();
               boolean var8 = false;
               return var8;
            }
         }

         session.commit();
         boolean var14 = true;
         return var14;
      } catch (SQLException var12) {
         logger.error("An unexpected error occurred during persistence operation.", var12);
         session.rollback();
         throw var12;
      } finally {
         session.close();
      }
   }

   public boolean delGroup(int group_id) throws SQLException {
      List childDeviceIds = this.getChildDeviceIdList(group_id, false);
      if (childDeviceIds != null && childDeviceIds.size() > 0) {
         throw new SQLException("Cannot delete this group because exist device(s).");
      } else {
         List childGroupIds = this.getChildGroupIdList(group_id, false);
         if (childGroupIds != null && childGroupIds.size() > 0) {
            throw new SQLException("Cannot delete this group because exist child group.");
         } else if (group_id <= 0) {
            throw new SQLException("Cannot deleted this group.");
         } else {
            DeviceGroup deviceGroup = this.getGroup(group_id);
            if (deviceGroup == null) {
               throw new SQLException("Cannot deleted this group.");
            } else if (0 != deviceGroup.getGroup_id().intValue() && 999999 != deviceGroup.getGroup_id().intValue()) {
               SqlSession session = this.openNewSession(false);

               boolean var7;
               try {
                  int cnt = ((DeviceGroupDaoMapper)this.getMapper(session)).delGroup(group_id);
                  ((DeviceGroupDaoMapper)this.getMapper(session)).delMapGroupUser(new Long((long)group_id));
                  if (cnt <= 0) {
                     session.rollback();
                     var7 = false;
                     return var7;
                  }

                  session.commit();
                  var7 = true;
               } catch (SQLException var11) {
                  logger.error("An unexpected error occurred during persistence operation.", var11);
                  session.rollback();
                  throw var11;
               } finally {
                  session.close();
               }

               return var7;
            } else {
               throw new SQLException("Cannot deleted this group.");
            }
         }
      }
   }

   public List getChildDeviceIdList(int groupId, boolean recursive, SqlSession sqlSession) throws SQLException {
      List rtnList = new ArrayList();
      List deviceIdList = null;
      if (recursive) {
         deviceIdList = ((DeviceGroupDaoMapper)this.getMapper(sqlSession)).getChildDeviceIdListRecursive(groupId);
      } else {
         deviceIdList = ((DeviceGroupDaoMapper)this.getMapper(sqlSession)).getChildDeviceIdList(groupId);
      }

      if (deviceIdList != null && deviceIdList.size() > 0) {
         for(int i = 0; i < deviceIdList.size(); ++i) {
            rtnList.add(((Map)deviceIdList.get(i)).get("device_id").toString());
         }
      }

      return rtnList;
   }

   public List getChildDeviceIdList(int groupId, boolean recursive) throws SQLException {
      return this.getChildDeviceIdList(groupId, recursive, (SqlSession)null);
   }

   public long getTotalOrganizationDeviceCountByGroupId(long groupId) throws SQLException {
      long rtnList = 0L;
      rtnList = ((DeviceGroupDaoMapper)this.getMapper()).getTotalOrganizationDeviceCountByGroupId(groupId);
      return rtnList;
   }

   public List getTotalOrganizationDeviceCountByGroupId(List deviceGroups) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getTotalOrganizationDeviceCountByGroupIds(deviceGroups);
   }

   public long getTotalApprovalDeviceCount() throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getTotalApprovalDeviceCount();
   }

   public List getDeviceCountByOrganization() throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getDeviceCountByOrganization();
   }

   public List getChildDeviceIdListByOrganName(String organName) throws SQLException {
      List rtnList = new ArrayList();
      List deviceIdList = ((DeviceGroupDaoMapper)this.getMapper()).getChildDeviceIdListByOrganName(organName);
      if (deviceIdList != null) {
         for(int i = 0; i < deviceIdList.size(); ++i) {
            rtnList.add(((Map)deviceIdList.get(i)).get("device_id").toString());
         }
      }

      return rtnList;
   }

   public List getChildDeviceIdListByGroupPermission(int groupId, boolean recursive, String userId) throws SQLException {
      List rtnList = new ArrayList();
      List deviceIdList = null;
      if (this.checkChildPermissions2(userId, (long)groupId) > 0) {
         deviceIdList = ((DeviceGroupDaoMapper)this.getMapper()).getChildDeviceIdList(groupId);
      }

      if (deviceIdList != null) {
         for(int i = 0; i < deviceIdList.size(); ++i) {
            rtnList.add(((Map)deviceIdList.get(i)).get("device_id").toString());
         }
      }

      if (recursive) {
         List groupIdList = this.getChildGroupIdList(groupId, true);
         Iterator iter = groupIdList.iterator();

         while(iter.hasNext()) {
            Long group = (Long)iter.next();
            if (this.checkChildPermissions2(userId, group) > 0) {
               List subGroupDeviceIdList = this.getChildDeviceIdList(group.intValue(), false);
               if (subGroupDeviceIdList != null && subGroupDeviceIdList.size() != 0) {
                  rtnList.addAll(subGroupDeviceIdList);
               }
            }
         }
      }

      return rtnList;
   }

   public List getChildDeviceIdList(int group_id) throws SQLException {
      List rtnList = new ArrayList();
      List deviceIdList = ((DeviceGroupDaoMapper)this.getMapper()).getChildDeviceIdList2(group_id);
      if (deviceIdList != null) {
         for(int i = 0; i < deviceIdList.size(); ++i) {
            rtnList.add(((Map)deviceIdList.get(i)).get("device_id").toString());
         }
      }

      return rtnList;
   }

   public List getChildDeviceList(int groupId, boolean recursive) throws SQLException {
      List deviceList = ((DeviceGroupDaoMapper)this.getMapper()).getChildDeviceList(groupId);
      if (recursive) {
         List groupIdList = this.getChildGroupIdList(groupId, true);
         Iterator iter = groupIdList.iterator();

         while(iter.hasNext()) {
            Long group = (Long)iter.next();
            List subGroupDeviceList = this.getChildDeviceList(group.intValue(), false);
            if (subGroupDeviceList != null && subGroupDeviceList.size() != 0) {
               deviceList.addAll(subGroupDeviceList);
            }
         }
      }

      return deviceList;
   }

   public List getChildDeviceList(int groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getChildDeviceList2(groupId);
   }

   public List getChildGroupIdList(int group_id, boolean recursive) throws SQLException {
      List rtList = new ArrayList();
      List groupIdList = ((DeviceGroupDaoMapper)this.getMapper()).getChildGroupIdList((long)group_id, 999999L);
      if (groupIdList != null) {
         for(int i = 0; i < groupIdList.size(); ++i) {
            if (recursive) {
               Long group = (Long)((Map)groupIdList.get(i)).get("group_id");
               rtList.add(group);
               List temp = this.getChildGroupIdList(group.intValue(), recursive);
               if (temp != null && temp.size() != 0) {
                  rtList.addAll(temp);
               }
            } else {
               rtList.add((Long)((Map)groupIdList.get(i)).get("group_id"));
            }
         }
      }

      return rtList;
   }

   public List getChildGroupIdList(int group_id, String device_type) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getChildGroupIdLists(group_id, device_type, 999999);
   }

   public List getChildGroupList(int group_id, boolean recursive) throws SQLException {
      return recursive ? ((DeviceGroupDaoMapper)this.getMapper()).getAllDeviceGroups(new Long((long)group_id), (String)null) : ((DeviceGroupDaoMapper)this.getMapper()).getChildGroupList(new Long((long)group_id), new Long(999999L));
   }

   public List getChildGroupListWithPermission(int group_id, String user_id, boolean recursive) throws SQLException {
      return recursive ? ((DeviceGroupDaoMapper)this.getMapper()).getAllDeviceGroupsWithPermission(new Long((long)group_id), user_id) : ((DeviceGroupDaoMapper)this.getMapper()).getOrgListWithPermission(new Long((long)group_id), new Long(999999L));
   }

   public List getChildGroupListByGroupType(int group_id, boolean recursive, String groupType) throws SQLException {
      String notGroupType = null;
      if (groupType.equalsIgnoreCase("SPLAYER")) {
         notGroupType = "iPLAYER";
      } else {
         notGroupType = "SPLAYER";
      }

      List groupList = new ArrayList();
      List tmpList = ((DeviceGroupDaoMapper)this.getMapper()).getChildGroupListByGroupType(group_id, 999999, notGroupType);
      if (tmpList != null) {
         if (recursive) {
            Iterator iter = tmpList.iterator();

            while(iter.hasNext()) {
               DeviceGroup deviceGroup = (DeviceGroup)iter.next();
               groupList.add(deviceGroup);
               List rtList = this.getChildGroupListByGroupType(deviceGroup.getGroup_id().intValue(), recursive, groupType);
               if (rtList != null && rtList.size() != 0) {
                  groupList.addAll(rtList);
               }
            }
         } else {
            groupList.addAll(tmpList);
         }
      }

      return groupList;
   }

   public List getChildGroupListByGroupTypeForSchedule(int group_id, boolean recursive, String groupType, ArrayList auth_tree_list, String userId, Long minPriority) throws SQLException {
      List groupList = new ArrayList();
      minPriority = minPriority != null ? minPriority : DeviceConstants.DEV_GROUP_BASIC_PRIORITY;
      List tmpList = ((DeviceGroupDaoMapper)this.getMapper()).getChildGroupListByGroupTypeForSchedule2(group_id, 999999);
      if (tmpList != null) {
         if (recursive) {
            List auth_default_group = this.getChildGroupListByGroupTypeForScheduleAuth(group_id, recursive, groupType, userId);
            if (auth_default_group != null && auth_default_group.size() != 0) {
               long depth = ((DeviceGroup)auth_default_group.get(0)).getGroup_depth() - 1L;

               while((long)auth_tree_list.size() <= depth) {
                  auth_tree_list.add(new ArrayList());
               }

               auth_tree_list.add((int)depth, auth_default_group);
            }

            Iterator iter = tmpList.iterator();

            while(iter.hasNext()) {
               DeviceGroup deviceGroup = (DeviceGroup)iter.next();
               groupList.add(deviceGroup);
               List rtList = this.getChildGroupListByGroupTypeForSchedule(deviceGroup.getGroup_id().intValue(), recursive, groupType, auth_tree_list, userId, minPriority);
               if (rtList != null && rtList.size() != 0) {
                  groupList.addAll(rtList);
               }
            }
         } else {
            groupList.addAll(tmpList);
         }
      }

      return groupList;
   }

   public List getAllVWLLayoutGroupList() throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getAllVWLLayoutGroupList();
   }

   public List getChildGroupListByGroupTypeForScheduleAuth(int group_id, boolean recursive, String groupType, String userId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getChildGroupListByGroupTypeForScheduleAuth(group_id, 999999, groupType, userId);
   }

   public String getDefaultProgramId(long groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getDefaultProgramId(groupId);
   }

   public int getDeviceGroupForUser(String strOrg) throws SQLException {
      Long group = ((DeviceGroupDaoMapper)this.getMapper()).getDeviceGroupForUser(strOrg);
      return group == null ? -1 : group.intValue();
   }

   public String getDeviceGroupRoot(int groupId) throws SQLException {
      Map info = ((DeviceGroupDaoMapper)this.getMapper()).getDeviceGroupRoot(groupId);
      int newGroupParentId = ((Long)info.get("P_GROUP_ID")).intValue();
      return newGroupParentId != 0 ? this.getDeviceGroupRoot(newGroupParentId) : (String)info.get("GROUP_NAME");
   }

   public int getDeviceOrgGroupId(int groupId) throws SQLException {
      Map info = ((DeviceGroupDaoMapper)this.getMapper()).getDeviceOrgGroupId(groupId);
      int newGroupParentId = ((Long)info.get("P_GROUP_ID")).intValue();
      return newGroupParentId != 0 ? this.getDeviceOrgGroupId(newGroupParentId) : ((Long)info.get("GROUP_ID")).intValue();
   }

   public Map getDeviceOrganizationByGroupId(int groupId) throws SQLException {
      Map info = ((DeviceGroupDaoMapper)this.getMapper()).getDeviceGroupRoot(groupId);

      try {
         int newGroupParentId = ((Long)info.get("P_GROUP_ID")).intValue();
         return newGroupParentId != 0 ? this.getDeviceOrganizationByGroupId(newGroupParentId) : info;
      } catch (Exception var4) {
         return null;
      }
   }

   public DeviceGroup getGroup(int groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getGroup(groupId);
   }

   public List getGroupType(int group_id) throws SQLException {
      List groupTypeList = null;
      List rtnList = new ArrayList();
      groupTypeList = ((DeviceGroupDaoMapper)this.getMapper()).selectDeviceTypeByGroupId(group_id);
      int loopSize = groupTypeList.size();

      for(int i = 0; i < loopSize; ++i) {
         String deviceType = ((Map)groupTypeList.get(i)).get("device_type").toString();
         String deviceTypeVersion = ((Map)groupTypeList.get(i)).get("device_type_version").toString();
         if (deviceType.equalsIgnoreCase("SPLAYER")) {
            if (deviceTypeVersion.equalsIgnoreCase(CommonDataConstants.TYPE_VERSION_1_0.toString())) {
               rtnList.add("SPLAYER");
            } else if (deviceTypeVersion.equalsIgnoreCase(CommonDataConstants.TYPE_VERSION_2_0.toString())) {
               rtnList.add("S2PLAYER");
            } else if (deviceTypeVersion.equalsIgnoreCase(CommonDataConstants.TYPE_VERSION_3_0.toString())) {
               rtnList.add("S3PLAYER");
            } else if (deviceTypeVersion.equalsIgnoreCase(CommonDataConstants.TYPE_VERSION_4_0.toString())) {
               rtnList.add("S4PLAYER");
            } else if (deviceTypeVersion.equalsIgnoreCase(CommonDataConstants.TYPE_VERSION_5_0.toString())) {
               rtnList.add("S5PLAYER");
            } else if (deviceTypeVersion.equalsIgnoreCase(CommonDataConstants.TYPE_VERSION_6_0.toString())) {
               rtnList.add("S6PLAYER");
            } else if (deviceTypeVersion.equalsIgnoreCase(CommonDataConstants.TYPE_VERSION_7_0.toString())) {
               rtnList.add("S7PLAYER");
            } else if (deviceTypeVersion.equalsIgnoreCase(CommonDataConstants.TYPE_VERSION_9_0.toString())) {
               rtnList.add("S9PLAYER");
            } else if (deviceTypeVersion.equalsIgnoreCase(CommonDataConstants.TYPE_VERSION_10_0.toString())) {
               rtnList.add("S10PLAYER");
            }
         } else if (!deviceType.equalsIgnoreCase("SIGNAGE") && !deviceType.equalsIgnoreCase("SIG_CHILD")) {
            if (deviceType.equalsIgnoreCase("APLAYER")) {
               rtnList.add("APLAYER");
            } else if (deviceType.equalsIgnoreCase("WPLAYER")) {
               rtnList.add("WPLAYER");
            } else if (deviceType.equalsIgnoreCase("S4PLAYER")) {
               rtnList.add("S4PLAYER");
            } else if (deviceType.equalsIgnoreCase("FLIP")) {
               rtnList.add("FLIP");
            } else if (deviceType.equalsIgnoreCase("RLEDBOX")) {
               rtnList.add("RLEDBOX");
            } else if (deviceType.equalsIgnoreCase("RSIGNAGE")) {
               rtnList.add("RSIGNAGE");
            } else if (deviceType.equalsIgnoreCase("RKIOSK")) {
               rtnList.add("RKIOSK");
            } else {
               rtnList.add("iPLAYER");
            }
         } else {
            rtnList.add("SIGNAGE");
         }
      }

      return rtnList;
   }

   public DeviceGroup getGroupByDeviceId(String deviceId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getGroupByDeviceId(deviceId);
   }

   public long getGroupIdByOrgBasic(String organGroupName, String childGroupName) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getGroupIdByOrgBasic(organGroupName, childGroupName);
   }

   public List getGroupList() throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getGroupList(0, 999999);
   }

   public List getGroupList(DeviceGroupFilter params) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getGroupListWithParams(0, 999999, params.getStartIndex(), params.getPageSize(), params);
   }

   public int getGroupListCnt(DeviceGroupFilter params) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getGroupListCntWithParams(0, 999999, params.getStartIndex(), params.getPageSize(), params);
   }

   public Map getGroupNameByDeviceId(String deviceId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getGroupNameByDeviceId(deviceId);
   }

   public String getGroupNameByGroupId(long groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getGroupNameByGroupId(groupId);
   }

   public List getGroupsForOrg(String strOrg) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getGroupsForOrg(strOrg);
   }

   public String getMessageGroupRoot(int groupId) throws SQLException {
      Map info = ((DeviceGroupDaoMapper)this.getMapper()).getMessageGroupRoot(groupId);
      int newGroupParentId = ((Long)info.get("P_GROUP_ID")).intValue();
      return newGroupParentId != 0 ? this.getMessageGroupRoot(newGroupParentId) : (String)info.get("GROUP_NAME");
   }

   public long getOrganGroupIdByName(String orgGroupName) throws SQLException {
      return "ROOT".equalsIgnoreCase(orgGroupName) ? 0L : (Long)((DeviceGroupDaoMapper)this.getMapper()).getOrganGroupIdByName(orgGroupName).get("group_id");
   }

   public Map getOrgGroupId(String groupName) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getOrgGroupId(groupName);
   }

   public String getOrgNameByGroupId(long groupId) throws SQLException {
      return this.getOrgNameByGroupId(groupId, (SqlSession)null);
   }

   public String getOrgNameByGroupId(long groupId, SqlSession session) throws SQLException {
      DeviceGroupDaoMapper mapper = session != null ? (DeviceGroupDaoMapper)this.getMapper(session) : (DeviceGroupDaoMapper)this.getMapper();
      Map groupMap = mapper.getOrgNameByGroupId(groupId);
      if (groupMap == null) {
         return null;
      } else {
         return (Long)groupMap.get("group_depth") <= 1L ? (String)groupMap.get("group_name") : this.getOrgNameByGroupId((Long)groupMap.get("p_group_id"), session);
      }
   }

   public Long getOrgIdByGroupId(long groupId) throws SQLException {
      Map groupMap = ((DeviceGroupDaoMapper)this.getMapper()).getOrgIdByGroupId(groupId);
      if (groupMap == null) {
         return null;
      } else {
         return (Long)groupMap.get("group_depth") <= 1L ? (Long)groupMap.get("group_id") : this.getOrgIdByGroupId((Long)groupMap.get("p_group_id"));
      }
   }

   public int getParentGroupId(int groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getParentGroupId(groupId);
   }

   public String getProgramGroupRoot(int groupId) throws SQLException {
      Map info = ((DeviceGroupDaoMapper)this.getMapper()).getProgramGroupRoot(groupId);
      int newGroupParentId = ((Long)info.get("P_GROUP_ID")).intValue();
      return newGroupParentId > 0 ? this.getProgramGroupRoot(newGroupParentId) : (String)info.get("GROUP_NAME");
   }

   public boolean moveGroup(int group_id, int new_parent_group_id, Long change_in_group_depth) throws SQLException {
      SqlSession sqlSession = this.openNewSession(false);
      boolean rtn = false;
      boolean deviceDepthChanged = false;

      boolean var7;
      try {
         if (group_id > 0) {
            if (group_id == new_parent_group_id) {
               throw new SQLException("Cannot move to oneself group.");
            }

            if (0 != group_id && 999999 != group_id) {
               ((DeviceGroupDaoMapper)this.getMapper(sqlSession)).setTotalCountByGroupIdWithRecursive((long)group_id, "DECREMENT");
               rtn = ((DeviceGroupDaoMapper)this.getMapper(sqlSession)).moveGroup(new Long((long)group_id), new Long((long)new_parent_group_id));
               if (rtn) {
                  deviceDepthChanged = ((DeviceGroupDaoMapper)this.getMapper(sqlSession)).changeGroupDepth(new Long((long)group_id), change_in_group_depth);
               }

               if (rtn && deviceDepthChanged) {
                  ((DeviceGroupDaoMapper)this.getMapper(sqlSession)).setTotalCountByGroupIdWithRecursive((long)group_id, "INCREMENT");
                  sqlSession.commit();
                  return rtn;
               }

               throw new SQLException("fail to move group");
            }

            throw new SQLException("Cannot move to this group.");
         }

         logger.error("Block to move schedule group of root_group_id");
         var7 = false;
      } catch (Exception var11) {
         logger.error("", var11);
         return rtn;
      } finally {
         sqlSession.close();
      }

      return var7;
   }

   public boolean setDefaultProgramId(long groupId, String programId) throws SQLException {
      return this.setDefaultProgramId(groupId, programId, (SqlSession)null);
   }

   public boolean setDefaultProgramId(long groupId, String programId, SqlSession session) throws SQLException {
      DeviceGroupDaoMapper mapper = session != null ? (DeviceGroupDaoMapper)this.getMapper(session) : (DeviceGroupDaoMapper)this.getMapper();
      return mapper.setDefaultProgramId(groupId, programId);
   }

   public boolean setGroup(DeviceGroup deviceGroup) throws SQLException {
      if (deviceGroup.getGroup_id() <= 0L) {
         logger.error("Block to set device group of root_group_id");
         return false;
      } else {
         return ((DeviceGroupDaoMapper)this.getMapper()).setGroup(deviceGroup);
      }
   }

   public boolean setOrgName(String originName, String newName) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).setOrgName(originName, newName);
   }

   public Object getMaxDepth() throws SQLException {
      Long depth_count = null;

      try {
         depth_count = ((DeviceGroupDaoMapper)this.getMapper()).getMaxDepth();
      } catch (SQLException var3) {
         logger.error("", var3);
      }

      return depth_count;
   }

   public List getDefaultDeviceGroup() {
      List default_group = null;

      try {
         default_group = ((DeviceGroupDaoMapper)this.getMapper()).getDefaultDeviceGroup();
      } catch (SQLException var3) {
         logger.error("", var3);
      }

      return default_group;
   }

   public List getSpecificDepthDeviceGroupList(int depth) throws SQLException {
      List result = null;

      try {
         result = ((DeviceGroupDaoMapper)this.getMapper()).getSpecificDepthDeviceGroupList(depth);
      } catch (SQLException var4) {
         logger.error("", var4);
      }

      return result;
   }

   public void getDeviceGroupTreeFirstLevel(String table, String organization, ArrayList tree_list, String sortType, String skipId, String groupType) {
      List default_group = null;

      try {
         default_group = ((DeviceGroupDaoMapper)this.getMapper()).getDeviceGroupTreeFirstLevel(table, organization, skipId, sortType);
         tree_list.add(0, default_group);
         logger.debug("DEFAULT_GROUP SIZE IS " + default_group.size());
         int i;
         DeviceGroup temp_group;
         if (!table.equals("MI_DMS_INFO_VWL_GROUP")) {
            for(i = 0; i < default_group.size(); ++i) {
               temp_group = (DeviceGroup)default_group.get(i);
               if (skipId.equals("") || skipId != null && temp_group.getGroup_id().intValue() != Integer.parseInt(skipId)) {
                  this.getDeviceGroupTreeSpecificLevel(table, temp_group.getGroup_id(), temp_group.getGroup_depth(), tree_list, sortType, skipId, groupType);
               }
            }
         } else {
            for(i = 0; i < default_group.size(); ++i) {
               temp_group = (DeviceGroup)default_group.get(i);
               if (skipId != null && (skipId.equals("") || temp_group.getGroup_id().intValue() != Integer.parseInt(skipId))) {
                  this.getVWLGroupTreeSpecificLevel(temp_group.getGroup_id(), temp_group.getGroup_depth(), tree_list);
               }
            }
         }
      } catch (SQLException var10) {
         logger.error("", var10);
      }

   }

   public void getDeviceGroupTreeFirstLevel(String table, String organization, ArrayList tree_list, ArrayList auth_tree_list, String sortType, String skipId, String groupType, String userId) {
      List default_group = null;

      try {
         default_group = ((DeviceGroupDaoMapper)this.getMapper()).getDeviceGroupTreeFirstLevel2(table, organization, skipId, sortType);
         tree_list.add(0, default_group);
         auth_tree_list.add(0, default_group);
         logger.debug("DEFAULT_GROUP SIZE IS " + default_group.size());
         int i;
         DeviceGroup temp_group;
         if (!table.equals("MI_DMS_INFO_VWL_GROUP")) {
            for(i = 0; i < default_group.size(); ++i) {
               temp_group = (DeviceGroup)default_group.get(i);
               if (skipId.equals("") || skipId != null && temp_group.getGroup_id().intValue() != Integer.parseInt(skipId)) {
                  this.getDeviceGroupTreeSpecificLevel(table, temp_group.getGroup_id(), temp_group.getGroup_depth(), tree_list, auth_tree_list, sortType, skipId, groupType, userId);
               }
            }
         } else {
            for(i = 0; i < default_group.size(); ++i) {
               temp_group = (DeviceGroup)default_group.get(i);
               if (skipId != null && (skipId.equals("") || skipId != null && temp_group.getGroup_id().intValue() != Integer.parseInt(skipId))) {
                  this.getVWLGroupTreeSpecificLevel(temp_group.getGroup_id(), temp_group.getGroup_depth(), tree_list);
               }
            }
         }
      } catch (Exception var12) {
         logger.error("", var12);
      }

   }

   private void getAuthTreeList(String table, long p_group_id, long depth, ArrayList auth_tree_list, String sortType, String skipId, String groupType, String userId) {
      try {
         List auth_default_group = ((DeviceGroupDaoMapper)this.getMapper()).getAuthTreeList(table, p_group_id, userId, groupType, sortType, skipId);
         if (auth_default_group != null && auth_default_group.size() != 0) {
            for(int i = 0; i < auth_default_group.size(); ++i) {
               Long gId = ((DeviceGroup)auth_default_group.get(i)).getGroup_id();
               if (gId != null) {
                  int dCnt = false;
                  int dCnt;
                  if (table.equals("MI_DMS_INFO_GROUP")) {
                     dCnt = this.getCntDeviceInDeviceGroup(gId.intValue());
                     ((DeviceGroup)auth_default_group.get(i)).setDevice_count((long)dCnt);
                  } else {
                     dCnt = this.getCntDeviceInLiteDeviceGroup(gId.intValue());
                     ((DeviceGroup)auth_default_group.get(i)).setDevice_count((long)dCnt);
                  }
               }
            }

            if (auth_tree_list.size() == (int)depth) {
               auth_tree_list.add((int)depth, auth_default_group);
            } else if ((long)auth_tree_list.size() > depth) {
               List authPGroupList = (List)auth_tree_list.get((int)depth);
               authPGroupList.addAll(auth_default_group);
               auth_tree_list.set((int)depth, authPGroupList);
            }
         } else if (auth_tree_list.size() == (int)depth) {
            auth_tree_list.add((int)depth, new ArrayList());
         }
      } catch (Exception var15) {
         logger.error("An unexpected error occurred.", var15);
      }

   }

   public void getDeviceGroupVWLTreeFirstLevel(String table, String organization, ArrayList tree_list, String sortType, String skipId, String groupType) {
      List default_group = null;

      try {
         default_group = ((DeviceGroupDaoMapper)this.getMapper()).getDeviceGroupTreeFirstLevel(table, organization, skipId, sortType);
         tree_list.add(0, default_group);
         logger.debug("DEFAULT_GROUP SIZE IS " + default_group.size());

         for(int i = 0; i < default_group.size(); ++i) {
            DeviceGroup temp_group = (DeviceGroup)default_group.get(i);
            this.getDeviceGroupVWLTreeSpecificLevel(table, temp_group, temp_group.getGroup_id(), temp_group.getGroup_depth(), tree_list, sortType, skipId, groupType);
         }
      } catch (Exception var10) {
         logger.error("An unexpected error occurred.", var10);
      }

   }

   private void getDeviceGroupTreeSpecificLevel(String table, long p_group_id, long depth, ArrayList tree_list, String sortType, String skipId, String groupType) {
      List default_group = null;

      try {
         default_group = ((DeviceGroupDaoMapper)this.getMapper()).getDeviceGroupTreeSpecificLevel(p_group_id, skipId, sortType);
         if (default_group != null && default_group.size() != 0) {
            int i;
            for(i = 0; i < default_group.size(); ++i) {
               Long gId = ((DeviceGroup)default_group.get(i)).getGroup_id();
               if (gId != null) {
                  int dCnt = false;
                  int dCnt;
                  if (table.equals("MI_DMS_INFO_GROUP")) {
                     dCnt = this.getCntDeviceInDeviceGroup(gId.intValue());
                     ((DeviceGroup)default_group.get(i)).setDevice_count((long)dCnt);
                  } else {
                     dCnt = this.getCntDeviceInLiteDeviceGroup(gId.intValue());
                     ((DeviceGroup)default_group.get(i)).setDevice_count((long)dCnt);
                  }

                  ((DeviceGroup)default_group.get(i)).setVwl_group(this.getBooleanVwlGroupId(gId));
               }
            }

            if (tree_list.size() == (int)depth) {
               tree_list.add((int)depth, default_group);
            } else {
               List pGroupList = (List)tree_list.get((int)depth);
               pGroupList.addAll(default_group);
               tree_list.set((int)depth, pGroupList);
            }

            for(i = 0; i < default_group.size(); ++i) {
               DeviceGroup temp_group = (DeviceGroup)default_group.get(i);
               if (skipId != null && (skipId.equals("") || temp_group.getGroup_id().intValue() != Integer.parseInt(skipId))) {
                  this.getDeviceGroupTreeSpecificLevel(table, temp_group.getGroup_id(), temp_group.getGroup_depth(), tree_list, sortType, skipId, groupType);
               }
            }
         }
      } catch (SQLException var14) {
         logger.error("An unexpected error occurred.", var14);
      }

   }

   private void getDeviceGroupTreeSpecificLevel(String table, long p_group_id, long depth, ArrayList tree_list, ArrayList auth_tree_list, String sortType, String skipId, String groupType, String userId) {
      List default_group = null;

      try {
         default_group = ((DeviceGroupDaoMapper)this.getMapper()).getDeviceGroupTreeSpecificLevel2(table, p_group_id, skipId, groupType, sortType);
         if (default_group != null && default_group.size() != 0) {
            int i;
            for(i = 0; i < default_group.size(); ++i) {
               Long gId = ((DeviceGroup)default_group.get(i)).getGroup_id();
               if (gId != null) {
                  int dCnt = false;
                  int dCnt;
                  if (table.equals("MI_DMS_INFO_GROUP")) {
                     dCnt = this.getCntDeviceInDeviceGroup(gId.intValue());
                     ((DeviceGroup)default_group.get(i)).setDevice_count((long)dCnt);
                  } else {
                     dCnt = this.getCntDeviceInLiteDeviceGroup(gId.intValue());
                     ((DeviceGroup)default_group.get(i)).setDevice_count((long)dCnt);
                  }

                  ((DeviceGroup)default_group.get(i)).setVwl_group(this.getBooleanVwlGroupId(gId));
               }
            }

            if (tree_list.size() == (int)depth) {
               tree_list.add((int)depth, default_group);
            } else {
               List pGroupList = (List)tree_list.get((int)depth);
               pGroupList.addAll(default_group);
               tree_list.set((int)depth, pGroupList);
            }

            if (table.equals("MI_DMS_INFO_GROUP")) {
               Long authDepth = ((DeviceGroup)default_group.get(0)).getGroup_depth() - 1L;
               this.getAuthTreeList(table, p_group_id, authDepth, auth_tree_list, sortType, skipId, groupType, userId);
            }

            for(i = 0; i < default_group.size(); ++i) {
               DeviceGroup temp_group = (DeviceGroup)default_group.get(i);
               if (skipId != null && (skipId.equals("") || temp_group.getGroup_id().intValue() != Integer.parseInt(skipId))) {
                  this.getDeviceGroupTreeSpecificLevel(table, temp_group.getGroup_id(), temp_group.getGroup_depth(), tree_list, auth_tree_list, sortType, skipId, groupType, userId);
               }
            }
         }
      } catch (Exception var16) {
         logger.error("An unexpected error occurred.", var16);
      }

   }

   private void getDeviceGroupVWLTreeSpecificLevel(String table, DeviceGroup group, long p_group_id, long depth, ArrayList tree_list, String sortType, String skipId, String groupType) {
      List default_group = null;

      try {
         default_group = ((DeviceGroupDaoMapper)this.getMapper()).getDeviceGroupVWLTreeSpecificLevel(table, p_group_id, skipId, groupType, sortType);
         if (default_group != null && default_group.size() != 0) {
            for(int i = 0; i < default_group.size(); ++i) {
               int dCnt = this.getCntDeviceInDeviceGroup(safeLongToInt(((DeviceGroup)default_group.get(i)).getGroup_id()));
               ((DeviceGroup)default_group.get(i)).setDevice_count((long)dCnt);
            }

            if (tree_list.size() == (int)depth) {
               tree_list.add((int)depth, default_group);
            } else if ((long)tree_list.size() < depth) {
               tree_list.add((int)depth, default_group);
            } else {
               List pGroupList = (List)tree_list.get((int)depth);
               pGroupList.addAll(default_group);
               tree_list.set((int)depth, pGroupList);
            }
         }
      } catch (Exception var14) {
         logger.error("An unexpected error occurred.", var14);
      }

   }

   private void getVWLGroupTreeSpecificLevel(long p_group_id, long depth, ArrayList tree_list) {
      List default_group = null;

      try {
         default_group = ((DeviceGroupDaoMapper)this.getMapper()).getVWLGroupTreeSpecificLevel(p_group_id);
         if (default_group != null && default_group.size() != 0) {
            for(int i = 0; i < default_group.size(); ++i) {
               String consoleId = ((DeviceGroup)default_group.get(i)).getDescription();
               if (consoleId != null) {
                  int dCnt = this.getCntDeviceInVwlConsoleDevice(consoleId);
                  ((DeviceGroup)default_group.get(i)).setDevice_count((long)dCnt);
               }
            }

            if (tree_list.size() == (int)depth) {
               tree_list.add((int)depth, default_group);
            } else {
               List pGroupList = (List)tree_list.get((int)depth);
               pGroupList.addAll(default_group);
               tree_list.set((int)depth, pGroupList);
            }
         }
      } catch (SQLException var10) {
         logger.error("", var10);
      }

   }

   public List getGroupIdByName(String groupName) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getGroupIdByName(groupName);
   }

   public boolean setDeviceGroupType(int groupId, String groupType) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).setDeviceGroupType(groupId, groupType);
   }

   public DeviceGroup getDeviceTopGroup(int groupId) throws SQLException {
      DeviceGroup deviceGroup = ((DeviceGroupDaoMapper)this.getMapper()).getDeviceTopGroup(groupId);
      if (deviceGroup == null) {
         return null;
      } else {
         int groupDepth = deviceGroup.getGroup_depth().intValue();
         if (groupDepth > 2) {
            int pGroupId = deviceGroup.getP_group_id().intValue();
            return this.getDeviceTopGroup(pGroupId);
         } else {
            return deviceGroup;
         }
      }
   }

   public int getCntDeviceInDeviceGroup(int groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getCntDeviceInDeviceGroup(groupId);
   }

   public Boolean getBooleanVwlGroupId(Long groupId) throws SQLException {
      String vwt_id = ((DeviceGroupDaoMapper)this.getMapper()).getBooleanVwlGroupId(groupId);
      return vwt_id != null;
   }

   public int getCntDeviceInLiteDeviceGroup(int groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getCntDeviceInLiteDeviceGroup(groupId);
   }

   public List getRedundancyGroups() throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getRedundancyGroups();
   }

   public boolean isRedundancyGroup(int groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).isRedundancyGroup(groupId);
   }

   public List getRedundantDeviceIdbyGroupId(int groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getRedundantDeviceIdbyGroupId(groupId);
   }

   public boolean setIsRedundancy(long groupId, boolean isRedundancy) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).setIsRedundancy(groupId, isRedundancy);
   }

   public String getVwlLayoutIdByGroupId(String groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getVwlLayoutIdByGroupId(Long.parseLong(groupId));
   }

   public List getVwlLayoutGroupId() throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getVwlLayoutGroupId();
   }

   public String getGroupNameByVwtId(String vwtId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getGroupNameByVwtId(vwtId);
   }

   public boolean setVwtId(String deviceId, String vwtId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).setVwtId(deviceId, vwtId);
   }

   public boolean isVwlGroup(String groupId) throws SQLException {
      String result = ((DeviceGroupDaoMapper)this.getMapper()).isVwlGroup(Long.parseLong(groupId));
      return result != null;
   }

   public String getGroupType(String pid) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getGroupType(Long.parseLong(pid));
   }

   public boolean isVWLLayoutGroup(String deviceGroupIds) throws SQLException {
      if (deviceGroupIds == null) {
         return false;
      } else {
         String[] deviceGroupList = deviceGroupIds.split(",");
         if (deviceGroupList.length != 0 && !deviceGroupList[0].equals("")) {
            List list = new ArrayList();
            String[] var4 = deviceGroupList;
            int var5 = deviceGroupList.length;

            for(int var6 = 0; var6 < var5; ++var6) {
               String s = var4[var6];
               list.add(Long.parseLong(s));
            }

            int result = ((DeviceGroupDaoMapper)this.getMapper()).isVWLLayoutGroup(list);
            return result > 0;
         } else {
            return false;
         }
      }
   }

   public Long getMinimumPriority(String deviceGroupIds) throws SQLException {
      String[] deviceGroupList = deviceGroupIds.split(",");
      if (deviceGroupList.length != 0 && !deviceGroupList[0].equals("")) {
         List list = new ArrayList();
         String[] var4 = deviceGroupList;
         int var5 = deviceGroupList.length;

         for(int var6 = 0; var6 < var5; ++var6) {
            String s = var4[var6];
            list.add(Long.parseLong(s));
         }

         Long cnt = ((DeviceGroupDaoMapper)this.getMapper()).getMinimumPriority(list);
         return cnt != null ? cnt : 0L;
      } else {
         return 0L;
      }
   }

   public boolean cancelVwlGroup(String groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).cancelVwlGroup(Long.parseLong(groupId));
   }

   public boolean updateDeviceGroupPriority(Long groupPriority, long groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).updateDeviceGroupPriority(groupPriority, groupId);
   }

   public String getDeviceTypeByMinimumPriority(Long minimumPriority) throws SQLException {
      return minimumPriority != 999999L ? ((DeviceGroupDaoMapper)this.getMapper()).getDeviceTypeByMinimumPriority(minimumPriority) : "SPLAYER";
   }

   public Float getDeviceTypeVersionByMinimumPriority(Long minimumPriority) throws SQLException {
      return minimumPriority != 999999L ? ((DeviceGroupDaoMapper)this.getMapper()).getDeviceTypeVersionByMinimumPriority(minimumPriority) : CommonDataConstants.TYPE_VERSION_1_0;
   }

   public Long getPriority(String deviceType, Float deviceTypeVersion) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getPriority(deviceType, deviceTypeVersion);
   }

   public boolean addPermissionsDeviceGroup(String userId, String groupId) throws SQLException {
      int cnt = false;
      int cnt = ((DeviceGroupDaoMapper)this.getMapper()).deleteFromMapGroupUser(userId);
      if (groupId != null && !groupId.equals("")) {
         String[] groupIdList = groupId.split(",");
         String[] var5 = groupIdList;
         int var6 = groupIdList.length;

         for(int var7 = 0; var7 < var6; ++var7) {
            String s = var5[var7];
            cnt = ((DeviceGroupDaoMapper)this.getMapper()).insertToMapGroupUser(userId, Long.parseLong(s));
         }
      }

      return cnt > 0;
   }

   public boolean v2AddPermissionsDeviceGroup(String userId, String groupId) throws SQLException {
      int cnt = 0;
      if (groupId != null && !groupId.equalsIgnoreCase("")) {
         cnt = ((DeviceGroupDaoMapper)this.getMapper()).insertToMapGroupUser(userId, Long.parseLong(groupId));
      }

      return cnt > 0;
   }

   public boolean v2DeleteDeviceGroup(String userId) throws SQLException {
      int cnt = 0;
      if (userId != null && !userId.equalsIgnoreCase("")) {
         cnt = ((DeviceGroupDaoMapper)this.getMapper()).deleteFromMapGroupUser(userId);
      }

      return cnt > 0;
   }

   public List getAuthDeviceGroupList(String userId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getAuthDeviceGroupList(userId);
   }

   public List getPermissionsDeviceGroup(String userId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getPermissionsDeviceGroup(userId);
   }

   public List getOrganizationGroup() throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getOrganizationGroup();
   }

   public List getOrgId(String orgName) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getOrgId(orgName);
   }

   public boolean checkExistGroupId(int groupId, String userId) throws SQLException {
      int count = ((DeviceGroupDaoMapper)this.getMapper()).checkExistGroupId((long)groupId, userId);
      return count > 0;
   }

   public boolean checkExistUserId(String userId) throws SQLException {
      int count = ((DeviceGroupDaoMapper)this.getMapper()).checkExistUserId(userId);
      return count > 0;
   }

   public boolean checkChildPermissions(int groupId, String userId) throws SQLException {
      ArrayList tree_list = new ArrayList();
      List default_group = null;
      String table = "MI_DMS_INFO_GROUP";
      default_group = ((DeviceGroupDaoMapper)this.getMapper()).checkChildPermissions1(table, (long)groupId);
      tree_list.add(0, default_group);
      logger.debug("DEFAULT_GROUP SIZE IS " + default_group.size());

      int i;
      for(i = 0; i < default_group.size(); ++i) {
         DeviceGroup temp_group = (DeviceGroup)default_group.get(i);
         this.getAuthDeviceGroupTreeSpecificLevel(table, temp_group.getGroup_id(), 0L, tree_list);
      }

      for(i = 0; i < tree_list.size(); ++i) {
         for(int j = 0; j < ((List)tree_list.get(0)).size(); ++j) {
            long group = ((DeviceGroup)((List)tree_list.get(i)).get(j)).getGroup_id();
            int count = ((DeviceGroupDaoMapper)this.getMapper()).checkChildPermissions2(userId, group);
            if (count > 0) {
               return true;
            }
         }
      }

      return false;
   }

   private void getAuthDeviceGroupTreeSpecificLevel(String table, long p_group_id, long depthTemp, ArrayList tree_list) {
      List default_group = null;
      Object[] args = new Object[]{p_group_id};
      long depth = depthTemp + 1L;

      try {
         default_group = ((DeviceGroupDaoMapper)this.getMapper()).getAuthDeviceGroupTreeSpecificLevel(table, p_group_id);
         if (default_group != null && default_group.size() != 0) {
            int i;
            for(i = 0; i < default_group.size(); ++i) {
               Long gId = ((DeviceGroup)default_group.get(i)).getGroup_id();
               if (gId != null) {
                  int dCnt = this.getCntDeviceInDeviceGroup(gId.intValue());
                  ((DeviceGroup)default_group.get(i)).setDevice_count((long)dCnt);
               }
            }

            tree_list.add((int)depth, default_group);

            for(i = 0; i < default_group.size(); ++i) {
               DeviceGroup temp_group = (DeviceGroup)default_group.get(i);
               this.getAuthDeviceGroupTreeSpecificLevel(table, temp_group.getGroup_id(), depthTemp, tree_list);
            }
         }
      } catch (Exception var14) {
         logger.error("An unexpected error occurred.", var14);
      }

   }

   public List getScheduleMappingDeviceGroupAuth(String programId, String userId, boolean include) {
      List deviceGroupList = null;

      try {
         deviceGroupList = ((DeviceGroupDaoMapper)this.getMapper()).getScheduleMappingDeviceGroupAuth(programId, userId, include);
      } catch (SQLException var6) {
         logger.error("An unexpected error occurred.", var6);
      }

      return deviceGroupList;
   }

   public List getMessageMappingDeviceGroupAuth(String programId, String userId, boolean include) {
      List deviceGroupList = null;

      try {
         deviceGroupList = ((DeviceGroupDaoMapper)this.getMapper()).getMessageMappingDeviceGroupAuth(programId, userId, include);
      } catch (SQLException var6) {
         logger.error("An unexpected error occurred.", var6);
      }

      return deviceGroupList;
   }

   public List getEventMappingDeviceGroupAuth(String programId, String userId, boolean include) {
      List deviceGroupList = null;

      try {
         deviceGroupList = ((DeviceGroupDaoMapper)this.getMapper()).getEventMappingDeviceGroupAuth(programId, userId, include);
      } catch (SQLException var6) {
         logger.error("An unexpected error occurred.", var6);
      }

      return deviceGroupList;
   }

   public List getDeviceTypesMapGroup(Long groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getDeviceTypesMapGroup(groupId);
   }

   public boolean setGroupTypeDefault(Long groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).setGroupTypeDefault((String)null, groupId);
   }

   public int getCntDeviceInVwlConsoleDevice(String consoleId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getCntDeviceInVwlConsoleDevice(consoleId);
   }

   public List getAllGroupName() throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getAllGroupName();
   }

   public static int safeLongToInt(long l) {
      if (l >= -2147483648L && l <= 2147483647L) {
         return (int)l;
      } else {
         throw new IllegalArgumentException(l + " cannot be cast to int without changing its value.");
      }
   }

   public List getGroupById(String cmd, long id) throws SQLException {
      return cmd.equals("DEVICE") ? ((DeviceGroupDaoMapper)this.getMapper()).getGroupById(cmd, id) : null;
   }

   public List getGroupByIdWithPermission(String cmd, long id, String userId) throws SQLException {
      return cmd.equals("DEVICE") ? ((DeviceGroupDaoMapper)this.getMapper()).getGroupByIdWithPermission(cmd, id, userId) : null;
   }

   public List getGroupById(List groupList) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getGroupByUserGroup(groupList);
   }

   public List getAuthGroupById(String cmd, long id) throws SQLException {
      return cmd.equals("DEVICE") ? ((DeviceGroupDaoMapper)this.getMapper()).getAuthGroupById(cmd, id) : null;
   }

   public List getVwlGroupById(String cmd, long id) throws SQLException {
      return cmd.equals("DEVICE") ? ((DeviceGroupDaoMapper)this.getMapper()).getVwlGroupById(cmd, id) : null;
   }

   public List getAdminVwlRootGroupById(String cmd, long id) throws SQLException {
      return cmd.equals("DEVICE") ? ((DeviceGroupDaoMapper)this.getMapper()).getAdminVwlRootGroupById(cmd, id) : null;
   }

   public List getRootGroupById(String cmd, String organization) throws SQLException {
      return cmd.equals("DEVICE") ? ((DeviceGroupDaoMapper)this.getMapper()).getRootGroupById(cmd, organization) : null;
   }

   public List getVwlRootGroupById(String cmd, String organization) throws SQLException {
      return cmd.equals("DEVICE") ? ((DeviceGroupDaoMapper)this.getMapper()).getVwlRootGroupById(cmd, organization) : null;
   }

   public int getOrganizationCount(String table) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getOrganizationCount(table);
   }

   public List getOrganization(String table) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getOrganization(table);
   }

   public boolean setOrganizationByDeviceId(String table, String organization, String deviceId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).setOrganizationByDeviceId(table, organization, deviceId);
   }

   public List getDynamicChildGroupIdList(int group_id, String deviceType) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getDynamicChildGroupIdList(group_id, deviceType, 999999);
   }

   public boolean getDeviceAuthor(long groupId, String userId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getDeviceAuthor(groupId, userId);
   }

   public boolean isSyncPlayGroup(int devGroupId) throws SQLException {
      int cnt = ((DeviceGroupDaoMapper)this.getMapper()).checkIsSyncGroup(devGroupId);
      return cnt > 0;
   }

   public void checkDeviceTotalCount(int depth) throws SQLException {
      int count = ((DeviceGroupDaoMapper)this.getMapper()).groupDepthCount(depth);
      int pageSize = 1000;
      int startPos = 0;
      if (count > 0) {
         int deviceCount = count / pageSize;

         for(int i = 0; i <= deviceCount; ++i) {
            if (i > 0) {
               startPos = pageSize * i + 1;
            }

            List deviceGroups = ((DeviceGroupDaoMapper)this.getMapper()).getDeviceTotalCountLists(depth, startPos, pageSize);

            for(int j = 0; j < deviceGroups.size(); ++j) {
               int childrenGroupCount = ((DeviceGroupDaoMapper)this.getMapper()).getChildrenGroupCount(((DeviceGroup)deviceGroups.get(j)).getP_group_id());
               int deviceTotalCount = false;
               int deviceTotalCount;
               if (childrenGroupCount > 0) {
                  deviceTotalCount = ((DeviceGroupDaoMapper)this.getMapper()).getCntDeviceInDeviceGroup(safeLongToInt(((DeviceGroup)deviceGroups.get(j)).getGroup_id()));
                  Map childrenCount = ((DeviceGroupDaoMapper)this.getMapper()).getChildrenSum(((DeviceGroup)deviceGroups.get(j)).getGroup_id());
                  if (childrenCount != null && childrenCount.get("sum") != null) {
                     deviceTotalCount = (int)((long)deviceTotalCount + (Long)childrenCount.get("sum"));
                  }
               } else {
                  deviceTotalCount = ((DeviceGroupDaoMapper)this.getMapper()).getCntDeviceInDeviceGroup(safeLongToInt(((DeviceGroup)deviceGroups.get(j)).getGroup_id()));
               }

               if (safeLongToInt(((DeviceGroup)deviceGroups.get(j)).getTotal_count()) != deviceTotalCount) {
                  ((DeviceGroupDaoMapper)this.getMapper()).updateDeviceTotalCount((long)deviceTotalCount, ((DeviceGroup)deviceGroups.get(j)).getGroup_id());
               }
            }
         }
      }

   }

   public int checkChildPermissions2(String userId, Long groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).checkChildPermissions2(userId, groupId);
   }

   public List getDeviceModelName(long groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getDeviceModelName(groupId);
   }

   public int getCntDeviceInDeviceGroupExceptFor(int groupId, List deviceTypeList) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getCntDeviceInDeviceGroupExceptFor(groupId, deviceTypeList);
   }

   public String getModelCountInfo(long groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getModelCountInfo(groupId);
   }

   public long getDiskSpaceRepository(String groupIds) throws SQLException {
      List groupLids = new ArrayList();
      if (groupIds != null && !groupIds.equals("")) {
         String[] groupId = groupIds.split(",");
         String[] var4 = groupId;
         int var5 = groupId.length;

         for(int var6 = 0; var6 < var5; ++var6) {
            String group = var4[var6];
            groupLids.add(Long.valueOf(group));
         }

         return ((DeviceGroupDaoMapper)this.getMapper()).getDiskSpaceRepository(groupLids);
      } else {
         return 0L;
      }
   }

   public List getAllDeviceGroups() throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getAllDGroups();
   }

   public List getAllDeviceGroups(long groupId, String groupName) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getAllDeviceGroups(groupId, groupName);
   }

   public List getAllDeviceGroups(List groupIds, String groupName) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getAllDeviceGroupsWithGroupIds(groupIds, groupName);
   }

   public List getAllAuthorityDeviceGroups(String userId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getAllAuthorityDeviceGroups(userId);
   }

   public int getUnapprovedDeviceCountByUser(List groupIds) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getUnapprovedDeviceCountByUser(groupIds);
   }

   public List getParentGroupNamePathByGroupId(Long groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getParentGroupNamePathByGroupId(groupId);
   }

   public String getParentOrgNameByGroupId(Long groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getParentOrgNameByGroupId(groupId);
   }

   public boolean changeDeviceOrgName(String name, String oldName) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).changeDeviceOrgName(name, oldName);
   }

   public int getCntAnalysisDeviceGroup() throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getCntAnalysisDeviceGroup();
   }

   public boolean setAnalysisDeviceGroup(long groupId, boolean value) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).setAnalysisDeviceGroup(groupId, value);
   }

   public String getDeviceTypeByGroupId(Long groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getDeviceTypeByGroupId(groupId);
   }

   public void addGroupTotalCount(Long groupId, Long count) {
      SqlSession sqlSession = this.openNewSession(false);

      try {
         if (((DeviceGroupDaoMapper)this.getMapper(sqlSession)).addTotalCountByGroupId(groupId, count)) {
            ((DeviceGroupDaoMapper)this.getMapper(sqlSession)).setTotalCountByGroupIdWithRecursive(groupId, "INCREMENT");
         }

         sqlSession.commit();
      } catch (Exception var8) {
         sqlSession.rollback();
      } finally {
         sqlSession.close();
      }

   }

   public boolean updateOrganizationByGroupId(long groupId, String organization) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).updateOrganizationByGroupId(groupId, organization);
   }

   public List getGroupDeviceListByOrganName(String organName) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getGroupDeviceListByOrganName(organName);
   }

   public DeviceGroup getDeviceOrgGroupByUserOrgId(long userOrgId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getDeviceOrgGroupByUserOrgId(userOrgId);
   }

   public List getAlarmDeviceGroup(long organizationId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getAlarmDeviceGroup(organizationId);
   }

   public List getAlarmDeviceGroupByName(String orgName) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getAlarmDeviceGroupByName(orgName);
   }

   public boolean addAlarmDeviceGroup(long organizationId, String orgName, String groupIds) throws SQLException {
      ((DeviceGroupDaoMapper)this.getMapper()).deleteAlarmDeviceGroup(organizationId);
      if (groupIds != null && !groupIds.equals("")) {
         String[] groupIdList = groupIds.split(",");
         String[] var6 = groupIdList;
         int var7 = groupIdList.length;

         for(int var8 = 0; var8 < var7; ++var8) {
            String s = var6[var8];
            ((DeviceGroupDaoMapper)this.getMapper()).insertAlarmDeviceGroup(organizationId, orgName, Long.parseLong(s));
         }
      }

      return true;
   }

   int insertAlarmDeviceGroup(Long organizationId, String orgName, Long groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).insertAlarmDeviceGroup(organizationId, orgName, groupId);
   }

   public Integer getCountByUserIdAndGroupId(String userId, Long groupId) throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getCountByUserIdAndGroupId(userId, groupId);
   }

   public List V2GetChildDeviceIdList(int groupId, boolean recursive) throws SQLException {
      List rtnList = new ArrayList();
      List deviceIdList = null;
      if (recursive) {
         deviceIdList = ((DeviceGroupDaoMapper)this.getMapper((SqlSession)null)).V2GetChildDeviceIdListRecursive(groupId);
      } else {
         deviceIdList = ((DeviceGroupDaoMapper)this.getMapper((SqlSession)null)).getChildDeviceIdList(groupId);
      }

      if (deviceIdList != null && deviceIdList.size() > 0) {
         for(int i = 0; i < deviceIdList.size(); ++i) {
            rtnList.add(((Map)deviceIdList.get(i)).get("device_id").toString());
         }
      }

      return rtnList;
   }

   public List getDeviceIdListByDeviceGroupPermission(String userId) throws SQLException {
      List deviceIdList = new ArrayList();
      List deviceMapList = ((DeviceGroupDaoMapper)this.getMapper()).getDeviceIdListByDeviceGroupPermission(userId);
      Iterator var4 = deviceMapList.iterator();

      while(var4.hasNext()) {
         Map map = (Map)var4.next();
         deviceIdList.add((String)map.get("DEVICE_ID"));
      }

      return deviceIdList;
   }

   public int getDeviceGroupTotalCount() throws SQLException {
      return ((DeviceGroupDaoMapper)this.getMapper()).getDeviceGroupTotalCount();
   }
}
