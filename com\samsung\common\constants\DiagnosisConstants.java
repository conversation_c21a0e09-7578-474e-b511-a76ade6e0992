package com.samsung.common.constants;

import java.io.File;

public class DiagnosisConstants {
   public static final String DIAG_STATUS_RUNNING_FAIL = "RUNNING FAIL";
   public static final String DIAG_STATUS_DB_UPDATE_FAIL = "DB UPDATE FAIL";
   public static final String DIAG_STATUS_DISCONNECT = "DISCONNECT";
   public static final String DIAG_STATUS_DIAGNOSING = "DIAGNOSING";
   public static final String DIAG_STATUS_RES_TIMEOUT = "TIMEOUT";
   public static final String DIAG_STATUS_RES_SUCCESS = "SUCCESS";
   public static final String DIAG_STATUS_RES_FAIL = "FAIL";
   public static final String DIAG_STATUS_RES_CANCEL = "CANCEL";
   public static final String DIAG_STATUS_RES_DUPLICATION = "ALREADY RUNNING";
   public static final String UPLOAD_STATUS_RUNNING_FAIL = "RUNNING FAIL";
   public static final String UPLOAD_STATUS_MISSING_FILE = "MISSING FILE";
   public static final String UPLOAD_STATUS_UPLOADING = "UPLOADING";
   public static final String UPLOAD_STATUS_RES_TIMEOUT = "TIMEOUT";
   public static final String UPLOAD_STATUS_RES_SUCCESS = "SUCCESS";
   public static final String UPLOAD_STATUS_RES_FAIL = "FAIL";
   public static final String UPLOAD_STATUS_RES_CANCEL = "CANCEL";
   public static final String UPLOAD_STATUS_OVER_CAPACITY = "OVER CAPACITY";
   public static final int UPLOAD_FILE_LIMIT_COUNT = 50;
   public static final long UPLOAD_FILE_LIMIT_SIZE = 104857600L;
   public static final String DIR_RESULT = "result";
   public static final String DIR_DIAG_LFD = "diagnosis_lfd";
   public static final String DIR_DIAG_SERVER = "diagnosis_server";
   public static final String DIR_DIAG_VWL = "diagnosis_vwl";
   public static final String DIR_DIAG_SOC = "diagnosis_soc";
   public static final String DIR_RESULT_DIAG_LFD;
   public static final String DIR_RESULT_DIAG_SERVER;
   public static final String DIR_RESULT_DIAG_VWL;
   public static final String SERVLET_TYPE_DIAG_TOOL = "DIAG_TOOL";
   public static final String SERVLET_TYPE_UPLOADER = "UPLOADER";
   public static final String SERVLET_TYPE_VWL = "VWL";
   public static final String SERVLET_STATUS_START = "START";
   public static final String SERVLET_STATUS_SUCCESS = "SUCCESS";
   public static final String SERVLET_STATUS_FAIL = "FAIL";
   public static final String SERVLET_STATUS_CANCEL = "CANCEL";
   public static final String SERVLET_STATUS_KEEPALIVE = "KEEPALIVE";
   public static final Long SERVER_DEFAULT_JOB_ID;
   public static final String DIAGNOSIS_TOOL_EXE = "MIDiagTool.exe";

   public DiagnosisConstants() {
      super();
   }

   static {
      DIR_RESULT_DIAG_LFD = "result" + File.separator + "diagnosis_lfd";
      DIR_RESULT_DIAG_SERVER = "result" + File.separator + "diagnosis_server";
      DIR_RESULT_DIAG_VWL = "result" + File.separator + "diagnosis_vwl";
      SERVER_DEFAULT_JOB_ID = 0L;
   }
}
