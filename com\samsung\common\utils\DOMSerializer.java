package com.samsung.common.utils;

import com.samsung.common.logger.LoggingManagerV2;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.io.Writer;
import java.util.Vector;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import org.apache.logging.log4j.Logger;
import org.w3c.dom.Attr;
import org.w3c.dom.Comment;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.ProcessingInstruction;
import org.xml.sax.SAXException;

public class DOMSerializer {
   static Logger logger = LoggingManagerV2.getLogger(DOMSerializer.class);
   private static Writer writer = null;
   private static String indentUnit = null;
   private static final int NODE_BEFORE_DOCUMENT_ELEMENT = -1;
   private static final int NODE_NOT_BEFORE_OR_AFTER_DOCUMENT_ELEMENT = 0;
   private static final int NODE_AFTER_DOCUMENT_ELEMENT = 1;

   public DOMSerializer() {
      super();
   }

   public static byte[] serialize(Node node, String strEncoding, boolean bOmitHeader) {
      try {
         ByteArrayOutputStream baos = new ByteArrayOutputStream();
         TransformerFactory tfactory = TransformerFactory.newInstance();
         Transformer serializer = tfactory.newTransformer();
         serializer.setOutputProperty("indent", "yes");
         serializer.setOutputProperty("method", "xml");
         serializer.setOutputProperty("encoding", strEncoding);
         serializer.setOutputProperty("omit-xml-declaration", bOmitHeader ? "yes" : "no");
         serializer.transform(new DOMSource(node), new StreamResult(new OutputStreamWriter(baos, strEncoding)));
         return baos.toByteArray();
      } catch (TransformerConfigurationException var6) {
         logger.error("", var6);
         return new byte[0];
      } catch (TransformerException var7) {
         logger.error("", var7);
         return new byte[0];
      } catch (UnsupportedEncodingException var8) {
         logger.error("", var8);
         return new byte[0];
      }
   }

   public static byte[] serialize(Document doc, String charName, String indent) {
      indentUnit = indent;

      try {
         ByteArrayOutputStream baos = new ByteArrayOutputStream();
         if (charName.equals("")) {
            writer = new OutputStreamWriter(baos);
         } else {
            writer = new OutputStreamWriter(baos, charName);
         }

         serializeNode(doc, "");
         writer.close();
         return baos.toByteArray();
      } catch (IOException var4) {
         return null;
      }
   }

   public static byte[] serialize(Node node, String charName, String indent, boolean bInherit) {
      indentUnit = indent;

      try {
         ByteArrayOutputStream baos = new ByteArrayOutputStream();
         if (charName != null && !charName.equals("")) {
            writer = new OutputStreamWriter(baos, charName);
         } else {
            writer = new OutputStreamWriter(baos);
         }

         if (bInherit) {
            Vector attrs = new Vector();
            NamedNodeMap nodeAttrs = node.getAttributes();
            if (nodeAttrs != null) {
               for(int i = 0; i < nodeAttrs.getLength(); ++i) {
                  Attr attr = (Attr)nodeAttrs.item(i);
                  if (attr.getName().startsWith("xmlns")) {
                     attrs.add(attr.cloneNode(true));
                  }
               }
            }

            int i;
            for(Node parent = node.getParentNode(); parent != node.getOwnerDocument(); parent = parent.getParentNode()) {
               NamedNodeMap attributes = parent.getAttributes();
               i = attributes.getLength();

               for(int i = 0; i < i; ++i) {
                  Attr attr = (Attr)attributes.item(i);
                  if (attr.getName().startsWith("xmlns")) {
                     attrs.add(attr.cloneNode(true));
                  }
               }
            }

            Node cloneNode = node.cloneNode(true);

            for(i = 0; i < attrs.size(); ++i) {
               ((Element)cloneNode).setAttributeNode((Attr)attrs.elementAt(i));
            }

            serializeNode(cloneNode, indent);
         } else {
            serializeNode(node, indent);
         }

         writer.close();
         return baos != null ? baos.toByteArray() : null;
      } catch (IOException var12) {
         return null;
      }
   }

   public static void serializeNode(Node node, String indent) throws IOException {
      int currentNodeType = node.getNodeType();
      int position = false;
      int position;
      switch(currentNodeType) {
      case 1:
         Element currentElement = (Element)node;
         if (currentElement.getOwnerDocument() == currentElement.getParentNode()) {
            writer.write("<");
         } else {
            writer.write("\n" + indent + "<");
         }

         writer.write(currentElement.getTagName());
         NamedNodeMap attributes = node.getAttributes();

         for(int i = 0; i < attributes.getLength(); ++i) {
            Attr attr = (Attr)attributes.item(i);
            writer.write(" " + attr.getName() + "=\"" + attr.getValue() + "\"");
         }

         writer.write(">");

         for(Node currentChild = node.getFirstChild(); currentChild != null; currentChild = currentChild.getNextSibling()) {
            String childIndent = indent + indentUnit;
            serializeNode(currentChild, childIndent);
         }

         if (currentElement.getFirstChild() == null) {
            writer.write("</");
         } else if (currentElement.getFirstChild().getNodeType() == 3) {
            writer.write("</");
         } else {
            writer.write("\n" + indent + "</");
         }

         writer.write(currentElement.getTagName());
         writer.write(">");
      case 2:
      case 5:
      case 6:
      case 10:
      case 11:
      case 12:
      default:
         break;
      case 3:
      case 4:
         if (node.getNodeValue() != null) {
            writer.write(node.getNodeValue());
         }
         break;
      case 7:
         position = getPositionRelativeToDocumentElement(node);
         if (position == 1) {
            writer.write("\n");
         }

         writer.write(indent + "<?" + ((ProcessingInstruction)node).getTarget() + " " + ((ProcessingInstruction)node).getData() + "?>");
         if (position == -1) {
            writer.write("\n");
         }
         break;
      case 8:
         position = getPositionRelativeToDocumentElement(node);
         if (position == 1) {
            writer.write("\n");
         }

         writer.write(indent + "<!--" + ((Comment)node).getData() + "-->");
         if (position == -1) {
            writer.write("\n");
         }
         break;
      case 9:
         for(Node child = node.getFirstChild(); child != null; child = child.getNextSibling()) {
            serializeNode(child, indent);
         }
      }

   }

   public static byte[] applyIndent(Node node, String indent, String charName) {
      indentUnit = indent;

      try {
         ByteArrayOutputStream baos = new ByteArrayOutputStream();
         writer = new OutputStreamWriter(baos, charName);
         indentNode(node, indent);
         writer.close();
         return baos != null ? baos.toByteArray() : null;
      } catch (IOException var4) {
         return null;
      }
   }

   public static byte[] applyIndent(byte[] data, String indent, String charName) {
      indentUnit = indent;

      try {
         DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
         DocumentBuilder builder = factory.newDocumentBuilder();
         ByteArrayInputStream bis = new ByteArrayInputStream(data);
         Document doc = builder.parse(bis);
         ByteArrayOutputStream baos = new ByteArrayOutputStream();
         writer = new OutputStreamWriter(baos, charName);
         indentNode(doc.getFirstChild(), "");
         writer.close();
         return baos != null ? baos.toByteArray() : null;
      } catch (ParserConfigurationException var8) {
         logger.error("", var8);
         return null;
      } catch (SAXException var9) {
         logger.error("", var9);
         return null;
      } catch (IOException var10) {
         logger.error("", var10);
         return null;
      }
   }

   public static void indentNode(Node node, String indent) throws IOException {
      int currentNodeType = node.getNodeType();
      int position = false;
      switch(currentNodeType) {
      case 1:
         Element currentElement = (Element)node;
         writer.write(indent + "<");
         writer.write(currentElement.getTagName());
         NamedNodeMap attributes = node.getAttributes();

         for(int i = 0; i < attributes.getLength(); ++i) {
            Attr attr = (Attr)attributes.item(i);
            writer.write(" " + attr.getName() + "=\"" + attr.getValue() + "\"");
         }

         writer.write(">");

         for(Node currentChild = node.getFirstChild(); currentChild != null; currentChild = currentChild.getNextSibling()) {
            String childIndent = indent + indentUnit;
            indentNode(currentChild, childIndent);
         }

         if (currentElement.getFirstChild() == null) {
            writer.write("</");
         } else if (currentElement.getFirstChild().getNextSibling() == null) {
            writer.write("</");
         } else {
            writer.write(indent + "</");
         }

         writer.write(currentElement.getTagName());
         writer.write(">");
      case 2:
      case 5:
      case 6:
      case 10:
      case 11:
      case 12:
      default:
         break;
      case 3:
      case 4:
         writer.write(node.getNodeValue());
         break;
      case 7:
         writer.write(indent + "<?" + ((ProcessingInstruction)node).getTarget() + " " + ((ProcessingInstruction)node).getData() + "?>");
         break;
      case 8:
         int position = getPositionRelativeToDocumentElement(node);
         writer.write(indent + "<!--" + ((Comment)node).getData() + "-->");
         break;
      case 9:
         for(Node child = node.getFirstChild(); child != null; child = child.getNextSibling()) {
            indentNode(child, indent);
         }
      }

   }

   private static int getPositionRelativeToDocumentElement(Node currentNode) {
      if (currentNode == null) {
         return 0;
      } else {
         Document doc = currentNode.getOwnerDocument();
         if (currentNode.getParentNode() != doc) {
            return 0;
         } else {
            Element documentElement = doc.getDocumentElement();
            if (documentElement == null) {
               return 0;
            } else if (documentElement == currentNode) {
               return 0;
            } else {
               for(Node x = currentNode; x != null; x = x.getNextSibling()) {
                  if (x == documentElement) {
                     return -1;
                  }
               }

               return 1;
            }
         }
      }
   }
}
