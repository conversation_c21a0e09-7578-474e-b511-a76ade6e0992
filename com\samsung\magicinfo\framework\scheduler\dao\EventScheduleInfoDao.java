package com.samsung.magicinfo.framework.scheduler.dao;

import com.samsung.common.db.PagedListInfo;
import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.DaoTools;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.SequenceDB;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.scheduler.entity.EventScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.EventScheduleEventEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramGroup;
import com.samsung.magicinfo.framework.scheduler.entity.ScheduleSearch;
import com.samsung.magicinfo.framework.scheduler.entity.SelectConditionScheduleAdmin;
import com.samsung.magicinfo.framework.scheduler.manager.EventScheduleGroupInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventScheduleGroupInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.EventScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.EventScheduleInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.SchSearchInfo;
import com.samsung.magicinfo.framework.scheduler.manager.SchSearchInfoImpl;
import com.samsung.magicinfo.framework.user.entity.UserGroup;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class EventScheduleInfoDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(EventScheduleInfoDao.class);

   public EventScheduleInfoDao() {
      super();
   }

   public EventScheduleInfoDao(SqlSession session) {
      super(session);
   }

   public boolean checkProgGroupChange(String eventScheduleIds, String strNewGroup) throws SQLException {
      String newRootGroup = this.getProgramGroupRoot(Integer.valueOf(strNewGroup));
      if (eventScheduleIds != null && !"".equals(eventScheduleIds)) {
         String[] ids = eventScheduleIds.split(":");
         int len = ids.length;

         for(int i = 0; i < len; ++i) {
            String strProg = ids[i];
            int iOldGroup = this.getProgramGroupById(strProg).intValue();
            String oldRootGroup = this.getProgramGroupRoot(iOldGroup);
            if (!oldRootGroup.equalsIgnoreCase(newRootGroup)) {
               return false;
            }
         }
      }

      return true;
   }

   public boolean recoverSchedule(String eventScheduleId, String progGrpId) throws SQLException {
      SqlSession sqlSession = this.openNewSession(false);
      EventScheduleInfoDaoMapper infoDaoMapper = (EventScheduleInfoDaoMapper)this.getMapper(sqlSession);

      boolean var6;
      try {
         int cnt = infoDaoMapper.updateEventScheduleSetNoDel(eventScheduleId);
         if (cnt <= 0) {
            sqlSession.rollback();
            var6 = false;
            return var6;
         }

         infoDaoMapper.deleteDevicesForSchedule(eventScheduleId);
         cnt = infoDaoMapper.updateEventMapScheduleGroupSetGroup(eventScheduleId, Long.parseLong(progGrpId));
         if (cnt > 0) {
            sqlSession.commit();
            return true;
         }

         sqlSession.rollback();
         var6 = false;
      } catch (SQLException var10) {
         this.logger.error(String.format("Exception when recover event schedule with id:%s", eventScheduleId), var10);
         sqlSession.rollback();
         var6 = false;
         return var6;
      } finally {
         sqlSession.close();
      }

      return var6;
   }

   public boolean deleteSchedule(String eventScheduleId, String userId) throws SQLException {
      int cnt = false;
      if (eventScheduleId != null && !eventScheduleId.equals("")) {
         SqlSession sqlSession = this.openNewSession(false);
         EventScheduleInfoDaoMapper infoDaoMapper = (EventScheduleInfoDaoMapper)this.getMapper(sqlSession);

         boolean var7;
         try {
            int cnt = infoDaoMapper.deleteSchedule(eventScheduleId);
            this.unmappingDeviceGroupAndDeployDefaultSchedule(eventScheduleId);
            if (cnt > 0) {
               long logId = (long)SequenceDB.getNextValue("MI_EVENT_LOG_SCHEDULE");
               EventScheduleLog log = new EventScheduleLog();
               log.setLogId(logId);
               log.setScheduleId(eventScheduleId);
               log.setEventType("02");
               log.setUserId(userId);
               infoDaoMapper.addEventScheduleLog(log);
               sqlSession.commit();
               boolean var9 = true;
               return var9;
            }

            sqlSession.rollback();
            boolean var6 = false;
            return var6;
         } catch (SQLException var13) {
            this.logger.error(String.format("Exception when try to delete schedule with id:%s", eventScheduleId), var13);
            sqlSession.rollback();
            var7 = false;
         } finally {
            sqlSession.close();
         }

         return var7;
      } else {
         return false;
      }
   }

   public boolean deleteSchedulePerm(String eventScheduleId, String userId) throws SQLException {
      int cnt = false;
      if (eventScheduleId != null && !eventScheduleId.isEmpty()) {
         SqlSession sqlSession = this.openNewSession(false);
         EventScheduleInfoDaoMapper infoDaoMapper = (EventScheduleInfoDaoMapper)this.getMapper(sqlSession);

         boolean var7;
         try {
            int cnt = infoDaoMapper.deleteEventInfoSchedule(eventScheduleId);
            cnt += infoDaoMapper.deleteEventScheduleEvent(eventScheduleId);
            infoDaoMapper.deleteScheduleGroupMap(eventScheduleId);
            this.unmappingDeviceGroupAndDeployDefaultSchedule(eventScheduleId);
            if (cnt <= 0) {
               sqlSession.rollback();
               boolean var16 = false;
               return var16;
            }

            long logId = (long)SequenceDB.getNextValue("MI_EVENT_LOG_SCHEDULE");
            EventScheduleLog log = new EventScheduleLog();
            log.setLogId(logId);
            log.setScheduleId(eventScheduleId);
            log.setEventType("04");
            log.setUserId(userId);
            infoDaoMapper.addEventScheduleLog(log);
            sqlSession.commit();
            boolean var9 = true;
            return var9;
         } catch (SQLException var13) {
            this.logger.error(String.format("Exception when delete event schedule perm with event id:%s", eventScheduleId), var13);
            sqlSession.rollback();
            var7 = false;
         } finally {
            sqlSession.close();
         }

         return var7;
      } else {
         return false;
      }
   }

   public boolean deleteAllSchedule(String groupId, String userOrg, String userId) throws SQLException {
      SqlSession sqlSession = this.openNewSession(false);
      EventScheduleInfoDaoMapper infoDaoMapper = (EventScheduleInfoDaoMapper)this.getMapper(sqlSession);
      boolean var6 = false;

      try {
         List programIdList = this.getAllProgramIdsForGroup(groupId, userOrg);
         int len = programIdList.size();

         for(int i = 0; i < len; ++i) {
            Map map = (Map)programIdList.get(i);
            String eventScheduleId = (String)map.get("SCHEDULE_ID");
            if (!eventScheduleId.equals("")) {
               EventScheduleEntity scheduleEntity = infoDaoMapper.getEventSchedule(eventScheduleId);
               String strStatus = scheduleEntity.getDeleted();
               int cnt;
               if ("N".equalsIgnoreCase(strStatus)) {
                  infoDaoMapper.deleteSchedule(eventScheduleId);
                  this.unmappingDeviceGroupAndDeployDefaultSchedule(eventScheduleId);
                  Long groupId1 = infoDaoMapper.getProgramGroupById(eventScheduleId);
                  if (groupId1 != null) {
                     ProgramGroupDao groupDao = new ProgramGroupDao();
                     int orgGroupId = groupDao.getProgramOrgGroupId(groupId1.intValue());
                     infoDaoMapper.updateEventMapScheduleGroupSetGroup(eventScheduleId, (long)orgGroupId);
                  }

                  EventScheduleLog log = new EventScheduleLog();
                  long logId = (long)SequenceDB.getNextValue("MI_EVENT_LOG_SCHEDULE");
                  log.setLogId(logId);
                  log.setScheduleId(eventScheduleId);
                  log.setEventType("04");
                  log.setUserId(userId);
                  cnt = infoDaoMapper.addEventScheduleLog(log);
                  if (cnt <= 0) {
                     sqlSession.rollback();
                     boolean var18 = false;
                     return var18;
                  }

                  sqlSession.commit();
               } else {
                  infoDaoMapper.deleteScheduleGroupMap(eventScheduleId);
                  this.unmappingDeviceGroupAndDeployDefaultSchedule(eventScheduleId);
                  cnt = infoDaoMapper.deleteEventInfoSchedule(eventScheduleId);
                  if (cnt <= 0) {
                     sqlSession.rollback();
                     boolean var27 = false;
                     return var27;
                  }

                  EventScheduleLog log = new EventScheduleLog();
                  long logId = (long)SequenceDB.getNextValue("MI_EVENT_LOG_SCHEDULE");
                  log.setLogId(logId);
                  log.setScheduleId(eventScheduleId);
                  log.setEventType("04");
                  log.setUserId(userId);
                  cnt = infoDaoMapper.addEventScheduleLog(log);
                  if (cnt <= 0) {
                     sqlSession.rollback();
                     boolean var17 = false;
                     return var17;
                  }

                  sqlSession.commit();
                  CommonUtils.deleteScheduleJob(eventScheduleId);
               }
            }
         }

         sqlSession.commit();
         return true;
      } catch (SQLException var22) {
         sqlSession.rollback();
         this.logger.error(String.format("Exception when delete all schedules"), var22);
         boolean var8 = false;
         return var8;
      } finally {
         sqlSession.close();
      }
   }

   public boolean deleteAllSchedulePerm(String groupId, String userOrg, String userId) throws SQLException {
      int cnt = false;
      SqlSession sqlSession = this.openNewSession(false);
      EventScheduleInfoDaoMapper mapper = (EventScheduleInfoDaoMapper)this.getMapper(sqlSession);

      boolean var8;
      try {
         List programIdList = this.getAllProgramIdsForGroup(groupId, userOrg);
         int len = programIdList.size();

         for(int i = 0; i < len; ++i) {
            Map map = (Map)programIdList.get(i);
            String eventScheduleId = (String)map.get("SCHEDULE_ID");
            if (!eventScheduleId.equals("")) {
               EventScheduleEntity scheduleEntity = mapper.getEventSchedule(eventScheduleId);
               String strStatus = scheduleEntity.getDeleted();
               if ("N".equalsIgnoreCase(strStatus)) {
                  List groups = mapper.getDeviceGroupInfo(eventScheduleId);
                  if (groups != null) {
                     int length = groups.size();

                     for(int index = 0; index < length; ++index) {
                        Long devGroup = (Long)((Map)groups.get(index)).get("DEVICE_GROUP_ID");
                        this.unmappingDeviceGroupAndDeployDefaultSchedule(devGroup, true);
                     }
                  }
               }

               mapper.deleteScheduleGroupMap(eventScheduleId);
               this.unmappingDeviceGroupAndDeployDefaultSchedule(eventScheduleId);
               int cnt = mapper.deleteEventInfoSchedule(eventScheduleId);
               if (cnt <= 0) {
                  sqlSession.rollback();
                  boolean var26 = false;
                  return var26;
               }

               long logId = (long)SequenceDB.getNextValue("MI_EVENT_LOG_SCHEDULE");
               EventScheduleLog log = new EventScheduleLog();
               log.setLogId(logId);
               log.setScheduleId(eventScheduleId);
               log.setEventType("04");
               log.setUserId(userId);
               mapper.addEventScheduleLog(log);
               if (cnt <= 0) {
                  sqlSession.rollback();
                  boolean var28 = false;
                  return var28;
               }

               sqlSession.commit();
               CommonUtils.deleteScheduleJob(eventScheduleId);
            }
         }

         sqlSession.commit();
         return true;
      } catch (SQLException var21) {
         this.logger.error(String.format("Exception when delete all schedules perm"), var21);
         sqlSession.rollback();
         var8 = false;
      } finally {
         sqlSession.close();
      }

      return var8;
   }

   private List getAllProgramIdsForGroup(String groupId, String rootGrpOrg) throws SQLException {
      groupId = groupId.toLowerCase();
      int group_id = -1;
      List childGroups = new ArrayList();
      if (groupId != null && !"".equals(groupId) && ("trash".equals(groupId) || "all".equals(groupId))) {
         group_id = this.getProgramGroupForUser(rootGrpOrg);
         if (group_id != -1) {
            childGroups = this.getChildGroupIdList(group_id, true);
         }
      }

      List list = ((EventScheduleInfoDaoMapper)this.getMapper()).getAllProgramIdsForGroup(groupId, group_id, (List)childGroups);
      return list;
   }

   public boolean updateProgramGroup(String strGroupId, String eventScheduleIds, String userId) throws SQLException {
      int cnt = false;
      if (eventScheduleIds != null && !"".equals(eventScheduleIds)) {
         SqlSession session = this.openNewSession(false);
         EventScheduleInfoDaoMapper mapper = (EventScheduleInfoDaoMapper)this.getMapper(session);

         boolean var8;
         try {
            String[] ids = eventScheduleIds.split(":");
            int len = ids.length;

            for(int i = 0; i < len; ++i) {
               String eventScheduleId = ids[i];
               if (!eventScheduleId.equals("")) {
                  boolean compatible = this.checkProgGroupChange(eventScheduleId, strGroupId);
                  int cnt = mapper.updateProgramGroup(new Long(strGroupId), eventScheduleId);
                  if (cnt <= 0) {
                     session.rollback();
                     boolean var24 = false;
                     return var24;
                  }

                  if (!compatible) {
                     this.unmappingDeviceGroupAndDeployDefaultSchedule(eventScheduleId);
                     long logId = (long)SequenceDB.getNextValue("MI_EVENT_LOG_SCHEDULE");
                     EventScheduleLog log = new EventScheduleLog();
                     log.setLogId(logId);
                     log.setScheduleId(eventScheduleId);
                     log.setEventType("02");
                     log.setUserId(userId);
                     mapper.addEventScheduleLog(log);
                     session.commit();
                  }
               }

               long logId = (long)SequenceDB.getNextValue("MI_EVENT_LOG_SCHEDULE");
               EventScheduleLog log = new EventScheduleLog();
               log.setLogId(logId);
               log.setScheduleId(eventScheduleId);
               log.setEventType("02");
               log.setUserId(userId);
               mapper.addEventScheduleLog(log);
            }

            session.commit();
            boolean var22 = true;
            return var22;
         } catch (SQLException var18) {
            this.logger.error("", var18);
            session.rollback();
            var8 = false;
         } finally {
            session.close();
         }

         return var8;
      } else {
         return false;
      }
   }

   public boolean updateDeviceGroup(String strGroupId, String eventScheduleId, String userId, String ipAddress) throws SQLException {
      int cnt = false;
      String[] newDevGrpIds = strGroupId.split(",");
      boolean devMapping = this.checkDeviceMapping(eventScheduleId, strGroupId);
      if (eventScheduleId != null && !"".equals(eventScheduleId)) {
         SqlSession session = this.openNewSession(false);
         EventScheduleInfoDaoMapper mapper = (EventScheduleInfoDaoMapper)this.getMapper(session);

         boolean var30;
         try {
            List originalDevGrpIds = mapper.getDeviceGroupInfo(eventScheduleId);
            int i;
            boolean isUnmappedDevGrpId;
            int len;
            if (originalDevGrpIds != null) {
               len = originalDevGrpIds.size();

               for(i = 0; i < len; ++i) {
                  Long targetDevGrpId = (Long)((Map)originalDevGrpIds.get(i)).get("DEVICE_GROUP_ID");
                  isUnmappedDevGrpId = true;
                  String[] var15 = newDevGrpIds;
                  int var16 = newDevGrpIds.length;

                  for(int var17 = 0; var17 < var16; ++var17) {
                     String newDevGrpId = var15[var17];
                     if (newDevGrpId.equalsIgnoreCase(targetDevGrpId + "")) {
                        isUnmappedDevGrpId = false;
                     }
                  }

                  if (isUnmappedDevGrpId) {
                     this.unmappingDeviceGroupAndDeployDefaultSchedule(targetDevGrpId, true, session);
                  }
               }
            }

            len = newDevGrpIds.length;

            for(i = 0; i < len; ++i) {
               String newDevGrpId = newDevGrpIds[i];
               if (!newDevGrpId.equals("")) {
                  this.unmappingDeviceGroupAndDeployDefaultSchedule(Long.parseLong(newDevGrpId), false, session);
                  int cnt = mapper.insertEventMapScheduleDevice(eventScheduleId, new Long(newDevGrpId));
                  if (cnt <= 0) {
                     session.rollback();
                     isUnmappedDevGrpId = false;
                     return isUnmappedDevGrpId;
                  }
               }
            }

            if (!devMapping) {
               session.rollback();
               boolean var28 = false;
               return var28;
            }

            long logId = (long)SequenceDB.getNextValue("MI_EVENT_LOG_SCHEDULE");
            EventScheduleLog log = new EventScheduleLog();
            log.setLogId(logId);
            log.setScheduleId(eventScheduleId);
            log.setEventType("01");
            log.setUserId(userId);
            log.setIp_address(ipAddress);
            ((EventScheduleInfoDaoMapper)this.getMapper(session)).addEventScheduleLog(log);
            session.commit();
            var30 = true;
         } catch (SQLException var22) {
            this.logger.error("", var22);
            session.rollback();
            boolean var11 = false;
            return var11;
         } finally {
            session.close();
         }

         return var30;
      } else {
         return false;
      }
   }

   private boolean checkDeviceMapping(String program_id, String device_group_ids) throws SQLException {
      if (device_group_ids == null) {
         return true;
      } else {
         String[] dGroupIdsArray = device_group_ids.split(",");
         EventScheduleInfoDao dao = new EventScheduleInfoDao();
         List prev_dev_map_list = dao.getDeviceProgramMapList(program_id);
         if (prev_dev_map_list != null && prev_dev_map_list.size() > 0) {
            for(int i = 0; i < prev_dev_map_list.size(); ++i) {
               Map dev_map = (Map)prev_dev_map_list.get(i);
               boolean match = false;
               String prev_dev_id = "" + dev_map.get("device_group_id");

               for(int count = 0; count < dGroupIdsArray.length; ++count) {
                  if (dGroupIdsArray[count].equals(prev_dev_id)) {
                     match = true;
                  }
               }

               if (!match) {
                  this.unmappingDeviceGroupAndDeployDefaultSchedule((Long)dev_map.get("device_group_id"), true);
               }
            }

            return true;
         } else {
            return true;
         }
      }
   }

   private void unmappingDeviceGroupAndDeployDefaultSchedule(String event_schedule_id) throws SQLException {
      this.unmappingDeviceGroupAndDeployDefaultSchedule(event_schedule_id, (SqlSession)null);
   }

   private void unmappingDeviceGroupAndDeployDefaultSchedule(String event_schedule_id, SqlSession session) throws SQLException {
      List unmappedGroupIdList = new ArrayList();
      List groupIdList = null;
      groupIdList = ((EventScheduleInfoDaoMapper)this.getMapper(session)).getDeviceGroupsMappingByScheduleId(event_schedule_id);
      Long groupId;
      if (groupIdList != null) {
         for(int i = 0; i < groupIdList.size(); ++i) {
            groupId = (Long)((Map)groupIdList.get(i)).get("device_group_id");
            unmappedGroupIdList.add(groupId);
         }
      }

      ((EventScheduleInfoDaoMapper)this.getMapper(session)).removeScheduleDeviceMappingsByScheduleId(event_schedule_id);
      Iterator var12 = unmappedGroupIdList.iterator();

      while(var12.hasNext()) {
         groupId = (Long)var12.next();
         DeviceGroupInfo d = DeviceGroupInfoImpl.getInstance();
         List devList = d.getChildDeviceIdList(groupId.intValue());
         EventScheduleInfo esInfo = EventScheduleInfoImpl.getInstance();

         try {
            esInfo.deployDefaultEventSchedule(devList);
         } catch (Exception var11) {
            this.logger.error("", var11);
         }
      }

   }

   private void unmappingDeviceGroupAndDeployDefaultSchedule(Long device_group_id, boolean deploy, SqlSession session) throws SQLException {
      ((EventScheduleInfoDaoMapper)this.getMapper(session)).unmappingDeviceGroupAndDeployDefaultSchedule(device_group_id);
      DeviceGroupInfo d = DeviceGroupInfoImpl.getInstance();
      List devList = d.getChildDeviceIdList(device_group_id.intValue());
      EventScheduleInfo esInfo = EventScheduleInfoImpl.getInstance();

      try {
         if (deploy) {
            esInfo.deployDefaultEventSchedule(devList);
         }
      } catch (Exception var8) {
         this.logger.error("", var8);
      }

   }

   private void unmappingDeviceGroupAndDeployDefaultSchedule(Long device_group_id, boolean deploy) throws SQLException {
      this.unmappingDeviceGroupAndDeployDefaultSchedule(device_group_id, deploy, (SqlSession)null);
   }

   /** @deprecated */
   @Deprecated
   public List getDistinctFrames(SelectConditionScheduleAdmin condObj) throws SQLException {
      throw new SQLException("table MI_EVENT_INFO_FRAME does not exist");
   }

   public PagedListInfo getScheduleAdminInfo(int startPos, int pageSize, Map condition) throws SQLException {
      SelectConditionScheduleAdmin condObj = (SelectConditionScheduleAdmin)condition.get("condition");
      String sort = condObj.getSort_name();
      if (sort != null) {
         sort = sort.toUpperCase();
      }

      String dir = condObj.getOrder_dir();
      if (dir != null) {
         dir = dir.toUpperCase();
      }

      String strNameLike = condObj.getNameLike();
      if (strNameLike != null) {
         strNameLike = strNameLike.replaceAll("_", "↓_");
      }

      String strGroup = condObj.getGroupType();
      String device_type = condObj.getDevice_type();
      String selId = condObj.getSelId();
      if (selId != null) {
         selId = selId.toLowerCase();
      }

      boolean isSelect = CommonUtils.checkNull(condObj.getIsSelect());
      String strRootGroup = condObj.getUserRootGroup();
      int group_id = strRootGroup != null ? this.getProgramGroupForUser(strRootGroup) : -1;
      List childGroups = new ArrayList();
      if (!"all".equalsIgnoreCase(strGroup) && !"trash".equalsIgnoreCase(strGroup)) {
         EventScheduleGroupInfo esGroupInfo = EventScheduleGroupInfoImpl.getInstance();
         ProgramGroup programGroup = esGroupInfo.getGroup(Integer.parseInt(strGroup));
         if (programGroup.getP_group_id() == 0L || programGroup.getP_group_id() == -1L) {
            childGroups = this.getChildGroupIdList(programGroup.getGroup_id().intValue(), true);
         }
      } else {
         UserGroupInfoImpl userGroupInfo;
         List manageGroupList;
         Iterator var17;
         UserGroup userGroup;
         EventScheduleGroupInfo eventScheduleGroupInfo;
         int orgId;
         if (strGroup.equalsIgnoreCase("ALL")) {
            if (group_id != -1) {
               childGroups = this.getChildGroupIdList(group_id, true);
            } else {
               userGroupInfo = UserGroupInfoImpl.getInstance();
               manageGroupList = userGroupInfo.getUserManageGroupListByUserId(SecurityUtils.getLoginUserId());
               if (manageGroupList != null && manageGroupList.size() > 0) {
                  var17 = manageGroupList.iterator();

                  while(var17.hasNext()) {
                     userGroup = (UserGroup)var17.next();
                     eventScheduleGroupInfo = EventScheduleGroupInfoImpl.getInstance();
                     orgId = eventScheduleGroupInfo.getOrgGroupIdByOrgGroupName(userGroup.getGroup_name());
                     ((List)childGroups).addAll(this.getChildGroupIdList(orgId, true));
                  }
               }
            }
         } else if (strGroup.equalsIgnoreCase("TRASH")) {
            if (group_id != -1) {
               childGroups = this.getChildGroupIdList(group_id, true);
            } else {
               userGroupInfo = UserGroupInfoImpl.getInstance();
               manageGroupList = userGroupInfo.getUserManageGroupListByUserId(SecurityUtils.getLoginUserId());
               if (manageGroupList != null && manageGroupList.size() > 0) {
                  var17 = manageGroupList.iterator();

                  while(var17.hasNext()) {
                     userGroup = (UserGroup)var17.next();
                     eventScheduleGroupInfo = EventScheduleGroupInfoImpl.getInstance();
                     orgId = eventScheduleGroupInfo.getOrgGroupIdByOrgGroupName(userGroup.getGroup_name());
                     ((List)childGroups).addAll(this.getChildGroupIdList(orgId, true));
                  }
               }
            }
         }
      }

      String[] arrDeviceType = new String[0];
      if (device_type != null && !device_type.equals("")) {
         arrDeviceType = device_type.split(",");
      }

      Map queryParameterMap = new HashMap();
      queryParameterMap.put("strGroup", strGroup);
      queryParameterMap.put("group_id", group_id);
      queryParameterMap.put("childGroups", childGroups);
      queryParameterMap.put("isSelect", isSelect);
      queryParameterMap.put("selId", selId);
      queryParameterMap.put("arrDeviceType", arrDeviceType);
      queryParameterMap.put("strNameLike", strNameLike);
      queryParameterMap.put("sort", sort);
      queryParameterMap.put("dir", dir);
      queryParameterMap.put("startPos", DaoTools.offsetStartPost(startPos));
      queryParameterMap.put("pageSize", pageSize);
      int intCount;
      if (condObj.getSelect_devgroup_ids() != null && condObj.getSelect_devgroup_ids() != "") {
         String[] devGroupIds_array = condObj.getSelect_devgroup_ids().split(",");
         if (devGroupIds_array != null && devGroupIds_array.length > 0) {
            List devGroupIdsArr = new ArrayList();

            for(intCount = 0; intCount < devGroupIds_array.length; ++intCount) {
               devGroupIdsArr.add(new Integer(devGroupIds_array[intCount]));
            }

            queryParameterMap.put("deviceGroupId", devGroupIdsArr);
         }
      }

      if (condObj.getStart_modified_date() != null && condObj.getStart_modified_date() != "") {
         queryParameterMap.put("startModifyDate", condObj.getStart_modified_date() + " 00:00:00");
      }

      if (condObj.getEnd_modified_date() != null && condObj.getEnd_modified_date() != "") {
         queryParameterMap.put("endModifyDate", condObj.getEnd_modified_date() + " 23:59:59");
      }

      List scheduleAdminInfo = ((EventScheduleInfoDaoMapper)this.getMapper()).getScheduleAdminInfo(queryParameterMap);
      Long scheduleAdminInfoCount = ((EventScheduleInfoDaoMapper)this.getMapper()).getScheduleAdminInfoCount(queryParameterMap);
      intCount = scheduleAdminInfoCount.intValue();
      if (!"trash".equalsIgnoreCase(strGroup)) {
         Iterator pgIt = scheduleAdminInfo.iterator();

         while(pgIt.hasNext()) {
            EventScheduleEntity tmp = (EventScheduleEntity)pgIt.next();
            StringBuffer groupStr = new StringBuffer();
            StringBuffer groupIdStr = new StringBuffer();
            String progId = tmp.getSchedule_id();
            List groups = ((EventScheduleInfoDaoMapper)this.getMapper()).getScheduleAdminInfoGroups(progId);
            Iterator groupIt = groups.iterator();
            Map data;
            if (groupIt.hasNext()) {
               data = (Map)groupIt.next();
               groupStr.append(data.get("GROUP_NAME"));
               groupIdStr.append(data.get("GROUP_ID"));
            }

            while(groupIt.hasNext()) {
               data = (Map)groupIt.next();
               groupStr.append(",").append(data.get("GROUP_NAME"));
               groupIdStr.append(",").append(data.get("GROUP_ID"));
            }

            tmp.setDevice_group_name(groupStr.toString());
            tmp.setDevice_group_id(groupIdStr.toString());
         }
      }

      PagedListInfo pgInfo = new PagedListInfo(scheduleAdminInfo, intCount);
      return pgInfo;
   }

   public PagedListInfo getScheduleByUser(int startPos, int pageSize, Map condition) throws SQLException {
      SelectConditionScheduleAdmin condObj = (SelectConditionScheduleAdmin)condition.get("condition");
      String sort = condObj.getSort_name();
      String dir = condObj.getOrder_dir();
      String strNameLike = condObj.getNameLike();
      String strGroup = condObj.getGroupType();
      String device_type = condObj.getDevice_type();
      String selId = condObj.getSelId();
      String isSelect = condObj.getIsSelect();
      String[] arrDeviceType = new String[0];
      if (device_type != null && !device_type.equals("")) {
         arrDeviceType = device_type.split(",");
      }

      if (dir != null) {
         dir = dir.toUpperCase();
      }

      if (strNameLike != null) {
         strNameLike = strNameLike.replaceAll("_", "^_");
      }

      int group_id = -1;
      List childGroups = new ArrayList();
      Map queryParameterMap = new HashMap();
      if (strGroup == null || strGroup.equals("") || !"trash".equalsIgnoreCase(strGroup) && !"all".equalsIgnoreCase(strGroup)) {
         queryParameterMap.put("strGroup", Long.valueOf(strGroup));
      } else {
         String strRootGroup = condObj.getUserRootGroup();
         group_id = this.getProgramGroupForUser(strRootGroup);
         if (group_id != -1) {
            childGroups = this.getChildGroupIdList(group_id, true);
         }

         queryParameterMap.put("strGroup", strGroup);
      }

      queryParameterMap.put("childGroups", childGroups);
      queryParameterMap.put("isSelect", isSelect);
      queryParameterMap.put("selId", selId);
      queryParameterMap.put("arrDeviceType", arrDeviceType);
      queryParameterMap.put("strNameLike", strNameLike);
      queryParameterMap.put("sort", sort);
      queryParameterMap.put("dir", dir);
      queryParameterMap.put("startPos", DaoTools.offsetStartPost(startPos));
      queryParameterMap.put("pageSize", pageSize);
      queryParameterMap.put("groupId", group_id);
      Long count = ((EventScheduleInfoDaoMapper)this.getMapper()).getScheduleByUserCount(queryParameterMap);
      int totalCnt = 0;
      if (count != null) {
         totalCnt = count.intValue();
      }

      List pgList = ((EventScheduleInfoDaoMapper)this.getMapper()).getScheduleByUser(queryParameterMap);
      PagedListInfo pgInfo = new PagedListInfo(pgList, totalCnt);
      return pgInfo;
   }

   public PagedListInfo getScheduleAdminInfoSearch(int startPos, int pageSize, Map condition) throws SQLException {
      SelectConditionScheduleAdmin condObj = (SelectConditionScheduleAdmin)condition.get("condition");
      String sort = condObj.getSort_name();
      String dir = condObj.getOrder_dir();
      String strGroup = condObj.getGroupType();
      String strSearchId = condObj.getSearch_id();
      String device_type = condObj.getDevice_type();
      SchSearchInfo searchInfo = SchSearchInfoImpl.getInstance();
      ScheduleSearch search = null;
      if (strSearchId != null && !"-1".equals(strSearchId)) {
         search = searchInfo.getSchSearchBySearchId(Long.parseLong(strSearchId));
      }

      int group_id = -1;
      if (strGroup != null && !"".equals(strGroup) && "all".equalsIgnoreCase(strGroup)) {
         String strRootGroup = condObj.getUserRootGroup();
         group_id = this.getProgramGroupForUser(strRootGroup);
      }

      List childGroups = new ArrayList();
      if (group_id != -1) {
         childGroups = this.getChildGroupIdList(group_id, true);
      }

      String[] arrDeviceType = new String[0];
      if (device_type != null && !device_type.equals("")) {
         arrDeviceType = device_type.split(",");
      }

      if (sort != null && !sort.equals("") && dir != null && !dir.equals("")) {
         dir = dir.toUpperCase();
      }

      Map queryParams = new HashMap();
      if (search != null) {
         queryParams.put("content", search.getContent());
         queryParams.put("create_start_date", search.getCreate_start_date());
         queryParams.put("create_end_date", search.getCreate_end_date());
         queryParams.put("modify_start_date", search.getModify_start_date());
         queryParams.put("modify_end_date", search.getModify_end_date());
         queryParams.put("deviceGroupId", search.getDevice_group());
         queryParams.put("contentId", search.getContent_id());
         queryParams.put("mapping", search.getMapping());
      }

      queryParams.put("arrDeviceType", arrDeviceType);
      queryParams.put("childGroups", childGroups);
      queryParams.put("dir", dir);
      queryParams.put("sort", sort);
      queryParams.put("strGroup", strGroup);
      queryParams.put("startPos", startPos);
      queryParams.put("pageSize", pageSize);
      queryParams.put("group_id", group_id);
      Long tmpCnt = ((EventScheduleInfoDaoMapper)this.getMapper()).getScheduleAdminInfoSearchCount(queryParams);
      int totalCnt = 0;
      if (tmpCnt != null) {
         totalCnt = tmpCnt.intValue();
      }

      List pgList = ((EventScheduleInfoDaoMapper)this.getMapper()).getScheduleAdminInfoSearch(queryParams);
      PagedListInfo pgInfo = new PagedListInfo(pgList, totalCnt);
      return pgInfo;
   }

   public PagedListInfo getScheduleByUserSearch(int startPos, int pageSize, Map condition) throws SQLException {
      SelectConditionScheduleAdmin condObj = (SelectConditionScheduleAdmin)condition.get("condition");
      String sort = condObj.getSort_name();
      String dir = condObj.getOrder_dir();
      String strGroup = condObj.getGroupType();
      String strSearchId = condObj.getSearch_id();
      String device_type = condObj.getDevice_type();
      String strNameLike = condObj.getNameLike();
      if (strNameLike != null) {
         strNameLike = strNameLike.replaceAll("_", "↓_");
      }

      SchSearchInfo searchInfo = SchSearchInfoImpl.getInstance();
      ScheduleSearch search = null;
      if (!strSearchId.equals("-1")) {
         search = searchInfo.getSchSearchBySearchId(Long.parseLong(strSearchId));
      }

      String[] arrDeviceType = null;
      if (device_type != null && !device_type.equals("")) {
         arrDeviceType = device_type.split(",");
      }

      int group_id = -1;
      List childGroups = new ArrayList();
      if (strGroup != null && !strGroup.equals("") && "all".equalsIgnoreCase(strGroup)) {
         String strRootGroup = condObj.getUserRootGroup();
         group_id = this.getProgramGroupForUser(strRootGroup);
         if (group_id != -1) {
            childGroups = this.getChildGroupIdList(group_id, true);
         }
      }

      if (sort != null && !sort.equals("") && dir != null && !dir.equals("")) {
         sort = sort.toUpperCase();
         dir = dir.toUpperCase();
      }

      Map queryParams = new HashMap();
      if (search != null) {
         queryParams.put("content", search.getContent());
         queryParams.put("create_start_date", search.getCreate_start_date());
         queryParams.put("create_end_date", search.getCreate_end_date());
         queryParams.put("modify_start_date", search.getModify_start_date());
         queryParams.put("modify_end_date", search.getModify_end_date());
         queryParams.put("deviceGroupId", search.getDevice_group());
         queryParams.put("contentId", search.getContent_id());
         queryParams.put("mapping", search.getMapping());
      }

      queryParams.put("arrDeviceType", arrDeviceType);
      queryParams.put("childGroups", childGroups);
      queryParams.put("dir", dir);
      queryParams.put("sort", sort);
      queryParams.put("strGroup", strGroup);
      queryParams.put("startPos", startPos);
      queryParams.put("pageSize", pageSize);
      queryParams.put("groupId", group_id);
      queryParams.put("strNameLike", strNameLike);
      Long tmpCnt = ((EventScheduleInfoDaoMapper)this.getMapper()).getScheduleByUserSearchCount(queryParams);
      int totalCnt = 0;
      if (tmpCnt != null) {
         totalCnt = tmpCnt.intValue();
      }

      List pgList = ((EventScheduleInfoDaoMapper)this.getMapper()).getScheduleByUserSearch(queryParams);
      PagedListInfo pgInfo = new PagedListInfo(pgList, totalCnt);
      return pgInfo;
   }

   private int getProgramGroupForUser(String strOrg) throws SQLException {
      Long group = ((EventScheduleInfoDaoMapper)this.getMapper()).getProgramGroupForUser(strOrg);
      return group == null ? -1 : group.intValue();
   }

   public List getChildGroupIdList(int group_id, boolean recursive) throws SQLException {
      List rtList = new ArrayList();
      List groupIdList = null;
      groupIdList = ((EventScheduleInfoDaoMapper)this.getMapper()).getChildGroupIdList(group_id, new Long(999999L));
      if (groupIdList != null) {
         for(int i = 0; i < groupIdList.size(); ++i) {
            if (recursive) {
               Long group = (Long)((Map)groupIdList.get(i)).get("group_id");
               rtList.add(group);
               List temp = this.getChildGroupIdList(group.intValue(), recursive);
               if (temp != null && temp.size() != 0) {
                  rtList.addAll(temp);
               }
            } else {
               rtList.add((Long)((Map)groupIdList.get(i)).get("group_id"));
            }
         }
      }

      return rtList;
   }

   private Long getProgramGroupById(String programId) throws SQLException {
      return ((EventScheduleInfoDaoMapper)this.getMapper()).getProgramGroupById(programId);
   }

   private String getProgramGroupRoot(int groupId) throws SQLException {
      Map info = ((EventScheduleInfoDaoMapper)this.getMapper()).getProgramGroupRoot(groupId);
      int newGroupParentId = ((Long)info.get("P_GROUP_ID")).intValue();
      return newGroupParentId != 0 ? this.getProgramGroupRoot(newGroupParentId) : (String)info.get("GROUP_NAME");
   }

   private String getDeviceGroupRoot(int groupId) throws SQLException {
      Map info = ((EventScheduleInfoDaoMapper)this.getMapper()).getDeviceGroupRoot(groupId);
      String strName = null;
      int newGroupParentId = ((Long)info.get("P_GROUP_ID")).intValue();
      if (newGroupParentId != 0) {
         return this.getDeviceGroupRoot(newGroupParentId);
      } else {
         strName = (String)info.get("GROUP_NAME");
         return strName;
      }
   }

   public boolean addEventSchedule(EventScheduleEntity eventScheduleEntity) throws SQLException {
      try {
         ((EventScheduleInfoDaoMapper)this.getMapper()).addEventSchedule(eventScheduleEntity);
         return true;
      } catch (Exception var3) {
         this.logger.error("", var3);
         return false;
      }
   }

   public boolean updateEventSchedule(EventScheduleEntity eventScheduleEntity) throws SQLException {
      try {
         ((EventScheduleInfoDaoMapper)this.getMapper()).updateEventSchedule(eventScheduleEntity);
         return true;
      } catch (Exception var3) {
         this.logger.error("", var3);
         return false;
      }
   }

   public boolean addEventScheduleEvent(EventScheduleEventEntity eventScheduleEventEntity) throws SQLException {
      try {
         ((EventScheduleInfoDaoMapper)this.getMapper()).addEventScheduleEvent(eventScheduleEventEntity);
         return true;
      } catch (Exception var3) {
         this.logger.error("", var3);
         return false;
      }
   }

   public boolean deleteEventScheduleEvent(String schedule_id) throws SQLException {
      try {
         ((EventScheduleInfoDaoMapper)this.getMapper()).deleteEventScheduleEvent(schedule_id);
         return true;
      } catch (Exception var3) {
         this.logger.error("", var3);
         return false;
      }
   }

   public EventScheduleEntity getEventSchedule(String schedule_id) throws SQLException {
      return ((EventScheduleInfoDaoMapper)this.getMapper()).getEventSchedule(schedule_id);
   }

   public EventScheduleEventEntity getEventScheudleWidhscheduleIdWidthEventId(String scheduleId, String eventId) throws SQLException {
      return ((EventScheduleInfoDaoMapper)this.getMapper()).getEventScheudleWidhscheduleIdWidthEventId(scheduleId, eventId);
   }

   public List getEventScheduleEventByEventScheduleId(String schedule_id) throws SQLException {
      return ((EventScheduleInfoDaoMapper)this.getMapper()).getEventScheduleEventByEventScheduleId(schedule_id);
   }

   public boolean setActiveScheduleVersion(String schedule_id, long version) throws SQLException {
      int cnt = ((EventScheduleInfoDaoMapper)this.getMapper()).updateActiveScheduleVersion(schedule_id, version);
      if (cnt > 0) {
         return true;
      } else {
         cnt = ((EventScheduleInfoDaoMapper)this.getMapper()).insertActiveScheduleVersion(schedule_id, version);
         return cnt > 0;
      }
   }

   public long getActiveScheduleVersion(String schedule_id) throws SQLException {
      Long ver = ((EventScheduleInfoDaoMapper)this.getMapper()).getActiveScheduleVersion(schedule_id);
      return ver != null ? ver : -1L;
   }

   public long getScheduleVersion(String schedule_id) throws SQLException {
      Long ver = ((EventScheduleInfoDaoMapper)this.getMapper()).getScheduleVersion(schedule_id);
      return ver != null ? ver : -1L;
   }

   public Map getDeviceGroupIdsAndName(String schedule_id) throws SQLException {
      List list = ((EventScheduleInfoDaoMapper)this.getMapper()).getDeviceGroupIdsAndName(schedule_id);
      int count = false;
      String group_ids = "";
      String group_names = "";

      for(int count = 0; count < list.size(); ++count) {
         Map m = (Map)list.get(count);
         if (count > 0) {
            group_ids = group_ids + ",";
            group_names = group_names + ",";
         }

         group_ids = group_ids + m.get("device_group_id");
         group_names = group_names + (String)((String)m.get("group_name"));
      }

      Map rt = new HashMap();
      rt.put("device_group_ids", group_ids);
      rt.put("group_names", group_names);
      return rt;
   }

   public boolean setScheduleDeployTime(String schedule_id) throws SQLException {
      int cnt = ((EventScheduleInfoDaoMapper)this.getMapper()).setScheduleDeployTime(schedule_id);
      return cnt > 0;
   }

   public List getDeviceProgramMapList(String programId) throws SQLException {
      return ((EventScheduleInfoDaoMapper)this.getMapper()).getDeviceProgramMapList(programId);
   }

   public boolean isProgramNameUnique(String program_name, String program_id, int prog_group_id) throws SQLException {
      return true;
   }

   public List getProgramGroupIdAndName(String schedule_id) throws SQLException {
      return ((EventScheduleInfoDaoMapper)this.getMapper()).getProgramGroupIdAndName(schedule_id);
   }

   public boolean addScheduleGroupMap(String schedule_id, long program_group_id) throws SQLException {
      int cnt = ((EventScheduleInfoDaoMapper)this.getMapper()).addScheduleGroupMap(schedule_id, program_group_id);
      return cnt > 0;
   }

   public boolean addScheduleDeviceGroupMap(String schedule_id, long device_group_id) throws SQLException {
      int cnt = ((EventScheduleInfoDaoMapper)this.getMapper()).addScheduleDeviceGroupMap(schedule_id, device_group_id);
      return cnt > 0;
   }

   public boolean deleteScheduleGroupMap(String schedule_id) throws SQLException {
      int cnt = ((EventScheduleInfoDaoMapper)this.getMapper()).deleteScheduleGroupMap(schedule_id);
      return cnt > 0;
   }

   public List getEventScheduleIdListByEventId(String event_id) throws SQLException {
      return ((EventScheduleInfoDaoMapper)this.getMapper()).getEventScheduleIdListByEventId(event_id);
   }

   public List getEventStartIndexListByEventId(String event_id) throws SQLException {
      return ((EventScheduleInfoDaoMapper)this.getMapper()).getEventStartIndexListByEventId(event_id);
   }

   public List getEventEndIndexListByEventId(String event_id) throws SQLException {
      return ((EventScheduleInfoDaoMapper)this.getMapper()).getEventEndIndexListByEventId(event_id);
   }

   public List getEventScheduleList() throws SQLException {
      return ((EventScheduleInfoDaoMapper)this.getMapper()).getEventScheduleList();
   }

   public String getEventScheduleIdByDeviceId(String deviceId) throws SQLException {
      Map result = ((EventScheduleInfoDaoMapper)this.getMapper()).getEventScheduleIdByDeviceId(deviceId);
      return result != null && (String)result.get("schedule_id") != null && !((String)result.get("schedule_id")).equals("") ? (String)result.get("schedule_id") : "00000000-0000-0000-0000-000000000000";
   }

   public long getVersionByEventScheduleId(String eventScheduleId) throws SQLException {
      if (eventScheduleId.equalsIgnoreCase("00000000-0000-0000-0000-000000000000")) {
         return -1L;
      } else {
         Map result = ((EventScheduleInfoDaoMapper)this.getMapper()).getVersionByEventScheduleId(eventScheduleId);
         return (Long)((Long)result.get("version"));
      }
   }

   public String getEventScheduleName(String eventScheduleId) throws SQLException {
      return ((EventScheduleInfoDaoMapper)this.getMapper()).getEventScheduleName(eventScheduleId);
   }

   public List getFilteringDeviceTypeList() throws SQLException {
      return ((EventScheduleInfoDaoMapper)this.getMapper()).getFilteringDeviceTypeList();
   }

   public List getEventScheduleViewList(String scheduleId) throws SQLException {
      return ((EventScheduleInfoDaoMapper)this.getMapper()).getEventScheduleViewList(scheduleId);
   }

   public int getEventScheduleListCount(Long groupId) {
      return ((EventScheduleInfoDaoMapper)this.getMapper()).getEventScheduleListCount(groupId);
   }

   public List getDeletedEventList() throws SQLException {
      return ((EventScheduleInfoDaoMapper)this.getMapper()).getDeletedEventList();
   }

   public String getCreatorIdByEventScheduleId(String eventScheduleId) throws SQLException {
      String retVal = "";
      String result = ((EventScheduleInfoDaoMapper)this.getMapper()).getCreatorIdByEventScheduleId(eventScheduleId);
      if (result != null) {
         retVal = result;
      }

      return retVal;
   }

   public String getCreatorIdByEventManagerId(String eventManagerId) throws SQLException {
      String retVal = "";
      String result = ((EventScheduleInfoDaoMapper)this.getMapper()).getCreatorIdByEventManagerId(eventManagerId);
      if (result != null) {
         retVal = result;
      }

      return retVal;
   }
}
