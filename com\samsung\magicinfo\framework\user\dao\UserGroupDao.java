package com.samsung.magicinfo.framework.user.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SequenceDB;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.entity.UserGroup;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class UserGroupDao extends SqlSessionBaseDao {
   private Logger logger = LoggingManagerV2.getLogger(UserGroupDao.class);

   public UserGroupDao() {
      super();
   }

   public UserGroupDao(SqlSession session) {
      super(session);
   }

   public UserGroup getAllByUserGroupId(Long userGroupId) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getAllByUserGroupId(userGroupId);
   }

   public List getAllOrganizationGroup() throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getAllOrganizationGroup();
   }

   public List getAllOrganizationGroupList() throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getAllOrganizationGroupList();
   }

   public Long getOrgCntInOrgGroupById(Long orgId) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getOrgCntInOrgGroupById(orgId);
   }

   public boolean deleteOrgGroupFromMultiOrgGroup(Long OrgId) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).deleteOrgGroupFromMultiOrgGroup(OrgId);
   }

   public Long getOrgCount(Long OrgId) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getOrgCount(OrgId);
   }

   public List getAllUserGroup(Map map, int startPos, int pageSize) throws SQLException {
      try {
         Map paramsMap = new HashMap();
         paramsMap.putAll(map);
         if (!map.get("src_name").equals("")) {
            String searchText = map.get("src_name").toString();
            searchText = searchText.replaceAll("_", "^_");
            paramsMap.put("group_name", searchText);
         }

         int offset = 0;
         if (startPos > 0) {
            offset = startPos - 1;
         }

         paramsMap.put("offset", offset);
         paramsMap.put("pageSize", pageSize);
         return ((UserGroupDaoMapper)this.getMapper()).getAllUserGroup(paramsMap);
      } catch (SQLException var6) {
         this.logger.error("", var6);
         return null;
      }
   }

   public int getCountAllUserGroup(Map map) throws SQLException {
      Map paramsMap = new HashMap();
      paramsMap.putAll(map);
      if (!map.get("src_name").equals("")) {
         String searchText = map.get("src_name").toString();
         searchText = searchText.replaceAll("_", "^_");
         paramsMap.put("group_name", searchText);
      }

      return ((UserGroupDaoMapper)this.getMapper()).getCountAllUserGroup(paramsMap);
   }

   public long addUserGroup(UserGroup userGroup) throws SQLException {
      long userGroupId = (long)SequenceDB.getNextValue("MI_USER_INFO_GROUP");
      if (userGroupId <= 0L) {
         return -1L;
      } else {
         return ((UserGroupDaoMapper)this.getMapper()).addUserGroup(userGroup, userGroupId) > 0 ? userGroupId : -1L;
      }
   }

   public List getChildGroupList(Long group_id, boolean recursive) throws SQLException {
      List groupList = new ArrayList();
      List resList = ((UserGroupDaoMapper)this.getMapper()).getChildGroupList(group_id, 999999);
      groupList.addAll(resList);
      if (recursive) {
         Iterator var5 = resList.iterator();

         while(var5.hasNext()) {
            UserGroup userGroup = (UserGroup)var5.next();
            groupList.addAll(this.getChildGroupList(userGroup.getGroup_id(), recursive));
         }
      }

      return groupList;
   }

   public Boolean setUserGroup(UserGroup userGroup) throws SQLException {
      if (userGroup.getGroup_id() <= 0L) {
         this.logger.fatal("Block to del/set/move schedule group of root_group_id");
         return false;
      } else {
         return ((UserGroupDaoMapper)this.getMapper()).setUserGroup(userGroup);
      }
   }

   public Boolean deleteUserGroupByUserGroupId(Long userGroupId) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).deleteUserGroupByUserGroupId(userGroupId);
   }

   public Boolean addMapGroupUser(String userId, Long userGroupId) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).addMapGroupUser(userId, userGroupId);
   }

   public Boolean deleteMapGroupUser(String userId, SqlSession session) throws SQLException {
      try {
         UserGroupDaoMapper mapper = (UserGroupDaoMapper)this.getMapper();
         if (session != null) {
            mapper = (UserGroupDaoMapper)this.getMapper(session);
         }

         mapper.deleteMapGroupUser(userId);
      } catch (SQLException var4) {
         throw var4;
      }

      return true;
   }

   public Boolean deleteMapGroupUser(String userId) throws SQLException {
      return this.deleteMapGroupUser(userId, (SqlSession)null);
   }

   public Long getUserGroupIdMapGroupUserByUserId(String userId) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getUserGroupIdMapGroupUserByUserId(userId);
   }

   public UserGroup getUserGroupAllByUserId(String userId) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getUserGroupAllByUserId(userId);
   }

   public Boolean setUserGroupMapGroupUserIdByUserId(String userId, Long userGroupId) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).setUserGroupMapGroupUserIdByUserId(userId, userGroupId);
   }

   public int getCountUserByUserGroupId(Long userGroupId) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getCountUserByUserGroupId(userGroupId);
   }

   public int getCountChildGroupByParentGroupId(Long parentGroupId) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getCountChildGroupByParentGroupId(parentGroupId);
   }

   public String getGroupNameByGroupId(Long userGroupId) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getGroupNameByGroupId(userGroupId);
   }

   public Long getGroupIdByName(String groupName, long orgGroupId) throws SQLException {
      UserGroupDaoMapper mapper = (UserGroupDaoMapper)this.getMapper();
      List groupIds = mapper.getGroupIdByName(groupName, orgGroupId);
      if (groupIds != null && groupIds.size() > 0) {
         if (groupIds.size() > 1) {
            this.logger.error("[INFO] getGroupIdByName size > 1 [" + groupName + "][" + orgGroupId + "]");
         }

         return (Long)groupIds.get(0);
      } else {
         return null;
      }
   }

   public int getExistGroupByName(String groupName, long orgGroupId) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getExistGroupByName(groupName, orgGroupId);
   }

   public int getExistOrganizationByName(String groupName) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getExistOrganizationByName(groupName);
   }

   public List getChildGroupIdList(long group_id, boolean recursive, SqlSession session) throws SQLException {
      List rtList = new ArrayList();
      UserGroupDaoMapper mapper = (UserGroupDaoMapper)this.getMapper();
      if (session != null) {
         mapper = (UserGroupDaoMapper)this.getMapper(session);
      }

      List groupIdList = mapper.getChildGroupIdList(group_id, 999999);
      if (groupIdList != null) {
         Iterator var8 = groupIdList.iterator();

         while(var8.hasNext()) {
            Map map = (Map)var8.next();
            if (recursive) {
               Long group = (Long)map.get("group_id");
               rtList.add(group);
               List temp = this.getChildGroupIdList((long)group.intValue(), recursive, session);
               if (temp != null && temp.size() != 0) {
                  rtList.addAll(temp);
               }
            } else {
               rtList.add((Long)map.get("group_id"));
            }
         }
      }

      return rtList;
   }

   public List getChildGroupIdList(long group_id, boolean recursive) throws SQLException {
      return this.getChildGroupIdList(group_id, recursive, (SqlSession)null);
   }

   public List getAllUserListByGroupIdList(List groupIdList, SqlSession session) throws SQLException {
      if (groupIdList != null && !groupIdList.isEmpty()) {
         UserGroupDaoMapper mapper = (UserGroupDaoMapper)this.getMapper();
         if (session != null) {
            mapper = (UserGroupDaoMapper)this.getMapper(session);
         }

         return mapper.getAllUserListByGroupIdList(groupIdList);
      } else {
         return Collections.emptyList();
      }
   }

   public List getAllUserListByGroupIdList(List groupIdList) throws SQLException {
      return this.getAllUserListByGroupIdList(groupIdList, (SqlSession)null);
   }

   public boolean deleteChildGroupByGroupId(String reason, long groupId) throws SQLException {
      if (groupId <= 0L) {
         this.logger.error("Block to del/set/move schedule group of root_group_id");
         return false;
      } else {
         SqlSession session = this.openNewSession(false);

         boolean var17;
         try {
            List groups = this.getChildGroupIdList(groupId, true, session);
            groups.add(groupId);
            List users = this.getAllUserListByGroupIdList(groups, session);
            UserInfo userInfo = UserInfoImpl.getInstance(session);
            boolean result = true;
            Iterator var9 = users.iterator();

            while(var9.hasNext()) {
               User user = (User)var9.next();
               result = this.deleteMapGroupUser(user.getUser_id(), session);
               if (!result) {
                  break;
               }

               result = userInfo.setIsDeletedByUserId(reason, user.getUser_id(), false);
               if (!result) {
                  break;
               }
            }

            if (!result) {
               session.rollback();
               var17 = false;
               return var17;
            }

            var9 = groups.iterator();

            while(var9.hasNext()) {
               Long groupIdToDelete = (Long)var9.next();
               if (((UserGroupDaoMapper)this.getMapper(session)).deleteChildGroupByGroupId_UserInfoGroup(groupIdToDelete) <= 0) {
                  session.rollback();
                  boolean var11 = false;
                  return var11;
               }
            }

            session.commit();
            var17 = result;
         } catch (SQLException var15) {
            session.rollback();
            throw var15;
         } finally {
            if (session != null) {
               session.close();
            }

         }

         return var17;
      }
   }

   public long getOrganizationGroupIdByGroupId(long groupId, SqlSession session) throws SQLException {
      UserGroupDaoMapper mapper = (UserGroupDaoMapper)this.getMapper();
      if (session != null) {
         mapper = (UserGroupDaoMapper)this.getMapper(session);
      }

      long pGroupId = mapper.getOrganizationGroupIdByGroupId(groupId);
      return pGroupId == 0L ? groupId : this.getOrganizationGroupIdByGroupId(pGroupId, session);
   }

   public long getOrganizationGroupIdByGroupId(long groupId) throws SQLException {
      return this.getOrganizationGroupIdByGroupId(groupId, (SqlSession)null);
   }

   public long getOrgGroupIdByName(String groupName) throws SQLException {
      try {
         return ((UserGroupDaoMapper)this.getMapper()).getOrgGroupIdByName(groupName);
      } catch (Exception var3) {
         return 0L;
      }
   }

   public long getCountOrgGroupIdByName(String groupName) throws SQLException {
      try {
         return ((UserGroupDaoMapper)this.getMapper()).getCountOrgGroupIdByName(groupName);
      } catch (Exception var3) {
         return 0L;
      }
   }

   public boolean getIsOrganizationGroupIdByGroupId(long groupId) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getIsOrganizationGroupIdByGroupId(groupId) != 0;
   }

   public Object getMaxDepth() throws SQLException {
      Object depth_count = null;

      try {
         depth_count = ((UserGroupDaoMapper)this.getMapper()).getMaxDepth();
      } catch (Exception var3) {
         this.logger.error("", var3);
      }

      return depth_count;
   }

   public List getSpecificDepthUserGroupList(int depth) throws SQLException {
      List result = null;

      try {
         result = ((UserGroupDaoMapper)this.getMapper()).getSpecificDepthUserGroupList(depth);
      } catch (Exception var4) {
         this.logger.error("", var4);
      }

      return result;
   }

   public List getRootGroup() throws SQLException {
      List result = null;

      try {
         result = ((UserGroupDaoMapper)this.getMapper()).getRootGroup();
      } catch (Exception var3) {
         this.logger.error("", var3);
      }

      return result;
   }

   public boolean insertNewUserGroup(UserGroup userGroup) throws SQLException {
      long userGroupId = (long)SequenceDB.getNextValue("MI_USER_INFO_GROUP");
      if (userGroupId <= 0L) {
         return false;
      } else {
         boolean result = false;

         try {
            result = ((UserGroupDaoMapper)this.getMapper()).insertNewUserGroup(userGroup, userGroupId);
         } catch (Exception var6) {
            this.logger.error("", var6);
         }

         return result;
      }
   }

   public boolean deleteUserGroup(UserGroup userGroup) throws SQLException {
      boolean result = false;
      long userGroupId = (long)SequenceDB.getNextValue("MI_USER_INFO_GROUP");
      if (userGroupId <= 0L) {
         return false;
      } else {
         try {
            result = ((UserGroupDaoMapper)this.getMapper()).deleteUserGroup(userGroup);
         } catch (Exception var6) {
            this.logger.error("", var6);
         }

         return result;
      }
   }

   public boolean updateUserGroup(UserGroup userGroup) throws SQLException {
      boolean result = false;
      long userGroupId = (long)SequenceDB.getNextValue("MI_USER_INFO_GROUP");
      if (userGroupId <= 0L) {
         return false;
      } else {
         try {
            result = ((UserGroupDaoMapper)this.getMapper()).updateUserGroup(userGroup);
         } catch (Exception var6) {
            this.logger.error("", var6);
         }

         return result;
      }
   }

   public boolean updateRootGroupID(UserGroup userGroup) throws SQLException {
      boolean result = false;
      long userGroupId = (long)SequenceDB.getNextValue("MI_USER_INFO_GROUP");
      if (userGroupId <= 0L) {
         return false;
      } else {
         try {
            result = ((UserGroupDaoMapper)this.getMapper()).updateRootGroupID(userGroup);
         } catch (Exception var6) {
            this.logger.error("", var6);
         }

         return result;
      }
   }

   public int getCountAllOrganization() throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getCountAllOrganization();
   }

   public void getUserGroupTreeFirstLevel(String organization, ArrayList tree_list, String sortType, String skipId) {
      try {
         List default_group = ((UserGroupDaoMapper)this.getMapper()).getUserGroupTreeFirstLevel(organization, sortType, skipId);
         tree_list.add(0, default_group);
         this.logger.debug("DEFAULT_GROUP SIZE IS " + default_group.size());
         Iterator var6 = default_group.iterator();

         while(true) {
            UserGroup temp_group;
            do {
               do {
                  if (!var6.hasNext()) {
                     return;
                  }

                  temp_group = (UserGroup)var6.next();
               } while(temp_group.getGroup_depth() == 0L);
            } while(!skipId.equals("") && temp_group.getGroup_id().intValue() == Integer.parseInt(skipId));

            this.getUserGroupTreeSpecificLevel(temp_group.getGroup_id(), temp_group.getGroup_depth(), tree_list, sortType, skipId);
         }
      } catch (Exception var8) {
         this.logger.error("", var8);
      }
   }

   private void getUserGroupTreeSpecificLevel(long p_group_id, long depth, ArrayList tree_list, String sortType, String skipId) {
      try {
         List default_group = ((UserGroupDaoMapper)this.getMapper()).getUserGroupTreeSpecificLevel(p_group_id, skipId, sortType);
         if (default_group != null && default_group.size() != 0) {
            if (tree_list.size() == (int)depth) {
               tree_list.add((int)depth, default_group);
            } else {
               List pGroupList = (List)tree_list.get((int)depth);
               pGroupList.addAll(default_group);
               tree_list.set((int)depth, pGroupList);
            }

            Iterator var12 = default_group.iterator();

            while(true) {
               UserGroup temp_group;
               do {
                  if (!var12.hasNext()) {
                     return;
                  }

                  temp_group = (UserGroup)var12.next();
               } while(!skipId.equals("") && temp_group.getGroup_id().intValue() == Integer.parseInt(skipId));

               this.getUserGroupTreeSpecificLevel(temp_group.getGroup_id(), temp_group.getGroup_depth(), tree_list, sortType, skipId);
            }
         }
      } catch (Exception var11) {
         this.logger.error("", var11);
      }

   }

   public void getUserOrganTree(ArrayList tree_list, String sortType) {
      try {
         List default_group = ((UserGroupDaoMapper)this.getMapper()).getUserOrganTree(sortType);
         tree_list.add(0, default_group);
      } catch (Exception var5) {
         this.logger.error("", var5);
      }

   }

   public boolean setCanDeleteByGroupId(long groupId) throws SQLException {
      SqlSession session = this.openNewSession(false);

      boolean var6;
      try {
         long organId = this.getOrganizationGroupIdByGroupId(groupId, session);
         if (this.setResetCanDeleteByOrganId(organId, session)) {
            List groupList = this.getRecuriveParentGroupListByGroupId(organId, groupId, session);
            Iterator var7 = groupList.iterator();

            Long groupIdForUpdate;
            do {
               if (!var7.hasNext()) {
                  session.commit();
                  boolean var16 = true;
                  return var16;
               }

               groupIdForUpdate = (Long)var7.next();
            } while(((UserGroupDaoMapper)this.getMapper(session)).setCanDeleteByGroupId_UserInfoGroup(groupIdForUpdate) > 0);

            session.rollback();
            boolean var9 = false;
            return var9;
         }

         session.rollback();
         var6 = false;
      } catch (SQLException var13) {
         session.rollback();
         throw var13;
      } finally {
         session.close();
      }

      return var6;
   }

   private boolean setResetCanDeleteByOrganId(long organId, SqlSession session) throws SQLException {
      UserGroupDaoMapper mapper = (UserGroupDaoMapper)this.getMapper();
      if (session != null) {
         mapper = (UserGroupDaoMapper)this.getMapper(session);
      }

      return mapper.setResetCanDeleteByOrganId(organId);
   }

   private List getRecuriveParentGroupListByGroupId(long organId, long groupId, SqlSession session) throws SQLException {
      List rtList = new ArrayList();
      if (groupId == organId) {
         return rtList;
      } else {
         UserGroupDaoMapper mapper = (UserGroupDaoMapper)this.getMapper();
         if (session != null) {
            mapper = (UserGroupDaoMapper)this.getMapper(session);
         }

         Long pGroupId = mapper.getRecuriveParentGroupListByGroupId(groupId);
         if (pGroupId != null && pGroupId != 0L) {
            rtList.add(groupId);
            rtList.addAll(this.getRecuriveParentGroupListByGroupId(organId, pGroupId, session));
         }

         return rtList;
      }
   }

   public boolean getCanDeleteByGroupId(long groupId) throws SQLException {
      String canDeleteByGroupId = ((UserGroupDaoMapper)this.getMapper()).getCanDeleteByGroupId(groupId);
      return canDeleteByGroupId.equals("Y");
   }

   public boolean getCanDeleteByGroupId(long userGroupId, long selectedGroupId) throws SQLException {
      int userGroupIdResult = ((UserGroupDaoMapper)this.getMapper()).getGroupDepthByGroupId(userGroupId);
      int selectedGroupIdResult = ((UserGroupDaoMapper)this.getMapper()).getGroupDepthByGroupId(selectedGroupId);
      return userGroupIdResult < selectedGroupIdResult;
   }

   public UserGroup getGroupById(long groupId) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getGroupById(groupId);
   }

   public int getOrgAdminCount(long orgGroupId) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getOrgAdminCount(orgGroupId);
   }

   public List getUserManageGroupListByUserId(String userId) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getUserManageGroupListByUserId(userId);
   }

   public Boolean saveUserManangerGroup(String userId, List orgList) throws SQLException {
      return true;
   }

   public Boolean deleteUserManageGroupListByUserId(String userId) throws SQLException {
      return true;
   }

   public List getMngOrgGroupList() throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getMngOrgGroupList();
   }

   public String getCurMngOrgGroup(String userId) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getCurMngOrgGroup(userId);
   }

   public Boolean setCurMngOrgGroup(String userId, String mngOrgGroupName) throws SQLException {
      SqlSession session = this.openNewSession(false);

      Boolean var5;
      try {
         ((UserGroupDaoMapper)this.getMapper(session)).unsetCurMngOrgGroup(userId);
         ((UserGroupDaoMapper)this.getMapper(session)).setCurMngOrgGroup(userId, mngOrgGroupName);
         session.commit();
         return true;
      } catch (Exception var9) {
         this.logger.error(var9);
         session.rollback();
         var5 = false;
      } finally {
         session.close();
      }

      return var5;
   }

   public Boolean unsetCurMngOrgGroup(String userId) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).unsetCurMngOrgGroup(userId);
   }

   public Long getRootGroupIdByGroupId(Long groupId) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getRootGroupIdByGroupId(groupId);
   }

   public List getUserGroupBySearchText(String organizationName, String searchText) throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getUserGroupBySearchText(organizationName, searchText);
   }

   public List getUserCountByOrganization() throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getUserCountByOrganization();
   }

   public int getUserGroupTotalCount() throws SQLException {
      return ((UserGroupDaoMapper)this.getMapper()).getUserGroupTotalCount();
   }
}
