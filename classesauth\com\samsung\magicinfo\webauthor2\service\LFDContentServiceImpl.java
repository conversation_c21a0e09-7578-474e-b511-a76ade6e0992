package com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.exception.service.LFDXmlParseFileException;
import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.LFDCanvasContent;
import com.samsung.magicinfo.webauthor2.model.LFDContent;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.model.VerificationResponse;
import com.samsung.magicinfo.webauthor2.util.FileHashUtil;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import javax.servlet.ServletContext;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpression;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

@Service
public class LFDContentServiceImpl implements LFDContentService {
  private static final Logger logger = LoggerFactory.getLogger(LFDContentService.class);
  
  private ContentService contentService;
  
  private ServletContext servletContext;
  
  private LFDMapperService lFDMapperService;
  
  private RemoteContentService remoteContentService;
  
  @Autowired
  public LFDContentServiceImpl(ContentService contentService, ServletContext servletContext, LFDMapperService lFDMapperService, RemoteContentService remoteContentService) {
    this.contentService = contentService;
    this.servletContext = servletContext;
    this.lFDMapperService = lFDMapperService;
    this.remoteContentService = remoteContentService;
  }
  
  public LFDContent getLFDContent(String contentId) {
    Content content = this.contentService.getContent(contentId);
    if (content == null || (content.getType() != MediaType.LFT && content.getType() != MediaType.LFD && content
      .getType() != MediaType.TLFD))
      return null; 
    String insertContents = this.servletContext.getRealPath("insertContents");
    Path workspaceFolder = Paths.get(insertContents, new String[0]);
    try {
      Files.createDirectories(workspaceFolder, (FileAttribute<?>[])new FileAttribute[0]);
    } catch (IOException ex) {
      logger.error(ex.getMessage(), ex);
    } 
    Path lfdContent = this.remoteContentService.getContentFileFromMagicInfoServer(workspaceFolder, content.getFileId(), content.getFileName());
    File file = lfdContent.toFile();
    Set<LFDCanvasContent> canvasContentSet = findCanvasSet(lfdContent.toFile());
    String hashFile = FileHashUtil.getHash(file);
    long fileSize = file.length();
    return new LFDContent(content, hashFile, fileSize, canvasContentSet);
  }
  
  public Page<Content> getLFDContentList(Pageable pageable, DeviceType deviceType) {
    return this.contentService.getContentResources(pageable, deviceType, Arrays.asList(new MediaType[] { MediaType.LFD }));
  }
  
  private Set<LFDCanvasContent> findCanvasSet(File file) {
    Set<LFDCanvasContent> canvasContentSet = new HashSet<>();
    DocumentBuilderFactory domFactory = DocumentBuilderFactory.newInstance();
    domFactory.setNamespaceAware(true);
    DocumentBuilder builder = null;
    try {
      builder = domFactory.newDocumentBuilder();
      Document doc = builder.parse(file);
      XPathFactory factory = XPathFactory.newInstance();
      XPath xpath = factory.newXPath();
      XPathExpression expr = xpath.compile("//FileItems/FileItem");
      NodeList list = (NodeList)expr.evaluate(doc, XPathConstants.NODESET);
      for (int i = 0; i < list.getLength(); i++) {
        Node node = list.item(i);
        String fileName = FilenameUtils.getName(node.getTextContent());
        String fileId = node.getAttributes().getNamedItem("FileID").getNodeValue();
        canvasContentSet.add(new LFDCanvasContent(fileId, fileName));
      } 
    } catch (XPathExpressionException|IOException|org.xml.sax.SAXException|javax.xml.parsers.ParserConfigurationException e) {
      throw new LFDXmlParseFileException(e.getMessage());
    } 
    return canvasContentSet;
  }
  
  public VerificationResponse verifyLFD(LFDContent lfdContent) {
    String lfdXml = this.remoteContentService.getXmlFileContents(lfdContent.getFileId(), lfdContent.getFileName());
    return this.lFDMapperService.validateWithResponse(lfdXml);
  }
}
