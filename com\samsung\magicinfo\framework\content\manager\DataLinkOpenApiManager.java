package com.samsung.magicinfo.framework.content.manager;

import com.samsung.magicinfo.framework.setup.entity.DatalinkServerEntity;
import com.samsung.magicinfo.framework.setup.entity.DatalinkServerTableEntity;
import com.samsung.magicinfo.framework.setup.manager.DatalinkServer;
import com.samsung.magicinfo.framework.setup.manager.DatalinkServerImpl;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.KeyManager;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

public class DataLinkOpenApiManager {
   protected final Log logger = LogFactory.getLog(this.getClass());
   private Map tableErrorCode = new HashMap();
   private Map dataErrorCode = new HashMap();

   public DataLinkOpenApiManager() {
      super();
   }

   public Map getTableErrorCode() {
      return this.tableErrorCode;
   }

   public Map getDataErrorCode() {
      return this.dataErrorCode;
   }

   public String makeUrl(String serverIp, String serverPort, Boolean serverSsl) {
      StringBuffer tableListUrl = new StringBuffer("");
      if (serverSsl) {
         tableListUrl.append("https");
      } else {
         tableListUrl.append("http");
      }

      tableListUrl.append("://" + serverIp + ":" + serverPort + "/DataLink/html/getDataTableList.do");
      return tableListUrl.toString();
   }

   public String makeUrlTable(String serverIp, String serverPort, String tableName, Boolean serverSsl) {
      StringBuffer url = new StringBuffer("");
      if (serverSsl) {
         url.append("https");
      } else {
         url.append("http");
      }

      url.append("://" + serverIp + ":" + serverPort + "/DataLink/html/getTableInfo.do/" + tableName);
      return url.toString();
   }

   private void trustAllHosts() {
      TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
         public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[0];
         }

         public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
         }

         public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
         }
      }};

      try {
         SSLContext sc = SSLContext.getInstance("TLS");
         sc.init((KeyManager[])null, trustAllCerts, new SecureRandom());
         HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
      } catch (Exception var3) {
         this.logger.error("", var3);
      }

   }

   public InputStream getHttpsInputStream(URL url) {
      InputStream in = null;
      HttpsURLConnection conn = null;

      try {
         this.trustAllHosts();
         conn = (HttpsURLConnection)url.openConnection();
         conn.setHostnameVerifier(new HostnameVerifier() {
            public boolean verify(String hostname, SSLSession session) {
               return true;
            }
         });
         in = conn.getInputStream();
      } catch (IOException var5) {
         this.logger.error("", var5);
      }

      return in;
   }

   public InputStreamReader getConnectionReader(URL url, boolean ssl) {
      InputStreamReader isr = null;
      InputStream inputStream = null;

      try {
         if (ssl) {
            HttpsURLConnection conn = (HttpsURLConnection)url.openConnection();
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);
            conn.setRequestProperty("Accept", "text/plain; charset=utf-8");
            inputStream = this.getHttpsInputStream(url);
         } else {
            URLConnection conn = url.openConnection();
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);
            conn.setRequestProperty("Accept", "text/plain; charset=utf-8");
            inputStream = conn.getInputStream();
         }

         if (inputStream != null) {
            isr = new InputStreamReader(inputStream, "UTF-8");
         }
      } catch (IOException var6) {
         this.logger.error("", var6);
      }

      return isr;
   }

   public HashMap getDataLinkTableList(String serverIp, String serverPort, String serverName, Boolean serverSsl, List datalinkAssignTableList) throws ParserConfigurationException, SAXException {
      HashMap totalResult = null;
      String tableListUrl = this.makeUrl(serverIp, serverPort, serverSsl);
      InputStreamReader isr = null;

      DocumentBuilder b;
      try {
         totalResult = new HashMap();
         URL url = new URL(tableListUrl);
         isr = this.getConnectionReader(url, serverSsl);
         StringBuffer sb = new StringBuffer();
         DocumentBuilderFactory factory;
         if (isr == null) {
            this.tableErrorCode.put(tableListUrl, "NO_CONNECT");
            factory = null;
            return factory;
         }

         int c;
         while((c = isr.read()) != -1) {
            sb.append((char)c);
         }

         isr.close();
         if (sb.toString().trim().length() >= 1) {
            factory = DocumentBuilderFactory.newInstance();
            factory.setValidating(false);
            b = factory.newDocumentBuilder();
            Document doc = b.parse(new InputSource(new StringReader(sb.toString())));
            Element root = doc.getDocumentElement();
            NodeList tablelist = root.getElementsByTagName("tablelist");
            int tableListSize = tablelist.getLength();
            if (tableListSize < 1) {
               this.tableErrorCode.put(tableListUrl, "NO_TABLE");
            }

            for(int i = 0; i < tableListSize; ++i) {
               DataLinkServerTable dataLinkServerTable = new DataLinkServerTable();
               Node tableNode = tablelist.item(i);
               String tableType = null;
               String tableName = null;
               String dynaName = null;
               String svrcName = null;
               NamedNodeMap namedNodeMap = tableNode.getAttributes();
               Node typeNode = namedNodeMap.getNamedItem("type");
               tableType = typeNode.getTextContent();
               NodeList tableNameList = tableNode.getChildNodes();
               int nameSize = tableNameList.getLength();

               Node nameNode;
               for(int nameSize_i = 0; nameSize_i < nameSize; ++nameSize_i) {
                  nameNode = tableNameList.item(nameSize_i);
                  if (nameNode.getNodeType() == 1) {
                     if (nameNode.getNodeName().equalsIgnoreCase("DYNANAME")) {
                        dynaName = nameNode.getTextContent();
                     } else if (nameNode.getNodeName().equalsIgnoreCase("NAME")) {
                        tableName = nameNode.getTextContent();
                     } else if (nameNode.getNodeName().equalsIgnoreCase("SVRCNAME")) {
                        svrcName = nameNode.getTextContent();
                     }
                  }
               }

               if (datalinkAssignTableList == null || datalinkAssignTableList != null && datalinkAssignTableList.size() > 0 && this.isAssignTable(datalinkAssignTableList, tableName)) {
                  dataLinkServerTable.setTableName(tableName);
                  dataLinkServerTable.setDynaName(dynaName);
                  dataLinkServerTable.setType(tableType);
                  dataLinkServerTable.setSvrcName(svrcName);
                  HashMap tempService = null;
                  ArrayList temp;
                  if (totalResult.containsKey(tableType)) {
                     tempService = (HashMap)totalResult.get(tableType);
                     nameNode = null;
                     if (tempService.containsKey(svrcName)) {
                        temp = (ArrayList)tempService.get(svrcName);
                     } else {
                        temp = new ArrayList();
                     }

                     temp.add(dataLinkServerTable);
                     tempService.put(svrcName, temp);
                  } else {
                     tempService = new HashMap();
                     temp = new ArrayList();
                     temp.add(dataLinkServerTable);
                     tempService.put(svrcName, temp);
                  }

                  totalResult.put(tableType, tempService);
               }
            }

            return totalResult;
         }

         this.tableErrorCode.put(tableListUrl, "NO_TABLE");
         b = null;
      } catch (MalformedURLException var44) {
         this.logger.error("", var44);
         return totalResult;
      } catch (UnsupportedEncodingException var45) {
         this.logger.error("", var45);
         return totalResult;
      } catch (IOException var46) {
         this.logger.error("", var46);
         return totalResult;
      } finally {
         if (isr != null) {
            try {
               isr.close();
            } catch (Exception var43) {
               this.logger.error("", var43);
            }
         }

      }

      return b;
   }

   public boolean isAssignTable(List datalinkAssignTableList, String tableName) {
      Iterator var3 = datalinkAssignTableList.iterator();

      DatalinkServerTableEntity datalinkServerTableEntity;
      do {
         if (!var3.hasNext()) {
            return false;
         }

         datalinkServerTableEntity = (DatalinkServerTableEntity)var3.next();
      } while(datalinkServerTableEntity.getTable_name() == null || !datalinkServerTableEntity.getTable_name().equals(tableName));

      return true;
   }

   public void addDataErrorCode(NodeList list, String url, String message) {
      if (list.getLength() < 1) {
         this.dataErrorCode.put(url, message);
      }

   }

   public boolean isSslUrl(String url) {
      boolean result = false;
      String[] arrUrl = url.split(":");
      if (arrUrl[0].equalsIgnoreCase("HTTP")) {
         result = false;
      } else if (arrUrl[0].equalsIgnoreCase("HTTPS")) {
         result = true;
      }

      return result;
   }

   public Map getTableData(String tableDataUrl) throws ParserConfigurationException, SAXException {
      Map resultMap = new HashMap();
      String columnLayout = "";
      String dataStore = "";
      InputStreamReader isr = null;

      label248: {
         DocumentBuilder b;
         try {
            URL url = new URL(tableDataUrl);
            isr = this.getConnectionReader(url, this.isSslUrl(tableDataUrl));
            StringBuffer sb = new StringBuffer();
            DocumentBuilderFactory factory;
            if (isr == null) {
               this.tableErrorCode.put(tableDataUrl, "NO_CONNECT");
               factory = null;
               return factory;
            }

            int c;
            while((c = isr.read()) != -1) {
               sb.append((char)c);
            }

            isr.close();
            if (sb.toString().trim().length() >= 1) {
               factory = DocumentBuilderFactory.newInstance();
               factory.setValidating(false);
               b = factory.newDocumentBuilder();
               Document doc = b.parse(new InputSource(new StringReader(sb.toString())));
               Element root = doc.getDocumentElement();
               NodeList colList = root.getElementsByTagName("colValue");
               columnLayout = columnLayout + "[";
               dataStore = dataStore + "[";
               this.addDataErrorCode(colList, tableDataUrl, "NO_DATA");

               for(int i = 0; i < colList.getLength(); ++i) {
                  NodeList entryList = colList.item(i).getChildNodes();
                  dataStore = dataStore + "{";
                  this.addDataErrorCode(entryList, tableDataUrl, "NO_DATA");

                  for(int j = 0; j < entryList.getLength(); ++j) {
                     if (entryList.item(j).getNodeType() == 1) {
                        NodeList keyValueList = entryList.item(j).getChildNodes();
                        String key = null;
                        String value = null;
                        this.addDataErrorCode(keyValueList, tableDataUrl, "NO_DATA");

                        for(int k = 0; k < keyValueList.getLength(); ++k) {
                           if (keyValueList.item(k).getNodeName().equals("key")) {
                              key = keyValueList.item(k).getTextContent();
                              if (i == 0) {
                                 columnLayout = columnLayout + "{name : '" + key + "', field : '" + key + "'},";
                              }
                           } else if (keyValueList.item(k).getNodeName().equals("value")) {
                              value = this.checkErrorChar(keyValueList.item(k).getTextContent());
                           }
                        }

                        dataStore = dataStore + "'" + key + "':'" + value + "',";
                     }
                  }

                  dataStore = dataStore.substring(0, dataStore.length() - 1);
                  dataStore = dataStore + "},";
               }

               dataStore = dataStore.substring(0, dataStore.length() - 1);
               dataStore = dataStore + "]";
               columnLayout = columnLayout.substring(0, columnLayout.length() - 1);
               columnLayout = columnLayout + "]";
               break label248;
            }

            this.dataErrorCode.put(tableDataUrl, "NO_DATA");
            b = null;
         } catch (MalformedURLException var34) {
            this.logger.error("", var34);
            break label248;
         } catch (UnsupportedEncodingException var35) {
            this.logger.error("", var35);
            break label248;
         } catch (IOException var36) {
            this.logger.error("", var36);
            break label248;
         } finally {
            if (isr != null) {
               try {
                  isr.close();
               } catch (Exception var33) {
                  this.logger.error("", var33);
               }
            }

         }

         return b;
      }

      resultMap.put("layout", columnLayout);
      resultMap.put("store", dataStore);
      return resultMap;
   }

   public Map getTableDataList(String tableDataUrl) throws ParserConfigurationException, SAXException {
      Map resultMap = new HashMap();
      ArrayList dataList = new ArrayList();
      ArrayList layoutList = new ArrayList();
      ArrayList headerList = new ArrayList();
      InputStreamReader isr = null;

      try {
         System.out.println("tableDataUrl " + tableDataUrl);
         URL url = new URL(tableDataUrl);
         isr = this.getConnectionReader(url, this.isSslUrl(tableDataUrl));
         StringBuffer sb = new StringBuffer();
         DocumentBuilderFactory factory;
         if (isr == null) {
            this.tableErrorCode.put(tableDataUrl, "NO_CONNECT");
            factory = null;
            return factory;
         }

         label236:
         while(true) {
            int c;
            if ((c = isr.read()) == -1) {
               isr.close();
               DocumentBuilder b;
               if (sb.toString().trim().length() < 1) {
                  this.dataErrorCode.put(tableDataUrl, "NO_DATA");
                  b = null;
                  return b;
               }

               factory = DocumentBuilderFactory.newInstance();
               factory.setValidating(false);
               b = factory.newDocumentBuilder();
               Document doc = b.parse(new InputSource(new StringReader(sb.toString())));
               Element root = doc.getDocumentElement();
               NodeList colList = root.getElementsByTagName("colValue");
               this.addDataErrorCode(colList, tableDataUrl, "NO_DATA");
               int i = 0;

               while(true) {
                  if (i >= colList.getLength()) {
                     break label236;
                  }

                  NodeList entryList = colList.item(i).getChildNodes();
                  Map dataMap = new HashMap();
                  this.addDataErrorCode(entryList, tableDataUrl, "NO_DATA");

                  for(int j = 0; j < entryList.getLength(); ++j) {
                     if (entryList.item(j).getNodeType() == 1) {
                        NodeList keyValueList = entryList.item(j).getChildNodes();
                        String key = null;
                        String value = null;
                        this.addDataErrorCode(keyValueList, tableDataUrl, "NO_DATA");

                        for(int k = 0; k < keyValueList.getLength(); ++k) {
                           if (keyValueList.item(k).getNodeName().equals("key")) {
                              key = keyValueList.item(k).getTextContent();
                              if (i == 0) {
                                 Map layoutMap = new HashMap();
                                 layoutMap.put("data", key);
                                 layoutMap.put("type", "text");
                                 layoutMap.put("width", "30");
                                 layoutList.add(layoutMap);
                                 headerList.add(key);
                              }
                           } else if (keyValueList.item(k).getNodeName().equals("value")) {
                              value = this.checkErrorChar(keyValueList.item(k).getTextContent());
                           }
                        }

                        dataMap.put(key, value);
                     }
                  }

                  dataList.add(dataMap);
                  ++i;
               }
            }

            sb.append((char)c);
         }
      } catch (MalformedURLException var37) {
         this.logger.error("", var37);
      } catch (UnsupportedEncodingException var38) {
         this.logger.error("", var38);
      } catch (IOException var39) {
         this.logger.error("", var39);
      } finally {
         if (isr != null) {
            try {
               isr.close();
            } catch (Exception var36) {
               this.logger.error("", var36);
            }
         }

      }

      resultMap.put("header", headerList);
      resultMap.put("layout", layoutList);
      resultMap.put("data", dataList);
      return resultMap;
   }

   public String checkErrorChar(String value) {
      value = value.replace("\n", " ");
      value = value.replace("'", "");
      return value;
   }

   public String makeParentAndChildren(String parentId, String parentName, String parentServer, ArrayList childrenList) {
      String result = "";
      result = "{ id : '" + parentId + "', name : '" + parentName + "', server : '" + parentServer + "',";
      result = result + "children:[";

      for(int i = 0; i < childrenList.size(); ++i) {
         result = result + "{_reference: '";
         result = result + (String)childrenList.get(i);
         result = result + "'}";
         if (i < childrenList.size() - 1) {
            result = result + ",";
         }
      }

      result = result + "]}";
      return result;
   }

   public String getDataLinkServerTableList(List ipList) throws ParserConfigurationException, SAXException {
      String result = "";
      String tmpResult = "";
      int rootCnt = 0;
      tmpResult = tmpResult + "[";
      Iterator itServerNameList = ipList.iterator();

      while(true) {
         String serverName;
         String serverIp;
         String serverPort;
         Boolean serverSsl;
         HashMap tableList;
         do {
            do {
               if (!itServerNameList.hasNext()) {
                  tmpResult = tmpResult.substring(0, tmpResult.length() - 1);
                  tmpResult = tmpResult + "]";
                  this.logger.info(tmpResult);
                  if (rootCnt > 0) {
                     result = tmpResult;
                  }

                  return result;
               }

               DatalinkServerEntity dataLinkServer = (DatalinkServerEntity)itServerNameList.next();
               serverName = dataLinkServer.getServer_name();
               serverIp = dataLinkServer.getIp_address();
               serverPort = dataLinkServer.getPort().toString();
               serverSsl = dataLinkServer.getUse_ssl();
               this.logger.info("serverName = " + serverName);
               tableList = this.getDataLinkTableList(serverIp, serverPort, serverName, serverSsl, (List)null);
            } while(tableList == null);
         } while(tableList.size() == 0);

         String start = "";
         String childServiceReferenceList = "";
         start = "{ id : " + rootCnt + ", name : '" + serverName + "', root:true, ";
         String childTypeReferenceList = "children:[";
         Set typeKeyList = tableList.keySet();
         Iterator itType = typeKeyList.iterator();

         String childServiceTypeReferenceList;
         String dataString;
         for(childServiceTypeReferenceList = ""; itType.hasNext(); childServiceTypeReferenceList = childServiceTypeReferenceList + childServiceReferenceList + dataString) {
            String type = (String)itType.next();
            this.logger.info("type = " + type);
            String tableString = "";
            HashMap typeTableList = (HashMap)tableList.get(type);
            childTypeReferenceList = childTypeReferenceList + "{_reference: '" + type + rootCnt + "'},";
            childServiceReferenceList = "{ id : '" + type + rootCnt + "', name : '" + type + "', server : '" + serverName + "',";
            childServiceReferenceList = childServiceReferenceList + "children:[";
            Set serviceKeyList = typeTableList.keySet();
            Iterator itService = serviceKeyList.iterator();
            dataString = "";

            for(String childTableReferenceList = ""; itService.hasNext(); dataString = dataString + childTableReferenceList + tableString) {
               String service = (String)itService.next();
               this.logger.info("service = " + service);
               ArrayList tableListByType = (ArrayList)typeTableList.get(service);
               Iterator itTableNameList = tableListByType.iterator();
               if (service == null) {
                  service = "";
               } else {
                  childServiceReferenceList = childServiceReferenceList + "{_reference: '" + type + "_" + service + rootCnt + "'},";
                  childTableReferenceList = "{ id : '" + type + "_" + service + rootCnt + "', name : '" + service + "', server : '" + serverName + "',";
                  childTableReferenceList = childTableReferenceList + "children:[";
               }

               DataLinkServerTable table;
               String url;
               for(tableString = ""; itTableNameList.hasNext(); tableString = tableString + "{ id : '" + type + "_" + service + "_" + table.getDynaName() + rootCnt + "', name : '" + table.getTableName() + "', dynaName : '" + table.getDynaName() + "', server : '" + serverName + "', url: '" + url + "'},") {
                  table = (DataLinkServerTable)itTableNameList.next();
                  this.logger.info("tableName = " + table.getTableName());
                  childTableReferenceList = childTableReferenceList + "{_reference: '" + type + "_" + service + "_" + table.getDynaName() + rootCnt + "'},";
                  url = this.makeUrlTable(serverIp, serverPort, table.getDynaName(), serverSsl);
               }

               childTableReferenceList = childTableReferenceList.substring(0, childTableReferenceList.length() - 1);
               childTableReferenceList = childTableReferenceList + "]},";
            }

            if (childServiceReferenceList.lastIndexOf(",") + 1 == childServiceReferenceList.length()) {
               childServiceReferenceList = childServiceReferenceList.substring(0, childServiceReferenceList.length() - 1);
               childServiceReferenceList = childServiceReferenceList + "]},";
            }
         }

         if (!childTypeReferenceList.equals("")) {
            childTypeReferenceList = childTypeReferenceList.substring(0, childTypeReferenceList.length() - 1);
            childTypeReferenceList = childTypeReferenceList + "]},";
         }

         tmpResult = tmpResult + start + childTypeReferenceList + childServiceTypeReferenceList;
         ++rootCnt;
      }
   }

   public ArrayList getDataLinkServerTableList2(List ipList, String organization) throws Exception {
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      DatalinkServer datalinkServer = DatalinkServerImpl.getInstance();
      int orgId = (int)userGroupInfo.getOrgGroupIdByName(organization);
      ArrayList groupList = new ArrayList();
      Iterator itServerNameList = ipList.iterator();
      int serviceCount = 1;

      while(true) {
         String serverName;
         String serverIp;
         String serverPort;
         Boolean serverSsl;
         HashMap tableList;
         do {
            do {
               if (!itServerNameList.hasNext()) {
                  return groupList;
               }

               List datalinkAssignTableList = null;
               DatalinkServerEntity dataLinkServer = (DatalinkServerEntity)itServerNameList.next();
               serverName = dataLinkServer.getServer_name();
               if (orgId == 0) {
                  datalinkAssignTableList = datalinkServer.getAllDatalinkServerTableInfo(serverName);
               } else {
                  datalinkAssignTableList = datalinkServer.getSelectedDatalinkServerTableByOrgIdAndServer((long)orgId, serverName);
               }

               serverIp = dataLinkServer.getIp_address();
               serverPort = dataLinkServer.getPort().toString();
               serverSsl = dataLinkServer.getUse_ssl();
               this.logger.info("serverName = " + serverName);
               LinkedHashMap serverMap = new LinkedHashMap();
               ++serviceCount;
               serverMap.put("id", serviceCount);
               serverMap.put("parent", "#");
               serverMap.put("text", serverName);
               serverMap.put("children", false);
               groupList.add(serverMap);
               tableList = this.getDataLinkTableList(serverIp, serverPort, serverName, serverSsl, datalinkAssignTableList);
            } while(tableList == null);
         } while(tableList.size() == 0);

         Set typeKeyList = tableList.keySet();
         Iterator itType = typeKeyList.iterator();
         int typeId = serviceCount;

         while(itType.hasNext()) {
            String type = (String)itType.next();
            this.logger.info("type = " + type);
            HashMap typeTableList = (HashMap)tableList.get(type);
            Set serviceKeyList = typeTableList.keySet();
            Iterator itService = serviceKeyList.iterator();
            LinkedHashMap typeMap = new LinkedHashMap();
            ++serviceCount;
            typeMap.put("id", serviceCount);
            typeMap.put("parent", typeId);
            typeMap.put("text", type);
            typeMap.put("children", false);
            groupList.add(typeMap);
            int serviceId = serviceCount;

            while(itService.hasNext()) {
               String service = (String)itService.next();
               this.logger.info("service = " + service);
               ArrayList tableListByType = (ArrayList)typeTableList.get(service);
               Iterator itTableNameList = tableListByType.iterator();
               if (service != null) {
                  LinkedHashMap serviceMap = new LinkedHashMap();
                  ++serviceCount;
                  serviceMap.put("id", serviceCount);
                  serviceMap.put("parent", serviceId);
                  serviceMap.put("text", service);
                  serviceMap.put("children", false);
                  groupList.add(serviceMap);
               }

               int tableId = serviceCount;

               while(itTableNameList.hasNext()) {
                  DataLinkServerTable table = (DataLinkServerTable)itTableNameList.next();
                  this.logger.info("tableName = " + table.getTableName());
                  String url = this.makeUrlTable(serverIp, serverPort, table.getDynaName(), serverSsl);
                  LinkedHashMap group = new LinkedHashMap();
                  group.put("id", url + "/" + serverName);
                  group.put("parent", tableId);
                  group.put("text", table.getTableName());
                  group.put("children", false);
                  groupList.add(group);
               }
            }
         }
      }
   }
}
